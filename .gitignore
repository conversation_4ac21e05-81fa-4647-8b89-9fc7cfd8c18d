.states
assets/external/
*.db
.web
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store


cache/
market_data_cache/
ibkr_market_data_cache/
cache_substack/
market_data/

output*
*_output
ThetaTerminal.jar
creds.txt
clearstreet*.json
schwab_token*.json


.env.*

/stats/
!src/stats/

# Ignore LLM context tools
repomix-output.*
digest.txt
.aider*

repomix.config.json

yfinance_cache/
data/

ThetaTerminal*.jar