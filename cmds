python -m brokers.cli.das_broker_cli submit --side buy --symbol AAPL --qty 1 --type limit --price 210 
python -m brokers.cli.hyperscalp --ticks 4 --exit_ticks 2 FGF 10 --rounds 10

python -m brokers.cli.das_broker_cli submit --side buy --symbol AAPL --qty 1 --type limit --price 205
python -m brokers.cli.das_broker_cli submit --side buy --symbol AAPL --qty 1 --type limit --price 205
python -m brokers.cli.das_broker_cli submit --side sell --symbol AAPL --qty 1 --type limit --price 205
python -m brokers.cli.das_locate_cli ensure --symbol NAMM --qty 10 --timeout 30
python -m brokers.cli.das_broker_cli submit --side sell --symbol SNGX --qty 10 --type limit --price 2.6
python -m brokers.cli.das_broker_cli submit --side sell --symbol SNGX --qty 10 --type limit --price 2.6 &> sngx_sell.log
python -m live_runner.pre_market_gap_short_twap_live_runner_das 2>&1 | tee output/logs/pre_market_gap_short_twap_live_runner_das.log 



