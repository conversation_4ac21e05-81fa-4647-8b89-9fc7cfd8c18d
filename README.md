# SignalForge

SignalForge is a Python framework for backtesting and productionizing trading strategies. Our framework enables rapid strategy development, robust backtesting, and a seamless transition to live trading environments. It is designed to encourage innovation by giving you the tools to prototype new strategies (for example, using the Substack ticker source) and safely productionize them with the live strategy orchestrator.

## Overview

SignalForge is built with three main goals in mind:

- **Develop New Strategies with Confidence:**  
  Easily create and test new strategies by extending our `BaseStrategy` class. Use existing examples (like the `substack_ticker_source`) to learn how to extract and process ticker events. This gives you the confidence to iterate over your trading ideas with both historical and simulated data.

- **Seamless Productionization of Strategies:**  
  When your strategy is validated in backtests, you can transition it into a live trading environment using the `live_strategy_orchestrator`. This orchestrator handles:
  - Ticker retrieval
  - Historical & live market data gathering
  - Signal generation and order execution  
  This end-to-end workflow minimizes manual intervention and helps you focus on refining your trading logic.

- **Modular Integration with Different Components:**  
  SignalForge is designed to be modular:
  - **Ticker Sources:** Plug in various ticker sources such as Substack, Twitter, email feeds, etc., by implementing the `ITickerSource` interface.
  - **Market Data Providers:** Utilize different market data providers (e.g., Polygon.io or custom providers) as long as they adhere to the `IMarketData` interface.
  - **Brokers:** Integrate with multiple brokers (like Alpaca, IBKR, or others) by implementing the `IBroker` interface.  
  This flexibility allows you to easily swap or add components as your trading strategies evolve.

## Getting Started

### Prerequisites

- Python 3.8+
- A valid API key for Polygon.io (or any other data source you plan to use)
- Dependencies as listed in `requirements.txt`

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd signalforge
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up your environment variables (e.g., Polygon.io API key):
   ```bash
   echo "POLYGON_API_KEY=your_api_key_here" > .env
   ```

## Usage

### Backtesting

Run the backtest with default parameters:
```bash
python -m src.main
```

To customize parameters:
```bash
python -m src.main \
    --market-cap-min 5000000 \
    --market-cap-max 50000000 \
    --gap-percentage 50 \
    --start-date 2024-01-01 \
    --end-date 2024-12-31 \
    --initial-capital 100000 \
    --max-position-size 5000 \
    --stop-loss 90 \
    --take-profit 100 \
    --max-positions 3
```

### Live Trading

Transition from backtesting to live trading using the `live_strategy_orchestrator`:
- **Validate Your Strategy:**  
  Use the backtesting scripts to fine-tune and validate your strategy.
- **Productionize Your Strategy:**  
  Plug your strategy into the orchestration layer to handle live data ingestion, signal generation, and order execution.
- **Example Usage:**  
  Run the live orchestrator with a specific ticker source (e.g., Substack):
  ```bash
  python src/substack_live_runner.py --chat_id <your_chat_id>
  ```
  You can customize this process for different ticker sources or change the broker integration by modifying the corresponding runner scripts.

## Extending SignalForge

### Adding New Ticker Sources

Integrate additional data providers (such as Twitter or email notifications) by creating a new module that implements the `ITickerSource` interface. For instance:
- Create a `twitter_ticker_source.py` that defines methods to fetch and process tweet data.
- Update the runner scripts to include your new ticker source.

### Adding New Brokers

Plug in different brokers by implementing the `IBroker` interface. Whether you choose a different trading platform or wish to add support for another API, SignalForge's modular design makes it easy to extend broker support.

### Adding New Market Data Providers

Extend your market data options by implementing the `IMarketData` interface. While our current implementation is based on Polygon.io, any data provider that conforms to the expected interface can be integrated.

## Output Files

SignalForge's backtesting module generates several output files:
1. `equity_curve.png`: A visual representation of your portfolio value over time.
2. `trade_log.csv`: A detailed log of all executed trades.
3. `performance_metrics.json`: Key performance statistics and metrics for your strategy.

## Contributing

1. Fork the repository.
2. Create a feature branch.
3. Commit your changes.
4. Push your branch.
5. Open a Pull Request.

## License

MIT License
