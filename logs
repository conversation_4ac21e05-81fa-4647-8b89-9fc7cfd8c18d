[2025-08-13 16:02:05 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['NYC', 'WSML', 'SNOA', 'KVAC']
[2025-08-13 16:02:05 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'WSML', 'SNOA', 'KVAC']
[2025-08-13 16:02:05 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:02:05 EDT] INFO - orchestrator.execution_orchestrator - Started processing ticker: SNOA
[2025-08-13 16:02:05 EDT] INFO - orchestrator.execution_orchestrator - Processing ticker SNOA with interval 0:01:00
[2025-08-13 16:02:05 EDT] INFO - orchestrator.execution_orchestrator - Strategy requested interval 0:01:00 and lookback 5 days, 0:00:00 for ticker SNOA
[2025-08-13 16:02:05 EDT] INFO - marketdata.polygon_streaming_market_data - Connecting to Polygon WebSocket for SNOA_60...
[2025-08-13 16:02:05 EDT] INFO - marketdata.polygon_streaming_market_data - Sent subscription request for AM.SNOA
[2025-08-13 16:02:05 EDT] INFO - marketdata.polygon_streaming_market_data - WebSocket receiver task started for SNOA_60.
[2025-08-13 16:02:05 EDT] INFO - marketdata.polygon_streaming_market_data - Starting message processor for SNOA_60...
[2025-08-13 16:02:17 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['WSML', 'NYC', 'SNOA', 'KVAC']
[2025-08-13 16:02:17 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['WSML', 'NYC', 'SNOA', 'KVAC']
[2025-08-13 16:02:17 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:02:23 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['NYC', 'WSML', 'SNOA', 'KVAC']
[2025-08-13 16:02:23 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'WSML', 'SNOA', 'KVAC']
[2025-08-13 16:02:23 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:02:30 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['WSML', 'SNOA', 'KVAC', 'NYC']
[2025-08-13 16:02:30 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['WSML', 'SNOA', 'KVAC', 'NYC']
[2025-08-13 16:02:30 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:03:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=74 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:03:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:03:00-04:00, current_time=16:03:00
[2025-08-13 16:03:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=1194307.20 (min=250000.00)
[2025-08-13 16:03:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=3.2951, last_open=4.2400, last_close=4.5000, red_bar=False, below_ma=False
[2025-08-13 16:03:57 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['KVAC', 'NYC', 'WSML', 'SNOA']
[2025-08-13 16:03:57 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'WSML', 'SNOA']
[2025-08-13 16:03:57 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:04:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=75 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:04:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:04:00-04:00, current_time=16:04:00
[2025-08-13 16:04:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=1909092.73 (min=250000.00)
[2025-08-13 16:04:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=3.4631, last_open=4.4200, last_close=4.6700, red_bar=False, below_ma=False
[2025-08-13 16:04:03 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['KVAC', 'WSML', 'SNOA', 'NYC']
[2025-08-13 16:04:03 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'WSML', 'SNOA', 'NYC']
[2025-08-13 16:04:03 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:05:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=76 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:05:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:05:00-04:00, current_time=16:05:00
[2025-08-13 16:05:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=2410195.33 (min=250000.00)
[2025-08-13 16:05:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=3.5951, last_open=4.6624, last_close=4.3500, red_bar=True, below_ma=False
[2025-08-13 16:06:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=77 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:06:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:06:00-04:00, current_time=16:06:00
[2025-08-13 16:06:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=2937997.84 (min=250000.00)
[2025-08-13 16:06:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=3.7453, last_open=4.4000, last_close=4.4816, red_bar=False, below_ma=False
[2025-08-13 16:07:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=78 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:07:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:07:00-04:00, current_time=16:07:00
[2025-08-13 16:07:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=3562353.53 (min=250000.00)
[2025-08-13 16:07:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=3.8723, last_open=4.5300, last_close=4.3300, red_bar=True, below_ma=False
[2025-08-13 16:07:28 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['SNOA', 'NYC', 'KVAC', 'WSML']
[2025-08-13 16:07:28 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC', 'WSML']
[2025-08-13 16:07:28 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:08:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=79 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:08:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:08:00-04:00, current_time=16:08:00
[2025-08-13 16:08:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=4167385.21 (min=250000.00)
[2025-08-13 16:08:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.0143, last_open=4.3400, last_close=4.4300, red_bar=False, below_ma=False
[2025-08-13 16:09:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=80 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:09:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:09:00-04:00, current_time=16:09:00
[2025-08-13 16:09:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=4583721.61 (min=250000.00)
[2025-08-13 16:09:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.1493, last_open=4.4300, last_close=4.3600, red_bar=True, below_ma=False
[2025-08-13 16:10:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=81 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:10:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:10:00-04:00, current_time=16:10:00
[2025-08-13 16:10:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=5165245.63 (min=250000.00)
[2025-08-13 16:10:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.2994, last_open=4.3600, last_close=4.6200, red_bar=False, below_ma=False
[2025-08-13 16:10:02 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 12 tickers: ['NYC', 'KVAC', 'DFND', 'CSTK', 'CVAR', 'WSML', 'CTIF', 'SNOA', 'EDGE', 'BUFH', 'CMCI', 'BUYZ']
[2025-08-13 16:10:02 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'DFND', 'CSTK', 'CVAR', 'WSML', 'CTIF', 'SNOA', 'EDGE', 'BUFH', 'CMCI', 'BUYZ']
[2025-08-13 16:10:02 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:10:08 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['LFAU', 'DFND', 'LFAE', 'SEPP', 'CSTK', 'CVAR', 'HEAT', 'QIG', 'LFAI', 'FEBZ', 'MAYZ', 'WSML', 'CTIF', 'LIAX', 'LIAK', 'JNEU', 'LFBB', 'PBFB', 'MGRO', 'PJFM', 'LFAF', 'SNOA', 'OCTM', 'XDOC', 'TJUN', 'NMAY', 'PCL', 'LFAQ', 'RVRB', 'PBSE', 'LIAB', 'PMJN', 'PMMR', 'MOTE', 'HYKE', 'JUNP', 'GHMS', 'HBTC', 'PMJL', 'PSCW', 'EDGE', 'PCS', 'LDRR', 'SPIN', 'IPAV', 'LFDR', 'LIAU', 'LFAL', 'PMJA', 'LFAN', 'SHDG', 'PBJN', 'LIAW', 'GBXC', 'MVAL', 'QBSF', 'ZJUN', 'LIBD', 'PMAP', 'TGLR', 'LFAX', 'VWID', 'GSEE', 'PJBF', 'LFAZ', 'VMAX', 'TDEC', 'PBFR', 'SPCZ', 'GBXA', 'PSCQ', 'LIAV', 'BUFH', 'LIAG', 'SEPU', 'LDER', 'SEPM', 'FLDZ', 'IPPP', 'LFAK', 'LIAP', 'LFAO', 'LIAE', 'XDAT', 'CMCI', 'OEUR', 'PBMY', 'GBXB', 'LFAW', 'BUYZ', 'LYLD', 'LIAY', 'KNOW', 'NOVZ', 'LFAV', 'JUNZ', 'LIAT', 'PMFB', 'PBDE', 'TJAN', 'NYC', 'MARU', 'LIAQ', 'GLBL', 'UXAP', 'LIAF', 'WHTX', 'EUDV', 'LFAR', 'PSFO', 'KVAC', 'PBOC', 'PBMR', 'LFBD', 'PSMR', 'LIAM', 'SNPV', 'EAOK', 'LFBE', 'UXJA', 'LIAC', 'LFAJ', 'LIAJ', 'PMMY', 'LIAO', 'INTM', 'PBNV']
[2025-08-13 16:10:08 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['LFAU', 'DFND', 'LFAE', 'SEPP', 'CSTK', 'CVAR', 'HEAT', 'QIG', 'LFAI', 'FEBZ', 'MAYZ', 'WSML', 'CTIF', 'LIAX', 'LIAK', 'JNEU', 'LFBB', 'PBFB', 'MGRO', 'PJFM', 'LFAF', 'SNOA', 'OCTM', 'XDOC', 'TJUN', 'NMAY', 'PCL', 'LFAQ', 'RVRB', 'PBSE', 'LIAB', 'PMJN', 'PMMR', 'MOTE', 'HYKE', 'JUNP', 'GHMS', 'HBTC', 'PMJL', 'PSCW', 'EDGE', 'PCS', 'LDRR', 'SPIN', 'IPAV', 'LFDR', 'LIAU', 'LFAL', 'PMJA', 'LFAN', 'SHDG', 'PBJN', 'LIAW', 'GBXC', 'MVAL', 'QBSF', 'ZJUN', 'LIBD', 'PMAP', 'TGLR', 'LFAX', 'VWID', 'GSEE', 'PJBF', 'LFAZ', 'VMAX', 'TDEC', 'PBFR', 'SPCZ', 'GBXA', 'PSCQ', 'LIAV', 'BUFH', 'LIAG', 'SEPU', 'LDER', 'SEPM', 'FLDZ', 'IPPP', 'LFAK', 'LIAP', 'LFAO', 'LIAE', 'XDAT', 'CMCI', 'OEUR', 'PBMY', 'GBXB', 'LFAW', 'BUYZ', 'LYLD', 'LIAY', 'KNOW', 'NOVZ', 'LFAV', 'JUNZ', 'LIAT', 'PMFB', 'PBDE', 'TJAN', 'NYC', 'MARU', 'LIAQ', 'GLBL', 'UXAP', 'LIAF', 'WHTX', 'EUDV', 'LFAR', 'PSFO', 'KVAC', 'PBOC', 'PBMR', 'LFBD', 'PSMR', 'LIAM', 'SNPV', 'EAOK', 'LFBE', 'UXJA', 'LIAC', 'LFAJ', 'LIAJ', 'PMMY', 'LIAO', 'INTM', 'PBNV']
[2025-08-13 16:10:08 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:10:15 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['UXJA', 'PBMY', 'PSFO', 'IPAV', 'PCS', 'PMAP', 'LDRR', 'LFBD', 'SNOA', 'LIAT', 'LFAW', 'NMAY', 'LIAO', 'KVAC', 'GBXC', 'EDGE', 'VWID', 'SNPV', 'BUFH', 'XDOC', 'MGRO', 'LFAU', 'LFAF', 'WSML', 'CSTK', 'LIAV', 'LFAQ', 'SEPP', 'LIAJ', 'LIBD', 'LFBE', 'MVAL', 'KNOW', 'DFND', 'FLDZ', 'TDEC', 'PCL', 'PBSE', 'LIAQ', 'SEPU', 'TGLR', 'LIAU', 'SHDG', 'LIAF', 'GHMS', 'LFAL', 'PBFR', 'GBXB', 'HBTC', 'LFAO', 'PMMY', 'MARU', 'GLBL', 'WHTX', 'JUNP', 'TJAN', 'CVAR', 'LIAE', 'PMMR', 'UXAP', 'LIAK', 'EUDV', 'OEUR', 'LFBB', 'PBFB', 'LFAJ', 'PMJL', 'NYC', 'LFAZ', 'LFAX', 'CMCI', 'GSEE', 'GBXA', 'JUNZ', 'BUYZ', 'LFAE', 'HYKE', 'PMFB', 'MOTE', 'LFAK', 'LIAX', 'LIAC', 'LFAV', 'PBNV', 'QBSF', 'ZJUN', 'LYLD', 'MAYZ', 'LFAN', 'LDER', 'SPIN', 'PBJN', 'EAOK', 'PBDE', 'PJBF', 'SPCZ', 'LIAM', 'QIG', 'INTM', 'PJFM', 'LIAW', 'PSMR', 'PSCW', 'LIAG', 'LIAY', 'FEBZ', 'LIAP', 'LFDR', 'VMAX', 'PBOC', 'XDAT', 'NOVZ', 'LFAI', 'SEPM', 'PMJA', 'LIAB', 'CTIF', 'PBMR', 'JNEU', 'OCTM', 'PMJN', 'PSCQ', 'IPPP', 'RVRB', 'HEAT', 'TJUN', 'LFAR']
[2025-08-13 16:10:15 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['UXJA', 'PBMY', 'PSFO', 'IPAV', 'PCS', 'PMAP', 'LDRR', 'LFBD', 'SNOA', 'LIAT', 'LFAW', 'NMAY', 'LIAO', 'KVAC', 'GBXC', 'EDGE', 'VWID', 'SNPV', 'BUFH', 'XDOC', 'MGRO', 'LFAU', 'LFAF', 'WSML', 'CSTK', 'LIAV', 'LFAQ', 'SEPP', 'LIAJ', 'LIBD', 'LFBE', 'MVAL', 'KNOW', 'DFND', 'FLDZ', 'TDEC', 'PCL', 'PBSE', 'LIAQ', 'SEPU', 'TGLR', 'LIAU', 'SHDG', 'LIAF', 'GHMS', 'LFAL', 'PBFR', 'GBXB', 'HBTC', 'LFAO', 'PMMY', 'MARU', 'GLBL', 'WHTX', 'JUNP', 'TJAN', 'CVAR', 'LIAE', 'PMMR', 'UXAP', 'LIAK', 'EUDV', 'OEUR', 'LFBB', 'PBFB', 'LFAJ', 'PMJL', 'NYC', 'LFAZ', 'LFAX', 'CMCI', 'GSEE', 'GBXA', 'JUNZ', 'BUYZ', 'LFAE', 'HYKE', 'PMFB', 'MOTE', 'LFAK', 'LIAX', 'LIAC', 'LFAV', 'PBNV', 'QBSF', 'ZJUN', 'LYLD', 'MAYZ', 'LFAN', 'LDER', 'SPIN', 'PBJN', 'EAOK', 'PBDE', 'PJBF', 'SPCZ', 'LIAM', 'QIG', 'INTM', 'PJFM', 'LIAW', 'PSMR', 'PSCW', 'LIAG', 'LIAY', 'FEBZ', 'LIAP', 'LFDR', 'VMAX', 'PBOC', 'XDAT', 'NOVZ', 'LFAI', 'SEPM', 'PMJA', 'LIAB', 'CTIF', 'PBMR', 'JNEU', 'OCTM', 'PMJN', 'PSCQ', 'IPPP', 'RVRB', 'HEAT', 'TJUN', 'LFAR']
[2025-08-13 16:10:15 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:10:21 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['PSFO', 'MOTE', 'LIAE', 'PSMR', 'LIAX', 'GBXC', 'GLBL', 'BUFH', 'PMMY', 'LFAL', 'LFAJ', 'FLDZ', 'LIAM', 'LDRR', 'LDER', 'NOVZ', 'KNOW', 'LIAT', 'LFDR', 'LIAW', 'LIBD', 'LIAC', 'LFAU', 'PJFM', 'LFAZ', 'ZJUN', 'PBFR', 'MGRO', 'PMJA', 'CVAR', 'GBXB', 'LIAP', 'LIAO', 'LFAI', 'SHDG', 'IPAV', 'LFAN', 'UXAP', 'GBXA', 'PMFB', 'JNEU', 'LFBE', 'EDGE', 'TJUN', 'PBFB', 'LFAR', 'HYKE', 'LFAF', 'LIAG', 'SEPM', 'OCTM', 'XDAT', 'VWID', 'PBJN', 'RVRB', 'JUNZ', 'LIAU', 'LIAY', 'SNPV', 'FEBZ', 'TJAN', 'GHMS', 'WSML', 'PCL', 'PMMR', 'CSTK', 'TGLR', 'LIAV', 'INTM', 'PMAP', 'PCS', 'PBMY', 'NMAY', 'LIAK', 'PBMR', 'QIG', 'KVAC', 'LFAX', 'MARU', 'IPPP', 'SEPU', 'DFND', 'LFBB', 'PSCW', 'OEUR', 'LIAB', 'LFAW', 'MVAL', 'NYC', 'XDOC', 'LFAV', 'LYLD', 'SPCZ', 'PSCQ', 'WHTX', 'GSEE', 'LFAO', 'VMAX', 'TDEC', 'PBSE', 'EUDV', 'PBDE', 'PBOC', 'SEPP', 'LFAE', 'QBSF', 'PMJL', 'LIAQ', 'MAYZ', 'HBTC', 'EAOK', 'LFBD', 'UXJA', 'HEAT', 'LFAQ', 'LFAK', 'LIAJ', 'BUYZ', 'SNOA', 'LIAF', 'PJBF', 'JUNP', 'SPIN', 'PBNV', 'PMJN', 'CMCI', 'CTIF']
[2025-08-13 16:10:21 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['PSFO', 'MOTE', 'LIAE', 'PSMR', 'LIAX', 'GBXC', 'GLBL', 'BUFH', 'PMMY', 'LFAL', 'LFAJ', 'FLDZ', 'LIAM', 'LDRR', 'LDER', 'NOVZ', 'KNOW', 'LIAT', 'LFDR', 'LIAW', 'LIBD', 'LIAC', 'LFAU', 'PJFM', 'LFAZ', 'ZJUN', 'PBFR', 'MGRO', 'PMJA', 'CVAR', 'GBXB', 'LIAP', 'LIAO', 'LFAI', 'SHDG', 'IPAV', 'LFAN', 'UXAP', 'GBXA', 'PMFB', 'JNEU', 'LFBE', 'EDGE', 'TJUN', 'PBFB', 'LFAR', 'HYKE', 'LFAF', 'LIAG', 'SEPM', 'OCTM', 'XDAT', 'VWID', 'PBJN', 'RVRB', 'JUNZ', 'LIAU', 'LIAY', 'SNPV', 'FEBZ', 'TJAN', 'GHMS', 'WSML', 'PCL', 'PMMR', 'CSTK', 'TGLR', 'LIAV', 'INTM', 'PMAP', 'PCS', 'PBMY', 'NMAY', 'LIAK', 'PBMR', 'QIG', 'KVAC', 'LFAX', 'MARU', 'IPPP', 'SEPU', 'DFND', 'LFBB', 'PSCW', 'OEUR', 'LIAB', 'LFAW', 'MVAL', 'NYC', 'XDOC', 'LFAV', 'LYLD', 'SPCZ', 'PSCQ', 'WHTX', 'GSEE', 'LFAO', 'VMAX', 'TDEC', 'PBSE', 'EUDV', 'PBDE', 'PBOC', 'SEPP', 'LFAE', 'QBSF', 'PMJL', 'LIAQ', 'MAYZ', 'HBTC', 'EAOK', 'LFBD', 'UXJA', 'HEAT', 'LFAQ', 'LFAK', 'LIAJ', 'BUYZ', 'SNOA', 'LIAF', 'PJBF', 'JUNP', 'SPIN', 'PBNV', 'PMJN', 'CMCI', 'CTIF']
[2025-08-13 16:10:21 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:10:27 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['MVAL', 'PJBF', 'SEPM', 'LIBD', 'HYKE', 'LIAX', 'SEPP', 'EDGE', 'SEPU', 'LFAE', 'RVRB', 'BUFH', 'XDOC', 'MAYZ', 'PMFB', 'CVAR', 'LFBE', 'EUDV', 'TJAN', 'SPCZ', 'PBOC', 'PMAP', 'GHMS', 'MARU', 'NOVZ', 'GBXB', 'GBXC', 'LFAF', 'LIAU', 'QIG', 'UXJA', 'LFBD', 'CTIF', 'ZJUN', 'PSCQ', 'PBNV', 'LFAL', 'LFAV', 'PSCW', 'LFDR', 'LFAO', 'LIAJ', 'LIAY', 'CMCI', 'PCL', 'LFAQ', 'SHDG', 'LIAP', 'PMJA', 'PBFR', 'LIAM', 'QBSF', 'LIAQ', 'TJUN', 'LFAX', 'SNOA', 'IPPP', 'LFAI', 'KNOW', 'WSML', 'KVAC', 'LFAW', 'LIAW', 'LDER', 'PBMY', 'LIAK', 'VWID', 'JNEU', 'LDRR', 'PBJN', 'IPAV', 'OEUR', 'GLBL', 'PBMR', 'DFND', 'PSFO', 'LIAG', 'GBXA', 'LFAR', 'INTM', 'PMJL', 'JUNP', 'PMMY', 'LFAZ', 'SNPV', 'LYLD', 'PMJN', 'LFAN', 'GSEE', 'LIAT', 'PBDE', 'PJFM', 'OCTM', 'NYC', 'PBSE', 'HBTC', 'MGRO', 'LIAF', 'MOTE', 'LFAJ', 'LIAV', 'CSTK', 'PCS', 'WHTX', 'EAOK', 'LIAB', 'FEBZ', 'LIAE', 'PSMR', 'PBFB', 'TDEC', 'HEAT', 'TGLR', 'JUNZ', 'BUYZ', 'SPIN', 'NMAY', 'PMMR', 'LIAO', 'LFAU', 'VMAX', 'LIAC', 'LFBB', 'UXAP', 'LFAK', 'XDAT', 'FLDZ']
[2025-08-13 16:10:27 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['MVAL', 'PJBF', 'SEPM', 'LIBD', 'HYKE', 'LIAX', 'SEPP', 'EDGE', 'SEPU', 'LFAE', 'RVRB', 'BUFH', 'XDOC', 'MAYZ', 'PMFB', 'CVAR', 'LFBE', 'EUDV', 'TJAN', 'SPCZ', 'PBOC', 'PMAP', 'GHMS', 'MARU', 'NOVZ', 'GBXB', 'GBXC', 'LFAF', 'LIAU', 'QIG', 'UXJA', 'LFBD', 'CTIF', 'ZJUN', 'PSCQ', 'PBNV', 'LFAL', 'LFAV', 'PSCW', 'LFDR', 'LFAO', 'LIAJ', 'LIAY', 'CMCI', 'PCL', 'LFAQ', 'SHDG', 'LIAP', 'PMJA', 'PBFR', 'LIAM', 'QBSF', 'LIAQ', 'TJUN', 'LFAX', 'SNOA', 'IPPP', 'LFAI', 'KNOW', 'WSML', 'KVAC', 'LFAW', 'LIAW', 'LDER', 'PBMY', 'LIAK', 'VWID', 'JNEU', 'LDRR', 'PBJN', 'IPAV', 'OEUR', 'GLBL', 'PBMR', 'DFND', 'PSFO', 'LIAG', 'GBXA', 'LFAR', 'INTM', 'PMJL', 'JUNP', 'PMMY', 'LFAZ', 'SNPV', 'LYLD', 'PMJN', 'LFAN', 'GSEE', 'LIAT', 'PBDE', 'PJFM', 'OCTM', 'NYC', 'PBSE', 'HBTC', 'MGRO', 'LIAF', 'MOTE', 'LFAJ', 'LIAV', 'CSTK', 'PCS', 'WHTX', 'EAOK', 'LIAB', 'FEBZ', 'LIAE', 'PSMR', 'PBFB', 'TDEC', 'HEAT', 'TGLR', 'JUNZ', 'BUYZ', 'SPIN', 'NMAY', 'PMMR', 'LIAO', 'LFAU', 'VMAX', 'LIAC', 'LFBB', 'UXAP', 'LFAK', 'XDAT', 'FLDZ']
[2025-08-13 16:10:27 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:10:33 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['LIAB', 'PMJN', 'PMMR', 'MOTE', 'HYKE', 'JUNP', 'GHMS', 'HBTC', 'PMJL', 'PSCW', 'EDGE', 'PCS', 'LDRR', 'SPIN', 'IPAV', 'LFDR', 'LIAU', 'LFAL', 'PMJA', 'LFAN', 'SHDG', 'PBJN', 'LIAW', 'GBXC', 'MVAL', 'QBSF', 'ZJUN', 'LIBD', 'PMAP', 'TGLR', 'LFAX', 'GSEE', 'VWID', 'PJBF', 'LFAZ', 'VMAX', 'TDEC', 'PBFR', 'SPCZ', 'GBXA', 'PSCQ', 'LIAV', 'BUFH', 'LIAG', 'SEPU', 'LDER', 'SEPM', 'FLDZ', 'IPPP', 'LFAK', 'LIAP', 'LFAO', 'LIAE', 'XDAT', 'CMCI', 'OEUR', 'PBMY', 'GBXB', 'LFAW', 'BUYZ', 'LYLD', 'LIAY', 'KNOW', 'NOVZ', 'LFAV', 'JUNZ', 'LIAT', 'PMFB', 'PBDE', 'TJAN', 'NYC', 'MARU', 'LIAQ', 'GLBL', 'UXAP', 'LIAF', 'WHTX', 'EUDV', 'LFAR', 'PSFO', 'KVAC', 'PBOC', 'PBMR', 'LFBD', 'PSMR', 'LIAM', 'SNPV', 'EAOK', 'LFBE', 'UXJA', 'LIAC', 'LFAJ', 'LIAJ', 'PMMY', 'LIAO', 'INTM', 'PBNV', 'LFAU', 'DFND', 'LFAE', 'SEPP', 'CSTK', 'CVAR', 'HEAT', 'QIG', 'LFAI', 'FEBZ', 'MAYZ', 'WSML', 'CTIF', 'LIAX', 'LIAK', 'JNEU', 'LFBB', 'PBFB', 'MGRO', 'PJFM', 'LFAF', 'SNOA', 'OCTM', 'XDOC', 'TJUN', 'NMAY', 'PCL', 'LFAQ', 'RVRB', 'PBSE']
[2025-08-13 16:10:33 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['LIAB', 'PMJN', 'PMMR', 'MOTE', 'HYKE', 'JUNP', 'GHMS', 'HBTC', 'PMJL', 'PSCW', 'EDGE', 'PCS', 'LDRR', 'SPIN', 'IPAV', 'LFDR', 'LIAU', 'LFAL', 'PMJA', 'LFAN', 'SHDG', 'PBJN', 'LIAW', 'GBXC', 'MVAL', 'QBSF', 'ZJUN', 'LIBD', 'PMAP', 'TGLR', 'LFAX', 'GSEE', 'VWID', 'PJBF', 'LFAZ', 'VMAX', 'TDEC', 'PBFR', 'SPCZ', 'GBXA', 'PSCQ', 'LIAV', 'BUFH', 'LIAG', 'SEPU', 'LDER', 'SEPM', 'FLDZ', 'IPPP', 'LFAK', 'LIAP', 'LFAO', 'LIAE', 'XDAT', 'CMCI', 'OEUR', 'PBMY', 'GBXB', 'LFAW', 'BUYZ', 'LYLD', 'LIAY', 'KNOW', 'NOVZ', 'LFAV', 'JUNZ', 'LIAT', 'PMFB', 'PBDE', 'TJAN', 'NYC', 'MARU', 'LIAQ', 'GLBL', 'UXAP', 'LIAF', 'WHTX', 'EUDV', 'LFAR', 'PSFO', 'KVAC', 'PBOC', 'PBMR', 'LFBD', 'PSMR', 'LIAM', 'SNPV', 'EAOK', 'LFBE', 'UXJA', 'LIAC', 'LFAJ', 'LIAJ', 'PMMY', 'LIAO', 'INTM', 'PBNV', 'LFAU', 'DFND', 'LFAE', 'SEPP', 'CSTK', 'CVAR', 'HEAT', 'QIG', 'LFAI', 'FEBZ', 'MAYZ', 'WSML', 'CTIF', 'LIAX', 'LIAK', 'JNEU', 'LFBB', 'PBFB', 'MGRO', 'PJFM', 'LFAF', 'SNOA', 'OCTM', 'XDOC', 'TJUN', 'NMAY', 'PCL', 'LFAQ', 'RVRB', 'PBSE']
[2025-08-13 16:10:33 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:10:39 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['KVAC', 'GBXC', 'EDGE', 'VWID', 'SNPV', 'BUFH', 'XDOC', 'MGRO', 'LFAU', 'LFAF', 'WSML', 'CSTK', 'LIAV', 'LFAQ', 'SEPP', 'LIAJ', 'LIBD', 'LFBE', 'MVAL', 'KNOW', 'DFND', 'FLDZ', 'TDEC', 'PCL', 'PBSE', 'LIAQ', 'SEPU', 'TGLR', 'LIAU', 'SHDG', 'LIAF', 'GHMS', 'LFAL', 'PBFR', 'GBXB', 'HBTC', 'LFAO', 'PMMY', 'MARU', 'GLBL', 'WHTX', 'JUNP', 'TJAN', 'CVAR', 'LIAE', 'PMMR', 'UXAP', 'LIAK', 'EUDV', 'OEUR', 'LFBB', 'PBFB', 'LFAJ', 'PMJL', 'NYC', 'LFAZ', 'LFAX', 'CMCI', 'GSEE', 'GBXA', 'JUNZ', 'BUYZ', 'LFAE', 'HYKE', 'PMFB', 'MOTE', 'LFAK', 'LIAX', 'LIAC', 'LFAV', 'PBNV', 'QBSF', 'ZJUN', 'LYLD', 'MAYZ', 'LFAN', 'LDER', 'SPIN', 'PBJN', 'EAOK', 'PBDE', 'PJBF', 'SPCZ', 'LIAM', 'QIG', 'INTM', 'PJFM', 'LIAW', 'PSMR', 'PSCW', 'LIAG', 'LIAY', 'FEBZ', 'LIAP', 'LFDR', 'VMAX', 'PBOC', 'XDAT', 'NOVZ', 'LFAI', 'SEPM', 'PMJA', 'LIAB', 'CTIF', 'PBMR', 'JNEU', 'OCTM', 'PMJN', 'PSCQ', 'IPPP', 'RVRB', 'HEAT', 'TJUN', 'LFAR', 'UXJA', 'PBMY', 'PSFO', 'IPAV', 'PCS', 'PMAP', 'LFBD', 'LDRR', 'SNOA', 'LIAT', 'LFAW', 'NMAY', 'LIAO']
[2025-08-13 16:10:40 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'GBXC', 'EDGE', 'VWID', 'SNPV', 'BUFH', 'XDOC', 'MGRO', 'LFAU', 'LFAF', 'WSML', 'CSTK', 'LIAV', 'LFAQ', 'SEPP', 'LIAJ', 'LIBD', 'LFBE', 'MVAL', 'KNOW', 'DFND', 'FLDZ', 'TDEC', 'PCL', 'PBSE', 'LIAQ', 'SEPU', 'TGLR', 'LIAU', 'SHDG', 'LIAF', 'GHMS', 'LFAL', 'PBFR', 'GBXB', 'HBTC', 'LFAO', 'PMMY', 'MARU', 'GLBL', 'WHTX', 'JUNP', 'TJAN', 'CVAR', 'LIAE', 'PMMR', 'UXAP', 'LIAK', 'EUDV', 'OEUR', 'LFBB', 'PBFB', 'LFAJ', 'PMJL', 'NYC', 'LFAZ', 'LFAX', 'CMCI', 'GSEE', 'GBXA', 'JUNZ', 'BUYZ', 'LFAE', 'HYKE', 'PMFB', 'MOTE', 'LFAK', 'LIAX', 'LIAC', 'LFAV', 'PBNV', 'QBSF', 'ZJUN', 'LYLD', 'MAYZ', 'LFAN', 'LDER', 'SPIN', 'PBJN', 'EAOK', 'PBDE', 'PJBF', 'SPCZ', 'LIAM', 'QIG', 'INTM', 'PJFM', 'LIAW', 'PSMR', 'PSCW', 'LIAG', 'LIAY', 'FEBZ', 'LIAP', 'LFDR', 'VMAX', 'PBOC', 'XDAT', 'NOVZ', 'LFAI', 'SEPM', 'PMJA', 'LIAB', 'CTIF', 'PBMR', 'JNEU', 'OCTM', 'PMJN', 'PSCQ', 'IPPP', 'RVRB', 'HEAT', 'TJUN', 'LFAR', 'UXJA', 'PBMY', 'PSFO', 'IPAV', 'PCS', 'PMAP', 'LFBD', 'LDRR', 'SNOA', 'LIAT', 'LFAW', 'NMAY', 'LIAO']
[2025-08-13 16:10:40 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:10:46 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['CMCI', 'PCL', 'LFAQ', 'SHDG', 'LIAP', 'PMJA', 'PBFR', 'LIAM', 'QBSF', 'LIAQ', 'TJUN', 'LFAX', 'SNOA', 'IPPP', 'LFAI', 'KNOW', 'WSML', 'KVAC', 'LFAW', 'LIAW', 'LDER', 'PBMY', 'LIAK', 'VWID', 'JNEU', 'LDRR', 'PBJN', 'IPAV', 'OEUR', 'GLBL', 'PBMR', 'DFND', 'PSFO', 'LIAG', 'GBXA', 'LFAR', 'INTM', 'PMJL', 'JUNP', 'PMMY', 'LFAZ', 'SNPV', 'LYLD', 'PMJN', 'LFAN', 'GSEE', 'LIAT', 'PBDE', 'PJFM', 'OCTM', 'NYC', 'PBSE', 'HBTC', 'MGRO', 'LIAF', 'MOTE', 'LFAJ', 'LIAV', 'CSTK', 'PCS', 'WHTX', 'EAOK', 'LIAB', 'FEBZ', 'LIAE', 'PSMR', 'PBFB', 'TDEC', 'HEAT', 'TGLR', 'JUNZ', 'BUYZ', 'SPIN', 'NMAY', 'PMMR', 'LIAO', 'LFAU', 'VMAX', 'LIAC', 'LFBB', 'UXAP', 'LFAK', 'XDAT', 'FLDZ', 'MVAL', 'PJBF', 'SEPM', 'LIBD', 'HYKE', 'LIAX', 'SEPP', 'EDGE', 'SEPU', 'LFAE', 'RVRB', 'BUFH', 'XDOC', 'MAYZ', 'PMFB', 'CVAR', 'LFBE', 'EUDV', 'TJAN', 'SPCZ', 'PBOC', 'PMAP', 'GHMS', 'MARU', 'NOVZ', 'GBXB', 'GBXC', 'LFAF', 'LIAU', 'QIG', 'UXJA', 'LFBD', 'CTIF', 'ZJUN', 'PSCQ', 'PBNV', 'LFAL', 'LFAV', 'PSCW', 'LFDR', 'LFAO', 'LIAJ', 'LIAY']
[2025-08-13 16:10:46 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['CMCI', 'PCL', 'LFAQ', 'SHDG', 'LIAP', 'PMJA', 'PBFR', 'LIAM', 'QBSF', 'LIAQ', 'TJUN', 'LFAX', 'SNOA', 'IPPP', 'LFAI', 'KNOW', 'WSML', 'KVAC', 'LFAW', 'LIAW', 'LDER', 'PBMY', 'LIAK', 'VWID', 'JNEU', 'LDRR', 'PBJN', 'IPAV', 'OEUR', 'GLBL', 'PBMR', 'DFND', 'PSFO', 'LIAG', 'GBXA', 'LFAR', 'INTM', 'PMJL', 'JUNP', 'PMMY', 'LFAZ', 'SNPV', 'LYLD', 'PMJN', 'LFAN', 'GSEE', 'LIAT', 'PBDE', 'PJFM', 'OCTM', 'NYC', 'PBSE', 'HBTC', 'MGRO', 'LIAF', 'MOTE', 'LFAJ', 'LIAV', 'CSTK', 'PCS', 'WHTX', 'EAOK', 'LIAB', 'FEBZ', 'LIAE', 'PSMR', 'PBFB', 'TDEC', 'HEAT', 'TGLR', 'JUNZ', 'BUYZ', 'SPIN', 'NMAY', 'PMMR', 'LIAO', 'LFAU', 'VMAX', 'LIAC', 'LFBB', 'UXAP', 'LFAK', 'XDAT', 'FLDZ', 'MVAL', 'PJBF', 'SEPM', 'LIBD', 'HYKE', 'LIAX', 'SEPP', 'EDGE', 'SEPU', 'LFAE', 'RVRB', 'BUFH', 'XDOC', 'MAYZ', 'PMFB', 'CVAR', 'LFBE', 'EUDV', 'TJAN', 'SPCZ', 'PBOC', 'PMAP', 'GHMS', 'MARU', 'NOVZ', 'GBXB', 'GBXC', 'LFAF', 'LIAU', 'QIG', 'UXJA', 'LFBD', 'CTIF', 'ZJUN', 'PSCQ', 'PBNV', 'LFAL', 'LFAV', 'PSCW', 'LFDR', 'LFAO', 'LIAJ', 'LIAY']
[2025-08-13 16:10:46 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:10:52 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['BUYZ', 'SNOA', 'LIAF', 'PJBF', 'JUNP', 'SPIN', 'PBNV', 'PMJN', 'CMCI', 'CTIF', 'PSFO', 'MOTE', 'LIAE', 'PSMR', 'LIAX', 'GBXC', 'GLBL', 'BUFH', 'PMMY', 'LFAL', 'LFAJ', 'FLDZ', 'LIAM', 'LDRR', 'LDER', 'NOVZ', 'KNOW', 'LIAT', 'LFDR', 'LIAW', 'LIBD', 'LIAC', 'LFAU', 'PJFM', 'LFAZ', 'ZJUN', 'PBFR', 'MGRO', 'PMJA', 'CVAR', 'GBXB', 'LIAP', 'LIAO', 'LFAI', 'SHDG', 'IPAV', 'LFAN', 'UXAP', 'GBXA', 'PMFB', 'JNEU', 'LFBE', 'EDGE', 'TJUN', 'PBFB', 'LFAR', 'HYKE', 'LFAF', 'LIAG', 'SEPM', 'OCTM', 'XDAT', 'VWID', 'PBJN', 'RVRB', 'JUNZ', 'LIAU', 'LIAY', 'SNPV', 'FEBZ', 'TJAN', 'GHMS', 'WSML', 'PCL', 'PMMR', 'CSTK', 'TGLR', 'LIAV', 'INTM', 'PMAP', 'PCS', 'PBMY', 'NMAY', 'LIAK', 'PBMR', 'QIG', 'KVAC', 'LFAX', 'MARU', 'IPPP', 'SEPU', 'DFND', 'LFBB', 'PSCW', 'OEUR', 'LIAB', 'LFAW', 'MVAL', 'NYC', 'XDOC', 'LFAV', 'LYLD', 'SPCZ', 'PSCQ', 'WHTX', 'GSEE', 'LFAO', 'VMAX', 'TDEC', 'PBSE', 'EUDV', 'PBDE', 'PBOC', 'SEPP', 'LFAE', 'QBSF', 'PMJL', 'LIAQ', 'MAYZ', 'HBTC', 'EAOK', 'LFBD', 'UXJA', 'HEAT', 'LFAQ', 'LFAK', 'LIAJ']
[2025-08-13 16:10:52 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['BUYZ', 'SNOA', 'LIAF', 'PJBF', 'JUNP', 'SPIN', 'PBNV', 'PMJN', 'CMCI', 'CTIF', 'PSFO', 'MOTE', 'LIAE', 'PSMR', 'LIAX', 'GBXC', 'GLBL', 'BUFH', 'PMMY', 'LFAL', 'LFAJ', 'FLDZ', 'LIAM', 'LDRR', 'LDER', 'NOVZ', 'KNOW', 'LIAT', 'LFDR', 'LIAW', 'LIBD', 'LIAC', 'LFAU', 'PJFM', 'LFAZ', 'ZJUN', 'PBFR', 'MGRO', 'PMJA', 'CVAR', 'GBXB', 'LIAP', 'LIAO', 'LFAI', 'SHDG', 'IPAV', 'LFAN', 'UXAP', 'GBXA', 'PMFB', 'JNEU', 'LFBE', 'EDGE', 'TJUN', 'PBFB', 'LFAR', 'HYKE', 'LFAF', 'LIAG', 'SEPM', 'OCTM', 'XDAT', 'VWID', 'PBJN', 'RVRB', 'JUNZ', 'LIAU', 'LIAY', 'SNPV', 'FEBZ', 'TJAN', 'GHMS', 'WSML', 'PCL', 'PMMR', 'CSTK', 'TGLR', 'LIAV', 'INTM', 'PMAP', 'PCS', 'PBMY', 'NMAY', 'LIAK', 'PBMR', 'QIG', 'KVAC', 'LFAX', 'MARU', 'IPPP', 'SEPU', 'DFND', 'LFBB', 'PSCW', 'OEUR', 'LIAB', 'LFAW', 'MVAL', 'NYC', 'XDOC', 'LFAV', 'LYLD', 'SPCZ', 'PSCQ', 'WHTX', 'GSEE', 'LFAO', 'VMAX', 'TDEC', 'PBSE', 'EUDV', 'PBDE', 'PBOC', 'SEPP', 'LFAE', 'QBSF', 'PMJL', 'LIAQ', 'MAYZ', 'HBTC', 'EAOK', 'LFBD', 'UXJA', 'HEAT', 'LFAQ', 'LFAK', 'LIAJ']
[2025-08-13 16:10:52 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:10:58 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 127 tickers: ['LIAG', 'GBXA', 'LFAR', 'INTM', 'PMJL', 'JUNP', 'PMMY', 'LFAZ', 'SNPV', 'LYLD', 'PMJN', 'LFAN', 'GSEE', 'LIAT', 'PBDE', 'PJFM', 'OCTM', 'NYC', 'PBSE', 'HBTC', 'MGRO', 'LIAF', 'MOTE', 'LFAJ', 'LIAV', 'CSTK', 'PCS', 'WHTX', 'EAOK', 'LIAB', 'FEBZ', 'LIAE', 'PSMR', 'PBFB', 'TDEC', 'HEAT', 'TGLR', 'JUNZ', 'BUYZ', 'SPIN', 'NMAY', 'PMMR', 'LIAO', 'LFAU', 'VMAX', 'LIAC', 'LFBB', 'UXAP', 'LFAK', 'XDAT', 'FLDZ', 'MVAL', 'PJBF', 'SEPM', 'LIBD', 'HYKE', 'LIAX', 'SEPP', 'EDGE', 'SEPU', 'LFAE', 'RVRB', 'BUFH', 'XDOC', 'MAYZ', 'PMFB', 'CVAR', 'LFBE', 'EUDV', 'TJAN', 'SPCZ', 'PBOC', 'PMAP', 'GHMS', 'MARU', 'NOVZ', 'GBXB', 'GBXC', 'LFAF', 'LIAU', 'QIG', 'UXJA', 'LFBD', 'CTIF', 'ZJUN', 'PSCQ', 'PBNV', 'LFAL', 'LFAV', 'PSCW', 'LFDR', 'LFAO', 'LIAJ', 'LIAY', 'CMCI', 'PCL', 'LFAQ', 'SHDG', 'LIAP', 'PMJA', 'PBFR', 'LIAM', 'QBSF', 'LIAQ', 'TJUN', 'LFAX', 'SNOA', 'IPPP', 'LFAI', 'KNOW', 'WSML', 'KVAC', 'LFAW', 'LIAW', 'LDER', 'PBMY', 'LIAK', 'VWID', 'JNEU', 'LDRR', 'PBJN', 'IPAV', 'OEUR', 'GLBL', 'PBMR', 'DFND', 'PSFO']
[2025-08-13 16:10:58 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['LIAG', 'GBXA', 'LFAR', 'INTM', 'PMJL', 'JUNP', 'PMMY', 'LFAZ', 'SNPV', 'LYLD', 'PMJN', 'LFAN', 'GSEE', 'LIAT', 'PBDE', 'PJFM', 'OCTM', 'NYC', 'PBSE', 'HBTC', 'MGRO', 'LIAF', 'MOTE', 'LFAJ', 'LIAV', 'CSTK', 'PCS', 'WHTX', 'EAOK', 'LIAB', 'FEBZ', 'LIAE', 'PSMR', 'PBFB', 'TDEC', 'HEAT', 'TGLR', 'JUNZ', 'BUYZ', 'SPIN', 'NMAY', 'PMMR', 'LIAO', 'LFAU', 'VMAX', 'LIAC', 'LFBB', 'UXAP', 'LFAK', 'XDAT', 'FLDZ', 'MVAL', 'PJBF', 'SEPM', 'LIBD', 'HYKE', 'LIAX', 'SEPP', 'EDGE', 'SEPU', 'LFAE', 'RVRB', 'BUFH', 'XDOC', 'MAYZ', 'PMFB', 'CVAR', 'LFBE', 'EUDV', 'TJAN', 'SPCZ', 'PBOC', 'PMAP', 'GHMS', 'MARU', 'NOVZ', 'GBXB', 'GBXC', 'LFAF', 'LIAU', 'QIG', 'UXJA', 'LFBD', 'CTIF', 'ZJUN', 'PSCQ', 'PBNV', 'LFAL', 'LFAV', 'PSCW', 'LFDR', 'LFAO', 'LIAJ', 'LIAY', 'CMCI', 'PCL', 'LFAQ', 'SHDG', 'LIAP', 'PMJA', 'PBFR', 'LIAM', 'QBSF', 'LIAQ', 'TJUN', 'LFAX', 'SNOA', 'IPPP', 'LFAI', 'KNOW', 'WSML', 'KVAC', 'LFAW', 'LIAW', 'LDER', 'PBMY', 'LIAK', 'VWID', 'JNEU', 'LDRR', 'PBJN', 'IPAV', 'OEUR', 'GLBL', 'PBMR', 'DFND', 'PSFO']
[2025-08-13 16:10:58 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:11:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=82 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:11:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:11:00-04:00, current_time=16:11:00
[2025-08-13 16:11:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=6091441.73 (min=250000.00)
[2025-08-13 16:11:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4642, last_open=4.6002, last_close=4.7000, red_bar=False, below_ma=False
[2025-08-13 16:11:05 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['SNOA', 'KVAC', 'WSML', 'NYC']
[2025-08-13 16:11:05 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'WSML', 'NYC']
[2025-08-13 16:11:05 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:11:11 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['NYC', 'KVAC', 'WSML', 'SNOA']
[2025-08-13 16:11:11 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'WSML', 'SNOA']
[2025-08-13 16:11:11 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:11:17 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['NYC', 'KVAC', 'WSML', 'SNOA']
[2025-08-13 16:11:17 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'WSML', 'SNOA']
[2025-08-13 16:11:17 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:11:23 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['NYC', 'KVAC', 'WSML', 'SNOA']
[2025-08-13 16:11:23 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'WSML', 'SNOA']
[2025-08-13 16:11:23 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:11:29 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['SNOA', 'WSML', 'KVAC', 'NYC']
[2025-08-13 16:11:29 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'WSML', 'KVAC', 'NYC']
[2025-08-13 16:11:29 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:11:48 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['SNOA', 'KVAC', 'NYC', 'WSML']
[2025-08-13 16:11:48 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC', 'WSML']
[2025-08-13 16:11:48 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:11:54 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['NYC', 'SNOA', 'KVAC', 'WSML']
[2025-08-13 16:11:54 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC', 'WSML']
[2025-08-13 16:11:54 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:12:00 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 4 tickers: ['KVAC', 'NYC', 'SNOA', 'WSML']
[2025-08-13 16:12:00 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA', 'WSML']
[2025-08-13 16:12:00 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:12:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=83 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:12:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:12:00-04:00, current_time=16:12:00
[2025-08-13 16:12:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=6645494.82 (min=250000.00)
[2025-08-13 16:12:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5012, last_open=4.7200, last_close=4.5700, red_bar=True, below_ma=False
[2025-08-13 16:13:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=84 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:13:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:13:00-04:00, current_time=16:13:00
[2025-08-13 16:13:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=6939346.86 (min=250000.00)
[2025-08-13 16:13:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5042, last_open=4.5900, last_close=4.5300, red_bar=True, below_ma=False
[2025-08-13 16:14:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=85 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 16:14:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:14:00-04:00, current_time=16:14:00
[2025-08-13 16:14:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=7314176.06 (min=250000.00)
[2025-08-13 16:14:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4732, last_open=4.5300, last_close=4.3600, red_bar=True, below_ma=True
[2025-08-13 16:14:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.36 (stop 10.90; dollar_vol=7314176.06)
[2025-08-13 16:14:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.36 (stop 10.90)
[2025-08-13 16:14:08 EDT] INFO - orchestrator.execution_orchestrator - Executed SELL order for SNOA: {'created_at': *************, 'updated_at': *************, 'order_id': '20250813_112982_23', 'reference_id': 'SNOA_f84e84', 'version': 3, 'account_id': '112982', 'account_number': '112982', 'state': 'closed', 'status': 'filled', 'symbol': 'SNOA', 'order_type': 'limit', 'side': 'sell-short', 'quantity': '22', 'price': '4.36', 'time_in_force': 'day-plus', 'average_price': 4.38, 'filled_quantity': '22', 'order_update_reason': 'execution-report', 'text': '', 'strategy': {'type': 'sor', 'urgency': 'moderate'}, 'running_position': '-22'}
[2025-08-13 16:14:08 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=20250813_112982_23, outcome=ExecOutcome.FILLED, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=22
[2025-08-13 16:14:08 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Execution filled - signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=22
[2025-08-13 16:15:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=86 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:15:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:15:00-04:00, current_time=16:15:00
[2025-08-13 16:15:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.5000, stop_price=10.9000
[2025-08-13 16:15:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:16:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=87 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:16:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:16:00-04:00, current_time=16:16:00
[2025-08-13 16:16:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.6200, stop_price=10.9000
[2025-08-13 16:16:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:16:10 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:16:10 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:16:10 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:16:22 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:16:22 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:16:22 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:16:29 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:16:29 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:16:29 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:16:35 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:16:35 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:16:35 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:16:41 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:16:41 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:16:41 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:16:48 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:16:48 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:16:48 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:16:54 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:16:54 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:16:54 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:17:00 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:17:00 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:17:00 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:17:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=88 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:17:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:17:00-04:00, current_time=16:17:00
[2025-08-13 16:17:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.8100, stop_price=10.9000
[2025-08-13 16:17:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:17:07 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:17:07 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:17:07 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:17:13 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:17:13 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:17:13 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:17:19 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:17:19 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:17:19 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:17:26 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:17:26 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:17:26 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:17:32 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:17:32 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:17:32 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:17:38 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:17:38 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:17:38 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:17:44 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:17:44 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:17:44 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:17:51 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:17:51 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:17:51 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:17:57 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:17:57 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:17:57 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:18:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=89 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:18:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:18:00-04:00, current_time=16:18:00
[2025-08-13 16:18:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.8800, stop_price=10.9000
[2025-08-13 16:18:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:18:03 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:03 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:03 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:18:10 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:10 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:10 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:18:16 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:16 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:16 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:18:22 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:18:22 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:18:22 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:18:29 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:18:29 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:18:29 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:18:35 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:18:35 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:18:35 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:18:41 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:18:41 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:18:41 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:18:47 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:47 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:47 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:18:54 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:54 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:18:54 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:19:00 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:19:00 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:19:00 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:19:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=90 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:19:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:19:00-04:00, current_time=16:19:00
[2025-08-13 16:19:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.8600, stop_price=10.9000
[2025-08-13 16:19:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:19:06 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:19:06 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:19:06 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:19:12 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:19:12 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:19:12 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:19:18 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:19:18 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:19:18 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:19:24 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:19:24 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:19:24 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:19:30 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:19:30 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:19:30 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:19:37 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:19:37 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:19:37 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:19:43 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:19:43 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:19:43 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:19:49 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:19:49 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:19:49 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:19:55 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:19:55 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:19:55 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:20:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=91 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:20:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:20:00-04:00, current_time=16:20:00
[2025-08-13 16:20:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.7800, stop_price=10.9000
[2025-08-13 16:20:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:20:01 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:20:01 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:20:01 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:20:08 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:20:08 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:20:08 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:20:14 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:20:14 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:20:14 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:20:20 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:20:20 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:20:20 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:20:26 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:20:26 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:20:26 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:20:32 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:20:32 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:20:32 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:20:38 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:20:38 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:20:38 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:20:44 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:20:44 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:20:44 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:20:51 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:20:51 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:20:51 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:20:57 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:20:57 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:20:57 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:21:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=92 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:21:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:21:00-04:00, current_time=16:21:00
[2025-08-13 16:21:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.8700, stop_price=10.9000
[2025-08-13 16:21:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:21:03 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:21:03 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:21:03 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:21:09 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:21:09 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:21:09 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:21:15 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:21:15 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:21:15 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:21:22 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:21:22 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:21:22 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:21:28 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:21:28 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:21:28 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:21:34 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:21:34 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:21:34 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:21:40 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:21:40 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:21:40 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:21:46 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:21:46 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:21:46 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:21:53 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:21:53 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:21:53 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:21:59 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:21:59 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:21:59 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:22:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=93 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:22:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:22:00-04:00, current_time=16:22:00
[2025-08-13 16:22:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.8600, stop_price=10.9000
[2025-08-13 16:22:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:22:05 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:05 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:05 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:22:11 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:11 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:11 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:22:17 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:22:17 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:22:17 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:22:23 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:22:23 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:22:23 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:22:30 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:30 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:30 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:22:36 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:22:36 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:22:36 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:22:42 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:22:42 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:22:42 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:22:48 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:48 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:48 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:22:54 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:54 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:22:54 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:23:00 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:00 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:00 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:23:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=94 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:23:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:23:00-04:00, current_time=16:23:00
[2025-08-13 16:23:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.9300, stop_price=10.9000
[2025-08-13 16:23:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:23:06 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:07 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:07 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:23:13 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:13 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:13 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:23:19 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:19 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:19 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:23:25 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:23:25 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:23:25 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:23:32 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:23:32 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:23:32 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:23:38 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:23:38 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:23:38 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:23:44 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:44 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:23:44 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:23:50 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:23:50 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:23:50 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:23:56 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:23:56 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:23:56 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:24:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=95 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:24:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:24:00-04:00, current_time=16:24:00
[2025-08-13 16:24:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.8903, stop_price=10.9000
[2025-08-13 16:24:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:24:03 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:24:03 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:24:03 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:24:09 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:24:09 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:24:09 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:24:15 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:24:15 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:24:15 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:24:21 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:24:21 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:24:21 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:24:27 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:24:27 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:24:27 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:24:34 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:24:34 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:24:34 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:24:40 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:24:40 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:24:40 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:24:47 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:24:47 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:24:47 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:24:53 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:24:53 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:24:53 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:24:59 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:24:59 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:24:59 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:25:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=96 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:25:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:25:00-04:00, current_time=16:25:00
[2025-08-13 16:25:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.7500, stop_price=10.9000
[2025-08-13 16:25:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:25:06 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:25:06 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:25:06 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:25:12 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:25:12 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:25:12 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:25:18 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:25:18 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:25:18 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:25:25 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:25:25 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:25:25 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:25:31 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:25:31 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:25:31 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:25:37 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:25:37 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:25:37 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:25:44 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:25:44 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:25:44 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:25:50 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:25:50 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:25:50 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:25:56 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:25:56 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:25:56 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:26:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=97 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:26:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:26:00-04:00, current_time=16:26:00
[2025-08-13 16:26:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.6900, stop_price=10.9000
[2025-08-13 16:26:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:26:02 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:26:02 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:26:02 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:26:08 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:26:08 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:26:08 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:26:15 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:26:15 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'SNOA', 'NYC']
[2025-08-13 16:26:15 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'SNOA', 'NYC']..
[2025-08-13 16:26:21 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:26:21 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:26:21 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:26:27 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:26:27 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:26:27 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:26:33 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:26:33 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:26:33 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:26:39 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:26:39 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:26:39 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:26:46 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:26:46 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:26:46 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:26:52 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:26:52 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'SNOA', 'KVAC']
[2025-08-13 16:26:52 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'SNOA', 'KVAC']..
[2025-08-13 16:26:58 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:26:58 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:26:58 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:27:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=98 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:27:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:27:00-04:00, current_time=16:27:00
[2025-08-13 16:27:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.7000, stop_price=10.9000
[2025-08-13 16:27:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:27:04 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:27:04 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:27:04 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:27:10 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:10 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:10 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:27:16 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:16 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:16 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:27:23 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:27:23 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:27:23 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:27:29 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:29 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:29 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:27:35 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:27:35 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:27:35 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:27:41 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:41 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:41 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:27:47 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:47 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:27:47 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:27:53 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:27:53 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'NYC', 'KVAC']
[2025-08-13 16:27:53 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'NYC', 'KVAC']..
[2025-08-13 16:28:00 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:28:00 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:28:00 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:28:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=99 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:28:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:28:00-04:00, current_time=16:28:00
[2025-08-13 16:28:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.6700, stop_price=10.9000
[2025-08-13 16:28:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:28:06 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:28:06 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'KVAC', 'NYC']
[2025-08-13 16:28:06 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC']..
[2025-08-13 16:28:12 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:28:12 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'KVAC', 'SNOA']
[2025-08-13 16:28:12 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'KVAC', 'SNOA']..
[2025-08-13 16:28:18 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 3 tickers: ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:28:18 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['KVAC', 'NYC', 'SNOA']
[2025-08-13 16:28:18 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['KVAC', 'NYC', 'SNOA']..
[2025-08-13 16:29:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=100 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:29:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:29:00-04:00, current_time=16:29:00
[2025-08-13 16:29:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.6200, stop_price=10.9000
[2025-08-13 16:29:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 16:30:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=101 bars, latest=1 bars, in_pos=True, traded=True
[2025-08-13 16:30:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 16:30:00-04:00, current_time=16:30:00
[2025-08-13 16:30:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - stop check: side=SHORT, last_high=4.5000, stop_price=10.9000
[2025-08-13 16:30:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - already traded today; skipping
[2025-08-13 18:36:47 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 6 tickers: ['XPON', 'GLUpA', 'NYC', 'KVAC', 'FGMCU', 'SNOA']
[2025-08-13 18:36:47 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['XPON', 'GLUpA', 'NYC', 'KVAC', 'FGMCU', 'SNOA']
[2025-08-13 18:36:47 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['XPON', 'NYC', 'KVAC', 'SNOA']..
[2025-08-13 18:36:47 EDT] INFO - orchestrator.execution_orchestrator - Started processing ticker: SNOA
[2025-08-13 18:36:47 EDT] INFO - orchestrator.execution_orchestrator - Processing ticker SNOA with interval 0:01:00
[2025-08-13 18:36:47 EDT] INFO - orchestrator.execution_orchestrator - Strategy requested interval 0:01:00 and lookback 5 days, 0:00:00 for ticker SNOA
[2025-08-13 18:36:47 EDT] INFO - marketdata.polygon_streaming_market_data - Connecting to Polygon WebSocket for SNOA_60...
[2025-08-13 18:36:47 EDT] INFO - marketdata.polygon_streaming_market_data - Sent subscription request for AM.SNOA
[2025-08-13 18:36:47 EDT] INFO - marketdata.polygon_streaming_market_data - WebSocket receiver task started for SNOA_60.
[2025-08-13 18:36:47 EDT] INFO - marketdata.polygon_streaming_market_data - Starting message processor for SNOA_60...
[2025-08-13 18:37:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=228 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:37:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:37:00-04:00, current_time=18:37:00
[2025-08-13 18:37:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20377159.28 (min=250000.00)
[2025-08-13 18:37:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4393, last_open=4.5000, last_close=4.5400, red_bar=False, below_ma=False
[2025-08-13 18:37:06 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 6 tickers: ['SNOA', 'FGMCU', 'KVAC', 'NYC', 'XPON', 'GLUpA']
[2025-08-13 18:37:06 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['SNOA', 'FGMCU', 'KVAC', 'NYC', 'XPON', 'GLUpA']
[2025-08-13 18:37:06 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['SNOA', 'KVAC', 'NYC', 'XPON']..
[2025-08-13 18:37:12 EDT] INFO - strategies.post_market_gap_short_strategy - Post‑market scan found 6 tickers: ['NYC', 'GLUpA', 'FGMCU', 'XPON', 'SNOA', 'KVAC']
[2025-08-13 18:37:12 EDT] INFO - tickers.ticker_info_scan_criteria - Got tickers from delegate criteria ['NYC', 'GLUpA', 'FGMCU', 'XPON', 'SNOA', 'KVAC']
[2025-08-13 18:37:12 EDT] INFO - tickers.ticker_info_scan_criteria - Tickers after filtering criteria and blocklist ['NYC', 'XPON', 'SNOA', 'KVAC']..
[2025-08-13 18:38:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=229 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:38:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:38:00-04:00, current_time=18:38:00
[2025-08-13 18:38:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20469127.72 (min=250000.00)
[2025-08-13 18:38:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4523, last_open=4.5500, last_close=4.5200, red_bar=True, below_ma=False
[2025-08-13 18:39:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=230 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:39:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:39:00-04:00, current_time=18:39:00
[2025-08-13 18:39:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20484197.40 (min=250000.00)
[2025-08-13 18:39:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4633, last_open=4.5200, last_close=4.5200, red_bar=False, below_ma=False
[2025-08-13 18:40:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=231 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:40:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:40:00-04:00, current_time=18:40:00
[2025-08-13 18:40:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20493261.93 (min=250000.00)
[2025-08-13 18:40:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4740, last_open=4.5200, last_close=4.5300, red_bar=False, below_ma=False
[2025-08-13 18:41:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=232 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:41:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:41:00-04:00, current_time=18:41:00
[2025-08-13 18:41:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20519414.65 (min=250000.00)
[2025-08-13 18:41:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4860, last_open=4.5300, last_close=4.5200, red_bar=True, below_ma=False
[2025-08-13 18:42:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=233 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:42:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:42:00-04:00, current_time=18:42:00
[2025-08-13 18:42:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20555518.35 (min=250000.00)
[2025-08-13 18:42:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4910, last_open=4.5000, last_close=4.4600, red_bar=True, below_ma=True
[2025-08-13 18:42:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.46 (stop 11.15; dollar_vol=20555518.35)
[2025-08-13 18:42:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.46 (stop 11.15)
[2025-08-13 18:42:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: market price below expected price 4.46
[2025-08-13 18:42:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: market price below expected price 4.46
[2025-08-13 18:42:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: market price below expected price 4.46
[2025-08-13 18:42:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 18:42:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: market price below expected price 4.46. Resetting day context.
[2025-08-13 18:43:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=234 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:43:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:43:00-04:00, current_time=18:43:00
[2025-08-13 18:43:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20566465.35 (min=250000.00)
[2025-08-13 18:43:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4930, last_open=4.4100, last_close=4.4500, red_bar=False, below_ma=True
[2025-08-13 18:44:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=235 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:44:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:44:00-04:00, current_time=18:44:00
[2025-08-13 18:44:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20599237.86 (min=250000.00)
[2025-08-13 18:44:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5000, last_open=4.4500, last_close=4.4900, red_bar=False, below_ma=True
[2025-08-13 18:45:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=236 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:45:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:45:00-04:00, current_time=18:45:00
[2025-08-13 18:45:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20645102.34 (min=250000.00)
[2025-08-13 18:45:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5090, last_open=4.4800, last_close=4.5600, red_bar=False, below_ma=False
[2025-08-13 18:46:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=237 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:46:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:46:00-04:00, current_time=18:46:00
[2025-08-13 18:46:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20662819.17 (min=250000.00)
[2025-08-13 18:46:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5120, last_open=4.5500, last_close=4.5300, red_bar=True, below_ma=False
[2025-08-13 18:47:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=238 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:47:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:47:00-04:00, current_time=18:47:00
[2025-08-13 18:47:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20679874.62 (min=250000.00)
[2025-08-13 18:47:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5110, last_open=4.5300, last_close=4.5300, red_bar=False, below_ma=False
[2025-08-13 18:48:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=239 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:48:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:48:00-04:00, current_time=18:48:00
[2025-08-13 18:48:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20713772.61 (min=250000.00)
[2025-08-13 18:48:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5120, last_open=4.5300, last_close=4.5300, red_bar=False, below_ma=False
[2025-08-13 18:49:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=240 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:49:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:49:00-04:00, current_time=18:49:00
[2025-08-13 18:49:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20723027.13 (min=250000.00)
[2025-08-13 18:49:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5110, last_open=4.5000, last_close=4.5100, red_bar=False, below_ma=True
[2025-08-13 18:51:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=241 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:51:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:51:00-04:00, current_time=18:51:00
[2025-08-13 18:51:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20738175.45 (min=250000.00)
[2025-08-13 18:51:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5110, last_open=4.5200, last_close=4.5300, red_bar=False, below_ma=False
[2025-08-13 18:52:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=242 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:52:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:52:00-04:00, current_time=18:52:00
[2025-08-13 18:52:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20757257.26 (min=250000.00)
[2025-08-13 18:52:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5100, last_open=4.5200, last_close=4.5100, red_bar=True, below_ma=False
[2025-08-13 18:53:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=243 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:53:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:53:00-04:00, current_time=18:53:00
[2025-08-13 18:53:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20767550.94 (min=250000.00)
[2025-08-13 18:53:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5100, last_open=4.5000, last_close=4.4600, red_bar=True, below_ma=True
[2025-08-13 18:53:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.46 (stop 11.15; dollar_vol=20767550.94)
[2025-08-13 18:53:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.46 (stop 11.15)
[2025-08-13 18:53:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.34% > cap 1.25%
[2025-08-13 18:53:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.34% > cap 1.25%
[2025-08-13 18:53:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.34% > cap 1.25%
[2025-08-13 18:53:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 18:53:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.34% > cap 1.25%. Resetting day context.
[2025-08-13 18:54:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=244 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:54:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:54:00-04:00, current_time=18:54:00
[2025-08-13 18:54:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20776377.66 (min=250000.00)
[2025-08-13 18:54:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5090, last_open=4.4900, last_close=4.4400, red_bar=True, below_ma=True
[2025-08-13 18:54:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.44 (stop 11.10; dollar_vol=20776377.66)
[2025-08-13 18:54:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.44 (stop 11.10)
[2025-08-13 18:54:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 18:54:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 18:54:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 18:54:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 18:54:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.35% > cap 1.25%. Resetting day context.
[2025-08-13 18:55:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=245 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:55:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:55:00-04:00, current_time=18:55:00
[2025-08-13 18:55:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20786995.36 (min=250000.00)
[2025-08-13 18:55:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.5050, last_open=4.4400, last_close=4.4500, red_bar=False, below_ma=True
[2025-08-13 18:56:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=246 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:56:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:56:00-04:00, current_time=18:56:00
[2025-08-13 18:56:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20827995.28 (min=250000.00)
[2025-08-13 18:56:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4910, last_open=4.4400, last_close=4.4200, red_bar=True, below_ma=True
[2025-08-13 18:56:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.42 (stop 11.05; dollar_vol=20827995.28)
[2025-08-13 18:56:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.42 (stop 11.05)
[2025-08-13 18:56:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 18:56:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 18:56:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 18:56:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 18:56:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.35% > cap 1.25%. Resetting day context.
[2025-08-13 18:57:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=247 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:57:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:57:00-04:00, current_time=18:57:00
[2025-08-13 18:57:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20831452.72 (min=250000.00)
[2025-08-13 18:57:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4790, last_open=4.4200, last_close=4.4100, red_bar=True, below_ma=True
[2025-08-13 18:57:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.41 (stop 11.03; dollar_vol=20831452.72)
[2025-08-13 18:57:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.41 (stop 11.03)
[2025-08-13 18:57:06 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 18:57:06 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 18:57:06 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 18:57:06 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 18:57:06 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.36% > cap 1.25%. Resetting day context.
[2025-08-13 18:58:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=248 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:58:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:58:00-04:00, current_time=18:58:00
[2025-08-13 18:58:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20851680.10 (min=250000.00)
[2025-08-13 18:58:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4690, last_open=4.4200, last_close=4.4300, red_bar=False, below_ma=True
[2025-08-13 18:59:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=249 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 18:59:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 18:59:00-04:00, current_time=18:59:00
[2025-08-13 18:59:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20883199.55 (min=250000.00)
[2025-08-13 18:59:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4590, last_open=4.4300, last_close=4.4300, red_bar=False, below_ma=True
[2025-08-13 19:00:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=250 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:00:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:00:00-04:00, current_time=19:00:00
[2025-08-13 19:00:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20908928.37 (min=250000.00)
[2025-08-13 19:00:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4500, last_open=4.4200, last_close=4.4200, red_bar=False, below_ma=True
[2025-08-13 19:01:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=251 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:01:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:01:00-04:00, current_time=19:01:00
[2025-08-13 19:01:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20917012.55 (min=250000.00)
[2025-08-13 19:01:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4390, last_open=4.4200, last_close=4.4200, red_bar=False, below_ma=True
[2025-08-13 19:02:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=252 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:02:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:02:00-04:00, current_time=19:02:00
[2025-08-13 19:02:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20928954.83 (min=250000.00)
[2025-08-13 19:02:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4290, last_open=4.4100, last_close=4.4100, red_bar=False, below_ma=True
[2025-08-13 19:03:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=253 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:03:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:03:00-04:00, current_time=19:03:00
[2025-08-13 19:03:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20933281.43 (min=250000.00)
[2025-08-13 19:03:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4245, last_open=4.4100, last_close=4.4149, red_bar=False, below_ma=True
[2025-08-13 19:04:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=254 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:04:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:04:00-04:00, current_time=19:04:00
[2025-08-13 19:04:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20940678.47 (min=250000.00)
[2025-08-13 19:04:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4245, last_open=4.4100, last_close=4.4400, red_bar=False, below_ma=False
[2025-08-13 19:05:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=255 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:05:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:05:00-04:00, current_time=19:05:00
[2025-08-13 19:05:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20951155.42 (min=250000.00)
[2025-08-13 19:05:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4225, last_open=4.4400, last_close=4.4300, red_bar=True, below_ma=False
[2025-08-13 19:06:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=256 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:06:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:06:00-04:00, current_time=19:06:00
[2025-08-13 19:06:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20955305.80 (min=250000.00)
[2025-08-13 19:06:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4225, last_open=4.4200, last_close=4.4200, red_bar=False, below_ma=True
[2025-08-13 19:07:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=257 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:07:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:07:00-04:00, current_time=19:07:00
[2025-08-13 19:07:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20965570.11 (min=250000.00)
[2025-08-13 19:07:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4245, last_open=4.4200, last_close=4.4300, red_bar=False, below_ma=False
[2025-08-13 19:08:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=258 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:08:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:08:00-04:00, current_time=19:08:00
[2025-08-13 19:08:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20979405.16 (min=250000.00)
[2025-08-13 19:08:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4265, last_open=4.4200, last_close=4.4500, red_bar=False, below_ma=False
[2025-08-13 19:09:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=259 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:09:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:09:00-04:00, current_time=19:09:00
[2025-08-13 19:09:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20980820.26 (min=250000.00)
[2025-08-13 19:09:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4285, last_open=4.4500, last_close=4.4500, red_bar=False, below_ma=False
[2025-08-13 19:10:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=260 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:10:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:10:00-04:00, current_time=19:10:00
[2025-08-13 19:10:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20986051.84 (min=250000.00)
[2025-08-13 19:10:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4325, last_open=4.4700, last_close=4.4600, red_bar=True, below_ma=False
[2025-08-13 19:11:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=261 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:11:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:11:00-04:00, current_time=19:11:00
[2025-08-13 19:11:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=20999178.24 (min=250000.00)
[2025-08-13 19:11:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4385, last_open=4.4700, last_close=4.4800, red_bar=False, below_ma=False
[2025-08-13 19:12:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=262 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:12:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:12:00-04:00, current_time=19:12:00
[2025-08-13 19:12:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21001758.72 (min=250000.00)
[2025-08-13 19:12:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4455, last_open=4.4800, last_close=4.4800, red_bar=False, below_ma=False
[2025-08-13 19:13:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=263 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:13:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:13:00-04:00, current_time=19:13:00
[2025-08-13 19:13:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21005603.52 (min=250000.00)
[2025-08-13 19:13:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4490, last_open=4.4400, last_close=4.4500, red_bar=False, below_ma=False
[2025-08-13 19:14:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=264 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:14:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:14:00-04:00, current_time=19:14:00
[2025-08-13 19:14:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21012800.22 (min=250000.00)
[2025-08-13 19:14:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4520, last_open=4.4700, last_close=4.4700, red_bar=False, below_ma=False
[2025-08-13 19:15:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=265 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:15:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:15:00-04:00, current_time=19:15:00
[2025-08-13 19:15:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21015984.66 (min=250000.00)
[2025-08-13 19:15:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4550, last_open=4.4600, last_close=4.4600, red_bar=False, below_ma=False
[2025-08-13 19:16:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=266 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:16:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:16:00-04:00, current_time=19:16:00
[2025-08-13 19:16:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21017008.16 (min=250000.00)
[2025-08-13 19:16:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4580, last_open=4.4500, last_close=4.4500, red_bar=False, below_ma=True
[2025-08-13 19:17:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=267 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:17:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:17:00-04:00, current_time=19:17:00
[2025-08-13 19:17:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21024889.11 (min=250000.00)
[2025-08-13 19:17:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4600, last_open=4.4400, last_close=4.4500, red_bar=False, below_ma=True
[2025-08-13 19:18:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=268 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:18:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:18:00-04:00, current_time=19:18:00
[2025-08-13 19:18:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21031381.66 (min=250000.00)
[2025-08-13 19:18:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4600, last_open=4.4500, last_close=4.4500, red_bar=False, below_ma=True
[2025-08-13 19:19:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=269 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:19:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:19:00-04:00, current_time=19:19:00
[2025-08-13 19:19:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21036734.89 (min=250000.00)
[2025-08-13 19:19:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4600, last_open=4.4500, last_close=4.4499, red_bar=True, below_ma=True
[2025-08-13 19:19:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.45 (stop 11.12; dollar_vol=21036734.89)
[2025-08-13 19:19:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.45 (stop 11.12)
[2025-08-13 19:19:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: market price below expected price 4.45
[2025-08-13 19:19:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: market price below expected price 4.45
[2025-08-13 19:19:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: market price below expected price 4.45
[2025-08-13 19:19:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:19:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: market price below expected price 4.45. Resetting day context.
[2025-08-13 19:21:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=270 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:21:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:21:00-04:00, current_time=19:21:00
[2025-08-13 19:21:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21064458.25 (min=250000.00)
[2025-08-13 19:21:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4600, last_open=4.4400, last_close=4.4600, red_bar=False, below_ma=False
[2025-08-13 19:22:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=271 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:22:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:22:00-04:00, current_time=19:22:00
[2025-08-13 19:22:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21067207.30 (min=250000.00)
[2025-08-13 19:22:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4590, last_open=4.5000, last_close=4.4700, red_bar=True, below_ma=False
[2025-08-13 19:23:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=272 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:23:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:23:00-04:00, current_time=19:23:00
[2025-08-13 19:23:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21071593.35 (min=250000.00)
[2025-08-13 19:23:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4599, last_open=4.4720, last_close=4.4893, red_bar=False, below_ma=False
[2025-08-13 19:24:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=273 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:24:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:24:00-04:00, current_time=19:24:00
[2025-08-13 19:24:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21075151.78 (min=250000.00)
[2025-08-13 19:24:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4620, last_open=4.4704, last_close=4.4704, red_bar=False, below_ma=False
[2025-08-13 19:25:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=274 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:25:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:25:00-04:00, current_time=19:25:00
[2025-08-13 19:25:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21089451.94 (min=250000.00)
[2025-08-13 19:25:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4630, last_open=4.4700, last_close=4.4800, red_bar=False, below_ma=False
[2025-08-13 19:27:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=275 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:27:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:27:00-04:00, current_time=19:27:00
[2025-08-13 19:27:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21099063.24 (min=250000.00)
[2025-08-13 19:27:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4630, last_open=4.4900, last_close=4.4600, red_bar=True, below_ma=True
[2025-08-13 19:27:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.46 (stop 11.15; dollar_vol=21099063.24)
[2025-08-13 19:27:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.46 (stop 11.15)
[2025-08-13 19:27:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.34% > cap 1.25%
[2025-08-13 19:27:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.34% > cap 1.25%
[2025-08-13 19:27:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.34% > cap 1.25%
[2025-08-13 19:27:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:27:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.34% > cap 1.25%. Resetting day context.
[2025-08-13 19:28:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=276 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:28:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:28:00-04:00, current_time=19:28:00
[2025-08-13 19:28:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21104851.40 (min=250000.00)
[2025-08-13 19:28:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4660, last_open=4.4800, last_close=4.4800, red_bar=False, below_ma=False
[2025-08-13 19:29:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=277 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:29:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:29:00-04:00, current_time=19:29:00
[2025-08-13 19:29:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21109090.40 (min=250000.00)
[2025-08-13 19:29:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4710, last_open=4.4800, last_close=4.5000, red_bar=False, below_ma=False
[2025-08-13 19:30:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=278 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:30:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:30:00-04:00, current_time=19:30:00
[2025-08-13 19:30:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21114009.44 (min=250000.00)
[2025-08-13 19:30:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4740, last_open=4.4900, last_close=4.4800, red_bar=True, below_ma=False
[2025-08-13 19:31:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=279 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:31:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:31:00-04:00, current_time=19:31:00
[2025-08-13 19:31:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21121114.22 (min=250000.00)
[2025-08-13 19:31:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4750, last_open=4.4600, last_close=4.4600, red_bar=False, below_ma=True
[2025-08-13 19:32:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=280 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:32:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:32:00-04:00, current_time=19:32:00
[2025-08-13 19:32:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21129307.73 (min=250000.00)
[2025-08-13 19:32:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4760, last_open=4.4600, last_close=4.4700, red_bar=False, below_ma=True
[2025-08-13 19:34:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=281 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:34:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:34:00-04:00, current_time=19:34:00
[2025-08-13 19:34:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21143032.18 (min=250000.00)
[2025-08-13 19:34:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4720, last_open=4.4698, last_close=4.4301, red_bar=True, below_ma=True
[2025-08-13 19:34:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.43 (stop 11.08; dollar_vol=21143032.18)
[2025-08-13 19:34:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.43 (stop 11.08)
[2025-08-13 19:34:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:34:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:34:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:34:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:34:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.35% > cap 1.25%. Resetting day context.
[2025-08-13 19:35:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=282 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:35:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:35:00-04:00, current_time=19:35:00
[2025-08-13 19:35:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21148520.02 (min=250000.00)
[2025-08-13 19:35:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4671, last_open=4.4600, last_close=4.4400, red_bar=True, below_ma=True
[2025-08-13 19:35:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.44 (stop 11.10; dollar_vol=21148520.02)
[2025-08-13 19:35:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.44 (stop 11.10)
[2025-08-13 19:35:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:35:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:35:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:35:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:35:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.35% > cap 1.25%. Resetting day context.
[2025-08-13 19:36:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=283 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:36:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:36:00-04:00, current_time=19:36:00
[2025-08-13 19:36:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21154994.77 (min=250000.00)
[2025-08-13 19:36:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4650, last_open=4.4500, last_close=4.4500, red_bar=False, below_ma=True
[2025-08-13 19:37:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=284 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:37:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:37:00-04:00, current_time=19:37:00
[2025-08-13 19:37:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21164284.48 (min=250000.00)
[2025-08-13 19:37:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4600, last_open=4.4500, last_close=4.4300, red_bar=True, below_ma=True
[2025-08-13 19:37:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.43 (stop 11.07; dollar_vol=21164284.48)
[2025-08-13 19:37:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.43 (stop 11.07)
[2025-08-13 19:37:08 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:37:08 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:37:08 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.35% > cap 1.25%
[2025-08-13 19:37:08 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:37:08 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.35% > cap 1.25%. Resetting day context.
[2025-08-13 19:38:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=285 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:38:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:38:00-04:00, current_time=19:38:00
[2025-08-13 19:38:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21172059.26 (min=250000.00)
[2025-08-13 19:38:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4560, last_open=4.4300, last_close=4.4200, red_bar=True, below_ma=True
[2025-08-13 19:38:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.42 (stop 11.05; dollar_vol=21172059.26)
[2025-08-13 19:38:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.42 (stop 11.05)
[2025-08-13 19:38:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: market price below expected price 4.42
[2025-08-13 19:38:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: market price below expected price 4.42
[2025-08-13 19:38:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: market price below expected price 4.42
[2025-08-13 19:38:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:38:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: market price below expected price 4.42. Resetting day context.
[2025-08-13 19:39:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=286 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:39:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:39:00-04:00, current_time=19:39:00
[2025-08-13 19:39:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21182343.38 (min=250000.00)
[2025-08-13 19:39:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4490, last_open=4.4200, last_close=4.4100, red_bar=True, below_ma=True
[2025-08-13 19:39:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.41 (stop 11.03; dollar_vol=21182343.38)
[2025-08-13 19:39:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.41 (stop 11.03)
[2025-08-13 19:39:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 19:39:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 19:39:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 19:39:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:39:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.36% > cap 1.25%. Resetting day context.
[2025-08-13 19:40:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=287 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:40:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:40:00-04:00, current_time=19:40:00
[2025-08-13 19:40:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21229418.98 (min=250000.00)
[2025-08-13 19:40:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4390, last_open=4.4100, last_close=4.4000, red_bar=True, below_ma=True
[2025-08-13 19:40:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.40 (stop 11.00; dollar_vol=21229418.98)
[2025-08-13 19:40:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.40 (stop 11.00)
[2025-08-13 19:40:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 19:40:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 19:40:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.36% > cap 1.25%
[2025-08-13 19:40:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:40:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.36% > cap 1.25%. Resetting day context.
[2025-08-13 19:41:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=288 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:41:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:41:00-04:00, current_time=19:41:00
[2025-08-13 19:41:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21241873.41 (min=250000.00)
[2025-08-13 19:41:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4300, last_open=4.4000, last_close=4.3900, red_bar=True, below_ma=True
[2025-08-13 19:41:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.39 (stop 10.97; dollar_vol=21241873.41)
[2025-08-13 19:41:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.39 (stop 10.97)
[2025-08-13 19:41:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: market price below expected price 4.39
[2025-08-13 19:41:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: market price below expected price 4.39
[2025-08-13 19:41:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: market price below expected price 4.39
[2025-08-13 19:41:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:41:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: market price below expected price 4.39. Resetting day context.
[2025-08-13 19:42:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=289 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:42:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:42:00-04:00, current_time=19:42:00
[2025-08-13 19:42:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21254153.81 (min=250000.00)
[2025-08-13 19:42:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4240, last_open=4.3900, last_close=4.4000, red_bar=False, below_ma=True
[2025-08-13 19:43:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=290 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:43:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:43:00-04:00, current_time=19:43:00
[2025-08-13 19:43:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21259315.13 (min=250000.00)
[2025-08-13 19:43:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4170, last_open=4.4000, last_close=4.4001, red_bar=False, below_ma=True
[2025-08-13 19:44:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=291 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:44:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:44:00-04:00, current_time=19:44:00
[2025-08-13 19:44:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21266980.07 (min=250000.00)
[2025-08-13 19:44:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4130, last_open=4.4000, last_close=4.3900, red_bar=True, below_ma=True
[2025-08-13 19:44:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.39 (stop 10.97; dollar_vol=21266980.07)
[2025-08-13 19:44:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.39 (stop 10.97)
[2025-08-13 19:44:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: market price below expected price 4.39
[2025-08-13 19:44:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: market price below expected price 4.39
[2025-08-13 19:44:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: market price below expected price 4.39
[2025-08-13 19:44:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:44:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: market price below expected price 4.39. Resetting day context.
[2025-08-13 19:45:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=292 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:45:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:45:00-04:00, current_time=19:45:00
[2025-08-13 19:45:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21286844.23 (min=250000.00)
[2025-08-13 19:45:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.4050, last_open=4.3800, last_close=4.3600, red_bar=True, below_ma=True
[2025-08-13 19:45:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=22 at 4.36 (stop 10.90; dollar_vol=21286844.23)
[2025-08-13 19:45:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 22 shares at 4.36 (stop 10.90)
[2025-08-13 19:45:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: market price below expected price 4.36
[2025-08-13 19:45:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: market price below expected price 4.36
[2025-08-13 19:45:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: market price below expected price 4.36
[2025-08-13 19:45:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:45:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: market price below expected price 4.36. Resetting day context.
[2025-08-13 19:46:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=293 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:46:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:46:00-04:00, current_time=19:46:00
[2025-08-13 19:46:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21314444.71 (min=250000.00)
[2025-08-13 19:46:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3920, last_open=4.3600, last_close=4.3200, red_bar=True, below_ma=True
[2025-08-13 19:46:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Entry conditions met → qty=23 at 4.32 (stop 10.80; dollar_vol=21314444.71)
[2025-08-13 19:46:01 EDT] INFO - strategies.post_market_gap_short_strategy - SNOA: Shorted 23 shares at 4.32 (stop 10.80)
[2025-08-13 19:46:07 EDT] ERROR - brokers.clear_street_locate_manager - locate failed for SNOA: fee 1.38% > cap 1.25%
[2025-08-13 19:46:07 EDT] ERROR - brokers.local_broker - Error executing market sell for SNOA: fee 1.38% > cap 1.25%
[2025-08-13 19:46:07 EDT] ERROR - orchestrator.execution_orchestrator - Error executing signal for SNOA: fee 1.38% > cap 1.25%
[2025-08-13 19:46:07 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA: on_exec - order_id=, outcome=ExecOutcome.ERROR, signal=SignalType.OPEN, trade_type=TradeType.SELL, filled_qty=0.0
[2025-08-13 19:46:07 EDT] WARNING - strategies.post_market_gap_short_strategy - SNOA: Execution failed with outcome ExecOutcome.ERROR: fee 1.38% > cap 1.25%. Resetting day context.
[2025-08-13 19:47:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=294 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:47:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:47:00-04:00, current_time=19:47:00
[2025-08-13 19:47:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21331119.54 (min=250000.00)
[2025-08-13 19:47:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3820, last_open=4.3200, last_close=4.3300, red_bar=False, below_ma=True
[2025-08-13 19:48:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=295 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:48:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:48:00-04:00, current_time=19:48:00
[2025-08-13 19:48:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21344823.86 (min=250000.00)
[2025-08-13 19:48:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3770, last_open=4.3300, last_close=4.3700, red_bar=False, below_ma=True
[2025-08-13 19:49:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=296 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:49:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:49:00-04:00, current_time=19:49:00
[2025-08-13 19:49:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21357952.16 (min=250000.00)
[2025-08-13 19:49:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3710, last_open=4.3500, last_close=4.3500, red_bar=False, below_ma=True
[2025-08-13 19:50:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=297 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:50:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:50:00-04:00, current_time=19:50:00
[2025-08-13 19:50:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21366357.66 (min=250000.00)
[2025-08-13 19:50:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3670, last_open=4.3500, last_close=4.3597, red_bar=False, below_ma=True
[2025-08-13 19:51:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=298 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:51:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:51:00-04:00, current_time=19:51:00
[2025-08-13 19:51:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21380442.17 (min=250000.00)
[2025-08-13 19:51:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3650, last_open=4.3600, last_close=4.3700, red_bar=False, below_ma=False
[2025-08-13 19:52:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=299 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:52:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:52:00-04:00, current_time=19:52:00
[2025-08-13 19:52:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21396318.17 (min=250000.00)
[2025-08-13 19:52:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3660, last_open=4.4000, last_close=4.4100, red_bar=False, below_ma=False
[2025-08-13 19:53:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=300 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:53:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:53:00-04:00, current_time=19:53:00
[2025-08-13 19:53:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21402165.77 (min=250000.00)
[2025-08-13 19:53:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3660, last_open=4.4200, last_close=4.4000, red_bar=True, below_ma=False
[2025-08-13 19:54:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=301 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:54:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:54:00-04:00, current_time=19:54:00
[2025-08-13 19:54:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - gap status: is_gap=True, dollar_vol=21406486.57 (min=250000.00)
[2025-08-13 19:54:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - entry gate: SMA=4.3670, last_open=4.4000, last_close=4.4000, red_bar=False, below_ma=False
[2025-08-13 19:55:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=302 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:55:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:55:00-04:00, current_time=19:55:00
[2025-08-13 19:55:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - outside post-market window: now=19:55:00, window=[16:00:00, 19:55:00)
[2025-08-13 19:56:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - process_bar: hist=303 bars, latest=1 bars, in_pos=False, traded=None
[2025-08-13 19:56:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - timestamps: current_ts=2025-08-13 19:56:00-04:00, current_time=19:56:00
[2025-08-13 19:56:01 EDT] DEBUG - strategies.post_market_gap_short_strategy - SNOA - outside post-market window: now=19:56:00, window=[16:00:00, 19:55:00)
