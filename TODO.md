Market data health integration follow-ups

- Update remaining runners to pass streaming market data to `attach_control_plane(...)` and use the returned `cp_market_data` in `ExecutionOrchestrator`:
  - `src/live_runner/synthetic_example_runner.py`
  - Any other runners using streaming feeds

- Integrate health wrapper into `market_data_builder.py` when constructing streaming providers via `build_streaming_market_data()` so callers automatically get health-checked market data.

- Extend Hub UI to surface market data health details (e.g., worst stale subscription and seconds over threshold) per agent.


## Flow based strategy on price insenstivie futures (like wheat)

- Get flow data from someplace - like cme block trades
- See if you can find a win-rate based on the flow data on short/mid timeframe (6-25 hrs)
- Backtest the strategy

- References
 - NopeLily 
    - https://x.com/nope_its_lily/status/1883570232406777951 
    - https://x.com/nope_its_lily/status/1886856176996081749
    - Core strategy - futures short/mid timeframe (6-25 hrs)
    - https://x.com/nope_its_lily/status/1886856176996081749
        Trading listed futures on short/mid timeframes directionally based on flow (6-24 hours, generally)

        The funny thing about flow trading is the profitability roughly scales as a function of price insensitive flow, which tends to be informed flow (hedgers, commodity producers and traders, fund managers).
        And the products retail traders love tend to be some of the least predictable
- Retail flows
    - https://x.com/nope_its_lily/status/1877014534499832207