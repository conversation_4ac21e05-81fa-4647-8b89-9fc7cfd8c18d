<!DOCTYPE html>
<html lang="en" class="h-full" data-theme="business">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Control-Plane Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = { plugins: [function({addBase}){ addBase({ ':root': { '--rounded-box': '0.5rem' } }) } ] };
    </script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.10/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
  </head>
  <body class="h-full bg-base-200">
    <div class="max-w-6xl mx-auto p-6 space-y-6 text-base-content">
      <h1 class="text-2xl font-semibold">Control-Plane Dashboard</h1>

      <!-- Agent Health -->
      <div class="card bg-base-100 shadow">
        <div class="card-body p-0">
          <div class="overflow-x-auto">
            <table class="table table-zebra table-sm w-full text-sm md:text-base">
              <thead>
                <tr>
                  <th class="whitespace-nowrap text-base-content text-xs md:text-sm">Agent</th>
                  <th class="whitespace-nowrap text-base-content text-xs md:text-sm">Latest</th>
                  <th class="text-right whitespace-nowrap text-base-content text-xs md:text-sm">Uptime 15m</th>
                  <th class="text-right whitespace-nowrap text-base-content text-xs md:text-sm">Uptime 60m</th>
                  <th class="whitespace-nowrap text-base-content text-xs md:text-sm">Timeline</th>
                </tr>
              </thead>
              <tbody id="health-body"
                     hx-get="/ui/health/table"
                     hx-trigger="load, every 10s"
                     hx-target="#health-body"
                     hx-swap="innerHTML">
                <tr><td colspan="5" class="text-center text-sm py-6">Loading health...</td></tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Row-level actions are available within the positions table below -->

      <!-- Positions table -->
      <div class="card bg-base-100 shadow">
        <div class="card-body p-0">
          <div class="overflow-x-auto">
            <table class="table table-zebra table-sm w-full text-sm md:text-base">
              <thead>
                <tr>
                  <th class="whitespace-nowrap text-base-content text-xs md:text-sm">Symbol</th>
                  <th class="text-right whitespace-nowrap text-base-content text-xs md:text-sm">Mkt</th>
                  <th class="text-right whitespace-nowrap text-base-content text-xs md:text-sm">R</th>
                  <th class="text-right whitespace-nowrap text-base-content text-xs md:text-sm">UnR</th>
                  <th class="text-right whitespace-nowrap text-base-content text-xs md:text-sm">Price</th>
                  <th class="whitespace-nowrap text-base-content text-xs md:text-sm">Actions</th>
                </tr>
              </thead>
              <tbody id="positions-body"
                     hx-get="/ui/positions/table"
                     hx-trigger="load, every 5s"
                     hx-target="#positions-body"
                     hx-swap="innerHTML">
                <tr><td colspan="6" class="text-center text-sm py-6">Loading...</td></tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div id="toast"></div>
    </div>

    <script></script>
  </body>
  </html>


