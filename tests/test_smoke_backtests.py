import subprocess
import sys
import os
import pytest
import warnings
import shlex # Used for safely quoting command for logging

# --- Keep get_project_root() and build_command_list() functions as before ---
def get_project_root():
    """
    Heuristic to find the project root directory.
    Adjust the marker files/dirs if needed for your project setup.
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    while current_dir != os.path.dirname(current_dir): # Stop at root ('/')
        if any(os.path.exists(os.path.join(current_dir, marker))
               for marker in ['src', 'pyproject.toml', '.git', 'requirements.txt']):
            return current_dir
        current_dir = os.path.dirname(current_dir)
    raise RuntimeError("Could not determine project root. Ensure the test is run within the project structure.")

def build_command_list(base_cmd, options_dict):
    """
    Builds a command list for subprocess from a base command and options dict.
    Handles boolean flags (present if True) and key-value arguments.
    """
    cmd = list(base_cmd) # Start with base like ['python', '-u', '-m', 'module']
    for key, value in options_dict.items():
        if not key.startswith('-'):
            warnings.warn(f"Option '{key}' does not start with '-'. Skipping.")
            continue
        if isinstance(value, bool):
            if value: cmd.append(key)
        elif value is not None:
            cmd.append(key)
            cmd.append(str(value))
    return cmd

# --- Keep backtest_scenarios list definition as before ---
backtest_scenarios = [
    (
        "OPBShort_GLMD_20250428", # Test ID
        { # CLI Options Dictionary
            "--strategy": "opb_short_strategy",
            "--risk": "0.1",
            "--max-position-size": ".1",
            "--tickers": "GLMD",
            "--min-market-cap": "1000000",
            "--max-market-cap": "500000000",
            "--start-date": "2025-04-28",
            "--end-date": "2025-04-28",
            "--trade-session": "full",
            "--no-log-file": True # Boolean flag example
        },
        [ # List of Expected Exact Lines (now checked in combined output)
            "DEBUG:stats.stats_builder:2025-04-28: OPEN GLMD -400 @ $2.49, cash change: $996.00, new cash: $100,996.00",
            "DEBUG:stats.stats_builder:2025-04-28: CLOSE GLMD -400 @ $1.65, cash change: $-660.00, new cash: $100,336.00"
        ]
    ),
    # Add more scenarios here if needed
]

@pytest.mark.parametrize(
    "test_id, cli_options, expected_output_lines", # Renamed last param for clarity
    backtest_scenarios,
    ids=[scenario[0] for scenario in backtest_scenarios]
)
def test_backtest_smoke(test_id, cli_options, expected_output_lines):
    """
    Runs a generic backtest command scenario and verifies expected logs
    appear in either stdout or stderr.
    Uses parametrization to run different scenarios defined in 'backtest_scenarios'.
    """
    project_root = get_project_root()
    venv_python = os.path.join(project_root, ".venv", "bin", "python")

    if not os.path.exists(venv_python):
         warnings.warn(
             f"Test '{test_id}': Virtual environment python not found at '{venv_python}'. "
             f"Falling back to sys.executable ('{sys.executable}'). "
             "Ensure test is run in the correct venv or .venv path is correct.",
             RuntimeWarning
         )
         venv_python = sys.executable

    base_command_parts = [
        venv_python,
        "-u",
        "-m", "backtest_runner.run"
    ]
    command = build_command_list(base_command_parts, cli_options)

    print(f"\n--- Running Smoke Test Scenario: {test_id} ---")
    print(f"Working Directory: {project_root}")
    print(f"Command: {' '.join(shlex.quote(str(part)) for part in command)}")

    try:
        process = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=False,
            cwd=project_root,
            timeout=180
        )
    except subprocess.TimeoutExpired as e:
        pytest.fail(f"Scenario '{test_id}' timed out after {e.timeout} seconds.\n"
                    f"Stdout:\n{e.stdout}\nStderr:\n{e.stderr}")
    except FileNotFoundError:
         pytest.fail(f"Scenario '{test_id}': Could not find Python executable at '{venv_python}'.")
    except Exception as e:
        pytest.fail(f"Scenario '{test_id}': An unexpected error occurred running subprocess: {e}")

    # Combine stdout and stderr for checking general output
    combined_output = process.stdout + "\n" + process.stderr

    # Print captured output for debugging
    print(f"\n--- Captured STDOUT ({test_id}) ---")
    print(process.stdout)
    print(f"--- End STDOUT ({test_id}) ---")
    if process.stderr:
        print(f"\n--- Captured STDERR ({test_id}) ---")
        print(process.stderr)
        print(f"--- End STDERR ({test_id}) ---")

    # --- Assertions ---
    # 1. Check exit code
    assert process.returncode == 0, \
        f"Scenario '{test_id}' failed: Backtest script exited with non-zero status code: {process.returncode}. Check stderr."

    # 2. Check specifically stderr for Python tracebacks
    assert "Traceback (most recent call last):" not in process.stderr, \
        f"Scenario '{test_id}' failed: Python traceback found in stderr."

    # 3. Check for all expected lines in the *combined* output
    missing_lines = []
    for i, expected_line in enumerate(expected_output_lines):
        # Check if the line exists in the combined output stream
        if expected_line not in combined_output:
            missing_lines.append(f"Line {i+1}: '{expected_line}'")

    assert not missing_lines, \
        (f"Scenario '{test_id}' failed: The following expected output lines were NOT found in stdout OR stderr:\n"
         + "\n".join(missing_lines))

    print(f"\n--- Smoke Test Scenario Passed: {test_id} ---")