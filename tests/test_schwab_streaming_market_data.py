import asyncio
import datetime as dt
import os
import pytest
import pytest_asyncio
import pandas as pd
from datetime import timed<PERSON><PERSON>
from dotenv import load_dotenv
from pathlib import Path

# Set asyncio mode explicitly to avoid warnings
pytest.asyncio_mode = "strict"
pytestmark = pytest.mark.asyncio(loop_scope="function")

# Load environment variables from .env.integration file
env_path = Path('.env.integration')
if env_path.exists():
    load_dotenv(dotenv_path=env_path)

from schwab.auth import easy_client
from schwab.client import Client

from marketdata.istreamingmarketdata import IStreamingMarketData
from marketdata.schwab_market_data import SchwabStreamingMarketData  # Assuming this is the correct import path


@pytest_asyncio.fixture(scope="function")
def event_loop():
    """Create an instance of the default event loop for each test case."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def schwab_client():
    """Create and return an authenticated Schwab client for testing."""
    api_key = os.environ.get("SCHWAB_API_KEY")
    app_secret = os.environ.get("SCHWAB_APP_SECRET")
    callback_url = os.environ.get("SCHWAB_CALLBACK_URL", "https://127.0.0.1:8182")
    token_path = os.environ.get("SCHWAB_TOKEN_PATH", "./schwab_token.json")
    
    if not api_key or not app_secret:
        pytest.skip("SCHWAB_API_KEY and SCHWAB_APP_SECRET environment variables are required")
    
    # Run blocking auth in separate thread; avoid interactive prompts during CI
    try:
        client = await asyncio.to_thread(
            easy_client,
            api_key=api_key,
            app_secret=app_secret,
            callback_url=callback_url,
            token_path=token_path,
            interactive=False,
        )
        return client
    except Exception as e:
        # If tokens are stale/invalid or interactive login required, skip gracefully
        pytest.skip(f"Schwab client unavailable (likely token too old or interactive login needed): {e}")


@pytest_asyncio.fixture
async def market_data(schwab_client):
    """Create and return a SchwabStreamingMarketData instance."""
    market_data = SchwabStreamingMarketData(schwab_client)
    await market_data.connect()
    yield market_data
    await market_data.close()


@pytest.mark.asyncio
async def test_subscribe_to_bars_returns_historical_data(market_data):
    """Test that subscribe_to_bars returns historical data for /NQ."""
    # Setup
    interval = timedelta(minutes=1)
    start_time = dt.datetime.now(dt.timezone.utc) - timedelta(days=1)
    lookback_window = timedelta(days=3)
    
    # Execute
    bar_generator = market_data.subscribe_to_bars(
        ticker="/NQ",
        interval=interval,
        start_time=start_time,
        lookback_window=lookback_window
    )
    
    # Get first result (historical data)
    hist_df, latest_df = await asyncio.wait_for(
        bar_generator.__anext__(),
        timeout=10.0
    )
    
    # breakpoint()
    
    # Assert
    assert isinstance(hist_df, pd.DataFrame)
    assert isinstance(latest_df, pd.DataFrame)
    assert not hist_df.empty, "Historical dataframe should not be empty"
    assert not latest_df.empty, "Latest dataframe should not be empty"
    
    # Check dataframe structure
    expected_columns = ["open", "high", "low", "close", "volume"]
    assert all(col in hist_df.columns for col in expected_columns)
    assert hist_df.index.name == "timestamp"
    
    # Check data types
    assert hist_df.index.dtype.kind == 'M'  # datetime index
    assert pd.api.types.is_numeric_dtype(hist_df["open"])
    assert pd.api.types.is_numeric_dtype(hist_df["high"])
    assert pd.api.types.is_numeric_dtype(hist_df["low"])
    assert pd.api.types.is_numeric_dtype(hist_df["close"])
    assert pd.api.types.is_numeric_dtype(hist_df["volume"])
    
    # Make sure we got some reasonable amount of history
    assert len(hist_df) > 0, "Should have received historical data"

@pytest.mark.asyncio
# @pytest.mark.skip(reason="This test is not working as expected")
async def test_subscribe_to_bars_streams_live_data(market_data):
    """Test that subscribe_to_bars streams live data for /NQ."""
    # Setup
    interval = timedelta(minutes=1)
    start_time = dt.datetime.now(dt.timezone.utc) - timedelta(days=1)
    lookback_window = timedelta(days=3)
    
    # Execute
    bar_generator = market_data.subscribe_to_bars(
        ticker="/NQ",
        interval=interval,
        start_time=start_time,
        lookback_window=lookback_window
    )
    
    # Get first result (historical data)
    hist_df, _ = await asyncio.wait_for(
        bar_generator.__anext__(),
        timeout=10.0
    )
    initial_len = len(hist_df)
    
    # Wait for the next update (with timeout configured from .env.integration)
    timeout = float(os.environ.get("SCHWAB_TEST_LIVE_DATA_TIMEOUT", 120.0))
    try:
        updated_hist_df, new_bar = await asyncio.wait_for(
            bar_generator.__anext__(),
            timeout=timeout
        )
        
        # Assert
        assert len(updated_hist_df) >= initial_len, "Updated dataframe should have at least as many rows as initial"
        assert isinstance(new_bar, pd.DataFrame), "Should receive a new bar as dataframe"
        assert len(new_bar) == 1, "Should receive a single new bar"
        
        # Check the new bar data
        assert not new_bar.empty, "New bar should not be empty"
        assert new_bar.index.name == "timestamp"
        assert all(col in new_bar.columns for col in ["open", "high", "low", "close", "volume"])
        
    except asyncio.TimeoutError:
        pytest.skip("Timed out waiting for live data - market might be closed or data feed inactive")


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])