import asyncio
import logging
from typing import Any, Dict

import pytest
from fastapi_websocket_rpc.rpc_methods import RpcMethodsBase
import uvicorn

from brokers.control_plane_broker import ControlPlaneBroker
from brokers.ibroker import <PERSON><PERSON><PERSON><PERSON>, OrderParamsBuilder, Position
from trading_framework.execution_feedback import StrategyExitRequest
from control_plane.hub import app, get_agent
from control_plane.agent import ControlAgent


logging.basicConfig(level=logging.INFO)

class MockDelegateBroker(IBroker):
    def __init__(self):
        self._positions: Dict[str, float] = {}
        self._orders: list[dict[str, Any]] = []

    async def check_connection(self):
        return True

    async def market_buy(self, params):
        # BUY increases long or decreases short
        qty = params.quantity if params.quantity is not None else 0
        self._orders.append({"side": "buy", "symbol": params.symbol, "qty": qty, "intent": params.position_intent})
        if params.position_intent == "open":
            self._positions[params.symbol] = self._positions.get(params.symbol, 0) + qty
        else:
            # close: cover shorts fully if quantity None, else reduce short
            if params.quantity is None:
                # cover entire short
                if self._positions.get(params.symbol, 0) < 0:
                    self._positions[params.symbol] = 0
            else:
                self._positions[params.symbol] = self._positions.get(params.symbol, 0) + qty
        return {"status": "ok"}

    async def market_sell(self, params):
        qty = params.quantity if params.quantity is not None else 0
        self._orders.append({"side": "sell", "symbol": params.symbol, "qty": qty, "intent": params.position_intent})
        if params.position_intent == "open":
            # open short
            self._positions[params.symbol] = self._positions.get(params.symbol, 0) - qty
        else:
            # close: sell to close longs fully if quantity None, else reduce long
            if params.quantity is None:
                if self._positions.get(params.symbol, 0) > 0:
                    self._positions[params.symbol] = 0
            else:
                self._positions[params.symbol] = self._positions.get(params.symbol, 0) - qty
        return {"status": "ok"}

    async def list_positions(self):
        out = []
        for sym, qty in self._positions.items():
            if qty != 0:
                out.append(Position(symbol=sym, quantity=qty))
        return out


class Agent(RpcMethodsBase):
    def __init__(self, broker: ControlPlaneBroker):
        super().__init__()
        self.broker = broker

    async def block_opens(self):
        await self.broker.block_opens()
        return "ok"

    async def unblock_opens(self):
        await self.broker.unblock_opens()
        return "ok"

    async def block_opens_for(self, symbol: str):
        await self.broker.block_opens_for(symbol)
        return "ok"

    async def close_all_positions(self):
        await self.broker.close_all_positions()
        return "ok"


@pytest.mark.asyncio
async def test_control_hub_agent_e2e():
    # Start uvicorn server on a random free port
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.bind(("127.0.0.1", 0))
    host, port = sock.getsockname()
    sock.close()

    config = uvicorn.Config(app, host="127.0.0.1", port=port, log_level="warning")
    server = uvicorn.Server(config)
    server_task = asyncio.create_task(server.serve())

    # wait a moment for server to start
    await asyncio.sleep(0.3)

    # Prepare broker and agent
    delegate = MockDelegateBroker()
    cp_broker = ControlPlaneBroker(delegate)

    # Put a long position to verify close later
    buy_open = (
        OrderParamsBuilder()
        .with_symbol("AAPL")
        .with_quantity(10)
        .with_expect_price(100.0)
        .with_position_intent("open")
        .with_current_datetime()
        .build()
    )
    await cp_broker.market_buy(buy_open)

    # Agent connects to hub
    # Use the ControlAgent wrapper (production class)
    control_agent = ControlAgent(hub_url=f"ws://127.0.0.1:{port}/ws", broker=cp_broker, node_id="node-1")

    # Connect agent synchronously and wait for readiness (no background task)
    await control_agent.connect_and_register()
    
    # Obtain the server-side session for this agent and invoke RPC into agent
    agent_session = get_agent("node-1")
    assert agent_session is not None
    
    # Ask hub -> agent to block opens for this strategy
    await agent_session.other.block_opens()
    with pytest.raises(StrategyExitRequest):
        await cp_broker.market_buy(
            OrderParamsBuilder()
            .with_symbol("MSFT")
            .with_quantity(1)
            .with_expect_price(1.0)
            .with_position_intent("open")
            .with_current_datetime()
            .build()
        )

    # Ask hub -> agent to close all positions and ensure zero
    await agent_session.other.close_all_positions()
    # Allow some time for async close tasks
    await asyncio.sleep(0.2)
    positions = await cp_broker.list_positions()
    assert len(positions) == 0

    # Cleanup
    await control_agent.close()
    server.should_exit = True
    await asyncio.sleep(0.2)


