import os
import asyncio
import json
import socket
from contextlib import suppress
from typing import Any

import pytest
import pytest_asyncio
import websockets

# Ensure pytest runs in strict asyncio mode
pytest.asyncio_mode = "strict"
pytestmark = pytest.mark.asyncio(loop_scope="function")


@pytest_asyncio.fixture()
async def polygon_stub_server():
    """Start an in-process stub that pretends to be Polygon.io.

    It records subscribe/unsubscribe requests coming from the mux server and
    exposes a handle to push events downstream during the test.
    """
    # Storage helpers shared with the test
    subscribe_messages: list[str] = []
    holder: dict[str, Any] = {}

    async def _handler(ws):
        # Store so the test can send events later
        holder["ws"] = ws
        # Initial connected status – mimics real Polygon
        await ws.send('[{"status":"connected"}]')
        # Expect auth message
        auth_msg = json.loads(await ws.recv())
        assert auth_msg["action"] == "auth"
        # Respond with auth_success
        await ws.send('[{"status":"auth_success"}]')

        # Record subsequent subscribe/unsubscribe requests
        async for raw in ws:
            msg = json.loads(raw)
            action = msg.get("action")
            if action == "subscribe":
                params = msg.get("params", "")
                subscribe_messages.extend(params.split(","))
            elif action == "unsubscribe":
                # We do not need this for current tests
                continue

    # Start the stub server on a random available port
    stub = await websockets.serve(_handler, "localhost", 0)
    port = stub.sockets[0].getsockname()[1]
    url = f"ws://localhost:{port}"

    yield {
        "url": url,
        "subscribe_messages": subscribe_messages,
        "holder": holder,
        "server": stub,
    }

    # Cleanup
    stub.close()
    await stub.wait_closed()


@pytest_asyncio.fixture()
async def mux_server(polygon_stub_server):
    """Launch the PolygonMuxServer wired to the stub upstream."""
    # Ensure the required env var is present before importing
    os.environ.setdefault("POLYGON_API_KEY", "TEST_KEY")

    # Import here so env var above is considered
    from marketdata import polygon_mux_server as pms

    # Point the mux at the stub upstream URL
    pms.POLYGON_WS_URL = polygon_stub_server["url"]
    pms.POLYGON_API_KEY = "TEST_KEY"

    # Pick a random free port for the downstream server
    with socket.socket() as s:
        s.bind(("localhost", 0))
        downstream_port = s.getsockname()[1]

    server = pms.PolygonMuxServer(host="localhost", port=downstream_port)
    task = asyncio.create_task(server.start())

    # Wait briefly for the server to be ready
    await asyncio.sleep(0.1)

    yield {
        "port": downstream_port,
        "task": task,
        "module": pms,
    }

    # Teardown – cancel the running server task
    task.cancel()
    with suppress(asyncio.CancelledError):
        await task


async def _connect_client(port):
    uri = f"ws://localhost:{port}"
    ws = await websockets.connect(uri)
    # Consume initial connected status message
    await ws.recv()
    # Authenticate
    await ws.send(json.dumps({"action": "auth", "params": ""}))
    await ws.recv()  # auth_success
    return ws


@pytest.mark.asyncio
async def test_muxing_and_second_minute_support(mux_server, polygon_stub_server):
    """End-to-end validation of subscription fan-out and AM/A channel handling."""

    port = mux_server["port"]

    # Two clients: one wants minute bars, the other wants second bars
    c_min = await _connect_client(port)
    c_sec = await _connect_client(port)

    # Subscribe → minute client
    await c_min.send(json.dumps({"action": "subscribe", "params": "AM.AAPL"}))
    await c_min.recv()  # confirmation

    # Subscribe → second client
    await c_sec.send(json.dumps({"action": "subscribe", "params": "A.AAPL"}))
    await c_sec.recv()  # confirmation

    # Give the mux a beat to forward subscribes upstream
    await asyncio.sleep(0.1)

    subs = polygon_stub_server["subscribe_messages"]
    # Upstream should have received *both* AM.AAPL & A.AAPL exactly once
    assert subs.count("AM.AAPL") == 1
    assert subs.count("A.AAPL") == 1

    # Grab upstream websocket so we can inject events
    upstream_ws = polygon_stub_server["holder"].get("ws")
    assert upstream_ws is not None, "Upstream websocket missing – stub setup failed"

    # Send a minute-level event – only c_min should receive it
    evt_min = [{"ev": "AM", "sym": "AAPL", "foo": 1}]
    await upstream_ws.send(json.dumps(evt_min))

    msg_min = json.loads(await asyncio.wait_for(c_min.recv(), timeout=1))
    assert msg_min == evt_min

    with pytest.raises(asyncio.TimeoutError):
        await asyncio.wait_for(c_sec.recv(), timeout=0.2)

    # Send a second-level event – only c_sec should receive it
    evt_sec = [{"ev": "A", "sym": "AAPL", "bar": 42}]
    await upstream_ws.send(json.dumps(evt_sec))

    msg_sec = json.loads(await asyncio.wait_for(c_sec.recv(), timeout=1))
    assert msg_sec == evt_sec

    with pytest.raises(asyncio.TimeoutError):
        await asyncio.wait_for(c_min.recv(), timeout=0.2)

    # Close clients
    await c_min.close()
    await c_sec.close() 