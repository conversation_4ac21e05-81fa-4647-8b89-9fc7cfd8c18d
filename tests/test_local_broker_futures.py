import asyncio
from datetime import datetime

import pytest

from brokers.local_broker import LocalBroker
from brokers.ibroker import OrderParamsBuilder
from tools.clock import Clock


class _StubQuote:
    def __init__(self, bid_price=None, ask_price=None):
        self.bid_price = bid_price
        self.ask_price = ask_price


class _StubMarketData:
    def __init__(self, bid, ask):
        self._bid = bid
        self._ask = ask

    async def get_quote_async(self, symbol: str):
        return _StubQuote(self._bid, self._ask)


pytestmark = pytest.mark.asyncio


async def _build_broker_for_symbol(symbol: str, bid: float, ask: float) -> LocalBroker:
    clock = Clock()
    md = _StubMarketData(bid, ask)
    return LocalBroker(market_data=md, initial_capital=100000.0, clock=clock)


async def _place_order(broker: LocalBroker, symbol: str, qty: float, price: float, intent: str):
    params = (
        OrderParamsBuilder()
        .with_symbol(symbol)
        .with_quantity(qty)
        .with_expect_price(price)
        .with_position_intent(intent)
        .with_current_datetime()
        .build()
    )
    if intent == "open":
        if qty > 0:
            return await broker.market_buy(params)
        else:
            # open short
            params = (
                OrderParamsBuilder()
                .with_symbol(symbol)
                .with_quantity(abs(qty))
                .with_expect_price(price)
                .with_position_intent("open")
                .with_current_datetime()
                .build()
            )
            return await broker.market_sell(params)
    else:
        # close long uses sell, close short uses buy
        return await broker.market_sell(params) if qty > 0 else await broker.market_buy(params)


@pytest.mark.asyncio
async def test_futures_realized_and_traded_value_scaling():
    symbol = "/NQ"
    broker = await _build_broker_for_symbol(symbol, bid=16010.0, ask=16010.0)

    # Open 1 contract long at 16000
    await _place_order(broker, symbol, qty=1, price=16000.0, intent="open")
    # Close at 16010
    await _place_order(broker, symbol, qty=1, price=16010.0, intent="close")

    # One closed trade with PnL scaled by 20 = (16010-16000)*1*20 = 200
    assert len(broker.closed_trades) == 1
    trade = broker.closed_trades[0]
    assert trade["symbol"] == symbol
    assert pytest.approx(trade["pnl"], rel=1e-6) == 200.0
    assert trade.get("contract_multiplier") == 20

    # Positions should show realized-only row with traded value scaled
    positions = await broker.list_positions()
    assert len(positions) == 1
    pos = positions[0]
    assert pos.symbol == symbol
    assert pos.quantity == 0
    assert pytest.approx(pos.realized or 0.0, rel=1e-6) == 200.0
    # traded value = exit_price * qty * multiplier = 16010 * 1 * 20
    assert pytest.approx(pos.total_traded_value or 0.0, rel=1e-6) == 16010.0 * 20
    # with a quote present and flat, market_value should be 0.0, unrealized None
    assert pos.market_value == 0.0
    assert pos.unrealized is None


@pytest.mark.asyncio
async def test_futures_unrealized_and_market_value_scaling():
    symbol = "/NQ"
    # Midpoint quote 16005
    broker = await _build_broker_for_symbol(symbol, bid=16004.0, ask=16006.0)

    # Open 1 contract long at 16000
    await _place_order(broker, symbol, qty=1, price=16000.0, intent="open")

    positions = await broker.list_positions()
    assert len(positions) == 1
    pos = positions[0]
    assert pos.symbol == symbol
    assert pos.quantity == 1
    # unrealized = (16005 - 16000) * 1 * 20 = 100
    assert pytest.approx(pos.unrealized or 0.0, rel=1e-6) == 100.0
    # market value = qty * price * 20 = 1 * 16005 * 20
    assert pytest.approx(pos.market_value or 0.0, rel=1e-6) == 16005.0 * 20

