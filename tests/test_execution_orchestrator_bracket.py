import asyncio
from datetime import datetime, timedelta, timezone

import pandas as pd
import pytest

from orchestrator.execution_orchestrator import ExecutionOrchestrator
from marketdata.istreamingmarketdata import IStreamingMarketData
from tickers.iticker_discover import ITickerDiscover
from trading_framework.core import BaseStrategy, BarSlice
from strategies.trade_signals import (
    TradeSignal,
    SignalType,
    TradeType,
)
from brokers.ibroker import <PERSON><PERSON><PERSON>r, OrderParams
from brokers.ibroker_extended import BracketOrderParams
from position_manager import PositionManager


# ---------------------------------------------------------------------------
# Dummy implementations                                                          
# ---------------------------------------------------------------------------

class DummyStreamingMarketData(IStreamingMarketData):
    """A minimal streaming market-data provider that yields a single bar."""

    async def subscribe_to_bars(self, ticker, interval, start_time, lookback_window):
        # Build a very small historical dataframe
        hist_index = pd.date_range(
            end=start_time - interval,
            periods=3,
            freq=interval,
            tz=timezone.utc,
        )
        hist_df = pd.DataFrame(
            {
                "open": [100.0, 101.0, 102.0],
                "high": [101.0, 102.0, 103.0],
                "low": [99.0, 100.0, 101.0],
                "close": [100.5, 101.5, 102.5],
                "volume": [1000, 1000, 1000],
            },
            index=hist_index,
        )
        hist_df.index.name = "timestamp"

        # Latest bar (open)
        latest_time = start_time
        latest_df = pd.DataFrame(
            {
                "open": [103.0],
                "high": [104.0],
                "low": [102.0],
                "close": [103.0],
                "volume": [1200],
            },
            index=[latest_time.replace(tzinfo=timezone.utc)],
        )
        latest_df.index.name = "timestamp"

        # Yield one tuple then finish
        yield hist_df, latest_df

    # The async generator type requires __aiter__ is provided by default


class DummyTickerDiscover(ITickerDiscover):
    """Returns a single ticker once then completes."""

    async def discover(self, timestamp: datetime | None = None):
        import pytz
        df = pd.DataFrame(
            {
                "timestamp": [datetime.now(pytz.utc)],
                "ticker": ["AAPL"],
            }
        )
        yield df  # Only one batch — orchestrator will exit afterwards


class DummyBroker(IBroker):
    """Records orders passed through the orchestrator."""

    def __init__(self):
        self.bracket_orders: list[BracketOrderParams] = []
        self.buy_orders: list[OrderParams] = []
        self.sell_orders: list[OrderParams] = []

    # ------------- IBroker interface -------------
    async def check_connection(self):
        return True

    async def market_buy(self, params: OrderParams):
        self.buy_orders.append(params)
        return {"order_id": "BUY1", "status": "filled"}

    async def market_sell(self, params: OrderParams):
        self.sell_orders.append(params)
        return {"order_id": "SELL1", "status": "filled"}

    async def list_positions(self):
        return []

    # ------------- complex-order helpers -------------
    async def supports_complex_orders(self):
        return True

    async def bracket_order(self, params: BracketOrderParams):
        self.bracket_orders.append(params)
        return {"order_id": "BRACKET1", "status": "accepted"}

    async def trailing_stop_order(self, params):
        return {"order_id": "TS1", "status": "accepted"}


# ---------------------------------------------------------------------------
# Test Strategy                                                               
# ---------------------------------------------------------------------------

class BracketTestStrategy(BaseStrategy):
    """Emits a single bracket order then shuts itself down."""

    def __init__(self):
        super().__init__()
        self._emitted = False

    # ---------- setup ----------
    def prepare(self, metadata):
        # bar_interval, lookback_window
        return timedelta(minutes=1), timedelta(minutes=3)

    # ---------- bar processing ----------
    def process_bar(self, bars: BarSlice):
        if self._emitted:
            return None

        _, latest = bars
        price = latest["close"].iloc[-1]
        stop_loss = price * 0.98
        take_profit = price * 1.02

        sig = TradeSignal.bracket_order(
            symbol=self.ctx["ticker"],
            trade_type=TradeType.BUY,
            entry_price=price,
            stop_loss_price=stop_loss,
            take_profit_price=take_profit,
            quantity=10,
        )
        self._emitted = True
        return [sig]

    # ---------- execution feedback ----------
    def process_exec(self, report):
        # After first execution, request orchestrator to stop processing this ticker
        exit_sig = TradeSignal(
            signal=SignalType.EXIT_STRATEGY,
            trade_type=TradeType.BUY,
            price=report.avg_price or 0,
            symbol=report.signal.symbol,
        )
        return [exit_sig]


# ---------------------------------------------------------------------------
# Pytest plumbing                                                             
# ---------------------------------------------------------------------------

pytest.asyncio_mode = "strict"
pytestmark = pytest.mark.asyncio(loop_scope="function")


# ---------------------------------------------------------------------------
# Integration test                                                            
# ---------------------------------------------------------------------------

@pytest.mark.asyncio
async def test_execution_orchestrator_bracket_flow():
    # Arrange components
    market_data = DummyStreamingMarketData()
    ticker_discover = DummyTickerDiscover()
    broker = DummyBroker()
    position_mgr = PositionManager()
    strategy_factory = BracketTestStrategy

    orchestrator = ExecutionOrchestrator(
        market_data,
        ticker_discover,
        strategy_factory,
        broker,
        position_mgr,
    )

    # Act – execute orchestrator and wait for all spawned tasks to finish
    await orchestrator.execute()

    # The orchestrator spawns independent tasks per-ticker; ensure they complete
    if orchestrator._tasks:
        await asyncio.gather(*orchestrator._tasks)

    # Assert – the dummy broker should have received exactly one bracket order
    assert len(broker.bracket_orders) == 1, "Expected exactly one bracket order to be executed"

    br_order = broker.bracket_orders[0]
    assert br_order.symbol == "AAPL"
    assert br_order.quantity == 10
    # Stop-loss / take-profit conversion already validated in params factory

    # Ensure orchestrator did not call fallback market orders
    assert broker.buy_orders == [] and broker.sell_orders == [] 