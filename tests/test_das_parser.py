"""Combined DasCmdParser tests.

This file consolidates the four individual test modules that previously lived in:
    * tests/test_das_parser_pos_trade.py
    * tests/test_das_parser_buy_workflow.py
    * tests/test_das_parser_basic.py
    * tests/test_das_parser_full_workflow.py

Keeping them together reduces fixture duplication while still exercising the
same behaviour end-to-end.
"""

from __future__ import annotations

import asyncio
import pytest

from brokers.das_broker import _State  # type: ignore
from brokers.das_parser import DasCmdParser

# ---------------------------------------------------------------------------
# 1. %POS / %TRADE parsing
# ---------------------------------------------------------------------------


@pytest.mark.asyncio
async def test_pos_and_trade_parsing():
    state = _State()
    parser = DasCmdParser(state, asyncio.get_event_loop().create_future())

    pos_line = "%POS AAPL 2 1 208.1 0 0 -0.3 2025/07/30-15:37:03 -0.02"
    trade_line = "%TRADE 118859 AAPL B 1 208.1 SMRTL 18:41:11 75266 + -0.00 0.00"

    await parser.handle_line(pos_line)
    await parser.handle_line(trade_line)

    # Position checks
    assert "AAPL" in state.positions
    pos = state.positions["AAPL"]
    assert pos["symbol"] == "AAPL"
    assert pos["long_qty"] == "2" or pos["long_qty"] == "2"  # basic sanity

    # Trade checks
    assert len(state.trades) == 1
    trade = state.trades[0]
    assert trade["trade_id"] == "118859"
    assert trade["symbol"] == "AAPL"
    assert trade["qty"] == "1"

# ---------------------------------------------------------------------------
# 2. Buy-side workflow (open position)
# ---------------------------------------------------------------------------

BUY_LINES = [
    "%OrderAct 75265 Sending Buy AAPL 1 208.1 SMAT 18:37:10  3777",
    "%ORDER 75265 3777 AAPL B L 1 1 0 208.1 SMAT Sending 18:37:10 0 1RB17217 CB4571 CMDAPI",
    "%ORDER 75265 3777 AAPL B L 1 1 0 208.1 SMAT Accepted 18:37:10 0 1RB17217 CB4571 CMDAPI",
    "%OrderAct 75265 Accept Buy AAPL 1 208.1 SMAT 18:37:10  3777",
    "%OrderAct 75266 Sending Buy AAPL 1 208.1 SMRTL 18:37:10  3777",
    "%ORDER 75266 3777 AAPL B L 1 1 0 208.1 SMRTL Sending 18:37:10 75265 1RB17217 CB4571 CMDAPI",
    "%ORDER 75265 3777 AAPL B L 1 0 0 208.1 SMAT Triggered 18:37:10 0 1RB17217 CB4571 CMDAPI",
    "%ORDER 75266 3777 AAPL B L 1 1 0 208.1 SMRTL Accepted 18:37:10 75265 1RB17217 CB4571 CMDAPI",
    "%OrderAct 75266 Accept Buy AAPL 1 208.1 SMRTL 18:37:10  3777",
    "%POS AAPL 1 0 208.1 0 0 0 2025/07/30-15:37:11 0",
    "%OrderAct 75266 Execute Buy AAPL 1 208.1 SMRTL 18:41:11  3777",
    "%ORDER 75266 3777 AAPL B L 1 0 0 208.1 SMRTL Executed 18:41:11 75265 1RB17217 CB4571 CMDAPI",
    "%TRADE 118859 AAPL B 1 208.1 SMRTL 18:41:11 75266 + -0.00 0.00",
]


@pytest.mark.asyncio
async def test_buy_workflow():
    state = _State()
    parser = DasCmdParser(state, asyncio.get_event_loop().create_future())

    for line in BUY_LINES:
        await parser.handle_line(line)

    # Order executed (lookup by token)
    order = state.orders[3777]
    assert order.token == 3777
    assert order.status == "Executed"

    # Position reflects long 1
    pos = state.positions.get("AAPL")
    assert pos is not None
    assert pos["long_qty"] in {"1", "1.0"}

    # Trade stored
    assert any(t["order_id"] == "75266" for t in state.trades)

# ---------------------------------------------------------------------------
# 3. DAY+ order accepted (no fills yet)
# ---------------------------------------------------------------------------


@pytest.mark.asyncio
async def test_day_plus_order_accepted():
    state = _State()
    loop = asyncio.get_event_loop()
    login_future: asyncio.Future[None] = loop.create_future()
    parser = DasCmdParser(state, login_future)

    sample_lines = [
        "#OrderSending",
        "%OrderAct 75265 Sending Buy AAPL 1 208.1 SMAT 18:37:10  3777",
        "%ORDER 75265 3777 AAPL B L 1 1 0 208.1 SMAT Sending 18:37:10 0 1RB17217 CB4571 CMDAPI",
        "%ORDER 75265 3777 AAPL B L 1 1 0 208.1 SMAT Accepted 18:37:10 0 1RB17217 CB4571 CMDAPI",
        "%OrderAct 75265 Accept Buy AAPL 1 208.1 SMAT 18:37:10  3777",
    ]

    for line in sample_lines:
        await parser.handle_line(line)

    assert 3777 in state.orders
    order = state.orders[3777]

    assert order.order_id == "75265"
    assert order.token == 3777
    assert order.symbol == "AAPL"
    assert order.side == "B"
    assert order.order_type == "L"
    assert order.qty == 1.0
    assert order.open_qty == 1.0
    assert (order.filled_qty in {None, 0})
    assert order.price == 208.1
    assert order.route == "SMAT"
    assert order.status == "Accepted"
    assert order.time == "18:37:10"

    # Token reverse-map kept in sync
    assert state.id_to_token["75265"] == 3777

    # Action captured from %OrderAct line
    assert order.last_action == "Accept"

# ---------------------------------------------------------------------------
# 4. End-to-end workflow: open position then close it
# ---------------------------------------------------------------------------

SAMPLE_LINES_FULL_WORKFLOW = [
    "#OrderSending",
    # --- BUY LEG ---
    "%OrderAct 75265 Sending Buy AAPL 1 208.1 SMAT 18:37:10  3777",
    "%ORDER 75265 3777 AAPL B L 1 1 0 208.1 SMAT Sending 18:37:10 0 1RB17217 CB4571 CMDAPI",
    "%ORDER 75265 3777 AAPL B L 1 1 0 208.1 SMAT Accepted 18:37:10 0 1RB17217 CB4571 CMDAPI",
    "%OrderAct 75265 Accept Buy AAPL 1 208.1 SMAT 18:37:10  3777",
    "%OrderAct 75266 Sending Buy AAPL 1 208.1 SMRTL 18:37:10  3777",
    "%ORDER 75266 3777 AAPL B L 1 1 0 208.1 SMRTL Sending 18:37:10 75265 1RB17217 CB4571 CMDAPI",
    "%ORDER 75265 3777 AAPL B L 1 0 0 208.1 SMAT Triggered 18:37:10 0 1RB17217 CB4571 CMDAPI",
    "%ORDER 75266 3777 AAPL B L 1 1 0 208.1 SMRTL Accepted 18:37:10 75265 1RB17217 CB4571 CMDAPI",
    "%OrderAct 75266 Accept Buy AAPL 1 208.1 SMRTL 18:37:10  3777",
    "%POS AAPL 1 0 208.1 0 0 -0.3 2025/07/30-15:37:11 -0.02",
    "%OrderAct 75266 Execute Buy AAPL 1 208.1 SMRTL 18:41:11  3777",
    "%ORDER 75266 3777 AAPL B L 1 0 0 208.1 SMRTL Executed 18:41:11 75265 1RB17217 CB4571 CMDAPI",
    "%TRADE 118859 AAPL B 1 208.1 SMRTL 18:41:11 75266 + -0.00 0.00",
    # --- SELL LEG ---
    "%OrderAct 75359 Sending Sell AAPL 1 208.3 EDGX 18:49:12  9365",
    "%ORDER 75359 9365 AAPL S L 1 1 0 208.3 EDGX Sending 18:49:12 0 1RB17217 CB4571 Montage",
    "%ORDER 75359 9365 AAPL S L 1 1 0 208.3 EDGX Accepted 18:49:12 0 1RB17217 CB4571 Montage",
    "%OrderAct 75359 Accept Sell AAPL 1 208.3 EDGX 18:49:12  9365",
    "%POS AAPL 0 0 0 0 0 0 2025/07/30-15:49:12 0",
    "%OrderAct 75359 Execute Sell AAPL 1 208.3 EDGX 18:49:12  9365",
    "%ORDER 75359 9365 AAPL S L 1 0 0 208.3 EDGX Executed 18:49:12 0 1RB17217 CB4571 Montage",
    "%TRADE 118947 AAPL S 1 208.3 EDGX 18:49:12 75359 - 0.00 0.20",
]


@pytest.mark.asyncio
async def test_complete_workflow():
    state = _State()
    parser = DasCmdParser(state, asyncio.get_event_loop().create_future())

    for line in SAMPLE_LINES_FULL_WORKFLOW:
        await parser.handle_line(line)

    # Buy leg executed
    buy_order = state.orders[3777]
    assert buy_order.token == 3777
    assert buy_order.status == "Executed"
    assert buy_order.open_qty == 0

    # Sell leg executed
    sell_order = state.orders[9365]
    assert sell_order.token == 9365
    assert sell_order.status == "Executed"

    # Position closed
    pos = state.positions.get("AAPL")
    assert pos is not None
    assert pos["long_qty"] in {"0", "0.0"}
    assert pos["short_qty"] in {"0", "0.0"}

    # Trades captured for both legs
    assert any(t["order_id"] == "75266" for t in state.trades)
    assert any(t["order_id"] == "75359" for t in state.trades)
