"""Unit tests for DasLocateManager locate workflow.

These tests stub out a minimal broker that exposes the two attributes used by
DasLocateManager:
    • ``inbound_queue`` – ``asyncio.Queue[str]`` for incoming CMD-API lines.
    • ``_send_line`` – async helper invoked by the manager to transmit lines.

No network I/O is performed.
"""

from __future__ import annotations

import asyncio
from typing import List

import pytest

from brokers.das_locate_manager import DasLocateManager, LocateFailedError  # type: ignore


class _StubBroker:  # pylint: disable=too-few-public-methods
    """Minimal stub implementing the interface DasLocateManager relies on."""

    def __init__(self) -> None:
        self.sent_lines: List[str] = []  # record outbound commands
        self.inbound_queue: asyncio.Queue[str] = asyncio.Queue()

    async def _send_line(self, line: str):  # noqa: D401 – signature mirrors real helper
        """Capture outbound *line* instead of touching the network."""
        self.sent_lines.append(line)


async def _wait_until(predicate, timeout: float = 1.0):
    """Utility: wait until *predicate()* returns truthy or timeout expires."""
    end = asyncio.get_event_loop().time() + timeout
    while not predicate():
        if asyncio.get_event_loop().time() >= end:
            raise asyncio.TimeoutError("condition not met in time")
        await asyncio.sleep(0.005)


# ---------------------------------------------------------------------------
# Basic happy-path workflow – locate accepted & filled
# ---------------------------------------------------------------------------

import contextlib


@pytest.mark.asyncio
async def test_locate_accepted_and_filled():
    broker = _StubBroker()

    # Allow up to 0.1% fee & budget 50 USD (offer is 0.05%)
    mgr = DasLocateManager(broker, max_fee_pct=0.001, locate_budget_limit=50)

    # Kick off locate request (runs in background)
    ensure_task = asyncio.create_task(
        mgr.ensure_locate("TSLA", 500, expect_price=100.0)
    )

    # Wait until the manager submitted the SLNEWORDER command
    await _wait_until(lambda: broker.sent_lines)
    first_cmd = broker.sent_lines[0]
    assert first_cmd.startswith("SLNEWORDER"), first_cmd

    # Token is the last field in SLNEWORDER (… SYMBOL QTY ROUTE <token>)
    token = first_cmd.split()[-1]  # token is last field in SLNEWORDER

    # Generate a dummy locate-id distinct from our token
    loc_id = "1001"

    # Simulate broker response – Offered at 0.05 USD/share (fee_pct = 0.0005)
    offered_line = (
        f"%SLOrder {loc_id} TSLA 500 500 0 0.05 Offered LOCATE1 09:00:00 0 {token}"
    )
    await broker.inbound_queue.put(offered_line)

    # Manager should respond with Accept using locate-id
    await _wait_until(lambda: len(broker.sent_lines) >= 2)
    assert broker.sent_lines[1] == f"SLOFFEROPERATION {loc_id} Accept"

    # Simulate broker sending final Located status (fill done)
    located_line = (
        f"%SLOrder {loc_id} TSLA 500 0 500 0.05 Located LOCATE1 09:00:01 0 {token}"
    )
    await broker.inbound_queue.put(located_line)

    try:
        # ensure_locate should now resolve without error
        await asyncio.wait_for(ensure_task, timeout=1)

        # Inventory bookkeeping – 500 used out of 500 available
        inv = mgr._inventory["TSLA"]  # pylint: disable=protected-access
        assert inv["used"] == 500
        assert inv["available"] >= 500
    finally:
        # Clean-up background reader to avoid pending task warnings
        mgr._reader_task.cancel()  # pylint: disable=protected-access
        with contextlib.suppress(asyncio.CancelledError):
            await mgr._reader_task


# ---------------------------------------------------------------------------
# Offer rejected due to high fee – ensure_locate should raise LocateFailedError
# ---------------------------------------------------------------------------


@pytest.mark.asyncio
async def test_locate_rejected_high_fee():
    broker = _StubBroker()
    mgr = DasLocateManager(broker, max_fee_pct=0.0001, locate_budget_limit=50)  # 0.01 % max fee

    ensure_task = asyncio.create_task(
        mgr.ensure_locate("AAPL", 100, expect_price=200.0)
    )

    # Wait for SLNEWORDER
    await _wait_until(lambda: broker.sent_lines)
    token = broker.sent_lines[0].split()[-1]

    # Distinct locate-id
    loc_id = "2002"

    # Offered very cheap (below min fee) → should be rejected
    offered_line = (
        f"%SLOrder {loc_id} AAPL 100 100 0 0.05 Offered LOCATE1 09:10:00 0 {token}"
    )
    await broker.inbound_queue.put(offered_line)

    # Wait until manager sends Reject
    await _wait_until(lambda: len(broker.sent_lines) >= 2)
    assert broker.sent_lines[1] == f"SLOFFEROPERATION {loc_id} Reject"

    # Broker responds with Cancelled after reject
    cancelled_line = (
        f"%SLOrder {loc_id} AAPL 100 100 0 0.05 Cancelled LOCATE1 09:10:01 0 {token}"
    )
    await broker.inbound_queue.put(cancelled_line)

    try:
        with pytest.raises(LocateFailedError):
            await asyncio.wait_for(ensure_task, timeout=1)
    finally:
        mgr._reader_task.cancel()  # pylint: disable=protected-access
        with contextlib.suppress(asyncio.CancelledError):
            await mgr._reader_task


# ---------------------------------------------------------------------------
# Already shortable (ETB) – ensure_locate should resolve immediately on %SLRET
# ---------------------------------------------------------------------------


@pytest.mark.asyncio
async def test_already_shortable_via_slret():
    """Verify that %SLRET 2 <symbol> … Already Shortable is treated as success."""
    broker = _StubBroker()
    mgr = DasLocateManager(broker)

    qty_requested = 3.0
    ensure_task = asyncio.create_task(mgr.ensure_locate("MSFT", qty_requested))

    # Wait for SLNEWORDER to be sent
    await _wait_until(lambda: broker.sent_lines)

    # Simulate DAS responding that the symbol is already shortable
    slret_line = "%SLRET 2 MSFT 0 0 Already Shortable"
    await broker.inbound_queue.put(slret_line)

    try:
        await asyncio.wait_for(ensure_task, timeout=1)
        # After success inventory should show used qty
        inv = mgr._inventory["MSFT"]  # pylint: disable=protected-access
        assert inv["used"] == qty_requested
        assert inv["available"] >= qty_requested
    finally:
        mgr._reader_task.cancel()  # pylint: disable=protected-access
        with contextlib.suppress(asyncio.CancelledError):
            await mgr._reader_task
