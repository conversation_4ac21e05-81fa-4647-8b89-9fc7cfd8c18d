*.ipynb

*.csv

scripts/
output/
market_data/
data/


# Clis
*_cli.py
*cli.py
cli/



# Too verbose strats
src/strategies/ltr
src/strategies/ml
src/locates

# unused
order_state_machine.py
ticker_event_buy_strategy.py
substack_ticker_source.py

# Unused stuff
src/optimizer/

# Not important
tests/

# All notebooks
sandbox/

README.md 
requirements.txt
TODO.md
setup.ip
Dockerfile
docker-compose.yml

clear_street_broker.py
indicators.py

yfinance*.py 
market_data_loader_adapter.py
old/
das*.py
ibroker_shortable.py

# Stats
src/stats/*.py

polygon*.py
backtest_substack_ticker.py 
reports/
ltr*.py


# Marketdaat

*ccxt*.py
*yfinance*.py
data_bento_csv_market_data.py
est_market_data_wrapper.py
*theta*.py