# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.txt -o requirements.txt
aiohappyeyeballs==2.6.1
    # via
    #   -r requirements.txt
    #   aiohttp
aiohttp==3.11.14
    # via
    #   -r requirements.txt
    #   instructor
aiosignal==1.3.2
    # via
    #   -r requirements.txt
    #   aiohttp
alpaca-py==0.39.0
    # via -r requirements.txt
annotated-types==0.7.0
    # via
    #   -r requirements.txt
    #   pydantic
anyio==4.9.0
    # via
    #   -r requirements.txt
    #   httpx
    #   openai
attrs==25.3.0
    # via
    #   -r requirements.txt
    #   aiohttp
cachetools==5.5.2
    # via
    #   -r requirements.txt
    #   google-auth
certifi==2025.1.31
    # via
    #   -r requirements.txt
    #   httpcore
    #   httpx
    #   polygon-api-client
    #   requests
charset-normalizer==3.4.1
    # via
    #   -r requirements.txt
    #   requests
click==8.1.8
    # via
    #   -r requirements.txt
    #   typer
contourpy==1.3.1
    # via
    #   -r requirements.txt
    #   matplotlib
cycler==0.12.1
    # via
    #   -r requirements.txt
    #   matplotlib
diskcache==5.6.3
    # via -r requirements.txt
distro==1.9.0
    # via
    #   -r requirements.txt
    #   openai
docstring-parser==0.16
    # via
    #   -r requirements.txt
    #   instructor
eventkit==1.0.3
    # via
    #   -r requirements.txt
    #   ib-async
fonttools==4.56.0
    # via
    #   -r requirements.txt
    #   matplotlib
frozenlist==1.5.0
    # via
    #   -r requirements.txt
    #   aiohttp
    #   aiosignal
google-ai-generativelanguage==0.6.15
    # via
    #   -r requirements.txt
    #   google-generativeai
google-api-core==2.24.2
    # via
    #   -r requirements.txt
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.165.0
    # via
    #   -r requirements.txt
    #   google-generativeai
google-auth==2.38.0
    # via
    #   -r requirements.txt
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via
    #   -r requirements.txt
    #   google-api-python-client
google-generativeai==0.8.4
    # via -r requirements.txt
googleapis-common-protos==1.69.2
    # via
    #   -r requirements.txt
    #   google-api-core
    #   grpcio-status
grpcio==1.71.0
    # via
    #   -r requirements.txt
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via
    #   -r requirements.txt
    #   google-api-core
h11==0.14.0
    # via
    #   -r requirements.txt
    #   httpcore
httpcore==1.0.7
    # via
    #   -r requirements.txt
    #   httpx
httplib2==0.22.0
    # via
    #   -r requirements.txt
    #   google-api-python-client
    #   google-auth-httplib2
httpx==0.28.1
    # via
    #   -r requirements.txt
    #   openai
ib-async==1.0.3
    # via -r requirements.txt
idna==3.10
    # via
    #   -r requirements.txt
    #   anyio
    #   httpx
    #   requests
    #   yarl
instructor==1.7.7
    # via -r requirements.txt
jinja2==3.1.6
    # via
    #   -r requirements.txt
    #   instructor
jiter==0.8.2
    # via
    #   -r requirements.txt
    #   instructor
    #   openai
kiwisolver==1.4.8
    # via
    #   -r requirements.txt
    #   matplotlib
loguru==0.7.3
    # via -r requirements.txt
markdown-it-py==3.0.0
    # via
    #   -r requirements.txt
    #   rich
markupsafe==3.0.2
    # via
    #   -r requirements.txt
    #   jinja2
matplotlib==3.10.1
    # via -r requirements.txt
mdurl==0.1.2
    # via
    #   -r requirements.txt
    #   markdown-it-py
msgpack==1.1.0
    # via
    #   -r requirements.txt
    #   alpaca-py
multidict==6.2.0
    # via
    #   -r requirements.txt
    #   aiohttp
    #   yarl
nest-asyncio==1.6.0
    # via
    #   -r requirements.txt
    #   ib-async
numpy==2.2.4
    # via
    #   -r requirements.txt
    #   contourpy
    #   eventkit
    #   matplotlib
    #   pandas
openai==1.68.2
    # via
    #   -r requirements.txt
    #   instructor
packaging==24.2
    # via
    #   -r requirements.txt
    #   matplotlib
pandas==2.2.3
    # via
    #   -r requirements.txt
    #   alpaca-py
pillow==11.1.0
    # via
    #   -r requirements.txt
    #   matplotlib
polygon-api-client==1.14.4
    # via -r requirements.txt
propcache==0.3.0
    # via
    #   -r requirements.txt
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via
    #   -r requirements.txt
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.4
    # via
    #   -r requirements.txt
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
pyasn1==0.6.1
    # via
    #   -r requirements.txt
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via
    #   -r requirements.txt
    #   google-auth
pydantic==2.10.6
    # via
    #   -r requirements.txt
    #   alpaca-py
    #   google-generativeai
    #   instructor
    #   openai
pydantic-core==2.27.2
    # via
    #   -r requirements.txt
    #   instructor
    #   pydantic
pygments==2.19.1
    # via
    #   -r requirements.txt
    #   rich
pyparsing==3.2.1
    # via
    #   -r requirements.txt
    #   httplib2
    #   matplotlib
python-dateutil==2.9.0.post0
    # via
    #   -r requirements.txt
    #   matplotlib
    #   pandas
python-dotenv==1.0.1
    # via -r requirements.txt
pytz==2025.1
    # via
    #   -r requirements.txt
    #   pandas
pyyaml==6.0.2
    # via -r requirements.txt
requests==2.32.3
    # via
    #   -r requirements.txt
    #   alpaca-py
    #   google-api-core
    #   instructor
rich==13.9.4
    # via
    #   -r requirements.txt
    #   instructor
    #   typer
rsa==4.9
    # via
    #   -r requirements.txt
    #   google-auth
shellingham==1.5.4
    # via
    #   -r requirements.txt
    #   typer
six==1.17.0
    # via
    #   -r requirements.txt
    #   python-dateutil
sniffio==1.3.1
    # via
    #   -r requirements.txt
    #   anyio
    #   openai
sseclient-py==1.8.0
    # via
    #   -r requirements.txt
    #   alpaca-py
tenacity==9.0.0
    # via
    #   -r requirements.txt
    #   instructor
tqdm==4.67.1
    # via
    #   -r requirements.txt
    #   google-generativeai
    #   openai
typer==0.15.2
    # via
    #   -r requirements.txt
    #   instructor
typing-extensions==4.12.2
    # via
    #   -r requirements.txt
    #   google-generativeai
    #   openai
    #   pydantic
    #   pydantic-core
    #   typer
tzdata==2025.1
    # via
    #   -r requirements.txt
    #   pandas
uritemplate==4.1.1
    # via
    #   -r requirements.txt
    #   google-api-python-client
urllib3==2.3.0
    # via
    #   -r requirements.txt
    #   polygon-api-client
    #   requests
websockets==14.2
    # via
    #   -r requirements.txt
    #   alpaca-py
    #   polygon-api-client
yarl==1.18.3
    # via
    #   -r requirements.txt
    #   aiohttp
-e .
pyarrow==19.0.1
quantstats==0.0.64
pandas-market-calendars==4.6.1
exchange_calendars==4.10
fastapi==0.115.6
uvicorn==0.34.0
fastapi-websocket-rpc==0.1.25
pytest==8.3.4
pytest-asyncio==0.25.3