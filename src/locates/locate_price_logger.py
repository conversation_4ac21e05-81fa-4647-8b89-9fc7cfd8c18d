import asyncio, csv, os, logging
from datetime import datetime, timezone
from typing import Tuple
import pytz

from brokers.clear_street_broker import ClearStreetBroker

logger = logging.getLogger(__name__)

class LocatePriceLogger:
    """
    Ask for a locate once a minute, record the price, immediately decline it.
    A value of -1 means 'no locate available within <poll_timeout> seconds'.
    """

    def __init__(
        self,
        broker: ClearStreetBroker,
        csv_path: str = "data/locate_prices.csv",
        poll_timeout: int = 30,              # seconds to wait for an offer
    ):
        self.broker = broker
        self.csv_path = csv_path
        self.poll_timeout = poll_timeout
        self._csv_lock = asyncio.Lock()
        # Eastern timezone for timestamps
        self.eastern_tz = pytz.timezone('US/Eastern')

        os.makedirs(os.path.dirname(csv_path) or ".", exist_ok=True)
        if not os.path.exists(csv_path):
            with open(csv_path, "w", newline="") as f:
                csv.writer(f).writerow(
                    ["est_ts", "symbol", "rate_cps", "total_cost"]  # header updated to EST
                )

    # ---------- public --------------------------------------------------

    async def run_forever(
        self, symbol: str, qty: int = 1, interval: int = 60
    ) -> None:
        """
        Background task – call with `asyncio.create_task(logger.run_forever("AAPL"))`.
        Cancelling the task cleanly stops logging for that symbol.
        Exits immediately for ETB (Easy to Borrow) symbols.
        """
        logger.info(f"Starting locate price logging for {symbol}")
        
        # First check if the symbol is ETB before entering the loop
        is_etb = await self.broker._is_etb(symbol)
        if is_etb:
            logger.info(f"Symbol {symbol} is ETB (Easy to Borrow), stopping locate price logging")
            return
            
        while True:
            t0 = asyncio.get_running_loop().time()
            cost = await self._sample_once(symbol, qty)
            await self._append(symbol, cost)
            
            # Log the cost information
            if cost == -1.0:
                logger.warning(f"No locate available for {symbol} within {self.poll_timeout}s")
            else:
                logger.info(f"Recorded locate for {symbol}: cost={cost}")

            # keep a roughly-`interval` cadence, even if _sample_once took time
            sleep_time = max(0, interval - (asyncio.get_running_loop().time() - t0))
            logger.debug(f"Sleeping for {sleep_time}s until next locate sample")
            await asyncio.sleep(sleep_time)

    # ---------- helpers -------------------------------------------------

    async def _sample_once(self, symbol: str, qty: int) -> float:
        """
        Request a locate, wait until it is 'offered' or until poll_timeout,
        then decline it. Returns total_cost or -1.0 if no locate is available.
        """
        logger.debug(f"Requesting locate for {qty} shares of {symbol}")
        await self.broker._ensure_client()

        try:
            loc_req = await self.broker.client.accounts.locate_orders.create(
                account_id=self.broker.account_id,
                symbol=symbol.upper(),
                quantity=str(qty),
                mpid=self.broker.mpid,
                reference_id=f"log-{symbol}-{datetime.now(tz=timezone.utc).isoformat()}",
            )
            
            loc_id = loc_req.locate_order_id
            logger.debug(f"Locate request submitted: ID {loc_id}")
            deadline = asyncio.get_running_loop().time() + self.poll_timeout

            while asyncio.get_running_loop().time() < deadline:
                status = await self.broker.client.accounts.locate_orders.retrieve(
                    account_id=self.broker.account_id,
                    locate_order_id=loc_id,
                )
                if status.status == "offered":
                    cost = float(status.total_cost) if status.total_cost else -1.0
                    logger.debug(f"Locate offered for {symbol}: cost={cost}")
                    await self._decline_quietly(loc_id)
                    return cost
                if status.status in {"rejected", "expired", "cancelled", "declined"}:
                    logger.warning(f"Locate rejected/expired for {symbol}, status={status.status}")
                    break
                await asyncio.sleep(1)

            logger.debug(f"Timeout waiting for locate offer for {symbol}")
            await self._decline_quietly(loc_id)
            return -1.0
        except Exception as e:
            logger.error(f"Error requesting locate for {symbol}: {str(e)}")
            return -1.0

    async def _decline_quietly(self, loc_id: str) -> None:
        try:
            logger.debug(f"Declining locate order {loc_id}")
            await self.broker.client.accounts.locate_orders.update(
                account_id=self.broker.account_id,
                locate_order_id=loc_id,
                accept=False,
            )
        except Exception as e:
            logger.warning(f"Failed to decline locate {loc_id}: {str(e)}")
            # ignore – the goal is simply "don't leave it open"

    async def _append(self, symbol: str, cost: float) -> None:
        async with self._csv_lock:
            # Convert UTC to Eastern time
            utc_now = datetime.now(timezone.utc)
            est_now = utc_now.astimezone(self.eastern_tz)
            est_timestamp = est_now.isoformat()
            
            with open(self.csv_path, "a", newline="") as f:
                csv.writer(f).writerow(
                    [est_timestamp, symbol.upper(), cost]
                )
            logger.debug(f"Appended locate data for {symbol} to {self.csv_path}")