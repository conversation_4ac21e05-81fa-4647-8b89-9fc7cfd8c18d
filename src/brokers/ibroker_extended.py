from dataclasses import dataclass
from typing import Optional, Dict, Any
from strategies.trade_signals import TradeSignal, SignalType, TradeType
from brokers.ibroker import OrderParams
from datetime import datetime

# ---------------------------------------------------------------------------
# Complex-order parameter objects (built on top of OrderParams)
# ---------------------------------------------------------------------------
@dataclass
class BracketOrderParams(OrderParams):
    action: str = "BUY"                         # BUY / SELL
    stop_loss_price: Optional[float] = None
    stop_limit_offset_pct: float | None = 0.25
    take_profit_price: Optional[float] = None
    entry_type: str = "MARKET"                  # MARKET / LIMIT …

    # -------- factory helper --------
    @classmethod
    def from_trade_signal(cls, sig: "TradeSignal", ts: datetime) -> "BracketOrderParams":
        if sig.signal != SignalType.BRACKET:
            raise ValueError("Expected SignalType.BRACKET")
        return cls(
            symbol=sig.symbol,
            quantity=sig.quantity,
            expect_price=sig.price,
            position_intent="open",
            datetime=ts,
            action="BUY" if sig.trade_type == TradeType.BUY else "SELL",
            stop_loss_price=sig.stop_loss.price if sig.stop_loss else None,
            take_profit_price=sig.take_profit.price if sig.take_profit else None,
            entry_type=sig.entry_condition.order_type.name if sig.entry_condition else "MARKET",
        )

@dataclass
class TrailingStopOrderParams(OrderParams):
    action: str = "BUY"
    initial_stop_price: Optional[float] = None
    trail_amount: Optional[float] = None
    is_percent: bool = True
    entry_type: str = "MARKET"

    # -------- factory helper --------
    @classmethod
    def from_trade_signal(cls, sig: "TradeSignal", ts: datetime) -> "TrailingStopOrderParams":
        if sig.signal != SignalType.TRAILING_STOP:
            raise ValueError("Expected SignalType.TRAILING_STOP")
        return cls(
            symbol=sig.symbol,
            quantity=sig.quantity,
            expect_price=sig.price,
            position_intent="open",
            datetime=ts,
            action="BUY" if sig.trade_type == TradeType.BUY else "SELL",
            initial_stop_price=sig.initial_stop,
            trail_amount=sig.trail_amount,
            is_percent=sig.is_percent,
            entry_type=sig.entry_condition.order_type.name if sig.entry_condition else "MARKET",
        )

class IBrokerExtended:
    """Extended broker interface that includes complex order capabilities."""
    
    async def supports_complex_orders(self) -> bool:
        """Check if broker supports native complex orders."""
        return False
    
    async def bracket_order(
        self,
        params: BracketOrderParams
    ) -> Dict[str, Any]:
        """Place a bracket order with the broker."""
        raise NotImplementedError("Broker does not support bracket orders")
    
    async def trailing_stop_order(
        self,
        params: TrailingStopOrderParams
    ) -> Dict[str, Any]:
        """Place a trailing stop order with the broker."""
        raise NotImplementedError("Broker does not support trailing stop orders")
