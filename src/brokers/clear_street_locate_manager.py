"""
clear_street_locate_manager.py
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Asynchronous locate-manager built for **Clear Street**.
It de-duplicates concurrent locate requests, re-cycles unused
locates, and enforces fee-caps / budget-limits before auto-accepting
locate offers.

The class is BROKER-AGNOSTIC – callers only need an initialised
``AsyncStudioSDK`` plus ``account_id`` & ``mpid``.  It can therefore be
re-used by different broker wrappers (e.g. ``ClearStreetBrokerV2``) to
provide short-selling support without copying large chunks of logic.
"""

from __future__ import annotations

import asyncio
import logging
import secrets
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Tuple

from studio_sdk import AsyncStudioSDK
from marketdata.imarketdata import IMarketData, LastQuote
from studio_sdk import APIError, NotFoundError

_logger = logging.getLogger(__name__)

__all__ = ["LocateFailedError", "ClearStreetLocateManager"]


class LocateFailedError(Exception):
    """Raised when a locate request ultimately cannot be fulfilled."""


@dataclass
class _OutstandingReq:
    qty: float
    expect_price: Optional[float]
    fut: asyncio.Future


@dataclass
class _SymbolState:
    allocated: List[Tuple[str, float, float]] = field(default_factory=list)  # (loc_id, alloc_qty, remaining)
    queue: List[_OutstandingReq] = field(default_factory=list)
    worker: Optional[asyncio.Task] = None
    lock: asyncio.Lock = field(default_factory=asyncio.Lock)


class ClearStreetLocateManager:
    """Async manager that pools & re-uses locates per-symbol.

    Parameters
    ----------
    sdk: AsyncStudioSDK
        An initialised Studio SDK instance (shares HTTP pool & auth).
    account_id: str
        Clear Street account identifier.
    mpid: str
        Market-participant identifier (required for locate calls).
    poll_interval: int, default 5
        Seconds between status polls while waiting for an offered/filled status.
    max_fee_pct: float, default 0.025
        Maximum acceptable locate fee as percentage of *notional* (total_cost ÷ (qty ⋅ expect_price)).
    locate_budget_limit: float | None
        Optional daily budget in USD.  When exceeded, new locates are refused.
    """

    def __init__(
        self,
        account_id: str,
        mpid: str,
        market_data: Optional[IMarketData] = None,
        *,
        poll_interval: int = 5,
        max_fee_pct: float = 0.025,
        locate_budget_limit: float = 250, # $250
    ) -> None:
        self._account_id = account_id
        self._mpid = mpid
        self._md = market_data

        self._poll_interval = poll_interval
        self._max_fee_pct = max_fee_pct
        self._locate_budget_limit = locate_budget_limit
        self._locate_budget_spent = 0.0

        # symbol → state
        self._symbols: Dict[str, _SymbolState] = {}
        self._symbols_lock = asyncio.Lock()

    # ─────────────────────────── public API ────────────────────────────
    async def ensure_locate(
        self,
        symbol: str,
        quantity: float,
        *,
        expect_price: Optional[float] = None,
        timeout: Optional[float] = None,
    ) -> List[Tuple[str, float]]:
        """Ensure *quantity* shares of *symbol* are located & reserved.

        Returns a list of ``(locate_id, qty_reserved)`` pairs once the
        reservation is guaranteed.  If *timeout* expires before the
        locate fills, :class:`asyncio.TimeoutError` is raised **but the
        underlying request continues in background**.
        """
        if quantity <= 0:
            raise ValueError("quantity must be positive")
        symbol = symbol.upper()

        async with self._symbols_lock:
            state = self._symbols.setdefault(symbol, _SymbolState())

        async with state.lock:
            # ── diagnostics ------------------------------------------------
            avail = sum(rem for _, _, rem in state.allocated)
            _logger.debug(
                "ensure_locate(%s): requested=%s, available_pool=%s, alloc_count=%s, queue_len=%s",
                symbol,
                quantity,
                avail,
                len(state.allocated),
                len([q for q in state.queue if not q.fut.done()]),
            )
            # try to satisfy immediately from remaining allocs
            alloc = self._consume_from_pool(state, quantity)
            if alloc is not None:
                return alloc

            # ------------------------------------------------------------------
            # Re-use any existing pending locate request (if its future is still
            # unresolved) instead of enqueueing another duplicate request. This
            # prevents the situation where a speculative pre-allocation
            # (timeout=0) triggers one locate order and a subsequent short sell
            # creates a *second* locate order while the first one is still in
            # flight.  We only create a new queue entry when **no** outstanding
            # future exists for the symbol.
            existing_req = next((req for req in state.queue if not req.fut.done()), None)
            if existing_req is not None and quantity <= existing_req.qty:
                fut = existing_req.fut  # wait on the in-flight locate
            else:
                # enqueue outstanding request and spin up worker if needed
                fut: asyncio.Future = asyncio.get_event_loop().create_future()
                state.queue.append(_OutstandingReq(quantity, expect_price, fut))
                if state.worker is None or state.worker.done():
                    state.worker = asyncio.create_task(
                        self._worker(symbol, state), name=f"locate-{symbol}"
                    )

        # immediate return if caller doesn't want to wait (timeout<=0)
        if timeout is not None and timeout <= 0:
            _logger.debug("ensure_locate called with timeout<=0 – returning immediately for %s", symbol)
            raise asyncio.TimeoutError()

        try:
            return await asyncio.wait_for(fut, timeout=timeout) if timeout else await fut
        except asyncio.TimeoutError:
            _logger.warning("ensure_locate timeout for %s – wait will continue in background", symbol)
            raise

    async def release_locate(self, symbol: str, locate_id: str, qty: float) -> None:
        """Return *qty* shares of *locate_id* back to the available pool."""
        if qty <= 0:
            return
        symbol = symbol.upper()
        async with self._symbols_lock:
            state = self._symbols.get(symbol)
        if not state:
            return
        async with state.lock:
            for idx, (lid, alloc_qty, remaining) in enumerate(state.allocated):
                if lid == locate_id:
                    new_remaining = min(alloc_qty, remaining + qty)
                    state.allocated[idx] = (lid, alloc_qty, new_remaining)
                    _logger.debug("released %.0f shares back to locate %s (%s)", qty, locate_id, symbol)
                    break

    # ------------------------------------------------------------------
    # Simplified release – let manager pick locate IDs
    async def release_quantity(self, symbol: str, quantity: float):
        """Convenience wrapper that releases *quantity* shares for *symbol*
        using the earliest consumed locates.  No-op if nothing allocated."""
        if quantity <= 0:
            return
        symbol = symbol.upper()
        async with self._symbols_lock:
            state = self._symbols.get(symbol)
        if not state:
            return
        async with state.lock:
            remaining_to_release = quantity
            for idx, (loc_id, alloc_qty, rem_qty) in enumerate(state.allocated):
                used = alloc_qty - rem_qty
                if used <= 0:
                    continue
                give_back = min(used, remaining_to_release)
                state.allocated[idx] = (loc_id, alloc_qty, rem_qty + give_back)
                remaining_to_release -= give_back
                _logger.debug("auto-released %.0f shares to locate %s (%s)", give_back, loc_id, symbol)
                if remaining_to_release <= 0:
                    break

    # ------------------------------------------------------------------
    # Context manager – auto-release on exception
    class _LocateContext:
        def __init__(self, mgr: "ClearStreetLocateManager", symbol: str, qty: float,
                     *, expect_price: Optional[float], timeout: Optional[float]):
            self._mgr = mgr
            self._symbol = symbol.upper()
            self._qty = qty
            self._expect_price = expect_price
            self._timeout = timeout
            self._reservations: List[Tuple[str, float]] = []

        async def __aenter__(self):
            self._reservations = await self._mgr.ensure_locate(
                self._symbol,
                self._qty,
                expect_price=self._expect_price,
                timeout=self._timeout,
            )
            return self._reservations

        async def __aexit__(self, exc_type, exc, tb):
            if exc_type is not None:
                # only rollback on error
                for loc_id, qty in self._reservations:
                    await self._mgr.release_locate(self._symbol, loc_id, qty)
            # do not suppress exceptions
            return False

    # expose factory
    def with_locates(
        self,
        symbol: str,
        quantity: float,
        *,
        expect_price: Optional[float] = None,
        timeout: Optional[float] = None,
    ) -> "ClearStreetLocateManager._LocateContext":
        """Return an async context manager that guarantees locates and rolls
        them back automatically if an exception occurs within the block."""
        return ClearStreetLocateManager._LocateContext(self, symbol, quantity, expect_price=expect_price, timeout=timeout)

    # ─────────────────────── internal worker per symbol ───────────────────────
    async def _worker(self, symbol: str, state: _SymbolState):
        """Serially fulfils outstanding locate requests for *symbol*."""
        while True:
            async with state.lock:
                # drop fulfilled futures & short-circuit
                state.queue = [r for r in state.queue if not r.fut.done()]
                if not state.queue:
                    state.worker = None
                    return
                # pending quantity required for the *first* waiter (FIFO)
                pending_qty = state.queue[0].qty
                # try consume again in case pool replenished meanwhile
                alloc = self._consume_from_pool(state, pending_qty)
                if alloc is not None:
                    # satisfy first waiting request
                    first = state.queue.pop(0)
                    if not first.fut.done():
                        first.fut.set_result(alloc)
                    continue
                # no supply → we need fresh locate for *pending_qty*
                expect_price = state.queue[0].expect_price  # use first request's expect_price (may be None)

            try:
                locate_id, located_qty = await self._request_locate(symbol, pending_qty, expect_price)
            except LocateFailedError as exc:
                _logger.error("locate failed for %s: %s", symbol, exc)
                # fail all waiting futures then bail out (caller may retry)
                async with state.lock:
                    for req in state.queue:
                        if not req.fut.done():
                            req.fut.set_exception(exc)
                    state.queue.clear()
                    state.worker = None
                return

            # on success, add to pool & loop – letting next iteration allocate to waiting req(s)
            async with state.lock:
                state.allocated.append((locate_id, located_qty, located_qty))

    async def _market_price_below_expect(self, symbol: str, expect_price: float) -> bool:
        """Return True if the most recent market price is below expect_price.
        Falls back gracefully if no market data provider is configured or data unavailable."""
        if self._md is None:
            return False  # no market data – cannot enforce check
        try:
            quote = await self._md.get_quote_async(symbol)
            if quote is None:
                return False
            latest_price = None
            if quote.ask_price is not None and quote.bid_price is not None:
                latest_price = (quote.ask_price + quote.bid_price) / 2
            else:
                latest_price = quote.ask_price or quote.bid_price
            return latest_price is not None and latest_price < expect_price
        except Exception as exc:
            _logger.debug("quote retrieval failed for %s: %s", symbol, exc)
            return False

    # ───────────────────── locate request lifecycle ──────────────────────
    async def _request_locate(
        self,
        symbol: str,
        quantity: float,
        expect_price: Optional[float],
    ) -> Tuple[str, float]:
        """Create locate at CS and poll until filled, enforcing fee-cap."""
        if self._locate_budget_limit is not None and self._locate_budget_spent >= self._locate_budget_limit:
            raise LocateFailedError("daily locate budget exhausted")

        ref = f"loc-{secrets.token_hex(6)}"
        try:
            req = await self._sdk.accounts.locate_orders.create(
                account_id=self._account_id,
                symbol=symbol,
                quantity=str(quantity),
                mpid=self._mpid,
                reference_id=ref,
            )
        except APIError as exc:
            raise LocateFailedError(f"broker API error: {exc}") from exc

        locate_id = req.locate_order_id
        abs_expiry_ts = (req.expires_at / 1000.0) if req.expires_at else 2 ** 31 - 1

        while True:
            if time.time() >= abs_expiry_ts:
                raise LocateFailedError("locate window closed before acceptable offer")

            await asyncio.sleep(self._poll_interval)
            try:
                st = await self._sdk.accounts.locate_orders.retrieve(
                    account_id=self._account_id, locate_order_id=locate_id
                )
            except NotFoundError:
                raise LocateFailedError(f"locate {locate_id} disappeared")
            except APIError as exc:
                _logger.debug("locate poll error for %s: %s", locate_id, exc)
                continue

            status = st.status
            if status == "offered":
                total_cost = float(st.total_cost or 0)
                located_qty = float(st.located_quantity or 0)
                if expect_price and located_qty:
                    fee_pct = total_cost / (located_qty * expect_price)
                    # Reject if live market price has fallen below expectation
                    if await self._market_price_below_expect(symbol, expect_price):
                        await self._sdk.accounts.locate_orders.update(
                            account_id=self._account_id, locate_order_id=locate_id, accept=False
                        )
                        raise LocateFailedError(
                            f"market price below expected price {expect_price:.2f}"
                        )
                    if fee_pct > self._max_fee_pct:
                        await self._sdk.accounts.locate_orders.update(
                            account_id=self._account_id, locate_order_id=locate_id, accept=False
                        )
                        raise LocateFailedError(
                            f"fee {fee_pct:.2%} > cap {self._max_fee_pct:.2%}")
                over_budget = (
                    self._locate_budget_limit is not None
                    and self._locate_budget_spent + total_cost > self._locate_budget_limit
                )
                if over_budget:
                    await self._sdk.accounts.locate_orders.update(
                        account_id=self._account_id, locate_order_id=locate_id, accept=False
                    )
                    raise LocateFailedError("locate would exceed daily budget")

                # accept
                await self._sdk.accounts.locate_orders.update(
                    account_id=self._account_id, locate_order_id=locate_id, accept=True
                )
                await asyncio.sleep(0.5)
                st_final = await self._sdk.accounts.locate_orders.retrieve(
                    account_id=self._account_id, locate_order_id=locate_id
                )
                if st_final.status != "filled":
                    # optimistic – treat as filled for now
                    _logger.debug("locate %s accepted, waiting for filled", locate_id)
                self._locate_budget_spent += total_cost
                return locate_id, float(st_final.located_quantity or located_qty)

            elif status in {"pending", "sent_to_broker"}:
                continue
            elif status in {"filled"}:
                return locate_id, float(st.located_quantity)
            elif status in {"rejected", "expired", "cancelled", "declined"}:
                raise LocateFailedError(f"locate {locate_id} {status}")
            else:
                _logger.debug("unknown locate status %s for %s", status, locate_id)

    # ─────────────────────── helper: consume pool ───────────────────────
    @staticmethod
    def _consume_from_pool(state: _SymbolState, qty_needed: float) -> Optional[List[Tuple[str, float]]]:
        """Try consume *qty_needed* from *state.allocated*.
        Return list of (locate_id, qty_used) or None if not enough available."""
        _logger.debug("consume_from_pool: need=%s, pool_available=%s", qty_needed, sum(r for *_, r in [(None, None, rem) for _, _, rem in state.allocated]))
        available_total = sum(rem for _, _, rem in state.allocated)
        if available_total < qty_needed:
            _logger.debug("consume_from_pool: insufficient – returning None")
            return None

        reserved: List[Tuple[str, float]] = []
        remaining_to_take = qty_needed
        for idx, (loc_id, alloc_qty, rem_qty) in enumerate(state.allocated):
            if rem_qty <= 0:
                continue
            take = min(rem_qty, remaining_to_take)
            state.allocated[idx] = (loc_id, alloc_qty, rem_qty - take)
            reserved.append((loc_id, take))
            remaining_to_take -= take
            _logger.debug("consume_from_pool: took=%s from %s (remaining_to_take=%s)", take, loc_id, remaining_to_take)
            if remaining_to_take <= 0:
                break
        return reserved 