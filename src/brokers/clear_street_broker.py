import os
import asyncio
import logging
import uuid
import json
from abc import ABC, abstractmethod
from decimal import Decimal
from typing import Optional, List, Dict, Any, Set, Tuple
from dataclasses import dataclass, field
import time

import httpx

from studio_sdk import AsyncStudioSDK
from studio_sdk.types.shared import Position as ClearStreetPosition
from studio_sdk.types.accounts import order_create_params
from studio_sdk import APIError, NotFoundError, UnprocessableEntityError, AuthenticationError, APIConnectionError
from .ibroker import IBroker, OrderParams, ShortLocateResponse, EST, Position
from datetime import datetime
from pytz import timezone
import contextlib

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# --- Custom Error for Locate Failures ---
class LocateFailedError(Exception):
    """Custom exception for failures during the locate process."""
    pass

# --- ClearStreet Broker Implementation ---


class ClearStreetBroker(IBroker):
    """
    IBroker implementation for Clear Street, supporting locates for short selling
    and fetching bearer tokens from a credentials file. Uses module-level logger.
    """

    DEFAULT_LOCATE_POLL_INTERVAL = 5
    DEFAULT_PENDING_POLL_INTERVAL = 5
    DEFAULT_LOCATE_TIMEOUT = 300
    DEFAULT_MAX_LOCATE_PRICE_ACCEPT_PCT = 0.025  # 2.5% default maximum acceptable locate fee
    CREDENTIALS_FILE_ENV_VAR = "CLEAR_STREET_CREDENTIALS_FILE"
    # OAuth token endpoint
    OAUTH_TOKEN_URL = "https://auth.clearstreet.io/oauth/token"

    ETB_CACHE_TTL = 24 * 60 * 60  # 1 day - Added default TTL

    # Add cache attributes inside the class definition
    _etb_cache: Optional[Set[str]] = None
    _etb_cache_lock = asyncio.Lock()

    _fetched_bearer_token: Optional[str] = None
    _token_fetch_lock = asyncio.Lock()

    # dictionary of per-locate locks – keyed by locate_id
    _accept_locks: dict[str, asyncio.Lock] = {}

    def __init__(
        self,
        account_id: str,
        mpid: str,
        bearer_token: Optional[str] = None,  # Highest priority
        credentials_file_path: Optional[str] = None,  # Second priority
        environment: str = "sandbox",
        locate_poll_interval: int = DEFAULT_LOCATE_POLL_INTERVAL,
        locate_timeout: int = DEFAULT_LOCATE_TIMEOUT,
        etb_cache_ttl: int = ETB_CACHE_TTL,  # Add TTL parameter
        max_locate_price_accept_pct: float = DEFAULT_MAX_LOCATE_PRICE_ACCEPT_PCT,  # Maximum acceptable locate fee percentage
        load_existing_locates: bool = False,  # Flag to disable loading existing locates, default to False
        force_limit_orders: bool = True,  # Flag to force limit orders for all 'open' intents,
        locate_budget_limit: float | None = None,
        pending_poll_interval: int = DEFAULT_PENDING_POLL_INTERVAL,
    ):
        """
        Initializes the ClearStreetBroker.

        Bearer token source precedence:
        1. `bearer_token` argument passed directly.
        2. Credentials file specified by `credentials_file_path` argument.
        3. Credentials file specified by `CLEAR_STREET_CREDENTIALS_FILE` environment variable.
        4. `STUDIO_SDK_BEARER_TOKEN` environment variable (handled internally by StudioSDK).

        Args:
            account_id: Your Clear Street account ID or number.
            mpid: Your Market Participant ID (required for locates).
            bearer_token: Your Clear Street API Bearer token (highest priority).
            credentials_file_path: Path to JSON credentials file ({ "client_id": "...", "client_secret": "..." }).
                                   Overrides env var if both are set.
            environment: "sandbox" or "production".
            locate_poll_interval: Seconds between checking locate status.
            locate_timeout: Maximum seconds to wait for a locate offer.
            etb_cache_ttl: Time in seconds to cache ETB list.
            max_locate_price_accept_pct: Maximum acceptable locate fee as a percentage (0.025 = 2.5%).
            load_existing_locates: Flag to disable loading existing locates.
            force_limit_orders: Flag to force limit orders for all hours.
        """
        if not account_id:
            raise ValueError("account_id is required")
        if not mpid:
            raise ValueError("mpid is required for locates")

        self._etb_cache_ttl = etb_cache_ttl  # Store TTL
        self._last_etb_fetch_time = 0.0  # Initialize fetch time
        self._max_locate_price_accept_pct = max_locate_price_accept_pct

        self.account_id = account_id
        self.mpid = mpid
        self.environment = environment.lower()
        self._locate_poll_interval = locate_poll_interval
        self._locate_timeout = locate_timeout
        # Removed self.logger initialization

        # Determine credentials file path (argument overrides environment variable)
        self._creds_file_path = credentials_file_path or os.environ.get(
            self.CREDENTIALS_FILE_ENV_VAR)

        self._direct_bearer_token = bearer_token
        self.client: Optional[AsyncStudioSDK] = None

        # Add a dictionary to track allocated locates
        # Key: symbol, Value: list of tuples (locate_id, allocated_quantity, remaining_quantity)
        self._allocated_locates: Dict[str, List[Tuple[str, float, float]]] = {}
        self._allocated_locates_lock = asyncio.Lock()
        self._load_existing_locates_enabled = load_existing_locates
        self.force_limit_orders = force_limit_orders
        
        # ─── locate-budget safety check ──────────────────────────────────────────
        self._locate_budget_limit   = locate_budget_limit      # e.g. 250.0 ➜ $250/day
        self._locate_budget_spent   = 0.0                      # running tally

        # ─── pending-locate infrastructure ──────────────────────────────────────
        self._pending_locates: dict[str, dict[str, dict[str, Any]]] = {}  # sym → {loc_id → meta}
        self._pending_locates_lock  = asyncio.Lock()
        self._pending_poll_interval = pending_poll_interval
        self._pending_poll_task: asyncio.Task | None = None

    async def _async_init(self):
        """Asynchronous part of initialization, including token fetching and loading existing locates."""
        if self.client:
            return

        final_bearer_token = self._direct_bearer_token
        token_source = "direct argument"

        if not final_bearer_token and self._creds_file_path:
            logger.info(
                f"No direct bearer token provided, attempting to fetch from credentials file: {self._creds_file_path}")
            async with self._token_fetch_lock:
                if ClearStreetBroker._fetched_bearer_token:
                    final_bearer_token = ClearStreetBroker._fetched_bearer_token
                    token_source = "cached from credentials file"
                else:
                    try:
                        final_bearer_token = await self._get_token_from_creds(self._creds_file_path)
                        ClearStreetBroker._fetched_bearer_token = final_bearer_token
                        token_source = "credentials file"
                        logger.info(
                            "Successfully fetched bearer token using credentials file.")
                    except Exception as e:
                        logger.error(
                            f"Failed to get bearer token from credentials file '{self._creds_file_path}': {e}")
                        final_bearer_token = None

        if not final_bearer_token:
            # Use the actual env var name defined by the SDK if available, otherwise a placeholder
            sdk_token_env = getattr(
                AsyncStudioSDK, 'ENV_BEARER_TOKEN', 'STUDIO_SDK_BEARER_TOKEN')
            token_source = f"SDK environment variable ({sdk_token_env})"
            logger.info(
                f"No direct token or credentials file token, relying on {token_source}.")

        try:
            self.client = AsyncStudioSDK(
                bearer_token=final_bearer_token,
                environment=self.environment,
            )
            logger.info(
                f"ClearStreetBroker initialized for account {self.account_id} in {self.environment} environment. Token source: {token_source}.")
            # Load existing locates after client is initialized
            if self._load_existing_locates_enabled:
                await self._load_existing_locates()
            # start background task once the client is ready
            if not self._pending_poll_task or self._pending_poll_task.done():
                self._pending_poll_task = asyncio.create_task(
                    self._poll_pending_locates(), name="PendingLocatePoll"
                )
        except Exception as e:
            logger.error(f"Failed to initialize AsyncStudioSDK: {e}")
            if not final_bearer_token:
                sdk_token_env = getattr(
                    AsyncStudioSDK, 'ENV_BEARER_TOKEN', 'STUDIO_SDK_BEARER_TOKEN')
                logger.error(
                    f"Ensure {sdk_token_env} is set or provide credentials.")
            raise ConnectionError(
                f"Failed to initialize Clear Street client: {e}") from e

    async def _load_existing_locates(self):
        """Load existing accepted locates from the broker to support restart scenarios."""
        await self._ensure_client()
        
        logger.info("Loading existing accepted locates...")
        try:
            locate_orders_response = await self.client.accounts.locate_orders.list(account_id=self.account_id)
            
            async with self._allocated_locates_lock:
                # Clear existing cache first
                self._allocated_locates = {}
                
                # Process only accepted/filled locates
                for locate in locate_orders_response.data:
                    # Check if status is "filled" (previously called "accepted")
                    if locate.status in ["filled"]:
                        symbol = locate.symbol.upper()
                        # default to 0.0 if empty or None
                        requested_qty = float(locate.requested_quantity) if locate.requested_quantity else 0.0
                        # if located_quantity is empty/None, use requested_qty
                        located_qty = float(locate.located_quantity) if locate.located_quantity else requested_qty
                        locate_id   = locate.locate_order_id if locate.locate_order_id else None                        
                        # Add to our tracking dictionary
                        if symbol not in self._allocated_locates:
                            self._allocated_locates[symbol] = []
                            
                        self._allocated_locates[symbol].append((locate_id, located_qty, located_qty))
                        logger.info(f"Loaded existing locate for {symbol}: ID {locate_id}, Quantity {located_qty}")
                
            total_locates = sum(len(locates) for locates in self._allocated_locates.values())
            logger.info(f"Loaded {total_locates} existing locates for {len(self._allocated_locates)} symbols")
            
        except Exception as e:
            logger.error(f"Error loading existing locates: {e}")
            raise

    async def _ensure_client(self):
        """Ensures the async client is initialized."""
        if not self.client:
            await self._async_init()
        if not self.client:
            raise ConnectionError(
                "Clear Street client could not be initialized.")

    async def pre_allocate_short_locate(self, params: OrderParams) -> ShortLocateResponse:
        """
        Requests a pre-allocation of shares for shorting (locate) before placing an order.
        Always gets a fresh locate rather than attempting to use existing ones.
        
        Parameters:
            params (OrderParams): Order parameters containing symbol and quantity.
            
        Returns:
            ShortLocateResponse: An object detailing the success or failure of the locate request.
        """
        await self._ensure_client()
        
        symbol = params.symbol.upper()
        quantity = params.quantity
        expect_price = params.expect_price
        
        if self._locate_budget_limit is not None and self._locate_budget_spent >= self._locate_budget_limit:
            raise LocateFailedError(
                f"Locate budget ${self._locate_budget_limit:,.2f} exhausted – new locates disabled."
            )

        if quantity <= 0:
            return ShortLocateResponse(
                success=False,
                symbol=symbol,
                requested_quantity=quantity,
                located_quantity=0.0,
                message="Quantity must be positive"
            )
        
        logger.info(f"Pre-allocating short locate for {quantity} shares of {symbol}")
        
        # Check if symbol is Easy-to-Borrow (ETB)
        is_etb = await self._is_etb(symbol)
        if is_etb:
            logger.info(f"Symbol {symbol} is Easy-to-Borrow (ETB). No locate needed.")
            return ShortLocateResponse(
                success=True,
                symbol=symbol,
                requested_quantity=quantity,
                located_quantity=quantity,
                message="Symbol is Easy-to-Borrow (ETB)"
            )
        
        # Schedule a locate in the background via _perform_locate
        logger.info(f"Symbol {symbol} is NOT ETB. Scheduling locate in background...")
        try:
            locate_order_id, _ = await self._perform_locate(symbol, quantity, expect_price, background_only=True)
            return ShortLocateResponse(
                success=True,
                symbol=symbol,
                requested_quantity=quantity,
                located_quantity=0.0,
                locate_id=locate_order_id,
                message=f"Locate request scheduled: {locate_order_id}"
            )
        except Exception as e:
            logger.error(f"Error scheduling locate for {symbol}: {e}")
            return ShortLocateResponse(
                success=False,
                symbol=symbol,
                requested_quantity=quantity,
                located_quantity=0.0,
                message=f"Error scheduling locate: {e}"
            )

    async def _get_token_from_creds(self, credentials_file: str) -> str:
        """
        Asynchronously get an access token using OAuth client credentials flow from a file.
        """
        logger.debug(
            f"Attempting to read credentials from: {credentials_file}")
        try:
            with open(credentials_file, 'r') as f:
                creds = json.load(f)

            client_id = creds.get("client_id")
            client_secret = creds.get("client_secret")

            if not client_id or not client_secret:
                raise ValueError(
                    "Credentials file must contain 'client_id' and 'client_secret'")

            payload = {
                "grant_type": "client_credentials",
                "client_id": client_id,
                "client_secret": client_secret,
                "audience": "https://api.clearstreet.io"
            }
            headers = {"accept": "application/json",
                       "content-type": "application/json"}

            logger.debug(f"Requesting token from {self.OAUTH_TOKEN_URL}")
            async with httpx.AsyncClient(timeout=20.0) as client:
                response = await client.post(self.OAUTH_TOKEN_URL, json=payload, headers=headers)
                response.raise_for_status()
                token_data = response.json()

            access_token = token_data.get("access_token")
            if not access_token:
                raise ValueError("Access token not found in OAuth response.")

            logger.debug("Successfully obtained access token via OAuth.")
            return access_token

        except FileNotFoundError:
            logger.error(f"Credentials file not found: '{credentials_file}'")
            raise
        except json.JSONDecodeError:
            logger.error(
                f"Invalid JSON in credentials file: '{credentials_file}'")
            raise
        except (ValueError, KeyError) as e:
            logger.error(f"Error processing credentials file or response: {e}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Network error requesting OAuth token: {e}")
            raise ConnectionError(
                f"Network error during token fetch: {e}") from e
        except httpx.HTTPStatusError as e:
            logger.error(
                f"HTTP error requesting OAuth token: Status {e.response.status_code}, Response: {e.response.text}")
            raise AuthenticationError(
                f"OAuth token request failed: Status {e.response.status_code}") from e
        except Exception as e:
            logger.error(
                f"Unexpected error getting token from credentials: {e}")
            raise

    async def _perform_locate(self, symbol: str, quantity: int, expect_price: Optional[float] = None, background_only: bool = False) -> Tuple[str, float]:
        """
        Handles the locate request, polling, and acceptance process.
        
        Args:
            symbol: The stock symbol to locate
            quantity: Number of shares to locate
            expect_price: Expected price of the stock (used for locate fee comparison)
            
        Returns:
            The locate order ID and located quantity if successful
            
        Raises:
            LocateFailedError: If the locate request fails or the fee is too high
        """
        """
        If another locate for *symbol* is already in our pending queue,
        poll that one first before we fire off a brand-new request.
        """
        # If caller only wants to enqueue and not wait, schedule and return immediately
        if background_only:
            await self._ensure_client()
            ref_id = f"locate-{uuid.uuid4()}"
            locate_req = await self.client.accounts.locate_orders.create(
                account_id=self.account_id,
                symbol=symbol.upper(),
                quantity=str(quantity),
                mpid=self.mpid,
                reference_id=ref_id,
            )
            locate_order_id = locate_req.locate_order_id
            abs_expiry_ts = (locate_req.expires_at / 1000.0) if locate_req.expires_at else (2**31 - 1)
            async with self._pending_locates_lock:
                self._pending_locates.setdefault(symbol, {})[locate_order_id] = {
                    "requested_quantity": quantity,
                    "expect_price": expect_price,
                    "expires_at": abs_expiry_ts,
                }
            return locate_order_id, 0.0
        # reuse existing pending if any
        reused = await self._try_wait_pending_locate(symbol, quantity, expect_price)
        if reused:
            return reused
        await self._ensure_client()
        ref_id = f"locate-{uuid.uuid4()}"
        logger.info(
            f"Requesting locate for {quantity} shares of {symbol} (Ref: {ref_id})")

        if expect_price is None:
            logger.warning(f"No expected price provided for {symbol}. Locate fee checking will be disabled.")

        if self._locate_budget_limit is not None and self._locate_budget_spent >= self._locate_budget_limit:
            raise LocateFailedError(
                f"Locate budget ${self._locate_budget_limit:,.2f} exhausted – new locates disabled."
            )
            
        try:
            locate_req = await self.client.accounts.locate_orders.create(
                account_id=self.account_id, symbol=symbol.upper(), quantity=str(quantity),
                mpid=self.mpid, reference_id=ref_id,
            )
            locate_order_id = locate_req.locate_order_id
            abs_expiry_ts   = (locate_req.expires_at / 1000.0) if locate_req.expires_at else (2**31 - 1)

            expire_dt_est = datetime.fromtimestamp(abs_expiry_ts, tz=EST)
            expire_str = expire_dt_est.strftime('%Y-%m-%d %H:%M:%S')
            logger.info(
                f"[{symbol.upper()}] Locate request {locate_order_id} expires at {expire_str} EST"
            )
            logger.info(
                f"[{symbol.upper()}] Locate request submitted: ID {locate_order_id}, Initial Status: {locate_req.status}")

            start_time = time.monotonic()
            while True:
                now_monotonic = time.monotonic()
                if (now_monotonic - start_time) > self._locate_timeout or time.time() >= abs_expiry_ts:
                    # we're out of runway — decide if we should park it or just give up
                    if time.time() < abs_expiry_ts:
                        async with self._pending_locates_lock:
                            self._pending_locates.setdefault(symbol, {})[locate_order_id] = {
                                "requested_quantity": quantity,
                                "expect_price": expect_price,
                                "expires_at": abs_expiry_ts,
                            }
                        logger.info(f"[{symbol.upper()}] Locate {locate_order_id} timed-out locally; moved to pending queue")
                    else:
                        logger.info(f"[{symbol.upper()}] Locate {locate_order_id} for {symbol} expired at broker")
                    raise LocateFailedError("Locate window closed before offer was acceptable")
                
                await asyncio.sleep(self._locate_poll_interval)

                try:
                    locate_status = await self.client.accounts.locate_orders.retrieve(
                        account_id=self.account_id, locate_order_id=locate_order_id
                    )
                    if locate_status.expires_at is not None:
                        abs_expiry_ts = (locate_status.expires_at / 1000.0)
                    status = locate_status.status
                    logger.debug(
                        f"[{symbol.upper()}] Polling locate ID {locate_order_id}: Status = {status}")

                    if status == "offered":
                        # Check if the locate total cost is within acceptable bounds
                        if locate_status.total_cost and locate_status.located_quantity and expect_price:
                            try:
                                total_cost = float(locate_status.total_cost)
                                quantity = float(locate_status.located_quantity)
                                # Calculate fee as a percentage of stock cost
                                fee_percent = total_cost / (quantity * expect_price)
                                logger.info(
                                    f"Locate offer for {symbol}: Total cost ${total_cost:.2f}, "
                                    f"{fee_percent:.2%} of expected stock cost "
                                    f"(${quantity * expect_price:.2f})")
                            except (ValueError, TypeError) as e:
                                logger.warning(
                                    f"Could not parse total cost or quantity for {symbol}: {e}. Proceeding with acceptance.")
                            else:
                                if fee_percent > self._max_locate_price_accept_pct:
                                    logger.warning(
                                        f"Rejecting locate offer for {symbol}: Fee {fee_percent:.2%} exceeds "
                                        f"maximum allowed {self._max_locate_price_accept_pct:.2%}")
                                    # Attempt to reject the locate offer
                                    await self.client.accounts.locate_orders.update(
                                        account_id=self.account_id, locate_order_id=locate_order_id, accept=False
                                    )
                                    raise LocateFailedError(
                                        f"Locate fee {fee_percent:.2%} for {symbol} exceeds maximum allowed {self._max_locate_price_accept_pct:.2%}")
                        else:
                            logger.warning(
                                f"Locate offer for {symbol} has no total cost or quantity information or no expected price provided. "
                                f"Proceeding without price check.")

                        logger.info(
                            f"Locate offer received for {symbol} ID {locate_order_id}. Accepting...")
                        await self._safe_accept(locate_order_id)
                        await asyncio.sleep(0.5)
                        final_status = await self.client.accounts.locate_orders.retrieve(
                            account_id=self.account_id, locate_order_id=locate_order_id
                        )
                        
                        # Updated to check for "filled" status instead of "accepted"
                        if final_status.status == "filled":
                            self._locate_budget_spent += float(locate_status.total_cost or 0)
                            logger.info(
                                f"Locate ID {locate_order_id} for {symbol} successfully filled.")
                            return locate_order_id, float(final_status.located_quantity)
                        else:
                            logger.warning(
                                f"Status for locate ID {locate_order_id} not 'filled' immediately after update. " 
                                f"Current status: {final_status.status}. Proceeding cautiously.")
                            return locate_order_id, float(final_status.located_quantity)

                    elif status in ["pending", "sent_to_broker"]:
                        continue
                    elif status in ["rejected", "expired", "cancelled", "declined"]:
                        raise LocateFailedError(
                            f"Locate request for {symbol} failed or was rejected. Status: {status}")
                    elif status == "filled":
                        logger.info(
                            f"Locate ID {locate_order_id} for {symbol} is already filled.")
                        return locate_order_id, float(locate_status.located_quantity)
                    else:
                        raise LocateFailedError(
                            f"Unknown locate status '{status}' for {symbol} ID {locate_order_id}")

                except NotFoundError:
                    raise LocateFailedError(
                        f"Locate order ID {locate_order_id} not found during polling.")
                except APIError as poll_err:
                    logger.warning(
                        f"API error polling locate ID {locate_order_id}: {poll_err}. Retrying...")

        except APIError as e:
            logger.error(f"API Error during locate process for {symbol}: {e}")
            raise LocateFailedError(f"API Error during locate: {e}") from e
        except Exception as e:
            logger.error(
                f"Unexpected error during locate process for {symbol}: {e}")
            raise LocateFailedError(
                f"Unexpected error during locate: {e}") from e

    async def _try_wait_pending_locate(
        self,
        symbol: str,
        quantity: int,
        expect_price: Optional[float] = None,
        wait_timeout: int | None = None,
    ) -> Optional[Tuple[str, float]]:
        """
        Look for a *live* pending locate in self._pending_locates for this symbol.
        If found, poll it for up to ``wait_timeout`` seconds (default 30).
        Returns (locate_id, located_qty) when it becomes ``filled``.
        If it expires/declines or times‐out locally, returns None so the caller
        can fire a fresh locate request.
        """
        if wait_timeout is None:
            wait_timeout = self._locate_timeout

        async with self._pending_locates_lock:
            pending_items = list(self._pending_locates.get(symbol, {}).items())

        if not pending_items:
            return None

        start = time.monotonic()
        while time.monotonic() - start < wait_timeout:
            for locate_id, meta in pending_items:
                try:
                    st = await self.client.accounts.locate_orders.retrieve(
                        account_id=self.account_id, locate_order_id=locate_id
                    )
                    status = st.status
                    if status == "filled":
                        located_qty = float(st.located_quantity)
                        # move from pending → allocated
                        async with self._allocated_locates_lock:
                            self._allocated_locates.setdefault(symbol, []).append(
                                (locate_id, located_qty, located_qty)
                            )
                        async with self._pending_locates_lock:
                            self._pending_locates[symbol].pop(locate_id, None)
                        logger.info(
                            f"Re‐used existing locate {locate_id} for {symbol} "
                            f"instead of creating a new request."
                        )
                        return locate_id, located_qty

                    elif status in {"expired","rejected","cancelled","declined"}:
                        # drop it – caller will fall through and request a fresh locate
                        async with self._pending_locates_lock:
                            self._pending_locates[symbol].pop(locate_id, None)
                except Exception as e:
                    logger.debug(f"wait‐pending poll failed for {locate_id}: {e}")

            await asyncio.sleep(self._locate_poll_interval)

        logger.error(
            f"Pending locate(s) for {symbol} timed out after {wait_timeout}s; not requesting new locate."
        )
        raise LocateFailedError(
            f"Pending locate(s) for {symbol} timed out after {wait_timeout}s"
        )

    async def _safe_accept(self, locate_id: str) -> None:
        """Accept a locate under a per-locate asyncio.Lock to avoid double-accepts."""
        lock = self._accept_locks.setdefault(locate_id, asyncio.Lock())
        async with lock:
            await self.client.accounts.locate_orders.update(
                account_id=self.account_id,
                locate_order_id=locate_id,
                accept=True,
            )

    # --- Core Broker Methods ---

    async def close_connection(self):
        """Closes the underlying HTTP client connection."""
        if self._pending_poll_task:
            self._pending_poll_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._pending_poll_task
        if self.client:
            try:
                await self.client.close()
                logger.info("Clear Street client connection closed.")
                self.client = None
            except Exception as e:
                logger.error(
                    f"Error closing Clear Street client connection: {e}")
                

    async def _poll_pending_locates(self) -> None:
        """Continuously watch locate orders that *didn't* reach 'offered' in time.
        If they become attractive (fee ≤ pct-cap **and** within $-budget) they are
        auto-accepted; otherwise they are rejected & removed."""
        try:
            while True:
                await asyncio.sleep(self._pending_poll_interval)

                async with self._pending_locates_lock:
                    items = [(sym, list(locs.items())) for sym, locs in self._pending_locates.items()]

                for symbol, loc_pairs in items:
                    for locate_id, meta in loc_pairs:
                        try:
                            if time.time() >= meta["expires_at"]:
                                async with self._pending_locates_lock:
                                    self._pending_locates[symbol].pop(locate_id, None)
                                    logger.info(f"Pending locate {locate_id} for {symbol} expired & removed")
                                    continue

                            st = await self.client.accounts.locate_orders.retrieve(
                                account_id=self.account_id, locate_order_id=locate_id
                            )
                            status = st.status
                            if status == "offered":
                                total_cost   = float(st.total_cost or 0)
                                located_qty  = float(st.located_quantity or 0)
                                expect_price = meta.get("expect_price")
                                fee_pct      = total_cost / (located_qty * expect_price)                                                

                                over_pct_cap  = fee_pct > self._max_locate_price_accept_pct
                                over_budget   = (self._locate_budget_limit is not None and
                                                self._locate_budget_spent + total_cost >
                                                self._locate_budget_limit)

                                if not over_pct_cap and not over_budget:
                                    await self.client.accounts.locate_orders.update(
                                        account_id=self.account_id,
                                        locate_order_id=locate_id,
                                        accept=True,
                                    )
                                    logger.info(f"Auto-accepted locate {locate_id} for {symbol}")
                                    self._locate_budget_spent += total_cost
                                    async with self._allocated_locates_lock:
                                        self._allocated_locates.setdefault(symbol, []).append(
                                            (locate_id, located_qty, located_qty)
                                        )
                                else:
                                    await self.client.accounts.locate_orders.update(
                                        account_id=self.account_id,
                                        locate_order_id=locate_id,
                                        accept=False,
                                    )
                                    logger.info(f"Rejected locate {locate_id} (pct/budget)")

                                async with self._pending_locates_lock:
                                    self._pending_locates[symbol].pop(locate_id, None)

                            elif status in {"expired", "rejected", "cancelled", "declined"}:
                                async with self._pending_locates_lock:
                                    self._pending_locates[symbol].pop(locate_id, None)
                        except Exception as e:
                            logger.warning(f"Pending-locate poll failed for {locate_id}: {e}")
        except asyncio.CancelledError:
            logger.info("Pending-locate poll task cancelled")


    async def check_connection(self) -> bool:
        """Checks API connection by attempting to retrieve account details."""
        logger.debug("Checking Clear Street connection...")
        try:
            await self._ensure_client()
            await self.client.accounts.retrieve(account_id=self.account_id)
            logger.debug("Clear Street connection successful.")
            return True
        except AuthenticationError:
            logger.error(
                "Clear Street Authentication Error. Check token source (direct, creds file, or env var).")
            return False
        except APIConnectionError as e:
            logger.error(f"Clear Street API Connection Error: {e}")
            return False
        except ConnectionError as e:
            logger.error(f"Clear Street client initialization failed: {e}")
            return False
        except APIError as e:
            logger.error(
                f"Clear Street API Error during connection check: {e} (Status: {getattr(e, 'status_code', 'N/A')})")
            return False
        except Exception as e:
            logger.error(
                f"Unexpected error during Clear Street connection check: {e}")
            return False

    async def _create_order(
        self,
        symbol: str,
        quantity: int,
        side: str,
        order_type: str = "market",
        time_in_force: str = "day",
        price: Optional[str] = None,
        stop_price: Optional[str] = None,
        reference_id: Optional[str] = None,
        datetime: Optional[datetime] = None,
        expect_price: Optional[float] = None,
        position_intent: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Internal helper to create an order. Automatically handles pre-market hours."""
        await self._ensure_client()
        if quantity <= 0:
            raise ValueError("Order quantity must be positive.")
        
        # only force limit orders for opening orders
        if self.force_limit_orders and position_intent == "open":
            logger.info("Force limit orders enabled. Using limit order for all hours.")
            if expect_price is None:
                raise ValueError("Force limit orders require expect_price")
            order_type = "limit"
            time_in_force = "day"
            expect_price = round(expect_price, 2)
            price = str(expect_price)
        
        # Handle pre-market hours if datetime is provided
        if datetime and self._is_pre_market(datetime):
            logger.info("Pre-market hours detected. Using limit order.")
            if expect_price is None:
                raise ValueError("Pre-market limit orders require expect_price")
            order_type = "limit"
            time_in_force = "day-plus"
            expect_price = round(expect_price, 2)
            price = str(expect_price)

        order_params = {
            "account_id": self.account_id, "symbol": symbol.upper(), "quantity": str(quantity),
            "side": side, "order_type": order_type, "time_in_force": time_in_force,
        }
        if price and order_type == "limit":
            # Round to 2 decimal places if it's a string representation of a float
            try:
                price_float = float(price)
                price = str(round(price_float, 2))
            except ValueError:
                # If conversion fails, keep original price
                pass
            order_params["price"] = price
        if stop_price and order_type in ["stop", "stop_limit"]:
            # Round stop price to 2 decimal places as well
            try:
                stop_price_float = float(stop_price)
                stop_price = str(round(stop_price_float, 2))
            except ValueError:
                pass
            order_params["stop_price"] = stop_price
        if reference_id:
            order_params["reference_id"] = reference_id

        logger.info(f"Placing order: {order_params}")
        try:
            order_response = await self.client.accounts.orders.create(**order_params)
            order_id = order_response.order_id
            logger.info(f"Order submitted successfully: ID {order_id}")

            # If limit, delegate to helper that polls & logs slippage
            if order_type.lower() == "limit":
                return await self._wait_for_limit_order_fill(
                    order_id=order_id,
                    symbol=symbol.upper(),
                    side=side,
                    order_type=order_type,
                    expect_price=expect_price,
                    quantity=quantity,
                )

            # Otherwise (market/etc.) return immediately
            return {
                "order_id": order_id,
                "status":   "submitted",
                "symbol":   symbol.upper(),
                "quantity": float(quantity),
                "side":     side,
                "type":     order_type,
            }
        except UnprocessableEntityError as e:
            logger.error(
                f"Order rejected by Clear Street: {e} - Response: {getattr(e, 'body', 'N/A')}")
            raise
        except APIError as e:
            logger.error(
                f"API Error placing order: {e} (Status: {getattr(e, 'status_code', 'N/A')})")
            raise
        except Exception as e:
            logger.error(f"Unexpected error placing order: {e}")
            raise

    async def _wait_for_limit_order_fill(
        self,
        order_id: str,
        symbol: str,
        side: str,
        order_type: str,
        expect_price: Optional[float],
        quantity: int,
    ) -> Dict[str, Any]:
        """
        Poll a limit order until it's filled (up to 5min), log slippage vs expect_price,
        cancel & error if timeout.
        """
        poll_interval = 5       # seconds between polls
        timeout       = 60  # total wait time in seconds
        start         = asyncio.get_event_loop().time()
        slip_amt = slip_pct = None

        while True:
            elapsed = asyncio.get_event_loop().time() - start
            if elapsed > timeout:
                logger.error(f"Timeout ({timeout}s) waiting for fill: ID {order_id}")
                try:
                    await self.client.accounts.orders.cancel(
                        order_id, account_id=self.account_id
                    )
                    logger.info(f"Limit order {order_id} cancelled due to timeout")
                except Exception as cancel_err:
                    logger.error(f"Failed to cancel order {order_id}: {cancel_err}")
                raise TimeoutError(f"Limit order {order_id} not filled in {timeout}s")

            await asyncio.sleep(poll_interval)
            try:
                resp   = await self.client.accounts.orders.retrieve(
                    order_id, account_id=self.account_id
                )
                ord    = resp.order
                status = ord.status

                if status == "filled":
                    filled_qty = float(ord.filled_quantity)
                    avg_price = ord.average_price

                    if expect_price and avg_price is not None:
                        slip_amt = avg_price - expect_price
                        slip_pct = slip_amt / expect_price
                        logger.info(
                            f"Limit order {order_id} filled: avg_price={avg_price}, "
                            f"expected={expect_price}, slippage={slip_pct:.2%}"
                        )

                    return {
                        "order_id":        order_id,
                        "status":          status,
                        "symbol":          symbol,
                        "quantity":        filled_qty,
                        "side":            side,
                        "type":            order_type,
                        "avg_price":       avg_price,
                        "slippage_amount": slip_amt if expect_price and avg_price is not None else None,
                        "slippage_pct":    slip_pct if expect_price and avg_price is not None else None,
                    }

                logger.debug(f"Limit order {order_id} status: {status}; retrying...")
            except APIError as e:
                logger.warning(f"Error retrieving order {order_id}: {e}; retrying...")

    async def market_buy(self, params: OrderParams) -> Dict[str, Any]:
        """
        Places a market buy order.
        
        Parameters:
            params (OrderParams): Order parameters object containing all required fields.
        """
        await self._ensure_client()

        # Validate position_intent
        if not params.position_intent or params.position_intent not in ["open", "close"]:
            raise ValueError(
                "position_intent must be provided and be either 'open' or 'close'")

        logger.info(
            f"Market Buy: {params.quantity} {params.symbol} (Intent: {params.position_intent}, Expected Price: {params.expect_price})")
        
        return await self._create_order(
            symbol=params.symbol, 
            quantity=params.quantity, 
            side="buy", 
            order_type="market", 
            time_in_force="day",
            datetime=params.datetime,
            expect_price=params.expect_price,
            position_intent=params.position_intent,
        )

    async def _get_current_position(self, symbol: str) -> Optional[float]:
        """Helper to get current position quantity for a symbol."""
        await self._ensure_client()
        try:
            positions = await self.list_positions()
            for pos in positions:
                if pos.get('symbol') == symbol.upper():
                    return pos.get('quantity', 0.0)
            return 0.0
        except Exception as e:
            logger.warning(
                f"Could not retrieve position for {symbol} to determine sell intent: {e}")
            return None

    async def market_sell(self, params: OrderParams) -> Dict[str, Any]:
        """
        Places a market sell order. If position_intent is 'open', checks for pre-allocated
        locates before attempting to get a new one.
        """
        await self._ensure_client()
        if not params.position_intent or params.position_intent not in ["open", "close"]:
            raise ValueError("position_intent must be provided and be either 'open' or 'close'")
        
        symbol = params.symbol.upper()
        quantity = params.quantity
        expect_price = params.expect_price
        position_intent = params.position_intent
        
        logger.info(f"Market Sell: {quantity} {symbol} (Intent: {position_intent}, Expected Price: {expect_price})")
        if quantity <= 0:
            raise ValueError("Sell quantity must be positive.")

        # Track locates consumed for this order so we can roll them back on failure
        used_locates: List[Tuple[str, float]] = []

        side: str
        if position_intent == "open":
            side = "sell-short"  # Use sell-short for opening positions
            logger.info("Position intent is 'open'. Checking ETB status...")
            is_etb = await self._is_etb(symbol)

            if is_etb:
                logger.info(f"Symbol {symbol} is Easy-to-Borrow (ETB). Skipping locate.")
            else:
                logger.info(f"Symbol {symbol} is NOT ETB. Checking for pre-allocated locates...")
                
                # Check if we have pre-allocated locates we can use
                has_sufficient_locate = False
                locate_ids = []
                
                async with self._allocated_locates_lock:
                    if symbol in self._allocated_locates:
                        available_quantity = 0.0
                        # For each existing locate for this symbol
                        for i, (locate_id, allocated_qty, remaining_qty) in enumerate(self._allocated_locates[symbol]):
                            if remaining_qty > 0:
                                available_from_this_locate = min(remaining_qty, quantity - available_quantity)
                                available_quantity += available_from_this_locate
                                
                                if available_from_this_locate > 0:
                                    locate_ids.append(locate_id)
                                    used_locates.append((locate_id, available_from_this_locate))
                                    # Consume that amount from remaining_qty
                                    self._allocated_locates[symbol][i] = (locate_id, allocated_qty, remaining_qty - available_from_this_locate)
                                
                                if available_quantity >= quantity:
                                    has_sufficient_locate = True
                                    break
                
                if has_sufficient_locate:
                    logger.info(f"Using pre-allocated locates for {symbol}: {', '.join(locate_ids)}")
                else:
                    logger.info(f"No pre-allocated locates available for {symbol}. Requesting new locate...")
                    try:
                        # Pass expected price to locate function
                        locate_order_id, located_qty = await self._perform_locate(symbol, quantity, expect_price)
                        logger.info(f"Locate successful (ID: {locate_order_id}). Proceeding with short sell order for {located_qty} {symbol}.")
                        
                        # Add this locate to our tracking map
                        async with self._allocated_locates_lock:
                            if symbol not in self._allocated_locates:
                                self._allocated_locates[symbol] = []
                            
                            # We are using this entire locate immediately
                            used_locates.append((locate_order_id, located_qty))
                            self._allocated_locates[symbol].append((locate_order_id, located_qty, 0.0))
                    except LocateFailedError as e:
                        logger.error(f"Locate failed for {symbol}: {e}. Cannot place short sell order.")
                        raise
        else:  # position_intent == "close"
            side = "sell"  # Use regular sell for closing longs
            logger.info("Position intent is 'close', closing long position, locate not required.")
            # No locate check needed

        # Place the order, restoring any consumed locates if the order fails
        try:
            return await self._create_order(
                symbol=symbol, 
                quantity=quantity, 
                side=side, 
                order_type="market", 
                time_in_force="day",
                datetime=params.datetime,
                expect_price=params.expect_price,
                position_intent=position_intent,
            )
        except Exception:
            if used_locates:
                async with self._allocated_locates_lock:
                    for loc_id, consumed_qty in used_locates:
                        for idx, (l_id, alloc_qty, rem_qty) in enumerate(self._allocated_locates.get(symbol, [])):
                            if l_id == loc_id:
                                # roll back the consumed amount
                                new_rem = min(alloc_qty, rem_qty + consumed_qty)
                                self._allocated_locates[symbol][idx] = (l_id, alloc_qty, new_rem)
                                break
            raise

    async def list_positions(self) -> List[Position]:
        """Retrieves all current positions as Position objects."""
        await self._ensure_client()
        logger.debug("Listing positions...")
        positions_out: List[Position] = []
        try:
            resp = await self.client.accounts.positions.list(account_id=self.account_id)
            for cs_pos in resp.data:
                try:
                    qty      = float(cs_pos.quantity)
                    avg_cost = float(cs_pos.average_cost) if cs_pos.average_cost else None
                    positions_out.append(Position(
                        symbol=cs_pos.symbol,
                        quantity=qty,
                        average_cost=avg_cost
                    ))
                except (ValueError, TypeError, AttributeError) as e:
                    logger.warning(f"Could not parse position {cs_pos}: {e}")
                    continue

            logger.debug(f"Found {len(positions_out)} positions.")
            return positions_out
        except APIError as e:
            logger.error(f"API Error listing positions: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error listing positions: {e}")
            return []

    async def _is_etb(self, symbol: str) -> bool:
        """
        Checks if a symbol is on the Easy-to-Borrow list for the account.
        Uses a timed cache to avoid excessive API calls.

        Returns:
            True if the symbol is ETB, False otherwise or if the check fails.
        """
        await self._ensure_client()
        symbol_upper = symbol.upper()
        current_time = asyncio.get_event_loop().time()

        async with self._etb_cache_lock:
            # Check if cache is valid
            if self._etb_cache is not None and (current_time - self._last_etb_fetch_time) < self._etb_cache_ttl:
                logger.debug(f"Using cached ETB list for {symbol_upper}.")
                return symbol_upper in self._etb_cache

            # Cache is invalid or doesn't exist, fetch fresh list
            logger.info("Fetching fresh Easy-to-Borrow (ETB) list...")
            try:
                # Ensure correct type hint if possible, otherwise remove it
                # from studio_sdk.types.accounts.easy_borrow_list_response import EasyBorrowListResponse
                etb_response = await self.client.accounts.easy_borrows.list(account_id=self.account_id)
                # Store as a set for efficient lookup
                self._etb_cache = set(etb_response.data)
                self._last_etb_fetch_time = current_time
                logger.info(f"Fetched {len(self._etb_cache)} ETB symbols.")
                return symbol_upper in self._etb_cache
            except APIError as e:
                logger.warning(
                    f"API Error fetching ETB list: {e}. Assuming '{symbol_upper}' is NOT ETB.")
                # Invalidate cache on error to force retry next time
                self._etb_cache = None
                self._last_etb_fetch_time = 0.0
                return False
            except Exception as e:
                logger.error(
                    f"Unexpected error fetching ETB list: {e}. Assuming '{symbol_upper}' is NOT ETB.")
                self._etb_cache = None
                self._last_etb_fetch_time = 0.0
                return False

    def _is_pre_market(self, order_dt: datetime) -> bool:
        """Return True if given datetime in EST is pre-market hours (4:00 to 9:30am EST)."""
        # Ensure timezone awareness and convert to EST
        if order_dt.tzinfo is None:
            dt_est = EST.localize(order_dt)
        else:
            dt_est = order_dt.astimezone(EST)

        start = dt_est.replace(hour=4, minute=0, second=0, microsecond=0)
        end = dt_est.replace(hour=9, minute=30, second=0, microsecond=0)
        return start <= dt_est < end

# --- Example Usage (Updated) ---


async def main():
    # Configure logging level via environment variable if desired
    log_level = os.environ.get("LOG_LEVEL", "INFO").upper()
    # Ensure the module logger level is set (basicConfig affects root if not already configured)
    logging.getLogger(__name__).setLevel(log_level)
    # BasicConfig might have already been called if this module was imported elsewhere,
    # but calling it here ensures some base config if run directly.
    # Use force=True if you need to override existing config (use cautiously).
    logging.basicConfig(
        level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # --- Configuration - UPDATED ENV VAR NAMES ---
    ACCOUNT_ID = os.environ.get("CLEAR_STREET_ACCOUNT_ID", "YOUR_ACCOUNT_ID")
    MPID = os.environ.get("CLEAR_STREET_MPID", "YOUR_MPID")
    ENVIRONMENT = os.environ.get("CLEAR_STREET_ENVIRONMENT", "sandbox")

    # Option 1: Provide bearer token directly (e.g., via env var like STUDIO_SDK_BEARER_TOKEN)
    # DIRECT_BEARER_TOKEN = os.environ.get("STUDIO_SDK_BEARER_TOKEN") # Let SDK handle env var lookup by passing None
    DIRECT_BEARER_TOKEN = None  # Set to None to test creds file or STUDIO_SDK_BEARER_TOKEN

    # Option 2: Set environment variable for credentials file path (CLEAR_STREET_CREDENTIALS_FILE)
    # OR provide the path directly to the constructor:
    # CREDS_FILE_PATH = "./path/to/your/creds.json"
    CREDS_FILE_PATH = None  # Set to None if using env var or direct token

    # Check if placeholder values are still present
    if "YOUR_" in ACCOUNT_ID or "YOUR_" in MPID:
        logger.error(
            "Please replace placeholder CLEAR_STREET_ACCOUNT_ID/CLEAR_STREET_MPID with actual values or set environment variables.")
        return

    # Check if at least one token source method seems configured
    # Use the updated env var name for creds file check
    creds_env_var_set = bool(os.environ.get(
        ClearStreetBroker.CREDENTIALS_FILE_ENV_VAR))
    sdk_token_env_var_set = bool(os.environ.get(
        getattr(AsyncStudioSDK, 'ENV_BEARER_TOKEN', 'STUDIO_SDK_BEARER_TOKEN')))

    if not DIRECT_BEARER_TOKEN and not CREDS_FILE_PATH and not creds_env_var_set and not sdk_token_env_var_set:
        sdk_token_env = getattr(
            AsyncStudioSDK, 'ENV_BEARER_TOKEN', 'STUDIO_SDK_BEARER_TOKEN')
        logger.error(f"No token source configured. Please provide bearer_token arg, credentials_file_path arg, "
                     f"or set {ClearStreetBroker.CREDENTIALS_FILE_ENV_VAR} or {sdk_token_env} env vars.")
        return

    # Initialize broker - REMOVED logger argument
    broker = ClearStreetBroker(
        account_id=ACCOUNT_ID,
        mpid=MPID,
        bearer_token=DIRECT_BEARER_TOKEN,
        credentials_file_path=CREDS_FILE_PATH,
        environment=ENVIRONMENT,
    )

    try:
        # --- Operations ---
        logger.info(
            "--- Checking Connection (will initialize client if needed) ---")
        is_connected = await broker.check_connection()
        logger.info(f"Connection status: {is_connected}")
        if not is_connected:
            return

        logger.info("\n--- Listing Initial Positions ---")
        positions = await broker.list_positions()
        logger.info(f"Current Positions: {positions}")

        # # --- Example: Market Buy ---
        buy_symbol = "MITQ"
        # buy_quantity = 5
        # try:
        #     logger.info(f"\n--- Attempting Market Buy: {buy_quantity} {buy_symbol} ---")
        #     # build an OrderParams and pass it in
        #     params = OrderParams(
        #         symbol=buy_symbol,
        #         quantity=buy_quantity,
        #         position_intent="open",
        #         datetime=EST.localize(datetime.now())
        #     )
        #     buy_result = await broker.market_buy(params)
        #     logger.info(f"Market Buy Result: {buy_result}")
        #     await asyncio.sleep(2)
        # except Exception as e:
        #     logger.error(f"Market Buy failed: {e}")

        # logger.info("\n--- Listing Positions After Buy ---")
        # positions = await broker.list_positions()
        # logger.info(f"Positions after buy: {positions}")

        # --- Example: Market Sell (Closing Long) ---
        # sell_close_quantity = 2
        # if any(p.symbol == buy_symbol and p.quantity > 0 for p in positions):
        #     try:
        #         logger.info(f"\n--- Attempting Market Sell (Close Long): {sell_close_quantity} {buy_symbol} ---")
        #         params = OrderParams(
        #             symbol=buy_symbol,
        #             quantity=sell_close_quantity,
        #             position_intent="close",
        #             datetime=EST.localize(datetime.now())
        #         )
        #         sell_close_result = await broker.market_sell(params)
        #         logger.info(f"Market Sell (Close) Result: {sell_close_result}")
        #         await asyncio.sleep(2)
        #     except Exception as e:
        #         logger.error(f"Market Sell (Close) failed: {e}")

        #     logger.info("\n--- Listing Positions After Sell (Close) ---")
        #     positions = await broker.list_positions()
        #     logger.info(f"Positions after sell (close): {positions}")
        # else:
        #     logger.warning(
        #         f"\n--- Skipping Market Sell (Close) as no long position found for {buy_symbol} ---")

        # --- Example: Market Sell (Short Sell - Requires Locate) ---
        short_symbol = "MITQ"
        short_quantity = 1
        try:
            logger.info(f"\n--- Attempting Market Sell (Short): {short_quantity} {short_symbol} ---")
            params = OrderParams(
                symbol=short_symbol,
                quantity=short_quantity,
                expect_price=0.85,
                position_intent="open",
                datetime=EST.localize(datetime.now())
            )
            short_sell_result = await broker.market_sell(params)
            logger.info(f"Market Sell (Short) Result: {short_sell_result}")
            await asyncio.sleep(2)
        except LocateFailedError as e:
            logger.error(f"Locate failed, short sell order not placed: {e}")
        except Exception as e:
            logger.error(f"Market Sell (Short) failed: {e}")

        logger.info("\n--- Listing Final Positions ---")
        positions = await broker.list_positions()
        logger.info(f"Final Positions: {positions}")

    except Exception as e:
        logger.error(
            f"An error occurred in the main execution: {e}", exc_info=True)
    finally:
        await broker.close_connection()
        logger.info("--- Example Script Finished ---")

if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()

    asyncio.run(main())
