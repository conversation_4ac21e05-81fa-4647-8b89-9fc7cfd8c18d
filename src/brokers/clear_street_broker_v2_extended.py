from __future__ import annotations

import secrets
from typing import Any, Dict, Sequence, Set, Optional

from studio_sdk.types.accounts import bulk_order_create_params

from brokers.ibroker_extended import (
    IBrokerExtended,
    BracketOrderParams,
    TrailingStopOrderParams,
)
from brokers.clear_street_broker_v2 import ClearStreetBrokerV2, OrderFactory

import asyncio
import logging
from dataclasses import dataclass

_logger = logging.getLogger(__name__)

@dataclass
class _OCOGroup:
    group_id: str
    order_ids: Set[str]

    def remove(self, oid: str) -> None:
        self.order_ids.discard(oid)

    def others(self, oid: str) -> Set[str]:
        return {o for o in self.order_ids if o != oid}


class ClearStreetBrokerV2Extended(ClearStreetBrokerV2, IBrokerExtended):
    """ClearStreetBrokerV2 with native complex-order (bracket & trailing-stop) support."""

    def __init__(self, account_id: str, bearer_token: Optional[str] = None, credentials_file_path: Optional[str] = None, *, environment: str = "sandbox", max_retries: int = 2, timeout: float = 30.0, order_fill_timeout: float = 45.0) -> None:
        super().__init__(account_id, bearer_token, credentials_file_path, environment=environment, max_retries=max_retries, timeout=timeout, order_fill_timeout=order_fill_timeout)
        self._oco_groups: dict[str, _OCOGroup] = {}
        self._order_to_group: dict[str, str] = {}
        self._oco_lock = asyncio.Lock()

    # ------------------------------------------------------------------
    # IBrokerExtended overrides
    async def supports_complex_orders(self) -> bool:  # type: ignore[override]
        return True

    async def bracket_order(self, params: BracketOrderParams):  # type: ignore[override]
        """Place a bracket order (entry + optional TP/SL) using Clear Street bulk API."""
        # Submit entry order and wait for fill
        entry = await self._place_and_wait(
            side=params.action.lower(),
            order_type=params.entry_type.lower(),
            params=params,
            price=params.expect_price if params.entry_type == "LIMIT" else None,
        )
        if entry.get("status") != "filled":
            raise RuntimeError("Entry order not filled – aborting TP/SL legs")

        # Build TP/SL legs (opposite side of the entry)
        opp_side = "sell" if params.action.upper() == "BUY" else "buy"
        shared_ref = f"brkt_{secrets.token_hex(4)}"
        legs: list[bulk_order_create_params.Order] = []

        if params.take_profit_price is not None:
            legs.append(
                OrderFactory.limit(
                    params.symbol,
                    params.quantity,
                    opp_side,
                    params.take_profit_price,
                    ref=f"{shared_ref}_tp",
                )
            )
        if params.stop_loss_price is not None:
            legs.append(
                OrderFactory.stop(
                    params.symbol,
                    params.quantity,
                    opp_side,
                    params.stop_loss_price,
                    ref=f"{shared_ref}_sl",
                )
            )

        # Submit TP/SL and register OCO group if both present
        if legs:
            res = await self._safe_bulk_create(legs)
            # Collect successfully submitted order_ids and track OCO
            order_ids = {item.order_id for item in res.data if item.submitted}
            if len(order_ids) >= 2:
                await self._register_oco_group(shared_ref, order_ids)

        return entry

    async def trailing_stop_order(self, params: TrailingStopOrderParams):  # type: ignore[override]
        """Place an entry order then attach a trailing-stop exit leg."""
        entry = await self._place_and_wait("buy", "market", params)
        if entry.get("status") != "filled":
            raise RuntimeError("Entry order not filled – trailing-stop leg aborted")

        await self._sdk.accounts.orders.create(
            account_id=self.account_id,
            symbol=params.symbol,
            quantity=str(params.quantity),
            side="sell",
            order_type="trailing_stop",
            trail_amount=str(params.trail_amount),
            trail_type="percent" if params.is_percent else "amount",
            time_in_force="day",
            reference_id=f"trail_{entry['order_id']}",
        )

        return entry 

    # ------------------------------------------------------------------
    # Internal – OCO utilities
    async def _register_oco_group(self, gid: str, order_ids: Set[str]):
        async with self._oco_lock:
            self._oco_groups[gid] = _OCOGroup(gid, set(order_ids))
            for oid in order_ids:
                self._order_to_group[oid] = gid
        _logger.debug("registered OCO group %s -> %s", gid, order_ids)

    async def _handle_fill_or_cancel(self, oid: str):
        async with self._oco_lock:
            gid = self._order_to_group.get(oid)
            if not gid:
                return
            group = self._oco_groups.get(gid)
            if not group:
                return
            to_cancel = group.others(oid)
            for cid in to_cancel:
                self._order_to_group.pop(cid, None)
            self._oco_groups.pop(gid, None)

        async def _cancel(order_id: str):
            try:
                await self._sdk.accounts.orders.cancel(order_id, account_id=self.account_id)
                _logger.info("OCO cancelled %s after sibling %s fill", order_id, oid)
            except Exception as exc:
                _logger.warning("failed to cancel OCO leg %s: %s", order_id, exc)

        await asyncio.gather(*(_cancel(c) for c in to_cancel))

    # ------------------------------------------------------------------
    # Internal – websocket dispatch
    async def _dispatch_ws(self, msg: Dict[str, Any]):
        payload = msg.get("payload", {})
        typ = payload.get("type")
        if typ in {"heartbeat", "subscribe-activity-ack"}:
            return
        if typ.endswith("update"):
            order = payload.get("order", {})
            oid = order.get("order_id")
            status = order.get("status")
            # resolve waiter
            if oid in self._order_waiters and not self._order_waiters[oid].future.done():
                self._order_waiters[oid].future.set_result(payload)
            # handle OCO
            TERMINAL_STATUSES = {"filled", "cancelled", "rejected", "expired"}
            if status in TERMINAL_STATUSES:
                await self._handle_fill_or_cancel(oid) 