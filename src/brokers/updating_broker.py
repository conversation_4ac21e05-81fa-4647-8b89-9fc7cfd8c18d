import logging
import dataclasses
from brokers.ibroker_extended import IBrokerExtended, BracketOrderParams
from brokers.ibkr_broker import IBKRBrokerExtended
from marketdata.imarketdata import IMarketData

class UpdatingIBroker(IBrokerExtended):
    def __init__(self,
                 raw_broker: IBKRBrokerExtended,
                 mdata: IMarketData,
                 tick: float = 0.01,
                 buffer_ticks: int = 1):
        # Ensure the raw broker extends IBrokerExtended (native complex-order support)
        assert isinstance(raw_broker, IBrokerExtended), \
            "raw_broker must extend IBrokerExtended"
        self.raw = raw_broker
        self.mdata = mdata
        self.tick  = tick
        self.buf   = buffer_ticks * tick

    async def bracket_order(self, params: BracketOrderParams, **kw):
        q = await self.mdata.get_quote_async(params.symbol)
        if params.entry_type.upper() == "LIMIT":
            # capture the original expect_price
            old_px = params.expect_price

            if params.action.upper() == "BUY":
                new_px = round(q.ask_price + self.buf, 2)
            else:
                new_px = round(q.bid_price - self.buf, 2)

            params = dataclasses.replace(params, expect_price=new_px)
            # compute and log the delta from old→new
            diff = new_px - old_px
            logging.info(
                "Spread %.4f | old expect_price %.2f | new expect_price %.2f | diff %.2f",
                q.ask_price - q.bid_price,
                old_px,
                new_px,
                diff,
            )

        return await self.raw.bracket_order(params, **kw)

    async def supports_complex_orders(self) -> bool:
        # Delegate to the underlying broker, assert it truly supports complex orders,
        # then report support ourselves.
        supported = await self.raw.supports_complex_orders()
        assert supported, "Underlying broker does not support complex orders"
        return True
