import asyncio
import logging
from dataclasses import dataclass
from typing import Dict, Optional

from brokers.ibroker import <PERSON><PERSON><PERSON><PERSON>, OrderParams, OrderParamsBuilder, Position
from brokers.ibroker_stats import IBrokerStats
from trading_framework.execution_feedback import StrategyExitRequest


logger = logging.getLogger(__name__)


@dataclass
class TickerPolicy:
    allow_open: bool = True
    allow_close: bool = True  # keep True to always allow risk-off


class ControlPlaneBroker(IBroker, IBrokerStats):
    """
    A broker decorator that enforces control-plane policies before delegating
    calls to the real broker.

    Policies supported:
      - Global: allow or block all new opens
      - Per-ticker: allow or block opens for specific symbols
      - Strategy halt: request exit and proactively close positions
    """

    def __init__(self, delegate_broker: IBroker):
        self._delegate: IBroker = delegate_broker
        self._allow_new_orders: bool = True
        self._exit_requested: bool = False
        self._ticker_policy: Dict[str, TickerPolicy] = {}
        self._lock = asyncio.Lock()

    # ------------- Control methods (invoked by ControlAgent) -------------
    async def halt_strategy(self) -> None:
        async with self._lock:
            self._exit_requested = True
            self._allow_new_orders = False
        # fire and forget close of all positions
        asyncio.create_task(self._close_all_positions())

    async def block_opens(self) -> None:
        async with self._lock:
            self._allow_new_orders = False

    async def unblock_opens(self) -> None:
        async with self._lock:
            self._allow_new_orders = True

    async def block_opens_for(self, symbol: str) -> None:
        async with self._lock:
            p = self._ticker_policy.setdefault(symbol, TickerPolicy())
            p.allow_open = False

    async def unblock_opens_for(self, symbol: str) -> None:
        async with self._lock:
            p = self._ticker_policy.setdefault(symbol, TickerPolicy())
            p.allow_open = True

    async def close_all_positions(self) -> None:
        asyncio.create_task(self._close_all_positions())

    async def close_positions_for(self, symbol: str) -> None:
        asyncio.create_task(self._close_positions_for_symbol(symbol))

    # --------------------------- IBroker API -----------------------------
    async def check_connection(self):
        return await self._delegate.check_connection()

    async def market_buy(self, params: OrderParams):
        await self._preflight(params, is_open_intent=params.position_intent == "open")
        return await self._delegate.market_buy(params)

    async def market_sell(self, params: OrderParams):
        await self._preflight(params, is_open_intent=params.position_intent == "open")
        return await self._delegate.market_sell(params)

    async def list_positions(self):
        return await self._delegate.list_positions()

    # --------------------------- IBrokerStats ----------------------------
    @property
    def market_data(self):
        return getattr(self._delegate, "market_data", None)

    @property
    def cash(self) -> float:
        return getattr(self._delegate, "cash", 0.0)

    @property
    def initial_capital(self) -> float:
        return getattr(self._delegate, "initial_capital", 0.0)

    @property
    def positions(self):
        return getattr(self._delegate, "positions", {})

    @property
    def closed_trades(self):
        return getattr(self._delegate, "closed_trades", [])

    @property
    def clock(self):
        return getattr(self._delegate, "clock", None)

    async def get_trades_dataframe(self):
        # pass-through to delegate stats
        if hasattr(self._delegate, "get_trades_dataframe"):
            return await self._delegate.get_trades_dataframe()
        return None

    # --------------------------- Internals ------------------------------
    async def _preflight(self, params: OrderParams, is_open_intent: bool) -> None:
        async with self._lock:
            if self._exit_requested:
                # immediate termination signal for strategy loop
                raise StrategyExitRequest("Strategy exit requested by control plane")

            if is_open_intent:
                if not self._allow_new_orders:
                    logger.warning("Open blocked (global): %s", params.symbol)
                    # raise to quickly unwind the running strategy coroutine
                    raise StrategyExitRequest("Global open-block active")

                pol = self._ticker_policy.get(params.symbol)
                if pol and not pol.allow_open:
                    logger.warning("Open blocked (symbol): %s", params.symbol)
                    raise StrategyExitRequest(f"Open blocked for {params.symbol}")

    async def _close_all_positions(self):
        try:
            positions = await self._delegate.list_positions()
            # Close symbol by symbol (delegate aggregates lots appropriately)
            symbols = {p.symbol for p in positions}
            await asyncio.gather(*(self._close_symbol_positions(sym) for sym in symbols))
        except Exception as e:
            logger.error("Error closing all positions: %s", e, exc_info=True)

    async def _close_positions_for_symbol(self, symbol: str):
        try:
            await self._close_symbol_positions(symbol)
        except Exception as e:
            logger.error("Error closing positions for %s: %s", symbol, e, exc_info=True)

    async def _close_symbol_positions(self, symbol: str):
        # Compute total long and short to avoid using quantity=None (disallowed by OrderParams validation)
        positions = await self._delegate.list_positions()
        long_total = sum(p.quantity for p in positions if p.symbol == symbol and p.quantity > 0)
        short_total = sum(p.quantity for p in positions if p.symbol == symbol and p.quantity < 0)

        if long_total > 0:
            try:
                stc = (
                    OrderParamsBuilder()
                    .with_symbol(symbol)
                    .with_quantity(long_total)
                    .with_expect_price(None)
                    .with_position_intent("close")
                    .with_current_datetime()
                    .build()
                )
                await self._delegate.market_sell(stc)
            except Exception:
                logger.exception("Error submitting STC for %s", symbol)

        if short_total < 0:
            try:
                btc = (
                    OrderParamsBuilder()
                    .with_symbol(symbol)
                    .with_quantity(abs(short_total))
                    .with_expect_price(None)
                    .with_position_intent("close")
                    .with_current_datetime()
                    .build()
                )
                await self._delegate.market_buy(btc)
            except Exception:
                logger.exception("Error submitting BTC for %s", symbol)

    # --------- Fallback: delegate any unknown attributes/methods ---------
    def __getattr__(self, name):
        return getattr(self._delegate, name)


