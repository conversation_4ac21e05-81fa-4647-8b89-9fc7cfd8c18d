import asyncio
import pandas as pd
from datetime import timedel<PERSON>, datetime
import uuid
from typing import List

from marketdata.imarketdata import IMarketData
from brokers.ibroker import <PERSON>Broker, OrderParams, Position
from brokers.ibroker_stats import IBrokerStats
from tools.clock import Clock  # Import the clock

import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class LocalBroker(IBroker, IBrokerStats):
    # Static instrument metadata (e.g., futures contract multipliers)
    # Extend as needed, keys can be exact symbols or roots like "/NQ"
    contract_metadata = {"/NQ": {"contract_multiplier": 20}}
    """
    A simulated broker that mimics a real broker by executing orders immediately
    and tracking positions locally using DataFrames. Uses a Clock instance to
    determine the current time (supporting both live and replay modes).
    """
    def __init__(self, market_data: IMarketData, initial_capital: float = 100000.0, clock: Clock = None, delegate_broker: IBroker = None):
        """
        Initializes the LocalBroker with a market data source, starting capital,
        and an optional Clock. If no Clock is provided, one is created.
        
        Args:
            market_data: Source for market data
            initial_capital: Starting cash amount
            clock: Optional Clock instance for time tracking
            delegate_broker: Optional broker to delegate orders to after local processing
        """
        self.market_data = market_data
        self.cash = initial_capital
        self.initial_capital = initial_capital
        # Change positions to track individual trades with entry times
        self.positions = {}  # symbol -> list of {'quantity': int, 'avg_price': float, 'entry_time': datetime, 'trade_id': str}
        self.closed_trades = []  # List to store closed trade details
        self.clock = clock or Clock()
        self.delegate_broker = delegate_broker
    
    # Add property decorators to fulfill IBrokerStats requirements - 
    # these simply return the existing attributes
    @property
    def market_data(self) -> IMarketData:
        """Returns the market data provider used by the broker."""
        return self.__dict__['market_data']
    
    @market_data.setter
    def market_data(self, value):
        self.__dict__['market_data'] = value
        
    @property
    def cash(self) -> float:
        """Returns the current cash balance."""
        return self.__dict__['cash']
    
    @cash.setter
    def cash(self, value):
        self.__dict__['cash'] = value
        
    @property
    def initial_capital(self) -> float:
        """Returns the initial capital used for the broker."""
        return self.__dict__['initial_capital']
    
    @initial_capital.setter
    def initial_capital(self, value):
        self.__dict__['initial_capital'] = value
        
    @property
    def positions(self) -> dict:
        """Returns the current positions dictionary."""
        return self.__dict__['positions']
    
    @positions.setter
    def positions(self, value):
        self.__dict__['positions'] = value
        
    @property
    def closed_trades(self) -> list:
        """Returns the list of closed trade records."""
        return self.__dict__['closed_trades']
    
    @closed_trades.setter
    def closed_trades(self, value):
        self.__dict__['closed_trades'] = value
        
    @property
    def clock(self) -> Clock:
        """Returns the clock instance used by the broker."""
        return self.__dict__['clock']
    
    @clock.setter
    def clock(self, value):
        self.__dict__['clock'] = value
        
    async def check_connection(self):
        """
        Simulates a healthy broker connection.
        """
        if self.delegate_broker is not None:
            return await self.delegate_broker.check_connection()
        return True

    # ------------------------------------------------------------------
    # Instrument helpers
    # ------------------------------------------------------------------
    def _is_futures_symbol(self, symbol: str) -> bool:
        return isinstance(symbol, str) and symbol.startswith("/")

    def _get_contract_multiplier(self, symbol: str) -> float:
        try:
            if not self._is_futures_symbol(symbol):
                return 1.0
            # First try exact key
            md = self.contract_metadata or {}
            if symbol in md and isinstance(md[symbol], dict):
                mult = md[symbol].get("contract_multiplier")
                if mult is not None:
                    return float(mult)
            # Then try a derived root like "/NQ" from "/NQH5"
            root = "/" + "".join([c for c in str(symbol)[1:] if c.isalpha()])
            if root in md and isinstance(md[root], dict):
                mult = md[root].get("contract_multiplier")
                if mult is not None:
                    return float(mult)
        except Exception:
            pass
        return 1.0

    async def market_buy(self, params: OrderParams):
        """
        Execute a market buy order
        
        Args:
            params (OrderParams): Order parameters object containing all required fields.
        """
        try:
            # Extract parameters from OrderParams
            symbol = params.symbol
            quantity = params.quantity
            expect_price = params.expect_price
            position_intent = params.position_intent
            current_time = params.datetime
            
            # Get current price from market data
            current_price = self._get_current_price(symbol, expect_price)
            
            # Handle closing an entire short position
            if position_intent == "close":
                if symbol in self.positions and any(pos['quantity'] < 0 for pos in self.positions.get(symbol, [])):
                    # Sum up all short positions
                    quantity = sum(abs(pos['quantity']) for pos in self.positions[symbol] if pos['quantity'] < 0)
                else:
                    logger.warning(f"Attempted to close entire short position for {symbol}, but no short position exists")
                    return {
                        "order_id": "",
                        "status": "rejected",
                        "reason": "No short position exists to close",
                        "symbol": symbol,
                        "quantity": 0,
                        "side": "buy",
                        "type": "market",
                        "avg_price": current_price,
                    }
            
            # Calculate cost and check if we have enough cash
            cost = quantity * current_price
            # Update cash balance
            self.cash -= cost
            
            # Update positions
            if position_intent == "open":
                # BTO - Opening a new long position
                if symbol not in self.positions:
                    self.positions[symbol] = []
                
                # Check if we have any short positions - this would be incorrect usage
                if any(pos['quantity'] < 0 for pos in self.positions.get(symbol, [])):
                    logger.warning(f"BTO requested but short position exists for {symbol}. Use BTC instead.")
                    raise ValueError(f"Cannot BTO when short position exists for {symbol}")
                
                # Create a new trade entry with a unique ID
                trade_id = str(uuid.uuid4())
                self.positions[symbol].append({
                    'quantity': quantity,
                    'avg_price': current_price,
                    'entry_time': current_time,
                    'trade_id': trade_id
                })
                
            elif position_intent == "close":
                # BTC - Closing or reducing short positions
                if symbol not in self.positions or not any(pos['quantity'] < 0 for pos in self.positions[symbol]):
                    logger.warning(f"BTC requested but no short position exists for {symbol}")
                    raise ValueError(f"Cannot BTC when no short position exists for {symbol}")
                
                # Sort short positions by entry time (FIFO)
                short_positions = sorted(
                    [pos for pos in self.positions[symbol] if pos['quantity'] < 0],
                    key=lambda x: x['entry_time']
                )
                
                remaining_quantity = quantity
                updated_positions = [pos for pos in self.positions[symbol] if pos['quantity'] >= 0]  # Keep all long positions
                
                for pos in short_positions:
                    if remaining_quantity <= 0:
                        # We've covered all the requested quantity
                        updated_positions.append(pos)
                        continue
                    
                    abs_quantity = abs(pos['quantity'])
                    if remaining_quantity >= abs_quantity:
                        # Close this entire position
                        # Calculate P&L
                        entry_price = pos['avg_price']
                        exit_price = current_price
                        pnl_per_share = entry_price - exit_price  # For shorts, profit when price goes down
                        closed_quantity = abs_quantity
                        pnl = pnl_per_share * closed_quantity
                        
                        # Log to closed_trades
                        self._log_and_add_closed_trade({
                            'symbol': symbol,
                            'entry_price': entry_price,
                            'exit_price': exit_price,
                            'quantity': closed_quantity,
                            'pnl': pnl,
                            'entry_time': pos['entry_time'],
                            'exit_time': current_time,
                            'position_type': 'short',
                            'trade_id': pos['trade_id']
                        })
                        
                        remaining_quantity -= abs_quantity
                    else:
                        # Partially close this position
                        # Calculate P&L for the closed portion
                        entry_price = pos['avg_price']
                        exit_price = current_price
                        pnl_per_share = entry_price - exit_price
                        closed_quantity = remaining_quantity
                        pnl = pnl_per_share * closed_quantity
                        
                        # Log to closed_trades
                        self._log_and_add_closed_trade({
                            'symbol': symbol,
                            'entry_price': entry_price,
                            'exit_price': exit_price,
                            'quantity': closed_quantity,
                            'pnl': pnl,
                            'entry_time': pos['entry_time'],
                            'exit_time': current_time,
                            'position_type': 'short',
                            'trade_id': pos['trade_id'] + f"_partial_{closed_quantity}"
                        })
                        
                        # Update the position with remaining quantity
                        updated_pos = pos.copy()
                        updated_pos['quantity'] += remaining_quantity  # Add positive to negative
                        updated_positions.append(updated_pos)
                        remaining_quantity = 0
                
                # Update the positions list
                self.positions[symbol] = updated_positions
                
                # If we couldn't close all requested quantity, this is an error
                if remaining_quantity > 0:
                    logger.warning(f"BTC quantity {quantity} exceeds total short position for {symbol}")
            
            local_result = {
                'symbol': symbol,
                'action': 'buy',
                'quantity': quantity,
                'price': current_price,
                'timestamp': current_time
            }
            
            # Delegate to another broker if one is provided
            if self.delegate_broker is not None:
                delegate_result = await self.delegate_broker.market_buy(params)
                return delegate_result
            
            return local_result
            
        except Exception as e:
            logger.error(f"Error executing market buy for {params.symbol}: {str(e)}")
            raise

    async def market_sell(self, params: OrderParams):
        """
        Execute a market sell order
        
        Args:
            params (OrderParams): Order parameters object containing all required fields.
        """
        try:
            # Extract parameters from OrderParams
            symbol = params.symbol
            quantity = params.quantity
            expect_price = params.expect_price
            position_intent = params.position_intent
            current_time = params.datetime
            
            # Get current price from market data
            current_price = self._get_current_price(symbol, expect_price)
            
            # Handle closing an entire long position
            if quantity is None and position_intent == "close":
                if symbol in self.positions and any(pos['quantity'] > 0 for pos in self.positions.get(symbol, [])):
                    # Sum up all long positions
                    quantity = sum(pos['quantity'] for pos in self.positions[symbol] if pos['quantity'] > 0)
                else:
                    logger.warning(f"Attempted to close entire long position for {symbol}, but no long position exists")
                    return {
                        "order_id": "",
                        "status": "rejected",
                        "reason": "No long position exists to close",
                        "symbol": symbol,
                        "quantity": 0,
                        "side": "sell",
                        "type": "market",
                        "avg_price": current_price,
                    }
            
            # Calculate proceeds
            proceeds = quantity * current_price
            
            # Update cash balance
            self.cash += proceeds
            
            # Update positions
            if position_intent == "open":
                # STO - Opening a new short position
                if symbol not in self.positions:
                    self.positions[symbol] = []
                
                # Check if we have any long positions - this would be incorrect usage
                if any(pos['quantity'] > 0 for pos in self.positions.get(symbol, [])):
                    logger.warning(f"STO requested but long position exists for {symbol}. Use STC instead.")
                    raise ValueError(f"Cannot STO when long position exists for {symbol}")
                
                # Create a new trade entry with a unique ID
                trade_id = str(uuid.uuid4())
                self.positions[symbol].append({
                    'quantity': -quantity,  # Negative for short positions
                    'avg_price': current_price,
                    'entry_time': current_time,
                    'trade_id': trade_id
                })
                
            elif position_intent == "close":
                # STC - Closing or reducing long positions
                if symbol not in self.positions or not any(pos['quantity'] > 0 for pos in self.positions[symbol]):
                    logger.warning(f"STC requested but no long position exists for {symbol}")
                    raise ValueError(f"Cannot STC when no long position exists for {symbol}")
                
                # Sort long positions by entry time (FIFO)
                long_positions = sorted(
                    [pos for pos in self.positions[symbol] if pos['quantity'] > 0],
                    key=lambda x: x['entry_time']
                )
                
                remaining_quantity = quantity
                updated_positions = [pos for pos in self.positions[symbol] if pos['quantity'] <= 0]  # Keep all short positions
                
                for pos in long_positions:
                    if remaining_quantity <= 0:
                        # We've sold all the requested quantity
                        updated_positions.append(pos)
                        continue
                    
                    if remaining_quantity >= pos['quantity']:
                        # Close this entire position
                        # Calculate P&L
                        entry_price = pos['avg_price']
                        exit_price = current_price
                        pnl_per_share = exit_price - entry_price  # For longs, profit when price goes up
                        closed_quantity = pos['quantity']
                        pnl = pnl_per_share * closed_quantity
                        
                        # Log to closed_trades
                        self._log_and_add_closed_trade({
                            'symbol': symbol,
                            'entry_price': entry_price,
                            'exit_price': exit_price,
                            'quantity': closed_quantity,
                            'pnl': pnl,
                            'entry_time': pos['entry_time'],
                            'exit_time': current_time,
                            'position_type': 'long',
                            'trade_id': pos['trade_id']
                        })
                        
                        remaining_quantity -= closed_quantity
                    else:
                        # Partially close this position
                        # Calculate P&L for the closed portion
                        entry_price = pos['avg_price']
                        exit_price = current_price
                        pnl_per_share = exit_price - entry_price
                        closed_quantity = remaining_quantity
                        pnl = pnl_per_share * closed_quantity
                        
                        # Log to closed_trades
                        self._log_and_add_closed_trade({
                            'symbol': symbol,
                            'entry_price': entry_price,
                            'exit_price': exit_price,
                            'quantity': closed_quantity,
                            'pnl': pnl,
                            'entry_time': pos['entry_time'],
                            'exit_time': current_time,
                            'position_type': 'long',
                            'trade_id': pos['trade_id'] + f"_partial_{closed_quantity}"
                        })
                        
                        # Update the position with remaining quantity
                        updated_pos = pos.copy()
                        updated_pos['quantity'] -= remaining_quantity
                        updated_positions.append(updated_pos)
                        remaining_quantity = 0
                
                # Update the positions list
                self.positions[symbol] = updated_positions
                
                # If we couldn't close all requested quantity, this is an error
                if remaining_quantity > 0:
                    logger.warning(f"STC quantity {quantity} exceeds total long position for {symbol}")
            
            local_result = {
                'symbol': symbol,
                'action': 'sell',
                'quantity': quantity,
                'price': current_price,
                'timestamp': current_time
            }
            
            # Delegate to another broker if one is provided
            if self.delegate_broker is not None:
                delegate_result = await self.delegate_broker.market_sell(params)
                return delegate_result
            
            return local_result
            
        except Exception as e:
            logger.error(f"Error executing market sell for {params.symbol}: {str(e)}")
            raise

    async def list_positions(self) -> List[Position]:
        """
        Return a single consolidated Position per symbol, including current price,
        market value, realized/unrealized PnL, and total traded notional.

        Also include realized-only rows for symbols with no open quantity.
        """
        positions_out: List[Position] = []

        # Aggregate realized and total traded notional from closed trades
        realized_by_symbol: dict[str, float] = {}
        traded_value_by_symbol: dict[str, float] = {}
        for trade in self.closed_trades:
            try:
                sym = trade.get('symbol')
                if not sym:
                    continue
                pnl_value = float(trade.get('pnl', 0.0))
                qty = float(trade.get('quantity', 0.0))
                exit_px = trade.get('exit_price')
                traded_val_inc = float(exit_px) * abs(qty) if exit_px is not None else 0.0
                # Adjust notional for futures via contract multiplier
                traded_val_inc *= self._get_contract_multiplier(sym)
                realized_by_symbol[sym] = realized_by_symbol.get(sym, 0.0) + pnl_value
                traded_value_by_symbol[sym] = traded_value_by_symbol.get(sym, 0.0) + traded_val_inc
            except Exception:
                continue

        # Symbols to report: union of open positions and any with realized history
        all_symbols: set[str] = set(realized_by_symbol.keys()) | set(self.positions.keys())

        for symbol in sorted(all_symbols):
            pos_list = self.positions.get(symbol, [])

            # Fetch current price (midpoint when possible)
            current_price: float | None = None
            try:
                quote = await self.market_data.get_quote_async(symbol)
                bid = getattr(quote, 'bid_price', None)
                ask = getattr(quote, 'ask_price', None)
                if bid is not None and ask is not None:
                    current_price = (bid + ask) / 2.0
                elif ask is not None:
                    current_price = ask
                elif bid is not None:
                    current_price = bid
            except Exception:
                current_price = None

            symbol_realized = realized_by_symbol.get(symbol, 0.0)
            symbol_traded_value = traded_value_by_symbol.get(symbol, 0.0) if symbol in traded_value_by_symbol else None

            # Consolidate: single row per symbol based on open lots only
            qty_sum: float = 0.0
            unrealized_sum: float = 0.0
            any_open = False
            for pos in pos_list:
                quantity_val = pos.get('quantity', 0)
                try:
                    quantity = float(quantity_val)
                except Exception:
                    continue
                if quantity == 0:
                    continue
                any_open = True
                qty_sum += quantity
                if current_price is not None:
                    avg_price_raw = pos.get('avg_price')
                    try:
                        avg_price = float(avg_price_raw) if avg_price_raw is not None else None
                    except Exception:
                        avg_price = None
                    if avg_price is not None:
                        multiplier = self._get_contract_multiplier(symbol)
                        if quantity > 0:
                            unrealized_sum += (current_price - avg_price) * quantity * multiplier
                        else:
                            unrealized_sum += (avg_price - current_price) * abs(quantity) * multiplier

            # If net quantity is zero, treat as flat (show realized-only row if any)
            has_open_position = any_open and qty_sum != 0

            market_value = None
            unrealized = None
            if has_open_position and current_price is not None:
                # For futures, scale by contract multiplier
                market_value = qty_sum * current_price * self._get_contract_multiplier(symbol)
                unrealized = unrealized_sum

            if has_open_position:
                positions_out.append(Position(
                    symbol=symbol,
                    quantity=qty_sum,
                    average_cost=None,  # not needed for UI; avoid cross-lot averaging
                    entry_time=None,
                    trade_id=None,
                    market_value=market_value,
                    current_price=current_price,
                    realized=symbol_realized,
                    unrealized=unrealized,
                    total_traded_value=symbol_traded_value,
                ))
            elif symbol in realized_by_symbol:
                positions_out.append(Position(
                    symbol=symbol,
                    quantity=0,
                    average_cost=None,
                    entry_time=None,
                    trade_id=None,
                    market_value=0.0 if current_price is not None else None,
                    current_price=current_price,
                    realized=symbol_realized,
                    unrealized=None,
                    total_traded_value=symbol_traded_value,
                ))

        return positions_out

    def _get_current_price(self, symbol, expect_price=None) -> float:
        """
        Get the current price for a symbol.
        
        Args:
            symbol: The ticker symbol
            expect_price: Optional expected price to use instead of market price
            
        Returns:
            The current price for the symbol
        """
        return expect_price

    async def get_trades_dataframe(self):
        """
        Generate a DataFrame of closed trades for use with TradeStats.
        
        Returns:
            pd.DataFrame: DataFrame containing trade information with columns:
                - symbol: The ticker symbol
                - entry_price: Entry price of the trade
                - exit_price: Exit price of the trade
                - quantity: Number of shares/contracts
                - pnl: Profit/loss of the trade
                - entry_time: Entry timestamp
                - close_date: Exit timestamp (used by TradeStats)
                - position_type: 'long' or 'short'
                - trade_id: Unique identifier for the trade
        """
        if not self.closed_trades:
            logger.warning("No closed trades found")
            return pd.DataFrame()
            
        # Convert the list of closed trades to a DataFrame
        trades_df = pd.DataFrame(self.closed_trades)
        
        # Rename exit_time to close_date to match TradeStats expected format
        if 'exit_time' in trades_df.columns:
            trades_df = trades_df.rename(columns={'exit_time': 'close_date'})
            
        # Sort by close date
        trades_df = trades_df.sort_values(by='close_date')
        
        return trades_df

    def _log_and_add_closed_trade(self, trade_data):
        """
        Helper method to log trade details and add to closed_trades list
        
        Args:
            trade_data: Dictionary containing trade details
        """
        # Apply contract multiplier to PnL for futures
        try:
            symbol = trade_data.get('symbol')
            pnl_value = float(trade_data.get('pnl', 0.0))
            multiplier = self._get_contract_multiplier(symbol)
            trade_data['pnl'] = pnl_value * multiplier
            # Persist the multiplier used for transparency/debugging
            if multiplier != 1.0:
                trade_data['contract_multiplier'] = multiplier
        except Exception:
            pass
        logger.info(f"Closed trade: {trade_data}")
        self.closed_trades.append(trade_data)
        
  
    async def pre_allocate_short_locate(self, params: OrderParams):
        if self.delegate_broker is not None:
            return await self.delegate_broker.pre_allocate_short_locate(params)
        else:
            super().pre_allocate_short_locate(params)