from __future__ import annotations

from dataclasses import dataclass, field

# Added for locate integration
from brokers.das_locate_manager import DasLocateManager
import asyncio
import itertools
import logging
import socket
from typing import Any, Literal, Dict, Optional, List
import contextlib
import random

from brokers.ibroker import OrderParams, Position, Order  # type: ignore
from brokers.iraworderbroker import IRawOrderBroker  # type: ignore
from brokers.das_parser import DasCmdParser  # type: ignore

__all__ = ["DasBroker"]
logger = logging.getLogger(__name__)

logging.basicConfig(level=logging.DEBUG)

@dataclass
class _State:
    """Runtime mutable state guarded by the event‑loop."""

    # Local counter used to generate unique *token* values
    # Start token counter at a random 4-digit number so that
    # reconnects in the same trading session do not collide with previous tokens.
    token_counter: itertools.count = field(default_factory=lambda: itertools.count(random.randint(1000, 9999)))
    # Map token (int) → Future waiting for real order_token (str)
    pending_tokens: Dict[int, asyncio.Future[str]] = field(default_factory=dict)
    # Reverse map order_id → order_token (useful for debugging)
    id_to_token: Dict[str, int] = field(default_factory=dict)
    # Map symbol → latest position snapshot dict
    positions: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    # Map order_token → latest Order snapshot
    orders: Dict[str, Order] = field(default_factory=dict)
    # Chronological list of trade execution dicts
    trades: List[Dict[str, Any]] = field(default_factory=list)


class DasBroker(IRawOrderBroker):
    """Low‑level DAS CMD API order broker."""

    def __init__(
        self,
        *,
        host: str,
        port: int,
        user: str,
        password: str,
        account: str | None = None,
        default_route: str = "FREE25",
        loop: Optional[asyncio.AbstractEventLoop] = None,
    ) -> None:
        self._host = host
        self._default_route = default_route
        self._port = port
        self._user = user
        self._password = password
        self._account = account or ""
        self._loop = loop or asyncio.get_event_loop()

        # Network primitives (set in connect())
        self._reader: asyncio.StreamReader | None = None
        self._writer: asyncio.StreamWriter | None = None

        # Background listener task
        self._listen_task: asyncio.Task[None] | None = None

        # Future set when we receive "#LOGIN SUCCESSED" from server
        self._login_future: asyncio.Future[None] = self._loop.create_future()

        # Public queue for *all* raw inbound lines (optional consumption)
        self.inbound_queue: asyncio.Queue[str] = asyncio.Queue()

        # Internal mutable state
        self._state = _State()
        # Line parser for inbound DAS messages
        self._parser = DasCmdParser(self._state, self._login_future)

        # Locate manager to handle short locates
        self._locate_manager = DasLocateManager(
            broker=self
        )

        # Protects writer writes so multiple coroutines can submit commands
        self._send_lock = asyncio.Lock()

    # ---------------------------------------------------------------------
    # Life‑cycle helpers
    # ---------------------------------------------------------------------
    async def connect(self) -> None:
        """Open TCP connection and authenticate with *LOGIN*."""
        logger.info("Connecting to DAS CMD API %s:%s", self._host, self._port)
        self._reader, self._writer = await asyncio.open_connection(self._host, self._port)

        # Start background reader FIRST so we capture the LOGIN response
        self._listen_task = asyncio.create_task(self._reader_loop(), name="das-broker-listener")

        # Send LOGIN – account is optional in protocol
        login_cmd = f"LOGIN {self._user} {self._password} {self._account}"
        await self._send_line(login_cmd)

        # Wait until we see "#LOGIN SUCCESSED" or time-out
        try:
            await asyncio.wait_for(self._login_future, timeout=5)
        except asyncio.TimeoutError as exc:
            raise TimeoutError("Timed-out waiting for LOGIN acknowledgement from DAS") from exc

    async def close(self, *, wait: bool = True) -> None:
        """Gracefully shut down network connection and background task."""
        if self._writer and not self._writer.is_closing():
            try:
                await self._send_line("QUIT")
            except Exception:  # noqa: BLE001 – best‑effort
                pass
            self._writer.close()
        if self._listen_task:
            self._listen_task.cancel()
            if wait:
                with contextlib.suppress(asyncio.CancelledError):
                    await self._listen_task

    # ------------------------------------------------------------------
    # IRawOrderBroker implementation
    # ------------------------------------------------------------------
    async def submit_order(
        self,
        *,
        side: Literal["buy", "sell", "sell-short"],
        params: OrderParams,
        order_type: Literal["market", "limit", "stop"] = "market",
        time_in_force: str = "day",
    ) -> str:
        """Translate high‑level params into *NEWORDER* and wait for order_token."""
        if not self.check_connection():
            raise ConnectionError("Not connected to DAS CMD API")

        # Map side → DAS token
        side_map = {
            "buy": "B",
            "sell": "S",
            "sell-short": "SS",
        }
        das_side = side_map[side]

        # Generate unique token (int)
        token = next(self._state.token_counter)

        route = self._default_route
        qty_val = params.quantity  # type: ignore[attr-defined]
        # DAS expects whole-share amounts without decimal; fall back to float string otherwise
        size = str(int(qty_val) if float(qty_val).is_integer() else qty_val)
        symbol = params.symbol  # type: ignore[attr-defined]

        if order_type == "market":
            price_field = "MKT"
        elif order_type == "limit":
            price = params.expect_price  # type: ignore[attr-defined]
            if price is None:
                raise ValueError("Limit order requires params.price")
            price_field = f"{price:.4f}" if isinstance(price, float) else str(price)
        else:  # "stop" etc – not fully implemented
            raise NotImplementedError("stop orders not supported")

        tif = time_in_force.upper()  # pass through e.g. DAY+

        cmd = f"NEWORDER {token} {das_side} {symbol} {route} {size} {price_field} TIF={tif}"
        # Prepare Future FIRST to avoid race if server responds immediately
        fut: asyncio.Future[str] = self._loop.create_future()
        self._state.pending_tokens[token] = fut

        await self._send_line(cmd)
        logger.debug("Sent: %s", cmd)
        try:
            order_token = await asyncio.wait_for(fut, timeout=params.timeout)
        except asyncio.TimeoutError as exc:
            self._state.pending_tokens.pop(token, None)
            raise TimeoutError("Timed‑out awaiting order acknowledgement from DAS") from exc
        return token

    async def check_order_status(self, order_token: str) -> Order | None:
        """Return the latest cached status for *order_token* (non‑blocking)."""
        logger.debug("Checking order status for token: %s", order_token)
        return self._state.orders.get(order_token)

    async def cancel_order(self, order_token: str):  # type: ignore[override]
        """Send *CANCEL order_token* to DAS and wait until the broker acknowledges it.

        After submitting the cancellation request we poll the in-memory order
        snapshot that is continuously updated by the background reader/parsing
        task.  The coroutine returns once the order is reported as *Cancelled*
        (either via the `%ORDER` *status* field or the `%OrderAct` *action*) or
        raises *TimeoutError* if the confirmation does not arrive in time.
        """
        if not self.check_connection():
            raise ConnectionError("Not connected to DAS CMD API")
    
        order = self.check_order_status(order_token)
        
        # Dispatch the CANCEL command first so the server starts processing
        await self._send_line(f"CANCEL {order.order_id}")
        logger.debug("Sent: CANCEL %s for order %s", order.order_id, order_token)

        # ------------------------------------------------------------------
        # Poll in-memory order map until we observe a cancellation
        # ------------------------------------------------------------------
        timeout = 10.0        # seconds
        poll_interval = 0.25  # seconds
        start_ts = self._loop.time()

        while True:
            order = await self.check_order_status(order_token)
            if order is not None:
                status_flag = order.status.lower()
                last_act    = order.last_action.lower()

                if status_flag.startswith("cancel") or last_act.startswith("cancel"):
                    logger.info("Order %s cancelled", order_token)
                    return order
                # If order filled meanwhile, cancellation is moot – stop waiting
                if status_flag in {"executed", "filled"}:
                    logger.warning("Order %s executed before cancellation could be confirmed", order_token)
                    return order

            if (self._loop.time() - start_ts) >= timeout:
                raise TimeoutError(f"Timed-out waiting for cancellation confirmation for order {order_token}")

            await asyncio.sleep(poll_interval)

    async def patch_order(self, *args, **kwargs):  # noqa: D401  # type: ignore[override]
        raise NotImplementedError("DAS CMD API uses REPLACE; not implemented here yet")

    # ------------------------------------------------------------------
    # IBroker requirement – positions
    # ------------------------------------------------------------------
    async def list_positions(self) -> list[Position]:  # type: ignore[override]
        """Return current open positions.

        The DAS CMD API order socket does not expose holdings directly. A proper
        implementation would subscribe to the separate quotes/positions feed.
        To keep this lightweight example functional we simply return an empty
        list so that callers such as the CLI can continue gracefully.
        """
        return []

    # ------------------------------------------------------------------
    # Utility helpers
    # ------------------------------------------------------------------
    def check_connection(self) -> bool:  # noqa: D401
        return self._writer is not None and not self._writer.is_closing()
    
    def is_order_filled(self, status: Order) -> bool:
        """Return True when order considered fully filled.

        Handles two possible shapes:
        1. Dict — the parsed order status snapshot from DasCmdParser.
        2. Plain string – raw status flag (e.g. "Executed").
        """
        logger.debug("is_order_filled check: %s", status)
        # TODO: Implement this properly
        # try:
        #     if float(status.filled_qty or 0) >= float(status.qty or 0):
        #         return True
        # except (TypeError, ValueError):
        #     pass
        return status.status.lower() == "executed"


    # ------------------------------------------------------------------
    # Locate-aware high-level order helpers overriding IRawOrderBroker
    # ------------------------------------------------------------------

    async def market_sell(self, params: OrderParams):  # type: ignore[override]
        """Submit a market-sell order with automatic locate handling.

        When *params.position_intent == "open"* (i.e. initiating a short
        position) we first secure an locate via the integrated
        `DasLocateManager`.  The locate reservation is wrapped in an
        ``async with`` context so that any exception raised during order
        execution automatically releases the reservation.
        """
        params.validate()

        # Only acquire locates when opening a new short position
        if params.position_intent == "open":
            async with self._locate_manager.with_locates(
                symbol=params.symbol,
                qty=params.quantity,
                expect_price=params.expect_price,
            ):
                return await super().market_sell(params)  # type: ignore[misc]
        # Closing or reducing position – delegate to base implementation
        return await super().market_sell(params)  # type: ignore[misc]

    async def market_buy(self, params: OrderParams):  # type: ignore[override]
        """Submit a market-buy order and release locates when closing shorts."""
        params.validate()

        result = await super().market_buy(params)  # type: ignore[misc]

        # When buying to close an existing short we can release any reserved locates
        if params.position_intent == "close":
            try:
                await self._locate_manager.release_locate(params.symbol, params.quantity)
            except Exception:
                # Non-fatal – log and continue
                logger.exception(
                    "Failed to release locates for %s after buy-close", params.symbol
                )
        return result

    async def _send_line(self, line: str) -> None:
        """Write single command line terminated by CRLF."""
        if self._writer is None:
            raise ConnectionError("Socket writer not ready – did you call connect()?")
        # Ensure **at most one** writer at a time
        async with self._send_lock:
            self._writer.write((line + "\r\n").encode())
            await self._writer.drain()

    # ------------------------------------------------------------------
    # Reader / parser
    # ------------------------------------------------------------------
    async def _reader_loop(self) -> None:  # noqa: C901 – fine for internal task
        assert self._reader is not None
        while True:
            raw = await self._reader.readline()
            if not raw:
                logger.warning("DAS socket closed by peer")
                break
            try:
                line = raw.decode().rstrip("\r\n")
            except UnicodeDecodeError:
                logger.exception("Failed to decode inbound line: %r", raw)
                continue

            # Fan‑out raw line to public queue (non‑blocking)
            if not self.inbound_queue.full():
                self.inbound_queue.put_nowait(line)

            # Parse for broker‑related messages
            await self._parser.handle_line(line)
