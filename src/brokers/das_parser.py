"""DAS CMD API line parser.

This module isolates the string-parsing logic for inbound messages coming from
DAS Trader’s CMD API.  Separating it from the networking code in
`das_broker.py` makes it easier to extend with additional message types in the
future without touching the socket handling layer.
"""

from __future__ import annotations

import asyncio
import logging
from typing import Any, Dict

from brokers.das_messages import MESSAGE_TYPES

logger = logging.getLogger(__name__)


class DasCmdParser:
    """Parse inbound DAS CMD API lines and mutate broker state accordingly."""

    def __init__(self, state: Any, login_future: asyncio.Future[None]):
        # The *state* object is expected to expose the same attributes as the
        # internal `_State` dataclass defined in `das_broker.py` – namely
        # `order_status`, `id_to_token` and `pending_tokens`.
        self._state = state
        self._login_future = login_future

    # ------------------------------------------------------------------
    # Public entry-point
    # ------------------------------------------------------------------
    async def handle_line(self, line: str) -> None:  # noqa: C901  – keeps logic local
        """Dispatch *line* to a specialised parser based on its prefix."""
        logger.info("Received line: %s", line)

        # Handle LOGIN / heartbeat lines first
        if line.startswith("#LOGIN") and "SUCCESSED" in line.upper():
            if self._login_future and not self._login_future.done():
                self._login_future.set_result(None)
            logger.info("Heartbeat/event: %s", line)
            return
        if line.startswith("#"):
            logger.info("Heartbeat/event: %s", line)
            return

        # Dispatch to typed message classes registered in brokers.das_messages
        for msg_cls in MESSAGE_TYPES:
            if msg_cls.matches(line):
                try:
                    msg = msg_cls.parse(line)
                except ValueError:
                    logger.warning("Malformed %s line: %s", msg_cls.__name__, line)
                    return
                await msg.apply(self._state)
                return

        logger.debug("Unhandled line: %s", line)
