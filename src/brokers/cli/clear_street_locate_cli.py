#!/usr/bin/env python3
"""
A CLI tool to test Clear Street Studio SDK locate order functionality.
This script allows creating a locate order and accepting/declining it using the
Studio SDK Python client with OAuth authentication.
"""

import os
import json
import argparse
import sys
import time
import requests
from typing import Optional, Dict, Any
from studio_sdk import StudioSDK
import dotenv

dotenv.load_dotenv()

def get_access_token(credentials_file: str) -> str:
    """
    Get an access token using OAuth client credentials flow.
    
    Args:
        credentials_file: Path to a JSON file containing client_id and client_secret
        
    Returns:
        str: The access token
    """
    try:
        with open(credentials_file, 'r') as f:
            creds = json.load(f)
            
        if not all(k in creds for k in ['client_id', 'client_secret']):
            raise ValueError("Credentials file must contain 'client_id' and 'client_secret'")
            
        payload = {
            "grant_type": "client_credentials",
            "client_id": creds["client_id"],
            "client_secret": creds["client_secret"],
            "audience": "https://api.clearstreet.io"
        }
        
        url = "https://auth.clearstreet.io/oauth/token"
        headers = {
            "accept": "application/json",
            "content-type": "application/json"
        }
        
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        
        token_data = response.json()
        return token_data["access_token"]
        
    except FileNotFoundError:
        print(f"Error: Credentials file '{credentials_file}' not found")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in credentials file '{credentials_file}'")
        sys.exit(1)
    except ValueError as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
    except requests.RequestException as e:
        print(f"Error obtaining access token: {str(e)}")
        sys.exit(1)

def setup_client(bearer_token: str, environment: str) -> StudioSDK:
    """Set up and return the Studio SDK client."""
    return StudioSDK(
        bearer_token=bearer_token,
        environment=environment,
    )

def create_locate_order(
    client: StudioSDK,
    account_id: str,
    symbol: str,
    quantity: str,
    mpid: str,
    reference_id: str,
    comments: Optional[str] = None,
) -> Dict[str, Any]:
    """Create a locate order using the SDK's dedicated method."""
    print(f"Creating locate order for {quantity} shares of {symbol} via {mpid}")
    
    # Prepare the parameters
    params = {
        "symbol": symbol,
        "quantity": quantity,
        "mpid": mpid,
        "reference_id": reference_id,
    }
    
    if comments:
        params["comments"] = comments
    
    # Using the SDK's built-in method for locate orders
    locate_order = client.accounts.locate_orders.create(
        account_id=account_id,
        **params
    )
    
    return locate_order.to_dict()

def accept_locate_order(
    client: StudioSDK,
    account_id: str,
    locate_order_id: str,
    accept: bool,
) -> Dict[str, Any]:
    """Accept or decline a locate order using the SDK's dedicated method."""
    action = "Accepting" if accept else "Declining"
    print(f"{action} locate order {locate_order_id}")
    
    # Using the SDK's built-in method to update locate orders
    result = client.accounts.locate_orders.update(
        account_id=account_id,
        locate_order_id=locate_order_id,
        accept=accept
    )
    
    # Get the updated locate order to show the result
    updated_order = client.accounts.locate_orders.retrieve(
        account_id=account_id,
        locate_order_id=locate_order_id
    )
    
    return updated_order.to_dict()

def list_locate_orders(
    client: StudioSDK,
    account_id: str,
) -> Dict[str, Any]:
    """List all locate orders for an account."""
    print(f"Listing locate orders for account {account_id}")
    
    # Using the SDK's built-in method to list locate orders
    locate_orders = client.accounts.locate_orders.list(
        account_id=account_id
    )
    
    return locate_orders.to_dict()

def main():
    parser = argparse.ArgumentParser(description="CLI tool for Clear Street locate orders")
    parser.add_argument("--creds", help="Path to credentials JSON file (contains client_id and client_secret)")
    parser.add_argument("--token", help="Bearer token (optional, if not using credentials file)")
    parser.add_argument("--env", default="sandbox", choices=["sandbox", "production"],
                        help="Environment to use (default: sandbox)")
    
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Create locate order command
    create_parser = subparsers.add_parser("create", help="Create a locate order")
    create_parser.add_argument("--account", required=True, help="Account ID or number")
    create_parser.add_argument("--symbol", required=True, help="Symbol to locate")
    create_parser.add_argument("--quantity", required=True, help="Quantity to locate")
    create_parser.add_argument("--mpid", required=True, help="Market participant ID")
    create_parser.add_argument("--ref", required=True, help="Your reference ID")
    create_parser.add_argument("--comments", help="Optional comments")
    
    # Accept locate order command
    accept_parser = subparsers.add_parser("accept", help="Accept or decline a locate order")
    accept_parser.add_argument("--account", required=True, help="Account ID or number")
    accept_parser.add_argument("--locate-id", required=True, help="Locate order ID")
    accept_parser.add_argument("--accept", action="store_true", help="Accept the order (default: false = decline)")
    
    # List locate orders command
    list_parser = subparsers.add_parser("list", help="List all locate orders for an account")
    list_parser.add_argument("--account", required=True, help="Account ID or number")
    
    args = parser.parse_args()
    
    # Check if a command was provided
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Get bearer token from args, environment, or OAuth flow
    bearer_token = None
    if args.token:
        bearer_token = args.token
    elif os.environ.get("STUDIO_SDK_BEARER_TOKEN"):
        bearer_token = os.environ.get("STUDIO_SDK_BEARER_TOKEN")
    elif args.creds:
        bearer_token = get_access_token(args.creds)
    else:
        print("Error: Either provide --token, set STUDIO_SDK_BEARER_TOKEN environment variable, or use --creds")
        sys.exit(1)
    
    # Initialize client
    client = setup_client(bearer_token, args.env)
    
    try:
        if args.command == "create":
            result = create_locate_order(
                client=client,
                account_id=args.account,
                symbol=args.symbol,
                quantity=args.quantity,
                mpid=args.mpid,
                reference_id=args.ref,
                comments=args.comments,
            )
            print("\nLocate order created successfully:")
            print(f"Locate Order ID: {result.get('locate_order_id', 'N/A')}")
            print(f"Status: {result.get('status', 'N/A')}")
            print(f"Symbol: {result.get('symbol', 'N/A')}")
            print(f"Quantity: {result.get('quantity', 'N/A')}")
            if result.get('rate'):
                print(f"Rate: {result.get('rate', 'N/A')}")
            
        elif args.command == "accept":
            result = accept_locate_order(
                client=client,
                account_id=args.account,
                locate_order_id=args.locate_id,
                accept=args.accept,
            )
            action = "accepted" if args.accept else "declined"
            print(f"\nLocate order {action} successfully:")
            print(f"Locate Order ID: {result.get('locate_order_id', 'N/A')}")
            print(f"Status: {result.get('status', 'N/A')}")
            
        elif args.command == "list":
            result = list_locate_orders(
                client=client,
                account_id=args.account,
            )
            print("\nLocate orders:")
            if result.get('data'):
                for locate in result.get('data', []):
                    print(f"- ID: {locate.get('locate_order_id')}, Symbol: {locate.get('symbol')}, "
                          f"Status: {locate.get('status')}, Quantity: {locate.get('quantity')}")
            else:
                print("No locate orders found.")
    
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()