"""
Minimal DAS‑CMD‑API test that:
1. Loads connection creds from .env or the process environment
2. Logs in
3. Dumps today’s positions and trades
4. Sends QUIT
"""

import os
import socket
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()


HOST      = os.getenv("DAS_HOST", "127.0.0.1")   # sensible fallback values
PORT      = int(os.getenv("DAS_PORT", "7777"))
USER      = os.getenv("DAS_USER")                # no fallback → required
PASSWORD  = os.getenv("DAS_PASSWORD")            # "
ACCOUNT   = os.getenv("DAS_ACCOUNT")             # "

required = {
    "DAS_USER": USER,
    "DAS_PASSWORD": PASSWORD,
    "DAS_ACCOUNT": ACCOUNT,
}
missing = [name for name, value in required.items() if not value]
if missing:
    raise RuntimeError(f"Missing required env vars: {', '.join(missing)}")


def talk(sock: socket.socket,
         cmd: str,
         expect_end_tokens=None,
         timeout: float = 0.5) -> str:
    """
    Send one CMD‑API command and read until either:
      • the socket times out, or
      • we see one of the tokens in expect_end_tokens (e.g. b"#POSEND")
    """
    sock.sendall((cmd + "\r\n").encode("ascii"))

    sock.settimeout(timeout)      # just do the side‑effect
    buf: bytes = b""              # <‑‑ real buffer lives here

    while True:
        try:
            chunk = sock.recv(4096)
            if not chunk:
                break             # connection closed
            buf += chunk
            if expect_end_tokens and any(t in buf for t in expect_end_tokens):
                break
        except socket.timeout:
            break                 # no more data within timeout window
    return buf.decode("ascii", errors="ignore")


with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
    s.connect((HOST, PORT))

    print(talk(s, f"LOGIN {USER} {PASSWORD} {ACCOUNT}"))

    # positions
    pos_raw = talk(s, "GET POSITIONS", expect_end_tokens=[b"#POSEND"])
    print("--- POSITIONS ---\n", pos_raw)

    # trades
    trd_raw = talk(s, "GET TRADES", expect_end_tokens=[b"#TradeEnd"])
    print("--- TRADES ---\n", trd_raw)

    talk(s, "QUIT")
