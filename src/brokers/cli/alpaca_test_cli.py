import os
import nest_asyncio
from datetime import datetime
import pytz
nest_asyncio.apply()

import asyncio
from dotenv import load_dotenv
from brokers.alpaca_broker import AlpacaBroker
from brokers.ibroker import OrderParamsBuilder

# Load environment variables from the .env file.
load_dotenv()

async def main():
    broker = AlpacaBroker(paper=True, extended_hours=False)
    
    if not await broker.check_connection():
        print("Failed to connect to Alpaca.")
        return
    
    # Create OrderParams using builder pattern
    est = pytz.timezone('US/Eastern')
    current_time = datetime.now(est)
    
    params = (OrderParamsBuilder()
        .with_symbol("AAPL")
        .with_quantity(50)
        .with_position_intent("open")
        .with_datetime(current_time)
        .build())
    
    # Place a market buy order.
    print("Placing Market Buy Order for AAPL...")
    await broker.market_buy(params)
    
    # Retrieve and print current positions.
    positions = await broker.list_positions()
    print("Positions returned:", positions)

if __name__ == "__main__":
    asyncio.run(main()) 