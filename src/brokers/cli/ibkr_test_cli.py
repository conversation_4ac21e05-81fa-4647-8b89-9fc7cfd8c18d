# ibkr_test_cli.py

import nest_asyncio
nest_asyncio.apply()

import asyncio
from datetime import datetime
import pytz

from ib_async import IB

from brokers.ibkr_broker import IBKRBroker
from brokers.ibroker import OrderParamsBuilder

async def main():
    ib = IB()
    # 1) connect and start the internal reader loop
    await ib.connectAsync('127.0.0.1', 8496, clientId=1245)
    print("Connected to IB")

    broker = IBKRBroker(ib, outside_rth=True)
    await broker.check_connection()

    est = pytz.timezone('US/Eastern')
    now = datetime.now(est)

    positions = await broker.list_positions()
    print("Positions returned:", positions)

    buy_params = (
        OrderParamsBuilder()
        .with_symbol("/MNQ")
        .with_quantity(1)
        .with_position_intent("open")
        .with_datetime(now)
        .build()
    )
    
    sell_params = (
        OrderParamsBuilder()
        .with_symbol("/NQ")
        .with_quantity(1)
        .with_position_intent("close")
        .with_datetime(now)
        .build()
    )

    print("Placing Market Buy Order for AAPL…")
    # order = await broker.market_buy(buy_params)
    # print("Order placed:", order.orderStatus)

    # print("Placing Market Sell Order for AAPL…")
    # order = await broker.market_sell(sell_params)
    # print("Order placed:", order.orderStatus)

    positions = await broker.list_positions()
    print("Positions returned:", positions)

    ib.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
