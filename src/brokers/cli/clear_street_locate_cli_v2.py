#!/usr/bin/env python3
"""

python -m brokers.cli.clear_street_locate_cli_v2 \
    --creds /home/<USER>/w/backtest/clearstreet-api-Dk1J87.json --env production \
    pre-short \
    --account 112982 \
    --mpid ATLS \
    --symbol OPEN \
    --expect-price 3.7  2>&1 | tee output/logs/locate_log 
    
clear_street_locate_cli_v2.py
=================================
A minimal CLI utility built on top of ClearStreetBrokerV2 / ClearStreetLocateManager
that helps validate the short-selling workflow.

Supported sub-commands
----------------------
1. pre-short – perform *pre_allocate_short_locate* then immediately submit a
   market *sell-short* order.
2. short     – directly submit a market *sell-short* order (broker takes care
   of locates automatically).

Defaults are tuned for quick validation:
• symbol: GCTK (reportedly ETB on Clear Street)
• quantity: 1 share

Examples
~~~~~~~~
Pre-allocate then short:
    $ python clear_street_locate_cli_v2.py pre-short \
        --account ACCT123 --mpid TESTX \
        --creds ./cs_creds.json --env sandbox

Short directly (no explicit pre-allocation):
    $ python clear_street_locate_cli_v2.py short \
        --account ACCT123 --mpid TESTX --token <BEARER>
"""

import argparse
import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Optional

import dotenv
import requests


import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s - %(name)s:%(lineno)d - %(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

from brokers.clear_street_broker_v2 import ClearStreetBrokerV2
from brokers.clear_street_locate_manager import (
    ClearStreetLocateManager,
    LocateFailedError,
)
from brokers.ibroker import OrderParams, EST

dotenv.load_dotenv()

# ---------------------------------------------------------------------------
# Helper – obtain bearer token via client-credentials file (sync for brevity)
# ---------------------------------------------------------------------------

def _get_access_token(creds_path: str) -> str:
    """Return OAuth *access_token* using Clear Street client-credentials flow."""
    try:
        with open(creds_path, "r") as fh:
            creds = json.load(fh)
        if not all(k in creds for k in ("client_id", "client_secret")):
            raise ValueError("credentials file must contain 'client_id' and 'client_secret'")
    except FileNotFoundError:
        print(f"Error: credentials file '{creds_path}' not found", file=sys.stderr)
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"Error: invalid JSON in credentials file '{creds_path}'", file=sys.stderr)
        sys.exit(1)

    url = "https://auth.clearstreet.io/oauth/token"
    payload = {
        "grant_type": "client_credentials",
        "client_id": creds["client_id"],
        "client_secret": creds["client_secret"],
        "audience": "https://api.clearstreet.io",
    }
    headers = {"accept": "application/json", "content-type": "application/json"}

    try:
        resp = requests.post(url, json=payload, headers=headers, timeout=20)
        resp.raise_for_status()
        return resp.json()["access_token"]
    except (requests.RequestException, KeyError) as exc:
        print(f"Error obtaining access token: {exc}", file=sys.stderr)
        sys.exit(1)


# ---------------------------------------------------------------------------
# Async main entry
# ---------------------------------------------------------------------------

async def _async_main():
    parser = argparse.ArgumentParser(
        description="Clear Street short-sell validation CLI (Broker V2)"
    )
    parser.add_argument("--creds", help="Path to JSON creds file (client_id/secret)")
    parser.add_argument("--token", help="Bearer token (overrides creds file)")
    parser.add_argument(
        "--env",
        default="sandbox",
        choices=["sandbox", "production"],
        help="Clear Street environment (default: sandbox)",
    )

    sub = parser.add_subparsers(dest="command", required=True)

    # helper to add common symbol/qty/mpid/account flags
    def _add_common_flags(p):
        p.add_argument("--account", required=True, help="Account ID / number")
        p.add_argument("--mpid", required=True, help="Market-participant ID (MPID)")
        p.add_argument("--symbol", default="GCTK", help="Ticker symbol (default: GCTK)")
        p.add_argument(
            "--quantity",
            type=float,
            default=1,
            help="Order quantity (default: 1)",
        )
        p.add_argument(
            "--expect-price",
            type=float,
            help="Expected price (optional – used for fee-checks / limit conv.)",
        )

    _add_common_flags(sub.add_parser("pre-short", help="Pre-allocate locate then short"))
    _add_common_flags(sub.add_parser("short", help="Direct short (broker handles locate)"))

    args = parser.parse_args()

    # ----------------------------------------------------------------------
    # Resolve bearer token in order of precedence
    # ----------------------------------------------------------------------
    bearer_token: Optional[str] = None
    if args.token:
        bearer_token = args.token
    elif os.environ.get("STUDIO_SDK_BEARER_TOKEN"):
        bearer_token = os.environ["STUDIO_SDK_BEARER_TOKEN"]
    elif args.creds:
        bearer_token = _get_access_token(args.creds)
    else:
        print(
            "Error: provide --token, set STUDIO_SDK_BEARER_TOKEN env var, or specify --creds",
            file=sys.stderr,
        )
        sys.exit(1)

    # ----------------------------------------------------------------------
    # Construct locate manager & broker
    # ----------------------------------------------------------------------
    locate_mgr = ClearStreetLocateManager(account_id=args.account, mpid=args.mpid, max_fee_pct=0.25)
    broker = ClearStreetBrokerV2(
        account_id=args.account,
        bearer_token=bearer_token,
        credentials_file_path=None,
        locate_manager=locate_mgr,
        environment=args.env,
    )

    # Ensure underlying connections / WS are up
    await broker.connect()

    symbol = args.symbol.upper()
    qty = args.quantity
    expect_price = args.expect_price

    params = OrderParams(
        symbol=symbol,
        quantity=qty,
        position_intent="open",
        # Use current time in EST correctly (avoid attaching EST to a naive local time)
        datetime=datetime.now(tz=EST),
        expect_price=expect_price,
    )

    try:
        if args.command == "pre-short":
            print(f"Pre-allocating locate for {qty} {symbol}…")
            loc_res = await broker.pre_allocate_short_locate(params)
            print("Pre-allocate response:", loc_res)
            print("Submitting short sell order…")
            order_res = await broker.market_sell(params)
            print("Short sell order response:", order_res)
        elif args.command == "short":
            print(f"Submitting short sell order for {qty} {symbol}…")
            order_res = await broker.market_sell(params)
            print("Short sell order response:", order_res)
    except LocateFailedError as exc:
        print(f"Locate failed: {exc}")
        sys.exit(1)
    except Exception as exc:
        print(f"Error: {exc}")
        sys.exit(1)
    finally:
        await broker.close()


# ---------------------------------------------------------------------------
# Script entry
# ---------------------------------------------------------------------------

if __name__ == "__main__":
    asyncio.run(_async_main()) 