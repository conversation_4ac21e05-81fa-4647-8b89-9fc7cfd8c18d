import nest_asyncio
nest_asyncio.apply()

import asyncio
import argparse
from datetime import datetime
import pytz
from brokers.schwab_extended_broker import <PERSON>hwabExtendedBroker as SchwabBroker
from brokers.ibroker import OrderParamsBuilder
from brokers.ibroker import Position
from brokers.ibroker_extended import BracketOrderParams

async def check_connection(broker):
    is_connected = await broker.check_connection()
    print(f"Connection status: {'Connected' if is_connected else 'Disconnected'}")
    return is_connected

async def place_market_buy(broker, args):
    try:
        # Create OrderParams using builder
        est = pytz.timezone('US/Eastern')
        current_time = datetime.now(est)
        
        params = (OrderParamsBuilder()
            .with_symbol(args.symbol)
            .with_quantity(args.quantity)
            .with_position_intent("open")
            .with_datetime(current_time)
            .build())
        
        print(f"Placing market buy order for {args.quantity} shares of {args.symbol}...")
        result = await broker.market_buy(params)
        if isinstance(result, dict) and result.get("status") == "success":
            print(f"Order placed successfully! (Status code: {result.get('code', 'unknown')})")
        else:
            print(f"Order result: {result}")
    except Exception as e:
        print(f"Failed to place buy order: {str(e)}")
        print("Full error details:", repr(e))

async def place_market_sell(broker, args):
    # Create OrderParams using builder
    est = pytz.timezone('US/Eastern')
    current_time = datetime.now(est)
    
    params = (OrderParamsBuilder()
        .with_symbol(args.symbol)
        .with_quantity(args.quantity)
        .with_position_intent("close")
        .with_datetime(current_time)
        .build())
    
    print(f"Placing market sell order for {args.quantity} shares of {args.symbol}...")
    result = await broker.market_sell(params)
    print(f"Order result: {result}")

async def list_positions(broker):
    print("Retrieving positions...")
    positions = await broker.list_positions()
    if not positions:
        print("No positions found.")
    else:
        print("\nCurrent Positions:")
        print("------------------")
        for position in positions:
            print(f"Symbol: {position.symbol}")
            print(f"Quantity: {position.quantity}")
            if position.average_cost is not None:
                print(f"Average Cost: ${position.average_cost}")
            if position.market_value is not None:
                print(f"Market Value: ${position.market_value}")
            if position.entry_time:
                print(f"Entry Time: {position.entry_time}")
            if position.trade_id:
                print(f"Trade ID: {position.trade_id}")
            print("------------------")

async def main():
    try:
        # Set up argument parser
        parser = argparse.ArgumentParser(description='Schwab Broker CLI')
        parser.add_argument(
            "--token-path", type=str, default=None,
            help="Optional path to authentication token file (overrides SCHWAB_TOKEN_PATH)"
        )
        subparsers = parser.add_subparsers(dest='command', help='Command to execute')
        
        # Check connection command
        subparsers.add_parser('check', help='Check connection to Schwab API')
        
        # Buy order command
        buy_parser = subparsers.add_parser('buy', help='Place a market buy order')
        buy_parser.add_argument('symbol', type=str, help='Symbol to buy (e.g., AAPL)')
        buy_parser.add_argument('quantity', type=int, help='Quantity to buy')
        
        # Sell order command
        sell_parser = subparsers.add_parser('sell', help='Place a market sell order')
        sell_parser.add_argument('symbol', type=str, help='Symbol to sell (e.g., AAPL)')
        sell_parser.add_argument('quantity', type=int, help='Quantity to sell')
        
        # List positions command
        subparsers.add_parser('positions', help='List current positions')

        # Bracket order command (complex order)
        bracket_parser = subparsers.add_parser('bracket', help='Place a MARKET → OCO(LIMIT, STOP) bracket order')
        bracket_parser.add_argument('symbol', type=str, help='Symbol to trade (e.g., AAPL)')
        bracket_parser.add_argument('quantity', type=int, help='Quantity to trade')
        bracket_parser.add_argument('take_profit', type=float, help='Take-profit price')
        bracket_parser.add_argument('stop_loss', type=float, help='Stop-loss price')
        bracket_parser.add_argument('--side', choices=['buy', 'sell'], default='buy', help='Entry side (buy=>long, sell=>short)')
        
        # Parse arguments
        args = parser.parse_args()
        
        # Initialize the SchwabBroker
        broker = SchwabBroker(token_path=args.token_path)
        
        print("Connecting to Schwab...")
        connected = await broker.connect()
        
        if not connected:
            print("Failed to connect to Schwab API")
            return
        
        print("Connected successfully!")
        
        # Execute the appropriate command
        if args.command == 'check':
            await check_connection(broker)
        elif args.command == 'buy':
            await place_market_buy(broker, args)
        elif args.command == 'sell':
            await place_market_sell(broker, args)
        elif args.command == 'positions':
            await list_positions(broker)
        elif args.command == 'bracket':
            # Build BracketOrderParams and submit complex order
            est = pytz.timezone('US/Eastern')
            current_time = datetime.now(est)

            params = BracketOrderParams(
                symbol      = args.symbol,
                quantity    = args.quantity,
                expect_price= 0.0,           # not used for MARKET parent
                position_intent="open",
                datetime    = current_time,
                action      = 'BUY' if args.side == 'buy' else 'SELL',
                stop_loss_price = args.stop_loss,
                take_profit_price = args.take_profit,
                entry_type  = 'MARKET',
            )

            print(f"Placing bracket order {args.side.upper()} {args.quantity} {args.symbol} TP={args.take_profit} SL={args.stop_loss}…")
            try:
                result = await broker.bracket_order(params)
                print("Bracket order placed! →", result)
            except Exception as e:
                print("Failed to place bracket order:", e)
        else:
            parser.print_help()
    
    except ValueError as e:
        print(f"Configuration error: {str(e)}")
        print("Full error details:", repr(e))
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        print("Full error details:", repr(e))

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExiting due to user interrupt...")
    except Exception as e:
        print(f"\nError: {e}") 