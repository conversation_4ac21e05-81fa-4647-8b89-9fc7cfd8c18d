# hyperscalper.py -----------------------------------------------------------
"""
Hyperscalper loop for <PERSON>hwab midpoint‑guarantee strategy.
Run:  python hyperscalper.py AAPL 100 --ticks 2 --poll 0.3
"""
import asyncio, argparse, math, sys, time
from datetime import datetime, timezone
import pytz
from brokers.schwab_extended_broker import SchwabExtendedBroker as SchwabBroker
from brokers.ibroker import OrderParamsBuilder
from tools.clock import is_regular_hours          # already in your repo

# ───────────────────────── helpers ──────────────────────────
TICK_SIZE = 0.01    # for most listed stocks; adjust for odd-lot ETFs/etc.
SELL_TIMEOUT = 60    # seconds to wait before market fallback on exit
BUY_TIMEOUT  = 60    # seconds to wait before market fallback on entry    # seconds to wait before market fallback    # for most listed stocks; adjust for odd‑lot ETFs/etc.

async def get_quote(broker, symbol: str):
    client = await broker.get_http_client()
    # Schwab returns a dict keyed by symbol
    r = await asyncio.to_thread(client.get_quotes, [symbol])
    q = r.json()[symbol]
    return {
        "bid":  float(q["quote"]["bidPrice"]),
        "ask":  float(q["quote"]["askPrice"]),
        "bid_sz": int(q["quote"]["bidSize"]),
        "ask_sz": int(q["quote"]["askSize"]),
        "timestamp": q["quote"]["quoteTime"] / 1000,  # ms → s
    }

async def wait_filled(client, account_hash: str, order_id: str, *, timeout=3):
    """Poll Schwab until order_id is FILLED, return avg fillPrice."""
    expiry = time.time() + timeout
    while time.time() < expiry:
        r = await asyncio.to_thread(client.get_order, order_id, account_hash)
        o = r.json()
        if o["status"] == "FILLED":
            # Schwab nests fills in orderActivityCollection
            legs = o["orderActivityCollection"][0]["executionLegs"]
            fills = [float(leg["price"]) * int(leg["quantity"]) for leg in legs]
            qty   = sum(int(leg["quantity"]) for leg in legs)
            return sum(fills) / qty
        await asyncio.sleep(0.05)
    raise TimeoutError(f"order {order_id} not FILLED within timeout")

# ───────────────────────── the loop ─────────────────────────
async def scalper_loop(symbol: str, qty: int, min_ticks: int,
                       exit_ticks: int, poll: float, max_rounds: int | None):
    broker = SchwabBroker(token_path="./schwab_token_2.json")
    await broker.connect()
    account_hash = await broker._get_account_hash()
    client = await broker.get_http_client()

    pnl_cum = 0.0
    round_n = 0
    est = pytz.timezone("US/Eastern")

    while max_rounds is None or round_n < max_rounds:
        # ── 0. session check ────────────────────────────────────────────
        now_et = datetime.now(est)
        if not is_regular_hours(now_et):
            await asyncio.sleep(poll); continue

        # ── 1. quote & spread check ─────────────────────────────────────
        q = await get_quote(broker, symbol)
        spread = q["ask"] - q["bid"]
        ticks  = spread / TICK_SIZE
        if ticks < min_ticks:
            await asyncio.sleep(poll); continue

        mid = (q["ask"] + q["bid"]) / 2
        round_n += 1
        print(f"[{round_n}] Spread {spread:.4f} ({ticks:.1f} ticks) → entry…")

        # ── 2. BUY at limit (mid-point), then fallback (should midpoint‑fill) ─────────────────────
        raw_buy_px = (q["bid"] + q["ask"]) / 2
        # Schwab precision rules
        buy_limit_px = round(raw_buy_px, 2) if raw_buy_px >= 1 else round(raw_buy_px, 4)
        if buy_limit_px >= q["ask"]:
            buy_limit_px = q["ask"] - (0.01 if q["ask"] >= 1 else 0.0001)
            buy_limit_px = round(buy_limit_px, 2 if buy_limit_px >= 1 else 4)

        buy_params = (
            OrderParamsBuilder()
            .with_symbol(symbol)
            .with_quantity(qty)
            .with_position_intent("open")
            .with_expect_price(buy_limit_px)
            .with_datetime(now_et)
            .build()
        )
        buy_resp = await broker.market_buy(buy_params)
        buy_order_id = buy_resp.get("order_id")

        try:
            buy_px = await wait_filled(client, account_hash, buy_order_id, timeout=BUY_TIMEOUT)
        except TimeoutError:
            print(f"  ⏳ Entry limit not filled in {BUY_TIMEOUT}s → fallback to market…")
            try:
                await asyncio.to_thread(client.cancel_order, buy_order_id, account_hash)
            except Exception:
                pass
            market_buy_params = (
                OrderParamsBuilder()
                .with_symbol(symbol)
                .with_quantity(qty)
                .with_position_intent("open")
                .with_datetime(now_et)
                .build()
            )
            buy_resp = await broker.market_buy(market_buy_params)
            buy_order_id = buy_resp.get("order_id")
            buy_px = await wait_filled(client, account_hash, buy_order_id)
        
        # ── 3. SELL at limit, then fallback ─────────────────────────────
        raw_limit_px = buy_px + exit_ticks * TICK_SIZE
        # Schwab precision: ≥$1 use 2 dp, < $1 use 4 dp
        if raw_limit_px >= 1:
            limit_px = round(raw_limit_px, 2)
        else:
            limit_px = round(raw_limit_px, 4)
        # Ensure limit_px is strictly greater than buy_px after rounding
        if limit_px <= buy_px:
            limit_px = (buy_px if buy_px >= 1 else round(buy_px, 4)) + (0.01 if buy_px >= 1 else 0.0001)
            limit_px = round(limit_px, 2 if limit_px >= 1 else 4)

        sell_params = (
            OrderParamsBuilder()
            .with_symbol(symbol)
            .with_quantity(qty)
            .with_position_intent("close")
            .with_expect_price(limit_px)
            .with_datetime(datetime.now(est))
            .build()
        )
        sell_resp = await broker.market_sell(sell_params)
        sell_order_id = sell_resp.get("order_id")

        try:
            sell_px = await wait_filled(
                client, account_hash, sell_order_id, timeout=SELL_TIMEOUT
            )
        except TimeoutError:
            print(f"  ⏳ Limit not filled in {SELL_TIMEOUT}s → fallback to market…")
            # Attempt to cancel the open limit order (ignore failures)
            try:
                await asyncio.to_thread(client.cancel_order, sell_order_id, account_hash)
            except Exception:
                pass

            market_sell_params = (
                OrderParamsBuilder()
                .with_symbol(symbol)
                .with_quantity(qty)
                .with_position_intent("close")
                .with_datetime(datetime.now(est))
                .build()
            )
            sell_resp = await broker.market_sell(market_sell_params)
            sell_order_id = sell_resp.get("order_id")
            sell_px = await wait_filled(client, account_hash, sell_order_id)

        # ── 4. P&L, report ──────────────────────────────────────────────
        pnl = (sell_px - buy_px) * qty
        pnl_cum += pnl
        ts = datetime.fromtimestamp(q["timestamp"], tz=timezone.utc).astimezone(est)
        print(f"  ✔ {symbol} {qty}@{buy_px:.4f} → {qty}@{sell_px:.4f}  "
              f"P&L = {pnl:+.2f}   Cum = {pnl_cum:+.2f}   {ts:%H:%M:%S}")
        await asyncio.sleep(poll)

# ────────────────────────── CLI ─────────────────────────────
if __name__ == "__main__":
    ap = argparse.ArgumentParser(description="Schwab hyper‑scalper loop")
    ap.add_argument("symbol")
    ap.add_argument("quantity", type=int)
    ap.add_argument("--ticks", type=int, default=2,
                    help="minimum inside spread in ticks to trigger entry")
    ap.add_argument("--exit_ticks", type=int, default=2,
                    help="ticks above buy price for initial limit exit")
    ap.add_argument("--poll", type=float, default=0.25,
                    help="seconds between quote polls")
    ap.add_argument("--rounds", type=int, default=None,
                    help="max trades before exit (omit for unlimited)")
    args = ap.parse_args()

    try:
        asyncio.run(
            scalper_loop(args.symbol.upper(), args.quantity,
                         args.ticks, args.exit_ticks, args.poll, args.rounds)
        )
    except KeyboardInterrupt:
        print("\nStopped by user")

# --------------------------------------------------------------------------
