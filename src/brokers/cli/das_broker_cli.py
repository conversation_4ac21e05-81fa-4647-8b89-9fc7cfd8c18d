#!/usr/bin/env python3
"""das_broker_cli.py – tiny asyncio CLI for DAS Trader broker

Usage examples
--------------
List positions::

    python das_broker_cli.py positions

Submit a market buy::

    python das_broker_cli.py \
        submit --side buy --symbol MSFT --qty 100 --type market

Submit a limit sell::

    python das_broker_cli.py \
        submit --side sell --symbol AAPL --qty 50 --type limit --price 195.5
"""

import os
import argparse
import asyncio
from datetime import datetime
from typing import Literal
from dotenv import load_dotenv

load_dotenv()

import pytz  # type: ignore

from brokers.ibroker import OrderParams
from brokers.das_broker import DasBroker

# ─── Load & validate environment vars ────────────────────────────────────────
HOST     = os.getenv("DAS_HOST", "127.0.0.1")
# Use the well-known DAS paper-trade port (11000) when DAS_PORT is not provided
PORT     = int(os.getenv("DAS_PORT", "11000"))
USER     = os.getenv("DAS_USER")
PASSWORD = os.getenv("DAS_PASSWORD")
ACCOUNT  = os.getenv("DAS_ACCOUNT")

_required = {
    "DAS_USER":     USER,
    "DAS_PASSWORD": PASSWORD,
    "DAS_ACCOUNT":  ACCOUNT,
}
_missing = [name for name, val in _required.items() if not val]
if _missing:
    raise RuntimeError(f"Missing required env vars: {', '.join(_missing)}")

EST = pytz.timezone("US/Eastern")

# ─── Helper – build OrderParams from CLI args ────────────────────────────────
def _build_params(
    symbol: str,
    qty: float,
    side: str,
    price: float | None
) -> OrderParams:
    return OrderParams(
        symbol=symbol.upper(),
        quantity=qty,
        position_intent="open" if side in {"buy", "sell-short"} else "close",
        datetime=datetime.now(EST),
        expect_price=price,
        timeout=60,
    )

# ─── Async flows ─────────────────────────────────────────────────────────────
async def _do_positions(broker: DasBroker) -> None:
    positions = await broker.list_positions()
    if not positions:
        print("No open positions.")
        return

    print("Symbol\tQty\tAvg cost")
    for pos in positions:
        print(f"{pos.symbol}\t{pos.quantity}\t{pos.average_cost}")

async def _do_submit(
    broker: DasBroker,
    *,
    side: Literal["buy", "sell", "sell-short"],
    symbol: str,
    qty: float,
    order_type: Literal["market", "limit", "stop"],
    price: float | None,
    tif: str,
) -> None:
    params = _build_params(symbol, qty, side, price)
    print("Submitting order…")
    try:
        if side == "buy":
            final_order = await broker.market_buy(params)
        else:
            # Handles both regular sells (closing long) and sell-shorts (opening short)
            final_order = await broker.market_sell(params)
    except TimeoutError:
        print(f"Error: market order not filled within {params.timeout}s")
        return
    oid = getattr(final_order, "order_id", None)
    status_str = getattr(final_order, "status", "")
    print(f"Order complete. Broker ID: {oid}, final status: {status_str}")
    return


# ─── CLI parsing ────────────────────────────────────────────────────────────
def _parse_cli() -> argparse.Namespace:
    p = argparse.ArgumentParser(
        description="Simple CLI for DAS Trader broker (via env vars)"
    )
    sub = p.add_subparsers(dest="cmd", required=True)

    # list positions
    sub.add_parser("positions", help="List open positions")

    # submit order
    s = sub.add_parser("submit", help="Submit new order")
    s.add_argument("--side",
                   choices=["buy", "sell", "sell-short"],
                   required=True)
    s.add_argument("--symbol", required=True)
    s.add_argument("--qty", type=float, required=True)
    s.add_argument("--type",
                   dest="order_type",
                   choices=["market", "limit", "stop"],
                   default="market")
    s.add_argument("--price", type=float,
                   help="Expected price (for limit/stop)")
    s.add_argument("--tif",
                   default="day",
                   help="Time‑in‑force (default: day)")

    return p.parse_args()

async def _main_async(opts: argparse.Namespace) -> None:
    # instantiate from env, ignore opts for connection
    broker = DasBroker(
        host=HOST,
        port=PORT,
        user=USER,
        password=PASSWORD,
        account=ACCOUNT,
    )

    try:
        await broker.connect()
        if opts.cmd == "positions":
            await _do_positions(broker)
        elif opts.cmd == "submit":
            await _do_submit(
                broker,
                side=opts.side,       # type: ignore[attr-defined]
                symbol=opts.symbol,   # type: ignore[attr-defined]
                qty=opts.qty,         # type: ignore[attr-defined]
                order_type=opts.order_type,  # type: ignore[attr-defined]
                price=opts.price,     # type: ignore[attr-defined]
                tif=opts.tif,         # type: ignore[attr-defined]
            )
    finally:
        await broker.close()

def main() -> None:
    opts = _parse_cli()
    asyncio.run(_main_async(opts))

if __name__ == "__main__":
    main()
