#!/usr/bin/env python3
"""das_locate_cli.py – simple asyncio CLI for DAS locate testing

This utility provides a very thin wrapper around :class:`DasLocateManager` so
that developers can manually exercise the locate workflow exposed by the DAS
Trader **CMD** socket.

The CLI relies exclusively on environment variables for connection/authentication
(similar to *das_broker_cli.py*) so that sensitive credentials are **not** passed
through the shell.

Environment variables (same as broker CLI)
-----------------------------------------
DAS_HOST       – IP/hostname of the CMD server (default: 127.0.0.1)
DAS_PORT       – Port of the CMD server (default: 11000)
DAS_USER       – Trader/user ID  (required)
DAS_PASSWORD   – Password         (required)
DAS_ACCOUNT    – Optional account identifier (can be blank)

Example usage
-------------
Request 1 000 shares of AAPL locate with a 30-second timeout::

    python das_locate_cli.py ensure --symbol AAPL --qty 1000 --timeout 30

Release 500 shares afterwards::

    python das_locate_cli.py release --symbol AAPL --qty 500

Inspect current in-memory inventory::

    python das_locate_cli.py inventory
"""
from __future__ import annotations

import os
import argparse
import asyncio
from typing import Optional

from dotenv import load_dotenv

load_dotenv()

import pytz  # type: ignore

from brokers.das_broker import DasBroker
from brokers.das_locate_manager import DasLocateManager, LocateFailedError

# ─── Load & validate environment vars ─────────────────────────────────────────
HOST     = os.getenv("DAS_HOST", "127.0.0.1")
PORT     = int(os.getenv("DAS_PORT", "11000"))
USER     = os.getenv("DAS_USER")
PASSWORD = os.getenv("DAS_PASSWORD")
ACCOUNT  = os.getenv("DAS_ACCOUNT", "")

_required = {
    "DAS_USER":     USER,
    "DAS_PASSWORD": PASSWORD,
}
_missing = [name for name, val in _required.items() if not val]
if _missing:
    raise RuntimeError(f"Missing required env vars: {', '.join(_missing)}")

EST = pytz.timezone("US/Eastern")

# ─── Async helpers ────────────────────────────────────────────────────────────
async def _do_ensure(
    mgr: DasLocateManager,
    *,
    symbol: str,
    qty: float,
    expect_price: Optional[float],
    timeout: float,
) -> None:
    symbol = symbol.upper()
    print(f"Requesting locate for {qty} {symbol}…")
    try:
        await mgr.ensure_locate(symbol, qty, expect_price, timeout)
    except LocateFailedError as exc:
        print(f"Locate failed: {exc}")
        return
    except asyncio.TimeoutError:
        print(f"Error: locate not filled within {timeout}s")
        return
    print("Locate reserved successfully.")

async def _do_release(
    mgr: DasLocateManager,
    *,
    symbol: str,
    qty: float,
) -> None:
    symbol = symbol.upper()
    await mgr.release_locate(symbol, qty)
    print(f"Released {qty} {symbol} locate.")

async def _do_inventory(mgr: DasLocateManager) -> None:
    if not mgr._inventory:  # pylint: disable=protected-access
        print("No locates in inventory.")
        return
    print("Symbol\tAvailable\tUsed")
    for sym, inv in mgr._inventory.items():  # pylint: disable=protected-access
        print(f"{sym}\t{inv['available']}\t{inv['used']}")

# ─── CLI parsing ──────────────────────────────────────────────────────────────

def _parse_cli() -> argparse.Namespace:
    p = argparse.ArgumentParser(
        description="Simple CLI for DAS locate manager (via env vars)"
    )
    sub = p.add_subparsers(dest="cmd", required=True)

    # ensure (request/hold) locates
    s_ensure = sub.add_parser("ensure", help="Request/ensure locates for symbol")
    s_ensure.add_argument("--symbol", required=True)
    s_ensure.add_argument("--qty", type=float, required=True)
    s_ensure.add_argument("--expect-price", type=float, help="Expected market price for fee cap checks")
    s_ensure.add_argument("--timeout", type=float, default=30.0, help="Timeout waiting for fill (seconds)")

    # release locates
    s_release = sub.add_parser("release", help="Return locates back to pool")
    s_release.add_argument("--symbol", required=True)
    s_release.add_argument("--qty", type=float, required=True)

    # inventory
    sub.add_parser("inventory", help="Show current locate inventory")

    return p.parse_args()

# ─── Entry ────────────────────────────────────────────────────────────────────
async def _main_async(opts: argparse.Namespace) -> None:
    broker = DasBroker(
        host=HOST,
        port=PORT,
        trader=USER,  # type: ignore[arg-type]
        password=PASSWORD,  # type: ignore[arg-type]
        account=ACCOUNT,
    )

    await broker.connect()
    mgr = DasLocateManager(broker)

    try:
        if opts.cmd == "ensure":
            await _do_ensure(
                mgr,
                symbol=opts.symbol,  # type: ignore[attr-defined]
                qty=opts.qty,  # type: ignore[attr-defined]
                expect_price=opts.expect_price,  # type: ignore[attr-defined]
                timeout=opts.timeout,  # type: ignore[attr-defined]
            )
        elif opts.cmd == "release":
            await _do_release(
                mgr,
                symbol=opts.symbol,  # type: ignore[attr-defined]
                qty=opts.qty,  # type: ignore[attr-defined]
            )
        elif opts.cmd == "inventory":
            await _do_inventory(mgr)
    finally:
        await broker.close()


def main() -> None:
    opts = _parse_cli()
    asyncio.run(_main_async(opts))


if __name__ == "__main__":
    main()
