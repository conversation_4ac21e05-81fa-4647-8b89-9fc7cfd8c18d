"""Schwab helper utilities for building complex bracket (trigger-OCO) orders.

This helper keeps all Schwab-specific JSON quirks in one place so that broker
classes can stay thin.
"""

from schwab.orders.generic import OrderBuilder
from schwab.orders.common import Duration, Session, OrderType, OrderStrategyType
from schwab.orders.equities import (
    equity_buy_market,
    equity_sell_market,
    equity_sell_short_market,
    equity_buy_to_cover_market,
    equity_sell_limit,
    equity_buy_to_cover_limit,
)

__all__ = ["build_equity_bracket_ott_market"]


def _build_stop_order(
    *,
    symbol: str,
    qty: int,
    side: str,
    stop_px: float,
    session: Session,
    duration: Duration,
) -> OrderBuilder:
    """Helper to build a STOP-market order with correct direction."""

    if side == "LONG":
        # Exit long → sell stop
        ob = equity_sell_market(symbol, qty)
    else:
        # Exit short → buy-to-cover stop
        ob = equity_buy_to_cover_market(symbol, qty)

    # Convert to STOP order type + set stop price
    ob.set_order_type(OrderType.STOP).set_stop_price(str(stop_px))
    # Ensure session/duration override
    ob.set_session(session).set_duration(duration)
    return ob


def build_equity_bracket_ott_market(
    *,
    symbol: str,
    qty: int,
    side: str,  # "LONG" | "SHORT"
    take_profit_px: float,
    stop_px: float,
    session: Session = Session.NORMAL,
    duration: Duration = Duration.DAY,
) -> OrderBuilder:
    """Return a **One-Triggers-Two** equity bracket order.

    * Parent  – MARKET (IOC) entry using Schwab templates.
    * Child #1 – LIMIT TP
    * Child #2 – STOP  SL
    * Children wrapped in OCO so the sibling auto-cancels.
    """

    # ── parent ────────────────────────────────────────────────────────────
    if side == "LONG":
        parent = equity_buy_market(symbol, qty)
    else:
        parent = equity_sell_short_market(symbol, qty)

    # Override for IOC entry
    parent.set_session(session).set_duration(duration)
    parent.set_order_strategy_type(OrderStrategyType.TRIGGER)

    # ── TP child (LIMIT) ────────────────────────────────────────────────
    if side == "LONG":
        tp_child = equity_sell_limit(symbol, qty, str(take_profit_px))
    else:
        tp_child = equity_buy_to_cover_limit(symbol, qty, str(take_profit_px))
    tp_child.set_session(session).set_duration(duration)

    # ── SL child (STOP) ─────────────────────────────────────────────────
    sl_child = _build_stop_order(
        symbol=symbol,
        qty=qty,
        side=side,
        stop_px=stop_px,
        session=session,
        duration=duration,
    )

    # ── Wrap TP & SL in OCO container ----------------------------------
    oco = OrderBuilder().set_order_strategy_type(OrderStrategyType.OCO)
    oco.add_child_order_strategy(tp_child).add_child_order_strategy(sl_child)

    # Attach children to parent and return
    parent.add_child_order_strategy(oco)
    return parent

# EOF 