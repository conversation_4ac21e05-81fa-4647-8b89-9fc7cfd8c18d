from __future__ import annotations

from abc import abstractmethod
import asyncio
import time
import logging
import datetime
from typing import Any, Literal

from .ibroker import I<PERSON>roker, OrderParams, Order, EST


logger = logging.getLogger(__name__)


class IRawOrderBroker(IBroker):
    """Low-level order wire-protocol. Sub-classes MUST be fully async-safe."""

    # ------------------------------------------------------------------
    # Abstract low-level primitives that concrete brokers MUST implement
    # ------------------------------------------------------------------

    @abstractmethod
    async def submit_order(
        self,
        *,
        side: Literal["buy", "sell", "sell-short"],
        params: OrderParams,
        order_type: Literal["market", "limit", "stop"] = "market",
        time_in_force: str = "day",
    ) -> str:
        """Submit a new order and return broker order_token."""
        ...

    @abstractmethod
    async def check_order_status(self, order_token: str) -> Order | None:
        """Return last known status; MUST be O(1) and NOT block on the REST API."""
        ...

    @abstractmethod
    async def patch_order(
        self,
        order_token: str,
        *,
        price: float | None = None,
        quantity: float | None = None,
    ) -> None:
        """Best-effort price / qty amend. Raise NotImplementedError if unsupported."""
        ...

    async def cancel_order(self, order_token: str):  # type: ignore[override]
        """Optional convenience; brokers may override for efficiency."""
        raise NotImplementedError

    # ---------------------------------------------------------------------
    # Default IBroker implementations using low-level raw order primitives
    # ---------------------------------------------------------------------

    async def _wait_for_fill(
        self,
        order_token: str,
        *,
        timeout: float = 60,
        poll_interval: float = 0.25,
    ) -> Order | None:
        """Poll *check_order_status* until filled or *timeout* expires."""
        start = time.monotonic()
        while True:
            order = await self.check_order_status(order_token)
            logger.debug("Poll order %s status=%s", order_token, order)
            if order is None:
                await asyncio.sleep(poll_interval)
                continue
            if self.is_order_filled(order):
                return order
            if (time.monotonic() - start) >= timeout:
                break
            await asyncio.sleep(poll_interval)
        # Timeout – best-effort cancel and raise
        try:
            await self.cancel_order(order_token)
        except NotImplementedError:
            pass
        raise TimeoutError(f"Order {order_token} not filled within {timeout}s")

    def is_order_filled(self, order: Order) -> bool:
        """Concrete brokers may override with richer status semantics."""
        return bool(order and str(order.status).lower() == "filled")

    # ------------------------------------------------------------------
    # Helper – detect pre-market hours (04:00-09:30 EST)
    # ------------------------------------------------------------------

    def _is_pre_market(self, order_dt: datetime.datetime) -> bool:
        """Return True if *order_dt* in EST falls within 04:00-09:30."""
        if order_dt.tzinfo is None:
            dt_est = EST.localize(order_dt)
        else:
            dt_est = order_dt.astimezone(EST)
        start = dt_est.replace(hour=4, minute=0, second=0, microsecond=0)
        end = dt_est.replace(hour=9, minute=30, second=0, microsecond=0)
        return start <= dt_est < end

    # ------------------------------------------------------------------
    # Convenience wrappers (mimic IBroker high-level API)
    # ------------------------------------------------------------------

    async def market_buy(self, params: OrderParams):  # type: ignore[override]
        """Submit a market-buy (or pre-market limit-buy) and wait until filled."""
        params.validate()

        # --- resolve order type based on session ------------------------
        order_type: Literal["market", "limit"] = (
            "limit" if self._is_pre_market(params.datetime) else "market"
        )
        if order_type == "limit" and params.expect_price is None:
            raise ValueError("Pre-market limit orders require expect_price to be set")

        order_token = await self.submit_order(
            side="buy",
            params=params,
            order_type=order_type,
            time_in_force="day+" if order_type == "limit" else "day",
        )
        order = await self._wait_for_fill(order_token, timeout=params.timeout)

        # Standardize response as a dict similar to IBKRBroker implementation
        filled_qty = order.filled_qty if order and order.filled_qty is not None else order.qty if order else None
        return {
            "order_id":  order_token,        # Return token as the order_id per caller expectation
            "status":    order.status if order else "unknown",
            "symbol":    order.symbol if order else params.symbol,
            "quantity":  filled_qty,
            "side":      "buy",
            "type":      order_type,
            "avg_price": order.price if order else None,
        }

    async def market_sell(self, params: OrderParams):  # type: ignore[override]
        """Submit a market-sell (or pre-market limit-sell) and wait until filled."""
        params.validate()

        # Heuristic: opening a position with *sell* likely means selling short.
        side: Literal["sell", "sell-short"] = (
            "sell-short" if params.position_intent == "open" else "sell"
        )

        # --- resolve order type based on session ------------------------
        order_type: Literal["market", "limit"] = (
            "limit" if self._is_pre_market(params.datetime) else "market"
        )
        if order_type == "limit" and params.expect_price is None:
            raise ValueError("Pre-market limit orders require expect_price to be set")

        order_token = await self.submit_order(
            side=side,
            params=params,
            order_type=order_type,
            time_in_force="day+" if order_type == "limit" else "day",
        )
        order = await self._wait_for_fill(order_token, timeout=params.timeout)

        # Standardize response as a dict similar to IBKRBroker implementation
        filled_qty = order.filled_qty if order and order.filled_qty is not None else order.qty if order else None
        return {
            "order_id":  order_token,        # Return token as the order_id per caller expectation
            "status":    order.status if order else "unknown",
            "symbol":    order.symbol if order else params.symbol,
            "quantity":  filled_qty,
            "side":      side,
            "type":      order_type,
            "avg_price": order.price if order else None,
        }
