# clearstreetbrokerv2.py – v3 with automatic OCO management
"""
Clear Street broker wrapper (**long‑only**) built on **AsyncStudioSDK** plus a
self‑contained *Order Coordinator* that automatically handles take‑profit /
stop‑loss OCO logic.

Key abstractions
================
1. **OrderFactory** – light helper for building validated order‑request dicts
   (market / limit / stop / stop‑limit / trailing‑stop).
2. **EventBus** – central asyncio broadcast channel fed by the WebSocket
   listener so higher‑level components can subscribe to real‑time updates
   without touching WS plumbing.

Public API remains compatible with the previous version (`market_buy`,
`bracket_order`, …) but callers no longer need to write bespoke TP/SL
cancellation code.

Out‑of‑scope: locates, short selling.
"""

from __future__ import annotations

import asyncio
import contextlib
import json
import logging
import secrets
import os
import httpx
from dataclasses import dataclass
from typing import Any, Dict, Optional, Sequence, Set, Callable, Awaitable

import websockets
from studio_sdk import (
    AsyncStudioSDK,
    APIConnectionError,
    APIStatusError,
)
from studio_sdk.types.accounts import bulk_order_create_params

from brokers.ibroker import (
    OrderParams,
    IBroker,
    Position,
)
from brokers.ibroker_extended import (
    BracketOrderParams,
    TrailingStopOrderParams,
)

from .ibroker import ShortLocateResponse, OrderParams

# ─── locate manager ---------------------------------------------------------
from .clear_street_locate_manager import (
    ClearStreetLocateManager,
    LocateFailedError,
)

import datetime
import pytz

_logger = logging.getLogger(__name__)

_WS_PROD = "wss://api.clearstreet.io/studio/v2/ws"
_WS_SANDBOX = "wss://sandbox-api.clearstreet.io/studio/v2/ws"

EST = pytz.timezone('America/New_York')

# ──────────────────────────── helper dataclasses ────────────────────────────
@dataclass
class _PendingOrder:
    order_id: str
    future: asyncio.Future


# ──────────────────────────── helper builders ───────────────────────────────
class OrderFactory:
    """Static helpers returning dicts compatible with SDK order params."""

    @staticmethod
    def market(symbol: str, qty: int | float, side: str, *, ref: str):
        return {
            "order_type": "market",
            "side": side,
            "symbol": symbol,
            "quantity": str(qty),
            "time_in_force": "day",
            "reference_id": ref,
        }

    @staticmethod
    def limit(symbol: str, qty: int | float, side: str, price: float, *, ref: str):
        o = OrderFactory.market(symbol, qty, side, ref=ref)
        o.update({"order_type": "limit", "price": f"{price:.2f}"})
        return o

    @staticmethod
    def stop(symbol: str, qty: int | float, side: str, stop_price: float, *, ref: str):
        o = OrderFactory.market(symbol, qty, side, ref=ref)
        o.update({"order_type": "stop", "stop_price": f"{stop_price:.2f}"})
        return o


# ──────────────────────────── WS helper client ─────────────────────────────
class _ActivityWebSocketClient:
    """Background WebSocket client that manages Clear Street *activity* stream.

    The client handles the initial subscribe handshake (waits for
    ``subscribe-activity-ack`` **and** ``replay-complete``) and keeps the
    connection alive with automatic reconnection. Incoming messages (other
    than the handshake control events) are routed to *on_message* callback.
    """

    def __init__(
        self,
        *,
        ws_url: str,
        bearer_token: str,
        account_id: str,
        on_message: Callable[[Dict[str, Any]], Awaitable[None]],
    ) -> None:
        self._ws_url = ws_url
        self._bearer_token = bearer_token
        self._account_id = account_id
        self._on_message = on_message

        self._task: Optional[asyncio.Task] = None
        self._ready = asyncio.Event()

    # ------------------------------------------------------------------
    # Public API
    async def connect(self):
        """Ensure the WS task is running and wait until handshake completes."""
        if self._task and not self._task.done():
            await self._ready.wait()
            return

        self._task = asyncio.create_task(self._run(), name="cs-ws")
        await self._ready.wait()

    async def close(self):
        if self._task and not self._task.done():
            self._task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._task

    # ------------------------------------------------------------------
    # Internal
    async def _run(self):
        while True:
            try:
                _logger.info("Connecting to Clear Street WS %s", self._ws_url)
                async with websockets.connect(self._ws_url, ping_interval=15) as ws:
                    _logger.info("WS connection established – sending subscribe-activity")

                    await ws.send(
                        json.dumps(
                            {
                                "authorization": self._bearer_token,
                                "payload": {
                                    "type": "subscribe-activity",
                                    "account_id": self._account_id,
                                },
                            }
                        )
                    )

                    got_ack = False

                    # Await handshake (ack + replay complete)
                    while True:
                        raw = await ws.recv()

                        # Parse first to see the message type so we can skip
                        # very noisy heartbeats from logs.
                        msg = json.loads(raw)
                        payload = msg.get("payload", {})
                        typ = payload.get("type")

                        # Log everything except heartbeats at *debug* level.
                        if typ != "heartbeat":
                            _logger.debug("WS raw recv: %s", raw)

                        # Handshake control messages ----------------------------------
                        if typ == "subscribe-activity-ack":
                            got_ack = True
                            _logger.debug("subscribe-activity-ack received")
                            continue  # keep waiting for replay-complete

                        if typ == "replay-complete":
                            if not got_ack:
                                _logger.warning("Received replay-complete before ack – continuing")
                            _logger.info("replay-complete received – WS ready")
                            self._ready.set()
                            # Skip dispatching replay-complete (control message)
                            continue

                        # Other messages – forward to broker callback ---------------
                        await self._on_message(msg)

            except Exception as exc:
                _logger.warning("WS closed (%s) – reconnecting in 2s", exc)
                self._ready.clear()
                await asyncio.sleep(2)

            # Loop will retry connection automatically


# ──────────────────────────── main broker class ─────────────────────────────
class ClearStreetBrokerV2(IBroker):
    """Async broker with automatic OCO TP/SL management."""

    # env-var for path to creds-file (mirrors v1 broker)
    CREDENTIALS_FILE_ENV_VAR = "CLEAR_STREET_CREDENTIALS_FILE"

    ETB_CACHE_TTL = 24 * 60 * 60  # 1 day

    def __init__(
        self,
        account_id: str,
        bearer_token: Optional[str] = None,
        credentials_file_path: Optional[str] = None,
        *,
        locate_manager: Optional[ClearStreetLocateManager] = None,
        environment: str = "sandbox",
        max_retries: int = 2,
        timeout: float = 30.0,
        order_fill_timeout: float = 45.0,
    ) -> None:
        if not account_id:
            raise ValueError("account_id required")

        self.account_id = account_id
        self._env = environment.lower()
        self._order_fill_timeout = order_fill_timeout
        # ETB cache
        self._etb_cache: Optional[Set[str]] = None
        self._last_etb_fetch_time: float = 0.0
        self._etb_cache_lock = asyncio.Lock()

        # ─── bearer-token resolution ─────────────────────────────────────
        self._creds_file_path = credentials_file_path or os.environ.get(
            self.CREDENTIALS_FILE_ENV_VAR
        )
        final_bearer_token: Optional[str] = bearer_token
        token_source = "direct argument"

        if not final_bearer_token and self._creds_file_path:
            try:
                final_bearer_token = self._get_token_from_creds(self._creds_file_path)
                token_source = f"credentials file ({self._creds_file_path})"
            except Exception as exc:
                _logger.error(
                    "Failed to load bearer token from creds file %s: %s",
                    self._creds_file_path,
                    exc,
                )

        if not final_bearer_token:
            env_var = getattr(AsyncStudioSDK, "ENV_BEARER_TOKEN", "STUDIO_SDK_BEARER_TOKEN")
            token_source = f"SDK environment variable ({env_var})"

        _logger.info("ClearStreetBrokerV2 initialising – bearer token source: %s", token_source)

        self._sdk = AsyncStudioSDK(
            bearer_token=final_bearer_token,
            environment=self._env,
            max_retries=max_retries,
            timeout=timeout,
        )

        # ─── locate & ETB infra ───────────────────────────────────────────
        self._locate_mgr = locate_manager
        self._locate_mgr._sdk = self._sdk # TODO: Fix this hack to pass the sdk to the locate manager

        # WS infra & event bus
        self._ws_url = _WS_SANDBOX if self._env == "sandbox" else _WS_PROD
        # Dedicated WebSocket client (started lazily)
        self._ws_client: Optional[_ActivityWebSocketClient] = None
        self._event_queue: asyncio.Queue[dict[str, Any]] = asyncio.Queue()

        # sync waiters for individual orders
        self._order_waiters: dict[str, _PendingOrder] = {}

    # ------------------------------------------------------------------
    # Public lifecycle helpers
    async def connect(self):
        await self.connect_ws()
        # Pre-load ETB cache
        await self._is_etb('AAPL')

    async def close(self):
        if self._ws_client is not None:
            await self._ws_client.close()
        await self._sdk.close()

    # ------------------------------------------------------------------
    # IBroker primitives
    async def check_connection(self):
        try:
            await self._sdk.accounts.retrieve(account_id=self.account_id)
            await self.connect_ws()
            return True
        except (APIStatusError, APIConnectionError):
            return False

    async def market_buy(self, params: OrderParams):
        symbol = params.symbol.upper()

        result = await self._place_and_wait("buy", "market", params)

        # If we were previously short, release shares back to locate pool
        if self._locate_mgr:
            await self._locate_mgr.release_quantity(symbol, params.quantity)

        return result

    async def market_sell(self, params: OrderParams):
        symbol = params.symbol.upper()

        # -------------------------- closing-long path --------------------------
        if params.position_intent != "open":
            return await self._place_and_wait("sell", "market", params)

        # -------------------------- short-sell path ----------------------------
        if not self._locate_mgr:
            raise RuntimeError("locate manager not configured – cannot short sell")

        side = "sell-short"

        # ETB stocks do not require locates
        if await self._is_etb(symbol):
            return await self._place_and_wait(side, "market", params)

        # Non-ETB – obtain locates (continue on timeout, rollback on order error)
        try:
            async with self._locate_mgr.with_locates(
                symbol,
                params.quantity,
                expect_price=params.expect_price,
                timeout=60.0, # 1 minute locate timeout
            ):
                return await self._place_and_wait(side, "market", params)
        except asyncio.TimeoutError:
            # Previously we proceeded without locates; now we fail fast so the
            # caller can decide how to handle the timeout.
            raise LocateFailedError(f"Locate request timed out for {symbol}")
        except LocateFailedError:
            raise

    # ------------------------------------------------------------------
    # Helper – is ETB with cache
    async def _is_etb(self, symbol: str) -> bool:
        symbol = symbol.upper()
        now = asyncio.get_event_loop().time()
        async with self._etb_cache_lock:
            if (
                self._etb_cache is not None
                and (now - self._last_etb_fetch_time) < self.ETB_CACHE_TTL
            ):
                return symbol in self._etb_cache

            try:
                resp = await self._sdk.accounts.easy_borrows.list(account_id=self.account_id)
                self._etb_cache = set(resp.data)
                self._last_etb_fetch_time = now
            except Exception as exc:
                _logger.warning("failed to fetch ETB list: %s – assume not ETB", exc)
                self._etb_cache = None
                self._last_etb_fetch_time = 0.0
                return False

            return symbol in self._etb_cache

    # ------------------------------------------------------------------
    # Helper – detect pre/post-market hours
    def _is_pre_market(self, order_dt: datetime.datetime) -> bool:
        """Return True if *order_dt* in EST falls within 04:00-09:30."""
        if order_dt.tzinfo is None:
            dt_est = EST.localize(order_dt)
        else:
            dt_est = order_dt.astimezone(EST)
        start = dt_est.replace(hour=4, minute=0, second=0, microsecond=0)
        end   = dt_est.replace(hour=9, minute=30, second=0, microsecond=0)
        return start <= dt_est < end

    def _is_post_market(self, order_dt: datetime.datetime) -> bool:
        """Return True if *order_dt* in EST falls within 16:00-20:00."""
        if order_dt.tzinfo is None:
            dt_est = EST.localize(order_dt)
        else:
            dt_est = order_dt.astimezone(EST)
        start = dt_est.replace(hour=16, minute=0, second=0, microsecond=0)
        end   = dt_est.replace(hour=20, minute=0, second=0, microsecond=0)
        return start <= dt_est < end

    # ------------------------------------------------------------------
    async def pre_allocate_short_locate(self, params: OrderParams):  # type: ignore[override]
        """Request locates for *params.symbol* without placing any sell order.

        The locate is performed via ``ClearStreetLocateManager`` and therefore
        benefits from de-duplication and auto-re-use once a real short sell is
        submitted.  If the locate is already available (ETB or previously
        allocated) it resolves immediately.
        """
        if not self._locate_mgr:
            raise RuntimeError("locate manager not configured – cannot allocate locates")

        symbol = params.symbol.upper()
        qty = params.quantity

        # ETB → no locate needed
        if await self._is_etb(symbol):
            return ShortLocateResponse(
                success=True,
                symbol=symbol,
                requested_quantity=qty,
                located_quantity=qty,
                message="Symbol is ETB – no locate required",
            )

        try:
            reservations = await self._locate_mgr.ensure_locate(
                symbol,
                qty,
                expect_price=params.expect_price,
                timeout=3600, # 1 hour locate timeout
            )
            located_qty = sum(r[1] for r in reservations)
            loc_ids = ",".join(r[0] for r in reservations)
            return ShortLocateResponse(
                success=True,
                symbol=symbol,
                requested_quantity=qty,
                located_quantity=located_qty,
                locate_id=loc_ids,
                message="Locate allocated",
            )
        except asyncio.TimeoutError:
            return ShortLocateResponse(
                success=True,
                symbol=symbol,
                requested_quantity=qty,
                located_quantity=0.0,
                message="Locate request pending – will complete in background",
            )
        except LocateFailedError as exc:
            return ShortLocateResponse(
                success=False,
                symbol=symbol,
                requested_quantity=qty,
                located_quantity=0.0,
                message=f"Locate failed: {exc}",
            )

    async def list_positions(self):
        resp = await self._sdk.accounts.positions.list(account_id=self.account_id)
        return [Position(symbol=p.symbol, quantity=float(p.quantity)) for p in resp.data]

    # ------------------------------------------------------------------
    # Internal – order submission helpers
    async def _place_and_wait(
        self,
        side: str,
        order_type: str,
        params: OrderParams,
        price: Optional[float] = None,
    ) -> Dict[str, Any]:
        await self.connect_ws()

        # --- determine final order_type & time_in_force -------------------
        final_type = order_type.lower()
        tif = "day"

        # Auto-convert market → limit and set day-plus during extended hours
        # Clear Street rejects "day" TIF orders outside regular session
        if self._is_pre_market(params.datetime) or self._is_post_market(params.datetime):
            if final_type == "market":
                if params.expect_price is None:
                    raise ValueError("Extended-hours market orders require expect_price to convert to limit")
                final_type = "limit"
                tif = "day-plus"
                price = params.expect_price
            elif final_type == "limit":
                tif = "day-plus"
        else:
            # Non pre-market: keep user-specified type; use default tif
            if final_type == "limit":
                tif = "day"
            elif final_type == "market":
                tif = "day"
                
        ref = f"{params.symbol}_{secrets.token_hex(3)}"

        req: Dict[str, Any] = {
            "account_id": self.account_id,
            "symbol": params.symbol.upper(),
            "quantity": str(params.quantity),
            "side": side,
            "order_type": final_type,
            "time_in_force": tif,
            "reference_id": ref,
        }

        if price is not None:
            try:
                price_float = float(price)
                price_str = str(round(price_float, 2))
            except (ValueError, TypeError):
                price_str = str(price)
            req["price"] = price_str

        resp = await self._sdk.accounts.orders.create(**req)
        order_id = resp.order_id

        fut = asyncio.get_event_loop().create_future()
        self._order_waiters[order_id] = _PendingOrder(order_id, fut)
        try:
            return await asyncio.wait_for(fut, timeout=self._order_fill_timeout)
        finally:
            self._order_waiters.pop(order_id, None)

    async def _safe_bulk_create(self, legs: Sequence[bulk_order_create_params.Order]):
        if not legs:
            raise ValueError("no legs to submit")
        br = await self._sdk.accounts.bulk_orders.create(
            account_id=self.account_id, orders=legs
        )
        if br.rejected:
            raise RuntimeError(f"bulk rejected {br.rejected}")
        return br

    # ------------------------------------------------------------------
    # Internal – websocket loop
    # ------------------------------------------------------------------
    # New WS entrypoint – wraps helper client
    async def connect_ws(self):
        """Ensure the activity WebSocket is connected & fully initialised."""
        if self._ws_client is None:
            self._ws_client = _ActivityWebSocketClient(
                ws_url=self._ws_url,
                bearer_token=self._sdk.bearer_token,
                account_id=self.account_id,
                on_message=self._dispatch_ws,
            )

        await self._ws_client.connect()

    async def _dispatch_ws(self, msg: Dict[str, Any]):
        payload = msg.get("payload", {})
        typ = payload.get("type")

        # Ignore low-value heartbeat chatter
        if typ == "heartbeat":
            return

        if typ in {"subscribe-activity-ack", "replay-complete"}:
            _logger.debug("WS %s received", typ)
            return

        _logger.info("WS message type: %s", typ)

        if typ == "order-update":
            order = payload.get("data", {})
            oid = order.get("order_id")
            status = str(order.get("status") or "").lower()
            state  = str(order.get("state")  or "").lower()

            _logger.info("Order update – id=%s status=%s state=%s", oid, status, state)

            # Resolve waiter ONLY when the order reaches a terminal state
            terminal_statuses = {"filled", "cancelled", "rejected"}
            terminal_states   = {"closed", "filled"}

            if status in terminal_statuses or state in terminal_states:
                waiter = self._order_waiters.get(oid)
                if waiter and not waiter.future.done():
                    waiter.future.set_result(order)
            return

        # Fallback: log other *-update messages at debug level
        if typ and typ.endswith("update"):
            _logger.debug("Unhandled update type %s: %s", typ, payload)

    # ------------------------------------------------------------------
    # Helper – fetch bearer token via creds file (sync)
    @staticmethod
    def _get_token_from_creds(credentials_file: str) -> str:
        """
        Synchronously obtain a bearer token using Clear Street's OAuth
        client-credentials flow. The JSON creds file must contain
        ``client_id`` and ``client_secret`` keys.
        """
        with open(credentials_file, "r") as fh:
            creds = json.load(fh)

        client_id = creds.get("client_id")
        client_secret = creds.get("client_secret")
        if not client_id or not client_secret:
            raise ValueError("credentials file missing 'client_id' or 'client_secret'")

        payload = {
            "grant_type": "client_credentials",
            "client_id": client_id,
            "client_secret": client_secret,
            "audience": "https://api.clearstreet.io",
        }
        headers = {"accept": "application/json", "content-type": "application/json"}

        with httpx.Client(timeout=20.0) as client:
            resp = client.post("https://auth.clearstreet.io/oauth/token", json=payload, headers=headers)
            resp.raise_for_status()
            data = resp.json()

        token = data.get("access_token")
        if not token:
            raise ValueError("access_token missing in OAuth response")
        return token
    
    # ------------------------------------------------------------------
    # Complex orders support – intentionally NOT implemented in base broker
    async def supports_complex_orders(self) -> bool:  # type: ignore[override]
        """Return False – base broker only supports simple market/limit orders."""
        return False

    async def bracket_order(self, params: BracketOrderParams):  # type: ignore[override]
        """Bracket orders are not supported in the base broker.

        Use ClearStreetBrokerV2Extended for native bracket order capability.
        """
        raise NotImplementedError(
            "Bracket orders not supported in ClearStreetBrokerV2. "
            "Use ClearStreetBrokerV2Extended for complex order functionality."
        )

    async def trailing_stop_order(self, params: TrailingStopOrderParams):  # type: ignore[override]
        """Trailing stop orders are not supported in the base broker.

        Use ClearStreetBrokerV2Extended for native trailing-stop order capability.
        """
        raise NotImplementedError(
            "Trailing stop orders not supported in ClearStreetBrokerV2. "
            "Use ClearStreetBrokerV2Extended for complex order functionality."
        )
