from abc import ABC, abstractmethod
from typing import Dict, List, Any

class IBrokerShortable(ABC):
    """
    IBrokerShortable defines methods specific to brokers that support short selling and locating shares.
    This interface extends the functionality of the base IBroker interface with short-specific operations.
    """
    
    @abstractmethod
    async def get_shortable_info(self, symbol: str) -> Dict[str, Any]:
        """
        Get shortable information for a symbol.
        
        Parameters:
            symbol (str): The ticker symbol
            
        Returns:
            dict: Information about shortable status including 'shortable', 'size', and margin rates
        """
        pass
    
    @abstractmethod
    async def short_locate_inquiry(self, symbol: str, quantity: float, route: str = None) -> Dict[str, Any]:
        """
        Inquire about the price and availability for locating shares to short.
        
        Parameters:
            symbol (str): The ticker symbol
            quantity (float): Number of shares to locate
            route (str, optional): The locate route to use
            
        Returns:
            dict: Information about the locate offer including price per share and available size
        """
        pass
    
    @abstractmethod
    async def request_short_locate(self, symbol: str, quantity: float, route: str = None) -> Dict[str, Any]:
        """
        Request shares to be located for short selling.
        
        Parameters:
            symbol (str): The ticker symbol
            quantity (float): Number of shares to locate
            route (str, optional): The locate route to use
            
        Returns:
            dict: Information about the short locate order
        """
        pass
    
    @abstractmethod
    async def cancel_short_locate(self, locate_id: str) -> bool:
        """
        Cancel a pending short locate order.
        
        Parameters:
            locate_id (str): The ID of the short locate order to cancel
            
        Returns:
            bool: True if cancellation was successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def accept_short_locate_offer(self, locate_id: str) -> bool:
        """
        Accept a short locate offer (for route systems that require acceptance).
        
        Parameters:
            locate_id (str): The ID of the short locate offer to accept
            
        Returns:
            bool: True if acceptance was successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def reject_short_locate_offer(self, locate_id: str) -> bool:
        """
        Reject a short locate offer.
        
        Parameters:
            locate_id (str): The ID of the short locate offer to reject
            
        Returns:
            bool: True if rejection was successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def list_short_locates(self) -> List[Dict[str, Any]]:
        """
        List all current short locate orders.
        
        Returns:
            list: A list of short locate order dictionaries
        """
        pass