# brokers/ibkr_broker.py

import asyncio
from ib_async import IB, Stock, Contract
from ib_async.order import MarketOrder, LimitOrder, StopOrder, StopLimitOrder
from ib_async.order import OrderStatus

from brokers.ibroker import I<PERSON><PERSON>r, OrderParams, Position
from brokers.ibroker_extended import IBrokerExtended, BracketOrderParams, TrailingStopOrderParams

from enum import Enum, auto
from typing import Dict, Any
from datetime import datetime, timedelta, timezone
from zoneinfo import ZoneInfo

# ---------------------------------------------------------------------------
# 1.  Generic wait-condition enum
# ---------------------------------------------------------------------------
class OrderWait(Enum):
    """
    How long should we wait after placing an order?
    """
    NONE        = auto()   # return immediately
    SUBMITTED   = auto()   # wait until TWS/Gateway says 'Submitted'
    FILLED      = auto()   # wait until the order is fully filled
    COMPLETE    = auto()   # wait until Filled *or* Cancelled


# ---------------------------------------------------------------------------
# 2.  Helper that turns a Trade into an asyncio awaitable
# ---------------------------------------------------------------------------
async def _wait_trade(
    trade,
    until: OrderWait = OrderWait.SUBMITTED,
    timeout: float | None = None
) -> None:
    """
    Block the caller until *trade* reaches the required state.
    Raises asyncio.TimeoutError if *timeout* (seconds) elapses.
    """
    if until is OrderWait.NONE:
        return  # nothing to wait for

    # ------------------------------------------------------------------
    # Which underlying IB status values satisfy the wait condition?
    # ------------------------------------------------------------------
    targets: set[str]
    if until is OrderWait.SUBMITTED:
        # Any state past PendingSubmit/PendingCancel is fine
        targets = {
            OrderStatus.PreSubmitted,
            OrderStatus.Submitted,
            OrderStatus.Filled,
            OrderStatus.Cancelled,
        }
    elif until is OrderWait.FILLED:
        targets = {OrderStatus.Filled}
    else:  # COMPLETE
        targets = {OrderStatus.Filled, OrderStatus.Cancelled}

    # Fast-path: already there
    if trade.orderStatus.status in targets:
        return

    done = asyncio.Event()

    # ib_async emits trade.statusEvent (same as ib_insync)
    def _on_status(tr):
        if tr is trade and tr.orderStatus.status in targets:
            done.set()

    trade.statusEvent += _on_status
    try:
        await asyncio.wait_for(done.wait(), timeout)
    finally:
        # Always detach the listener to avoid leaks
        trade.statusEvent -= _on_status


class IBKRBroker(IBroker):
    """
    IBKRBroker using the async IB API end to end.
    """
    def __init__(self, ib: IB, outside_rth: bool = True, default_exchange: str = "SMART"):
        """
        ib               – connected IB client
        outside_rth      – allow orders outside regular trading hours
        default_exchange – exchange/route tag for stock contracts (e.g. SMART, ISLAND)
        """
        self.ib = ib
        self.outside_rth = outside_rth
        self.default_exchange = default_exchange

    async def check_connection(self) -> bool:
        connected = self.ib.isConnected()
        print(f"Connected to IB: {connected}")
        if connected:
            try:
                server_time = await self.ib.reqCurrentTimeAsync()
                print(f"IB Server Time: {server_time}")
            except Exception as e:
                print(f"Error retrieving IB Server Time: {e}")
        return connected

  
      # ---------- contract factories -------------------------------------------------
    def _create_stock(self, symbol: str):
        """
        Build a stock Contract routed via the broker's default_exchange.
        """
        c = Stock(symbol, exchange=self.default_exchange, currency="USD")
        return c

    async def _create_future(self, symbol: str, lastTradeDateOrContractMonth: str):
        """
        Quickly build an NQ, ES, CL … future.
        """
        c = Contract()
        c.symbol      = symbol
        c.secType     = "FUT"
        c.exchange    = "CME"
        c.currency    = "USD"
        c.lastTradeDateOrContractMonth = lastTradeDateOrContractMonth
        qualified, = await self.ib.qualifyContractsAsync(c)
        return qualified
    
    async def _create_cont_future(self, symbol: str):
        """
        Quickly build an NQ, ES, CL … future.
        """
        c = Contract()
        c.symbol      = symbol
        c.secType     = "CONTFUT"
        c.exchange    = "CME"
        c.currency    = "USD"
        qualified, = await self.ib.qualifyContractsAsync(c)
        return qualified

    # ---------- order builders -----------------------------------------------------
    def _next_id(self) -> int:
        return self.ib.client.getReqId()

    async def _market_order(self, action: str, qty: float):
        o = MarketOrder(action, qty, outsideRth=self.outside_rth)
        o.orderId = self._next_id()
        return o

    # ---------- public API ---------------------------------------------------------
    async def market_order(
        self,
        action: str,
        params: OrderParams,
        *,
        wait: OrderWait = OrderWait.SUBMITTED,
        timeout: float | None = 30,
    ):
        """
        Place a market order and (optionally) await a status.

        Parameters
        ----------
        action      'BUY' or 'SELL'
        params      Your OrderParams dataclass
        wait        Choose how long to block the coroutine
        timeout     Seconds before raising asyncio.TimeoutError
        """
        # Decide which Contract object to use
        if isinstance(params.symbol, str):
            if params.symbol.startswith("/"):
                contract = await self._create_cont_future(params.symbol)
            else:
                contract = self._create_stock(params.symbol)
        else:
            contract = params.symbol  # already an IB Contract

        order = await self._market_order(action, params.quantity)
        print(f"Placing Market Order #{order.orderId}: {action} {params.quantity}")

        trade = self.ib.placeOrder(contract, order)   # returns Trade
        trade.orderId = order.orderId                 # convenience

        # wait here, if requested
        await _wait_trade(trade, until=wait, timeout=timeout)

        # Wrap the Trade object into a dict for compatibility with ExecutionOrchestrator
        filled_qty = getattr(trade.orderStatus, "filled", None)
        avg_price  = getattr(trade.orderStatus, "avgFillPrice", None)
        symbol     = params.symbol if isinstance(params.symbol, str) else getattr(trade.contract, "symbol", None)

        return {
            "order_id":  trade.orderId,
            "status":    trade.orderStatus.status,
            "symbol":    symbol,
            "quantity":  filled_qty,
            "side":      trade.order.action,
            "type":      "market",
            "avg_price": avg_price,
        }

    async def market_buy(self, params: OrderParams, **kw):
        return await self.market_order("BUY", params, **kw)

    async def market_sell(self, params: OrderParams, **kw):
        return await self.market_order("SELL", params, **kw)

    async def list_positions(self) -> list[Position]:
        raw_positions = self.ib.positions()
        result = []
        for p in raw_positions:
            result.append(
                Position(
                    symbol=getattr(p.contract, "symbol", None),
                    quantity=getattr(p, "position", 0),
                    average_cost=getattr(p, "avgCost", None),
                    entry_time=None,
                    trade_id=None,
                    market_value=None,
                )
            )
        return result


class IBKRBrokerExtended(IBKRBroker, IBrokerExtended):
    '''
    IBKRBroker with native Bracket & OCO support.
    '''
    async def supports_complex_orders(self) -> bool:
        return True

    async def bracket_order(
        self,
        params: BracketOrderParams,
        *,
        wait: OrderWait = OrderWait.FILLED,
        timeout: float | None = 30,
    ) -> Dict[str, Any]:
        '''
        Place a native IBKR bracket (OCO) order.

        Supports MARKET and LIMIT (via expect_price) parents.
        '''
        # Resolve contract
        if isinstance(params.symbol, str):
            if params.symbol.startswith('/'):
                contract = await self._create_cont_future(params.symbol)
            else:
                contract = self._create_stock(params.symbol)
        else:
            contract = params.symbol

        # Build parent order ------------------------------------------------
        parent_action = params.action.upper()
        opp_action    = 'SELL' if parent_action == 'BUY' else 'BUY'

        entry_type = params.entry_type.upper()
        if entry_type == 'MARKET':
            parent = MarketOrder(parent_action, params.quantity, outsideRth=self.outside_rth)
        elif entry_type == 'LIMIT':
            if params.expect_price is None:
                raise ValueError('Limit bracket orders require expect_price as the limit price')
            parent = LimitOrder(parent_action, params.quantity, params.expect_price, outsideRth=self.outside_rth)
        else:
            raise ValueError(f'Unsupported entry_type {params.entry_type!r}. Only MARKET or LIMIT are supported.')

        parent.orderId = self._next_id()

        if timeout is not None and timeout >= 5:          # IB rejects shorter
            parent.tif = "GTD"
            expiry = datetime.now(tz=timezone.utc) + timedelta(seconds=timeout)
            parent.goodTillDate = expiry.strftime("%Y%m%d %H:%M:%S")
        else:
            parent.tif = "IOC"                            # legacy default

        parent.transmit = True

        # ---- Prepare exit legs ------------------------------------------
        # Force exits to be valid outside RTH so they can fire pre-/post-market
        tp_order = (LimitOrder(opp_action, params.quantity, params.take_profit_price,
                            outsideRth=True)
                    if params.take_profit_price is not None else None)

            # ---- NEW: Stop-Limit instead of Stop ----------------------------
        if params.stop_loss_price is not None:
            # 1. Derive a default limit component if caller did not give one
            pct = getattr(params, "stop_limit_offset_pct", 0.25) or 0.25
            cushion = 1 + pct/100 if parent_action == 'BUY' else 1 - pct/100
            limit_price = round(params.stop_loss_price * cushion, 2)  # two-decimals stock

            sl_order = StopLimitOrder(
                action      = opp_action,
                totalQuantity = params.quantity,
                stopPrice   = params.stop_loss_price,   # ← trigger
                lmtPrice    = limit_price,              # ← worst acceptable fill
                outsideRth  = True
            )
        else:
            sl_order = None

        # ---- New: add an EOD-flatten leg -------------------------------
        #  * activates a few seconds before the primary exchange close
        #  * shares the same OCA group so the first filled order cancels others
        eod_flatten_secs_before_close = 180            # 3 minutes before close
        exchange_tz = ZoneInfo("America/New_York")    # set per-contract if needed
        now_et      = datetime.now(tz=exchange_tz)

        # NOTE: if you trade multiple exchanges, replace this helper with a
        #       table of official closes or read the schedule from IBKR's API.
        close_today = now_et.replace(hour=15, minute=55, second=0, microsecond=0)
        if now_et > close_today:                      # already after close → use next session
            close_today = close_today + timedelta(days=1)

        good_after = (close_today - timedelta(seconds=eod_flatten_secs_before_close)
                    ).astimezone(timezone.utc)

        eod_order              = MarketOrder(opp_action, params.quantity, outsideRth=True)
        eod_order.goodAfterTime = good_after.strftime("%Y%m%d %H:%M:%S")

        # ---- 3. Place FOK parent ---------------------------------------
        trade = self.ib.placeOrder(contract, parent)

        # ---- 4. Await status of FOK parent ------------------------------
        await _wait_trade(trade, until=wait, timeout=timeout)

        # ----------------------------------------------------------------
        if trade.orderStatus.status == OrderStatus.Filled and trade.remaining() == 0:
            oca = f"OCO_{parent.orderId}"

            for leg in (tp_order, sl_order, eod_order):
                if leg:
                    leg.orderId  = self._next_id()
                    leg.ocaGroup = oca
                    # only the *last* leg transmitted should set transmit=True
                    leg.transmit = False

            # last leg in list = transmit=True so the whole OCA becomes active
            for leg in filter(None, (tp_order, sl_order, eod_order)):
                leg.transmit = True  # this will be overridden until the final loop

            # send the exit legs
            for leg in filter(None, (tp_order, sl_order, eod_order)):
                self.ib.placeOrder(contract, leg)
        else:
            raise RuntimeError(f"FOK parent order {parent.orderId} did not fill. Aborting bracket order.")

        # ---- 6. Return concise summary ----------------------------------
        return {
            "parent_order_id": parent.orderId,
            "status": trade.orderStatus.status,
            "symbol": params.symbol if isinstance(params.symbol, str) else getattr(contract, "symbol", None),
            "quantity": params.quantity,
            "side": parent_action,
            "type": "FOK+OCO",
            "take_profit_id": getattr(tp_order, "orderId", None),
            "stop_loss_id":  getattr(sl_order, "orderId", None),
            "eod_flatten_id": eod_order.orderId,
        }

    # --------------------------------------
    # Trailing‑Stop Order (optional)
    # --------------------------------------
    async def trailing_stop_order(
        self,
        params: TrailingStopOrderParams,
        *,
        wait: OrderWait = OrderWait.COMPLETE,
        timeout: float | None = 30,
    ) -> Dict[str, Any]:  # type: ignore[override]
        """Place a native IBKR trailing‑stop order (simple wrapper)."""
        # Trailing stop implementation can be added here similarly
        raise NotImplementedError("Trailing‑stop support not yet implemented. PRs welcome!")
