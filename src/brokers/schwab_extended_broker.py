from __future__ import annotations

import asyncio
import logging
from typing import Any, Dict

from schwab.orders.common import Session, Duration

from .schwab_broker import <PERSON><PERSON><PERSON><PERSON><PERSON>r, _is_futures_symbol  # type: ignore
from .schwab_bracket_helpers import build_equity_bracket_ott_market
from .ibroker_extended import BracketOrderParams, IBrokerExtended

logger = logging.getLogger(__name__)

class SchwabExtendedBroker(SchwabBroker, IBrokerExtended):
    """Drop-in replacement that can place TRIGGER-OCO brackets in one shot."""

    # ------------------------------------------------------------------
    # Framework capability inquiry
    # ------------------------------------------------------------------
    async def supports_complex_orders(self) -> bool:  # type: ignore[override]
        """Return True because <PERSON><PERSON><PERSON> supports TRIGGER_OCO for equities."""
        return True

    # ------------------------------------------------------------------
    # New public method used by ExecutionOrchestrator
    # ------------------------------------------------------------------
    async def bracket_order(self, params: BracketOrderParams) -> Dict[str, Any]:  # type: ignore[override]
        """Place an **entry-MARKET → OCO(LIMIT, STOP)** bracket.

        Args:
            params: Populated by *BracketOrderParams.from_trade_signal()*.

        Returns:
            Minimal dict echoing Schwab's response (order id & status).
        """

        if _is_futures_symbol(params.symbol):
            raise NotImplementedError("Schwab API: futures bracket not supported")

        # -- ensure authentication -------------------------------------
        await self.connect()
        if not self.client:
            raise RuntimeError("Schwab client not connected after connect()")

        account_hash = await self._get_account_hash()

        # -- build Schwab order spec -----------------------------------
        side = "LONG" if params.action.upper() in ("BUY", "LONG") else "SHORT"
        builder = build_equity_bracket_ott_market(
            symbol=params.symbol,
            qty=params.quantity,
            side=side,
            take_profit_px=params.take_profit_price or 0.0,
            stop_px=params.stop_loss_price or 0.0,
            session=Session.NORMAL,
            duration=Duration.DAY,
        )

        order_spec = builder.build()
        logger.debug("Submitting Schwab bracket: %s", order_spec)

        response = await asyncio.to_thread(
            self.client.place_order,
            account_hash=account_hash,
            order_spec=order_spec,
        )

        logger.info(
            "Bracket order response: status=%s, headers=%s, text=%s",
            response.status_code,
            response.headers,
            response.text,
        )

        if response.status_code not in (200, 201):
            raise RuntimeError(f"Bracket order failed: {response.text}")

        # ── extract order id ───────────────────────────────────────────────
        order_id: str | None = None

        # 1️⃣ If a body exists, use it.
        if response.headers.get("content-length", "0") != "0":
            try:
                order_id = response.json().get("orderId")
            except ValueError:  # empty/invalid JSON
                pass

        # 2️⃣ Otherwise parse the Location header:
        if not order_id:
            loc = response.headers.get("Location") or response.headers.get("location")
            if loc:
                order_id = loc.rsplit("/", 1)[-1]

        return {
            "status": "submitted",
            "code": response.status_code,
            "order_id": order_id,
        }

    # Schwab currently doesn't expose native trailing-stop after entry; raise explicit.
    async def trailing_stop_order(self, params, **kw):  # type: ignore[override]
        raise NotImplementedError("Schwab API: trailing-stop order not supported")

# EOF 