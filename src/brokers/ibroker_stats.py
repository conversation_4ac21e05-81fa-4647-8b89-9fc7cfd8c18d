import abc
import pandas as pd
from tools.clock import Clock
from marketdata.imarketdata import IMarketData


class IBrokerStats(abc.ABC):
    """
    Interface for broker statistics functionality.
    Provides methods necessary for statistical analysis and reporting.
    Any broker implementation that wants to work with stats loggers
    should implement this interface.
    """
    
    @property
    @abc.abstractmethod
    def initial_capital(self) -> float:
        """
        Returns the initial capital used for the broker.
        
        Returns:
            float: The initial capital amount
        """
        pass
    
    @property
    @abc.abstractmethod
    def cash(self) -> float:
        """
        Returns the current cash balance.
        
        Returns:
            float: The current cash amount
        """
        pass
    
    @property
    @abc.abstractmethod
    def closed_trades(self) -> list:
        """
        Returns the list of closed trade records.
        
        Returns:
            list: List of closed trade records
        """
        pass
    
    @property
    @abc.abstractmethod
    def clock(self) -> Clock:
        """
        Returns the clock instance used by the broker.
        
        Returns:
            Clock: The clock instance
        """
        pass
    
    @property
    @abc.abstractmethod
    def market_data(self) -> IMarketData:
        """
        Returns the market data provider used by the broker.
        
        Returns:
            IMarketData: The market data provider
        """
        pass
    
    @property
    @abc.abstractmethod
    def positions(self) -> dict:
        """
        Returns the current positions dictionary.
        
        Returns:
            dict: Dictionary of positions
        """
        pass
    
    @abc.abstractmethod
    async def list_positions(self) -> list:
        """
        Lists all current open positions with additional details.
        
        Returns:
            list: List of position details
        """
        pass
    
    @abc.abstractmethod
    async def get_trades_dataframe(self) -> pd.DataFrame:
        """
        Generate a DataFrame of closed trades for use with TradeStats.
        
        Returns:
            pd.DataFrame: DataFrame containing trade information
        """
        pass 