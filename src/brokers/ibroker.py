from abc import ABC, abstractmethod
from datetime import datetime
from dataclasses import dataclass, field
from typing import Optional, List
import pytz

EST = pytz.timezone('US/Eastern')

@dataclass
class OrderParams:
    """
    Contains parameters for market order execution.
    
    Attributes:
        symbol (str): The ticker symbol.
        quantity (float): Order quantity.
        expect_price (Optional[float]): The price around which the order is expected to be filled.
                                       Optional field.
        position_intent (str): Intent of the order - "open" or "close".
        datetime (datetime): Date and time of the order in EST timezone.
    """
    symbol: str
    quantity: float
    position_intent: str
    datetime: datetime
    expect_price: Optional[float] = None
    timeout: float = 60.0  # seconds to wait for fill before caller gives up

    def validate(self):
        """
        Validates that all required parameters are present and valid.
        
        Raises:
            ValueError: If any parameter is missing or invalid.
        """
        if not self.symbol:
            raise ValueError("Symbol is required")
        if self.quantity is None:
            raise ValueError("Quantity is required")
        if self.position_intent not in ["open", "close"]:
            raise ValueError("Position intent must be 'open' or 'close'")
        if not isinstance(self.datetime, datetime):
            raise ValueError("Datetime must be a valid datetime object")
            
        # Verify timezone is EST
        if self.datetime.tzinfo is None:
            raise ValueError("Datetime must have timezone information")
        if str(self.datetime.tzinfo) != str(EST):
            raise ValueError("Datetime must be in US/Eastern timezone")
        
        if self.timeout <= 0 or self.timeout > 600:
            raise ValueError("Timeout must be greater than 0 and less than 600 seconds")
        
class OrderParamsBuilder:
    """
    Builder for creating OrderParams instances in a fluent, step-by-step manner.
    """
    
    def __init__(self):
        self._symbol: Optional[str] = None
        self._quantity: Optional[float] = None
        self._expect_price: Optional[float] = None
        self._position_intent: Optional[str] = None
        self._datetime: Optional[datetime] = None
        self._timeout: float = 60.0
    
    def with_symbol(self, symbol: str) -> 'OrderParamsBuilder':
        """Set the ticker symbol."""
        self._symbol = symbol
        return self
    
    def with_quantity(self, quantity: float) -> 'OrderParamsBuilder':
        """Set the order quantity."""
        self._quantity = quantity
        return self
    
    def with_expect_price(self, expect_price: float) -> 'OrderParamsBuilder':
        """Set the expected price."""
        self._expect_price = expect_price
        return self
    
    def with_position_intent(self, position_intent: str) -> 'OrderParamsBuilder':
        """Set the position intent ('open' or 'close')."""
        self._position_intent = position_intent
        return self
    
    def with_datetime(self, dt: datetime) -> 'OrderParamsBuilder':
        """
        Set the order datetime.
        
        Args:
            dt (datetime): A datetime object. If no timezone is provided, 
                         it will be assumed to be in EST timezone.
        """
        # If datetime has no timezone, localize it to EST
        if dt.tzinfo is None:
            dt = EST.localize(dt)
        # If datetime is in another timezone, convert to EST
        elif str(dt.tzinfo) != str(EST):
            dt = dt.astimezone(EST)
            
        self._datetime = dt
        return self
    
    def with_current_datetime(self) -> 'OrderParamsBuilder':
        """Set the datetime to the current time in EST timezone."""
        self._datetime = datetime.now(EST)
        return self

    def with_timeout(self, timeout: float) -> 'OrderParamsBuilder':
        """Set a timeout (seconds) for waiting on order fill."""
        self._timeout = timeout
        return self
    
    def build(self) -> OrderParams:
        """
        Build and validate the OrderParams object.
        
        Returns:
            OrderParams: A validated OrderParams instance.
            
        Raises:
            ValueError: If any required parameter is missing or invalid.
        """
        params = OrderParams(
            symbol=self._symbol,
            quantity=self._quantity,
            expect_price=self._expect_price,
            position_intent=self._position_intent,
            datetime=self._datetime,
            timeout=self._timeout
        )
        params.validate()
        return params


@dataclass
class ShortLocateResponse:
    """
    Represents the result of a short locate request.

    Attributes:
        success (bool): True if the locate was successfully secured, False otherwise.
        symbol (str): The symbol for which the locate was requested.
        requested_quantity (float): The quantity requested for locate.
        located_quantity (float): The quantity successfully located (might be less than requested).
        locate_id (Optional[str]): A unique identifier for the locate, if provided by the broker.
        message (Optional[str]): Additional information or reason for failure.
        timestamp (datetime): Timestamp of when the locate response was generated.
    """
    success: bool
    symbol: str
    requested_quantity: float
    located_quantity: float = 0.0 # Default to 0 if unsuccessful or not specified
    locate_id: Optional[str] = None
    message: Optional[str] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(EST))


@dataclass
class Position:
    """
    Standardized position type for IBroker.list_positions.

    Attributes:
        symbol (str):         Ticker symbol.
        quantity (float):     Shares/contracts held
                              (positive = long, negative = short).
        average_cost (Optional[float]):
                              Average entry price per share/contract.
        entry_time (Optional[datetime]):
                              Timestamp when the position was opened.
        trade_id (Optional[str]):
                              Unique identifier for the trade.
        market_value (Optional[float]):
                              Current market value (quantity * current_price).
        current_price (Optional[float]):
                               Current price from market data.
        realized (Optional[float]):
                               Realized PnL aggregated for this symbol from closed trades.
        unrealized (Optional[float]):
                               Unrealized PnL for this open position.
        total_traded_value (Optional[float]):
                               Total dollar notional traded for this symbol (sum of exit_price * quantity for closed trades).
    """
    symbol: str
    quantity: float
    average_cost: Optional[float] = None
    entry_time: Optional[datetime] = None
    trade_id: Optional[str]   = None
    market_value: Optional[float] = None
    current_price: Optional[float] = None
    realized: Optional[float] = None
    unrealized: Optional[float] = None
    total_traded_value: Optional[float] = None


@dataclass
class Order:
    """Standardized order type for broker order tracking.

    Reflects latest status updates from broker messages.
    """
    order_id: str
    token: int | None = None
    symbol: str = ""
    side: str = ""
    order_type: str = ""
    qty: float | None = None
    open_qty: float | None = None
    filled_qty: float | None = None
    cxl_qty: float | None = None
    price: float | None = None
    route: str = ""
    status: str = ""
    time: str = ""
    last_action: Optional[str] = None
    raw_order: Optional[str] = None
    raw_action: Optional[str] = None


class IBroker(ABC):
    """
    IBroker defines the common asynchronous methods for brokers.
    Both IBKRBroker and AlpacaBroker will implement this interface.
    """

    @abstractmethod
    async def check_connection(self):
        """
        Checks if the broker connection is active.
        Returns:
            True if connected, False otherwise.
        """
        pass

    @abstractmethod
    async def market_buy(self, params: OrderParams):
        """
        Convenience method to place a market buy order.
        
        Parameters:
            params (OrderParams): Order parameters containing symbol, quantity, 
                                 expected price, position intent, and datetime.
        """
        pass

    @abstractmethod
    async def market_sell(self, params: OrderParams):
        """
        Convenience method to place a market sell order.
        
        Parameters:
            params (OrderParams): Order parameters containing symbol, quantity, 
                                 expected price, position intent, and datetime.
        """
        pass

    @abstractmethod
    async def list_positions(self) -> List[Position]:
        """
        Retrieves all current open positions.

        Returns:
            A list of Position objects.
        """
        pass
    
    
    async def pre_allocate_short_locate(self, params: OrderParams):
        """
        Requests a pre-allocation of shares for shorting (locate).

        NOTE: This method is optional and may not be supported by all brokers.
        Concrete broker implementations supporting this feature MUST override this method.
        If not supported, calling this method will raise NotImplementedError by default.

        Parameters:
            params (OrderParams): Order parameters containing symbol and quantity.

        Returns:
            ShortLocateResponse: An object detailing the success or failure of the locate request.

        """
        return ShortLocateResponse(
                success=True,
                symbol=params.symbol,
                requested_quantity=params.quantity,
                located_quantity=params.quantity,
                message="Default broker logic, always return success"
            )