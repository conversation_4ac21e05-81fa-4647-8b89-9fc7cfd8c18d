"""Typed message objects for DAS CMD API lines.

Each concrete subclass is responsible for:
1. Parsing a raw line into structured fields (`parse` classmethod).
2. Mutating broker shared state via `apply(state)`.

This keeps all knowledge about a specific line-type in one place and
lets the high-level parser simply route the line to the right class.
"""

from __future__ import annotations

import abc
import asyncio
import logging
from dataclasses import dataclass, field
from typing import Any, ClassVar, Dict, List, Protocol
from brokers.ibroker import Order  # type: ignore

logger = logging.getLogger(__name__)


class _StateProtocol(Protocol):
    """Structural subset of the DasBroker _State dataclass.

    This avoids a hard import cycle and makes unit-testing easier – any
    object that exposes these attributes will do.
    """

    id_to_token: Dict[str, int]
    pending_tokens: Dict[int, "asyncio.Future[str]"]
    positions: Dict[str, Dict[str, Any]]  # new – symbol → position snapshot
    trades: List[Dict[str, Any]]  # new – chronological list of trades
    orders: Dict[int, "Order"]  # new – token → Order snapshot


class BaseMsg(abc.ABC):
    """Abstract base for all DAS message objects."""

    #: Line prefix that uniquely identifies this message type.
    prefix: ClassVar[str]

    # ------------------------------------------------------------------
    # Factory helpers
    # ------------------------------------------------------------------
    @classmethod
    def matches(cls, line: str) -> bool:  # noqa: D401 – verb is okay
        """Return *True* if *line* looks like this message type."""
        return line.startswith(cls.prefix)

    # ------------------------------------------------------------------
    # Required overrides
    # ------------------------------------------------------------------
    @classmethod
    @abc.abstractmethod
    def parse(cls, line: str) -> "BaseMsg":  # noqa: D401 – verb is okay
        """Parse *line* into a new message instance or raise *ValueError*."""

    @abc.abstractmethod
    async def apply(self, state: _StateProtocol) -> None:  # noqa: D401
        """Apply side-effects on shared *state* (in-place mutation allowed)."""


# ----------------------------------------------------------------------
# Concrete message classes
# ----------------------------------------------------------------------

@dataclass(slots=True)
class OrderLine(BaseMsg):
    """Parsed ``%ORDER`` line."""

    prefix: ClassVar[str] = "%ORDER"

    order_id: str
    token: int
    symbol: str
    side: str
    order_type: str
    qty: str
    open_qty: str
    cxl_qty: str
    price: str
    route: str
    status: str
    time: str
    raw: str = field(repr=False)

    # ------------------------------------------------------------------
    # Parsing
    # ------------------------------------------------------------------
    @classmethod
    def parse(cls, line: str) -> "OrderLine":  # type: ignore[override]
        parts: List[str] = line.split()
        if len(parts) < 13:
            raise ValueError("Malformed %ORDER line – expected ≥13 fields")

        order_id = parts[1]
        try:
            token_int = int(parts[2])
        except ValueError as exc:
            raise ValueError("Token field is not an int in %ORDER line") from exc

        c = cls(
            order_id=order_id,
            token=token_int,
            symbol=parts[3],
            side=parts[4],
            order_type=parts[5],
            qty=parts[6],
            open_qty=parts[7],
            cxl_qty=parts[8],
            price=parts[9],
            route=parts[10],
            status=parts[11],
            time=parts[12],
            raw=line,
        )
        logger.debug("Parsed ORDER line: %s", c)
        return c

    # ------------------------------------------------------------------
    # Side-effects
    # ------------------------------------------------------------------
    async def apply(self, state: _StateProtocol) -> None:  # type: ignore[override]
        """Update cached order status + fulfil any pending token Future."""

        filled_qty_calc = ""
        try:
            diff = float(self.qty) - float(self.open_qty)
            filled_qty_calc = str(int(diff)) if diff.is_integer() else str(diff)
        except (ValueError, TypeError):
            pass

        status: Dict[str, Any] = {
            "order_id": self.order_id,
            "token": self.token,
            "symbol": self.symbol,
            "side": self.side,
            "type": self.order_type,
            "qty": self.qty,
            "open_qty": self.open_qty,
            "filled_qty": filled_qty_calc,
            "cxl_qty": self.cxl_qty,
            "price": self.price,
            "route": self.route,
            "status": self.status,
            "time": self.time,
        }

        state.id_to_token[self.order_id] = self.token

        # Update standardized Order object map
        order_obj = state.orders.get(self.token)
        if order_obj is None:
            order_obj = Order(order_id=self.order_id)
        def _to_float(val: str | None):
            try:
                return float(val) if val is not None else None
            except (TypeError, ValueError):
                return None
        order_obj.token = self.token
        order_obj.symbol = self.symbol
        order_obj.side = self.side
        order_obj.order_type = self.order_type
        order_obj.qty = _to_float(self.qty)
        order_obj.open_qty = _to_float(self.open_qty)
        order_obj.cxl_qty = _to_float(self.cxl_qty)
        order_obj.price = _to_float(self.price)
        if order_obj.qty is not None and order_obj.open_qty is not None:
            order_obj.filled_qty = order_obj.qty - order_obj.open_qty
        order_obj.route = self.route
        order_obj.status = self.status
        order_obj.time = self.time
        order_obj.raw_order = self.raw
        state.orders[self.token] = order_obj

        fut = state.pending_tokens.pop(self.token, None)
        if fut and not fut.done():
            fut.set_result(self.order_id)


@dataclass(slots=True)
class OrderActLine(BaseMsg):
    """Parsed ``%OrderAct`` line."""

    prefix: ClassVar[str] = "%OrderAct"

    order_id: str
    action: str
    token: int | None
    raw: str = field(repr=False)

    @classmethod
    def parse(cls, line: str) -> "OrderActLine":  # type: ignore[override]
        # %OrderAct 17451 Accept Buy AAPL 1 200 SMRTL 08:00:57  6532
        parts = line.split()
        if len(parts) < 3:
            raise ValueError("Malformed %OrderAct line – expected ≥3 fields")
        # Token is specified as the last column per docs, but may be missing
        token_val: int | None = None
        if parts[-1].isdigit():
            try:
                token_val = int(parts[-1])
            except ValueError:
                token_val = None
        c = cls(order_id=parts[1], action=parts[2], token=token_val, raw=line)
        logger.debug("Parsed OrderAct line: %s", c)
        return c

    async def apply(self, state: _StateProtocol) -> None:  # type: ignore[override]
        # Resolve token – use parsed token
        token_key: int | None = self.token
        if token_key is None:
            # Cannot associate action with order – skip
            logger.warning("Cannot associate action with order %s", self.order_id)
            return
        order_obj = state.orders.get(token_key)
        if order_obj is None:
            from brokers.ibroker import Order
            order_obj = Order(order_id=self.order_id, token=token_key)
            
        if order_obj.order_id != self.order_id:
            logger.warning("Order ID mismatch: %s != %s", order_obj.order_id, self.order_id)
            order_obj.order_id = self.order_id # Override order_id with the one from the action
            state.id_to_token[self.order_id] = token_key
        order_obj.last_action = self.action
        order_obj.raw_action = self.raw
        state.orders[token_key] = order_obj


@dataclass(slots=True)
class PosLine(BaseMsg):
    """Parsed ``%POS`` line representing current open position for a symbol."""

    prefix: ClassVar[str] = "%POS"

    symbol: str
    long_qty: str
    short_qty: str
    avg_price: str
    raw: str = field(repr=False)

    @classmethod
    def parse(cls, line: str) -> "PosLine":  # type: ignore[override]
        parts = line.split()
        if len(parts) < 6:
            raise ValueError("Malformed %POS line – expected ≥6 fields")
        # According to manual: %POS SYMBOL longQty shortQty avgPrice ...
        return cls(symbol=parts[1], long_qty=parts[2], short_qty=parts[3], avg_price=parts[4], raw=line)

    async def apply(self, state: _StateProtocol) -> None:  # type: ignore[override]
        state.positions[self.symbol] = {
            "symbol": self.symbol,
            "long_qty": self.long_qty,
            "short_qty": self.short_qty,
            "avg_price": self.avg_price,
            "raw": self.raw,
        }


@dataclass(slots=True)
class TradeLine(BaseMsg):
    """Parsed ``%TRADE`` execution line."""

    prefix: ClassVar[str] = "%TRADE"

    trade_id: str
    symbol: str
    side: str
    qty: str
    price: str
    route: str
    time: str
    order_id: str
    raw: str = field(repr=False)

    @classmethod
    def parse(cls, line: str) -> "TradeLine":  # type: ignore[override]
        parts = line.split()
        if len(parts) < 9:
            raise ValueError("Malformed %TRADE line – expected ≥9 fields")
        return cls(
            trade_id=parts[1],
            symbol=parts[2],
            side=parts[3],
            qty=parts[4],
            price=parts[5],
            route=parts[6],
            time=parts[7],
            order_id=parts[8],
            raw=line,
        )

    async def apply(self, state: _StateProtocol) -> None:  # type: ignore[override]
        trade_dict = {
            "trade_id": self.trade_id,
            "symbol": self.symbol,
            "side": self.side,
            "qty": self.qty,
            "price": self.price,
            "route": self.route,
            "time": self.time,
            "order_id": self.order_id,
            "raw": self.raw,
        }
        state.trades.append(trade_dict)

        # Update standardized Order snapshot keyed by token
        token_key = state.id_to_token.get(self.order_id)
        if token_key is None:
            # Cannot link trade to existing order – bail out gracefully
            return
        order_obj = state.orders.get(token_key)
        if order_obj is None:
            from brokers.ibroker import Order
            order_obj = Order(order_id=self.order_id, token=token_key)
        order_obj.filled_qty = float(self.qty)
        order_obj.status = "Executed"
        state.orders[token_key] = order_obj




@dataclass(slots=True)
class SLRetLine(BaseMsg):
    """Parsed ``%SLRET`` locate return line."""

    prefix: ClassVar[str] = "%SLRET"

    ret_type: str
    symbol: str
    offer_price: str
    offer_size: str
    notes: str
    raw: str = field(repr=False)

    # ------------------------------------------------------------------
    # Parsing
    # ------------------------------------------------------------------
    @classmethod
    def parse(cls, line: str) -> "SLRetLine":  # type: ignore[override]
        parts = line.split()
        if len(parts) < 5:
            raise ValueError("Malformed %SLRET line – expected ≥5 fields")
        ret_type = parts[1]
        symbol = parts[2]
        offer_price = parts[3]
        offer_size = parts[4]
        notes = " ".join(parts[5:]) if len(parts) > 5 else ""        
        c = cls(
            ret_type=ret_type,
            symbol=symbol.upper(),
            offer_price=offer_price,
            offer_size=offer_size,
            notes=notes,
            raw=line,
        )
        logger.debug("Parsed SLRET line: %s", c)
        return c

    # ------------------------------------------------------------------
    # Side-effects
    # ------------------------------------------------------------------
    async def apply(self, state):  # type: ignore[override]
        """Delegate handling to the provided *state* (LocateManager)."""
        from brokers.das_locate_manager import DasLocateManager
        if isinstance(state, DasLocateManager):
            await state._process_sl_ret_msg(self)

@dataclass(slots=True)
class SLOrderLine(BaseMsg):
    """Parsed ``%SLOrder`` locate line."""

    prefix: ClassVar[str] = "%SLOrder"

    loc_id: str
    status: str
    symbol: str
    qty: str
    price: str
    token: str | None  # DAS token we supplied during SLNEWORDER
    raw: str = field(repr=False)

    # ------------------------------------------------------------------
    # Parsing
    # ------------------------------------------------------------------
    @classmethod
    def parse(cls, line: str) -> "SLOrderLine":  # type: ignore[override]
        parts = line.split()
        # Expected format per DAS docs:
        # %SLOrder <loc_id> <symbol> <shares> <open_shares> <exe_shares> <exe_price> <status> <route> <time> <lmtPrice> <token> [notes...]
        # %SLOrder 13171 NAMM 13 0 13 0.0499 Located LOCATE1 09:11:44 0 43208459 Accepted!
        if len(parts) < 12:
            raise ValueError("Malformed %SLOrder line – expected ≥12 fields")

        loc_id = parts[1]
        symbol = parts[2]
        qty_str = parts[3]  # total shares requested
        price_str = parts[6]  # execution price (0 until located)
        status = parts[7]
        token_str: str | None = parts[11] if len(parts) > 11 else None

        return cls(
            loc_id=loc_id,
            status=status,
            symbol=symbol.upper(),
            qty=qty_str,
            price=price_str,
            token=token_str,
            raw=line,
        )

    # ------------------------------------------------------------------
    # Side-effects
    # ------------------------------------------------------------------
    async def apply(self, state):  # type: ignore[override]
        """Delegate handling to the provided *state* (LocateManager)."""
        # Defer import to avoid circular dependency
        from brokers.das_locate_manager import DasLocateManager
        if isinstance(state, DasLocateManager):
            await state._process_sl_order_msg(self)
        else:
            # Unknown state object – ignore gracefully
            pass

# Registry – extend as new message types are added
MESSAGE_TYPES: List[type[BaseMsg]] = [
    OrderLine,
    OrderActLine,
    PosLine,
    TradeLine,
    # Note: SLRetLine intentionally excluded from generic parser – handled by DasLocateManager

]
