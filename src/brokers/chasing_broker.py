from __future__ import annotations

import asyncio
import logging
import time
from typing import Optional

from brokers.iraworderbroker import IRawOrderBroker
from brokers.ibroker import <PERSON><PERSON><PERSON>r, OrderParams, Position
from marketdata.imarketdata import IMarketData

_logger = logging.getLogger(__name__)


class ChasingBroker(IBroker):
    """Smart limit-chasing broker built on top of a raw order broker."""

    def __init__(
        self,
        delegate: IRawOrderBroker,
        data: IMarketData,
        *,
        step: float = 0.02,   # $ hop size
        timeout: float = 20.0,  # overall life-time
        clip_pct: float = 0.25,  # % of NBBO spread we are willing to pay up
    ) -> None:
        self._delegate = delegate
        self._data = data
        self._step = step
        self._timeout = timeout
        self._clip_pct = clip_pct

    # ------------------------------------------------------------------
    # IBroker passthroughs / helpers
    async def check_connection(self):
        return await self._delegate.check_connection()

    async def list_positions(self) -> list[Position]:  # type: ignore[override]
        return await self._delegate.list_positions()

    async def pre_allocate_short_locate(self, params: OrderParams):  # type: ignore[override]
        return await self._delegate.pre_allocate_short_locate(params)

    # ------------------------------------------------------------------
    async def market_buy(self, params: OrderParams):
        oid = await self._submit_chasing_order("buy", params)
        return await self._delegate._wait_for_terminal(oid, params.timeout)

    async def market_sell(self, params: OrderParams):
        side = "sell" if params.position_intent != "open" else "sell-short"
        oid = await self._submit_chasing_order(side, params)
        return await self._delegate._wait_for_terminal(oid, params.timeout)

    # ------------------------------------------------------------------
    async def _submit_chasing_order(self, side: str, params: OrderParams) -> str:
        # Determine initial reference price (expect_price or NBBO mid)
        price = params.expect_price
        if price is None:
            quote = await self._data.get_quote_async(params.symbol)
            bid = getattr(quote, "bid_price", getattr(quote, "bid", None))
            ask = getattr(quote, "ask_price", getattr(quote, "ask", None))
            if bid is not None and ask is not None:
                price = (bid + ask) / 2
            else:
                price = ask or bid  # fall back to best available
        # Build params clone for delegate
        cloned = OrderParams(
            symbol=params.symbol,
            quantity=params.quantity,
            expect_price=price,
            position_intent=params.position_intent,
            datetime=params.datetime,
            timeout=params.timeout,
        )
        oid = await self._delegate.submit_order(side=side, params=cloned, order_type="limit")
        # Chasing loop
        await self._chase_loop(oid, side, cloned)
        return oid

    async def _chase_loop(self, oid: str, side: str, params: OrderParams):
        start = time.monotonic()
        last_price = params.expect_price or 0.0
        while True:
            # Check status without REST
            st = await self._delegate.check_order_status(oid)
            if st:
                status = getattr(st, "status", "").lower()
                state_attr = ""
                if status in {"filled", "cancelled", "rejected"} or state_attr in {"closed", "filled"}:
                    return  # done
            if (time.monotonic() - start) > self._timeout:
                _logger.info("Chasing timeout reached – cancelling order %s", oid)
                await self._delegate.cancel_order(oid)
                return
            # Get fresh quote
            quote = await self._data.get_quote_async(params.symbol)
            bid = getattr(quote, "bid_price", getattr(quote, "bid", None))
            ask = getattr(quote, "ask_price", getattr(quote, "ask", None))
            if bid is None or ask is None:
                await asyncio.sleep(0.3)
                continue
            spread = ask - bid
            if spread <= 0:
                await asyncio.sleep(0.3)
                continue

            if side == "buy":
                target_price = min(bid + spread * self._clip_pct, ask)
                new_price = min(last_price + self._step, target_price)
            else:  # sell / sell-short
                target_price = max(ask - spread * self._clip_pct, bid)
                new_price = max(last_price - self._step, target_price)

            # Only attempt amend if price changed meaningfully
            if abs(new_price - last_price) >= 0.005:  # 0.5 cent threshold
                try:
                    await self._delegate.patch_order(oid, price=new_price)
                    _logger.debug("Patched order %s to %.2f", oid, new_price)
                except NotImplementedError:
                    _logger.debug("Patch unsupported – cancelling & resubmitting")
                    await self._delegate.cancel_order(oid)
                    cloned = OrderParams(
                        symbol=params.symbol,
                        quantity=params.quantity,
                        expect_price=new_price,
                        position_intent=params.position_intent,
                        datetime=params.datetime,
                        timeout=params.timeout,
                    )
                    oid = await self._delegate.submit_order(side=side, params=cloned, order_type="limit")
                last_price = new_price
            await asyncio.sleep(0.3)

    # Fallback delegation for any other attributes
    def __getattr__(self, item):
        return getattr(self._delegate, item)