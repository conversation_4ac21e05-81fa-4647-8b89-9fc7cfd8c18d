from __future__ import annotations

import abc
import asyncio
import itertools
import random  # Added for random starting token
import logging
from contextlib import asynccontextmanager
from typing import Dict, Tuple, Optional

logger = logging.getLogger(__name__)


class LocateFailedError(Exception):
    """Raised when a locate cannot be secured within the timeout window."""
    pass


class BaseLocateManager(abc.ABC):
    """Broker‑agnostic locate inventory + concurrency logic."""

    def __init__(self, *, 
                 max_fee_pct: float,
                 locate_budget_limit: float) -> None:
        """Initialize base locate manager.

        Parameters
        ----------
        max_fee_pct:
            Maximum acceptable locate fee expressed as *price ÷ expect_price*.
            Offers more expensive than this ratio are rejected.
        locate_budget_limit:
            Optional daily budget in USD. When the running total of accepted
            locate costs exceeds this limit, subsequent locate requests are
            rejected.
        """
        # {symbol: {"available": float, "used": float}}
        self._inventory: Dict[str, Dict[str, float]] = {}
        self._inv_lock = asyncio.Lock()
        # Fee / budget controls
        self._max_fee_pct = max_fee_pct
        self._locate_budget_limit = locate_budget_limit
        self._locate_budget_spent = 0.0

    # ------------------------------------------------------------------
    # Public API
    async def ensure_locate(
        self,
        symbol: str,
        qty: float,
        expect_price: Optional[float] = None,
        timeout: float = 30.0,
        route: Optional[str] = None,
    ):
        """Block until *qty* shares are reserved for *symbol* or raise error."""
        symbol = symbol.upper()
        async with self._inv_lock:
            inv = self._inventory.setdefault(symbol, {"available": 0.0, "used": 0.0})
            free = inv["available"] - inv["used"]
            if free >= qty:
                inv["used"] += qty
                return

            need = qty - free

        # Resolve route (default fallback)
        route = route or getattr(self, "_default_route", None)

        # Submit new locate request outside the lock
        locate_id, fut = await self._submit_locate_request(symbol, need, expect_price, route)

        try:
            filled = await asyncio.wait_for(fut, timeout=timeout)
        except Exception:
            # propagate LocateFailedError / Timeout
            raise

        # After fill, re‑enter critical section to update counters
        async with self._inv_lock:
            inv = self._inventory.setdefault(symbol, {"available": 0.0, "used": 0.0})
            free = inv["available"] - inv["used"]
            if free < need:
                # Even after fill we still don't have enough
                raise LocateFailedError(f"Locate fill insufficient: needed {need}, got {filled}")
            inv["used"] += qty  # reserve caller's entire request

    async def release_locate(self, symbol: str, qty: float):
        """Return *qty* shares back to the available pool."""
        symbol = symbol.upper()
        async with self._inv_lock:
            inv = self._inventory.get(symbol)
            if not inv:
                logger.warning("release_locate on unknown symbol %s", symbol)
                return
            inv["used"] = max(inv["used"] - qty, 0.0)

    @asynccontextmanager
    async def with_locates(
        self,
        symbol: str,
        qty: float,
        expect_price: Optional[float] = None,
        timeout: float = 30.0,
        route: Optional[str] = None,
    ):
        await self.ensure_locate(symbol, qty, expect_price, timeout, route)
        try:
            yield
        finally:
            await self.release_locate(symbol, qty)

    # ------------------------------------------------------------------
    # Offer evaluation helpers
    def _should_accept_offer(
        self,
        qty: float,
        offer_price: float,
        expect_price: Optional[float] = None,
    ) -> bool:
        """Decide whether to accept an offered locate.

        The default implementation tests two conditions:
        1. Daily budget – the cumulative cost after acceptance must not exceed
           *locate_budget_limit* (if configured).
        2. Minimum fee percentage – if *expect_price* was provided by the
           caller, the ratio *offer_price / expect_price* must be **above**
           *min_fee_pct*.

        Sub-classes can override this method for custom logic.
        """
        cost = offer_price * qty
        # Budget guard
        if (
            self._locate_budget_limit is not None
            and self._locate_budget_spent + cost > self._locate_budget_limit
        ):
            return False

        # Fee guard – reject when fee exceeds cap
        if expect_price and expect_price > 0:
            fee_pct = offer_price / expect_price
            if fee_pct > self._max_fee_pct:
                return False
        return True

    def _record_locate_cost(self, cost: float) -> None:
        """Update budget spent."""
        self._locate_budget_spent += cost

    # ------------------------------------------------------------------
    # Internal helpers
    @abc.abstractmethod
    async def _submit_locate_request(
        self,
        symbol: str,
        qty: float,
        expect_price: Optional[float],
        route: str,
    ) -> Tuple[str, asyncio.Future]:
        """Kick off broker‑specific locate request and return (id, future).

        The returned *future* **must** resolve with the filled quantity or
        raise *LocateFailedError*.
        """

class DasLocateManager(BaseLocateManager):
    """Locate manager that talks to DAS Trader CMD API."""

    def __init__(self, broker, *, 
                 default_route: str = "LOCATE1", 
                 max_fee_pct: float = 0.0, # 0.01% max fee cap TODO: Fix this
                 locate_budget_limit: float = 50.0) -> None:
        """
        Parameters
        ----------
        broker :
            An instance of :class:`DasBroker` already connected.  We rely on its
            ``inbound_queue`` for `%SLOrder` updates and on its internal
            ``_send_line`` helper to transmit locate commands.
        """
        super().__init__(max_fee_pct=max_fee_pct, locate_budget_limit=locate_budget_limit)
        self._broker = broker
        self._default_route = default_route
        # Start token counter from a random offset to minimize id collisions after restarts
        self._token_counter = itertools.count(random.randint(1_000_000, 2**31 - 1))
        # loc_id → (symbol, qty, future, expect_price)
        self._pending: Dict[str, Tuple[str, float, asyncio.Future, Optional[float]]] = {}
        # Background task that listens for SLOrder updates
        self._reader_task = asyncio.create_task(self._reader_loop(), name="das-locate-reader")

    # ------------------------------------------------------------------
    # Base overrides
    async def _submit_locate_request(
        self,
        symbol: str,
        qty: float,
        expect_price: Optional[float],
        route: str,
    ) -> Tuple[str, asyncio.Future]:
        token = next(self._token_counter)
        # NOTE: The official CMD API docs call this *SLNEWORDER*.
        # For simplicity we omit advanced fields such as route or price.
        cmd = f"SLNEWORDER {symbol} {int(qty)} {route} {token}"
        fut: asyncio.Future = asyncio.get_event_loop().create_future()
        self._pending[str(token)] = (symbol, qty, fut, expect_price)

        # DasBroker currently exposes `_send_line`; ideally this should be
        # promoted to a public helper (TODO).
        await self._broker._send_line(cmd)  # pylint: disable=protected-access
        logger.debug("Sent locate request: %s", cmd)

        return str(token), fut

    # ------------------------------------------------------------------
    # Background reader
    async def _reader_loop(self):
        from brokers.das_messages import SLOrderLine, SLRetLine  # local import to avoid circular
        while True:
            line = await self._broker.inbound_queue.get()
            if SLOrderLine.matches(line):
                try:
                    msg = SLOrderLine.parse(line)
                except ValueError:
                    logger.debug("Malformed %%SLOrder line: %s", line)
                    continue
                await msg.apply(self)
            elif SLRetLine.matches(line):
                try:
                    msg = SLRetLine.parse(line)
                except ValueError:
                    logger.debug("Malformed %%SLRET line: %s", line)
                    continue
                await msg.apply(self)

    # ------------------------------------------------------------------
    # Re-defined message processor (clean version overriding earlier)
    async def _process_sl_order_msg(self, msg):  # noqa: C901 – keep logic self-contained
        """Handle a parsed :class:`SLOrderLine` object coming from DAS."""
        loc_id = msg.loc_id  # DAS internal locate ID
        token_key = str(msg.token) if msg.token is not None else None
        status = msg.status.lower()

        filled_qty = 0.0
        offer_price = 0.0
        try:
            filled_qty = float(msg.qty)
        except (TypeError, ValueError):
            pass
        try:
            offer_price = float(msg.price)
        except (TypeError, ValueError):
            pass
        symbol = msg.symbol.upper()

        if token_key is None:
            logger.debug("SLOrder line without token – ignoring: %s", msg.raw)
            return

        pending = self._pending.get(token_key)
        if not pending:
            return  # Locate not ours / already handled

        _, _req_qty, fut, expect_price = pending

        # ----------------------- immediate success -----------------------
        if status in {"located", "executed", "accepted"} and filled_qty > 0:
            await self._mark_locate_success(token_key, symbol, filled_qty, offer_price, fut)
            return

        # ----------------------------- failure ---------------------------
        if status in {"rejected", "denied", "cancelled"}:
            await self._mark_locate_failure(token_key, status, fut)
            return

        # ----------------------------- offered ---------------------------
        if status == "offered":
            accept_offer = self._should_accept_offer(filled_qty, offer_price, expect_price)
            op = "Accept" if accept_offer else "Reject"
            cmd = f"SLOFFEROPERATION {loc_id} {op}"
            await self._broker._send_line(cmd)  # pylint: disable=protected-access
            logger.debug("Sent %s for locate offer %s (price=%.4f)", op.lower(), loc_id, offer_price)
            if not accept_offer:
                await self._mark_locate_failure(token_key, "offer rejected", fut)
            return

    # ------------------------------------------------------------------
    # Helpers
    async def _process_sl_ret_msg(self, msg):
        """Handle a parsed :class:`SLRetLine` coming from DAS.

        Success cases:
        1. ``ret_type == 1`` **and** ``offer_size > 0`` – shares are available at the quoted price.
        2. ``ret_type == 2`` **and** message indicates *Already Shortable* – no locate required.

        Any other response is treated as a failure.
        """
        try:
            ret_type = int(msg.ret_type)
        except (TypeError, ValueError):
            ret_type = None

        symbol = msg.symbol.upper()
        note_lower = (msg.notes or "").lower()

        # ------------------------------------------------------------------
        # Numeric helpers
        # ------------------------------------------------------------------
        def _to_float(val):
            try:
                return float(val) if val is not None else 0.0
            except (TypeError, ValueError):
                return 0.0

        offer_price = _to_float(msg.offer_price)
        offer_size = _to_float(msg.offer_size)

        # Recognise the "Already Shortable" hint exactly like before
        is_already_shortable = "already shortable" in note_lower or (
            (msg.offer_price in {"0", "0.0"}) and (msg.offer_size in {"0", "0.0"})
        )

        is_success = (ret_type == 1 and offer_size > 0) or (ret_type == 2 and is_already_shortable)

        # ------------------------------------------------------------------
        # Resolve any pending locate for this symbol (first match only)
        # ------------------------------------------------------------------
        for token_key, (sym, req_qty, fut, _exp_price) in list(self._pending.items()):
            if sym.upper() != symbol:
                continue

            if is_success:
                filled_qty = min(req_qty, offer_size) if ret_type == 1 else req_qty
                await self._mark_locate_success(token_key, symbol, filled_qty, offer_price, fut)
            else:
                reason = msg.notes or f"SLRET {ret_type} – locate failed"
                await self._mark_locate_failure(token_key, reason, fut)
            break  # Handle only the first matching request

    async def _mark_locate_success(self, loc_id: str, symbol: str, filled_qty: float, price: float, fut: asyncio.Future):
        async with self._inv_lock:
            inv = self._inventory.setdefault(symbol, {"available": 0.0, "used": 0.0})
            inv["available"] += filled_qty
        # Track budget spend
        self._record_locate_cost(price * filled_qty)
        if not fut.done():
            fut.set_result(filled_qty)
        self._pending.pop(loc_id, None)
        logger.info("Locate filled: %s %s shares", symbol, filled_qty)

    async def _mark_locate_failure(self, loc_id: str, reason: str, fut: asyncio.Future):
        if not fut.done():
            fut.set_exception(LocateFailedError(f"DAS locate failed: {reason}"))
        self._pending.pop(loc_id, None)
        logger.warning("Locate %s failed: %s", loc_id, reason)

    async def _auto_accept_offer(self, loc_id: str):
        """Accept a type‑1 locate offer automatically."""
        cmd = f"SLOFFEROPERATION {loc_id} Accept"
        await self._broker._send_line(cmd)  # pylint: disable=protected-access
        logger.debug("Sent auto‑accept for locate offer %s", loc_id)
