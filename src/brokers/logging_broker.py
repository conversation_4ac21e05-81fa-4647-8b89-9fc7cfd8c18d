from .ibroker import <PERSON><PERSON><PERSON>r, OrderParams
from .ibroker_stats import IBrokerStats
from dataclasses import asdict
from marketdata.imarketdata import LastQuote
import logging
import os
import asyncio
import json
from .local_broker import LocalBroker
from log_utils import create_order_logger

class LoggingBroker(IBroker, IBrokerStats):
    """
    A broker implementation that wraps another broker (delegate) and logs every method call.
    Useful for debugging and development; all actions are logged using the logging module.
    """
    def __init__(self, broker: IBroker, logger=None, order_log_prefix: str = "orders"):
        """
        Initializes the LoggingBroker with a delegate broker.

        Parameters:
            broker (IBroker): An instance of a broker that implements the IBroker interface.
            logger (logging.Logger, optional): A logger instance. If None, a default logger will be created.
            order_log_prefix (str): Prefix for the order log filename.
        """
        # Ensure we only wrap a LocalBroker implementation
        assert isinstance(broker, LocalBroker), "LoggingBroker requires a LocalBroker instance"
        self.broker = broker
        self.logger = logger or logging.getLogger(__name__)
        self.order_logger = create_order_logger(order_log_prefix)

    async def check_connection(self):
        self.logger.info("Checking broker connection...")
        status = await self.broker.check_connection()
        self.logger.info(f"Connection status: {status}")
        return status

    async def market_buy(self, params: OrderParams):
        self.logger.info(f"Placing market BUY order: params={params}")
        # Start quote retrieval in parallel
        quote_task = asyncio.create_task(self.market_data.get_quote_async(params.symbol))
        # Place the actual buy
        result = await self.broker.market_buy(params)
        self.logger.info(f"Market BUY order result: {result}")
        # Fire-and-forget the combined logging
        asyncio.create_task(self._background_log_order("BUY", params, result, quote_task))
        return result

    async def market_sell(self, params: OrderParams):
        self.logger.info(f"Placing market SELL order: params={params}")
        quote_task = asyncio.create_task(self.market_data.get_quote_async(params.symbol))
        result = await self.broker.market_sell(params)
        self.logger.info(f"Market SELL order result: {result}")
        asyncio.create_task(self._background_log_order("SELL", params, result, quote_task))
        return result

    async def list_positions(self, *args, **kwargs):
        self.logger.info(f"Retrieving list of positions... args={args}, kwargs={kwargs}")
        positions = await self.broker.list_positions(*args, **kwargs)
        self.logger.info(f"Current Positions: {positions}")
        return positions

    def __getattr__(self, name):
        """
        Proxy any unrecognized attributes or methods to the delegate broker.
        """
        return getattr(self.broker, name)
        
    @property
    def initial_capital(self):
        self.logger.debug("Accessing initial_capital from delegate broker")
        return self.broker.initial_capital
        
    @property
    def cash(self):
        self.logger.debug("Accessing cash from delegate broker")
        return self.broker.cash
        
    @property
    def closed_trades(self):
        self.logger.debug("Accessing closed_trades from delegate broker")
        return self.broker.closed_trades
        
    @property
    def clock(self):
        self.logger.debug("Accessing clock from delegate broker")
        return self.broker.clock
        
    @property
    def market_data(self):
        self.logger.debug("Accessing market_data from delegate broker")
        return self.broker.market_data
        
    @property
    def positions(self):
        self.logger.debug("Accessing positions from delegate broker")
        return self.broker.positions
        
    async def get_trades_dataframe(self):
        self.logger.info("Getting trades dataframe from delegate broker")
        trades_df = await self.broker.get_trades_dataframe()
        self.logger.info(f"Retrieved trades dataframe with {len(trades_df)} rows")
        return trades_df

    async def _background_log_order(
        self, side: str, params: OrderParams, result, quote_task: asyncio.Task
    ):
        """Background task: await quote & log both order + quote."""
        try:
            last_quote = await quote_task
        except Exception as e:
            self.logger.warning(f"Background quote fetch failed: {e}")
            last_quote = None

        # --- First line: dump all data as JSON ---
        full_data = {
            "side": side,
            "params": {
                "symbol": params.symbol,
                "quantity": params.quantity,
                "expect_price": params.expect_price,
                "position_intent": params.position_intent,
                "datetime": params.datetime.isoformat() if hasattr(params.datetime, "isoformat") else str(params.datetime),
            },
            "result": result,
            "last_quote": None
        }
        if last_quote is not None:
            # always a LastQuote instance
            full_data["last_quote"] = asdict(last_quote)
        # ensure any Timestamp/datetime/etc. is stringified
        self.order_logger.info(json.dumps(full_data, default=str))

        # --- Second line: computed metrics ---
        metrics = {}
        expected_price = params.expect_price
        metrics["expected_price"] = expected_price
        avg_price = None
        if isinstance(result, dict):
            avg_price = result.get("avg_price")
        if avg_price is not None:
            metrics["avg_price"] = avg_price

        # compute midpoint using LastQuote
        if last_quote is not None and last_quote.bid_price is not None and last_quote.ask_price is not None:
            midpoint = (last_quote.bid_price + last_quote.ask_price) / 2
            metrics["midpoint"] = midpoint
            if avg_price is not None and midpoint:
                metrics["pct_diff_mid_avg"] = (avg_price - midpoint) / midpoint * 100

        # slippage vs expected price
        if avg_price is not None and expected_price not in (None, 0):
            metrics["pct_diff_avg_expected"] = (avg_price - expected_price) / expected_price * 100

        # also safe-guard metric dumps
        self.order_logger.info(json.dumps(metrics, default=str))

    # (order-logger creation is now delegated to log_utils.create_order_logger) 