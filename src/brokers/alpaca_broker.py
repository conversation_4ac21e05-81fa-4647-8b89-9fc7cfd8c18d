import os
import asyncio
from alpaca.trading.client import TradingClient
from alpaca.trading.requests import MarketOrderRequest, LimitOrderRequest
from alpaca.trading.enums import OrderSide, TimeInForce, PositionIntent
from dotenv import load_dotenv
from .ibroker import IBroker, OrderParams, Position
from tools.clock import is_regular_hours, Clock
import logging
from typing import List

logger = logging.getLogger(__name__)
load_dotenv()

class AlpacaBroker(IBroker):
    """
    AlpacaBroker provides simple methods for placing orders using Alpaca's TradingClient.
    It mirrors the interface of IBKRBroker, supporting:
      - check_connection
      - market_order (or limit order in extended hours)
      - market_buy
      - market_sell
      - list_positions
      
    If the API credentials are not passed via arguments, they are loaded from the environment
    variables ALPACA_KEY and ALPACA_SECRET.
    
    Note:
      When trading during extended hours (self.extended_hours is True), market orders are not supported.
      Instead, a limit order will be submitted using the provided expect_price.
      
    References:
      Alpaca API Documentation: https://alpaca.markets/sdks/python/trading.html
    """
    def __init__(self, api_key: str = None, secret_key: str = None, paper: bool = True, extended_hours: bool = False):
        # Load credentials from environment if not provided.
        if api_key is None:
            api_key = os.getenv("ALPACA_KEY")
        if secret_key is None:
            secret_key = os.getenv("ALPACA_SECRET")
        if not api_key or not secret_key:
            raise ValueError("Alpaca API credentials are required. Provide them as parameters or define ALPACA_KEY and ALPACA_SECRET in your environment.")

        self.trading_client = TradingClient(api_key, secret_key, paper=paper)
        # Flag indicating whether orders should be eligible for extended-hours trading.
        self.extended_hours = extended_hours
        self.clock = Clock()
    
    async def check_connection(self):
        """
        Checks if the connection to Alpaca is active by fetching account details.
        
        Returns:
            True if connection is successful; False otherwise.
        """
        try:
            account = await asyncio.to_thread(self.trading_client.get_account)
            logger.info("Connected to Alpaca. Account details:")
            logger.info(f"ID: {account.id}, Status: {account.status}, Buying Power: {account.buying_power}")
            return True
        except Exception as e:
            logger.error("Error checking Alpaca connection:", e)
            return False
    
    async def market_order(self, action, params: OrderParams):
        """
        Place an order for the given symbol.
        
        In normal trading hours, a market order is submitted. However, if self.extended_hours is True,
        market orders are not supported outside of normal trading hours. In that case, a limit order is submitted.
        
        Parameters:
            action (str): "BUY" or "SELL".
            params (OrderParams): Order parameters containing symbol, quantity, expected price, etc.
            
        Returns:
            The order object returned by Alpaca's submit_order method.
        """
        if action.upper() == "BUY":
            order_side = OrderSide.BUY
        elif action.upper() == "SELL":
            order_side = OrderSide.SELL
        else:
            raise ValueError("Invalid action. Use 'BUY' or 'SELL'.")

        symbol = params.symbol
        quantity = params.quantity
        expect_price = params.expect_price
        position_intent = params.position_intent

        # Determine alpaca_position_intent based on position_intent and action
        alpaca_position_intent = None
        if position_intent:
            if position_intent.lower() == "open" and action.upper() == "BUY":
                alpaca_position_intent = PositionIntent.BUY_TO_OPEN
            elif position_intent.lower() == "close" and action.upper() == "BUY":
                alpaca_position_intent = PositionIntent.BUY_TO_CLOSE
            elif position_intent.lower() == "open" and action.upper() == "SELL":
                alpaca_position_intent = PositionIntent.SELL_TO_OPEN
            elif position_intent.lower() == "close" and action.upper() == "SELL":
                alpaca_position_intent = PositionIntent.SELL_TO_CLOSE
            else:
                raise ValueError("Invalid position_intent. Use 'open' or 'close'.")

        # Use limit order if we're outside regular hours and extended_hours is enabled
        if self.extended_hours:
            if expect_price is None:
                raise ValueError("For extended hours trading, an expected price (limit price) must be provided.")
            
            # Round the price to 2 decimal places to avoid sub-penny pricing errors
            rounded_price = round(float(expect_price), 2)
            
            order_request = LimitOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=order_side,
                limit_price=rounded_price,
                time_in_force=TimeInForce.DAY,
                position_intent=alpaca_position_intent  # Using the correct PositionIntent enum
            )
            logger.info(f"Placing Extended Hours Limit Order: {action.upper()} {quantity} shares of {symbol} at limit price {rounded_price} (original: {expect_price}){' with position_intent: ' + position_intent if position_intent else ''}")
        else:
            order_request = MarketOrderRequest(
                symbol=symbol,
                qty=quantity,
                side=order_side,
                time_in_force=TimeInForce.DAY,
                extended_hours=False,
                position_intent=alpaca_position_intent  # Using the correct PositionIntent enum
            )
            logger.info(f"Placing Market Order: {action.upper()} {quantity} shares of {symbol} with extended_hours={self.extended_hours}{' and position_intent: ' + position_intent if position_intent else ''}")

        order = await asyncio.to_thread(self.trading_client.submit_order, order_data=order_request)
        return order
    
    async def market_buy(self, params: OrderParams):
        """
        Convenience method to place an order to buy.
        In extended hours, a limit order is submitted using the provided expect_price.
        
        Parameters:
            params (OrderParams): Order parameters object containing all required fields.
        """
        return await self.market_order("BUY", params)

    async def market_sell(self, params: OrderParams):
        """
        Convenience method to place an order to sell.
        In extended hours, a limit order is submitted using the provided expect_price.
        
        Parameters:
            params (OrderParams): Order parameters object containing all required fields.
        """
        return await self.market_order("SELL", params)

    async def list_positions(self) -> List[Position]:
        """
        Retrieve a list of current positions in the account.
        
        Returns:
            A list of Position objects.
        """
        raw_positions = await asyncio.to_thread(self.trading_client.get_all_positions)
        positions_out: List[Position] = []
        for p in raw_positions:
            # qty comes back as a string
            try:
                qty = float(p.qty)
            except:
                qty = p.qty

            avg_cost = None
            if getattr(p, 'avg_entry_price', None) is not None:
                avg_cost = float(p.avg_entry_price)

            market_value = None
            if getattr(p, 'market_value', None) is not None:
                market_value = float(p.market_value)

            positions_out.append(Position(
                symbol=p.symbol,
                quantity=qty,
                average_cost=avg_cost,
                market_value=market_value
            ))
        return positions_out 