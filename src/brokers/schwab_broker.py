import os
import asyncio
from schwab.auth import easy_client
from schwab.orders.common import Duration, Session
from schwab.orders.equities import (
    equity_buy_market, equity_buy_limit, equity_sell_market, equity_sell_limit,
    equity_sell_short_market, equity_sell_short_limit,
    equity_buy_to_cover_market, equity_buy_to_cover_limit
)
from schwab.client import Client
from .ibroker import IBroker, OrderParams, Position
import logging
from typing import List
from tools.clock import is_regular_hours

logger = logging.getLogger(__name__)

def _is_futures_symbol(symbol: str) -> bool:
    # StreetSmart format:  /ROOT + month code + 1-digit year, e.g. /MNQM5
    return symbol.startswith('/')

# ------------------------------------------------------------------
# Helper: Schwab price precision rules
# ------------------------------------------------------------------

def _format_price(px: float) -> str:
    """Return price as string: 2 decimals >= $1, 4 decimals otherwise."""
    if px >= 1:
        return format(round(px, 2), ".2f")
    return format(round(px, 4), ".4f")


class SchwabBroker(IBroker):
    """
    Schwab broker implementation using schwab-py library.
    """
    
    def __init__(self, 
                 api_key=None, 
                 app_secret=None, 
                 callback_url=None, 
                 token_path=None):
        """
        Initialize the Schwab broker.
        
        Parameters:
            api_key (str): Your Schwab application's app key. Defaults to SCHWAB_API_KEY env var.
            app_secret (str): Application secret provided upon app approval. Defaults to SCHWAB_APP_SECRET env var.
            callback_url (str): Your application's callback URL. Defaults to SCHWAB_CALLBACK_URL env var or "https://127.0.0.1:8182".
            token_path (str): Path to store the authentication token. Defaults to SCHWAB_TOKEN_PATH env var or "./schwab_token.json".
        """
        self.api_key = api_key or os.environ.get("SCHWAB_API_KEY")
        self.app_secret = app_secret or os.environ.get("SCHWAB_APP_SECRET")
        self.callback_url = callback_url or os.environ.get("SCHWAB_CALLBACK_URL", "https://127.0.0.1:8182")
        self.token_path = token_path or os.environ.get("SCHWAB_TOKEN_PATH", "./schwab_token.json")
        self.client = None
        
        if not self.api_key or not self.app_secret:
            raise ValueError("API key and app secret must be provided either directly or through environment variables")
        
        
    async def connect(self):
        """
        Connect to Schwab API.
        """
        # if we already have a client, don't re-run easy_client()
        if self.client is not None:
            return True
        # Using asyncio.to_thread to run the blocking authentication in a separate thread
        self.client = await asyncio.to_thread(
            easy_client,
            api_key=self.api_key,
            app_secret=self.app_secret,
            callback_url=self.callback_url,
            token_path=self.token_path
        )
        return self.client is not None

    # ─── New helper ────────────────────────────────────────────────────────────
    async def get_http_client(self) -> Client:
        """
        Ensure we're connected and return the underlying Schwab HTTP client.
        Raises RuntimeError if the connection fails.
        """
        success = await self.connect()
        if not success or self.client is None:
            raise RuntimeError("Failed to connect to Schwab API")
        return self.client
    # ───────────────────────────────────────────────────────────────────────────
        
    async def check_connection(self):
        """
        Checks if the broker connection is active.
        Returns:
            True if connected, False otherwise.
        """
        if not self.client:
            return False
            
        try:
            # Make a simple API call to verify connection
            resp = await asyncio.to_thread(self.client.get_user_principals)
            return resp.status_code == 200
        except Exception:
            return False
            
    async def market_buy(self, params: OrderParams):
        """
        Place a market buy order for an equity.
        
        Parameters:
            params (OrderParams): Order parameters containing symbol, quantity, expected price, position intent, and datetime.
        """
        
        if _is_futures_symbol(params.symbol):
            raise NotImplementedError("Schwab API: futures order entry not yet supported")
        if not self.client:
            raise RuntimeError("Not connected to Schwab API")
    
        try:
            # Pick market vs limit (extended hours) based on the timestamp in params
            order_time       = params.datetime
            symbol           = params.symbol
            quantity         = params.quantity
            position_intent  = params.position_intent
            expect_price     = params.expect_price

            if is_regular_hours(order_time):
                # Regular hours
                if expect_price is not None:
                    # Use LIMIT order at expected price
                    logger.info(
                        f"Placing regular-hours LIMIT buy for {symbol} at {expect_price}"
                    )
                    if position_intent == "close":
                        order_builder = equity_buy_to_cover_limit(symbol, quantity, _format_price(expect_price))
                    else:
                        order_builder = equity_buy_limit(symbol, quantity, _format_price(expect_price))
                    session = Session.NORMAL
                else:
                    # Market order
                    if position_intent == "close":
                        logger.info(f"Using buy_to_cover MARKET for {symbol} based on position_intent={position_intent}")
                        order_builder = equity_buy_to_cover_market(symbol, quantity)
                    else:
                        order_builder = equity_buy_market(symbol, quantity)
                    session = Session.NORMAL
            else:
                # Extended hours → must use a limit order
                if expect_price is None:
                    raise ValueError("Limit price (expect_price) is required for orders outside regular hours")
                logger.info(f"Placing extended-hours limit buy for {symbol} at {expect_price}")
                if position_intent == "close":
                    order_builder = equity_buy_to_cover_limit(symbol, quantity, _format_price(expect_price))
                else:
                    order_builder = equity_buy_limit(symbol, quantity, _format_price(expect_price))
                session = Session.SEAMLESS

            # Apply common settings
            order_builder = order_builder \
                .set_duration(Duration.DAY) \
                .set_session(session)
            
            # Submit the order
            response = await asyncio.to_thread(
                self.client.place_order,
                account_hash=await self._get_account_hash(),
                order_spec=order_builder.build()
            )
                        
            # Log the response
            logger.info(
                "Response from placing market buy order: status=%s, headers=%s, text=%s",
                response.status_code,
                response.headers,
                response.text,
            )

            # Both 200 and 201 are success codes
            if response.status_code not in (200, 201):
                raise ValueError(
                    f"Order failed with status {response.status_code}: {response.text}"
                )

            # ── extract order id ────────────────────────────────────────────
            order_id: str | None = None

            # 1️⃣ If a body exists, check JSON for orderId
            if response.headers.get("content-length", "0") != "0":
                try:
                    order_id = response.json().get("orderId")
                except ValueError:
                    pass  # ignore invalid/empty JSON

            # 2️⃣ Fallback to Location header
            if not order_id:
                loc = response.headers.get("Location") or response.headers.get("location")
                if loc:
                    order_id = loc.rsplit("/", 1)[-1]

            return {"status": "success", "code": response.status_code, "order_id": order_id}
            
        except Exception as e:
            raise ValueError(f"Error placing market buy order: {str(e)}")
        
    async def market_sell(self, params: OrderParams):
        """
        Place a market sell order for an equity.
        
        Parameters:
            params (OrderParams): Order parameters containing symbol, quantity, expected price, position intent, and datetime.
        """        
        if _is_futures_symbol(params.symbol):
              raise NotImplementedError("Schwab API: futures order entry not yet supported")
        if not self.client:
            raise RuntimeError("Not connected to Schwab API")
            
        try:
            # Pick market vs limit (extended hours) based on the timestamp in params
            order_time       = params.datetime
            symbol           = params.symbol
            quantity         = params.quantity
            position_intent  = params.position_intent
            expect_price     = params.expect_price

            if is_regular_hours(order_time):
                # Regular hours
                if expect_price is not None:
                    # Use LIMIT order at expected price
                    logger.info(
                        f"Using LIMIT sell for {symbol} at {expect_price} (ticks) based on position_intent={position_intent}"
                    )
                    if position_intent == "open":
                        order_builder = equity_sell_short_limit(symbol, quantity, _format_price(expect_price))
                    else:
                        order_builder = equity_sell_limit(symbol, quantity, _format_price(expect_price))
                    session = Session.NORMAL
                else:
                    # Market order
                    if position_intent == "open":
                        logger.info(
                            f"Using sell_short MARKET for {symbol} based on position_intent={position_intent}"
                        )
                        order_builder = equity_sell_short_market(symbol, quantity)
                    else:
                        order_builder = equity_sell_market(symbol, quantity)
                    session = Session.NORMAL
            else:
                # Extended hours → must use a limit order
                if expect_price is None:
                    raise ValueError("Limit price (expect_price) is required for orders outside regular hours")
                logger.info(f"Placing extended-hours limit sell for {symbol} at {expect_price}")
                if position_intent == "open":
                    order_builder = equity_sell_short_limit(symbol, quantity, _format_price(expect_price))
                else:
                    order_builder = equity_sell_limit(symbol, quantity, _format_price(expect_price))
                session = Session.SEAMLESS

            # Apply common settings
            order_builder = order_builder \
                .set_duration(Duration.DAY) \
                .set_session(session)
            
            # Submit the order
            response = await asyncio.to_thread(
                self.client.place_order,
                account_hash=await self._get_account_hash(),
                order_spec=order_builder.build()
            )
            
            # Log the response
            logger.info(
                "Response from placing market sell order: status=%s, headers=%s, text=%s",
                response.status_code,
                response.headers,
                response.text,
            )

            # Both 200 and 201 are success codes
            if response.status_code not in (200, 201):
                raise ValueError(
                    f"Order failed with status {response.status_code}: {response.text}"
                )

            # ── extract order id ────────────────────────────────────────────
            order_id: str | None = None

            # 1️⃣ If a body exists, check JSON for orderId
            if response.headers.get("content-length", "0") != "0":
                try:
                    order_id = response.json().get("orderId")
                except ValueError:
                    pass  # ignore invalid/empty JSON

            # 2️⃣ Fallback to Location header
            if not order_id:
                loc = response.headers.get("Location") or response.headers.get("location")
                if loc:
                    order_id = loc.rsplit("/", 1)[-1]

            return {"status": "success", "code": response.status_code, "order_id": order_id}
            
        except Exception as e:
            raise ValueError(f"Error placing market sell order: {str(e)}")
        
    async def list_positions(self) -> List[Position]:
        """
        Retrieves current positions.
        Returns:
            A list of positions.
        """
        if not self.client:
            raise RuntimeError("Not connected to Schwab API")
            
        account_hash = await self._get_account_hash()
        response = await asyncio.to_thread(
            self.client.get_account,
            account_hash=account_hash,
            fields=[Client.Account.Fields.POSITIONS]
        )
        
        data = response.json()
        positions_out: List[Position] = []
        if 'securitiesAccount' in data and 'positions' in data['securitiesAccount']:
            for pos in data['securitiesAccount']['positions']:
                symbol = pos['instrument']['symbol']
                if 'longQuantity' in pos:
                    quantity = float(pos['longQuantity'])
                else:
                    quantity = -float(pos.get('shortQuantity', 0))

                avg_cost = float(pos['averagePrice']) if pos.get('averagePrice') is not None else None
                market_value = float(pos['marketValue']) if pos.get('marketValue') is not None else None

                positions_out.append(Position(
                    symbol=symbol,
                    quantity=quantity,
                    average_cost=avg_cost,
                    market_value=market_value
                ))
        return positions_out
    
    async def _get_account_hash(self):
        """
        Helper method to get the primary account hash value.
        Account hashes are required by the Schwab API instead of raw account numbers.
        """
        if not self.client:
            raise RuntimeError("Not connected to Schwab API")
            
        response = await asyncio.to_thread(self.client.get_account_numbers)
        
        if response.status_code != 200:
            raise RuntimeError(f"Failed to get account numbers: {response.text}")
            
        data = response.json()
        
        # Get the first account's hash value
        if data and len(data) > 0 and 'hashValue' in data[0]:
            return data[0]['hashValue']
            
        raise RuntimeError("No account hash found") 