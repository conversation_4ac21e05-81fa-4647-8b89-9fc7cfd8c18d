import argparse
import asyncio
from datetime import datetime
import pytz
from brokers.alpaca_broker import AlpacaBroker
from brokers import LoggingBroker
from brokers.local_broker import LocalBroker
from brokers.ibroker import OrderParamsBuilder
from marketdata.theta_market_data import ThetaMarketData
from tools.clock import Clock

async def main_async(args):
    # Instantiate the broker based on the CLI argument:
    if args.broker == "local":
        clock = Clock()
        market_data = ThetaMarketData()
        broker = LocalBroker(market_data, initial_capital=100000, clock=clock)
        print("Using LocalBroker")
    else:
        broker = LoggingBroker(AlpacaBroker(paper=True, extended_hours=True))
        print("Using Production Broker (LoggingBroker wrapping AlpacaBroker)")
    
    # Currently we support only market orders.
    if args.order_type.lower() != "market":
        print("Unsupported order type. Only 'market' orders are supported.")
        return
    
    # Create OrderParams object using builder pattern
    est = pytz.timezone('US/Eastern')
    current_time = datetime.now(est)
    
    order_params = (OrderParamsBuilder()
        .with_symbol(args.symbol)
        .with_quantity(args.quantity)
        .with_expect_price(0)
        .with_position_intent("open")
        .with_datetime(current_time)
        .build())
    
    # Place the order based on order side.
    if args.side.lower() == "buy":
        print(f"Placing market buy for {args.quantity} shares of {args.symbol}")
        order = await broker.market_buy(order_params)
    elif args.side.lower() == "sell":
        print(f"Placing market sell for {args.quantity} shares of {args.symbol}")
        order = await broker.market_sell(order_params)
    else:
        print("Invalid order side; please use 'buy' or 'sell'.")
        return

    print("Order response:", order)

def main():
    parser = argparse.ArgumentParser(
        description="Simple CLI for testing brokers by placing a market order."
    )
    parser.add_argument(
        "--broker",
        type=str,
        choices=["local", "production"],
        default="production",
        help="Broker to test: choose 'local' for LocalBroker or 'production' for a real broker via LoggingBroker."
    )
    parser.add_argument("--symbol", type=str, required=True, help="Ticker symbol (e.g., 'AAPL').")
    parser.add_argument("--quantity", type=float, required=True, help="Quantity to trade.")
    parser.add_argument(
        "--side",
        type=str,
        choices=["buy", "sell"],
        required=True,
        help="Order side: 'buy' or 'sell'."
    )
    parser.add_argument(
        "--order_type",
        type=str,
        choices=["market"],
        default="market",
        help="Order type (currently only supports 'market' orders)."
    )
    
    args = parser.parse_args()
    asyncio.run(main_async(args))

if __name__ == "__main__":
    main() 