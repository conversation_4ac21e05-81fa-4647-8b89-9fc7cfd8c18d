"""
Thin re‑export layer so callers can write:

    from trading_framework import Strategy, BaseStrategy, DayContext, PositionMixin, ...

Nothing in here should import concrete strategies.
"""
from .core import Strategy, BaseStrategy
from .day_ctx import DayContext, PositionState
from .mixins import (
    StrategyMixin, DailyStrategyMixin, PositionMixin,
)
from .execution_feedback import ExecutionReport, ExecOutcome
from position_manager import PositionManager

__all__ = [
    "Strategy", "BaseStrategy", "PositionManager",
    "DayContext", "PositionState",
    "StrategyMixin", "DailyStrategyMixin", "PositionMixin",
    "ExecutionReport", "ExecOutcome",
] 