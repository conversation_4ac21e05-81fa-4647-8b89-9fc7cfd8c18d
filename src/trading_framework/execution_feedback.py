from __future__ import annotations
from dataclasses import dataclass
from datetime import datetime
from enum import Enum, auto
from typing import Any, Dict, Optional
from strategies.trade_signals import TradeSignal

class ExecOutcome(Enum):
    FILLED    = auto()   # completely filled
    PARTIAL   = auto()   # partial fill
    CANCELLED = auto()
    REJECTED  = auto()
    ERROR     = auto()   # network / unknown

@dataclass
class ExecutionReport:
    order_id:    str
    signal:      TradeSignal        # originating signal
    outcome:     ExecOutcome
    filled_qty:  float  = 0.0
    avg_price:   float  = 0.0
    broker_msg:  str    = ""
    metadata:    Dict[str, Any] | None = None 
    timestamp:   datetime | None = None 


class StrategyExitRequest(Exception):
    """Raised to request immediate termination of a strategy's processing loop."""
    pass