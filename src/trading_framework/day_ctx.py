from __future__ import annotations
from dataclasses import dataclass, field
from datetime import date, datetime
from typing import Any, Dict
import logging

from strategies.trade_signals import TradeSignal, SignalType, TradeType
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome 

logger = logging.getLogger(__name__)

@dataclass
class PositionState:
    side:        str   | None = None   # "LONG" / "SHORT"
    qty:         float | None = None
    entry_px:    float | None = None
    entry_ts:    datetime | None = None
    stop_px:     float | None = None
    target_px:   float | None = None

    def clear(self) -> None:
        self.__dict__.update(PositionState().__dict__)

@dataclass
class DayContext:
    today:   date | None = None
    pos:     PositionState = field(default_factory=PositionState)
    custom:  Dict[str, Any] = field(default_factory=dict)

    # ---------- helpers ----------
    def is_new_day(self, ts: datetime) -> bool:
        return self.today != ts.date()

    def roll(self, ts: datetime) -> None:
        self.today = ts.date()
        self.pos.clear()
        self.custom.clear() 
        
    def reset(self) -> None:
        """Reset position state and custom data without changing the date.
        Useful for error recovery to allow restarting the strategy."""
        # Log the current state before clearing
        pos_info = {k: v for k, v in self.pos.__dict__.items() if v is not None}
        custom_info = {k: v for k, v in self.custom.items()}
        
        logger.info(
            "Resetting DayContext: position=%s, custom=%s",
            pos_info,
            custom_info
        )
        
        # Clear the state
        self.pos.clear()
        self.custom.clear()

    # Called by PositionMixin.post_bar()
    def update_position_from_signal(self, signal: TradeSignal) -> None:
        if signal.signal == SignalType.OPEN:
            # initialize a new position
            self.pos.side     = "LONG" if signal.trade_type == TradeType.BUY else "SHORT"
            self.pos.qty      = signal.quantity
            self.pos.entry_px = signal.price
            self.pos.entry_ts = signal.source_bar.name  # timestamp
            # metadata keys used in ORBStrategy
            self.pos.stop_px   = signal.metadata.get("stop_price")
            self.pos.target_px = signal.metadata.get("target_price")
        elif signal.signal == SignalType.CLOSE:
            # fully closed
            self.pos.clear()

    # Called by PositionMixin.on_exec()
    def update_position_from_execution(self, report: ExecutionReport) -> None:
        sig = report.signal
        # if opening order filled, record actual fill
        if sig.signal == SignalType.OPEN and report.outcome == ExecOutcome.FILLED:
            self.pos.side     = "LONG" if sig.trade_type == TradeType.BUY else "SHORT"
            self.pos.qty      = report.filled_qty
            self.pos.entry_px = report.avg_price
            self.pos.entry_ts = report.timestamp
            # keep the same stops/targets from signal metadata
            self.pos.stop_px   = sig.metadata.get("stop_price")
            self.pos.target_px = sig.metadata.get("target_price")
        # if closing order fills (partial or full), clear
        elif sig.signal == SignalType.CLOSE and report.outcome in (
            ExecOutcome.FILLED, ExecOutcome.PARTIAL
        ):
            self.pos.clear()