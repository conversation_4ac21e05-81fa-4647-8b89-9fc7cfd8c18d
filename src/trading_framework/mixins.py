from __future__ import annotations
from typing import Optional, Sequence, Any, Dict, Tuple

import pandas as pd
from trading_framework.indicators import LazyIndicatorRegistry
from trading_framework.day_ctx import DayContext, PositionState
from trading_framework.execution_feedback import ExecutionReport
from strategies.trade_signals import TradeSignal
from .core import BaseStrategy, StrategyMixin
from trading_framework.indicators import OnDemandIndicatorMixin


BarSlice = Tuple[pd.DataFrame, pd.DataFrame]       # hist, latest


class IndicatorMixin(OnDemandIndicatorMixin, StrategyMixin):
    """Puts a LazyIndicatorRegistry at `ctx["ind"]` and keeps it updated."""

    def pre_bar(self, bars: BarSlice, ctx, **kwargs) -> bool:
        # unpack into your existing signature
        hist, latest = bars
        ticker = ctx.get("ticker")
        # TODO: We use self.preprocess_bar since its from older interface compatible mixin
        # call your preprocess_bar—returns False to skip, True to continue
        return self.preprocess_bar(hist, latest, ctx, ticker, metadata=None)
    

class DailyStrategyMixin(IndicatorMixin):
    """Automatically reset `DayContext` at the first bar of every session."""

    def initialize_context(self, ctx):
        super().initialize_context(ctx)
        ctx["day"] = DayContext()
    
    def pre_bar(self, bars: BarSlice, ctx, **_) -> bool:
        # First let the indicator mixin run
        if not super().pre_bar(bars, ctx):
            return False
            
        hist, latest = bars
        day_ctx = ctx["day"]
        
       # Check if we're in a new day
        if not latest.empty and day_ctx.is_new_day(latest.index[-1]):
            # Roll over to the new day (clears pos & custom)
            day_ctx.roll(latest.index[-1])
            
        return True

class PositionMixin(StrategyMixin):
    """Tracks position state using DayContext."""
    
    def initialize_context(self, ctx):
        # Make sure we have a day context
        if "day" not in ctx:
            ctx["day"] = DayContext()
    
    def pre_bar(self, bars: BarSlice, ctx, **_) -> bool:
        # No special pre-processing needed
        return True
    
    def post_bar(self, signals, bars: BarSlice, ctx, **_):
        if not signals:
            return signals
            
        # Update position based on signals
        day_ctx = ctx["day"]
        for signal in signals:
            day_ctx.update_position_from_signal(signal)
            
        return signals
        
    def pre_exec(self, report, ctx):
        # Update position based on execution report
        if report and ctx.get("day"):
            ctx["day"].update_position_from_execution(report)


    def in_pos(self, ctx) -> bool:
        return ctx["day"].pos.side is not None

    def open_pos(
        self,
        ctx,
        side: str,
        qty: float,
        entry_px: float,
        entry_ts: datetime,
        stop_px: float,
        target_px: float,
    ) -> None:
        """Seed PositionState in DayContext from a new trade signal."""
        day_ctx = ctx["day"]
        day_ctx.pos.side     = side
        day_ctx.pos.qty      = qty
        day_ctx.pos.entry_px = entry_px
        day_ctx.pos.entry_ts = entry_ts
        day_ctx.pos.stop_px  = stop_px
        day_ctx.pos.target_px= target_px

    def close_pos(self, ctx) -> None:
        """Clear PositionState in DayContext on exit."""
        ctx["day"].pos.clear()


class IntradayStrategy(DailyStrategyMixin, PositionMixin, BaseStrategy):
    """
    Base class for intraday strategies.

    Automatically sets up:
      • LazyIndicatorRegistry at ctx['ind']
      • DayContext at ctx['day'] and resets each new day
      • PositionState tracking with in_pos(), open_pos(), close_pos(), etc.

    Default prepare() uses 1-minute bars and 20-day lookback.
    """

    def __init__(self) -> None:
        super().__init__()
        self.add_mixin(self)

# Re‑export the original StrategyMixin so callers need just one import path.
__all__ = [
    "IntradayStrategy",
    "DailyStrategyMixin",
    "PositionMixin",
] 