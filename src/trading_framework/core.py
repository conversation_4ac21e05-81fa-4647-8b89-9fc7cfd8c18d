from __future__ import annotations
from abc import ABC, abstractmethod
from datetime import timedelta
from typing import Any, Optional, Sequence, Tuple

import pandas as pd
from trading_framework.execution_feedback import ExecutionReport
from strategies.trade_signals import TradeSignal

BarSlice = Tuple[pd.DataFrame, pd.DataFrame]          # (hist, latest)

# ----------------------------------------------------------------- #
#  Public Strategy interface                                         #
# ----------------------------------------------------------------- #

class Strategy(ABC):
    """Simplified strategy life‑cycle."""

    # ---------- life‑cycle ----------
    @abstractmethod
    def on_start(
        self,
        ticker: str,
        interval_hint: timedelta,
        metadata: dict[str, Any],
    ) -> tuple[timedelta, timedelta]:
        """Return (bar_interval, lookback_window)."""

    # ---------- market‑data ----------
    @abstractmethod
    def on_bar(
        self,
        bars: BarSlice,
    ) -> Optional[Sequence[TradeSignal]]:
        ...

    # ---------- broker feedback ----------
    def on_exec(
        self,
        report: ExecutionReport,
    ) -> Optional[Sequence[TradeSignal]]:
        return None

    # ---------- graceful shutdown ----------
    def on_stop(self) -> None: ...


class StrategyMixin:
    """Base class – override the hooks you need."""
    def initialize_context(self, ctx: Dict[str, Any]) -> None: ...
    def pre_bar(self, bars: BarSlice, ctx: Dict[str, Any]) -> bool: return True
    def post_bar(self, sigs,      bars: BarSlice, ctx: Dict[str, Any]):
        return sigs
    
    def pre_exec(self, rpt, ctx: Dict[str, Any]) -> None: ...


# ----------------------------------------------------------------- #
#  BaseStrategy with context & mixin plumbing                        #
# ----------------------------------------------------------------- #

class BaseStrategy(Strategy):
    """
    A ready‑to‑inherit implementation that gives you:
      • self.ctx     – plain dict you own
      • mixin hooks  – pre_bar / post_bar / on_exec
    """

    # ---------------- constructor ----------------
    def __init__(self) -> None:
        self.ctx: dict[str, Any] = {}
        self.mixins: list[StrategyMixin] = []

    # ---------------- mixin mgmt -----------------
    def add_mixin(self, mixin: StrategyMixin) -> "BaseStrategy":
        self.mixins.append(mixin)
        return self

    # ---------------- Strategy surface -----------

    # (1) on_start
    def on_start(
        self,
        ticker: str,
        interval_hint: timedelta,
        metadata: dict[str, Any],
    ) -> tuple[timedelta, timedelta]:
        # make ticker available to mixins (indicator registry)
        self.ctx = {"ticker": ticker, **self._create_initial_context()}
        for m in self.mixins:
            m.initialize_context(self.ctx)
        return self.prepare(metadata)

    # (2) on_bar
    def on_bar(
        self,
        bars: BarSlice,
    ) -> Optional[Sequence[TradeSignal]]:
        # pre‑bar mixin hooks
        for m in self.mixins:
            if not m.pre_bar(bars, self.ctx):
                return None

        sigs = self.process_bar(bars)

        # post‑bar mixin hooks
        for m in self.mixins:
            sigs = m.post_bar(sigs, bars, self.ctx)
        return sigs

    # (3) on_exec
    def on_exec(
        self,
        report: ExecutionReport,
    ) -> Optional[Sequence[TradeSignal]]:
        for m in self.mixins:
            m.pre_exec(report, self.ctx)
        return self.process_exec(report)

    # (4) on_stop – no special behaviour
    # -------------------------------------------------------------- #
    # Abstracts for subclasses                                       #
    # -------------------------------------------------------------- #
    def _create_initial_context(self) -> dict[str, Any]:
        return {}

    @abstractmethod
    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]: ...

    @abstractmethod
    def process_bar(self, bars: BarSlice) -> Optional[Sequence[TradeSignal]]: ...

    def process_exec(
        self,
        report: ExecutionReport,
    ) -> Optional[Sequence[TradeSignal]]:
        return None 