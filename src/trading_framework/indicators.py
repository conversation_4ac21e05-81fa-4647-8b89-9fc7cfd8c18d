from typing import Optional, List, Dict, Any, Callable, Tuple, Union
from datetime import timedelta, datetime, date, time
import pandas as pd
import numpy as np
import logging
from functools import lru_cache, partial
from pytz import timezone

logger = logging.getLogger(__name__)

class LazyIndicatorRegistry:
    """
    Registry for indicator functions with on-demand evaluation.
    
    This class provides a way to register indicator functions and compute them
    only when they are requested.
    """
    
    def __init__(self):
        """Initialize the registry."""
        self._indicator_functions = {}  # Maps indicator name to calculation function
        self._indicator_deps = {}       # Tracks dependencies between indicators
        self._historical_bars = None    # Reference to current historical bars data
        self._current_open_bar = None   # Reference to current open bar data
        self._ticker = None             # Current ticker symbol
    
    def update_data(self, historical_bars, current_open_bar, ticker):
        """
        Update the data used for indicator calculations.
        
        Args:
            historical_bars: Historical price data
            current_open_bar: Current open bar data
            ticker: The ticker symbol
        """
        self._historical_bars = historical_bars
        self._current_open_bar = current_open_bar
        self._ticker = ticker
    
    def register_indicator(self, name: str, func: Callable, dependencies: List[str] = None):
        """
        Register an indicator function.
        
        Args:
            name: The name of the indicator
            func: The function to calculate the indicator
            dependencies: List of indicator names this indicator depends on
        """
        self._indicator_functions[name] = func
        if dependencies:
            self._indicator_deps[name] = dependencies
    
    def register_factory(self, factory_name: str, factory_func: Callable):
        """
        Register an indicator factory function that can generate parameterized indicators.
        
        Args:
            factory_name: The name of the factory
            factory_func: The factory function that returns an indicator function
        """
        self._indicator_functions[factory_name] = factory_func
    
    def get(self, name: str, *args, **kwargs):
        """
        Get the value of an indicator, computing it on demand.
        
        This method supports two calling patterns:
        1. get('indicator_name') - Get a simple indicator
        2. get('factory_name', arg1, arg2, param1=value1) - Get a parameterized indicator
        
        Args:
            name: The name of the indicator or factory
            *args: Positional arguments for parameterized indicators
            **kwargs: Keyword arguments for parameterized indicators
            
        Returns:
            The indicator value
        """
        # Ensure we have the necessary data
        if self._historical_bars is None or self._historical_bars.empty:
            return None
        
        # Check if we need to resolve dependencies first
        if name in self._indicator_deps:
            for dep in self._indicator_deps[name]:
                self.get(dep)  # Ensure dependency is calculated
        
        # Compute the indicator
        try:
            if name not in self._indicator_functions:
                logger.warning(f"Indicator function '{name}' not found in registry")
                return None
            
            func = self._indicator_functions[name]
            
            # Handle parameterized indicators (factory pattern)
            if args or kwargs:
                # Factory function should return the actual calculator
                if callable(func):
                    indicator_func = func(*args, **kwargs)
                    if callable(indicator_func):
                        result = indicator_func(
                            self._historical_bars, 
                            self._current_open_bar,
                            self._ticker
                        )
                    else:
                        # If the factory directly returned a value
                        result = indicator_func
                else:
                    logger.error(f"Factory '{name}' is not callable")
                    return None
            else:
                # Simple indicator
                result = func(
                    self._historical_bars, 
                    self._current_open_bar,
                    self._ticker
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating indicator '{name}': {e}", exc_info=True)
            return None


class OnDemandIndicatorMixin:
    """
    Mixin that provides on-demand calculation of indicators.
    
    This mixin sets up a LazyIndicatorRegistry in the context and registers common
    indicator functions. Indicators are only calculated when explicitly requested.
    """
    
    def initialize_context(self, context):
        """Initialize the indicator registry in the context."""
        context['indicators'] = LazyIndicatorRegistry()
        context['ind'] = context['indicators']
        self._register_indicators(context['indicators'])
    
    def preprocess_bar(
        self,
        historical_bars,
        current_open_bar,
        context,
        ticker,
        metadata
    ):
        """Update the data references for on-demand calculation."""
        indicators = context.get('indicators')
        if indicators:
            indicators.update_data(historical_bars, current_open_bar, ticker)
        return True
    
    def _register_indicators(self, registry: LazyIndicatorRegistry):
        """
        Register indicator functions with the registry.
        
        This method adds both simple indicators and parameterized indicator factories.
        
        Args:
            registry: The LazyIndicatorRegistry
        """
        # Register some basic indicators
        registry.register_indicator('current_price', self._current_price)
        registry.register_indicator('bar_range', self._bar_range)
        
        # Register time indicators
        registry.register_indicator('current_timestamp', self._current_timestamp)
        registry.register_indicator('is_rth', self._is_regular_trading_hours)
        
        # Register factories for parameterized indicators
        registry.register_factory('avg_volume', self._avg_volume_factory)
        registry.register_factory('ma', self._moving_average_factory)
        registry.register_factory('atr', self._atr_factory)
        registry.register_factory('prev_day_close', self._prev_day_close_factory)
        registry.register_factory('rel_volume', self._relative_volume_factory)
        
        # Register opening range indicator factory
        registry.register_factory('opening_range', self._opening_range_factory)
        
        # Register opening range relative volume factory
        registry.register_factory('opening_range_rel_volume', self._opening_range_rel_volume_factory)
        
        # Register VWAP indicator factory
        registry.register_factory('vwap', self._vwap_factory)
    
    # === Basic indicator functions ===
    
    def _current_price(self, historical_bars, current_open_bar, ticker):
        """Get the most recent close price."""
        if historical_bars.empty:
            return None
        return historical_bars['close'].iloc[-1]
    
    def _bar_range(self, historical_bars, current_open_bar, ticker):
        """Get the range of the most recent bar."""
        if historical_bars.empty:
            return None
        last_bar = historical_bars.iloc[-1]
        return last_bar['high'] - last_bar['low']
    
    def _current_timestamp(self, historical_bars, current_open_bar, ticker):
        """Get the timestamp of the most recent bar."""
        if current_open_bar is not None and not current_open_bar.empty:
            return current_open_bar.index[0]
        elif not historical_bars.empty:
            return historical_bars.index[-1]
        return None
    
    def _is_regular_trading_hours(self, historical_bars, current_open_bar, ticker):
        """Check if the current bar is within regular trading hours."""
        timestamp = self._current_timestamp(historical_bars, current_open_bar, ticker)
        if timestamp is None:
            return False
        
        time = timestamp.time()
        return time(9, 30) <= time < time(16, 0)
    
    # === Parameterized indicator factories ===
    
    def _avg_volume_factory(self, period=20, dollar=False):
        """
        Factory for average volume indicators.
        
        Args:
            period: The number of days to average
            dollar: Whether to calculate dollar volume
            
        Returns:
            A function that calculates the average volume
        """
        def _avg_volume_calculator(historical_bars, current_open_bar, ticker):
            if historical_bars.empty or len(historical_bars) < period:
                return None
            
            # Get the current date
            current_date = historical_bars.index[-1].date()
            
            # Filter data to only include the last 'period' days
            cutoff_date = current_date - timedelta(days=period)
            recent_bars = historical_bars[historical_bars.index.date > cutoff_date]
            
            # Group data by date
            daily_data = recent_bars.groupby(recent_bars.index.date)
            
            # Calculate daily volumes
            if dollar:
                # Calculate dollar volume
                recent_bars['dollar_volume'] = recent_bars['volume'] * recent_bars['close']
                daily_volumes = recent_bars.groupby(recent_bars.index.date)['dollar_volume'].sum()
            else:
                daily_volumes = daily_data['volume'].sum()
            
            # Get the average volume for the specified period
            if len(daily_volumes) < period:
                return None
                
            return daily_volumes.mean()
            
        return _avg_volume_calculator
    
    def _moving_average_factory(self, period=20, column='close', type='simple'):
        """
        Factory for moving average indicators.
        
        Args:
            period: The number of days to average (not bars)
            column: The column to calculate the MA on
            type: The type of moving average ('simple', 'exponential', 'weighted')
            
        Returns:
            A function that calculates the moving average
        """
        def _ma_calculator(historical_bars, current_open_bar, ticker):
            if historical_bars.empty:
                return None
            
            # Get the current date
            current_date = self._current_timestamp(historical_bars, current_open_bar, ticker).date()
            
            # Filter data to only include the last 'period' days
            cutoff_date = current_date - timedelta(days=period)
            filtered_bars = historical_bars[historical_bars.index.date > cutoff_date]
            
            if filtered_bars.empty:
                return None
            
            # Group by date to ensure we're working with daily data
            daily_data = filtered_bars.groupby(filtered_bars.index.date)[column]
            
            if type == 'exponential':
                # For EMA, calculate on the daily closing values
                daily_closes = daily_data.last()
                if len(daily_closes) < 2:  # Need at least 2 days for meaningful EMA
                    return None
                return daily_closes.ewm(span=len(daily_closes)).mean().iloc[-1]
            elif type == 'simple':
                # Simple average of daily values
                daily_closes = daily_data.last()
                return daily_closes.mean()
            elif type == 'weighted':
                # Weighted average of daily values
                daily_closes = daily_data.last()
                if len(daily_closes) < 2:
                    return None
                weights = np.arange(1, len(daily_closes) + 1)
                return np.average(daily_closes, weights=weights)
            else:
                # Default to simple
                daily_closes = daily_data.last()
                return daily_closes.mean()
        
        return _ma_calculator
    
    def _atr_factory(self, period=14):
        """
        Factory for Average True Range (ATR) indicator.
        
        Args:
            period: The number of days for ATR calculation (not bars)
            
        Returns:
            A function that calculates the ATR
        """
        def _atr_calculator(historical_bars, current_open_bar, ticker):
            if historical_bars.empty:
                return None
            
            # Get the current date
            current_date = historical_bars.index[-1].date()
            
            # Filter data to include enough days for calculation
            # We need more days because we're looking back 'period' days
            lookback_buffer = 30  # Buffer to ensure we have enough trading days
            cutoff_date = current_date - timedelta(days=period + lookback_buffer)
            filtered_bars = historical_bars[historical_bars.index.date > cutoff_date]
            
            if filtered_bars.empty:
                return None
                
            # Convert intraday bars to daily OHLC bars
            daily_bars = filtered_bars.groupby(filtered_bars.index.date).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            })
            
            # Make sure we have at least 2 days of data
            if len(daily_bars) < 2:
                return None
                
            # Calculate True Range on daily bars
            high = daily_bars['high']
            low = daily_bars['low']
            prev_close = daily_bars['close'].shift(1)
            
            # True Range is the greatest of:
            # 1. Current High - Current Low
            # 2. |Current High - Previous Close|
            # 3. |Current Low - Previous Close|
            tr1 = high - low
            tr2 = (high - prev_close).abs()
            tr3 = (low - prev_close).abs()
            
            # For each row, get the maximum of the three TR calculations
            daily_tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # Calculate ATR (simple average of TR for the specified period)
            # Make sure we only use the most recent 'period' days
            if len(daily_tr) <= period:
                return daily_tr.mean()
            else:
                return daily_tr.iloc[-period:].mean()
            
        return _atr_calculator
    
    
    def _prev_day_close_factory(self, days_back=1):
        """
        Factory for previous day's close price.
        
        Args:
            days_back: Number of days to look back
            
        Returns:
            A function that retrieves the previous day's close
        """
        def _prev_day_close_calculator(historical_bars, current_open_bar, ticker):
            if historical_bars.empty:
                return None
                
            # Get current date
            current_date = historical_bars.index[-1].date()
            
            # Look back specified number of days
            for i in range(1, 10):  # Look back up to 10 days to find trading days
                check_date = current_date - timedelta(days=i)
                prev_day_data = historical_bars[historical_bars.index.date == check_date]
                
                if not prev_day_data.empty:
                    days_back_count = 1
                    
                    # If we need to go back more than one trading day
                    if days_back > 1:
                        for j in range(i + 1, i + 20):  # Look for more previous trading days
                            earlier_date = current_date - timedelta(days=j)
                            earlier_data = historical_bars[historical_bars.index.date == earlier_date]
                            
                            if not earlier_data.empty:
                                days_back_count += 1
                                if days_back_count == days_back:
                                    return earlier_data['close'].iloc[-1]
                    else:
                        return prev_day_data['close'].iloc[-1]
            
            return None
            
        return _prev_day_close_calculator
    
    def _relative_volume_factory(self, period=20):
        """
        Factory for relative volume indicator.
        
        Args:
            period: The number of days to average for the baseline
            
        Returns:
            A function that calculates relative volume
        """
        def _rel_volume_calculator(historical_bars, current_open_bar, ticker):
            if historical_bars.empty:
                return None
                
            # Get current date
            current_date = historical_bars.index[-1].date()
            
            # Get current day's data
            current_day_data = historical_bars[historical_bars.index.date == current_date]
            if current_day_data.empty:
                return None
                
            # Get current day's volume
            current_day_volume = current_day_data['volume'].sum()
            
            # Get average volume factory
            avg_volume_calc = self._avg_volume_factory(period=period)
            
            # Calculate average volume - pass the ticker parameter here
            avg_volume = avg_volume_calc(historical_bars, current_open_bar, ticker)
            if avg_volume is None or avg_volume == 0:
                return None
                
            # Calculate relative volume
            return current_day_volume / avg_volume
            
        return _rel_volume_calculator

    def _opening_range_factory(self, minutes=15, include_dollar_volume=True):
        """
        Factory for opening range indicators.
        
        Args:
            minutes: The number of minutes to define the opening range (default: 15)
            include_dollar_volume: Whether to calculate dollar volume (default: True)
            
        Returns:
            A function that calculates the opening range high, low, open, close and optionally dollar volume
        """
        def _opening_range_calculator(historical_bars, current_open_bar, ticker):
            if historical_bars.empty:
                return None
            
            # Get current timestamp
            current_timestamp = self._current_timestamp(historical_bars, current_open_bar, ticker)
            if current_timestamp is None:
                return None
            
            current_date = current_timestamp.date()
            
            # Get today's data
            today_data = historical_bars[historical_bars.index.date == current_date]
            if today_data.empty:
                return None
            
            # Define market open time (9:30 AM)
            market_open_time = time(9, 30)
            
            # Calculate opening range end time
            market_open_datetime = datetime.combine(current_date, market_open_time)
            opening_range_end_datetime = market_open_datetime + timedelta(minutes=minutes)
            opening_range_end_time = opening_range_end_datetime.time()
            
            # Get opening range data
            opening_range_data = today_data[
                (today_data.index.time >= market_open_time) & 
                (today_data.index.time < opening_range_end_time)
            ]
            
            # Check if we have enough data
            if opening_range_data.empty:
                return None
            
            # Calculate opening range high and low
            opening_range_high = opening_range_data['high'].max()
            opening_range_low = opening_range_data['low'].min()
            
            # Get opening range open and close
            opening_range_open = opening_range_data['open'].iloc[0]
            opening_range_close = opening_range_data['close'].iloc[-1]
            
            result = {
                'high': opening_range_high,
                'low': opening_range_low,
                'open': opening_range_open,
                'close': opening_range_close
            }
            
            # Calculate dollar volume if requested
            if include_dollar_volume:
                dollar_volume = (opening_range_data['volume'] * opening_range_data['close']).sum()
                result['dollar_volume'] = dollar_volume
            
            # Calculate average volume for the opening range
            result['avg_volume'] = opening_range_data['volume'].mean()
            
            return result
            
        return _opening_range_calculator

    def _opening_range_rel_volume_factory(self, period=5, minutes=5):
        """
        Factory for opening range relative volume indicator.
        
        Compares the current day's opening range volume to the average
        opening range volume of the past N days.
        
        Args:
            period: The number of days to average for the baseline (default: 5)
            minutes: The number of minutes to define the opening range (default: 5)
            
        Returns:
            A function that calculates opening range relative volume
        """
        def _opening_range_rel_volume_calculator(historical_bars, current_open_bar, ticker):
            if historical_bars.empty:
                return None
                
            # Get current timestamp
            current_timestamp = self._current_timestamp(historical_bars, current_open_bar, ticker)
            if current_timestamp is None:
                return None
            
            current_date = current_timestamp.date()
            
            # Define market open time (9:30 AM)
            market_open_time = time(9, 30)
            
            # Calculate opening range end time
            market_open_datetime = datetime.combine(current_date, market_open_time)
            opening_range_end_datetime = market_open_datetime + timedelta(minutes=minutes)
            opening_range_end_time = opening_range_end_datetime.time()
            
            # Get today's opening range data
            today_data = historical_bars[historical_bars.index.date == current_date]
            if today_data.empty:
                return None
                
            today_opening_range = today_data[
                (today_data.index.time >= market_open_time) & 
                (today_data.index.time < opening_range_end_time)
            ]
            
            if today_opening_range.empty:
                return None
                
            # Calculate today's opening range volume
            today_opening_range_volume = today_opening_range['volume'].sum()
            
            # Get historical opening range volumes for past 'period' days
            historical_opening_range_volumes = []
            
            # Look back up to period+10 days to find 'period' trading days
            for i in range(1, period + 10):
                past_date = current_date - timedelta(days=i)
                past_data = historical_bars[historical_bars.index.date == past_date]
                
                if not past_data.empty:
                    past_opening_range = past_data[
                        (past_data.index.time >= market_open_time) & 
                        (past_data.index.time < opening_range_end_time)
                    ]
                    
                    if not past_opening_range.empty:
                        past_opening_range_volume = past_opening_range['volume'].sum()
                        historical_opening_range_volumes.append(past_opening_range_volume)
                        
                        if len(historical_opening_range_volumes) >= period:
                            break
            
            # Check if we have enough historical data
            if len(historical_opening_range_volumes) < period:
                return None
                
            # Calculate average historical opening range volume
            avg_opening_range_volume = sum(historical_opening_range_volumes) / len(historical_opening_range_volumes)
            
            if avg_opening_range_volume == 0:
                return None
                
            # Calculate relative volume
            return today_opening_range_volume / avg_opening_range_volume
            
        return _opening_range_rel_volume_calculator

    def _vwap_factory(self, from_market_open=True):
        """
        Factory for VWAP (Volume Weighted Average Price) indicator.
        
        Args:
            from_market_open: Whether to calculate VWAP from market open (default: True)
            
        Returns:
            A function that calculates VWAP
        """
        def _vwap_calculator(historical_bars, current_open_bar, ticker):
            if historical_bars.empty:
                return None
            
            # Get current timestamp
            current_timestamp = historical_bars.index[-1]
            current_date = current_timestamp.date()
            
            # Define market open time
            market_open_time = time(9, 30)
            
            # Filter data based on parameter
            if from_market_open:
                # Calculate VWAP from market open
                day_start = datetime.combine(current_date, market_open_time)
                from pytz import timezone as pytz_timezone
                day_start = pytz_timezone('US/Eastern').localize(day_start)
                df = historical_bars[historical_bars.index >= day_start].copy()
            else:
                # Calculate VWAP for the entire dataset
                df = historical_bars.copy()
            
            if df.empty:
                return None
            
            # Calculate VWAP
            df["cum_volume"] = df["volume"].cumsum()
            df["cum_dollar"] = ((df["high"] + df["low"] + df["close"]) / 3 * df["volume"]).cumsum()
            df["vwap"] = df["cum_dollar"] / df["cum_volume"]
            
            # Return the most recent VWAP value
            return df["vwap"].iloc[-1]
            
        return _vwap_calculator


# Example of how to use the on-demand indicator system
class ParameterizedIndicatorStrategy:
    """
    Example of how to use parameterized indicators on demand.
    
    This is just a reference implementation showing how to access indicators.
    """
    
    def example_usage(self, context, historical_bars, current_open_bar, ticker):
        """Example of how to use the indicator system."""
        # Access the indicator registry
        indicators = context['indicators']
        
        # Get simple indicators
        current_price = indicators.get('current_price')
        is_trading_hours = indicators.get('is_rth')
        
        # Get parameterized indicators
        ma_20 = indicators.get('ma', 20)
        ma_50 = indicators.get('ma', 50, type='exponential')
        atr_14 = indicators.get('atr', 14)
        
        # Get volume indicators
        avg_volume = indicators.get('avg_volume', 20)
        rel_volume = indicators.get('rel_volume', 20)
        
        # Use the indicators in your strategy logic
        if ma_20 and ma_50 and ma_20 > ma_50:
            # Bullish crossover
            pass 