import requests
from bs4 import BeautifulSoup
from datetime import datetime, date
from typing import List, Dict, Optional, Union

class CryptoUniverseSelector:
    """
    A client for fetching historical coin market cap data from CoinMarketCap.

    Example usage:
        client = HistoricalCMC()
        dates = client.get_available_dates()  # [datetime.date(2013, 4, 28), ...]
        data = client.get_top_coins(dates[-1], top=50)
    """

    BASE_URL = 'https://coinmarketcap.com'
    HISTORICAL_LISTING = '/historical/'

    def __init__(self, session: Optional[requests.Session] = None):
        self.session = session or requests.Session()

    def get_available_dates(self) -> List[date]:
        """
        Scrapes the /historical/ landing page for all available snapshot dates.
        Returns a list of datetime.date objects.
        """
        url = f"{self.BASE_URL}{self.HISTORICAL_LISTING}"
        resp = self.session.get(url)
        resp.raise_for_status()
        soup = BeautifulSoup(resp.text, 'html.parser')

        date_links = soup.select('a.historical-link')
        raw_dates = []
        for a in date_links:
            href = a.get('href', '')
            parts = href.strip('/').split('/')
            if len(parts) == 2 and parts[0] == 'historical':
                raw_dates.append(parts[1])

        unique_strings = sorted(set(raw_dates))
        return [datetime.strptime(ds, '%Y%m%d').date() for ds in unique_strings]

    def get_top_coins(self,
                      snapshot_date: Union[str, date],
                      top: int = 20) -> List[Dict]:
        """
        Fetches the historical snapshot for the given date (datetime.date or 'YYYYMMDD').
        Parses the top N coins by market cap and returns list of dicts:
        [
            {
                'rank': int,
                'name': str,
                'symbol': str,
                'market_cap': float,
                'price': float,
                'circulating_supply': float,
                'percent_change_1h': Optional[float],
                'percent_change_24h': Optional[float],
                'percent_change_7d': Optional[float]
            },
            ...
        ]
        """
        # Normalize date to string
        if isinstance(snapshot_date, date):
            date_str = snapshot_date.strftime('%Y%m%d')
        else:
            date_str = snapshot_date

        url = f"{self.BASE_URL}{self.HISTORICAL_LISTING}{date_str}/"
        resp = self.session.get(url)
        resp.raise_for_status()
        soup = BeautifulSoup(resp.text, 'html.parser')

        rows = soup.find_all('tr', class_='cmc-table-row')
        results = []

        for row in rows[:top]:
            cols = row.find_all('td')
            if len(cols) < 7:
                continue

            rank = int(cols[0].get_text(strip=True))
            name_tag = cols[1].select_one('a.cmc-table__column-name--name')
            name = name_tag.get_text(strip=True) if name_tag else ''
            symbol = cols[2].get_text(strip=True)
            market_cap = self._parse_currency(cols[3].get_text())
            price = self._parse_currency(cols[4].get_text())
            circulating = self._parse_currency(cols[5].get_text().split()[0])

            pc_base = 6
            if cols[6].find('a'):
                pc_base = 7
            pc_1h = self._parse_percent(cols[pc_base].get_text())
            pc_24h = self._parse_percent(cols[pc_base + 1].get_text())
            pc_7d = self._parse_percent(cols[pc_base + 2].get_text())

            results.append({
                'rank': rank,
                'name': name,
                'symbol': symbol,
                'market_cap': market_cap,
                'price': price,
                'circulating_supply': circulating,
                'percent_change_1h': pc_1h,
                'percent_change_24h': pc_24h,
                'percent_change_7d': pc_7d,
            })

        return results

    @staticmethod
    def _parse_currency(value: str) -> float:
        """
        Converts currency strings like '$1,488,566,971.96' or '11,091,325' to float.
        """
        cleaned = value.replace('$', '').replace(',', '').replace('--', '').strip()
        try:
            return float(cleaned) if cleaned else 0.0
        except ValueError:
            return 0.0

    @staticmethod
    def _parse_percent(value: str) -> Optional[float]:
        """
        Converts percent strings like '0.64%' to float; returns None for '--'.
        """
        cleaned = value.replace('%', '').strip()
        try:
            return float(cleaned)
        except ValueError:
            return None


if __name__ == '__main__':
    client = CryptoUniverseSelector()
    dates = client.get_available_dates()
    print("Available dates:", dates[:5], "...")
    sample = client.get_top_coins(dates[-1], top=10)
    for entry in sample:
        print(entry)