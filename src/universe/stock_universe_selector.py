from pathlib import Path
import pandas as pd
import datetime as dt
from typing import List, Optional, Set, Tuple, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
import numbers
from tqdm import tqdm

class StockUniverseSelector:
    """
    Builds a universe of tickers using local PolygonDiskMarketData and a ticker_info_store.
    Filters for common stocks whose data range overlaps with the given window, and caches results.

    Returns a DataFrame with each row containing ticker and its detail fields as columns.
    """

    def __init__(
        self,
        polygon_data,
        ticker_info_store,
        cache_dir: Union[str, Path] = "cache/universe",
        max_workers: int = 32
    ):
        self.polygon_data = polygon_data
        self.tinfo = ticker_info_store
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_workers = max_workers

    def _cache_path(self, window_start: dt.datetime, window_end: dt.datetime) -> Path:
        return self.cache_dir / f"stock_uni_{window_start.date()}_{window_end.date()}.parq"

    def select(
        self,
        window_start: dt.datetime,
        window_end: dt.datetime
    ) -> pd.DataFrame:
        cache_file = self._cache_path(window_start, window_end)
        if cache_file.exists():
            return pd.read_parquet(cache_file)

        symbols = self.polygon_data.get_available_symbols()

        # build list of tickers whose data overlaps the window
        tickers_to_fetch: List[Tuple[str, dt.datetime]] = []
        for ticker in symbols:
            dr = self.polygon_data.get_data_date_range(ticker)
            if not dr:
                continue
            avail_start, avail_end = dr
            if avail_start is None or avail_end is None:
                continue
            if avail_start <= window_end and avail_end >= window_start:
                tickers_to_fetch.append((ticker, avail_end))

        rows: List[dict] = []
        # counters
        total = len(tickers_to_fetch)
        errored = 0
        invalid = 0
        filtered_out = 0
        processed = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_map = {
                executor.submit(self.tinfo.get_ticker_details, ticker, avail_end): (ticker, avail_end)
                for ticker, avail_end in tickers_to_fetch
            }

            for future in tqdm(
                as_completed(future_map),
                total=total,
                desc="Fetching ticker details"
            ):
                ticker, avail_end = future_map[future]
                try:
                    details = future.result()
                except Exception:
                    errored += 1
                    continue

                # normalize to dict
                if isinstance(details, pd.DataFrame) and not details.empty:
                    details_dict = details.iloc[0].to_dict()
                elif isinstance(details, dict):
                    details_dict = details.copy()
                else:
                    invalid += 1
                    continue

                # sanitize for parquet
                for k, v in list(details_dict.items()):
                    if isinstance(v, dict):
                        details_dict[k] = json.dumps(v)
                    elif not isinstance(v, (str, numbers.Number, bool, dt.datetime, dt.date, type(None))):
                        details_dict[k] = str(v)

                # filter to common stock
                if details_dict.get("type") != "CS":
                    filtered_out += 1
                    continue

                row = {"ticker": ticker}
                row.update(details_dict)
                rows.append(row)
                processed += 1

        # summary
        print(f"Total candidates: {total}")
        print(f"  • Successfully processed: {processed}")
        print(f"  • Errored on fetch: {errored}")
        print(f"  • Invalid details format: {invalid}")
        print(f"  • Filtered out (not CS): {filtered_out}")

        universe_df = pd.DataFrame(rows)
        universe_df.to_parquet(cache_file)
        return universe_df

    def select_by_mcap_range(
        self,
        window_start: dt.datetime,
        window_end: dt.datetime,
        min_mcap: float,
        max_mcap: float
    ) -> pd.DataFrame:
        """
        Returns tickers whose estimated market cap (wso * close price) at either
        the start or end of [window_start, window_end] falls in [min_mcap, max_mcap].
        """
        # 1) get base universe of common stocks
        base_uni = self.select(window_start, window_end)
        rows: List[dict] = []

        # 2) fetch & compute market cap in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_map = {
                executor.submit(
                    self._compute_mcap_row,
                    ticker, window_start, window_end, min_mcap, max_mcap
                ): ticker
                for ticker in base_uni["ticker"].tolist()
            }
            for future in as_completed(future_map):
                try:
                    result = future.result()
                except Exception:
                    continue
                if result:
                    rows.append(result)

        # 3) return a DataFrame of all matches
        return pd.DataFrame(rows)

    def _compute_mcap_row(
        self,
        ticker: str,
        window_start: dt.datetime,
        window_end: dt.datetime,
        min_mcap: float,
        max_mcap: float
    ) -> Optional[dict]:
        # adjust window to ticker's availability
        dr = self.polygon_data.get_data_date_range(ticker)
        if not dr:
            return None
        avail_start, avail_end = dr
        if avail_start is None or avail_end is None:
            return None
        # determine effective start/end: never before avail_start, never after avail_end
        effective_start = max(window_start, avail_start)
        effective_end   = min(window_end, avail_end)
        if effective_start > effective_end:
            return None

        # fetch ticker details at the effective start date
        details = self.tinfo.get_ticker_details(ticker, effective_start)
        if not details:
            # no ticker details: assume it passes and include only the ticker
            return {"ticker": ticker}
        wso = details.get("weighted_shares_outstanding", None)
        if wso is None or pd.isna(wso) or wso <= 0:
            return {"ticker": ticker}

        df = self.polygon_data.gather_historical_data(
            ticker,
            effective_start,
            effective_end,
            interval=86400  # one‐day bars
        )
        if df is None or df.empty:
            return None

        price_start = df["close"].iloc[0]
        price_end   = df["close"].iloc[-1]
        mcap_start = wso * price_start
        mcap_end   = wso * price_end

        if not (
            (min_mcap <= mcap_start <= max_mcap) or
            (min_mcap <= mcap_end   <= max_mcap)
        ):
            return None

        return {
            "ticker": ticker,
            "weighted_shares_outstanding": wso,
            "price_start": price_start,
            "price_end": price_end,
            "mcap_start": mcap_start,
            "mcap_end": mcap_end,
        } 
        
    def constituents_for(
        self,
        window_start: dt.datetime,
        window_end: dt.datetime,
        min_mcap: float,
        max_mcap: float
    ) -> pd.DataFrame:
        """
        Returns a DataFrame with one row per (business) date and ticker,
        for all tickers whose market‐cap at window start/end is in [min_mcap, max_mcap],
        and which were trading on that date.
        Columns: date (Timestamp), ticker (str)
        """
        # 1) grab tickers in the MCAP band
        uni = self.select_by_mcap_range(window_start, window_end, min_mcap, max_mcap)
        tickers = uni["ticker"].tolist()

        # 2) full business‐day calendar for the window
        bdays = pd.bdate_range(
            start=window_start.normalize(),
            end=window_end.normalize()
        )

        rows: List[dict] = []
        for ticker in tickers:
            dr = self.polygon_data.get_data_date_range(ticker)
            if not dr:
                continue
            avail_start, avail_end = dr
            # clamp to our window
            eff_start = max(window_start, avail_start)
            eff_end   = min(window_end,   avail_end)
            if eff_start > eff_end:
                continue

            # pick only bdays where the ticker was alive
            valid_days = bdays[(bdays >= eff_start) & (bdays <= eff_end)]
            for day in valid_days:
                rows.append({"date": day, "ticker": ticker})

        df = pd.DataFrame(rows)
        # if you prefer date-first ordering:
        df = df.sort_values(["date", "ticker"]).reset_index(drop=True)
        return df

    def select_by_price_and_volume(
        self,
        window_start: dt.datetime,
        window_end: dt.datetime,
        min_price: float,
        min_dollar_volume: float
    ) -> pd.DataFrame:
        """
        Returns a MultiIndex DataFrame with levels (date, ticker) for all symbols
        whose trailing-10-day average close price (excluding the current day)
        is >= min_price and whose trailing-10-day average dollar-volume
        (close * volume, averaged over the previous 10 trading days and shifted
        by one day) is >= min_dollar_volume on trading dates between
        window_start and window_end.

        Using a 10-day look-back and shifting by one trading day eliminates
        look-ahead bias on both price and volume dimensions.

        Example:
            >>> from datetime import datetime
            >>> selector = StockUniverseSelector(polygon_data, tinfo)
            >>> df = selector.select_by_price_and_volume(
            ...     datetime(2022,1,3), datetime(2022,1,7),
            ...     min_price=10.0, min_dollar_volume=1e7
            ... )
            >>> df.index
            MultiIndex([(Timestamp('2022-01-03 00:00:00'), 'AAPL'),
                        (Timestamp('2022-01-03 00:00:00'), 'MSFT'),
                        (Timestamp('2022-01-04 00:00:00'), 'GOOG'),
                        (Timestamp('2022-01-05 00:00:00'), 'AMZN'),
                        (Timestamp('2022-01-07 00:00:00'), 'TSLA')],
                       names=['date', 'ticker'])
        """
        # load ticker metadata to filter for common stocks (type "CS")
        info_df = self.tinfo.load_ticker_data(window_end, refresh_cache=True)
        cs_tickers = set(info_df.loc[info_df["type"] == "CS", "ticker"])

        symbols = self.polygon_data.get_available_symbols()
        # if we successfully loaded types, drop anything that's not CS
        if cs_tickers is not None:
            symbols = [ticker for ticker in symbols if ticker in cs_tickers]

        rows: List[dict] = []
        for ticker in symbols:
            # skip tickers with no overlap
            dr = self.polygon_data.get_data_date_range(ticker)
            if not dr:
                continue
            avail_start, avail_end = dr
            if avail_start is None or avail_end is None:
                continue
            if avail_start > window_end or avail_end < window_start:
                continue

            # clamp to available window
            eff_start = max(window_start, avail_start)
            eff_end   = min(window_end,   avail_end)

            # load daily OHLCV
            df = self.polygon_data.gather_historical_data(
                ticker, eff_start, eff_end
            )
            if df is None or df.empty:
                continue

            # compute trailing 10-day averages for close price and dollar volume,
            # both shifted by one day so only information available up to the
            # previous trading day is used.
            df = df.copy()
            df['dollar_volume_daily'] = df['close'] * df['volume']
            df['avg_close_10d'] = (
                df['close']
                .rolling(window=10, min_periods=10)
                .mean()
                .shift(1)
            )
            df['avg_dollar_volume_10d'] = (
                df['dollar_volume_daily']
                .rolling(window=10, min_periods=10)
                .mean()
                .shift(1)
            )

            # apply filters using trailing averages
            mask = (
                (df['avg_close_10d'] >= min_price) &
                (df['avg_dollar_volume_10d'] >= min_dollar_volume)
            )

            # record each (date, ticker) passing both thresholds
            for date in df.index[mask]:
                rows.append({'date': date, 'ticker': ticker})

        # build and return a MultiIndex DataFrame
        result = pd.DataFrame(rows, columns=['date', 'ticker'])
        return result.set_index(['date', 'ticker']).sort_index()