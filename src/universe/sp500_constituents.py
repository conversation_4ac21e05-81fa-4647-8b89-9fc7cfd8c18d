import pandas as pd
from pathlib import Path
from datetime import datetime, date
from typing import List, Union, Optional

class SP500Constituents:
    """
    A client for retrieving S&P 500 constituents as of any given date.

    Example:
        client = SP500Constituents(
            csv_file="data/S&P 500 Historical Components & Changes (03-10-2025).csv",
            parquet_store="data/sp500_constituents.parquet"
        )
        symbols = client.constituents_for("2024-01-01")
    """

    def __init__(
        self,
        csv_file: Union[str, Path] = "S&P 500 Historical Components & Changes(03-10-2025).csv",
        parquet_store: Union[str, Path] = "data/sp500_constituents.parquet",
        force: bool = False
    ):
        self.csv_file = Path(csv_file)
        self.parquet_store = Path(parquet_store)
        self.index_df = self._build_index(force)

    def _build_index(self, force: bool) -> pd.DataFrame:
        """
        Parses the CSV of historical S&P 500 component lists into a long-form DataFrame
        with one row per symbol per snapshot date, and caches it as a Parquet file.
        """
        if self.parquet_store.exists() and not force:
            return pd.read_parquet(self.parquet_store)

        # Read CSV: first column is date, second is quoted comma-separated symbols
        df = pd.read_csv(
            self.csv_file,
            header=None,
            names=["snapshot_date", "symbols"],
            parse_dates=[0]
        )
        # Split and explode symbol lists
        df["symbol_list"] = df["symbols"].str.strip('"').str.split(',')
        records = []
        for _, row in df.iterrows():
            snap = row["snapshot_date"].date() if isinstance(row["snapshot_date"], pd.Timestamp) else row["snapshot_date"]
            for rank, sym in enumerate(row["symbol_list"], start=1):
                records.append({
                    "snapshot_date": snap,
                    "rank": rank,
                    "symbol": sym
                })
        index_df = (
            pd.DataFrame(records)
            .set_index("snapshot_date")
            .sort_index()
            .sort_values(["snapshot_date", "rank"])
        )
        # Cache to Parquet
        index_df.to_parquet(self.parquet_store)
        return index_df

    def get_snapshot_before(
        self,
        ts: Union[str, datetime, date]
    ) -> pd.Timestamp:
        """
        Returns the most recent snapshot date <= ts.
        """
        ts = pd.to_datetime(ts)
        if ts.tz is not None:
            ts = ts.tz_convert(None)
        ts = ts.normalize().date()

        dates = pd.to_datetime(self.index_df.index.unique()).date
        # Find rightmost date <= ts
        valid_dates = [d for d in dates if d <= ts]
        if not valid_dates:
            raise ValueError(f"No snapshot available on or before {ts}")
        return max(valid_dates)

    def constituents_for(
        self,
        ts: Union[str, datetime, date]
    ) -> List[str]:
        """
        Returns the list of S&P 500 symbols valid at the given timestamp.
        """
        snap = self.get_snapshot_before(ts)
        subset = (
            self.index_df.loc[snap]
            .sort_values("rank")
        )
        return subset["symbol"].tolist()
