"""
Backtest script for ticker source events extracted from Substack with filtering.

This script uses the run.py backtest framework to test the TickerEventBuyStrategy
with filtered ticker events from a SubstackTickerSource.
"""

import asyncio
from datetime import datetime
from zoneinfo import ZoneInfo
from dotenv import load_dotenv

load_dotenv()

# Import the run_backtest function from run.py
from backtest_runner.run import run_backtest

async def main():
    # Define the window for ticker events in Eastern Standard Time.
    est_zone = ZoneInfo("America/New_York")
    start_date = datetime.fromisoformat("2025-03-01").replace(tzinfo=est_zone).replace(hour=4, minute=0, second=0, microsecond=0)
    end_date = datetime.fromisoformat("2025-03-28").replace(tzinfo=est_zone).replace(hour=16, minute=0, second=0, microsecond=0)
    
    # Define strategy parameters
    strategy_params = {
        "profit_target": 0.05,
        "stop_loss_percent": 0.03,
        "max_hold_hours": 3,
        "max_position_value": 25000
    }
    
    # Define ticker source parameters
    ticker_source_params = {
        "chat_id": "836125",
        "rate_limit": 1,
        "min_market_cap": 0,
        "max_market_cap": 10_000_000_000,
        "trade_session": "rth"
    }
    
    # Run the backtest using the run_backtest function with our custom source
    results = await run_backtest(
        strategy_name="ticker_event_buy_strategy",
        risk_per_position=0.25,
        start_date=start_date,
        end_date=end_date,
        initial_capital=100000.0,
        strategy_params=strategy_params,
        ticker_selection="ticker-source",
        ticker_source="filtered_substack_ticker_source",
        ticker_source_params=ticker_source_params,
        tickers_per_second=10.0
    )
    
if __name__ == "__main__":
    asyncio.run(main())