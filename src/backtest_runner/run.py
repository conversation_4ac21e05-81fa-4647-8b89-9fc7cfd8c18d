import asyncio
import logging
import importlib
from datetime import datetime, timedelta
import pytz
import os
import argparse
import json
import sys
from contextlib import contextmanager

from marketdata.backtesting_market_data import BacktestingMarketData
from marketdata.market_data_builder import MarketDataBuilder
from marketdata.theta_splicing_market_data import ThetaSplicingMarketData
from marketdata.theta_market_data import ThetaMarketData
from tickers.preset_ticker_discover import PresetTickerDiscover
from tickers.dynamic_ticker_discover import DynamicTickerDiscover
from tools.clock import Clock
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from brokers.local_broker import LocalBroker
from brokers.logging_broker import LoggingBroker
from position_manager import PositionManager
from stats.stats_loggers import BacktestStatsLogger

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_file_name(strategy_name, strategy_params, start_date, end_date, file_type, extension):
    """
    Generate a standardized file name for reports and logs.
    
    Args:
        strategy_name: Name of the strategy
        strategy_params: Dictionary of strategy parameters
        start_date: Start date of the backtest
        end_date: End date of the backtest
        file_type: Type of file (e.g., 'backtest_report', 'trade_stats', 'backtest_log')
        extension: File extension (e.g., 'html', 'log')
        
    Returns:
        str: Formatted file name
    """
    # Create a string representation of strategy parameters for the filename
    params_str = "_".join([f"{k}_{v}" for k, v in strategy_params.items()])
    if params_str:
        params_str = "_" + params_str
    
    # Generate file name
    return f"{strategy_name}{params_str}_{file_type}_{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}.{extension}"

@contextmanager
def redirect_output_to_file(strategy_name, strategy_params, start_date, end_date):
    """
    Context manager to redirect stdout, stderr, and logging to a log file.
    
    Args:
        strategy_name: Name of the strategy
        strategy_params: Dictionary of strategy parameters
        start_date: Start date of the backtest
        end_date: End date of the backtest
        
    Yields:
        str: Path to the log file
    """
    # Save original stdout and stderr
    original_stdout = sys.stdout
    original_stderr = sys.stderr
    
    # Save original logging configuration
    root_logger = logging.getLogger()
    original_handlers = root_logger.handlers.copy()
    
    # Create output directory if it doesn't exist
    output_dir = "output/logs"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate log file name
    log_filename = generate_file_name(
        strategy_name, 
        strategy_params, 
        start_date, 
        end_date, 
        "backtest_log", 
        "log"
    )
    log_path = os.path.join(output_dir, log_filename)
    
    try:
        # Redirect stdout and stderr to the log file
        log_file = open(log_path, 'w')
        sys.stdout = log_file
        sys.stderr = log_file
        
        # Also redirect logging to the same file
        # First, remove all existing handlers
        for handler in original_handlers:
            root_logger.removeHandler(handler)
        
        # Add a file handler for the log file
        file_handler = logging.FileHandler(log_path)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        root_logger.addHandler(file_handler)
        
        print(f"Redirecting output to log file: {log_path}")
        logging.info(f"Redirecting output to log file: {log_path}")
        
        yield log_path
    finally:
        # Restore original stdout and stderr
        sys.stdout = original_stdout
        sys.stderr = original_stderr
        
        # Restore original logging configuration
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)
            
        for handler in original_handlers:
            root_logger.addHandler(handler)
        
        if 'log_file' in locals():
            log_file.close()
            logging.info(f"Log file created at: {log_path}")
            
async def run_backtest(
    strategy_name: str,
    risk_per_position: float,
    start_date: datetime = None,
    end_date: datetime = None,
    tickers: list = None,
    initial_capital: float = 100000,
    strategy_params: dict = None,
    benchmark_ticker: str = "QQQ",
    trade_session: str = "rth",
    ticker_selection: str = "preset",
    min_market_cap: float = None,
    max_market_cap: float = None,
    min_price: float = None,
    ticker_source: str = None,
    ticker_source_params: dict = None,
    tickers_per_second: float = 10.0,
    run_monte_carlo: bool = True,
    monte_carlo_simulations: int = 1000,
    monte_carlo_days: int = 365,
    max_position_size: float = None
):
    """
    Run a backtest with the specified strategy and parameters.
    
    Args:
        strategy_name: Name of the strategy module (without .py extension)
        risk_per_position: Risk per position as a percentage of capital
        start_date: Start date for the backtest (default: 30 days before end_date)
        end_date: End date for the backtest (default: current date)
        tickers: List of tickers to test (default: ["TQQQ"])
        initial_capital: Initial capital for the backtest (default: 100000)
        strategy_params: Dictionary of parameters to pass to the strategy (default: {})
        benchmark_ticker: Ticker to use as benchmark (default: "QQQ")
        trade_session: Trading session type, either "rth" (regular trading hours) or "full" (default: "rth")
        ticker_selection: Method to select tickers, either "preset", "dynamic", or "ticker-source" (default: "preset")
        min_market_cap: Minimum market cap for dynamic ticker selection (default: None)
        max_market_cap: Maximum market cap for dynamic ticker selection (default: None)
        min_price: Minimum price for dynamic ticker selection (default: None)
        ticker_source: Name of the ticker source module to use with ticker-source selection (default: None)
        ticker_source_params: Dictionary of parameters to pass to the ticker source (default: {})
        tickers_per_second: How many tickers to yield per second in ticker-source mode (default: 10.0)
        run_monte_carlo: Whether to run Monte Carlo simulation (default: True)
        monte_carlo_simulations: Number of Monte Carlo simulations to run (default: 1000)
        monte_carlo_days: Number of days to project in Monte Carlo simulation (default: 365)
        max_position_size: Maximum position size as a percentage of initial capital (e.g., 0.2 for 20%)
    """
    # Set default values
    if tickers is None:
        tickers = ["TQQQ"]
    
    if strategy_params is None:
        strategy_params = {}
    
    if ticker_source_params is None:
        ticker_source_params = {}
    
    # Set default dates if not provided
    eastern = pytz.timezone('US/Eastern')
    if end_date is None:
        end_date = eastern.localize(datetime.now())
    
    if start_date is None:
        start_date = end_date - timedelta(days=30)
    
    # Initialize clock for backtesting
    clock = Clock()
    
    # Set up market data
    market_data = (MarketDataBuilder()
                        .with_trade_session(trade_session) 
                        .with_period("intraday")
                        .build_market_data())

    backtesting_data = BacktestingMarketData(
        market_data=market_data,
        start_date=start_date,
        end_date=end_date,
        clock=clock
    )
    
    # Initialize ticker discoverer based on selection method
    if ticker_selection == "dynamic":
        ticker_discover = DynamicTickerDiscover(
            min_market_cap=min_market_cap,
            max_market_cap=max_market_cap,
            min_price=min_price,
            start_time=start_date
        )
    elif ticker_selection == "ticker-source":
        # Import the BacktestTickerDiscoverAdapter
        from tickers.backtest_ticker_discover_adapter import BacktestTickerDiscoverAdapter
        
        # Import the specified ticker source
        if ticker_source is None:
            raise ValueError("ticker_source must be specified when using ticker-source selection")
        
        try:
            ticker_source_module = importlib.import_module(f"tickers.{ticker_source}")
            
            # Find the first class that implements ITickerSource
            from tickers.tickersource import ITickerSource
            
            ticker_source_class = None
            for attr_name in dir(ticker_source_module):
                attr = getattr(ticker_source_module, attr_name)
                if (isinstance(attr, type) and 
                    attr.__module__ == ticker_source_module.__name__ and 
                    issubclass(attr, ITickerSource) and 
                    attr != ITickerSource):
                    ticker_source_class = attr
                    break
            
            if ticker_source_class is None:
                raise ValueError(f"Could not find any class implementing ITickerSource in module {ticker_source}")
            
            # Initialize the ticker source with the provided parameters
            ticker_source_instance = ticker_source_class(**ticker_source_params)
            
            # Create the backtest adapter
            ticker_discover = BacktestTickerDiscoverAdapter(
                ticker_source=ticker_source_instance,
                start_date=start_date,
                end_date=end_date,
                tickers_per_second=tickers_per_second
            )
            
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to import ticker source {ticker_source}: {e}")
            raise
    else:
        ticker_discover = PresetTickerDiscover(tickers=tickers, start_time=start_date)
    
    # Dynamically import the strategy module
    try:
        strategy_module = importlib.import_module(f"strategies.{strategy_name}")
        
        # Import the newer Strategy interface as well
        from trading_framework.core import Strategy
        
        # Find the first class that implements Strategy
        strategy_class = None
        for attr_name in dir(strategy_module):
            attr = getattr(strategy_module, attr_name)
            if (isinstance(attr, type) and
                attr.__module__ == strategy_module.__name__ and
                 issubclass(attr, Strategy) and
                (attr != Strategy)):  # Skip the base classes themselves
                strategy_class = attr
                break
        
        if strategy_class is None:
            raise ValueError(f"Could not find any class implementing Strategy in module {strategy_name}")
        
        # Initialize strategy with provided parameters
        strategy_factory = lambda: strategy_class(**strategy_params)
        
    except (ImportError, AttributeError) as e:
        logger.error(f"Failed to import strategy {strategy_name}: {e}")
        raise
    
    # Initialize position manager and broker
    position_manager = PositionManager(max_positions=100, risk_per_position=risk_per_position, 
                                      initial_capital=initial_capital, max_position_size=max_position_size)
    local_broker = LoggingBroker(LocalBroker(market_data=market_data, clock=clock, initial_capital=initial_capital))
    
    # Create the execution orchestrator
    orchestrator = ExecutionOrchestrator(
        market_data=backtesting_data,
        ticker_discover=ticker_discover,
        strategy_factory=strategy_factory,
        broker=local_broker,
        position_manager=position_manager
    )
    
    # Run backtest using the orchestrator
    logger.info(f"Starting backtest for {strategy_name} from {start_date} to {end_date}")
    await orchestrator.execute()
    await orchestrator.stop()
    
    # Create the stats logger for the backtest
    stats_logger = BacktestStatsLogger(
        strategy_name=strategy_name,
        strategy_params=strategy_params,
        broker=local_broker,
        start_date=start_date,
        end_date=end_date
    )
    
    # Log backtest statistics
    storage_info = await stats_logger.log_backtest_stats(
        benchmark_ticker=benchmark_ticker,
        run_monte_carlo=run_monte_carlo,
        monte_carlo_simulations=monte_carlo_simulations,
        monte_carlo_days=monte_carlo_days
    )
    
    logger.info(f"Backtest completed and data stored at: {storage_info['base_dir']}")
    
    # Build stats objects to return (for backward compatibility)
    portfolio_stats = await stats_logger.stats_builder.build_portfolio_stats(
        start_date=start_date.date(),
        end_date=end_date.date(),
        benchmark_ticker=benchmark_ticker
    )
    
    trade_stats = await stats_logger.stats_builder.build_trade_stats()
    portfolio_values = await stats_logger.stats_builder.get_daily_portfolio_values(start_date.date(), end_date.date())
    trades_df = await local_broker.get_trades_dataframe()
    
    return {
        "portfolio_stats": portfolio_stats,
        "trade_stats": trade_stats,
        "trades_df": trades_df,
        "portfolio_values": portfolio_values,
        "storage_info": storage_info
    }

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run a backtest with a specified strategy')
    
    parser.add_argument('--strategy', type=str, required=True,
                        help='Name of the strategy module (without .py extension)')
    
    parser.add_argument('--risk', type=float, required=True,
                        help='Risk per position as a percentage of capital')
    
    parser.add_argument('--start-date', type=str, default=None,
                        help='Start date for the backtest (YYYY-MM-DD)')
    
    parser.add_argument('--end-date', type=str, default=None,
                        help='End date for the backtest (YYYY-MM-DD)')
    
    parser.add_argument('--tickers', type=str, default="TQQQ",
                        help='Comma-separated list of tickers to test')
    
    parser.add_argument('--capital', type=float, default=100000,
                        help='Initial capital for the backtest')
    
    parser.add_argument('--benchmark', type=str, default="QQQ",
                        help='Ticker to use as benchmark')
    
    parser.add_argument('--params', type=str, default="{}",
                        help='JSON string of parameters to pass to the strategy')
    
    parser.add_argument('--trade-session', type=str, default="rth", choices=["rth", "full"],
                        help='Trading session type: "rth" (regular trading hours) or "full" (default: "rth")')
    
    parser.add_argument('--ticker-selection', type=str, default="preset", 
                        choices=["preset", "dynamic", "ticker-source"],
                        help='Method to select tickers: "preset", "dynamic", or "ticker-source" (default: "preset")')
    
    parser.add_argument('--min-market-cap', type=float, default=None,
                        help='Minimum market cap for dynamic ticker selection')
    
    parser.add_argument('--max-market-cap', type=float, default=None,
                        help='Maximum market cap for dynamic ticker selection')
    
    parser.add_argument('--min-price', type=float, default=None,
                        help='Minimum price for dynamic ticker selection')
    
    parser.add_argument('--ticker-source', type=str, default=None,
                        help='Name of the ticker source module to use with ticker-source selection')
    
    parser.add_argument('--ticker-source-params', type=str, default="{}",
                        help='JSON string of parameters to pass to the ticker source')
    
    parser.add_argument('--tickers-per-second', type=float, default=10.0,
                        help='How many tickers to yield per second in ticker-source mode')
    
    parser.add_argument('--run-monte-carlo', action='store_true', default=True,
                        help='Run Monte Carlo simulation on trade results')
    
    parser.add_argument('--monte-carlo-simulations', type=int, default=1000,
                        help='Number of Monte Carlo simulations to run')
    
    parser.add_argument('--monte-carlo-days', type=int, default=365,
                        help='Number of days to project in Monte Carlo simulation')
    
    parser.add_argument('--no-log-file', action='store_true',
                        help='Disable output redirection to log file')
    
    parser.add_argument('--max-position-size', type=float, default=None,
                        help='Maximum position size as a percentage of initial capital (e.g., 0.2 for 20%)')
    
    return parser.parse_args()

async def main():
    args = parse_args()
    
    # Parse dates
    eastern = pytz.timezone('US/Eastern')
    start_date = None
    end_date = None
    
    if args.start_date:
        # Set start date to beginning of day (00:00:00)
        start_date = eastern.localize(datetime.strptime(args.start_date, "%Y-%m-%d").replace(hour=4, minute=0))
    
    if args.end_date:
        # Set end date to end of day (23:59:59)
        end_date = eastern.localize(datetime.strptime(args.end_date, "%Y-%m-%d").replace(hour=16, minute=0))
    
    # Parse tickers
    tickers = args.tickers.split(',')
    
    # Parse strategy parameters
    try:
        strategy_params = json.loads(args.params)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON for strategy parameters: {args.params}")
        return
    
    # Parse ticker source parameters
    try:
        ticker_source_params = json.loads(args.ticker_source_params)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON for ticker source parameters: {args.ticker_source_params}")
        return
    
    # Create BacktestStatsLogger for handling log file output
    if not args.no_log_file:
        stats_logger = BacktestStatsLogger(
            strategy_name=args.strategy,
            strategy_params=strategy_params,
            broker=LoggingBroker(None),  # Not needed for just file redirection
            start_date=start_date or datetime.now(),
            end_date=end_date or datetime.now()
        )
        
        with stats_logger.redirect_output_to_file():
            await run_backtest(
                strategy_name=args.strategy,
                risk_per_position=args.risk,
                start_date=start_date,
                end_date=end_date,
                tickers=tickers,
                initial_capital=args.capital,
                strategy_params=strategy_params,
                benchmark_ticker=args.benchmark,
                trade_session=args.trade_session,
                ticker_selection=args.ticker_selection,
                min_market_cap=args.min_market_cap,
                max_market_cap=args.max_market_cap,
                min_price=args.min_price,
                ticker_source=args.ticker_source,
                ticker_source_params=ticker_source_params,
                tickers_per_second=args.tickers_per_second,
                run_monte_carlo=args.run_monte_carlo,
                monte_carlo_simulations=args.monte_carlo_simulations,
                monte_carlo_days=args.monte_carlo_days,
                max_position_size=args.max_position_size
            )
    else:
        await run_backtest(
            strategy_name=args.strategy,
            risk_per_position=args.risk,
            start_date=start_date,
            end_date=end_date,
            tickers=tickers,
            initial_capital=args.capital,
            strategy_params=strategy_params,
            benchmark_ticker=args.benchmark,
            trade_session=args.trade_session,
            ticker_selection=args.ticker_selection,
            min_market_cap=args.min_market_cap,
            max_market_cap=args.max_market_cap,
            min_price=args.min_price,
            ticker_source=args.ticker_source,
            ticker_source_params=ticker_source_params,
            tickers_per_second=args.tickers_per_second,
            run_monte_carlo=args.run_monte_carlo,
            monte_carlo_simulations=args.monte_carlo_simulations,
            monte_carlo_days=args.monte_carlo_days,
            max_position_size=args.max_position_size
        )

if __name__ == "__main__":
    asyncio.run(main()) 