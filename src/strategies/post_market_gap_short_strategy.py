from __future__ import annotations

import logging
from datetime import datetime, timedelta, time
from typing import Any, List, Optional

import numpy as np
import pandas as pd
from pytz import timezone

from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecOutcome, ExecutionReport
from trading_framework.mixins import IntradayStrategy
from strategies.trade_signals import SignalType, TradeSignal, TradeType
from tickers.iscancriteria import IScanCriteria

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class PostMarketGapShortStrategy(IntradayStrategy, IScanCriteria):
    """Short post‑market gap‑ups that roll over below an *n*‑bar SMA."""

    def __init__(
        self,
        *,
        ma_window: int = 10,
        min_gap_up_pct: float = 50.0,
        min_postmkt_dollarvol: float = 250_000,
        stop_loss_pct: float = 150.0,
        exit_time: time = time(19, 55),  # 20:00 ET
        min_price_threshold: float = 0.5,
    ) -> None:
        super().__init__()
        self.ma_window = int(ma_window)
        self.min_gap_up_pct = float(min_gap_up_pct)
        self.min_postmkt_dollarvol = float(min_postmkt_dollarvol)
        self.stop_loss_pct = float(stop_loss_pct)
        self.exit_time = exit_time
        self.post_scan_start_time = time(16, 0)
        self.post_scan_end_time = time(19, 30)
        self.min_price_threshold = float(min_price_threshold)

        logger.info(
            "Initialized PostMarketGapShortStrategy: ma_window=%s, min_gap_up_pct=%s, "
            "min_postmkt_dollarvol=%s, stop_loss_pct=%s, exit_time=%s, "
            "min_price_threshold=%s",
            self.ma_window,
            self.min_gap_up_pct,
            self.min_postmkt_dollarvol,
            self.stop_loss_pct,
            self.exit_time,
            self.min_price_threshold,
        )

    # ──────────────────────────────────────────────────────────────────────
    # Framework‑required methods
    # ----------------------------------------------------------------------
    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]:
        # minute bars with ~5 days of history for gap checks & indicators
        return timedelta(minutes=1), timedelta(days=5)

    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        indicators = self.ctx.get("ind")
        current_ts: datetime = indicators.get("current_timestamp")

        # High-level diagnostic for each call
        logger.debug(
            "%s - process_bar: hist=%d bars, latest=%d bars, in_pos=%s, traded=%s",
            ticker,
            0 if hist is None else len(hist),
            0 if latest is None else len(latest),
            self.in_pos(self.ctx),
            day.custom.get("traded"),
        )

        if hist.empty or latest.empty:
            return None

        logger.debug(
            "%s - timestamps: current_ts=%s, current_time=%s",
            ticker,
            current_ts,
            current_ts.time(),
        )

        # ─── Exit: end‑of‑session ──────────────────────────────────────────
        if self.in_pos(self.ctx) and current_ts.time() >= self.exit_time:
            logger.debug(
                "%s - end-of-session exit: time=%s >= exit_time=%s, qty=%s",
                ticker,
                current_ts.time(),
                self.exit_time,
                day.pos.qty,
            )
            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.CLOSE,
                trade_type=TradeType.BUY,
                price=latest["open"].iloc[0],
                quantity=day.pos.qty,
                metadata={"exit_reason": "END_OF_SESSION"},
                source_bar=latest.iloc[0],
            )
            self.close_pos(self.ctx)
            day.custom["traded"] = True
            logger.info("%s: Liquidated at end of session", ticker)
            return [sig]

        # ─── Exit: stop‑loss ───────────────────────────────────────────────
        if self.in_pos(self.ctx):
            stop_price = day.pos.stop_px
            last_high = hist["high"].iloc[-1]
            logger.debug(
                "%s - stop check: side=%s, last_high=%.4f, stop_price=%.4f",
                ticker,
                day.pos.side,
                last_high,
                stop_price,
            )
            if day.pos.side == "SHORT" and hist["high"].iloc[-1] >= stop_price:
                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.CLOSE,
                    trade_type=TradeType.BUY,
                    price=stop_price,
                    quantity=day.pos.qty,
                    metadata={"exit_reason": "STOP_LOSS"},
                    source_bar=latest.iloc[0],
                )
                self.close_pos(self.ctx)
                day.custom["traded"] = True
                logger.info("%s: Stop‑loss hit (%.2f)", ticker, stop_price)
                return [sig]

        # ─── One trade per day ─────────────────────────────────────────────
        if day.custom.get("traded"):
            logger.debug("%s - already traded today; skipping", ticker)
            return None

        # Only evaluate entries in post‑market session
        if not (self.post_scan_start_time <= current_ts.time() < self.exit_time):
            logger.debug(
                "%s - outside post-market window: now=%s, window=[%s, %s)",
                ticker,
                current_ts.time(),
                self.post_scan_start_time,
                self.exit_time,
            )
            return None

        # ─── Check gap‑up & volume status (update every bar) ───────────────
        is_gap, dollar_vol = self._is_postmkt_gap_up(hist)
        day.custom["stock_status"] = "GAP_UP" if is_gap else "NOT_GAP_UP"
        logger.debug(
            "%s - gap status: is_gap=%s, dollar_vol=%.2f (min=%.2f)",
            ticker,
            is_gap,
            dollar_vol,
            self.min_postmkt_dollarvol,
        )

        if not self.in_pos(self.ctx) and is_gap:
            # Need at least ma_window closed bars for SMA
            if len(hist) < self.ma_window:
                logger.debug(
                    "%s - insufficient bars for SMA: have=%d need=%d",
                    ticker,
                    len(hist),
                    self.ma_window,
                )
                return None

            sma = hist["close"].rolling(self.ma_window).mean().iloc[-1]
            last_close_bar = hist.iloc[-1]

            red_bar = last_close_bar["close"] < last_close_bar["open"]
            below_ma = last_close_bar["close"] < sma

            logger.debug(
                "%s - entry gate: SMA=%.4f, last_open=%.4f, last_close=%.4f, red_bar=%s, below_ma=%s",
                ticker,
                sma,
                last_close_bar["open"],
                last_close_bar["close"],
                red_bar,
                below_ma,
            )

            if red_bar and below_ma and dollar_vol >= self.min_postmkt_dollarvol:
                entry_price = latest["open"].iloc[0]
                stop_price = entry_price * (1 + self.stop_loss_pct / 100)
                qty = self.position_manager.calculate_position_size(
                    ticker=ticker,
                    hist_data=hist,
                    price=entry_price,
                    stop_loss=stop_price,
                )
                if qty <= 0:
                    logger.warning("%s: position size <= 0, skip", ticker)
                    return None

                logger.info(
                    "%s: Entry conditions met → qty=%s at %.2f (stop %.2f; dollar_vol=%.2f)",
                    ticker,
                    qty,
                    entry_price,
                    stop_price,
                    dollar_vol,
                )

                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.OPEN,
                    trade_type=TradeType.SELL,
                    price=entry_price,
                    quantity=qty,
                    metadata={
                        "stop_price": stop_price,
                        "risk": stop_price - entry_price,
                        "entry_type": "POST_MARKET_MA_BREAK",
                        "timestamp": current_ts,
                        "dollar_vol": dollar_vol,
                    },
                    source_bar=latest.iloc[0],
                )

                self.open_pos(
                    self.ctx,
                    side="SHORT",
                    qty=qty,
                    entry_px=entry_price,
                    entry_ts=current_ts,
                    stop_px=stop_price,
                    target_px=0,
                )
                day.custom["traded"] = True
                logger.info(
                    "%s: Shorted %s shares at %.2f (stop %.2f)",
                    ticker,
                    qty,
                    entry_price,
                    stop_price,
                )
                return [sig]

        return None

    # ──────────────────────────────────────────────────────────────────────
    # Helper methods
    # ----------------------------------------------------------------------
    def _is_postmkt_gap_up(self, hist: pd.DataFrame) -> tuple[bool, float]:
        """Return (is_gap_up, dollar_vol_so_far)."""

        """*RTH* close = last bar between 15:00–16:00 of the current day."""
        if hist.empty:
            logger.debug("_is_postmkt_gap_up: hist empty => (False, 0.0)")
            return False, 0.0

        # Current calendar day
        today = hist.index[-1].date()

        # Slice today's RTH close
        rth_df = hist[
            (hist.index.date == today)
            & (hist.index.time >= time(15, 0))
            & (hist.index.time < time(16, 0))
        ]
        if rth_df.empty:
            logger.debug("_is_postmkt_gap_up: rth_df empty => (False, 0.0)")
            return False, 0.0
        rth_close = rth_df["close"].iloc[-1]
        logger.debug("_is_postmkt_gap_up: rth_close=%.4f", rth_close)

        # Slice *today's* post‑market bars
        pm_mask = (hist.index.date == today) & (
            hist.index.time >= self.post_scan_start_time
        )
        pm_df = hist[pm_mask]

        if pm_df.empty:
            logger.debug("_is_postmkt_gap_up: pm_df empty => (False, 0.0)")
            return False, 0.0

        postmkt_high = pm_df["high"].max()
        gap_pct = (postmkt_high - rth_close) / rth_close * 100
        dollar_vol = (pm_df["close"] * pm_df["volume"]).sum()

        is_gap = (gap_pct >= self.min_gap_up_pct) and (
            dollar_vol >= self.min_postmkt_dollarvol
        )
        logger.debug(
            "_is_postmkt_gap_up: postmkt_high=%.4f, gap_pct=%.2f, dollar_vol=%.2f, min_gap=%.2f, min_dvol=%.2f => %s",
            postmkt_high,
            gap_pct,
            dollar_vol,
            self.min_gap_up_pct,
            self.min_postmkt_dollarvol,
            is_gap,
        )
        return is_gap, dollar_vol

    # ──────────────────────────────────────────────────────────────────────
    # Scanner (runs once at/after 16:00 ET)                                
    # ----------------------------------------------------------------------
    def scan(
        self,
        historical_df: pd.DataFrame,
        current_day_df: Optional[pd.DataFrame] = None,
    ) -> List[str]:
        """Return symbols that closed regular hours ≥ `min_price_threshold`
        and are gapping ≥ `min_gap_up_pct` in the first post‑market print."""

        now = datetime.now(timezone("US/Eastern"))
        if now.time() < self.post_scan_start_time or now.time() > self.post_scan_end_time:
            return []  # Only scan once post‑market begins

        df = historical_df.copy()
        if current_day_df is not None and not current_day_df.empty:
            df = pd.concat([df, current_day_df], ignore_index=True)

        # Expect columns: date, symbol, close, day_close (RTH close)
        df["date"] = pd.to_datetime(df["date"]).dt.date
        todays_rows = df[df["date"] == now.date()]

        if "day_close" not in todays_rows.columns:
            logger.warning("scan(): 'day_close' column missing; skipping.")
            return []

        todays_rows = todays_rows.dropna(subset=["close", "day_close"])

        todays_rows["gap_up_pct"] = (
            (todays_rows["close"] - todays_rows["day_close"])
            / todays_rows["day_close"]
            * 100
        )

        matches = todays_rows[
            (todays_rows["close"] >= self.min_price_threshold)
            & (todays_rows["gap_up_pct"] >= self.min_gap_up_pct)
        ]["symbol"].unique().tolist()

        logger.info("Post‑market scan found %d tickers: %s", len(matches), matches)
        return matches

    # ──────────────────────────────────────────────────────────────────────
    # Execution‑report handler                                            
    # ----------------------------------------------------------------------
    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]

        # Exit strategy on failed/partial executions
        logger.debug(
            "%s: on_exec - order_id=%s, outcome=%s, signal=%s, trade_type=%s, filled_qty=%s",
            ticker,
            getattr(report, "order_id", None),
            report.outcome,
            getattr(report.signal, "signal", None),
            getattr(report.signal, "trade_type", None),
            getattr(report, "filled_qty", None),
        )

        if report.outcome != ExecOutcome.FILLED:
            logger.warning(
                "%s: Execution failed with outcome %s: %s. Resetting day context.",
                ticker,
                report.outcome,
                report.broker_msg,
            )
            day.reset()
        else:
            logger.info(
                "%s: Execution filled - signal=%s, trade_type=%s, filled_qty=%s",
                ticker,
                report.signal.signal,
                report.signal.trade_type,
                getattr(report, "filled_qty", None),
            )
        return []  # No follow‑up signals generated
