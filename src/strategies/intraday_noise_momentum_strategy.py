from __future__ import annotations

import logging
from datetime import timedelta, time
from typing import List, Optional, Sequence

import numpy as np
import pandas as pd
from pytz import timezone

from trading_framework.core import BarSlice
from trading_framework.mixins import IntradayStrategy
from trading_framework.day_ctx import DayContext
from strategies.trade_signals import TradeSignal, SignalType, TradeType

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class IntradayNoiseMomentumStrategy(IntradayStrategy):
    """Intraday Noise‑Momentum Strategy

    Streaming implementation of the intraday momentum logic contained in
    ``simulate_intraday_momentum``.  At every *rebalance* bar (``trade_freq``)
    it compares the current close to adaptive σ‑bands that scale with the
    average absolute open‑to‑minute move (``sigma_open``) of the same bucket
    over the last ``vol_lookback`` days.

    LONG  when price > UB and price > VWAP
    SHORT when price < LB and price < VWAP

        UB = max(open, prev_close) · (1 + band_mult · σ_open)
        LB = min(open, prev_close) · (1 − band_mult · σ_open)

    Positions are flipped / flattened on the next rebalance if the signal
    changes, or liquidated at ``exit_time`` (default 15:59 ET).  Sizing can
    either be *notional* (``full_notional``) or *vol‑target* where the
    leverage cap is determined by the ratio ``target_vol / d‑vol`` capped at
    ``max_leverage``.
    """

    # ---------------- constructor ---------------- #
    def __init__(
        self,
        *,
        trade_freq: int = 30,
        band_mult: float = 1.0,
        vol_lookback: int = 15,
        sizing_type: str = "vol_target",  # or "full_notional"
        target_vol: float = 0.03,
        max_leverage: float = 4.0,
        exit_time: time = time(15, 59),
    ) -> None:
        super().__init__()

        self.trade_freq = trade_freq
        self.band_mult = band_mult
        self.vol_lookback = vol_lookback
        self.sizing_type = sizing_type
        self.target_vol = target_vol
        self.max_leverage = max_leverage
        self.exit_time = exit_time

        self.market_open = time(9, 30)
        self.est = timezone("US/Eastern")

        # orchestrator injects concrete PositionManager impl
        self.position_manager = None
        
        logger.info(
            "Initialized IntradayNoiseMomentumStrategy: trade_freq=%s, band_mult=%.2f, "
            "vol_lookback=%s, sizing_type=%s, target_vol=%.4f, max_leverage=%.1f, exit_time=%s",
            self.trade_freq,
            self.band_mult,
            self.vol_lookback,
            self.sizing_type,
            self.target_vol,
            self.max_leverage,
            self.exit_time,
        )

    # ---------------- life‑cycle ---------------- #
    def prepare(self, meta) -> tuple[timedelta, timedelta]:
        """Tell the orchestrator we want minute bars and ~``vol_lookback``
        days of history to build σ‑open and daily volatility.
        """
        logger.debug("Preparing strategy with %d days of history", self.vol_lookback + 10)
        return timedelta(minutes=1), timedelta(days=self.vol_lookback + 10)

    # ---------------- bar handler ---------------- #
    def process_bar(self, bars: BarSlice) -> Optional[Sequence[TradeSignal]]:
        hist, latest = bars
        
        ticker = self.ctx.get("ticker")
        logger.debug("%s - process_bar called: hist %d bars, latest %d bars", 
                    ticker, len(hist) if not hist.empty else 0, 
                    len(latest) if not latest.empty else 0)
        
        if hist.empty or latest.empty:
            logger.debug("%s - hist.empty=%s, latest.empty=%s - skipping processing", 
                        ticker, hist.empty, latest.empty)
            return None
        
        hist = hist.between_time("09:30", "16:00").sort_index()
        if not isinstance(latest.index, pd.DatetimeIndex):
            latest.index = pd.to_datetime(latest.index)
        latest = latest.between_time("09:30", "16:00").sort_index()
                
        if hist.empty or latest.empty:
            logger.debug("%s - After filtering for market hours: hist.empty=%s, latest.empty=%s - skipping", 
                        ticker, hist.empty, latest.empty)
            return None

        last_bar = hist.iloc[-1]
        latest_bar = latest.iloc[0]
        ts: pd.Timestamp = latest_bar.name

        # ignore anything outside the regular cash session
        if ts.time() < self.market_open or ts.time() > time(16, 0):
            logger.debug("%s - %s Outside regular session hours (09:30-16:00) - skipping", 
                        ticker, ts)
            return None

        day: DayContext = self.ctx["day"]

        # -------- intraday aggregates (VWAP, open px) -------- #
        if "open_px" not in day.custom:
            # first bar of the session
            day.custom.update(
                open_px=latest_bar['open'],
            )
            logger.info("%s - %s First bar of session, setting open_px=%.2f", 
                       ticker, ts, latest_bar['open'])
        
        # Calculate VWAP directly from historical bars for current day
        today_bars = hist[hist.index.date == ts.date()].copy()
        
        # Calculate VWAP if we have data with volume
        if not today_bars.empty and today_bars['volume'].sum() > 0:
            today_bars['turnover'] = today_bars['close'] * today_bars['volume']
            vwap = today_bars['turnover'].sum() / today_bars['volume'].sum()
            logger.info("%s - %s Calculated VWAP=%.2f from %d bars with total volume=%d", 
                       ticker, ts, vwap, len(today_bars), today_bars['volume'].sum())
        else:
            # Fallback if no volume or data
            vwap = last_bar.close
            logger.debug("%s - %s No volume data available, using close=%.2f as VWAP", 
                       ticker, ts, vwap)
            
        open_px = day.custom["open_px"]

        # previous session close
        prev_close = self._get_prev_close(hist, ts)
        if prev_close is None:
            logger.debug("%s - %s Unable to determine previous close - skipping", ticker, ts)
            return None  # not enough data yet

        # minute bucket (1‑based index)
        minute_of_day = int(
            ((ts - ts.normalize()) / pd.Timedelta(minutes=1)) - (9 * 60 + 30) + 1
        )
        if minute_of_day <= 0:
            logger.debug("%s - %s Invalid minute_of_day=%d - skipping", ticker, ts, minute_of_day)
            return None

        # σ‑open for this minute bucket
        sigma_open = self._compute_sigma_open(hist, ts, minute_of_day)
        if sigma_open is None or np.isnan(sigma_open):
            logger.debug("%s - %s Unable to compute sigma_open for minute=%d - skipping", 
                        ticker, ts, minute_of_day)
            return None

        # band levels
        upper_band = max(open_px, prev_close) * (1 + self.band_mult * sigma_open)
        lower_band = min(open_px, prev_close) * (1 - self.band_mult * sigma_open)
        
        logger.info(
            "%s - %s Band calculations: open_px=%.2f, prev_close=%.2f, sigma_open=%.5f, "
            "upper_band=%.2f, lower_band=%.2f, close=%.2f, vwap=%.2f",
            ticker, ts, open_px, prev_close, sigma_open, upper_band, lower_band, last_bar.close, vwap
        )

        signal = 0
        if last_bar.close > upper_band and last_bar.close > vwap:
            signal = 1
            logger.info("%s - %s LONG signal triggered: close > upper_band AND close > vwap", ticker, ts)
        elif last_bar.close < lower_band and last_bar.close < vwap:
            signal = -1
            logger.info("%s - %s SHORT signal triggered: close < lower_band AND close < vwap", ticker, ts)
        else:
            logger.debug("%s - %s No signal triggered", ticker, ts)

        rebalance_bar = (minute_of_day % self.trade_freq == 0)
        sigs: List[TradeSignal] = []

        # -------------- end‑of‑day liquidation -------------- #
        if self.in_pos(self.ctx) and ts.time() >= self.exit_time:
            logger.info("%s - %s End-of-day liquidation triggered at exit_time=%s", 
                       ticker, ts, self.exit_time)
            sigs += self._exit_market(ticker, latest, reason="END_OF_DAY")
            return sigs

        # -------------- rebalance logic -------------- #
        if not rebalance_bar:
            logger.debug("%s - %s Not a rebalance bar (minute %d %% %d != 0) - skipping", 
                        ticker, ts, minute_of_day, self.trade_freq)
            return sigs or None

        logger.info("%s - %s Rebalance bar (minute %d)", ticker, ts, minute_of_day)
        
        current_side = (
            "LONG" if self.in_pos(self.ctx) and day.pos.side == "LONG" else
            "SHORT" if self.in_pos(self.ctx) else None
        )
        current_signal = 1 if current_side == "LONG" else -1 if current_side == "SHORT" else 0

        logger.info("%s - %s Current position: side=%s, signal=%d, new_signal=%d", 
                    ticker, ts, current_side, current_signal, signal)

        if current_signal != signal:
            logger.info(
                "%s - %s Signal change detected: %d → %d, position action required", 
                ticker, ts, current_signal, signal
            )
            
            # close existing pos first
            if current_signal != 0:
                logger.info("%s - %s Closing existing %s position", ticker, ts, current_side)
                sigs += self._exit_market(ticker, latest, reason="REBALANCE")
            
            # open new side, if any
            if signal != 0:
                logger.info("%s - %s Opening new position with signal=%d", ticker, ts, signal)
                entry_sig = self._build_entry(ticker, hist, latest, signal)
                if entry_sig:
                    sigs.append(entry_sig)

        return sigs or None

    # ---------------- helper funcs ---------------- #
    def _compute_sigma_open(
        self, hist: pd.DataFrame, ts: pd.Timestamp, minute_bucket: int
    ) -> Optional[float]:
        """Rolling mean of |move from open| for *minute_bucket* across the
        last ``vol_lookback`` complete days (shifted by one day).
        """
        ticker = self.ctx.get("ticker")
        logger.debug("%s - _compute_sigma_open called for minute_bucket=%d, ts=%s", 
                    ticker, minute_bucket, ts)
                
        if hist.empty:
            logger.debug("%s - _compute_sigma_open: hist empty - returning None", ticker)
            return None

        hist_prev = hist[hist.index.date < ts.date()].copy()
        if hist_prev.empty:
            logger.debug("%s - _compute_sigma_open: no previous day data - returning None", ticker)
            return None

        hist_prev["day"] = hist_prev.index.date
        open_map = hist_prev.groupby("day")["open"].first().to_dict()
        hist_prev["move_open"] = (
            hist_prev["close"] / hist_prev["day"].map(open_map) - 1
        ).abs()
        hist_prev["minute_of_day"] = (
            ((hist_prev.index - hist_prev.index.normalize()) / pd.Timedelta(minutes=1))
            - (9 * 60 + 30) + 1
        ).round().astype(int)

        md_df = hist_prev[hist_prev["minute_of_day"] == minute_bucket]
        if md_df.empty:
            logger.debug("%s - _compute_sigma_open: no data for minute_bucket=%d - returning None", 
                        ticker, minute_bucket)
            return None

        # one point per day is enough – keep most recent ``vol_lookback`` days
        md_df = (
            md_df.groupby("day").tail(1).sort_values("day", ascending=False).head(self.vol_lookback)
        )
        
        if md_df.empty:
            logger.debug("%s - _compute_sigma_open: filtered data is empty - returning None", ticker)
            return None
            
        sigma = md_df["move_open"].mean()
        logger.debug("%s - _compute_sigma_open: calculated sigma_open=%.5f from %d days of data", 
                    ticker, sigma, len(md_df))
        return sigma

    def _get_prev_close(self, hist: pd.DataFrame, ts: pd.Timestamp) -> Optional[float]:
        ticker = self.ctx.get("ticker")
        logger.debug("%s - _get_prev_close called for ts=%s", ticker, ts)
        
        prev_day_hist = hist[hist.index.date < ts.date()]
        if prev_day_hist.empty:
            logger.debug("%s - _get_prev_close: no previous day data - returning None", ticker)
            return None
            
        prev_close = prev_day_hist.iloc[-1]["close"]
        logger.debug("%s - _get_prev_close: found prev_close=%.2f from %s", 
                    ticker, prev_close, prev_day_hist.iloc[-1].name)
        return prev_close

    def _compute_daily_dvol(self, hist: pd.DataFrame, ts: pd.Timestamp) -> Optional[float]:
        ticker = self.ctx.get("ticker")
        logger.debug("%s - _compute_daily_dvol called for ts=%s", ticker, ts)
        
        daily = hist[hist.index.date <= ts.date()].copy()
        if daily.empty:
            logger.debug("%s - _compute_daily_dvol: daily data empty - returning None", ticker)
            return None
            
        daily["day"] = daily.index.date
        closes = daily.groupby("day")["close"].last()
        
        if len(closes) < self.vol_lookback + 1:
            logger.debug("%s - _compute_daily_dvol: not enough days (%d < %d) - returning None", 
                        ticker, len(closes), self.vol_lookback + 1)
            return None
            
        dvol = closes.pct_change().rolling(self.vol_lookback).std().shift(1)
        result = dvol.iloc[-1]
        
        logger.debug("%s - _compute_daily_dvol: calculated dvol=%.5f from %d days of data", 
                    ticker, result, self.vol_lookback)
        return result

    # ---------------- entry / exit ---------------- #
    def _build_entry(
        self,
        ticker: str,
        hist: pd.DataFrame,
        latest: pd.DataFrame,
        signal: int,
    ) -> Optional[TradeSignal]:
        ts = latest.iloc[0].name
        logger.debug("%s - %s _build_entry called with signal=%d", ticker, ts, signal)
        
        side = "LONG" if signal == 1 else "SHORT"
        trade_type = TradeType.BUY if side == "LONG" else TradeType.SELL
        
        # Get the latest bar
        bar = latest.iloc[0]
        # Use the open price from the latest bar for entry
        entry_px = bar.open

        # --- sizing ---
        if self.sizing_type == "vol_target":
            dvol = self._compute_daily_dvol(hist, bar.name)
            if dvol is not None and not np.isnan(dvol) and dvol > 0:
                leverage = min(self.target_vol / dvol, self.max_leverage)
                logger.info("%s - %s Vol-target sizing: dvol=%.5f, target_vol=%.5f, leverage=%.2f", 
                           ticker, ts, dvol, self.target_vol, leverage)
            else:
                leverage = self.max_leverage
                logger.error("%s - %s Invalid dvol, using max_leverage=%.2f", 
                           ticker, ts, leverage)
        else:
            leverage = 1.0
            logger.info("%s - %s Full notional sizing: leverage=%.2f", ticker, ts, leverage)

        qty = self.position_manager.calculate_position_size_by_leverage(
            price=entry_px,
            notional_multiplier=leverage,
        )
        if not qty or qty <= 0:
            logger.warning("%s - %s Invalid qty=%s – skipping entry", ts, ticker, qty)
            return None

        # store position (no explicit stop/target)
        self.open_pos(self.ctx, side, qty, entry_px, bar.name, stop_px=-1, target_px=-1)

        meta = {
            "signal": signal,
            "leverage": leverage,
            "sizing_type": self.sizing_type,
        }

        logger.info(
            "%s - %s INM ENTRY %s %s @ %.2f qty %s leverage %.2f",
            ts, ticker, trade_type.name, ticker, entry_px, qty, leverage
        )

        return TradeSignal(
            signal=SignalType.OPEN,
            symbol=ticker,
            trade_type=trade_type,
            price=entry_px,
            quantity=qty,
            metadata=meta,
            source_bar=bar,
        )

    def _exit_market(
        self,
        ticker: str,
        latest: pd.DataFrame,
        reason: str = "",
    ) -> List[TradeSignal]:
        ts = latest.iloc[0].name
        pos = self.ctx["day"].pos
        logger.error("%s - %s _exit_market called with reason=%s", ticker, ts, reason)
        
        # Get the latest bar
        bar = latest.iloc[0]
        # Use the open price from the latest bar for exit
        px = bar.open
        trade_type = TradeType.SELL if pos.side == "LONG" else TradeType.BUY

        logger.info(
            "%s - %s INM EXIT %s %s @ %.2f qty %s reason %s",
            ts, ticker, trade_type.name, ticker, px, pos.qty, reason
        )

        sig = TradeSignal(
            symbol=ticker,
            signal=SignalType.CLOSE,
            trade_type=trade_type,
            price=px,
            quantity=pos.qty,
            metadata={"exit_reason": reason},
            source_bar=bar,
        )
        self.close_pos(self.ctx)
        return [sig]