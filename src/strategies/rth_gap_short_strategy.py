from __future__ import annotations

"""
Regular‑Hours Gap‑EMA Short Strategy (live‑trading implementation)
-----------------------------------------------------------------

Entry
  - During RTH only (09:30–15:30 ET): first closed 1‑minute bar where
    - the bar is red (close < open), and
    - the bar's close is below an EMA(`ema_span`) of closes, and
    - the bar's close indicates a gap‑up ≥ `min_gap_up_pct` vs previous day's RTH close, and
    - cumulative RTH dollar volume up to the signal ≥ `min_cumulative_dollar_volume`, and
    - price ≥ `min_price_threshold`.
  - Execute at the next (forming) bar's open.

Exit
  - Stop‑loss: fixed `stop_loss_pct` percent above entry.
  - End‑of‑day: close any open position at 15:55 ET.

Position sizing is delegated to the supplied `position_manager`.

This class follows the same public interface as other Intraday strategies so it
can be dropped into the same trading framework. No scanner is provided; plug in
an external one (e.g., `PreMarketGapScanner` configured for RTH hours).
"""

from typing import Optional, List, Any
from datetime import datetime, timedelta, time
import logging

import pandas as pd

from trading_framework.mixins import IntradayStrategy
from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome

from strategies.trade_signals import TradeSignal, SignalType, TradeType


logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class RTHGapEMAShortStrategy(IntradayStrategy):
    """Live strategy that shorts RTH gap‑ups breaking below an EMA."""

    def __init__(
        self,
        *,
        ema_span: int = 5,
        min_gap_up_pct: float = 70.0,
        min_cumulative_dollar_volume: float = 250_000,
        min_price_threshold: float = 1.0,
        stop_loss_pct: float = 150.0,
        entry_end_time: time = time(15, 30),
        exit_time: time = time(15, 55),
    ) -> None:
        super().__init__()
        self.ema_span = int(ema_span)
        self.min_gap_up_pct = float(min_gap_up_pct)
        self.min_cumulative_dollar_volume = float(min_cumulative_dollar_volume)
        self.min_price_threshold = float(min_price_threshold)
        self.stop_loss_pct = float(stop_loss_pct)
        self.entry_end_time = entry_end_time
        self.exit_time = exit_time
        self.market_open_time = time(9, 30)
        self.market_close_time = time(16, 0)
        self._first_target_qty: float | None = None

        logger.info(
            (
                "Initialized RTHGapEMAShortStrategy: ema_span=%s, min_gap_up_pct=%s, "
                "min_cum_dollar_vol=%s, min_price_threshold=%s, stop_loss_pct=%s, "
                "entry_end_time=%s, exit_time=%s"
            ),
            self.ema_span,
            self.min_gap_up_pct,
            self.min_cumulative_dollar_volume,
            self.min_price_threshold,
            self.stop_loss_pct,
            self.entry_end_time,
            self.exit_time,
        )

    # Framework‑required methods
    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]:
        # 1‑minute bars, 5‑day look‑back
        return timedelta(minutes=1), timedelta(days=5)

    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars  # `hist` = closed bars; `latest` = current forming bar
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        indicators = self.ctx.get("ind")
        current_ts: datetime = indicators.get("current_timestamp")  # provided by framework

        logger.debug(
            "%s - process_bar at %s: hist=%d, latest=%d",
            ticker,
            current_ts,
            len(hist),
            len(latest),
        )

        if hist.empty or latest.empty:
            return None

        # End‑of‑day exit
        if self.in_pos(self.ctx) and current_ts.time() >= self.exit_time:
            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.CLOSE,
                trade_type=TradeType.BUY,
                price=latest["open"].iloc[0],
                quantity=day.pos.qty,
                metadata={"exit_reason": "END_OF_DAY"},
                source_bar=latest.iloc[0],
            )
            self.close_pos(self.ctx)
            self._first_target_qty = None
            day.custom["traded"] = True
            logger.info("%s: End‑of‑day exit", ticker)
            return [sig]

        # Stop‑loss check against last closed bar's high
        if self.in_pos(self.ctx):
            stop_price = day.pos.stop_px
            if day.pos.side == "SHORT" and hist["high"].iloc[-1] >= stop_price:
                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.CLOSE,
                    trade_type=TradeType.BUY,
                    price=stop_price,
                    quantity=day.pos.qty,
                    metadata={"exit_reason": "STOP_LOSS"},
                    source_bar=latest.iloc[0],
                )
                self.close_pos(self.ctx)
                self._first_target_qty = None
                day.custom["traded"] = True
                logger.info("%s: Stop‑loss hit (%.2f)", ticker, stop_price)
                return [sig]

        # One trade per day
        if day.custom.get("traded"):
            return None

        # Entry logic (RTH only)
        now_t = current_ts.time()
        if not self.in_pos(self.ctx) and (self.market_open_time <= now_t <= self.entry_end_time):
            if len(hist) < max(self.ema_span, 2):
                return None

            # Restrict to today's RTH for signal evaluation
            today_date = latest.index[-1].date()
            hist_today_rth = hist[(hist.index.date == today_date) &
                                  (hist.index.time >= self.market_open_time) &
                                  (hist.index.time < self.market_close_time)]
            if hist_today_rth.empty:
                return None

            # Compute EMA on all closed bars up to now
            ema_series = hist["close"].ewm(span=self.ema_span, adjust=False).mean()
            last_closed = hist.iloc[-1]
            last_close = float(last_closed["close"])  # signal bar close
            last_open = float(last_closed["open"])    # signal bar open
            ema_val = float(ema_series.iloc[-1])

            # Price and EMA conditions
            red_bar = last_close < last_open
            below_ema = last_close < ema_val
            price_ok = last_close >= self.min_price_threshold

            # Previous day's RTH close
            prev_close = self._get_prev_rth_close(hist, today_date)
            if prev_close is None or prev_close <= 0:
                return None
            gap_pct = (last_close - prev_close) / prev_close * 100.0

            # Cumulative dollar volume up to signal (today RTH only)
            hist_today_rth = hist_today_rth.copy()
            hist_today_rth["dollar_volume"] = hist_today_rth["close"] * hist_today_rth["volume"]
            cum_dollar_vol = float(hist_today_rth["dollar_volume"].sum())

            logger.debug(
                (
                    "%s - RTH entry eval: last_close=%.4f last_open=%.4f ema=%.4f red=%s below_ema=%s "
                    "gap_pct=%.2f min_gap=%.2f price_ok=%s cum_dvol=%.0f min_cum_dvol=%.0f"
                ),
                ticker,
                last_close,
                last_open,
                ema_val,
                red_bar,
                below_ema,
                gap_pct,
                self.min_gap_up_pct,
                price_ok,
                cum_dollar_vol,
                self.min_cumulative_dollar_volume,
            )

            if red_bar and below_ema and price_ok and gap_pct >= self.min_gap_up_pct and cum_dollar_vol >= self.min_cumulative_dollar_volume:
                entry_price = float(latest["open"].iloc[0])  # enter at next bar open
                if entry_price <= 0:
                    return None
                stop_price = entry_price * (1 + self.stop_loss_pct / 100.0)

                calculated_qty = self.position_manager.calculate_position_size(
                    ticker=ticker,
                    hist_data=hist,
                    price=entry_price,
                    stop_loss=stop_price,
                )
                if calculated_qty <= 0:
                    logger.warning("%s: position size <= 0, skip", ticker)
                    return None

                if self._first_target_qty is None:
                    self._first_target_qty = calculated_qty
                final_qty = min(self._first_target_qty, calculated_qty)
                if final_qty <= 0:
                    return None

                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.OPEN,
                    trade_type=TradeType.SELL,
                    price=entry_price,
                    quantity=final_qty,
                    metadata={
                        "stop_price": stop_price,
                        "risk": stop_price - entry_price,
                        "entry_type": f"RTH_EMA{self.ema_span}_BREAK",
                        "timestamp": current_ts,
                        "gap_pct": gap_pct,
                        "cum_dollar_vol": cum_dollar_vol,
                    },
                    source_bar=latest.iloc[0],
                )

                self.open_pos(
                    self.ctx,
                    side="SHORT",
                    qty=final_qty,
                    entry_px=entry_price,
                    entry_ts=current_ts,
                    stop_px=stop_price,
                    target_px=0,
                )
                day.custom["traded"] = True
                logger.info("%s: Shorted %s shares at %.4f (stop %.4f)", ticker, final_qty, entry_price, stop_price)
                return [sig]

        return None

    # Helper methods
    def _get_prev_rth_close(self, hist: pd.DataFrame, today_date) -> float | None:
        """Return the previous day's RTH close price, or None if unavailable."""
        if hist.empty:
            return None
        prev_mask = hist.index.date < today_date
        if not prev_mask.any():
            return None
        prev_df = hist[prev_mask]
        prev_rth = prev_df[(prev_df.index.time >= self.market_open_time) & (prev_df.index.time < self.market_close_time)]
        if prev_rth.empty:
            return None
        return float(prev_rth["close"].iloc[-1])

    # Execution‑report handler
    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]

        if report.outcome != ExecOutcome.FILLED:
            # Reset the day context to allow the strategy to try again later
            logger.warning(
                "%s: Execution failed with outcome %s: %s. Resetting day context.",
                ticker,
                report.outcome,
                report.broker_msg,
            )
            day.reset()
            return []
        return []



