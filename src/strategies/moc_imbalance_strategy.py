from __future__ import annotations

import logging
from datetime import timedelta, time
from typing import List, Optional, Sequence

import numpy as np
import pandas as pd
from pytz import timezone

from trading_framework.core import BarSlice
from trading_framework.mixins import IntradayStrategy
from trading_framework.day_ctx import DayContext
from strategies.trade_signals import TradeSignal, SignalType, TradeType

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class MOCImbalanceStrategy(IntradayStrategy):
    """MOC‑Imbalance Momentum (streaming)

    Real‑time implementation of the ``simulate_moc_pop`` back‑test.

    **Key differences / simplifications**
    * **Futures only** – equity branch is TODO.
    * **Fixed sizing** – ``contract_qty`` contracts per trade (no vol‑target).
    * Trades once per day:
        * Evaluate 15:50 ET bar
        * Enter at 15:51 ET *open*
        * Exit at 15:58 ET *open*
    """

    # ---------------- constructor ---------------- #
    def __init__(
        self,
        *,
        threshold_sd: float = 1.5,
        contract_qty: int = 1,
        exit_time: time = time(15, 58),
        roll_window: int = 60,
        # ---- sizing (futures) ----
        target_vol: float = 0.02,
        max_leverage_futures: float = 8.0,
        contract_multiplier: float = 20.0,
        max_contracts: Optional[int] = None,
    ) -> None:
        super().__init__()
        self.threshold_sd = threshold_sd
        self.contract_qty = contract_qty
        self.exit_time = exit_time
        self.roll_window = roll_window
        self.target_vol = target_vol
        self.max_leverage_futures = max_leverage_futures
        self.contract_multiplier = contract_multiplier
        self.max_contracts = max_contracts

        self.market_open = time(9, 30)
        self.est = timezone("US/Eastern")

        # orchestrator will inject a concrete PositionManager impl
        self.position_manager = None

        logger.info(
            "Initialized MOCImbalanceStrategy: threshold_sd=%.2f, "
            "contract_qty=%s, exit_time=%s, roll_window=%d, "
            "target_vol=%.4f, max_lev=%.2f, contract_mult=%.2f, max_contracts=%s",
            self.threshold_sd,
            self.contract_qty,
            self.exit_time,
            self.roll_window,
            self.target_vol,
            self.max_leverage_futures,
            self.contract_multiplier,
            self.max_contracts,
        )

    # ---------------- life‑cycle ---------------- #
    def prepare(self, meta) -> tuple[timedelta, timedelta]:
        """Request minute bars and a couple of hours of history."""
        # 1‑minute bars and ~7 hours history (safely > roll_window)
        return timedelta(minutes=1), timedelta(hours=7)

    # ---------------- bar handler ---------------- #
    def process_bar(self, bars: BarSlice) -> Optional[Sequence[TradeSignal]]:
        hist, latest = bars

        ticker = self.ctx.get("ticker")
        if hist.empty or latest.empty:
            return None

        # regular trading hours only
        hist = hist.between_time("09:30", "16:00").sort_index()
        latest = latest.between_time("09:30", "16:00").sort_index()
        if hist.empty or latest.empty:
            return None

        bar = latest.iloc[0]
        ts: pd.Timestamp = bar.name
        day: DayContext = self.ctx["day"]

        # restrict history to today's bars (ensures 09:30 open is present)
        hist_today = hist[hist.index.date == ts.date()]
        if hist_today.empty:
            return None

        # -------- evaluate imbalance @ 15:51 (use completed 15:50 bar) -------- #
        if ts.time() == time(15, 51):
            # The latest bar is the 15:51 *open* (close not yet known).
            # Use the last completed bar in ``hist`` (15:50) to evaluate
            # the imbalance.
            bar_1550 = hist.iloc[-1]

            # r350: 15:50 close vs 15:50 open
            try:
                r350 = (bar_1550.close / bar_1550.open) - 1
            except ZeroDivisionError:
                r350 = np.nan

            # intraday σ of 1-min returns (last ``roll_window`` bars)
            ret1 = hist["close"].pct_change().iloc[-self.roll_window:]
            roll_sd = ret1.std()

            side = 0  # no-trade by default
            if not np.isnan(roll_sd) and abs(r350) >= self.threshold_sd * roll_sd:
                # 09:30 session open price – first bar of today
                open_px = hist_today.iloc[0].open
                bias_same_dir = np.sign(r350) == np.sign(bar_1550.close - open_px)
                if bias_same_dir:
                    side = 1 if r350 > 0 else -1

            # persist for next bars
            day.custom["imbalance_side"] = side
            day.custom["entry_done"] = False

            logger.info(
                "%s - %s Imbalance eval: r350=%.4f, roll_sd=%.5f, side=%s",
                ticker,
                ts,
                r350,
                roll_sd,
                side,
            )
            # Do NOT return here – entry logic follows in the same bar

        sigs: List[TradeSignal] = []

        # -------- entry @ 15:51 -------- #
        if ts.time() == time(15, 51) and not day.custom.get("entry_done", False):
            side = day.custom.get("imbalance_side", 0)
            if side != 0:
                sigs.append(
                    self._build_entry(
                        ticker=ticker,
                        bar=bar,
                        side=side,
                        hist_today=hist_today,
                    )
                )
                day.custom["entry_done"] = True

        # -------- exit at / after 15:58 -------- #
        if self.in_pos(self.ctx) and ts.time() >= self.exit_time:
            sigs += self._exit_market(ticker, bar, reason="END_OF_DAY")

        return sigs or None

    # ---------------- helpers ---------------- #
    def _build_entry(
        self,
        *,
        ticker: str,
        bar: pd.Series,
        side: int,
        hist_today: pd.DataFrame,
    ) -> TradeSignal:
        ts = bar.name
        trade_type = TradeType.BUY if side == 1 else TradeType.SELL
        entry_px = bar.open

        # --- sizing: futures vol-target (single mode) ---
        qty = self.contract_qty
        leverage = None
        dvol = self._compute_daily_dvol(hist_today, ts)
        # If user wants fixed contracts, allow target_vol=1 to pick max_contracts
        if self.max_contracts is not None and self.target_vol >= 0.999:
            qty = int(self.max_contracts)
            leverage = None  # not meaningful in fixed-contract mode
        else:
            if dvol is not None and not np.isnan(dvol) and dvol > 0:
                leverage = min(self.target_vol / dvol, self.max_leverage_futures)
            else:
                leverage = self.max_leverage_futures

            try:
                notional = float(self.position_manager.initial_capital) * float(leverage)
                denom = max(entry_px * self.contract_multiplier, 1e-9)
                qty = max(1, int(round(notional / denom)))
            except Exception:
                qty = self.contract_qty

        if self.max_contracts is not None:
            qty = max(1, min(qty, int(self.max_contracts)))

        self.open_pos(
            self.ctx,
            "LONG" if side == 1 else "SHORT",
            qty,
            entry_px,
            ts,
            stop_px=-1,
            target_px=-1,
        )

        logger.info(
            "%s - %s ENTRY %s %s @ %.2f qty %s%s",
            ticker,
            ts,
            trade_type.name,
            ticker,
            entry_px,
            qty,
            f" lev={leverage:.2f}" if leverage is not None else "",
        )

        return TradeSignal(
            symbol=ticker,
            signal=SignalType.OPEN,
            trade_type=trade_type,
            price=entry_px,
            quantity=qty,
            metadata={
                "strategy": "MOC_IMBALANCE",
                "leverage": leverage,
            },
            source_bar=bar,
        )

    def _compute_daily_dvol(self, hist_today: pd.DataFrame, ts: pd.Timestamp) -> Optional[float]:
        """Estimate daily volatility from intraday 1-min returns up to ts.

        dvol ≈ std(1m returns) × sqrt(390)
        """
        try:
            up_to_now = hist_today.loc[:ts]
            ret1 = up_to_now["close"].pct_change().dropna()
            if ret1.empty:
                return None
            return float(ret1.std()) * float(np.sqrt(390))
        except Exception:
            return None

    def _exit_market(
        self,
        ticker: str,
        bar: pd.Series,
        *,
        reason: str = "",
    ) -> List[TradeSignal]:
        ts = bar.name
        pos = self.ctx["day"].pos
        trade_type = TradeType.SELL if pos.side == "LONG" else TradeType.BUY

        logger.info(
            "%s - %s EXIT %s %s @ %.2f qty %s reason=%s",
            ticker,
            ts,
            trade_type.name,
            ticker,
            bar.open,
            pos.qty,
            reason,
        )

        sig = TradeSignal(
            symbol=ticker,
            signal=SignalType.CLOSE,
            trade_type=trade_type,
            price=bar.open,
            quantity=pos.qty,
            metadata={"exit_reason": reason},
            source_bar=bar,
        )
        self.close_pos(self.ctx)
        return [sig]


# -------------------------------------------------------------------
# TODO: Add equity support (commission handling, leverage caps, share sizing)
# -------------------------------------------------------------------
