from typing import Optional, Tu<PERSON>, Dict, Any, List, TypedDict
from datetime import timedelta, time, datetime
import pandas as pd
import numpy as np
import logging
from pytz import timezone

from trading_framework.mixins import IntradayStrategy
from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome

from strategies.trade_signals import TradeSignal, SignalType, TradeType
from tickers.iscancriteria import IScanCriteria

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class OPBShortStrategy(IntradayStrategy, IScanCriteria):
    """
    Open Price Break **Short** Strategy (refactored to the *trading_framework*).

    **April 23 2025 update** – *minimum‑price enforcement now happens **only** in
    the scanner:*

    * `min_price_threshold` (default ``$1``) is compared against the ticker's
      **current (intraday) price** inside :py:meth:`scan`.  
      Tickers below the threshold are simply not returned, so the strategy
      never starts tracking them.
    * There is **no price gate** inside :py:meth:`process_bar` anymore – once a
      symbol has passed the scanner it is free to trade.
    """
    def __init__(self, min_price_threshold: float = 1.0, entry_window_minutes: int = 5):
        super().__init__()
        # Strategy parameters
        self.min_gap_up_pct = 50.0
        self.min_volume_dollars = 250_000
        self.stop_loss_pct = 1000.0
        self.exit_time = time(15, 55)
        self.market_open_time = time(9, 30)
        
        # New entry window configuration
        self.entry_window_minutes = entry_window_minutes
      
        self.min_price_threshold = float(min_price_threshold)

        logger.info("Initialized OPBShortStrategy with min_gap_up_pct=%s, min_volume_dollars=%s, stop_loss_pct=%s, exit_time=%s, entry_window_minutes=%s, min_price_threshold=%s",
                    self.min_gap_up_pct, self.min_volume_dollars, self.stop_loss_pct, self.exit_time, self.entry_window_minutes, self.min_price_threshold)

    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]:
        # 1-minute bars, lookback 3 days
        return timedelta(minutes=1), timedelta(days=3)

    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars
        ticker = self.ctx.get("ticker")
        if hist.empty or latest.empty:
            logger.debug("%s: Missing data - hist_empty=%s, latest_empty=%s", ticker, hist.empty, latest.empty)
            return None

        day = self.ctx["day"]
        indicators = self.ctx.get("ind")
        current_timestamp = indicators.get("current_timestamp")
        current_time = current_timestamp.time()
        current_date = current_timestamp.date()
        
        # logger.debug("%s: Processing bar at %s", ticker, current_timestamp)

        # End-of-day exit
        if self.in_pos(self.ctx) and current_time >= self.exit_time:
            logger.info("%s: End-of-day exit at %s", ticker, current_timestamp)
            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.CLOSE,
                trade_type=TradeType.BUY,
                price=latest['open'].iloc[0],
                quantity=day.pos.qty,
                metadata={"exit_reason": "END_OF_DAY"},
                source_bar=latest.iloc[0]
            )
            self.close_pos(self.ctx)
            day.custom['traded'] = True
            return [sig]

        # Stop-loss check
        if self.in_pos(self.ctx):
            stop_price = day.pos.stop_px
            if day.pos.side == "SHORT" and hist['high'].iloc[-1] >= stop_price:
                logger.info("%s: Stop-loss triggered at price %.2f (stop: %.2f)", ticker, hist['high'].iloc[-1], stop_price)
                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.CLOSE,
                    trade_type=TradeType.BUY,
                    price=stop_price,
                    quantity=day.pos.qty,
                    metadata={"exit_reason": "STOP_LOSS"},
                    source_bar=latest.iloc[0]
                )
                self.close_pos(self.ctx)
                day.custom['traded'] = True
                return [sig]

        # Only one trade per day
        if day.custom.get('traded'):
            logger.debug("%s: Already traded today, skipping", ticker)
            return None

        # Check gap-up on every bar
        is_gap = self._is_gap_up(hist, latest, ticker)
        status = "GAP_UP" if is_gap else "NOT_GAP_UP"
        day.custom['stock_status'] = status
        
        # Pre-market SHORT_LOCATE request
        market_open_dt = timezone('US/Eastern').localize(
            datetime.combine(current_date, self.market_open_time)
        )
        
        # Pre-market locate request - only if we haven't already requested a locate
        if current_timestamp < market_open_dt and is_gap and not day.custom.get('locate_requested'):
            current_price = latest['close'].iloc[-1]  # Use current price for estimation
            stop_price = current_price * (1 + self.stop_loss_pct / 100)
            
            # Calculate expected position size
            qty = self.position_manager.calculate_position_size(
                ticker=ticker,
                hist_data=hist,
                price=current_price,
                stop_loss=stop_price
            )
            
            if qty > 0:
                logger.info("%s: Requesting pre-market short locate for %d shares at %.2f", 
                           ticker, qty, current_price)
                
                # Mark that we've requested a locate to avoid duplicate requests
                day.custom['locate_requested'] = True
                day.custom['requested_qty']   = qty
                
                # Create SHORT_LOCATE signal
                return [TradeSignal.short_locate_request(
                    symbol=ticker,
                    quantity=qty,
                    expected_price=current_price,
                    metadata={
                        "pre_market": True,
                        "estimated_stop_price": stop_price,
                        "timestamp": current_timestamp
                    },
                    source_bar=latest.iloc[0]
                )]

        # Entry window using current_timestamp
        window_end = market_open_dt + timedelta(minutes=self.entry_window_minutes)
        # Regular entry logic during entry window
        if market_open_dt <= current_timestamp < window_end and not self.in_pos(self.ctx):
            
            locate_req   = day.custom.get('locate_requested', False)
            located_qty  = int(day.custom.get('located_quantity', 0))
            
            if day.custom['stock_status'] == "GAP_UP":
                entry_price = latest['open'].iloc[0]
                stop_price = entry_price * (1 + self.stop_loss_pct / 100)
                risk = stop_price - entry_price
                       
                if locate_req:                           # we asked for a locate earlier
                    if located_qty == 0:                  # not filled yet → wait
                        logger.debug("%s: locate requested but not filled, skipping", ticker)
                        return None
                    qty = located_qty    
                else:
                    logger.info("%s: Entry window open at %s, entry_price=%.2f, stop_price=%.2f, risk=%.2f", ticker, current_timestamp, entry_price, stop_price, risk)
                    qty = self.position_manager.calculate_position_size(
                        ticker=ticker,
                        hist_data=hist,
                        price=entry_price,
                        stop_loss=stop_price
                    )                
                if qty <= 0:
                    logger.warning("%s: Position size <= 0, skipping trade", ticker)
                    return None
                
                logger.debug("%s: Calculated position size: %s", ticker, qty)
                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.OPEN,
                    trade_type=TradeType.SELL,
                    price=entry_price,
                    quantity=qty,
                    metadata={
                        "stop_price": stop_price,
                        "risk": risk,
                        "entry_type": "MARKET_OPEN",
                        "timestamp": current_timestamp
                    },
                    source_bar=latest.iloc[0]
                )
                self.open_pos(
                    self.ctx,
                    side="SHORT",
                    qty=qty,
                    entry_px=entry_price,
                    entry_ts=current_timestamp,
                    stop_px=stop_price,
                    target_px=None
                )
                logger.info("%s: Generated SHORT signal at %.2f for %s shares", ticker, entry_price, qty)
                return [sig]
        
        return None

    def _is_gap_up(
        self,
        hist: pd.DataFrame,
        latest: pd.DataFrame,
        ticker: str
    ) -> bool:
        # similar logic to original _is_gap_up, simplified
        combined = pd.concat([hist, latest])
        today = latest.index[-1].date()
        prev = combined[combined.index.map(lambda dt: dt.date() < today)]
        if prev.empty:
            logger.error("%s: No previous data for gap check", ticker)
            return False
        prev_rth = prev[prev.index.map(lambda dt: time(9,30) <= dt.time() < time(16,0))]
        if prev_rth.empty:
            logger.error("%s: No previous RTH data for gap check", ticker)
            return False
        prev_close = prev_rth['close'].iloc[-1]
        curr_open = combined[combined.index.map(lambda dt: dt.date() == today)]['open'].iloc[-1]
        gap_pct = (curr_open - prev_close) / prev_close * 100
        # logger.debug("%s: Calculated gap_pct=%.2f%% (threshold=%.2f%%)", ticker, gap_pct, self.min_gap_up_pct)
        if gap_pct < self.min_gap_up_pct:
            return False
        vol = combined[combined.index.map(lambda dt: dt.date() == today)]['volume']
        dollars = (vol * combined['close']).sum()
        # logger.debug("%s: Total dollar volume=%.2f (threshold=%.2f)", ticker, dollars, self.min_volume_dollars)
        return dollars >= self.min_volume_dollars


    def scan(self, historical_df: pd.DataFrame, current_day_df: Optional[pd.DataFrame] = None) -> List[str]:
        """
        Return tickers that 
        (1) are within the pre‑market/early‑market window,
        (2) have gapped up ≥ ``min_gap_up_pct`` **and**
        (3) trade at or above ``min_price_threshold``.
        """
        # Get current time in US/Eastern timezone
        current_time = datetime.now(timezone('US/Eastern'))
        current_date = current_time.date()
        
        # Define time window: pre-market to 5 minutes after market open
        market_open = datetime.combine(current_date, self.market_open_time)
        market_open = timezone('US/Eastern').localize(market_open)
        cutoff_time = market_open + timedelta(minutes=self.entry_window_minutes)
            
        # Return empty list if current time is after cutoff time
        if current_time > cutoff_time:
            return []
        
        # Filter for efficiency: Only keep necessary fields
        df = historical_df.copy()
        
        # Incorporate current day data if available
        if current_day_df is not None and not current_day_df.empty:
            current_day_filtered = current_day_df.copy()
            df = pd.concat([df, current_day_filtered], ignore_index=True)
        
        # Convert to daily data for each ticker
        df['date'] = pd.to_datetime(df['date']).dt.date
        daily_df = df.groupby(['symbol', 'date']).agg({
            'open': 'first',
            'close': 'last',
            'volume': 'sum'
        }).reset_index()
        
        matching_tickers = []
        
        # Process each ticker
        for ticker, ticker_df in daily_df.groupby('symbol'):
            # Sort by date to ensure chronological order
            ticker_df = ticker_df.sort_values('date').reset_index(drop=True)
            
            # Skip if we don't have at least 2 days of data
            if len(ticker_df) < 2:
                continue
            
            # Get previous day's close and current day's open
            prev_day_close = ticker_df['close'].iloc[-2]
            # logger.debug("%s: prev_day_close=%.2f", ticker, prev_day_close)
            # The current price is what we want since 
            # current_day_price is continously changing throughput the day
            current_day_price = ticker_df['close'].iloc[-1]
            # logger.debug("%s: current_day_price=%.2f", ticker, current_day_price)
            
            # Calculate gap up percentage
            gap_up_pct = ((current_day_price - prev_day_close) / prev_day_close) * 100
            
            if current_day_price < self.min_price_threshold:
                # logger.debug("%s: price %.2f < min %.2f – skip", ticker, current_day_price, self.min_price_threshold)
                continue
            
            # Check if gap up percentage meets criteria
            if gap_up_pct >= self.min_gap_up_pct:
                logger.info("%s: Gap up of %.2f%% detected in scan (prev close: %.2f, current: %.2f)", 
                           ticker, gap_up_pct, prev_day_close, current_day_price)
                matching_tickers.append(ticker)
        
        logger.info("Scan found %d matching tickers: %s", len(matching_tickers), matching_tickers)
        return matching_tickers

    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        """
        Handle execution reports, particularly for SHORT_LOCATE responses.
        
        Args:
            report: Execution report from the broker
            
        Returns:
            List of follow-up signals, if any
        """
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        
        # Check if this is a locate response - maintain existing special handling
        if report.signal.signal == SignalType.SHORT_LOCATE:
            # Check if outcome is FILLED (success)
            if report.outcome == ExecOutcome.FILLED:
                # Store the locate ID and quantity for later use
                day.custom['locate_id'] = report.metadata.get('locate_id')
                day.custom['located_quantity'] = report.metadata.get('located_quantity')
                logger.info("%s: Successfully pre-allocated %s shares for shorting with locate ID: %s", 
                           ticker, report.metadata.get('located_quantity'), report.metadata.get('locate_id'))
                return []
            else:
                # If locate failed, exit the strategy for this ticker
                logger.warning("%s: Failed to locate shares for shorting: %s. Exiting strategy.", 
                             ticker, report.broker_msg)
                
                # Use the new helper with a custom exit reason prefix
                return [TradeSignal.exit_on_execution_failure(report, exit_reason_prefix="LOCATE_")]
        
        # For other types of signals, handle non-FILLED outcomes
        elif report.outcome != ExecOutcome.FILLED:
            logger.warning("%s: Execution failed with outcome %s: %s. Exiting strategy.", 
                          ticker, report.outcome, report.broker_msg)
            return [TradeSignal.exit_on_execution_failure(report)]
        
        # For FILLED outcomes with other signal types, no follow-up signals needed
        return []