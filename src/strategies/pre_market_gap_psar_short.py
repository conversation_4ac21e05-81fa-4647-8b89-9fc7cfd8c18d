from __future__ import annotations
"""
Pre‑Market Gap‑PSAR Short Strategy (multi‑trade intraday)
-------------------------------------------------------
Unlimited number of *non‑overlapping* shorts per ticker/day:
  • **Entry**  – First *pre‑market* PSAR **flip‑to‑DOWN** on 5‑minute bars
    (timestamp 04:05‑09:29 ET) **and**
        – stock already flagged by upstream scanner as a gap‑up ≥ ``min_gap_up_pct``
        – current open ≥ ``min_price_threshold``
        – cumulative pre‑market $‑volume ≥ ``min_volume_dollars`` up to flip time
  • **Exit**   – On next PSAR **flip‑to‑UP** *or* at ``hard_exit_time`` (default 13:00 ET)
    plus a fixed **stop‑loss** ``stop_loss_pct`` (default 150 %) above entry.
  • Strategy may enter again later the same day after closing – no one‑trade limit.

This class follows the IntradayStrategy public interface used in the trading
framework (compatible with *PreMarketGapMAShortStrategy*).  It operates on
*minute* bars provided by the framework but derives PSAR signals from
*closed* 5‑minute buckets built from the historical minute data.  All
indicator calculations use closed bars only; the *latest* forming bar is used
exclusively for execution prices (entry/exit) to mirror the back‑test logic.
"""

from datetime import datetime, timedelta, time
from typing import Any, List, Optional
import logging

import pandas as pd
from pytz import timezone

from trading_framework.mixins import IntradayStrategy
from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome
from strategies.trade_signals import TradeSignal, SignalType, TradeType
from tickers.iscancriteria import IScanCriteria

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class PreMarketGapPSARShortStrategy(IntradayStrategy):
    """Shorts pre‑market gap‑ups on PSAR flips with optional re‑entries."""

    def __init__(
        self,
        psar_accel: float = 0.02,
        psar_max: float = 0.2,
        min_gap_up_pct: float = 50.0,
        min_volume_dollars: float = 250_000,
        stop_loss_pct: float = 150.0,
        hard_exit_time: time = time(13, 0),
        premkt_start_time: time = time(4, 0),
        premkt_end_time: time = time(9, 29),
        min_price_threshold: float = 1,
    ) -> None:
        super().__init__()
        self.psar_accel = float(psar_accel)
        self.psar_max = float(psar_max)
        self.min_gap_up_pct = float(min_gap_up_pct)
        self.min_volume_dollars = float(min_volume_dollars)
        self.stop_loss_pct = float(stop_loss_pct)
        self.hard_exit_time = hard_exit_time
        self.premkt_start_time = premkt_start_time
        self.premkt_end_time = premkt_end_time
        self.min_price_threshold = float(min_price_threshold)

        logger.info(
            "Initialized PreMarketGapPSARShortStrategy: psar_accel=%s, psar_max=%s, "
            "min_gap_up_pct=%s, min_volume_dollars=%s, stop_loss_pct=%s, "
            "hard_exit_time=%s, min_price_threshold=%s",
            self.psar_accel,
            self.psar_max,
            self.min_gap_up_pct,
            self.min_volume_dollars,
            self.stop_loss_pct,
            self.hard_exit_time,
            self.min_price_threshold,
        )

    # ──────────────────────────────────────────────────────────────────────
    # Framework requirements
    # --------------------------------------------------------------------
    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]:
        # minute bars, 5‑day look‑back provides >400 5‑minute buckets
        return timedelta(minutes=1), timedelta(days=5)

    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars  # `hist` closed bars; `latest` current forming bar
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]

        if hist.empty or latest.empty:
            return None

        current_ts: datetime = latest.index[0]
        logger.debug("%s – processing at %s (hist=%d, latest=%d)", ticker, current_ts, len(hist), len(latest))

        signals: List[TradeSignal] = []

        # ─── Exit logic (hard exit) ───────────────────────────────────────
        if self.in_pos(self.ctx) and current_ts.time() >= self.hard_exit_time:
            signals += self._exit_position(
                day=day,
                latest_bar=latest.iloc[0],
                reason="HARD_EXIT",
            )

        # ─── Exit logic (stop‑loss) ───────────────────────────────────────
        if self.in_pos(self.ctx):
            stop_price = day.pos.stop_px
            # For a short position a stop triggers if price ≥ stop
            if hist["high"].iloc[-1] >= stop_price:
                signals += self._exit_position(
                    day=day,
                    latest_bar=latest.iloc[0],
                    price=stop_price,
                    reason="STOP_LOSS",
                )

        # ─── Exit logic (PSAR flip‑to‑UP) ────────────────────────────────────
        if self.in_pos(self.ctx):
            five_df = (
                hist[["open", "high", "low", "close", "volume"]]
                .resample("5T", label="right", closed="right")
                .agg(
                    open=("open", "first"),
                    high=("high", "max"),
                    low=("low", "min"),
                    close=("close", "last"),
                    volume=("volume", "sum"),
                    count=("close", "count"),
                )
                .query("count == 5")
                .drop(columns="count")
            )

            if len(five_df) >= 2:
                psar_df = psar(
                    five_df.high,
                    five_df.low,
                    accel=self.psar_accel,
                    max_accel=self.psar_max,
                )
                five_df = five_df.join(psar_df)
                five_df["flip"] = five_df["flip"] & five_df["trend"].shift(1).notna()

                last_row = five_df.iloc[-1]
                if last_row.flip and last_row.trend == "up":
                    signals += self._exit_position(
                        day=day,
                        latest_bar=latest.iloc[0],
                        reason="PSAR_FLIP_UP",
                    )



        # After potential exits, check entry logic. We allow **multiple**
        # trades per day but never overlapping positions.
        if not self.in_pos(self.ctx):
            entry_signal = self._try_entry(hist, latest)
            if entry_signal is not None:
                signals.append(entry_signal)

        return signals or None

    # ──────────────────────────────────────────────────────────────────────
    # Helper methods
    # --------------------------------------------------------------------
    def _try_entry(self, hist: pd.DataFrame, latest: pd.DataFrame) -> Optional[TradeSignal]:
        """Evaluate entry conditions and return a TradeSignal if they are met."""
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        now_ts: datetime = latest.index[0]

        # Only pre‑market window
        if not (self.premkt_start_time <= now_ts.time() <= self.premkt_end_time):
            return None

        # Price threshold
        current_open = latest["open"].iloc[0]
        if current_open < self.min_price_threshold:
            return None

        # Avoid re‑triggering on the same flip
        last_processed_flip: datetime | None = day.custom.get("last_flip_ts")

        # Build 5‑minute bars **from closed minute bars** (`hist`)
        five_df = (
            hist[["open", "high", "low", "close", "volume"]]
            .resample("5T", label="right", closed="right")
            .agg(
                open=("open", "first"),
                high=("high", "max"),
                low=("low", "min"),
                close=("close", "last"),
                volume=("volume", "sum"),
                count=("close", "count"),
            )
            .query("count == 5")
            .drop(columns="count")
        )
        if len(five_df) < 2:
            return None

        # Compute PSAR and detect flips
        psar_df = psar(five_df.high, five_df.low, accel=self.psar_accel, max_accel=self.psar_max)
        five_df = five_df.join(psar_df)
        five_df["flip"] = five_df["flip"] & five_df["trend"].shift(1).notna()

        last_row = five_df.iloc[-1]
        if not (last_row.flip and last_row.trend == "down"):
            return None  # No down flip → no entry

        flip_ts: datetime = last_row.name.to_pydatetime()
        if last_processed_flip is not None and flip_ts == last_processed_flip:
            return None  # already handled this flip

        # ─── Pre‑trade validation (gap & volume) ───────────────────────
        if not self._passes_gap_and_volume_checks(hist, latest, flip_ts):
            return None

        # Determine position size via position_manager
        stop_price = current_open * (1 + self.stop_loss_pct / 100)
        qty = self.position_manager.calculate_position_size(
            ticker=ticker,
            hist_data=hist,
            price=current_open,
            stop_loss=stop_price,
        )
        if qty <= 0:
            logger.warning("%s: calculated position size <= 0, skip trade", ticker)
            return None

        # ─── Create OPEN signal & open position ────────────────────────
        sig = TradeSignal(
            symbol=ticker,
            signal=SignalType.OPEN,
            trade_type=TradeType.SELL,
            price=current_open,
            quantity=qty,
            metadata={
                "stop_price": stop_price,
                "entry_type": "PSAR_FLIP_DOWN",
                "flip_ts": flip_ts,
            },
            source_bar=latest.iloc[0],
        )

        self.open_pos(
            self.ctx,
            side="SHORT",
            qty=qty,
            entry_px=current_open,
            entry_ts=now_ts,
            stop_px=stop_price,
            target_px=0,  # no fixed target
        )

        # Record this flip to avoid duplicate entries
        day.custom["last_flip_ts"] = flip_ts
        # Track for performance analysis
        trades_counter = day.custom.get("trades", 0) + 1
        day.custom["trades"] = trades_counter

        logger.info("%s: Entered SHORT #%d at %.2f (stop %.2f) on PSAR flip", ticker, trades_counter, current_open, stop_price)
        return sig

    # ------------------------------------------------------------------
    def _exit_position(
        self,
        day: Any,
        latest_bar: pd.Series,
        price: float | None = None,
        reason: str = "PSAR_FLIP_UP",
    ) -> List[TradeSignal]:
        ticker = self.ctx.get("ticker")
        exit_price = price if price is not None else latest_bar["open"]
        qty = day.pos.qty

        sig = TradeSignal(
            symbol=ticker,
            signal=SignalType.CLOSE,
            trade_type=TradeType.BUY,
            price=exit_price,
            quantity=qty,
            metadata={"exit_reason": reason},
            source_bar=latest_bar,
        )
        self.close_pos(self.ctx)
        logger.info("%s: Exited SHORT at %.2f, reason=%s", ticker, exit_price, reason)
        return [sig]

    # ------------------------------------------------------------------
    def _passes_gap_and_volume_checks(
        self,
        hist: pd.DataFrame,
        latest: pd.DataFrame,
        flip_ts: datetime,
    ) -> bool:
        """Mirror the checks from the back‑test implementation."""
        ticker = self.ctx.get("ticker")
        today = flip_ts.date()

        # Previous day's RTH close
        prev = hist[hist.index.map(lambda dt: dt.date() < today)]
        if prev.empty:
            return False
        prev_rth = prev[(prev.index.time >= time(9, 30)) & (prev.index.time < time(16, 0))]
        if prev_rth.empty:
            return False
        prev_close = prev_rth["close"].iloc[-1]

        # Last close before flip
        hist_up_to_flip = hist[hist.index <= flip_ts]
        if hist_up_to_flip.empty:
            return False
        last_close = hist_up_to_flip["close"].iloc[-1]
        gap_pct = (last_close - prev_close) / prev_close * 100
        if gap_pct < self.min_gap_up_pct:
            logger.debug("%s: gap %.1f%% < min %.1f%%", ticker, gap_pct, self.min_gap_up_pct)
            return False

        # Cumulative $‑volume in the pre‑market window up to flip
        premkt = hist_between_times(hist_up_to_flip, self.premkt_start_time, self.premkt_end_time)
        dollars = (premkt["close"] * premkt["volume"]).sum()
        if dollars < self.min_volume_dollars:
            logger.debug("%s: $‑vol %.0f < min %.0f", ticker, dollars, self.min_volume_dollars)
            return False

        return True

    # ------------------------------------------------------------------
    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        """Handle broker execution reports. Reuse logic from MA strategy."""
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        if report.outcome != ExecOutcome.FILLED:
            logger.warning("%s: Execution failed (%s): %s", ticker, report.outcome, report.broker_msg)
            # Reset only position state; keep day.custom (so flips aren't reused)
            day.reset()
        return []


# ──────────────────────────────────────────────────────────────────────────
# Utility helpers (keep minimal to avoid external deps)                    
# ------------------------------------------------------------------------

def hist_between_times(df: pd.DataFrame, start_t: time, end_t: time) -> pd.DataFrame:
    """Return sub‑DataFrame where the **index.time** is within [start_t, end_t]."""
    mask = (df.index.time >= start_t) & (df.index.time <= end_t)
    return df[mask]

def psar(high, low, accel=0.02, max_accel=0.2):
    """Return a DataFrame with columns ['psar','trend','flip']."""
    psar      = high.copy().iloc[0:]*0.0   # placeholder
    trend     = pd.Series(index=high.index, dtype='object')
    af        = accel
    ep        = low.iloc[0]                # extreme point
    long_pos  = True                       # start “long” for PSAR logic

    psar.iloc[0]  = low.iloc[0]            # initial SAR
    trend.iloc[0] = 'up'

    for i in range(1, len(high)):
        prev_sar  = psar.iloc[i-1]
        if long_pos:
            psar.iloc[i] = prev_sar + af * (ep - prev_sar)
            psar.iloc[i] = min(psar.iloc[i], low.iloc[i-1], low.iloc[i-2] if i>1 else low.iloc[i-1])
            if low.iloc[i] < psar.iloc[i]:
                long_pos = False
                psar.iloc[i] = ep
                ep = high.iloc[i]
                af = accel
        else:  # short trend
            psar.iloc[i] = prev_sar + af * (ep - prev_sar)
            psar.iloc[i] = max(psar.iloc[i], high.iloc[i-1], high.iloc[i-2] if i>1 else high.iloc[i-1])
            if high.iloc[i] > psar.iloc[i]:
                long_pos = True
                psar.iloc[i] = ep
                ep = low.iloc[i]
                af = accel

        if long_pos:
            if high.iloc[i] > ep:
                ep = high.iloc[i]
                af = min(af + accel, max_accel)
            trend.iloc[i] = 'up'
        else:
            if low.iloc[i] < ep:
                ep = low.iloc[i]
                af = min(af + accel, max_accel)
            trend.iloc[i] = 'down'

    flip = trend != trend.shift(1)
    return pd.DataFrame({'psar': psar, 'trend': trend, 'flip': flip})
