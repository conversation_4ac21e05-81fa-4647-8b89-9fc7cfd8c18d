from __future__ import annotations

import logging
from collections import Counter
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor, as_completed
from dataclasses import dataclass
from typing import Any, Dict, List, Tuple

import numpy as np
import pandas as pd

from indicators.qpi import qpi_single

logger = logging.getLogger(__name__)


# -----------------------------------------------------------------------------
# Helper ----------------------------------------------------------------------
# -----------------------------------------------------------------------------

def strip_tz(ts: pd.Timestamp) -> pd.Timestamp:
    """Return a tz‑naïve copy of *ts* (works for aware/naïve)."""
    return ts.tz_localize(None) if ts.tzinfo is not None else ts


# -----------------------------------------------------------------------------
# GapExtractor ----------------------------------------------------------------
# ---------------------------------------------------------------------------
@dataclass(slots=True)
class GapExtractor:
    """
    Identify *daily* gap events using gap_pct and ATR filters.

    Parameters
    ----------
    qpi_window : int, default 3
        N-day return window used by QPI (for feature computation).
    atr_window : int, default 14
        Window for ATR calculation (days).
    gap_atr_threshold : float, default 0.5
        Minimum gap size in ATR multiples.
    min_gap_pct : float, default 0.10
        Minimum gap percentage (fractional; e.g. 0.10 == 10%).
    min_avg_10d_dollar_volume : float, default 100_000
        Liquidity filter: require 10-day avg dollar volume ≥ this.
    min_gap_daily_price : float, default 0.50
        Skip stocks whose previous close is below this.
    use_live_market_cap : bool, default False
        When True the later add_market_cap_info step skips the historical calculation.
    """
    qpi_window: int = 3
    atr_window: int = 14
    gap_atr_threshold: float = 0.1
    min_gap_pct: float = 0.05
    min_avg_10d_dollar_volume: float = 100_000.0
    min_gap_daily_price: float = 0.50
    use_live_market_cap: bool = False

    # ---------------------------------------------------------------------
    # Public API -----------------------------------------------------------
    # ---------------------------------------------------------------------

    def extract_gap_events(
        self,
        daily_bars: Dict[str, pd.DataFrame],
        *,
        start_date: pd.Timestamp | str | None = None,
        end_date: pd.Timestamp | str | None = None,
    ) -> pd.DataFrame:
        """Return a *DataFrame* of gap events that pass all filters."""

        start_date = pd.Timestamp(start_date) if start_date is not None else None
        end_date = pd.Timestamp(end_date) if end_date is not None else None

        items: List[Tuple[str, pd.DataFrame]] = list(daily_bars.items())
        max_workers = min(16, len(items)) or 1

        gap_events: List[Dict[str, Any]] = []
        stats = Counter()

        with ThreadPoolExecutor(max_workers=max_workers) as exe:
            fut_map = {
                exe.submit(self._process_ticker, tkr, df, start_date, end_date): tkr
                for tkr, df in items
            }
            for fut in as_completed(fut_map):
                tkr = fut_map[fut]
                try:
                    events, counters = fut.result()
                    gap_events.extend(events)
                    stats.update(counters)
                except Exception as exc:  # pragma: no cover – debug aid
                    logger.exception("GapExtractor: %s failed: %s", tkr, exc)

        # compute denominator (total rows across all tickers)
        total_days = sum(df["close"].shift(1).notna().sum() for df in daily_bars.values())
        # grab each counter
        price_fail = stats["price_fail"]
        volume_fail = stats["volume_fail"]
        gap_pct_fail = stats["gap_pct_fail"]
        gap_atr_fail = stats["gap_atr_fail"]
        passed = stats["passed"]

        # now display counts + percentages
        logger.info(
            "GapExtractor (total days=%d): price_fail=%d (%.1f%%) | volume_fail=%d (%.1f%%) "
            "| gap_pct_fail=%d (%.1f%%) | gap_atr_fail=%d (%.1f%%) | passed=%d (%.1f%%)",
            total_days,
            price_fail,   100 * price_fail   / total_days,
            volume_fail,  100 * volume_fail  / total_days,
            gap_pct_fail, 100 * gap_pct_fail / total_days,
            gap_atr_fail, 100 * gap_atr_fail / total_days,
            passed,       100 * passed       / total_days,
        )

        return pd.DataFrame(gap_events)

    # ---------------------------------------------------------------------
    # Internal helpers -----------------------------------------------------
    # ---------------------------------------------------------------------
    def _process_ticker(
        self,
        ticker: str,
        df: pd.DataFrame,
        start_date: pd.Timestamp | None,
        end_date: pd.Timestamp | None,
    ) -> Tuple[List[Dict[str, Any]], Counter]:
        """Run the filters for *one* ticker and return (events, counters)."""

        # Ensure chronological order & tz‑naïve index --------------------
        df = df.sort_index().copy()
        df.index = df.index.map(strip_tz)

        df["dollar_volume"] = df["close"] * df["volume"]

        # 10‑day average dollar volume (using the *preceding* 10 days)
        df["avg10d_dv"] = df["dollar_volume"].shift(1).rolling(10).mean()

        # Gap percentage (today open vs yesterday close)
        df["gap_pct"] = df["open"] / df["close"].shift(1) - 1.0

        # ----------------------------------------------------------------
        # QPI calculation -----------------------------------------------
        # ----------------------------------------------------------------
        try:
            qpi = qpi_single(df["close"], window=self.qpi_window)
        except Exception as exc:
            logger.warning("QPI failed for %s: %s", ticker, exc)
            qpi = pd.Series(np.nan, index=df.index)
        df["qpi"] = qpi

        # ----------------------------------------------------------------
        # Build masks ----------------------------------------------------
        # ----------------------------------------------------------------
        mask_price    = df["close"].shift(1) >= self.min_gap_daily_price
        mask_volume   = df["avg10d_dv"] >= self.min_avg_10d_dollar_volume
        mask_gap_pct  = df["gap_pct"].abs() >= self.min_gap_pct
        # use the precomputed 14-day ATR (% of close)
        mask_gap_atr  = (df["gap_pct"].abs() / df["atr14"]) >= self.gap_atr_threshold

        mask_all = mask_price & mask_volume & mask_gap_pct & mask_gap_atr

        # Date window filter (after computing other masks so we can count)
        if start_date is not None:
            mask_all &= df.index >= strip_tz(start_date)
        if end_date is not None:
            mask_all &= df.index <= strip_tz(end_date)

        counters = Counter()
        counters["price_fail"]     = (~mask_price).sum()
        counters["volume_fail"]    = (~mask_volume).sum()
        counters["gap_pct_fail"]   = (~mask_gap_pct).sum()
        counters["gap_atr_fail"]   = (~mask_gap_atr).sum()
        counters["passed"]         = mask_all.sum()

        # ----------------------------------------------------------------
        # Collect events -------------------------------------------------
        # ----------------------------------------------------------------
        events = []

        for day in df.index[mask_all]:
            prev_close = df.at[day - pd.Timedelta(days=1), "close"] if day - pd.Timedelta(days=1) in df.index else np.nan
            events.append(
                {
                    "ticker": ticker,
                    "day": day,
                    "gap_pct": df.at[day, "gap_pct"],
                    "qpi": df.at[day, "qpi"],
                    "prev_close": prev_close,
                    "close": df.at[day, "close"],
                    "open": df.at[day, "open"],
                    "high": df.at[day, "high"],
                    "low": df.at[day, "low"],
                    "volume": df.at[day, "volume"],
                }
            )

        return events, counters

    
    def add_market_cap_info(self, gap_df, daily_bars, ticker_info_store, min_market_cap=None, max_market_cap=None):
        """
        Adds market cap information to gap events and optionally filters by market cap.
        
        Parameters:
        -----------
        gap_df : DataFrame
            DataFrame of gap events
        daily_bars : dict
            Dictionary of daily price data by ticker
        ticker_info_store : object
            Object that provides ticker information like shares outstanding
        min_market_cap : float, optional
            Minimum market cap in dollars
        max_market_cap : float, optional
            Maximum market cap in dollars
            
        Returns:
        --------
        DataFrame
            Enhanced gap events DataFrame with market cap information
        """
        if gap_df.empty:
            return gap_df

        if self.use_live_market_cap:
            # Live‐mode: pull market_cap straight from the ticker snapshot
            snapshot_df = ticker_info_store.load_ticker_data()
            if snapshot_df.empty or 'market_cap' not in snapshot_df.columns:
                print("Warning: live market cap mode selected, but no 'market_cap' in ticker snapshot; returning gap_df with None")
                gap_df['market_cap'] = None
                return gap_df
            market_cap_map = snapshot_df.set_index('ticker')['market_cap']
            gap_df['market_cap'] = gap_df['ticker'].map(market_cap_map)
            # apply optional filters
            filtered_df = gap_df
            if min_market_cap is not None:
                filtered_df = filtered_df[filtered_df['market_cap'] >= min_market_cap]
            if max_market_cap is not None:
                filtered_df = filtered_df[filtered_df['market_cap'] <= max_market_cap]
            filtered_count = len(gap_df) - len(filtered_df)
            print(f"Filtered out {filtered_count} events based on live market cap criteria")
            print(f"Remaining: {len(filtered_df)} events")
            return filtered_df

        # First add ticker details using the ticker_info_store
        gap_df = self._add_ticker_details(gap_df, ticker_info_store)
        
        # Create a temporary column for market cap calculation
        gap_df['market_cap'] = None
        
        # Calculate market cap for each event
        for idx, row in gap_df.iterrows():
            ticker = row['ticker']
            day = row['day']
            total_shares = row['total_shares']
            
            # Skip if total_shares is missing
            if pd.isna(total_shares) or total_shares <= 0:
                continue
                
            # Get the price on the day before the gap (prev_close)
            df_d = daily_bars.get(ticker)
            if df_d is None or day not in df_d.index:
                continue
                
            day_prev = df_d.index[df_d.index < day].max()
            if day_prev not in df_d.index:
                continue
                
            prev_close = df_d.at[day_prev, 'close']
            market_cap = total_shares * prev_close
            gap_df.at[idx, 'market_cap'] = market_cap
        
        # Apply filters if provided
        filtered_df = gap_df.copy()
        
        if min_market_cap is not None:
            filtered_df = filtered_df[filtered_df['market_cap'] >= min_market_cap]
            
        if max_market_cap is not None:
            filtered_df = filtered_df[filtered_df['market_cap'] <= max_market_cap]
            
        # Count the number of rows filtered
        filtered_count = len(gap_df) - len(filtered_df)
        print(f"Filtered out {filtered_count} events based on market cap criteria")
        print(f"Remaining: {len(filtered_df)} events")
        
        return filtered_df
    
    def _add_ticker_details(self, gap_df, ticker_info_store, max_workers=10):
        """
        Adds additional ticker details to the gap_events DataFrame using parallel processing.
        
        Parameters:
        -----------
        gap_df : DataFrame
            DataFrame containing gap events with 'ticker' and 'day' columns
        ticker_info_store : object
            Object that provides ticker information
        max_workers : int
            Maximum number of worker threads to use
            
        Returns:
        --------
        DataFrame
            Enhanced DataFrame with added ticker details
        """
        # Create copies of the columns to add the data to
        gap_df['total_shares'] = None
        gap_df['total_employees'] = None
        gap_df['list_date'] = None
        
        # Function to process a single row
        def process_row(idx):
            row = gap_df.iloc[idx]
            ticker = row['ticker']
            date = row['day']
            
            # Fetch the ticker details for this specific ticker and date
            details = ticker_info_store.get_ticker_details(ticker, date)
            
            return (idx, details)
        
        # Use ThreadPoolExecutor to parallelize the processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all rows to the executor
            futures = [executor.submit(process_row, idx) for idx in range(len(gap_df))]
            
            # Process results as they become available
            for future in as_completed(futures):
                idx, details = future.result()
                if details:
                    gap_df.at[idx, 'total_shares'] = details.get('weighted_shares_outstanding')
                    gap_df.at[idx, 'total_employees'] = details.get('total_employees')
                    gap_df.at[idx, 'list_date'] = details.get('list_date')
        
        return gap_df