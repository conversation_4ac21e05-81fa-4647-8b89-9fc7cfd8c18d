from __future__ import annotations
from dataclasses import dataclass
from pathlib import Path
import lightgbm as lgb
import pandas as pd
import logging
from strategies.ltr.gap_extractor import GapExtractor

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

@dataclass
class TrainerConfig:
    feature_cols: list[str]
    lgb_params: dict = None

class LTRModelTrainer:
    """
    Trains a LambdaMART (LightGBM) model for gap events.
    """

    def __init__(self, gap_extractor: GapExtractor, feature_extractor,
                 daily_bars, ticker_info_store, intraday_fetcher=None,
                 cfg: TrainerConfig | None = None):
        self.gap_extractor   = gap_extractor
        self.feature_extractor = feature_extractor
        self.daily_bars      = daily_bars
        self.ticker_info_store = ticker_info_store
        self.intraday_fetcher = intraday_fetcher
        self.cfg = cfg or TrainerConfig(feature_cols=[])
        self.model: lgb.Booster | None = None

    # ------------------------------------------------------------------
    # 1)  preferred explicit-window interface
    # ------------------------------------------------------------------
    def train(self,
              train_start: pd.Timestamp, train_end: pd.Timestamp,
              val_start: pd.Timestamp,   val_end: pd.Timestamp
              ) -> lgb.Booster:
        """
        Train/validate on *exact* windows the caller specifies.
        """
        train_df = self._make_feature_df(train_start, train_end)
        val_df   = self._make_feature_df(val_start, val_end)

        dtrain = self._make_lgb_dataset(train_df)
        dval   = self._make_lgb_dataset(val_df)

        params = self.cfg.lgb_params or {
            "objective": "lambdarank",
            "metric": "ndcg",
            "learning_rate": 0.05,
            "num_leaves": 63,
            "min_data_in_leaf": 30,
            "ndcg_eval_at": [10],
        }
        self.model = lgb.train(
            params, dtrain, valid_sets=[dval],
            num_boost_round=400,
            callbacks=[lgb.early_stopping(20), lgb.log_evaluation(50)]
        )
        return self.model

    # ------------------------------------------------------------------
    # 2)  backward-compat helper (unchanged behaviour)
    # ------------------------------------------------------------------
    def train_until(self, end_date: pd.Timestamp,
                    lookback_years: int = 5, val_size: float = 0.2
                    ) -> lgb.Booster:
        start_date = end_date - pd.DateOffset(years=lookback_years)
        feat_df = self._make_feature_df(start_date, end_date)

        # chronological split
        days = sorted(feat_df["day"].unique())
        cut  = int(len(days) * (1 - val_size))
        train_df = feat_df[feat_df.day.isin(days[:cut])]
        val_df   = feat_df[feat_df.day.isin(days[cut:])]

        dtrain = self._make_lgb_dataset(train_df)
        dval   = self._make_lgb_dataset(val_df)

        params = self.cfg.lgb_params or {
            "objective": "lambdarank",
            "metric": "ndcg",
            "learning_rate": 0.05,
            "num_leaves": 63,
            "min_data_in_leaf": 30,
            "ndcg_eval_at": [10],
        }
        self.model = lgb.train(
            params, dtrain, valid_sets=[dval],
            num_boost_round=400,
            callbacks=[lgb.early_stopping(20), lgb.log_evaluation(50)]
        )
        return self.model

    # ------------------------------------------------------------------
    # internals
    # ------------------------------------------------------------------
    def _make_feature_df(self, start, end):
        logger.info(f"[_make_feature_df] start={start}, end={end}")
        gaps = self.gap_extractor.extract_gap_events(
            self.daily_bars, start_date=start, end_date=end
        )
        logger.info(f"[_make_feature_df] extracted gaps shape={gaps.shape}, columns={list(gaps.columns)}")
        gaps = self.gap_extractor.add_market_cap_info(
            gaps, self.daily_bars, self.ticker_info_store
        )
        logger.info(f"[_make_feature_df] after add_market_cap shape={gaps.shape}, columns={list(gaps.columns)}")
        feats = self.feature_extractor.build_features(
            gaps, self.daily_bars, self.intraday_fetcher
        )
        logger.info(f"[_make_feature_df] built features shape={feats.shape}, columns={list(feats.columns)}")
        expected_cols = self.cfg.feature_cols + ["ltr_target"]
        missing_cols = set(expected_cols) - set(feats.columns)
        logger.info(f"[_make_feature_df] expected columns for dropna: {expected_cols}")
        logger.info(f"[_make_feature_df] missing columns: {missing_cols}")

        rows_before = len(feats)
        logger.info(f"[_make_feature_df] rows before dropna for ltr_target: {rows_before}")

        # Drop rows ONLY if ltr_target is NaN. LightGBM will handle NaNs in feature_cols.
        initial_feature_rows = len(feats)
        feats = feats.dropna(subset=self.cfg.feature_cols + ["ltr_target"])
        rows_after_target_dropna = len(feats)
        logger.info(f"[_make_feature_df] Rows dropped due to NaN ltr_target: {initial_feature_rows - rows_after_target_dropna}")
        logger.info(f"[_make_feature_df] rows after dropna (on ltr_target only): {rows_after_target_dropna}")

        feats = feats.sort_values("day")
        return feats.reset_index(drop=True)

    def _make_lgb_dataset(self, df):
        grp_sizes = (df.groupby("day").size()
                       .loc[df["day"].unique()]   # keep chronological order
                       .values)                   # <<< bug-fix #2
        return lgb.Dataset(df[self.cfg.feature_cols],
                           label=df["ltr_target"],
                           group=grp_sizes)

    def save(self, path):
        """
        Persist the trained LightGBM model to `path`.
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        if self.model is None:
            raise ValueError("No model to save. Train or load a model first.")
        # LightGBM Booster has a save_model method
        self.model.save_model(str(path))

    def load(self, path):
        """
        Load a LightGBM model from `path` into this trainer.
        """
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(f"Model file not found at {path}")
        # Initialize a Booster from the file
        self.model = lgb.Booster(model_file=str(path))
        return self.model

    def train_year(
        self,
        years: list[int],
        lookback_years: int = 5,
        val_size: float = 0.2
    ) -> dict[int, lgb.Booster]:
        """
        Train a separate LambdaMART model for each year in `years`,
        using`train_until(...)` under the hood. Returns { year: Booster }.
        """
        models: dict[int, lgb.Booster] = {}
        for yr in years:
            end_date = pd.Timestamp(f"{yr}-01-01")
            model = self.train_until(
                end_date,
                lookback_years=lookback_years,
                val_size=val_size
            )
            models[yr] = model
        return models
