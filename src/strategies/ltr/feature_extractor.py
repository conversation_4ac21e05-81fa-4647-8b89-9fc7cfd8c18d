import numpy as np
import tqdm
import pandas as pd
from datetime import datetime, timedelta, time
from collections import Counter
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing
from typing import Any, Dict, List, Optional
import logging
from pytz import timezone

def strip_tz(ts):
    """Convert a Timestamp to tz-naïve if it is tz-aware."""
    return ts.tz_localize(None) if ts.tzinfo is not None else ts

class FeatureExtractor:
    """
    Extracts features from daily price data for gap events.
    """
    
    def __init__(self, max_workers = 16):
        """
        Initialize FeatureExtractor.
        
        Parameters:
        -----------
        max_workers : int, optional
            Maximum number of worker threads to use
        """
        self.max_workers = max_workers
        
    def build_features(self, gap_df, daily_bars, intraday_fetcher):
        """
        Build feature rows for the model from gap events and daily bars.
        
        Parameters:
        -----------
        gap_df : DataFrame
            Dataframe with gap events data
        daily_bars : dict
            Dictionary of daily bar data by ticker
        intraday_fetcher : callable, optional
            Function to fetch intraday data for more accurate feature calculation
            
        Returns:
        --------
        feature_df : DataFrame
            Dataframe with features for the model
        """
        if gap_df.empty:
            print("No gap events to build features from")
            return pd.DataFrame()
            
        return self._build_features_parallel(gap_df, daily_bars, intraday_fetcher)
    
    def _build_features_parallel(self, gap_df, daily_bars, intraday_fetcher):
        """
        Build features using parallel processing.
        
        Parameters:
        -----------
        gap_df : DataFrame
            Dataframe with gap events data
        daily_bars : dict
            Dictionary of daily bar data by ticker
        intraday_fetcher : callable, optional
            Function to fetch intraday data for more accurate feature calculation
            
        Returns:
        --------
        feature_df : DataFrame
            Dataframe with features for the model
        """        
        num_cores = self.max_workers or min(multiprocessing.cpu_count(), 32)
        
        # Split tickers into batches for parallel processing
        all_tickers = gap_df['ticker'].unique()
        ticker_batches = np.array_split(all_tickers, num_cores)
        ticker_batches = [batch.tolist() for batch in ticker_batches if len(batch) > 0]
        
        # Process function for each batch
        def process_ticker_batch(ticker_batch):
            local_rows = []
            local_reasons = {}
            
            # Pre-fetch daily bars for this batch to avoid redundant lookups
            batch_daily_bars = {tkr: daily_bars.get(tkr) for tkr in ticker_batch}
            
            # Group by ticker for efficient processing
            batch_gap_df = gap_df[gap_df['ticker'].isin(ticker_batch)]
            ticker_groups = batch_gap_df.groupby('ticker')
            
            for tkr, group in ticker_groups:
                df_d = batch_daily_bars.get(tkr)
                if df_d is None:
                    local_reasons['ticker_missing'] = local_reasons.get('ticker_missing', 0) + len(group)
                    continue
                    
                # Sort group by date for sequential processing
                group = group.sort_values('day')
                
                # Pre-calculate commonly used values for this ticker
                available_days = df_d.index.tolist()
                available_days.sort()  # Ensure chronological order
                day_to_idx = {day: i for i, day in enumerate(available_days)}
                
                # Pre-calculate moving averages and other metrics per ticker
                ma_periods = [5, 10, 20, 50]
                ma_values = {}
                
                for period in ma_periods:
                    ma_values[period] = df_d['close'].rolling(period).mean()
                    
                vol_10d_mean = df_d['volume'].rolling(10).mean()
                pct_change = df_d['open'].pct_change()
                
                # Process each day for this ticker
                for _, g in group.iterrows():
                    day = g['day'].normalize()
                    
                    if day not in df_d.index:
                        local_reasons['day_missing'] = local_reasons.get('day_missing', 0) + 1
                        continue
                        
                    # Find previous trading days efficiently
                    day_idx = day_to_idx.get(day)
                    if day_idx is None or day_idx < 2:
                        local_reasons['insufficient_history'] = local_reasons.get('insufficient_history', 0) + 1
                        continue
                        
                    day_prev = available_days[day_idx - 1]
                    day_prev_prev = available_days[day_idx - 2]
                    
                    # Handle ATR
                    atr14 = df_d.at[day, 'atr14']
                    if pd.isna(atr14) or atr14 == 0:
                        if not pd.isna(df_d.at[day_prev, 'atr14']) and df_d.at[day_prev, 'atr14'] != 0:
                            atr14 = df_d.at[day_prev, 'atr14']
                            local_reasons['used_prev_atr'] = local_reasons.get('used_prev_atr', 0) + 1
                        else:
                            try:
                                # Use a window of available days
                                last_10_idx = max(0, day_idx - 10)
                                last_days = available_days[last_10_idx:day_idx]
                                if len(last_days) >= 5:
                                    ranges = [(df_d.at[d, 'high'] - df_d.at[d, 'low']) / df_d.at[d, 'close'] for d in last_days]
                                    atr14 = sum(ranges) / len(ranges)
                                    local_reasons['approximated_atr'] = local_reasons.get('approximated_atr', 0) + 1
                                else:
                                    local_reasons['atr_nan'] = local_reasons.get('atr_nan', 0) + 1
                                    continue
                            except:
                                local_reasons['atr_nan'] = local_reasons.get('atr_nan', 0) + 1
                                continue
                    
                    fwd_ret = df_d.at[day, 'close'] / df_d.at[day, 'open'] - 1
                    
                    # Calculate prev_gap_atr
                    prev_day_close = df_d.at[day_prev_prev, 'close']
                    prev_day_atr = df_d.at[day_prev, 'atr14']
                    
                    if pd.isna(prev_day_atr) or prev_day_atr == 0:
                        prev_day_atr = atr14
                    
                    prev_gap_atr = (df_d.at[day_prev, 'open']/prev_day_close - 1) / prev_day_atr
                    
                    # Calculate max_gap30_atr
                    try:
                        rolling_max = pct_change.rolling(30).max()
                        max_gap30 = rolling_max.at[day] / atr14
                        if pd.isna(max_gap30):
                            # Fallback calculation
                            valid_idx = max(0, day_idx - 30)
                            valid_changes = pct_change.iloc[valid_idx:day_idx+1]
                            valid_changes = valid_changes[~pd.isna(valid_changes)]
                            if len(valid_changes) > 0:
                                max_gap30 = valid_changes.max() / atr14
                                local_reasons['approximated_max_gap30'] = local_reasons.get('approximated_max_gap30', 0) + 1
                            else:
                                max_gap30 = 0
                                local_reasons['defaulted_max_gap30'] = local_reasons.get('defaulted_max_gap30', 0) + 1
                    except:
                        max_gap30 = 0
                        local_reasons['max_gap_error'] = local_reasons.get('max_gap_error', 0) + 1
                    
                    # Calculate log_mcap
                    mcap = g.get('market_cap')
                    if pd.notna(mcap) and mcap and mcap > 0:
                        log_mcap = np.log(mcap)
                    else:
                        log_mcap = np.nan                    
                    # Calculate listing age
                    list_date_str = g.get('list_date')
                    log_listing_age = np.nan
                    
                    if list_date_str is not None and not pd.isna(list_date_str):
                        try:
                            list_date = datetime.strptime(list_date_str, '%Y-%m-%d').date()
                            event_date = day.date()
                            listing_age_days = (event_date - list_date).days
                            if listing_age_days > 0:
                                log_listing_age = np.log(listing_age_days)
                        except (ValueError, TypeError):
                            pass
                    
                    # Previous day return / ATR
                    prev_day_return = df_d.at[day_prev, 'close']/df_d.at[day_prev, 'open'] - 1
                    prev_day_return_atr = prev_day_return / prev_day_atr
                    
                    # Previous day volume / avg 10 day volume
                    try:
                        vol_10d_mean_val = vol_10d_mean.at[day_prev]
                        if pd.isna(vol_10d_mean_val) or vol_10d_mean_val == 0:
                            last_10_idx = max(0, day_idx - 10)
                            vol_10d_mean_val = df_d.iloc[last_10_idx:day_idx]['volume'].mean()
                        
                        prev_vol_ratio = df_d.at[day_prev, 'volume'] / vol_10d_mean_val
                    except:
                        prev_vol_ratio = 1.0
                        local_reasons['vol_ratio_default'] = local_reasons.get('vol_ratio_default', 0) + 1
                    
                    # Calculate MA features efficiently
                    ma_features = {}
                    for period in ma_periods:
                        try:
                            ma_value = ma_values[period].at[day_prev]
                            if pd.isna(ma_value):
                                # Calculate with available data
                                valid_idx = max(0, day_idx - period)
                                if day_idx - valid_idx >= max(3, period//2):
                                    ma_value = df_d.iloc[valid_idx:day_idx]['close'].mean()
                                else:
                                    ma_value = np.nan
                            
                            if not pd.isna(ma_value):
                                ma_features[f'dist_ma{period}_atr'] = (df_d.at[day_prev, 'close'] - ma_value) / prev_day_atr
                            else:
                                ma_features[f'dist_ma{period}_atr'] = np.nan
                        except:
                            ma_features[f'dist_ma{period}_atr'] = np.nan
                            key = f'ma{period}_calc_error'
                            local_reasons[key] = local_reasons.get(key, 0) + 1
                    
                    # Build the final row
                    local_rows.append({
                        "ticker": tkr,
                        "day": day,
                        "fwd_ret": fwd_ret,
                        "gap_atr": g.get('gap_pct') / atr14,
                        "qpi": g.get("qpi"),
                        "prev_gap_atr": prev_gap_atr,
                        "max_gap30_atr": max_gap30,
                        "log_mcap": log_mcap,
                        "log_listing_age": log_listing_age,
                        "weekday": day.weekday(),
                        "prev_day_return_atr": prev_day_return_atr,
                        "prev_vol_ratio": prev_vol_ratio,
                        **ma_features
                    })
            
            return local_rows, local_reasons
        
        # Use thread pool for parallel execution
        all_rows = []
        all_reasons = Counter()
        
        print(f"Processing {len(all_tickers)} tickers using {num_cores} cores...")
        
        with ThreadPoolExecutor(max_workers=num_cores) as executor:
            future_results = {executor.submit(process_ticker_batch, batch): batch for batch in ticker_batches}
            
            for future in as_completed(future_results):
                batch = future_results[future]
                try:
                    local_rows, local_reasons = future.result()
                    all_rows.extend(local_rows)
                    
                    # Merge reasons
                    for reason, count in local_reasons.items():
                        all_reasons[reason] += count
                        
                    print(f"Completed batch with {len(batch)} tickers, got {len(local_rows)} rows")
                except Exception as e:
                    import traceback
                    traceback.print_exc()
                    print(f"Error processing batch: {e}")
        
        print("Row-filter summary:", all_reasons)
        print(f"Total rows retained: {len(all_rows)}")
        
        if len(all_rows) == 0:
            print("No feature rows were created!")
            return pd.DataFrame()
        
        # Create DataFrame from all rows
        feature_df = pd.DataFrame(all_rows)
        
        # Calculate deciles
        feature_df = self.add_daily_deciles(feature_df)
        feature_df['ltr_target'] = 11 - feature_df['decile']  # 10 = best, 1 = worst
        
        return feature_df
    
    
    def add_daily_deciles(self, df, *, day_col='day', ret_col='fwd_ret'):
        """
        Adds a `decile` column: 1 = worst returns (good short), 10 = best returns.
        Works even if a day has <10 names.
        
        Parameters:
        -----------
        df : DataFrame
            DataFrame with forward returns
        day_col : str
            Column name for day
        ret_col : str
            Column name for return
            
        Returns:
        --------
        DataFrame
            DataFrame with decile column added
        """
        if ret_col not in df.columns:
            raise KeyError(f"Column '{ret_col}' missing from frame.")

        # make sure the day lives in its own column
        if day_col not in df.columns:
            df = df.reset_index().rename(columns={'index': day_col})

        def _label(g):
            n = len(g)
            ranks = g[ret_col].rank(method='first', ascending=True)   # 1 = worst
            dec = (np.floor((ranks - 1) / n * 10) + 1).astype(int)    # 1-10
            return g.assign(decile=dec)

        return (
            df.copy()
              .sort_values(day_col)
              .groupby(day_col, group_keys=False)
              .apply(_label)
        )

