import pandas as pd
import lightgbm as lgb

class LTRModelRanker:
    """
    Stateless scorer that converts a trained LightGBM model
    + feature pipeline into a ranked DataFrame.
    """

    def __init__(self, model: lgb.Booster,
                 gap_extractor, feature_extractor, feature_cols):
        self.model = model
        self.gap_extractor = gap_extractor
        self.feature_extractor = feature_extractor
        self.feature_cols = feature_cols

    def rank(self, daily_bars, ticker_info_store, intraday_fetcher=None,
             start_date=None, end_date=None) -> pd.DataFrame:
        gaps = self.gap_extractor.extract_gap_events(
            daily_bars, start_date=start_date, end_date=end_date
        )
        gaps = self.gap_extractor.add_market_cap_info(
            gaps, daily_bars, ticker_info_store
        )
        feats = self.feature_extractor.build_features(
            gaps, daily_bars, intraday_fetcher
        ).dropna(subset=self.feature_cols)

        feats["score"] = self.model.predict(feats[self.feature_cols])
        return feats
