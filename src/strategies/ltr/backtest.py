# gap_strategy/backtest/runner.py
import pandas as pd
import numpy as np
from collections import defaultdict

class BacktestRunner:
    def __init__(
        self, trainer, ranker_factory, commission_per_trade: float = 0.0,
        k_frac: float = 0.1, min_k: int = 1,
        short_alloc: float = 0.0, long_alloc: float = 0.05
    ):
        """
        trainer:  an *instance* of ModelTrainer
        ranker_factory: lambda model -> ModelRanker  (so we can rebuild each year)
        """
        self.trainer = trainer
        self.ranker_factory = ranker_factory
        self.commission = commission_per_trade
        self.k_frac = k_frac
        self.min_k = min_k
        self.short_alloc = short_alloc
        self.long_alloc = long_alloc

    # ---------- main loop ----------
    def run_yearly(
        self, years: list[int], daily_bars, ticker_info_store, intraday_fetcher=None,
        random_sims: int = 0
    ):
        equity_curves: dict[int, pd.Series] = {}
        trades_all = []
        for yr in years:
            model = self.trainer.train_until(pd.Timestamp(f"{yr}-01-01"))
            ranker = self.ranker_factory(model)

            # rank & back-test on current year
            feats = ranker.rank(
                daily_bars, ticker_info_store, intraday_fetcher,
                start_date=f"{yr}-01-01", end_date=f"{yr}-12-31"
            )
            equity, trades, metrics = self._backtest_on_df(feats)
            equity_curves[yr] = equity
            trades["year"] = yr
            trades_all.append(trades)

            print(f"{yr} Ranked: return {metrics['total_return']:.1%}, "
                  f"sharpe {metrics['sharpe']:.2f}")

            # — run random‐selection backtests and print comparison table
            if random_sims and random_sims > 0:
                # collect metrics from many random draws
                random_metrics_list = []
                for _ in range(random_sims):
                    _, _, rand_metrics = self._backtest_random_on_df(feats)
                    random_metrics_list.append(rand_metrics)
                rand_df    = pd.DataFrame(random_metrics_list)
                random_mean = rand_df.mean()
                random_std  = rand_df.std()

                # permutation p-value for total_return
                p_return = ((rand_df['total_return'] >= metrics['total_return']).sum() + 1) \
                           / (random_sims + 1)

                # build a comparison table
                comp_df = pd.DataFrame({
                    'ranked':      pd.Series(metrics),
                    'random_mean': random_mean,
                    'random_std':  random_std,
                })
                comp_df['p_value'] = ''
                comp_df.at['total_return', 'p_value'] = p_return

                print(f"{yr} Ranked vs Random Comparison (n_sims={random_sims}):")
                print(comp_df)

        comb_equity = self._combine_curves(equity_curves)
        return comb_equity, pd.concat(trades_all)

    # ---------- helpers ----------
    def _backtest_on_df(self, df):
        pnl = defaultdict(float)
        trades = []
        for day, g in df.groupby("day"):
            # determine number of positions based on allocations
            g = g.sort_values("score", ascending=False)
            k = max(self.min_k, int(len(g) * self.k_frac))
            n_short = k if self.short_alloc > 0 else 0
            n_long = k if self.long_alloc > 0 else 0
            n_positions = n_short + n_long
            if n_positions == 0 or len(g) < n_positions:
                continue
            short_leg = g.head(n_short)
            long_leg = g.tail(n_long)

            # gross returns
            ret_s = -short_leg["fwd_ret"].mean() * self.short_alloc if n_short > 0 else 0
            ret_l = long_leg["fwd_ret"].mean() * self.long_alloc if n_long > 0 else 0
            gross = ret_s + ret_l

            # commissions  (flat $/trade OR bps – adapt as needed)
            n_trades = n_positions
            cost = n_trades * self.commission
            net = gross - cost
            pnl[day] = net

            for _, row in pd.concat([short_leg, long_leg]).iterrows():
                trades.append({
                    "day": day,
                    "symbol": row["ticker"],
                    "dir": "short" if row.name in short_leg.index else "long",
                    "gross_ret": -row["fwd_ret"] if row.name in short_leg.index else row["fwd_ret"],
                    "commission": self.commission,
                    "net_ret": (-row["fwd_ret"] if row.name in short_leg.index else row["fwd_ret"]) - self.commission,
                    "score": row["score"]
                })

        pnl = pd.Series(pnl).sort_index()
        equity = (1 + pnl).cumprod()
        metrics = self._metrics(equity, pnl, trades)
        return equity, pd.DataFrame(trades), metrics

    def _backtest_random_on_df(self, df):
        """
        Backtest using random selection instead of score ranking.
        Returns (equity, trades_df, metrics).
        """
        pnl = defaultdict(float)
        trades = []
        for day, g in df.groupby("day"):
            # determine number of positions based on allocations
            k = max(self.min_k, int(len(g) * self.k_frac))
            n_short = k if self.short_alloc > 0 else 0
            n_long = k if self.long_alloc > 0 else 0
            n_positions = n_short + n_long
            if n_positions == 0 or len(g) < n_positions:
                continue
            rand_sample = g.sample(n=n_positions, replace=False)
            short_leg = rand_sample.iloc[:n_short]
            long_leg = rand_sample.iloc[n_short:]

            # gross returns
            ret_s = -short_leg["fwd_ret"].mean() * self.short_alloc if n_short > 0 else 0
            ret_l = long_leg["fwd_ret"].mean() * self.long_alloc if n_long > 0 else 0
            gross = ret_s + ret_l

            # commission costs
            n_trades = n_positions
            cost = n_trades * self.commission
            pnl[day] = gross - cost

            for _, row in pd.concat([short_leg, long_leg]).iterrows():
                trades.append({
                    "day": day,
                    "symbol": row["ticker"],
                    "dir": "short" if row.name in short_leg.index else "long",
                    "gross_ret": -row["fwd_ret"] if row.name in short_leg.index else row["fwd_ret"],
                    "commission": self.commission,
                    "net_ret": (-row["fwd_ret"] if row.name in short_leg.index else row["fwd_ret"]) - self.commission,
                    "score": row["score"]
                })

        pnl = pd.Series(pnl).sort_index()
        equity = (1 + pnl).cumprod()
        metrics = self._metrics(equity, pnl, trades)
        return equity, pd.DataFrame(trades), metrics

    def _metrics(self, equity, pnl, trades):
        m = {}
        if equity.empty:
            return m
        m["total_return"] = equity.iloc[-1] - 1
        dr = equity.pct_change().dropna()
        m["sharpe"] = np.sqrt(252) * dr.mean() / dr.std() if dr.std() else 0
        m["max_dd"] = (equity / equity.cummax() - 1).min()
        m["total_trades"] = len(trades)
        m["commission_paid"] = len(trades) * self.commission
        m["win_rate_net"] = np.mean(pd.Series([t["net_ret"] for t in trades]) > 0)
        return m

    @staticmethod
    def _combine_curves(curves: dict[int, pd.Series]) -> pd.Series:
        scale = 1.0
        out = []
        for yr in sorted(curves):
            s = curves[yr] * scale
            out.append(s)
            scale = s.iloc[-1]
        return pd.concat(out).sort_index()
