from __future__ import annotations

from datetime import timedelta, time
from typing import Optional, Sequence, Literal, Any

import numpy as np
import pytz
import logging

from trading_framework.core import BarSlice
from trading_framework.day_ctx import DayContext
from trading_framework.mixins import IntradayStrategy
from strategies.trade_signals import TradeSignal, TradeType, SignalType

__all__ = ["FastScalpStrategy"]

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class FastScalpStrategy(IntradayStrategy):
    """Live execution strategy."""

    LIQUIDITY_THRESHOLD = 500_000  # USD

    def __init__(
        self,
        *,
        F: float = 0.60,
        K: float = 1.00,
        ewma_halflife: int = 900,
        soft_close: str = "15:30:00",
        hard_close: str = "15:30:00",
        qty: int = 100,
    ) -> None:
        super().__init__()
        self.F = float(F)
        self.K = float(K)
        self.halflife = int(ewma_halflife)
        self.soft_t = time.fromisoformat(soft_close)
        self.hard_t = time.fromisoformat(hard_close)
        self.qty = int(qty)
        # Convenience – reusable tz objects
        self._tz_est = pytz.timezone("US/Eastern")

    # ── Framework hooks ────────────────────────────────────────────────────
    def prepare(self, meta: dict[str, Any]) -> tuple[timedelta, timedelta]:
        # 1‑second bars, 6× half‑life look‑back for liquidity
        return timedelta(seconds=1), timedelta(hours=6)

    def _create_initial_context(self) -> dict:
        return {
            "state": "FLAT",
            "R": np.nan,
            "ref_px": np.nan,
            "upper": np.nan,
            "lower": np.nan,
            "entry_ts": None,
        }

    def _round_price(self, price: float) -> float:
        # for US stocks, ticks are usually $0.01
        return round(price, 2)

    # ── Core Logic ─────────────────────────────────────────────────────────
    def process_bar(self, bars: BarSlice) -> Optional[Sequence[TradeSignal]]:
        ticker = self.ctx.get("ticker")
        hist, latest = bars  # hist = *closed* bars; latest = forming bar (len==1)

        if hist.empty or latest.empty:
            return None  # nothing to do yet

        logger.debug("%s - process_bar called: hist %d bars, latest %d bars", ticker, len(hist), len(latest))
        bar_open = float(latest["open"].iloc[0])       # for entry LIMIT
        bar_ts = latest.index[0]                        # timestamp of forming bar
        bar_close = float(hist["close"].iloc[-1])      # most‑recent closed bar
        ctx = self.ctx
        day: DayContext = ctx["day"]

        # ── 1) Premarket liquidity gate ───────────────────────────────────
        if not day.custom.get("liquidity_ok", False):
            # Filter today's hist bars between 04:00 and current forming time (EST)
            est_idx = hist.index.tz_convert(self._tz_est)
            today_est_date = bar_ts.astimezone(self._tz_est).date()
            mask_today = est_idx.date == today_est_date
            mask_pm = (est_idx.time >= time(4, 0)) & (est_idx.time < bar_ts.astimezone(self._tz_est).time())
            pm_hist = hist.loc[mask_today & mask_pm]
            dollar_vol = float((pm_hist["close"] * pm_hist["volume"]).sum())
            logger.debug("%s - premarket liquidity dollar_vol=%.2f, threshold=%d", ticker, dollar_vol, self.LIQUIDITY_THRESHOLD)
            if dollar_vol >= self.LIQUIDITY_THRESHOLD:
                logger.debug("%s - liquidity threshold met", ticker)
                day.custom["liquidity_ok"] = True
            else:
                return None  # standby until threshold met

        # ── 2) Hard close – flatten immediately ───────────────────────────
        if bar_ts.time() >= self.hard_t and ctx["state"] == "LONG":
            return [
                TradeSignal(
                    symbol=ctx["ticker"],
                    signal=SignalType.EXIT_STRATEGY,
                    trade_type=TradeType.SELL,
                    price=0.0,
                    metadata={"reason": "HARD_CLOSE"},
                    source_bar=latest.iloc[0],
                )
            ]

        # ── 3) Adaptive range Rₜ (EWMA of closed bars) ────────────────────
        rolling = hist["close"].rolling(self.halflife)
        roll_max = rolling.max()
        roll_min = rolling.min()
        R_raw = roll_max - roll_min
        alpha = 1 - np.exp(np.log(0.5) / self.halflife)
        R_series = R_raw.ewm(alpha=alpha, adjust=False).mean().ffill()
        R = R_series.iloc[-1]
        ctx["R"] = float(R)
        logger.debug("%s - R updated: R=%f", ticker, ctx["R"])

        # ── 4) State‑machine ───────────────────────────────────────────────
        sigs: list[TradeSignal] = []
        state = ctx["state"]
        logger.debug("%s - state=%s", ticker, state)

        # —— FLAT ————————————————————————————————————————————————
        if state == "FLAT":
            logger.debug("%s - FLAT state: ref_px=%s, upper=%s, lower=%s", ticker, ctx["ref_px"], ctx["upper"], ctx["lower"])
            if np.isnan(R) or R == 0:
                return None

            if np.isnan(ctx["ref_px"]):
                ctx["ref_px"] = bar_close
                ctx["upper"] = ctx["ref_px"] + R * self.F
                ctx["lower"] = ctx["ref_px"] - R * self.F
                logger.debug("%s - initialized ref_px=%.2f, upper=%.2f, lower=%.2f", ticker, ctx["ref_px"], ctx["upper"], ctx["lower"])
                return None

            entry_triggered = bar_close <= ctx["lower"]

            if not entry_triggered:
                logger.debug("%s - entry not triggered: bar_close=%.2f, upper=%.2f, lower=%.2f", ticker, bar_close, ctx["upper"], ctx["lower"])
                return None

            # round everything to penny ticks:
            entry_px   = self._round_price(bar_open)
            stop_loss  = self._round_price(entry_px - R * self.K)
            take_profit= self._round_price(entry_px + R * self.F)

            sigs.append(
                TradeSignal.bracket_order(
                    symbol=ctx["ticker"],
                    trade_type=TradeType.BUY,
                    entry_price=entry_px,
                    stop_loss_price=stop_loss,
                    take_profit_price=take_profit,
                    quantity=self.qty,
                    metadata={"tp": take_profit, "sl": stop_loss},
                    source_bar=latest.iloc[0],
                )
            )
            logger.info("%s - placing bracket order BUY @%.2f SL=%.2f TP=%.2f qty=%d", ticker, entry_px, stop_loss, take_profit, self.qty)
            ctx.update(
                state="LONG",
                entry_ts=bar_ts,
                ref_px=np.nan,
                upper=np.nan,
                lower=np.nan,
            )
            return sigs

        # —— LONG ————————————————————————————————————————————————
        if state == "LONG":
            logger.debug("%s - LONG state: time=%s, soft_t=%s", ticker, bar_ts.time(), self.soft_t)
            if bar_ts.time() >= self.soft_t:
                logger.debug("%s - soft close triggered at %s", ticker, bar_ts.time())
                sigs.append(
                    TradeSignal(
                        symbol=ctx["ticker"],
                        signal=SignalType.EXIT_STRATEGY,
                        trade_type=TradeType.SELL,
                        price=0.0,
                        metadata={"reason": "SOFT_CLOSE"},
                        source_bar=latest.iloc[0],
                    )
                )
            return sigs or None

        return None
