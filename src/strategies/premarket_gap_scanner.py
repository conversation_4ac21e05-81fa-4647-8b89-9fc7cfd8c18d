from __future__ import annotations

from typing import List, Optional
from datetime import datetime, time
import pandas as pd
from pytz import timezone

from tickers.iscancriteria import IScanCriteria

class PreMarketGapScanner(IScanCriteria):
    """
    Selects tickers that gapped ≥ `min_gap_up_pct` and meet a price floor
    """

    def __init__(
        self,
        *,
        min_gap_up_pct: float = 50.0,
        min_price_threshold: float = 0.5,
        scap_stop_time: time = time(9, 35),
        scan_start_time: time = time(4, 0),
    ) -> None:
        self.min_gap_up_pct = float(min_gap_up_pct)
        self.min_price_threshold = float(min_price_threshold)
        self._scap_stop_time = scap_stop_time
        self._scan_start_time = scan_start_time
        self._tz = timezone("US/Eastern")

    def scan(
        self,
        historical_df: pd.DataFrame,
        current_day_df: Optional[pd.DataFrame] = None,
    ) -> List[str]:
        """Return tickers that meet our gap-up and price criteria before 15:00 ET."""
        now_et = datetime.now(self._tz)
        if not (self._scan_start_time <= now_et.time() < self._scap_stop_time):
            return []

        df = historical_df.copy()
        if current_day_df is not None and not current_day_df.empty:
            df = pd.concat([df, current_day_df], ignore_index=True)

        df["date"] = pd.to_datetime(df["date"]).dt.date
        daily = (
            df.groupby(["symbol", "date"])
            .agg({"open": "first", "close": "last", "volume": "sum"})
            .reset_index()
            .sort_values(["symbol", "date"])
        )

        matches: list[str] = []
        for symbol, g in daily.groupby("symbol"):
            if len(g) < 2:
                continue
            prev_close = g["close"].iloc[-2]
            curr_price = g["close"].iloc[-1]
            gap_pct = (curr_price - prev_close) / prev_close * 100
            if curr_price >= self.min_price_threshold and gap_pct >= self.min_gap_up_pct:
                matches.append(symbol)
        return matches
