from __future__ import annotations
from datetime import datetime, timedelta, time
from typing import Any, Dict, List, Optional, Sequence, TypedDict

import logging
import pandas as pd
from pytz import timezone

from trading_framework.core import BarSlice
from trading_framework.mixins import IntradayStrategy
from trading_framework.day_ctx import DayContext
from strategies.trade_signals import TradeSignal, SignalType, TradeType

logger = logging.getLogger(__name__)

class ORBExtra(TypedDict, total=False):
    opening_range_high: float
    opening_range_low: float
    opening_range_direction: str
    or_up_volume: float
    or_down_volume: float
    imbalance_ratio_used: float
    stop_price: float
    target_price: float
    atr: float
    atr_stop_pct: float
    risk_per_share: float


class ORBStrategy(IntradayStrategy):
    """
    5‑minute Opening‑Range‑Breakout with ATR stops & optional volume imbalance filter.
    """

    # -------- constructor --------
    def __init__(self, imbalance_ratio: Optional[float] = 1.5) -> None:
        super().__init__()
        
        self.open_range = 5
        self.exit_time = time(15, 58)
        self.risk_reward = 10
        self.atr_stop_pct = 0.10
        self.imbalance_ratio = imbalance_ratio
        self.market_open = time(9, 30)
        self.est = timezone("US/Eastern")

        self.position_manager = None

    # -------- life‑cycle --------
    def prepare(self, meta) -> tuple[timedelta, timedelta]:
        """
        `meta` is the dict passed by the orchestrator. It may contain:
        """
        return timedelta(minutes=1), timedelta(days=20)

    # -------- bar handler --------
    def process_bar(self, bars: BarSlice) -> Optional[Sequence[TradeSignal]]:
        hist, latest = bars
        if hist.empty or latest.empty:
            return None
        
        ticker = self.ctx.get("ticker")
        # update indicators for this bar
        indicators = self.ctx["ind"]

        bar = latest.iloc[0]                   # pd.Series
        ts = bar.name
        day: DayContext = self.ctx["day"]

        # ------------------------------------------------------ #
        #  EXIT‑RELATED                                          #
        # ------------------------------------------------------ #
        if self.in_pos(self.ctx):
            if ts.time() >= self.exit_time:
                return self._exit_market(ticker, bar, reason="END_OF_DAY")

            pos = day.pos
            if pos.side == "LONG":
                if bar.low <= pos.stop_px:
                    return self._exit_market(ticker, bar, pos.stop_px, "STOP_LOSS")
                if bar.high >= pos.target_px:
                    return self._exit_market(ticker, bar, pos.target_px, "PROFIT_TARGET")
            else:  # SHORT
                if bar.high >= pos.stop_px:
                    return self._exit_market(ticker, bar, pos.stop_px, "STOP_LOSS")
                if bar.low <= pos.target_px:
                    return self._exit_market(ticker, bar, pos.target_px, "PROFIT_TARGET")

        # ------------------------------------------------------ #
        #  ENTRY CHECK – only on first bar *after* OR            #
        # ------------------------------------------------------ #
        or_end_time = (datetime.combine(ts.date(), self.market_open) +
                       timedelta(minutes=self.open_range)).time()
        prev_ts = hist.index[-1]
        if (prev_ts.time() < or_end_time) and (ts.time() >= or_end_time):
            if self.in_pos(self.ctx) or day.custom.get("traded"):
                return None

            entry_sig = self._build_entry(ticker, hist, bar, indicators)
            if entry_sig:
                day.custom["traded"] = True
                return [entry_sig]

        return None

    # ------------------------------------------------------------------ #
    #  Build entry if all filters pass                                   #
    # ------------------------------------------------------------------ #
    def _build_entry(
        self,
        ticker: str,
        hist: pd.DataFrame,
        bar: pd.Series,
        ind,
    ) -> Optional[TradeSignal]:

        last_n = hist.iloc[-self.open_range:]
        if len(last_n) < self.open_range:
            return None

        or_open = last_n["open"].iloc[0]
        or_close = last_n["close"].iloc[-1]
        or_high = last_n["high"].max()
        or_low = last_n["low"].min()

        # flat OR
        if or_close == or_open:
            logger.info("%s OR flat – skip", bar.name)
            return None

        direction = "UP" if or_close > or_open else "DOWN"

        # volume imbalance
        up_vol = last_n[last_n["close"] > last_n["open"]]["volume"].sum()
        dn_vol = last_n[last_n["close"] < last_n["open"]]["volume"].sum()

        if self.imbalance_ratio and self.imbalance_ratio > 0:
            ok = (
                (direction == "UP" and up_vol >= dn_vol * self.imbalance_ratio)
                or
                (direction == "DOWN" and dn_vol >= up_vol * self.imbalance_ratio)
            )
            if not ok:
                logger.info("%s imbalance filter failed", bar.name)
                return None

        # ATR
        atr14 = ind.get("atr", 14)
        if atr14 is None or atr14 <= 0:
            logger.warning("%s invalid ATR", bar.name)
            return None

        risk_per_share = atr14 * self.atr_stop_pct
        entry_px = bar.open

        if direction == "UP":
            stop_px = or_close - risk_per_share
            risk_amt = entry_px - stop_px
            target_px = entry_px + self.risk_reward * risk_amt
            trade_type = TradeType.BUY
            side = "LONG"
        else:
            stop_px = or_close + risk_per_share
            risk_amt = stop_px - entry_px
            target_px = entry_px - self.risk_reward * risk_amt
            trade_type = TradeType.SELL
            side = "SHORT"

        # qty
        qty = self.position_manager.calculate_position_size(
            ticker=ticker,
            hist_data=hist,
            price=entry_px,
            stop_loss=stop_px,
        )
        if not qty or qty <= 0:
            logger.warning("%s qty %s – skip", bar.name, qty)
            return None

        # store position
        self.open_pos(self.ctx, side, qty, entry_px, bar.name, stop_px, target_px)

        meta: ORBExtra = {
            "opening_range_high": or_high,
            "opening_range_low": or_low,
            "opening_range_direction": direction,
            "or_up_volume": up_vol,
            "or_down_volume": dn_vol,
            "imbalance_ratio_used": self.imbalance_ratio,
            "stop_price": stop_px,
            "target_price": target_px,
            "atr": atr14,
            "atr_stop_pct": self.atr_stop_pct,
            "risk_per_share": risk_per_share,
        }

        logger.info("%s ORB ENTRY %s %s @ %.2f stop %.2f tgt %.2f",
                    bar.name, trade_type.name, qty, entry_px, stop_px, target_px)

        return TradeSignal(
            signal=SignalType.OPEN,
            symbol=ticker,
            trade_type=trade_type,
            price=entry_px,
            quantity=qty,
            metadata=meta,
            source_bar=bar,
        )

    # ------------------------------------------------------------------ #
    #  Exit helpers                                                      #
    # ------------------------------------------------------------------ #
    def _exit_market(
        self,
        ticker: str,
        bar: pd.Series,
        price_override: float | None = None,
        reason: str = "",
    ) -> List[TradeSignal]:
        pos = self.ctx["day"].pos
        px = price_override if price_override is not None else bar.open
        trade_type = TradeType.SELL if pos.side == "LONG" else TradeType.BUY

        sig = TradeSignal(
            symbol=ticker,
            signal=SignalType.CLOSE,
            trade_type=trade_type,
            price=px,
            quantity=pos.qty,
            metadata={"exit_reason": reason},
            source_bar=bar,
        )
        self.close_pos(self.ctx)
        return [sig]
