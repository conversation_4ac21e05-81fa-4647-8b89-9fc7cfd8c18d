from __future__ import annotations
import logging
from datetime import timedelta, datetime
from typing import Optional, Sequence

import pandas as pd
from trading_framework.core import BarSlice
from trading_framework.mixins import IntradayStrategy
from strategies.trade_signals import TradeSignal, SignalType, TradeType

logger = logging.getLogger(__name__)


class ExampleStrategy(IntradayStrategy):
    """
    Emits an *OPEN‑BUY* on even bars,
    followed by a *CLOSE‑SELL* on the next bar.

    ►   Ensures we never try to open a new short while still long,
        so the broker error disappears.
    """

    # -----------------------------------------------------------------
    # life‑cycle
    # -----------------------------------------------------------------
    def __init__(self) -> None:
        super().__init__()
        self.interval_counter: int  = 0
        # Removed self.in_long; using PositionMixin for position tracking

    def prepare(self, meta) -> tuple[timedelta, timedelta]:
        # 1‑minute bars, 1‑day lookback – identical to old version
        return timedelta(minutes=1), timedelta(days=1)

    # -----------------------------------------------------------------
    # bar handler
    # -----------------------------------------------------------------
    def process_bar(self, bars: BarSlice) -> Optional[Sequence[TradeSignal]]:
        sigs: list[TradeSignal] = []
        hist, latest = bars
        if latest.empty:
            return None
        
        ticker = self.ctx.get("ticker")
        current_ts = self.ctx["ind"].get("current_timestamp")
        
        current_price = self.ctx["ind"].get("current_price")

        if self.interval_counter % 2 == 0:
            # ----------------------------------------------------------
            # EVEN bar  ->  open long (if flat)
            # ----------------------------------------------------------
            if not self.in_pos(self.ctx):
                sigs.append(
                    TradeSignal(
                        symbol     = ticker,
                        signal     = SignalType.OPEN,
                        trade_type = TradeType.BUY,
                        price      = current_price * .95,
                        quantity   = 1,
                        metadata   = {"action": "buy", "interval": self.interval_counter},
                        source_bar = latest.iloc[0],
                    )
                )
                # record the new long in DayContext
                self.open_pos(
                    self.ctx,
                    side="LONG",
                    qty=1,
                    entry_px=current_price * .95,
                    entry_ts=current_ts,
                    stop_px=0.0,
                    target_px=0.0,
                )
                logger.info("Generated BUY-OPEN at interval %d", self.interval_counter)
            else:
                logger.debug("Already long; skipping BUY on even interval")

        else:
            # ----------------------------------------------------------
            # ODD bar  ->  close long (if in position)
            # ----------------------------------------------------------
            if self.in_pos(self.ctx):
                sigs.append(
                    TradeSignal(
                        symbol     = ticker,
                        signal     = SignalType.CLOSE,
                        trade_type = TradeType.SELL,
                        price      = current_price * 1.05,
                        quantity   = 1,
                        metadata   = {"action": "sell", "interval": self.interval_counter},
                        source_bar = latest.iloc[0],
                    )
                )
                # clear position in DayContext
                self.close_pos(self.ctx)
                logger.info("Generated SELL-CLOSE at interval %d", self.interval_counter)
            else:
                logger.debug("Flat; nothing to close on odd interval")

        self.interval_counter += 1
        return sigs or None
