from __future__ import annotations

import logging
from datetime import datetime, timedelta, time
from typing import Any, Dict, List, Optional

import pandas as pd
from pytz import timezone

from trading_framework.mixins import IntradayStrategy
from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome
from strategies.trade_signals import TradeSignal, SignalType, TradeType
from tickers.iscancriteria import IScanCriteria

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# ─────────────────────────────────────────────────────────────────────────────
# Helper – ATR (simple moving average of True‑Range)
# -----------------------------------------------------------------------------

def _calc_atr(df: pd.DataFrame, length: int = 14) -> pd.Series:
    prev_close = df["close"].shift(1)
    tr = pd.concat([
        df["high"] - df["low"],
        (df["high"] - prev_close).abs(),
        (df["low"] - prev_close).abs(),
    ], axis=1).max(axis=1)
    return tr.rolling(length, min_periods=length).mean()


class GapFirstRedShortStrategy(IntradayStrategy):
    """First‑red gap short with per‑lot risk management."""

    # ───────────────────────────── Init ────────────────────────────────
    def __init__(
        self,
        *,
        min_gap_up_pct: float = 50.0,
        min_premkt_dollar_volume: float = 250_000,
        min_price_threshold: float = 0.75,
        atr_length: int = 14,
        atr_multiplier: float = 0.5,
        max_r_multiple: float = 10.0,
        entry_start_time: time = time(4, 0),
        entry_end_time: time = time(9, 29),
        end_of_day_exit: time = time(15, 55),
    ) -> None:
        super().__init__()
        self.min_gap_up_pct = float(min_gap_up_pct)
        self.min_premkt_dollar_volume = float(min_premkt_dollar_volume)
        self.min_price_threshold = float(min_price_threshold)
        self.atr_length = int(atr_length)
        self.atr_multiplier = float(atr_multiplier)
        self.max_r_multiple = float(max_r_multiple)
        self.entry_start_time = entry_start_time
        self.entry_end_time = entry_end_time
        self.end_of_day_exit = end_of_day_exit
        logger.info(
            "GapFirstRedShortStrategy ⏰ %s‑%s, gap≥%.1f%%, ATR=%dx%.2f, R=%d",
            self.entry_start_time,
            self.entry_end_time,
            self.min_gap_up_pct,
            self.atr_length,
            self.atr_multiplier,
            self.max_r_multiple,
        )

    # ─────────────────────── Framework hooks ───────────────────────────
    def prepare(self, metadata: Dict[str, Any]) -> tuple[timedelta, timedelta]:
        # 1‑minute bars, look‑back 5 days
        return timedelta(minutes=1), timedelta(days=5)

    # Main loop
    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars  # closed bars, forming bar
        if hist.empty or latest.empty:
            logger.debug("process_bar skipped: hist.empty=%s, latest.empty=%s", hist.empty, latest.empty)
            return None

        # Framework / ctx helpers
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        indicators = self.ctx.get("ind", {})
        current_ts: datetime = indicators.get("current_timestamp")
        if current_ts is None:
            current_ts = latest.index[-1].to_pydatetime()
        logger.debug("%s - process_bar at %s: hist %d bars, latest %d bars", ticker, current_ts, len(hist), len(latest))

        # persistent per‑day list of open trades
        open_trades: List[Dict[str, Any]] = day.custom.setdefault("open_trades", [])
        signals: List[TradeSignal] = []

        # ─── 1. Exit logic (iterate over a *copy* because we remove) ───────
        last_closed = hist.iloc[-1]
        for trade in open_trades.copy():
            exited = False
            reason = None
            exit_px = None

            # stop‑loss (short ⇒ price ≥ stop)
            if last_closed["high"] >= trade["stop_px"]:
                exit_px = trade["stop_px"]
                reason = "STOP_LOSS"
                exited = True
            # profit target
            elif last_closed["low"] <= trade["target_px"]:
                exit_px = trade["target_px"]
                reason = f"{self.max_r_multiple}R"
                exited = True
            # end‑of‑day
            elif current_ts.time() >= self.end_of_day_exit:
                exit_px = latest["open"].iloc[0]
                reason = "END_OF_DAY"
                exited = True

            if exited:
                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.CLOSE,
                    trade_type=TradeType.BUY,
                    price=exit_px,
                    quantity=trade["qty"],
                    metadata={"exit_reason": reason},
                    source_bar=trade["source_bar"],  # tie back to original entry
                )
                signals.append(sig)
                # Remove by identity to avoid ambiguous pandas Series comparison
                open_trades[:] = [t for t in open_trades if t is not trade]
                logger.info("%s: closed lot from %s @ %.2f (%s)", ticker, trade["source_bar"].name, exit_px, reason)

        # ─── 2. Entry gate (time window) ───────────────────────────────────
        now_t = current_ts.time()
        if not (self.entry_start_time <= now_t <= self.entry_end_time):
            logger.debug("%s - outside entry window (%s not in %s-%s)", ticker, now_t, self.entry_start_time, self.entry_end_time)
            return signals or None

        # ─── 3. Entry conditions ───────────────────────────────────────────
        # must be gap‑up
        if not self._is_gap_up(hist):
            logger.debug("%s - not gap-up or below threshold", ticker)
            return signals or None

        signal_bar = hist.iloc[-1]
        if signal_bar["close"] >= signal_bar["open"]:
            logger.debug("%s - last bar not red (open=%.2f close=%.2f)", ticker, signal_bar["open"], signal_bar["close"])
            return signals or None  # not red

        # Liquidity filter
        if signal_bar["close"] * signal_bar["volume"] < self.min_premkt_dollar_volume:
            logger.debug("%s - liquidity filter failed (dollar_volume=%.2f < threshold %.2f)", ticker, signal_bar["close"] * signal_bar["volume"], self.min_premkt_dollar_volume)
            return signals or None

        # ATR warm‑up
        if len(hist) < self.atr_length + 1:
            logger.debug("%s - ATR warm-up: need %d bars, have %d", ticker, self.atr_length + 1, len(hist))
            return signals or None
        atr_val = _calc_atr(hist, self.atr_length).iloc[-1]
        if pd.isna(atr_val):
            logger.debug("%s - ATR value NA", ticker)
            return signals or None

        entry_px = latest["open"].iloc[0]
        stop_px = signal_bar["high"] + self.atr_multiplier * atr_val
        risk = stop_px - entry_px
        if risk <= 0:
            logger.debug("%s - risk <= 0 (entry %.2f stop %.2f)", ticker, entry_px, stop_px)
            return signals or None
        target_px = entry_px - self.max_r_multiple * risk

        qty = self.position_manager.calculate_position_size(
            ticker=ticker,
            hist_data=hist,
            price=entry_px,
            stop_loss=stop_px,
        )
        if qty <= 0:
            logger.debug("%s - position size <= 0, skip (entry %.2f stop %.2f)", ticker, entry_px, stop_px)
            return signals or None

        # register new trade lot
        trade = {
            "source_bar": signal_bar,
            "entry_px": entry_px,
            "stop_px": stop_px,
            "target_px": target_px,
            "qty": qty,
        }
        open_trades.append(trade)

        sig = TradeSignal(
            symbol=ticker,
            signal=SignalType.OPEN,
            trade_type=TradeType.SELL,
            price=entry_px,
            quantity=qty,
            metadata={
                "stop_price": stop_px,
                "target_price": target_px,
                "risk": risk,
                "entry_type": "FIRST_RED",
            },
            source_bar=signal_bar,
        )
        signals.append(sig)
        logger.info("%s: opened lot @ %.2f; stop %.2f, target %.2f", ticker, entry_px, stop_px, target_px)

        return signals or None

    # ───────────────────────── Exec report ──────────────────────────────
    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        """Handle execution feedback.

        We keep our own per-lot accounting in ``day.custom['open_trades']``.  The
        execution handler therefore only needs to update / clean up that list
        based on what actually happened at the broker.

        1.  OPEN order failures – simply *remove* the corresponding lot from
            ``open_trades`` so the strategy forgets about it.  We consider both
            outright failures (REJECTED, CANCELLED, ERROR) **and** zero-fill
            PARTIALs (``filled_qty == 0``).
        2.  CLOSE order fills – reduce or remove the matching lot size.  If the
            order only partially filled we keep the remainder and the normal
            exit logic will retry on the next bar.
        3.  CLOSE order failures – do nothing; the exit logic will try again
            next bar.
        """
        day     = self.ctx["day"]
        ticker  = self.ctx.get("ticker")
        open_trades: List[dict] = day.custom.get("open_trades", [])

        sig      = report.signal
        outcome  = report.outcome

        # Helper: find the trade that corresponds to this execution (if any)
        def _find_trade() -> Optional[dict]:
            for t in open_trades:
                sb = t.get("source_bar")
                if sb is not None and sig.source_bar is not None and sb.name == sig.source_bar.name:
                    return t
            return None

        # ── Successful / partial fills ──────────────────────────────────
        if outcome in (ExecOutcome.FILLED, ExecOutcome.PARTIAL):
            if sig.signal == SignalType.CLOSE:
                trade = _find_trade()
                if trade is None:
                    return []  # nothing to update

                # Reduce qty based on fill amount; treat FILLED as full size
                filled = report.filled_qty or trade.get("qty", 0)
                remaining = trade["qty"] - filled
                if remaining <= 0:
                    # Remove by identity to avoid ambiguous pandas Series comparison
                    open_trades[:] = [t for t in open_trades if t is not trade]
                    logger.info("%s: lot fully closed via exec %s – removed", ticker, report.order_id)
                else:
                    trade["qty"] = remaining
                    logger.info("%s: lot partially closed (%s/%s) – %s left", ticker, filled, filled+remaining, remaining)
            # No updates needed for OPEN fills – we already accounted when the
            # lot was created earlier in process_bar.
            return []

        # ── Order failed ────────────────────────────────────────────────
        logger.warning("%s exec %s – %s", report.signal.symbol, outcome, report.broker_msg)

        if sig.signal == SignalType.OPEN:
            # Remove the pending lot so we don't manage a position that never
            # existed at the broker.
            trade = _find_trade()
            if trade:
                # Remove by identity to avoid ambiguous pandas Series comparison
                open_trades[:] = [t for t in open_trades if t is not trade]
                logger.info("%s: removed lot due to OPEN failure – %s", ticker, outcome.name)
        # For CLOSE failures we intentionally keep the lot – process_bar will
        # re-emit another CLOSE signal next bar.

        return []

    # ───────────────────────── Helpers ──────────────────────────────────
    def _is_gap_up(self, hist: pd.DataFrame) -> bool:
        ticker = self.ctx.get("ticker")
        if hist.empty:
            logger.debug("%s - _is_gap_up: hist empty -> False", ticker)
            return False
        today = hist.index[-1].date()
        prev_rth = hist[(hist.index.date < today) & (hist.index.time >= time(9, 30)) & (hist.index.time < time(16, 0))]
        if prev_rth.empty:
            logger.debug("%s - _is_gap_up: prev_rth empty -> False", ticker)
            return False
        prev_close = prev_rth["close"].iloc[-1]
        today_rows = hist[hist.index.date == today]
        if today_rows.empty:
            logger.debug("%s - _is_gap_up: today_rows empty -> False", ticker)
            return False
        last_close = today_rows["close"].iloc[-1]
        gap = (last_close - prev_close) / prev_close * 100
        logger.debug("%s - _is_gap_up: prev_close=%.2f last_close=%.2f gap=%.2f%% (threshold %.2f%%)", ticker, prev_close, last_close, gap, self.min_gap_up_pct)
        return gap >= self.min_gap_up_pct
