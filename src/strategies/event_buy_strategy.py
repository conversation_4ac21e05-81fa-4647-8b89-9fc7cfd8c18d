from __future__ import annotations

"""
Event‑Driven Intraday Buy Strategy
----------------------------------
This strategy is instantiated **once per ticker** produced by an external event
feed (e.g. `TickerSourceDiscoverAdapter`).

* **Entry** – Go **LONG** inside **15 minutes** after the event **during regular
  trading hours** (RTH: 09:30‑16:00 ET).
* **Stop‑loss** – The *lowest price of the day so far* (updated every bar).
* **Exit** – Liquidate at 15:55 ET if still open.

All datetimes are timezone‑aware and normalised to **US/Eastern**.  The strategy
relies on the utility functions in `tools.clock` for market‑calendar logic.
"""

from datetime import datetime, timedelta, time
from typing import Any, List, Optional
import logging

import numpy as np
import pandas as pd

from trading_framework.mixins import IntradayStrategy
from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome

from strategies.trade_signals import TradeSignal, SignalType, TradeType

# ⇩ New: use the global clock helpers instead of a ctx‑injected object
from tools.clock import is_regular_hours, is_trading_day, get_session_bounds

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class EventBuyStrategy(IntradayStrategy):
    """Intraday strategy that reacts to ticker events from an external feed."""

    def __init__(
        self,
        entry_delay: timedelta = timedelta(minutes=15),
        entry_window_duration: timedelta = timedelta(minutes=5),
        exit_time: time = time(15, 55),
        min_dollar_vol: float = 1000.0,
        target_risk_fraction: float = 0.1,
        initial_equity: float = 100_000.0,
        max_leverage: float = 1.0,
    ) -> None:
        super().__init__()
        self.entry_delay = entry_delay
        self.entry_window_duration = entry_window_duration
        self.exit_time = exit_time
        self.min_dollar_vol = min_dollar_vol
        self.target_risk_fraction = target_risk_fraction
        self.initial_equity = initial_equity
        self.max_leverage = max_leverage
        self.event_ts: datetime | None = None  # injected in :meth:`prepare`

        logger.info(
            "Initialized EventBuyStrategy: entry_delay=%s, entry_window_duration=%s, exit_time=%s",
            self.entry_delay,
            self.entry_window_duration,
            self.exit_time,
        )

    # ────────────────────────────────────────────────────────────────────
    # Framework hooks
    # ------------------------------------------------------------------
    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]:
        """Capture the event timestamp and declare data requirements."""
        self.event_ts = metadata.get("timestamp")
        if self.event_ts is None:
            raise ValueError("EventBuyStrategy expects 'timestamp' in metadata")

        # 1‑minute bars; need only today's data for low‑of‑day calculations
        return timedelta(minutes=1), timedelta(days=1)

    def _avg_dollar_vol(self, df: pd.DataFrame) -> float:
        """Average per-minute dollar volume since open."""
        if df is None or df.empty or 'volume' not in df.columns or 'close' not in df.columns:
            return 0.0
        return float((df['close'] * df['volume']).mean())

    def _intraday_sigma_price(self, pre_minutes: pd.DataFrame, entry_price: float) -> float | None:
        """
        Realized vol estimate using minutes from open->signal, scaled to full day.
        - Compute minute log returns r_i = ln(P_t/P_{t-1}) on 'close'.
        - Daily RV estimate ≈ (390/n) * sum(r_i^2); sigma = sqrt(RV).
        - Convert to price-vol: sigma_price ≈ entry_price * sigma_return.
        """
        if pre_minutes is None or pre_minutes.empty or 'close' not in pre_minutes.columns:
            return None
        # need at least a few minutes to be meaningful
        if len(pre_minutes) < 5:
            return None
        px = pre_minutes['close'].astype(float)
        r = np.log(px / px.shift(1)).dropna()
        n = len(r)
        if n == 0:
            return None
        rv_daily = (390.0 / n) * float((r**2).sum())
        if rv_daily <= 0 or not np.isfinite(rv_daily):
            return None
        sigma_return = np.sqrt(rv_daily)
        return float(entry_price * sigma_return)

    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars
        if hist.empty or latest.empty:
            return None

        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        indicators = self.ctx["ind"]
        current_ts: datetime = indicators.get("current_timestamp")

        # Filter historical bars to **today** only. Previous sessions are ignored
        today_mask = hist.index.date == current_ts.date()
        hist_today = hist[today_mask]

        # ─── End‑of‑day exit ────────────────────────────────────────────
        if self.in_pos(self.ctx) and current_ts.time() >= self.exit_time:
            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.CLOSE,
                trade_type=TradeType.SELL,
                price=latest["open"].iloc[0],
                quantity=day.pos.qty,
                metadata={"exit_reason": "END_OF_DAY"},
                source_bar=latest.iloc[0],
            )
            self.close_pos(self.ctx)
            day.custom["traded"] = True
            logger.info("%s: EOD exit", ticker)
            return [sig]

        # ─── One trade per day ──────────────────────────────────────────
        if day.custom.get("traded"):
            return None

        # ─── Entry conditions ──────────────────────────────────────────
        entry_start_ts = self.event_ts + self.entry_delay
        entry_end_ts = entry_start_ts + self.entry_window_duration
        within_window = entry_start_ts <= current_ts <= entry_end_ts

        if (
            not self.in_pos(self.ctx)
            and within_window
            and is_regular_hours(current_ts)
            and is_trading_day(current_ts)
        ):
            pre_signal_bars = hist_today.copy()

            avg_dv = self._avg_dollar_vol(pre_signal_bars)
            if avg_dv < self.min_dollar_vol:
                logger.warning("%s: Low liquidity (%.2f < %.2f); skipping", ticker, avg_dv, self.min_dollar_vol)
                day.custom["traded"] = True # Skip for the rest of the day
                return None

            entry_price = latest["open"].iloc[0]
            
            per_share_risk = self._intraday_sigma_price(pre_signal_bars, entry_price)

            if not per_share_risk or per_share_risk <= 0 or not np.isfinite(per_share_risk):
                logger.warning("%s: Invalid per-share risk; using default 1", ticker)
                per_share_risk = 1

            qty = int((self.initial_equity * self.target_risk_fraction) / per_share_risk)
            
            max_shares_allowed = int((self.max_leverage * self.initial_equity) / entry_price)
            if qty > max_shares_allowed:
                qty = max_shares_allowed

            if qty <= 0:
                logger.warning("%s: position size <= 0; skipping", ticker)
                day.custom["traded"] = True
                return None

            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.OPEN,
                trade_type=TradeType.BUY,
                price=entry_price,
                quantity=qty,
                metadata={
                    "entry_type": "EVENT_BUY_VOL_TARGET",
                    "event_ts": self.event_ts,
                    "per_share_risk": per_share_risk,
                },
                source_bar=latest.iloc[0],
            )

            self.open_pos(
                self.ctx,
                side="LONG",
                qty=qty,
                entry_px=entry_price,
                entry_ts=current_ts,
                stop_px=0, # No stop loss
                target_px=0,
            )
            day.custom["traded"] = True
            logger.info("%s: Bought %d @ %.2f (risk %.2f)", ticker, qty, entry_price, per_share_risk)
            return [sig]

        return None

    # ────────────────────────────────────────────────────────────────────
    # Execution‑report handling                                            
    # ------------------------------------------------------------------
    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]

        if report.outcome != ExecOutcome.FILLED:
            logger.warning(
                "%s: Execution %s (%s) – exiting strategy for the day.",
                ticker,
                report.outcome,
                report.broker_msg
            )
            # If an entry order fails, we might want to reset to allow another attempt
            # For now, we exit for the day as per original logic.
            self.close_pos(self.ctx)
            day.custom["traded"] = True
            return [TradeSignal.exit_on_execution_failure(report)]
        return []
