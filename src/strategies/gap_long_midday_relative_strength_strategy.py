from __future__ import annotations

from typing import Optional, List, Any, Dict
from datetime import datetime, timedelta, time, date
import logging
import os
from pathlib import Path
from abc import ABC, abstractmethod


import pandas as pd
from pytz import timezone

from trading_framework.mixins import IntradayStrategy
from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome
from strategies.trade_signals import TradeSignal, SignalType, TradeType
from tickers.iscancriteria import IScanCriteria
from marketdata.imarketdata import IMarketData

from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

tz = timezone("US/Eastern")
MAX_FETCH_WORKERS = 16

# ──────────────────────────────────────────────────────────────────────
# Ranking API
# ──────────────────────────────────────────────────────────────────────
class RankCriterion(ABC):
    """Pluggable score generator (lower score ⇒ weaker / laggard)."""

    ascending: bool = True          # True ⇒ sort ascending
    _scores: Dict[str, float]

    def __init__(self) -> None:
        self._scores = {}

    @abstractmethod
    def prepare(                     # noqa: D401
        self,
        *,
        market_data: Optional[IMarketData] = None,
        symbols: Optional[List[str]] = None,
        day: Optional[date] = None,
        now: Optional[datetime] = None,
        **kwargs,
    ) -> None:
        """Populate ``_scores`` (in-place)."""

    # ..................................................................
    @property
    def scores(self) -> Dict[str, float]:
        return self._scores

    # ..................................................................
    def top_n(self, n: int) -> List[str]:
        if not self._scores:
            logger.warning("No scores to rank")
            return []
        return [s for s, _ in sorted(self._scores.items(), key=lambda kv: kv[1], reverse=not self.ascending)][:n]

# ──────────────────────────────────────────────────────────────────────
# Concrete criteria
# ──────────────────────────────────────────────────────────────────────
class ReturnSincePMOpenRank(RankCriterion):
    """Return = (latest_close ÷ 04:00_open) − 1, *caller supplies closes*."""

    ascending = True

    def __init__(self, pm_open_cache: Dict[date, Dict[str, float]]) -> None:
        super().__init__()
        self.pm_open_cache = pm_open_cache

    # ..................................................................
    def prepare(                     # noqa: D401
        self,
        *,
        day: date,
        latest_closes: Dict[str, float],
        **kwargs,
    ) -> None:
        pm_opens = self.pm_open_cache.get(day, {})
        if not pm_opens or not latest_closes:
            logger.warning("ReturnSincePMOpenRank – missing pm_opens or latest_closes for %s",
                           day)
            return

        # work on the intersection only
        for sym in pm_opens.keys() & latest_closes.keys():
            o4   = pm_opens[sym]
            pnow = latest_closes[sym]
            if o4 and pnow:
                self._scores[sym] = (pnow / o4) - 1.0


class VWAPTWAPDistanceRank(RankCriterion):
    """Score = *VWAP − TWAP* from 04:00 to *now* (smaller ⇒ weaker)."""

    ascending = True

    # ..................................................................
    def prepare(
        self,
        *,
        market_data: IMarketData,
        symbols: List[str],
        day: date,
        now: datetime,
        prev_closes: Optional[Dict[str, float]] = None,
        min_gap_up_pct: Optional[float] = None,
        **kwargs,
    ) -> None:
        if not symbols:
            return

        # default fetch start at 04:00 Eastern
        default_start = tz.localize(datetime.combine(day, time(4, 0)))
        # build per-symbol gap threshold if we have prev_closes
        threshold_map: Dict[str, float] = {}
        if prev_closes and min_gap_up_pct is not None:
            threshold_map = {
                sym: prev_closes[sym] * (1 + min_gap_up_pct / 100.0)
                for sym in symbols
                if sym in prev_closes
            }

        def _calc_score(sym: str) -> Optional[float]:
            try:
                df = market_data.gather_historical_data(sym, default_start, now, interval=60)
                if df is None or df.empty:
                    return None
                # if we know the gap threshold, slice from first breakout bar
                if sym in threshold_map and "high" in df.columns:
                    thr = threshold_map[sym]
                    highs = df["high"].astype(float)
                    br = df[highs >= thr]
                    if not br.empty:
                        df = df.loc[br.index[0]:]
                price = df["close"].astype(float)
                vol = df["volume"].astype(float)
                if vol.sum() == 0:
                    return None
                vwap = (price * vol).sum() / vol.sum()
                twap = price.mean()
                return vwap - twap
            except Exception as e:
                logger.debug("%s – VWAP/TWAP fetch failed: %s", sym, e)
                return None

        # run in parallel
        with ThreadPoolExecutor(max_workers=MAX_FETCH_WORKERS) as pool:
            futures = {pool.submit(_calc_score, s): s for s in symbols}
            total = len(futures)
            count = 0

            for fut in as_completed(futures):
                count += 1
                sym = futures[fut]
                score = fut.result()
                if score is not None:
                    self._scores[sym] = score

                if count % 100 == 0 or count == total:
                    logger.info(
                        "VWAPTWAPDistanceRank – processed %d/%d symbols", count, total
                    )
                    logger.info(f"Scores: {self._scores}")


class GapLongMiddayRelativeStrengthStrategy(IntradayStrategy, IScanCriteria):
    """Afternoon long on morning gap‑ups that *under‑performed* intraday."""


    # ..................................................................
    def __init__(
        self,
        *,
        min_gap_up_pct: float = 60.0,
        min_dollar_rth_volume: float = 500_000, # We already have a pre-market filter
        top_n: int = 2,
        entry_time: time = time(13, 0),
        exit_time: time = time(15, 55),
        min_price_threshold: float = 0.5,
        min_dollar_premarket_volume: float = 500_000,
        ticker_info_scan_criteria: Optional[IScanCriteria] = None,
        market_data: IMarketData,
        rank_criterion: Optional[RankCriterion] = None,
    ) -> None:
        super().__init__()
        self.min_gap_up_pct = float(min_gap_up_pct)
        self.min_dollar_rth_volume = float(min_dollar_rth_volume)
        self.min_dollar_premarket_volume = float(min_dollar_premarket_volume)
        self.top_n = int(top_n)
        self.entry_time = entry_time
        self.exit_time = exit_time
        self.min_price_threshold = float(min_price_threshold)
        self.ticker_info_scan_criteria = ticker_info_scan_criteria
        self.market_data = market_data

        # Internal caches ------------------------------------------------
        self.market_open_time: time = time(9, 30)
        self._pm_open_cache: Dict[date, Dict[str, float]] = {}
        self._pre_filter_done_date: Optional[date] = None
        self._filtered_syms_today: List[str] = []
        self.scanned_date: Optional[date] = None
        self._prev_closes_for_rank: Dict[str, float] = {}

        # Ranking --------------------------------------------------------
        self.rank_criterion = rank_criterion or ReturnSincePMOpenRank(self._pm_open_cache) # or VWAPTWAPDistanceRank() 

        logger.info(
            "Initialised GapLongMiddayRelativeStrength v2.1: gap≥%.1f%%, min$RTHvol≥%.0f, min$PMvol≥%.0f, top_n=%d, rank=%s",
            self.min_gap_up_pct,
            self.min_dollar_rth_volume,
            self.min_dollar_premarket_volume,
            self.top_n,
            self.rank_criterion.__class__.__name__,
        )

    # ───────────────────────────────────────────────────────────────────
    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]:
        # 1-minute bars, 3-day look-back
        return timedelta(minutes=1), timedelta(days=5)

    # ───────────────────────────────────────────────────────────────────
    def process_bar(self, bars: BarSlice):
        hist, latest = bars
        if hist.empty or latest.empty:
            return None

        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        now = self.ctx["ind"].get("current_timestamp")

        # End-of-day exit
        if self.in_pos(self.ctx) and now.time() >= self.exit_time:
            o = latest["open"].iloc[0]
            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.CLOSE,
                trade_type=TradeType.SELL,
                price=o,
                quantity=day.pos.qty,
                metadata={"exit_reason": "END_OF_DAY"},
                source_bar=latest.iloc[0],
            )
            self.close_pos(self.ctx)
            day.custom["traded"] = True
            logger.info("%s – closed EOD (%s @ %.2f)", ticker, day.pos.qty, o)
            return [sig]

        # Stop-loss at session LoD
        if self.in_pos(self.ctx):
            stop_px = day.pos.stop_px
            if hist["low"].iloc[-1] <= stop_px:
                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.CLOSE,
                    trade_type=TradeType.SELL,
                    price=stop_px,
                    quantity=day.pos.qty,
                    metadata={"exit_reason": "STOP_LOSS"},
                    source_bar=latest.iloc[0],
                )
                self.close_pos(self.ctx)
                day.custom["traded"] = True
                logger.info("%s – stop-loss hit at %.2f", ticker, stop_px)
                return [sig]

        # Only one trade per day
        if day.custom.get("traded"):
            return None

        if (not self.in_pos(self.ctx)) and (now.time() >= self.entry_time):
            if day.custom.get("entry_checked"):
                return None
            day.custom["entry_checked"] = True

            today_hist = hist[hist.index.date == now.date()]
            if today_hist.empty:
                return None

            # Pre-market dollar-volume check
            pm_hist = today_hist[today_hist.index.time < self.market_open_time]
            pm_dvol = (pm_hist["close"] * pm_hist["volume"]).sum() if not pm_hist.empty else 0
            if pm_dvol < self.min_dollar_premarket_volume:
                logger.debug(
                    f"{ticker} – pre-market dollar volume {pm_dvol:.2f} below threshold "
                    f"{self.min_dollar_premarket_volume:.2f}, skipping entry"
                )
                return None

            entry_px = latest["open"].iloc[0]
            # compute stop loss as the min low after first bar exceeding gap threshold from prev close
            prev_hist = hist[hist.index.date < now.date()]
            # only include regular trading hours (9:30–16:00) from prior days
            prev_hist = prev_hist.between_time(self.market_open_time, time(16, 0))
            if prev_hist.empty:
                return None
            prev_close = prev_hist["close"].iloc[-1]
            threshold_price = prev_close * (1 + self.min_gap_up_pct / 100.0)
            # find first breakout bar
            breakouts = today_hist[today_hist["high"] >= threshold_price]
            if not breakouts.empty:
                first_idx = breakouts.index[0]
                stop_px = today_hist.loc[first_idx:, "low"].min()
            else:
                stop_px = today_hist["low"].min()
            if entry_px <= 0 or stop_px >= entry_px:
                return None

            qty = self.position_manager.calculate_position_size(
                ticker=ticker,
                hist_data=hist,
                price=entry_px,
                stop_loss=stop_px,
            )
            if qty <= 0:
                return None

            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.OPEN,
                trade_type=TradeType.BUY,
                price=entry_px,
                quantity=qty,
                metadata={"stop_price": stop_px, "entry_type": "PM_LAGGARD"},
                source_bar=latest.iloc[0],
            )
            self.open_pos(
                self.ctx,
                side="LONG",
                qty=qty,
                entry_px=entry_px,
                entry_ts=now,
                stop_px=stop_px,
                target_px=0.0,
            )
            day.custom["traded"] = True
            logger.info("%s – LONG %s @ %.2f (stop %.2f)", ticker, qty, entry_px, stop_px)
            return [sig]

        return None

   
    # ------------------------------------------------------------------
    # Pre-market filter helper
    # ------------------------------------------------------------------
    def _run_premarket_filter(
        self,
        *,
        historical_df: pd.DataFrame,
        current_day_df: pd.DataFrame,
        day: date,
    ) -> List[str]:
        base_syms: List[str] = self.ticker_info_scan_criteria.scan(historical_df, current_day_df)
        if not base_syms:
            return []
        
        logger.info(f"Total post filtered symbols: {len(base_syms)}")

        start_pm = tz.localize(datetime.combine(day, time(4, 0)))
        end_pm = tz.localize(datetime.combine(day, time(9, 29)))

        stats: Dict[str, Dict[str, float]] = {}

        def _fetch_stats(sym: str):
            try:
                df = self.market_data.gather_historical_data(sym, start_pm, end_pm, interval=1800)
                if df is None or df.empty:
                    return
                hi = float(df["high"].max())
                o4 = float(df.iloc[0]["open"])
                pm_dvol = float((df["close"] * df["volume"]).sum())
                stats[sym] = {"pm_high": hi, "pm_open": o4, "pm_dvol": pm_dvol}
            except Exception as e:
                logger.error("%s – pre-market 30-min fetch failed: %s", sym, e)

        with ThreadPoolExecutor(max_workers=MAX_FETCH_WORKERS) as pool:
            futures = [pool.submit(_fetch_stats, s) for s in base_syms]

            total = len(futures)
            count = 0

            for fut in as_completed(futures):
                count += 1
                try:
                    fut.result()
                except Exception as e:
                    pass

                if count % 100 == 0 or count == total:
                    logger.info(
                        "Pre-market stats fetched for %d/%d symbols", count, total
                    )

        if not stats:
            logger.info("No stats fetched for pre-market filter")
            return []

        agg = pd.DataFrame.from_dict(stats, orient="index")

        # Cache 04:00 opens for ranking criteria
        self._pm_open_cache.setdefault(day, {}).update(agg["pm_open"].to_dict())

        # Previous close ----------------------------------------------
        hist = historical_df.copy()
        hist["date"] = pd.to_datetime(hist["date"]).dt.date
        prev_close = (
            hist[hist["date"] < day].sort_values(["symbol", "date"]).groupby("symbol")["close"].last()
        )
        agg = agg.join(prev_close.rename("prev_close"), how="inner")
        
        # Current-day RTH $‑volume ------------------------------------
        today_rows = current_day_df.copy()
        today_rows["date"] = pd.to_datetime(today_rows["date"]).dt.date
        today_rows = today_rows[today_rows["date"] == day]
        today_rows = today_rows[today_rows["symbol"].isin(base_syms)]
        today_rows["rth_dvol"] = today_rows["close"] * today_rows["day_volume"]
        agg = agg.join(today_rows.groupby("symbol")["rth_dvol"].last(), how="inner")
        agg = agg.join(today_rows.groupby("symbol")["close"].last().rename("last_px"), how="inner")

        # Filters ------------------------------------------------------
        agg["gap_pct"] = (agg["pm_high"] - agg["prev_close"]) / agg["prev_close"] * 100.0
        agg["total_dvol"] = agg["rth_dvol"]
        mask = (
            (agg["gap_pct"] >= self.min_gap_up_pct)
            & (agg["total_dvol"] >= self.min_dollar_rth_volume)
            & (agg["last_px"] >= self.min_price_threshold)
        )
        
        filtered = agg[mask].index.tolist()
        logger.info("Pre-market filter → %d / %d symbols passed", len(filtered), len(base_syms))
        # capture the previous-close for each passed symbol
        self._prev_closes_for_rank = agg["prev_close"].loc[filtered].to_dict()
        return filtered

    # ------------------------------------------------------------------
    def scan(self, historical_df: pd.DataFrame, current_day_df: pd.DataFrame) -> List[str]:
        now = datetime.now(tz)
        today = now.date()

        if now.time() < time(9, 45):
            return []
        
        if self._pre_filter_done_date != today:
            self._filtered_syms_today = self._run_premarket_filter(historical_df=historical_df, current_day_df=current_day_df, day=today)
            self._pre_filter_done_date = today

        if not self._filtered_syms_today:
            logger.info("No symbols passed pre-market filter")
            return []
        
        today_rows = current_day_df.copy()
        today_rows["date"] = pd.to_datetime(today_rows["date"]).dt.date
        today_rows = today_rows[today_rows["date"] == today]

        latest_closes: Dict[str, float] = (
            today_rows[today_rows["symbol"].isin(self._filtered_syms_today)]
            .groupby("symbol")["close"]
            .last()
            .to_dict()
        )
        if not latest_closes:
            logger.error("No latest closes found for today %s", today)
            return []
        
        # ---- rank the survivors ----
        self.rank_criterion.prepare(
            market_data=self.market_data,
            symbols=self._filtered_syms_today,
            day=today,
            now=now,
            prev_closes=self._prev_closes_for_rank,
            min_gap_up_pct=self.min_gap_up_pct,
            latest_closes=latest_closes,
        )
        laggards = self.rank_criterion.top_n(self.top_n)
        logger.info(f"Ranked {len(laggards)} symbols: {laggards}")
                        
        # Emit once per day after entry_time ----------------------------
        if now.time() >= self.entry_time and self.scanned_date != today:
            self.scanned_date = today
            return laggards
        
        return []

    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]

        # Exit if any execution outcome other than FILLED
        if report.outcome != ExecOutcome.FILLED:
            logger.warning(
                "%s: Execution failed with outcome %s: %s. Resetting day context.",
                ticker,
                report.outcome,
                report.broker_msg,
            )
            # Reset the day context to allow the strategy to try again
            day.reset()
            return []
        # No special handling needed for FILLED outcomes
        return []