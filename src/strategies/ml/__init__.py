from __future__ import annotations

import datetime as _dt
import functools
import pathlib
import pickle
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional

import numpy as np
import pandas as pd
import logging

from indicators.qpi import qpi_single

# ---------------- Third‑party ML & TA ----------------------------------------
from ta.momentum import RSIIndicator
from ta.volatility import AverageTrueRange
from xgboost import XGBClassifier

# ---------------- Market‑data interface provided by user ---------------------
from abc import ABC, abstractmethod
import asyncio
from datetime import datetime

from marketdata import IMarketData


CACHE_DIR = pathlib.Path(".cache")
CACHE_DIR.mkdir(exist_ok=True)

TRAIN_YEARS_LOOKBACK = 1
PROB_THRESHOLD = 0.6
MAX_POSITIONS = 20
HOLD_DAYS = 6
STOP_LOSS_PCT = 0.05

# constants at top of Backtester
LOOKBACK_YEARS = 5
WINDOW_N       = 3
TRADING_DAYS   = 252

MIN_BARS_QPI   = LOOKBACK_YEARS * TRADING_DAYS + WINDOW_N + 1
HISTORY_SPAN   = MIN_BARS_QPI * (365 / TRADING_DAYS) + 30
LIQUIDITY_USD_MIN = 250_000                 
LIQUIDITY_MIN_PRICE = 1                 

logger = logging.getLogger(__name__)

@dataclass
class MarketDataAdapter:
    
    def __init__(self, prices):
        self.prices_d = prices

    def prices(self, symbol: str, start: _dt.date, end: _dt.date) -> pd.DataFrame:
        df = self.prices_d[symbol]

        # 1) Make sure the index really is datetime
        if not isinstance(df.index, pd.DatetimeIndex):
            df = df.copy()
            df.index = pd.to_datetime(df.index)

        # 2) Convert your python dates into Timestamps
        start_ts = pd.to_datetime(start - _dt.timedelta(days=HISTORY_SPAN)) 
        end_ts   = pd.to_datetime(end)

        # 3) If your DatetimeIndex has a tz, localize your Timestamps too
        tz = getattr(df.index, "tz", None)
        if tz:
            # only localize if they're naive
            if start_ts.tzinfo is None:
                start_ts = start_ts.tz_localize(tz)
                end_ts   = end_ts.tz_localize(tz)

        # 4) Now do label slicing
        return df.loc[start_ts:end_ts].copy()

    # ---------------------------------------------------------------------
    def median_adv(self, symbol: str, asof: _dt.date, window: int = 63) -> float:
        start = asof - _dt.timedelta(days=window * 2)  # buffer for weekends
        df = self.prices(symbol, start, asof - _dt.timedelta(days=1))
        if df.empty:
            return np.nan
        return float((df["close"] * df["volume"]).rolling(window).median().iloc[-1])


# ----------------------------------------------------------------------------
# 3. Feature Engineering (unchanged)
# ----------------------------------------------------------------------------

@dataclass
class FeatureEngineer:
    roc_windows: Tuple[int, ...] = (2, 5, 20, 63, 126, 252)
    rsi_windows: Tuple[int, ...] = (2, 3, 5, 14)
    atr_window: int = 14
    sma_window: int = 200

    def transform(self, px: pd.DataFrame) -> pd.DataFrame:
        df = px.copy()
        for w in self.roc_windows:
            df[f"roc_{w}"] = df["close"].pct_change(w)
        for w in self.rsi_windows:
            df[f"rsi_{w}"] = RSIIndicator(df["close"], window=w).rsi() / 100.0
        atr = AverageTrueRange(
            df["high"], df["low"], df["close"], window=self.atr_window
        ).average_true_range()
        df["atr_norm"] = atr / df["close"]
        df["ibs"] = (df["close"] - df["low"]) / (df["high"] - df["low"]).replace(0, np.nan)
        df["sma200_dist"] = (df["close"] / df["close"].rolling(self.sma_window).mean()) - 1
        return df

    def feature_names(self) -> List[str]:
        """List of all feature columns produced by transform."""
        names = []
        names += [f"roc_{w}" for w in self.roc_windows]
        names += [f"rsi_{w}" for w in self.rsi_windows]
        names += ["atr_norm", "ibs", "sma200_dist"]
        return names

# ----------------------------------------------------------------------------
# 4. ML training utilities (unchanged)
# ----------------------------------------------------------------------------


@dataclass
class ModelTrainer:
    feature_cols: List[str]
    label_col: str = "target"
    model_params: Dict = field(
        default_factory=lambda: dict(
            n_estimators=200,
            max_depth=4,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            eval_metric="logloss",
            n_jobs=-1,
            random_state=42,
        )
    )

    def fit(self, train: pd.DataFrame) -> XGBClassifier:
        X = train[self.feature_cols]
        y = train[self.label_col]
        model = XGBClassifier(**self.model_params)
        model.fit(X, y)
        return model

    def yearly_models(
        self, all_data: pd.DataFrame, first_year: int, last_year: int
    ) -> Dict[int, XGBClassifier]:
        models: Dict[int, XGBClassifier] = {}
        
        dates = all_data.index.get_level_values("date")
        if hasattr(dates, "tz") and dates.tz is not None:
            dates = dates.tz_convert(None)
        dates = dates.date
        
        for yr in range(first_year, last_year + 1):    
            train_end   = _dt.date(yr - 1, 12, 31)
            train_start = _dt.date(yr - TRAIN_YEARS_LOOKBACK, 1, 1)
            
            print(f'{yr} {train_start} {train_end}')
            mask = (dates >= train_start) & (dates <= train_end)
            train_df = all_data.loc[mask]
            if train_df.empty:
                continue
            models[yr] = self.fit(train_df.dropna())
            with open(CACHE_DIR / f"model_{yr}.pkl", "wb") as fh:
                pickle.dump(models[yr], fh)
        return models



# ---------------------------------------------------------------------------
#   Core – SimpleBacktester
# ---------------------------------------------------------------------------

# ---------------------------------------------------------------------------
#   Core – SimpleBacktester
# ---------------------------------------------------------------------------
@dataclass
class SimpleBacktester:
    """Vectorised back‑tester that pulls everything from *one* DataFrame."""

    all_df: pd.DataFrame
    models: Dict[int, XGBClassifier]
    feature_cols: List[str] = field(repr=False)
    p_thresh: float = PROB_THRESHOLD
    max_positions: int = MAX_POSITIONS


    # ---------------------------------------------------------------------
    def run(self, start_dt: _dt.date, end_dt: _dt.date, constituents_for_func):
        symbol_level = self.all_df.index.names.index("symbol")

        # ensure sorted, unique index
        if not self.all_df.index.is_monotonic_increasing:
            self.all_df = self.all_df.sort_index()
        self.all_df = self.all_df[~self.all_df.index.duplicated(keep="first")]

        date_idx = self.all_df.index.get_level_values("date")
        date_tz  = date_idx.tz
        have_tz  = date_tz is not None

        min_date = date_idx.min()
        max_date = date_idx.max()
        logger.info("all_df date span: %s → %s (tz=%s)", min_date, max_date, date_tz)

        equity_curve: List[Dict] = []
        open_trades: Dict[str, Dict] = {}
        cash: float = 1.0

        bdays = pd.bdate_range(start_dt, end_dt)
        if have_tz:
            bdays = bdays.tz_localize(date_tz)

        need_cols = self.feature_cols + ["qpi_3", "close", "volume"] 

        # -----------------------------------------------------------------
        for today_ts in bdays:
            today: _dt.date = today_ts.date()
            model = self.models.get(today.year)

            # EXIT -------------------------------------------------------
            drops: List[str] = []
            for sym, tr in open_trades.items():
                age = (today - tr["entry_dt"]).days
                key_date = pd.Timestamp(today, tz=date_tz) if have_tz else today
                try:
                    close_px = float(self.all_df.loc[(key_date, sym), "close"])
                except KeyError:
                    continue
                ret = close_px / tr["entry_px"] - 1
                if (STOP_LOSS_PCT and ret <= -STOP_LOSS_PCT) or age >= HOLD_DAYS:
                    cash += tr["capital"] * (1 + ret)
                    drops.append(sym)
            for sym in drops:
                open_trades.pop(sym, None)

            # ENTRY ------------------------------------------------------
            if model is not None:
                universe   = constituents_for_func(today)
                candidates = [s for s in universe if s not in open_trades]

                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug("[%s] Initial candidates: %d", today, len(candidates))

                if candidates:
                    key_date = pd.Timestamp(today, tz=date_tz) if have_tz else today
                    try:
                        daily_df = self.all_df.xs(key_date, level="date", drop_level=False)
                    except KeyError:
                        daily_df = pd.DataFrame()

                    if daily_df.empty:
                        if logger.isEnabledFor(logging.DEBUG):
                            logger.debug("[%s] No rows for this date in all_df", today)
                        continue

                    day_df = daily_df.loc[daily_df.index.get_level_values(symbol_level).isin(candidates)]

                    liq_mask = (day_df["close"] * day_df["volume"]) >= LIQUIDITY_USD_MIN & (day_df["close"] > LIQUIDITY_MIN_PRICE)
                    day_df = day_df.loc[liq_mask]

                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug("[%s] after liquidity filter: %d", today, len(day_df))
    
                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug("[%s] bars present for %d/%d candidates", today, len(day_df), len(candidates))

                    day_df = day_df.dropna(subset=need_cols, how="any")

                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug("[%s] after dropna(cols), rows left: %d", today, len(day_df))

                    if day_df.empty:
                        continue

                    filt_df = day_df[day_df["qpi_3"] < 15]

                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug("[%s] after QPI filter: %d", today, len(filt_df))

                    if filt_df.empty:
                        continue

                    probs = model.predict_proba(filt_df[self.feature_cols])[:, 1]
                    sel = (
                        pd.Series(probs, index=filt_df.index.get_level_values(symbol_level))
                          .sort_values(ascending=False)
                          .loc[lambda s: s > self.p_thresh]
                          .head(self.max_positions - len(open_trades))
                    )

                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug("[%s] above p_thresh: %d", today, len(sel))
                        logger.debug(f"above p_thresh: {sel}")

                    if not sel.empty:
                        cap_per = cash / max(1, self.max_positions - len(open_trades))
                        for sym, prob in sel.items():
                            entry_px = float(filt_df.loc[(key_date, sym), "close"])
                            open_trades[sym] = dict(
                                entry_dt=today,
                                entry_px=entry_px,
                                prob=prob,
                                capital=cap_per,
                            )
                            cash -= cap_per

            # MARK‑TO‑MARKET -------------------------------------------
            nav = cash
            key_date = pd.Timestamp(today, tz=date_tz) if have_tz else today
            for sym, tr in open_trades.items():
                try:
                    close_px = float(self.all_df.loc[(key_date, sym), "close"])
                except KeyError:
                    continue
                nav += tr["capital"] * (close_px / tr["entry_px"])

            equity_curve.append(dict(date=today, nav=nav))

        logger.info("Backtest finished – %s bars", len(equity_curve))
        return pd.DataFrame(equity_curve).set_index("date")

    # -----------------------------------------------------------------
    @functools.cached_property
    def fe_cols(self) -> List[str]:
        return self.feature_cols
