from __future__ import annotations

"""
Pre-Market Gap-Up TWAP Short Strategy
------------------------------------

TODO
[ ] Use limit orders for close instead of market orders

This strategy gradually shorts (TWAP) large pre-market gap-ups that break
below a short-term moving average.  It re-uses the gap scanner defined in
`premarket_gap_scanner.py` – no `scan()` implementation is provided here.

Key features
~~~~~~~~~~~~
1. **Entry window**   – 04:00 → 09:29 ET
   • Stock must qualify as a gap-up ≥ `min_gap_up_pct` (see `_is_gap_up`).
   • Previous closed 1-min bar must be *red* **and** below the `ma_window` SMA.
   • Short up to `twap_entry_dollar_cap` notional each minute until the
     cumulative short size reaches the risk-based `target_qty`.

2. **Exit window**    – 11:00 → 15:54 ET
   • Buy-to-cover up to `twap_exit_dollar_cap` notional per minute until
     flat.
   • At/after `day_close` (default 15:55 ET) force-cover any remainder with
     a MARKET order so we always finish the session flat.

3. **Execution failure handling**
   • If a tranche order fails (ExecutionReport `outcome` ≠ FILLED/PARTIAL)
     we simply ignore it – the logic will attempt again in the next bar.

Internally we keep our own position accounting (`_current_qty`,
`_target_qty`) instead of relying on `DayContext.pos`, because the base
framework overwrites quantities for every new signal and does not support
partial closes.  This keeps the TWAP logic consistent regardless of how the
broker executes individual slices.
"""

from typing import Optional, List, Any
from datetime import datetime, timedelta, time, date
import logging

import pandas as pd

from trading_framework.mixins import IntradayStrategy
from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome
from strategies.trade_signals import TradeSignal, SignalType, TradeType
from tickers.iscancriteria import IScanCriteria

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class PreMarketGapShortTwapStrategy(IntradayStrategy):
    """Live strategy that shorts pre-market gap-ups using TWAP entry/exit."""

    # ──────────────────────────────────────────────────────────────────────
    # Construction / parameters
    # ----------------------------------------------------------------------
    def __init__(
        self,
        *,
        ma_window: int = 10,
        min_gap_up_pct: float = 50.0,
        min_volume_dollars: float = 250_000,
        stop_loss_pct: float = 150.0,
        # TWAP sizing
        twap_entry_dollar_cap: float = 2_000,
        twap_exit_dollar_cap: float = 500,
        # Time configuration
        entry_start_time: time = time(4, 0),
        entry_end_time:   time = time(9, 29),
        exit_start_time:  time = time(11, 0),
        day_close:        time = time(15, 55),
        # Price floor
        min_price_threshold: float = 0.5,
    ) -> None:
        super().__init__()

        # Configuration
        self.ma_window              = int(ma_window)
        self.min_gap_up_pct         = float(min_gap_up_pct)
        self.min_volume_dollars     = float(min_volume_dollars)
        self.stop_loss_pct          = float(stop_loss_pct)
        self.twap_entry_dollar_cap  = float(twap_entry_dollar_cap)
        self.twap_exit_dollar_cap   = float(twap_exit_dollar_cap)

        self.entry_start_time = entry_start_time
        self.entry_end_time   = entry_end_time
        self.exit_start_time  = exit_start_time
        self.day_close        = day_close

        self.market_open_time = time(9, 30)
        self.min_price_threshold = float(min_price_threshold)

        # Per-day state (reset each session)
        self._current_day: date | None = None
        self._current_qty: float = 0.0   # net short (>0 means short)
        self._target_qty:  float | None = None

        logger.info(
            "Initialized PreMarketGapShortTwapStrategy: ma_window=%s, min_gap_up_pct=%s, "
            "min_volume_dollars=%s, stop_loss_pct=%s, twap_entry_cap=%s, twap_exit_cap=%s, "
            "day_close=%s",
            self.ma_window,
            self.min_gap_up_pct,
            self.min_volume_dollars,
            self.stop_loss_pct,
            self.twap_entry_dollar_cap,
            self.twap_exit_dollar_cap,
            self.day_close,
        )

    # ──────────────────────────────────────────────────────────────────────
    # Framework-required methods
    # ----------------------------------------------------------------------
    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]:
        # 1-minute bars, 5-day look-back for indicators & gap check
        return timedelta(minutes=1), timedelta(days=5)

    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars  # hist = closed bars; latest = current forming bar

        # Grab ticker early for logging purposes
        ticker = self.ctx.get("ticker")

        # ── High-level call diagnostic ────────────────────────────────
        logger.debug(
            "%s - process_bar called: hist=%d bars, latest=%d bars, current_qty=%s, target_qty=%s",
            ticker,
            len(hist) if hist is not None else 0,
            len(latest) if latest is not None else 0,
            self._current_qty,
            self._target_qty,
        )

        # Bail out early if we don't have data to work with
        if hist.empty or latest.empty:
            return None

        day    = self.ctx["day"]
        ind    = self.ctx.get("ind")
        current_ts: datetime = ind.get("current_timestamp")  # injected by framework
        current_time = current_ts.time()

        # ── Reset state for a new trading day ────────────────────────────
        if self._current_day != current_ts.date():
            logger.debug("%s – new session detected, resetting per-day state", ticker)
            self._current_day = current_ts.date()
            self._current_qty = 0.0
            self._target_qty  = None
            day.custom.clear()

        open_price  = latest["open"].iloc[0]
        price_floor_ok = open_price >= self.min_price_threshold

        # ────────────────────────────────────────────────────────────────
        # EXIT LOGIC – (1) scheduled TWAP exit window, (2) force EOD
        # ----------------------------------------------------------------
        signals: List[TradeSignal] = []
        if self._current_qty > 0:
            # 1) TWAP exit slices
            if self.exit_start_time <= current_time < self.day_close:
                order_qty = int(min(
                    self._current_qty,
                    self.twap_exit_dollar_cap / open_price
                ))
                if order_qty > 0:
                    sig = TradeSignal(
                        symbol=ticker,
                        signal=SignalType.CLOSE,
                        trade_type=TradeType.BUY,
                        price=open_price,
                        quantity=order_qty,
                        metadata={"exit_reason": "TWAP_EXIT"},
                        source_bar=latest.iloc[0],
                    )
                    signals.append(sig)
                    logger.info(
                        "%s: TWAP exit slice %s shares at %.2f (remaining=%s)",
                        ticker, order_qty, open_price, self._current_qty - order_qty
                    )
                    return signals  # one signal per bar is plenty

            # 2) Force flat at/after day_close
            if current_time >= self.day_close:
                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.CLOSE,
                    trade_type=TradeType.BUY,
                    price=open_price,  # MARKET → use bar open
                    quantity=self._current_qty,
                    metadata={"exit_reason": "EOD_FORCE"},
                    source_bar=latest.iloc[0],
                )
                signals.append(sig)
                logger.info("%s: EOD force-cover %s shares at %.2f", ticker, self._current_qty, open_price)
                # Reset internal counters (execution update will also adjust)
                self._target_qty = None
                return signals

        # ────────────────────────────────────────────────────────────────
        # Entry eligibility checks (single qualifying gap-up stock per day)
        # ----------------------------------------------------------------
        # Prevent any entry attempts outside entry window
        if not (self.entry_start_time <= current_time <= self.entry_end_time):
            return None

        # Already reached target? nothing more to do
        if self._target_qty is not None and self._current_qty >= self._target_qty:
            logger.warning("%s - already reached target %s", ticker, self._target_qty)
            return None

        # Check gap-up status (refresh every bar)
        is_gap = self._is_gap_up(hist, latest) and price_floor_ok
        day.custom["stock_status"] = "GAP_UP" if is_gap else "NOT_GAP_UP"
        if not is_gap:
            logger.warning("%s - not a gap-up", ticker)
            return None

        # ───────────────────────────────────────────────────────────
        # Pre-market locate/pre-allocate request (before MA/red-bar gate)
        # -----------------------------------------------------------------
        locate_requested = day.custom.get("locate_requested", False)
        if (
            not locate_requested
            and current_time < self.market_open_time  # only pre-market locate
            and self._target_qty is None  # only calculate once
        ):
            stop_price = open_price * (1 + self.stop_loss_pct / 100)
            calc_qty = self.position_manager.calculate_position_size(
                ticker=ticker,
                hist_data=hist,
                price=open_price,
                stop_loss=stop_price,
            )
            if calc_qty > 0:
                # Store requested qty & target for later use
                self._target_qty = calc_qty
                day.custom["locate_requested"] = True
                day.custom["requested_qty"] = calc_qty

                logger.info(
                    "%s: Requesting pre-market short locate/pre-allocate for %s shares at %.2f",
                    ticker,
                    calc_qty,
                    open_price,
                )

                return [
                    TradeSignal.short_locate_request(
                        symbol=ticker,
                        quantity=calc_qty,
                        expected_price=open_price,
                        metadata={
                            "pre_market": True,
                            "estimated_stop_price": stop_price,
                            "timestamp": current_ts,
                        },
                        source_bar=latest.iloc[0],
                    )
                ]

        # Require moving-average gate
        if len(hist) < self.ma_window:
            return None
        
        sma        = hist["close"].rolling(self.ma_window).mean().iloc[-1]
        last_close = hist["close"].iloc[-1]
        last_open  = hist["open"].iloc[-1]
        red_bar    = last_close < last_open
        below_ma   = last_close < sma
        if not (red_bar and below_ma):
            logger.warning("%s - not below MA", ticker)
            return None

        # ---------------------------------------------------------------
        # Ensure we have locate filled before attempting entry
        # ---------------------------------------------------------------
        locate_req   = day.custom.get("locate_requested", False)
        located_qty  = int(day.custom.get("located_quantity", 0))
        if locate_req and located_qty == 0:
            logger.debug("%s: locate requested but not yet filled, waiting", ticker)
            return None

        # ----------------------------------------------------------------
        # Determine target position size once, on first qualifying bar
        # ----------------------------------------------------------------
        if self._target_qty is None:
            stop_price   = open_price * (1 + self.stop_loss_pct / 100)
            calc_qty     = self.position_manager.calculate_position_size(
                ticker=ticker,
                hist_data=hist,
                price=open_price,
                stop_loss=stop_price,
            )
            if calc_qty <= 0:
                logger.warning("%s: calculated position size <= 0, skipping", ticker)
                return None
            self._target_qty   = calc_qty
            logger.info(
                "%s: Target short qty set to %s (calc=%s)",
                ticker, self._target_qty, calc_qty
            )

        remaining_qty = self._target_qty - self._current_qty
        if remaining_qty <= 0:
            return None

        order_qty = int(min(remaining_qty, self.twap_entry_dollar_cap / open_price))
        if order_qty <= 0:
            return None

        stop_price = open_price * (1 + self.stop_loss_pct / 100)
        sig = TradeSignal(
            symbol=ticker,
            signal=SignalType.OPEN,
            trade_type=TradeType.SELL,
            price=open_price,
            quantity=order_qty,
            metadata={
                "stop_price": stop_price,
                "risk": stop_price - open_price,
                "entry_type": "PREMARKET_TWAP",
                "timestamp": current_ts,
            },
            source_bar=latest.iloc[0],
        )

        # Seed position state on very first tranche
        if self._current_qty == 0:
            self.open_pos(
                self.ctx,
                side="SHORT",
                qty=order_qty,
                entry_px=open_price,
                entry_ts=current_ts,
                stop_px=stop_price,
                target_px=0,
            )

        logger.info(
            "%s: TWAP entry slice %s shares at %.2f (remaining target=%s)",
            ticker, order_qty, open_price, remaining_qty - order_qty
        )
        return [sig]

    # ──────────────────────────────────────────────────────────────────────
    # Execution report handler – update internal position accounting
    # ----------------------------------------------------------------------
    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        ticker = self.ctx.get("ticker")
        day    = self.ctx["day"]

        # Handle locate / pre-allocate execution reports first
        if report.signal.signal == SignalType.SHORT_LOCATE:
            if report.outcome == ExecOutcome.FILLED:
                located_qty = report.metadata.get("located_quantity") or report.filled_qty
                # Fallback: if broker doesn't return a quantity, assume the originally requested amount
                if located_qty is None or located_qty <= 0:
                    located_qty = self._target_qty

                logger.info(
                    "%s: Locate filled – located_quantity=%s", ticker, located_qty
                )

                # Persist the located quantity so process_bar can see it
                day.custom["located_quantity"] = located_qty

                # Only shrink the target if the actual located size is smaller
                if located_qty is not None and located_qty > 0:
                    if self._target_qty is None or self._target_qty > located_qty:
                        self._target_qty = located_qty
                return []
            else:
                logger.warning(
                    "%s: Failed to locate shares – outcome=%s, msg=%s",
                    ticker,
                    report.outcome,
                    report.broker_msg,
                )
                if report.outcome in (ExecOutcome.REJECTED, ExecOutcome.CANCELLED):
                    self.ctx["day"].reset()
                return []

        # ── Standard execution handling for trade fills ───────────────―
        # Update only on (partial) fills of OPEN/CLOSE orders
        if report.outcome in (ExecOutcome.FILLED, ExecOutcome.PARTIAL):
            if report.signal.trade_type == TradeType.SELL and report.signal.signal == SignalType.OPEN:
                self._current_qty += report.filled_qty
            elif report.signal.trade_type == TradeType.BUY and report.signal.signal == SignalType.CLOSE:
                self._current_qty -= report.filled_qty
            logger.debug(
                "%s: Exec update – trade_type=%s, signal=%s, filled=%s, new_qty=%s",
                ticker,
                report.signal.trade_type,
                report.signal.signal,
                report.filled_qty,
                self._current_qty,
            )
            # When fully flat, clear stored target and ctx position
            if self._current_qty <= 0:
                self._current_qty = 0
                self._target_qty  = None
                self.close_pos(self.ctx)
        else:
            # Non-filled – differentiate handling based on order type
            logger.warning(
                "%s: Execution failed (outcome=%s). Will retry on next bar.",
                ticker,
                report.outcome,
            )

            if report.signal.signal == SignalType.OPEN:
                # OPEN order failed – nothing to roll back, keep state as-is
                pass
            elif report.signal.signal == SignalType.CLOSE:
                # CLOSE order failed – restore DayContext position if it was optimistically cleared
                if day.pos.side is None:
                    day.pos.side = "SHORT"
                    day.pos.qty  = self._current_qty
        return []

    # ──────────────────────────────────────────────────────────────────────
    # Helper – same gap-up logic as MA strategy
    # ----------------------------------------------------------------------
    def _is_gap_up(self, hist: pd.DataFrame, latest: pd.DataFrame) -> bool:
        ticker = self.ctx.get("ticker")
        logger.debug("%s - _is_gap_up called: hist %d bars, latest %d bars", ticker, len(hist), len(latest))

        if hist.empty and latest.empty:
            logger.debug("%s - _is_gap_up: hist and latest empty => False", ticker)
            return False

        today = latest.index[-1].date()

        prev = hist[hist.index.map(lambda dt: dt.date() < today)]
        logger.debug("%s - prev bars count=%d", ticker, len(prev))
        if prev.empty:
            logger.debug("%s - _is_gap_up: prev empty => False", ticker)
            return False

        prev_rth = prev[(prev.index.time >= time(9, 30)) & (prev.index.time < time(16, 0))]
        logger.debug("%s - prev_rth count=%d", ticker, len(prev_rth))
        if prev_rth.empty:
            logger.debug("%s - _is_gap_up: prev_rth empty => False", ticker)
            return False

        prev_close = prev_rth["close"].iloc[-1]
        logger.debug("%s - prev_close=%.2f", ticker, prev_close)

        # Pull today's pre-market rows from hist (may be empty at very first bar)
        today_mask = hist.index.map(lambda dt: dt.date() == today)
        today_rows = hist[today_mask]
        logger.debug("%s - today_rows count=%d", ticker, len(today_rows))

        last_close = today_rows["close"].iloc[-1] if not today_rows.empty else 0.0

        gap_pct = (last_close - prev_close) / prev_close * 100
        logger.debug("%s - last_close=%.2f, gap_pct=%.2f, min_gap_up_pct=%.2f", ticker, last_close, gap_pct, self.min_gap_up_pct)
        if gap_pct < self.min_gap_up_pct:
            logger.debug("%s - gap_pct < threshold => False", ticker)
            return False

        dollars = (today_rows["close"] * today_rows["volume"]).sum()
        result = dollars >= self.min_volume_dollars
        logger.debug("%s - dollars=%.2f, min_vol=%.2f => %s", ticker, dollars, self.min_volume_dollars, result)
        return result 