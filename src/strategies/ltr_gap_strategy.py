from __future__ import annotations

"""
Live implementation of the **LTR Gap‑Ranking intraday strategy** that you back‑tested
with `BacktestRunner`.

It plugs directly into your trading framework by implementing both
`IntradayStrategy` **and** `IScanCriteria`, so you can paper‑trade it exactly
like your other live modules (e.g. *PreMarketGapMAShortStrategy*).

The intraday logic is intentionally simple – **buy at the 09:30 open and close
at 15:55 ET** – matching your back‑test.

### 2025‑05‑13 updates
1. **<PERSON>an no longer depends on wall‑clock time** – `trade_date` is inferred from
   the incoming **`current_day_df['date']`** (or the most recent row in
   `historical_df` if `current_day_df` is empty).
2. **historical_df is already aggregated to daily bars**. The scan now treats
   both `historical_df` and `current_day_df` as *daily* OHLCV data, so we drop
   the minute‑→daily collapse step.
"""

from datetime import datetime, timedelta, time
from typing import Any, Dict, List, Optional
import logging
from pathlib import Path
import os

import pandas as pd
import numpy as np
from pytz import timezone

# ─── Framework & strategy deps ────────────────────────────────────────────────
from trading_framework.mixins import IntradayStrategy
from tickers.iscancriteria import IScanCriteria
from trading_framework.core import BarSlice
from strategies.trade_signals import TradeSignal, SignalType, TradeType

# ─── Gap‑ranking pipeline deps ───────────────────────────────────────────────
from strategies.ltr.gap_extractor import GapExtractor
from strategies.ltr.feature_extractor import FeatureExtractor
from strategies.ltr.ltr_trainer import LTRModelTrainer, TrainerConfig
from strategies.ltr.ltr_ranker import LTRModelRanker

# ─── Market data deps ────────────────────────────────────────────────────────
from marketdata.market_data_builder import MarketDataBuilder
from universe.stock_universe_selector import StockUniverseSelector
from dateutil.relativedelta import relativedelta

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# ---------------------------------------------------------------------------
# Helper – timezone utilities
# ---------------------------------------------------------------------------
# Pre-market gap ranking opens at 4:30 ET (30 min after pre-market open at 4 ET)
MARKET_OPEN_TIME = time(4, 30)
DEFAULT_EXIT_TIME = time(15, 55)
# Start ranking as soon as pre-market gap data is ready (4:30 ET)
RANKING_START_TIME = time(4, 30)
# Allow entry in a 5 min window from 4:30–4:35 ET
LATEST_ENTRY_TIME = time(4, 35)

# where we'll persist per-year LTR models
MODEL_STORE_DIR = Path("cache/models/ltr")

# Add the flag at the top of the file
PREVENT_LOOKAHEAD = os.getenv("PREVENT_LOOKAHEAD", "false").lower() in ("true", "1", "yes")

# ---------------------------------------------------------------------------
# Main live strategy class
# ---------------------------------------------------------------------------
class LTRGapLiveStrategy(IntradayStrategy, IScanCriteria):
    """Live **long‑only** version of the LTR gap‑ranking strategy."""

    # ──────────────────────────────────────────────────────────────────────
    # Construction
    # --------------------------------------------------------------------
    def __init__(
        self,
        ticker_info_store,
        intraday_fetcher,
        market_data_provider,
        feature_cols: List[str],
        *,
        k_frac: float = 0.10,
        min_k: int = 1,
        exit_time: time = DEFAULT_EXIT_TIME,
    ) -> None:
        super().__init__()

        # External dependencies ------------------------------------------------
        self.ticker_info_store = ticker_info_store
        self.intraday_fetcher = intraday_fetcher
        self.feature_cols = feature_cols

        # Scan / sizing params --------------------------------------------------
        self.k_frac = float(k_frac)
        self.min_k = int(min_k)
        self.exit_time = exit_time

        # Internal model cache --------------------------------------------------
        self._rankers: dict[int, LTRModelRanker] = {}

        # Re‑usable pipeline objects -------------------------------------------
        self._gap_extractor = GapExtractor(
            gap_atr_threshold=1,
            gap_pct_threshold=0.15,
            min_gap_pct_threshold=0.05,
            min_avg_10d_dollar_volume=1000,
            min_gap_daily_price=0.5,
            market_data_provider=market_data_provider,
            premarket_volume_threshold=100_000,
            use_live_market_cap=True
        )
        self._feature_extractor = FeatureExtractor()

        logger.info(
            "Initialized LTRGapLiveStrategy: k_frac=%.2f min_k=%d exit_time=%s",
            self.k_frac,
            self.min_k,
            self.exit_time,
        )

    # ──────────────────────────────────────────────────────────────────────
    # IScanCriteria implementation
    # --------------------------------------------------------------------
    def scan(
        self,
        historical_df: pd.DataFrame,
        current_day_df: Optional[pd.DataFrame] = None,
    ) -> List[str]:
        """Return tickers to trade for *trade_date*.

        Both inputs are **already daily bars** with columns:
        `['date', 'open', 'high', 'low', 'close', 'volume', 'symbol']`.
        """

        # only perform ranking after pre-market data window closes (4:30 ET)
        now_et = datetime.now(timezone("US/Eastern")).time()
        # if now_et < RANKING_START_TIME:
        #     return []
        if historical_df.empty and (current_day_df is None or current_day_df.empty):
            return []

        # ------------------------------------------------------------------
        # Merge historical + current day daily bars
        # ------------------------------------------------------------------
        daily_df = (
            historical_df
            if current_day_df is None or current_day_df.empty
            else pd.concat([historical_df, current_day_df], ignore_index=True)
        )

        # Prevent lookahead bias by setting high and low equal to open for the current day
        if PREVENT_LOOKAHEAD and current_day_df is not None and not current_day_df.empty:
            # Get the current day's data
            current_day_dates = current_day_df['date'].unique()
            
            # For each current day's data, set high=low=open
            for date in current_day_dates:
                mask = daily_df['date'] == date
                daily_df.loc[mask, 'high'] = daily_df.loc[mask, 'open']
                daily_df.loc[mask, 'low'] = daily_df.loc[mask, 'open']

        # Determine trade_date from current_day_df if present, else latest date
        trade_dt = pd.to_datetime(current_day_df["date"].iloc[-1])
        trade_date = trade_dt.date()

        
        logger.info("[%s] Scanning gap universe …", trade_date)

        # Pivot into dict[ticker → DataFrame] expected by GapExtractor
        gap_daily_bars: dict[str, pd.DataFrame] = {}
        for tkr, g in daily_df.groupby("symbol"):
            g = g.sort_values("date").reset_index(drop=True)
            # drop the symbol column, index by date, then add ATR
            df = g.drop(columns=["symbol"]).set_index("date")
            df = self._add_atr14(df)
            gap_daily_bars[tkr] = df

        # ------------------------------------------------------------------
        # Identify today's gaps & rank them
        # ------------------------------------------------------------------

        ranker = self._get_ranker_for_year(trade_date.year)
        ranked = ranker.rank(
            gap_daily_bars,
            ticker_info_store=self.ticker_info_store,
            intraday_fetcher=self.intraday_fetcher,
            start_date=trade_date,
            end_date=trade_date
        ).sort_values("score", ascending=False)

        k = max(self.min_k, int(len(ranked) * self.k_frac))
        picks = ranked.head(k)["ticker"].tolist()
        
        bottom_k = ranked.tail(k)["ticker"].tolist()

        logger.info(
            "[%s] Selected Top %d/%d tickers: %s", trade_date, len(picks), len(ranked), picks
        )
        
        logger.info(
            "[%s] Unselected Bottom %d/%d tickers: %s", trade_date, len(bottom_k), len(ranked), bottom_k
        )
        return picks

    # ──────────────────────────────────────────────────────────────────────
    # IntradayStrategy implementation
    # --------------------------------------------------------------------
    def prepare(self, metadata: Dict[str, Any]) -> tuple[timedelta, timedelta]:
        """Require 1‑minute bars and 2 days of history for intraday sizing."""
        return timedelta(minutes=1), timedelta(days=2)

    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars  # closed bars, current forming bar
        ticker: str = self.ctx["ticker"]
        day = self.ctx["day"]
        current_ts: datetime = self.ctx["ind"].get("current_timestamp")

        # Skip any entry attempts beyond our pre-market entry window (4:35 ET)
        if not self.in_pos(self.ctx) and current_ts.time() > LATEST_ENTRY_TIME:
            return None
        # Skip if we've already traded -----------------------------------
        if day.custom.get("traded"):
            return None

        # --- Exit at 15:55 ------------------------------------------------
        if self.in_pos(self.ctx) and current_ts.time() >= self.exit_time:
            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.CLOSE,
                trade_type=TradeType.SELL,
                price=latest["open"].iloc[0],  # use bar open as MOC proxy
                quantity=day.pos.qty,
                metadata={"exit_reason": "END_OF_DAY"},
                source_bar=latest.iloc[0],
            )
            self.close_pos(self.ctx)
            day.custom["traded"] = True
            logger.info("%s – closed position at %.2f (EOD)", ticker, sig.price)
            return [sig]

        # --- Entry window: pre-market open at 4:30 through 4:35 (first 5 min) ------------
        if not self.in_pos(self.ctx) and MARKET_OPEN_TIME <= current_ts.time() <= LATEST_ENTRY_TIME:
            entry_price = latest["open"].iloc[0]
            qty = self.position_manager.calculate_position_size(
                ticker=ticker, hist_data=hist, price=entry_price, stop_loss=0.0
            )
            if qty <= 0:
                logger.warning("%s – position size<=0, skipping", ticker)
                day.custom["traded"] = True
                return None

            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.OPEN,
                trade_type=TradeType.BUY,
                price=entry_price,
                quantity=qty,
                metadata={"entry_type": "GAP_RANK_LONG"},
                source_bar=latest.iloc[0],
            )

            self.open_pos(
                self.ctx,
                side="LONG",
                qty=qty,
                entry_px=entry_price,
                entry_ts=current_ts,
                stop_px=0.0,
                target_px=0.0,
            )
            day.custom["traded"] = True
            logger.info("%s – bought %d @ %.2f", ticker, qty, entry_price)
            return [sig]

        return None

    # ──────────────────────────────────────────────────────────────────────
    # Internal helpers
    # --------------------------------------------------------------------
    def _get_ranker_for_year(self, year: int) -> LTRModelRanker:
        if year in self._rankers:
            return self._rankers[year]

        # path where we cache per-year models
        model_path = MODEL_STORE_DIR / f"ltr_model_{year}.txt"

        if model_path.exists():
            logger.info("Loading LTR model for %s ...", year)
            # skip the expensive daily_bars work when we're only loading
            trainer = LTRModelTrainer(
                self._gap_extractor,
                self._feature_extractor,
                {},                            # no daily bars needed
                self.ticker_info_store,
                self.intraday_fetcher,
                TrainerConfig(feature_cols=self.feature_cols),
            )
            model = trainer.load(model_path)
        else:
            logger.info("Training LTR model for %s ...", year)
            MODEL_STORE_DIR.mkdir(parents=True, exist_ok=True)
            trainer_cfg = TrainerConfig(feature_cols=self.feature_cols)
            
            six_years_ago = datetime.now() - relativedelta(months=6 * 12)
            disk_market_data = (MarketDataBuilder()
                        .with_disk_data(start_date=six_years_ago)
                        .build_market_data())

            # load + pivot market data (expensive!)
            daily_bars: dict[str, pd.DataFrame] = {}
            
            end_dt = pd.Timestamp(f"{year}-01-01", tz="US/Eastern")
            start_dt = end_dt - relativedelta(years=5)
            
            universe_selector  = StockUniverseSelector(disk_market_data, self.ticker_info_store)
            ticker_universe    = universe_selector.select(start_dt, end_dt)['ticker'].tolist()
            print(f"Universe size: {len(ticker_universe)}")

            for tkr  in ticker_universe:
                df = disk_market_data.gather_historical_data(ticker=tkr, start_dt=start_dt, end_dt=end_dt, interval=86400)
                if df is not None and not df.empty:
                    df = self._add_atr14(df)
                    daily_bars[tkr] = df

            # full trainer (with actual data) → train → save
            trainer = LTRModelTrainer(
                self._gap_extractor,
                self._feature_extractor,
                daily_bars,
                self.ticker_info_store,
                self.intraday_fetcher,
                trainer_cfg,
            )
            model_by_year = trainer.train_year([year])
            model = model_by_year[year]
            trainer.save(model_path)

        # wrap in a ranker and cache
        ranker = LTRModelRanker(
            model,
            self._gap_extractor,
            self._feature_extractor,
            self.feature_cols,
        )
        self._rankers[year] = ranker
        return ranker

    def _add_atr14(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add a 14-day ATR column (% of close)."""
        tr = np.maximum.reduce([
            df['high'] - df['low'],
            (df['high'] - df['close'].shift()).abs(),
            (df['low']  - df['close'].shift()).abs()
        ])
        df['atr14'] = pd.Series(tr, index=df.index).rolling(window=14).mean()
        return df
