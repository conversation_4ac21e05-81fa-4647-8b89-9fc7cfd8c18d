from enum import Enum
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List, Union

class SignalType(Enum):
    OPEN = "OPEN"
    CLOSE = "CLOSE"
    BRACKET = "BRACKET"
    TRAILING_STOP = "TRAILING_STOP"
    EXIT_STRATEGY = "EXIT_STRATEGY"
    SHORT_LOCATE = "SHORT_LOCATE"

class TradeType(Enum):
    BUY = "BUY"
    SELL = "SELL"


class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


@dataclass
class OrderCondition:
    """Price condition for an order."""
    order_type: OrderType
    price: Optional[float] = None
    
    def is_triggered(self, price: float) -> bool:
        """Check if this condition is triggered at the given price."""
        if self.order_type == OrderType.MARKET:
            return True
        elif self.order_type == OrderType.LIMIT:
            if price <= self.price:
                return True
        elif self.order_type == OrderType.STOP:
            if price >= self.price:
                return True
        return False


class TradeSignal:
    """
    Unified trade signal class that can represent both simple and complex orders.
    
    For simple orders:
        signal: OPEN or CLOSE
        trade_type: BUY or SELL
        price: Execution price
        
    For bracket orders:
        signal: BRACKET
        trade_type: BUY or SELL
        price: Entry price
        stop_loss: Stop loss price
        take_profit: Take profit price
        
    For trailing stop orders:
        signal: TRAILING_STOP
        trade_type: BUY or SELL
        price: Entry price
        initial_stop: Initial stop price
        trail_amount: Amount to trail
        is_percent: Whether trail amount is a percentage
    """
    
    def __init__(
        self,
        signal: SignalType,
        trade_type: TradeType,
        price: float,
        quantity: Optional[float] = None,
        symbol: Optional[str] = None,
        metadata: Dict[str, Any] = None,
        source_bar: Any = None,
        # Complex order parameters
        entry_condition: Optional[OrderCondition] = None,
        stop_loss: Optional[Union[float, OrderCondition]] = None,
        take_profit: Optional[Union[float, OrderCondition]] = None,
        initial_stop: Optional[float] = None,
        trail_amount: Optional[float] = None,
        is_percent: bool = True
    ):
        self.signal = signal
        self.trade_type = trade_type
        self.price = price
        self.quantity = quantity
        self.symbol = symbol
        self.metadata = metadata or {}
        self.source_bar = source_bar
        
        # For complex orders
        self.entry_condition = entry_condition
        
        # Store stop loss as OrderCondition
        if stop_loss is not None:
            if isinstance(stop_loss, float):
                self.stop_loss = OrderCondition(OrderType.STOP, stop_loss)
            else:
                self.stop_loss = stop_loss
        else:
            self.stop_loss = None
            
        # Store take profit as OrderCondition
        if take_profit is not None:
            if isinstance(take_profit, float):
                self.take_profit = OrderCondition(OrderType.LIMIT, take_profit)
            else:
                self.take_profit = take_profit
        else:
            self.take_profit = None
        
        # For trailing stop orders
        self.initial_stop = initial_stop
        self.trail_amount = trail_amount
        self.is_percent = is_percent
        
        if self.symbol is None or not isinstance(self.symbol, str) or self.symbol.strip() == "":
            raise ValueError("TradeSignal requires a non-empty string 'symbol'.")
        # Ensure symbol doesn't have leading/trailing whitespace
        self.symbol = self.symbol.strip()
    
    def __repr__(self):
        """Return a string representation of the TradeSignal."""
        base_repr = (f"TradeSignal(signal={self.signal}, trade_type={self.trade_type}, "
                     f"price={self.price}, quantity={self.quantity}")
        
        if self.symbol:
            base_repr += f", symbol={self.symbol}"
        
        if self.signal == SignalType.BRACKET:
            base_repr += (f", stop_loss={self.stop_loss.price if self.stop_loss else None}, "
                          f"take_profit={self.take_profit.price if self.take_profit else None}")
        
        elif self.signal == SignalType.TRAILING_STOP:
            base_repr += (f", initial_stop={self.initial_stop}, "
                          f"trail_amount={self.trail_amount}, is_percent={self.is_percent}")
        
        base_repr += f", metadata={self.metadata}, bar_ts={getattr(self.source_bar, 'name', None)})"
        return base_repr
    
    def to_simple_signals(self) -> List['TradeSignal']:
        """
        Convert a complex order signal to one or more simple signals.
        Useful for brokers that don't support complex orders.
        
        Returns:
            List of simple TradeSignal objects
        """
        if self.signal in (SignalType.OPEN, SignalType.CLOSE):
            # Already a simple signal
            return [self]
        
        if self.signal == SignalType.BRACKET:
            # For a bracket order, just return the entry signal
            # The stop loss and take profit will be managed by the state machine
            entry_signal = TradeSignal(
                signal=SignalType.OPEN,
                trade_type=self.trade_type,
                price=self.price,
                quantity=self.quantity,
                symbol=self.symbol,
                metadata={
                    **self.metadata,
                    "complex_order_type": "BRACKET"
                },
                source_bar=self.source_bar,
            )
            return [entry_signal]
        
        elif self.signal == SignalType.TRAILING_STOP:
            # For a trailing stop order, just return the entry signal
            # The trailing stop will be managed by the state machine
            entry_signal = TradeSignal(
                signal=SignalType.OPEN,
                trade_type=self.trade_type,
                price=self.price,
                quantity=self.quantity,
                symbol=self.symbol,
                metadata={
                    **self.metadata,
                    "complex_order_type": "TRAILING_STOP"
                },
                source_bar=self.source_bar,
            )
            return [entry_signal]
        
        return [self]  # Unknown type, return as is
    
    @classmethod
    def bracket_order(
        cls,
        symbol: str,
        trade_type: TradeType,
        entry_price: float,
        stop_loss_price: float,
        take_profit_price: float,
        quantity: Optional[float] = None,
        metadata: Dict[str, Any] = None,
        entry_condition: Optional[OrderCondition] = None,
        source_bar: Any = None,
    ) -> 'TradeSignal':
        """
        Create a bracket order signal.
        
        Args:
            symbol: The ticker symbol
            trade_type: BUY or SELL
            entry_price: Entry price (used if entry_condition is None)
            stop_loss_price: Stop loss price
            take_profit_price: Take profit price
            quantity: Order quantity
            metadata: Additional metadata
            entry_condition: Optional custom entry condition
            source_bar: The bar that triggered this signal
            
        Returns:
            TradeSignal with BRACKET signal type
        """
        # Create default entry condition if not provided
        if entry_condition is None:
            entry_condition = OrderCondition(OrderType.LIMIT, entry_price)
        
        return cls(
            signal=SignalType.BRACKET,
            trade_type=trade_type,
            price=entry_price,
            quantity=quantity,
            symbol=symbol,
            metadata=metadata,
            entry_condition=entry_condition,
            stop_loss=stop_loss_price,
            take_profit=take_profit_price,
            source_bar=source_bar,
        )
    
    @classmethod
    def market_bracket_order(
        cls,
        symbol: str,
        trade_type: TradeType,
        stop_loss_price: float,
        take_profit_price: float,
        quantity: Optional[float] = None,
        metadata: Dict[str, Any] = None,
        source_bar: Any = None,
    ) -> 'TradeSignal':
        """
        Create a bracket order with market entry.
        
        Args:
            symbol: The ticker symbol
            trade_type: BUY or SELL
            stop_loss_price: Stop loss price
            take_profit_price: Take profit price
            quantity: Order quantity
            metadata: Additional metadata
            source_bar: The bar that triggered this signal
            
        Returns:
            TradeSignal with BRACKET signal type
        """
        return cls(
            signal=SignalType.BRACKET,
            trade_type=trade_type,
            price=0.0,  # Price will be determined by market
            quantity=quantity,
            symbol=symbol,
            metadata=metadata,
            entry_condition=OrderCondition(OrderType.MARKET),
            stop_loss=stop_loss_price,
            take_profit=take_profit_price,
            source_bar=source_bar,
        )
    
    @classmethod
    def trailing_stop_order(
        cls,
        symbol: str,
        trade_type: TradeType,
        entry_price: float,
        initial_stop_price: float,
        trail_amount: float,
        is_percent: bool = True,
        quantity: Optional[float] = None,
        metadata: Dict[str, Any] = None,
        entry_condition: Optional[OrderCondition] = None,
        source_bar: Any = None,
    ) -> 'TradeSignal':
        """
        Create a trailing stop order signal.
        
        Args:
            symbol: The ticker symbol
            trade_type: BUY or SELL
            entry_price: Entry price (used if entry_condition is None)
            initial_stop_price: Initial stop price
            trail_amount: Amount to trail (dollar or percent)
            is_percent: Whether trail amount is a percentage
            quantity: Order quantity
            metadata: Additional metadata
            entry_condition: Optional custom entry condition
            source_bar: The bar that triggered this signal
            
        Returns:
            TradeSignal with TRAILING_STOP signal type
        """
        # Create default entry condition if not provided
        if entry_condition is None:
            entry_condition = OrderCondition(OrderType.LIMIT, entry_price)
        
        return cls(
            signal=SignalType.TRAILING_STOP,
            trade_type=trade_type,
            price=entry_price,
            quantity=quantity,
            symbol=symbol,
            metadata=metadata,
            entry_condition=entry_condition,
            initial_stop=initial_stop_price,
            trail_amount=trail_amount,
            is_percent=is_percent,
            source_bar=source_bar,
        )
    
    @classmethod
    def short_locate_request(
        cls,
        symbol: str,
        quantity: float,
        expected_price: float,
        metadata: Dict[str, Any] = None,
        source_bar: Any = None,
    ) -> 'TradeSignal':
        """
        Create a signal to request a short locate before placing an order.
        
        Args:
            symbol: The ticker symbol to locate shares for
            quantity: The number of shares to locate
            expected_price: The expected price of the stock (for fee calculation)
            metadata: Additional metadata
            source_bar: The bar that triggered this request
            
        Returns:
            TradeSignal with SHORT_LOCATE signal type
        """
        return cls(
            signal=SignalType.SHORT_LOCATE,
            trade_type=TradeType.SELL,
            price=expected_price,
            quantity=quantity,
            symbol=symbol,
            metadata=metadata,
            source_bar=source_bar,
        )

    @classmethod
    def exit_on_execution_failure(
        cls,
        report: 'ExecutionReport',
        symbol: str = None,
        exit_reason_prefix: str = "EXECUTION_",
    ) -> 'TradeSignal':
        """
        Create an EXIT_STRATEGY signal in response to a failed execution.
        
        Args:
            report: The execution report that failed
            symbol: Optional ticker symbol (defaults to the symbol from the report)
            exit_reason_prefix: Prefix for the exit reason in metadata
            
        Returns:
            TradeSignal with EXIT_STRATEGY signal type
        """
        sig_symbol = symbol or report.signal.symbol
        
        return cls(
            signal=SignalType.EXIT_STRATEGY,
            trade_type=TradeType.SELL,  # Doesn't matter for EXIT_STRATEGY
            price=0.0,
            symbol=sig_symbol,
            metadata={
                "exit_reason": f"{exit_reason_prefix}{report.outcome.name}",
                "broker_message": report.broker_msg
            },
            source_bar=report.signal.source_bar
        )