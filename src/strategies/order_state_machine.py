from enum import Enum
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List, Union

from strategies.trade_signals import *

class OrderState(Enum):
    """Possible states for an order."""
    PENDING = "PENDING"      # Waiting for entry condition
    ACTIVE = "ACTIVE"        # Position opened, monitoring exit conditions
    COMPLETED = "COMPLETED"  # Order fully executed
    CANCELLED = "CANCELLED"  # Order was cancelled

class OrderEvent(Enum):
    """Events that can trigger state transitions."""
    ENTRY_TRIGGERED = "ENTRY_TRIGGERED"
    STOP_LOSS_TRIGGERED = "STOP_LOSS_TRIGGERED"
    TAKE_PROFIT_TRIGGERED = "TAKE_PROFIT_TRIGGERED"
    CANCEL_ORDER = "CANCEL_ORDER"

class OrderStateMachine:
    """
    State machine for managing complex orders.
    
    This tracks the state of an order and generates trade signals
    based on price movements.
    """
    
    def __init__(self, order_id: str, signal: TradeSignal):
        """
        Initialize the state machine.
        
        Args:
            order_id: Unique identifier for this order
            signal: The complex trade signal to manage
        """
        self.order_id = order_id
        self.signal = signal
        self.state = OrderState.PENDING
        self.entry_price = None
        self.entry_time = None
        self.exit_price = None
        self.exit_time = None
        self.exit_reason = None
        self.highest_price = None  # For trailing stops
        self.current_stop_price = None
        
        # Set initial stop price for trailing stops
        if signal.signal == SignalType.TRAILING_STOP:
            self.current_stop_price = signal.initial_stop
    
    def process_bar(self, historical_bars, current_open_bar) -> Optional[List[TradeSignal]]:
        """
        Process a new price bar and update the state machine.
        
        Args:
            historical_bars: Historical price data
            current_open_bar: Current open bar data
            
        Returns:
            List of trade signals, if any are generated
        """
        if historical_bars.empty:
            return None
            
        # Get current price info
        last_bar = historical_bars.iloc[-1]
        current_price = last_bar['close']
        current_time = last_bar.name  # Timestamp
        
        # Process based on current state
        if self.state == OrderState.PENDING:
            # Check entry condition
            if self.signal.entry_condition and self.signal.entry_condition.is_triggered(current_price):
                return self._handle_event(OrderEvent.ENTRY_TRIGGERED, current_price, current_time)
                    
        elif self.state == OrderState.ACTIVE:
            # Update trailing stop if applicable
            if self.signal.signal == SignalType.TRAILING_STOP and self.entry_price is not None:
                self._update_trailing_stop(current_price)
            
            # Check exit conditions
            if self.signal.signal == SignalType.BRACKET:
                # Check stop loss
                if self.signal.stop_loss and self.signal.stop_loss.is_triggered(current_price):
                    return self._handle_event(OrderEvent.STOP_LOSS_TRIGGERED, current_price, current_time)
                
                # Check take profit
                if self.signal.take_profit and self.signal.take_profit.is_triggered(current_price):
                    return self._handle_event(OrderEvent.TAKE_PROFIT_TRIGGERED, current_price, current_time)
                    
            elif self.signal.signal == SignalType.TRAILING_STOP and self.current_stop_price is not None:
                # Check trailing stop
                if current_price <= self.current_stop_price:
                    return self._handle_event(OrderEvent.STOP_LOSS_TRIGGERED, current_price, current_time)
        
        return None
    
    def _handle_event(self, event: OrderEvent, price: float, timestamp) -> Optional[List[TradeSignal]]:
        """
        Handle a state machine event.
        
        Args:
            event: The event that occurred
            price: Current price
            timestamp: Current timestamp
            
        Returns:
            List of trade signals, if any are generated
        """
        if event == OrderEvent.ENTRY_TRIGGERED and self.state == OrderState.PENDING:
            # Entry condition triggered
            self.state = OrderState.ACTIVE
            self.entry_price = price
            self.entry_time = timestamp
            self.highest_price = price  # Initialize for trailing stops
            
            # No need to generate a signal, as the entry was already signaled
            return None
            
        elif event == OrderEvent.STOP_LOSS_TRIGGERED and self.state == OrderState.ACTIVE:
            # Stop loss triggered
            self.state = OrderState.COMPLETED
            self.exit_price = price
            self.exit_time = timestamp
            self.exit_reason = "STOP_LOSS"
            
            # Generate a close signal in the opposite direction of the entry
            close_trade_type = TradeType.SELL if self.signal.trade_type == TradeType.BUY else TradeType.BUY
            
            return [
                TradeSignal(
                    signal=SignalType.CLOSE,
                    trade_type=close_trade_type,
                    price=price,
                    quantity=self.signal.quantity,
                    symbol=self.signal.symbol,
                    metadata={
                        **self.signal.metadata,
                        "order_id": self.order_id,
                        "entry_price": self.entry_price,
                        "entry_time": self.entry_time,
                        "exit_reason": self.exit_reason
                    }
                )
            ]
            
        elif event == OrderEvent.TAKE_PROFIT_TRIGGERED and self.state == OrderState.ACTIVE:
            # Take profit triggered
            self.state = OrderState.COMPLETED
            self.exit_price = price
            self.exit_time = timestamp
            self.exit_reason = "TAKE_PROFIT"
            
            # Generate a close signal in the opposite direction of the entry
            close_trade_type = TradeType.SELL if self.signal.trade_type == TradeType.BUY else TradeType.BUY
            
            return [
                TradeSignal(
                    signal=SignalType.CLOSE,
                    trade_type=close_trade_type,
                    price=price,
                    quantity=self.signal.quantity,
                    symbol=self.signal.symbol,
                    metadata={
                        **self.signal.metadata,
                        "order_id": self.order_id,
                        "entry_price": self.entry_price,
                        "entry_time": self.entry_time,
                        "exit_reason": self.exit_reason
                    }
                )
            ]
            
        elif event == OrderEvent.CANCEL_ORDER:
            # Order cancelled
            if self.state == OrderState.ACTIVE:
                # Need to exit the position
                self.state = OrderState.CANCELLED
                self.exit_price = price
                self.exit_time = timestamp
                self.exit_reason = "CANCELLED"
                
                # Generate a close signal in the opposite direction of the entry
                close_trade_type = TradeType.SELL if self.signal.trade_type == TradeType.BUY else TradeType.BUY
                
                return [
                    TradeSignal(
                        signal=SignalType.CLOSE,
                        trade_type=close_trade_type,
                        price=price,
                        quantity=self.signal.quantity,
                        symbol=self.signal.symbol,
                        metadata={
                            **self.signal.metadata,
                            "order_id": self.order_id,
                            "entry_price": self.entry_price,
                            "entry_time": self.entry_time,
                            "exit_reason": self.exit_reason
                        }
                    )
                ]
            else:
                # Just mark as cancelled
                self.state = OrderState.CANCELLED
                return None
        
        return None
    
    def _update_trailing_stop(self, current_price: float) -> None:
        """
        Update the trailing stop level based on price movement.
        
        Args:
            current_price: Current market price
        """
        if self.signal.signal != SignalType.TRAILING_STOP or self.state != OrderState.ACTIVE:
            return
            
        # Only update if price moves higher
        if current_price > self.highest_price:
            # Update the highest observed price
            old_highest = self.highest_price
            self.highest_price = current_price
            
            # Calculate the price movement
            price_movement = self.highest_price - old_highest
            
            # Only adjust stop if there was significant movement
            if price_movement > 0:
                # Calculate the new stop price
                if self.signal.is_percent:
                    # Trail by a percentage of current price
                    trail_amount = self.highest_price * (self.signal.trail_amount / 100.0)
                    self.current_stop_price = self.highest_price - trail_amount
                else:
                    # Trail by a fixed amount
                    self.current_stop_price = self.highest_price - self.signal.trail_amount


# ---- Order Processor Mixin ----

class OrderProcessorMixin:
    """
    Mixin that manages complex orders for a strategy.
    
    This provides:
    1. Registration of complex orders
    2. State machine management
    3. Automatic signal generation as order conditions are met
    """
    
    def initialize_context(self, context):
        """
        Initialize order management in the context.
        
        Args:
            context: Strategy context
        """
        # Initialize order tracking
        context['complex_orders'] = {}
        context['state_machines'] = {}
        context['next_order_id'] = 1
        context['generated_signals'] = []
    
    def preprocess_bar(
        self,
        historical_bars,
        current_open_bar,
        context,
        ticker,
        metadata
    ):
        """
        Process all order state machines before strategy logic.
        
        Args:
            historical_bars: Historical price data
            current_open_bar: Current open bar data
            context: Current strategy context
            ticker: The ticker symbol
            metadata: Additional context information
            
        Returns:
            bool: Always True to continue processing
        """
        # Process all state machines
        generated_signals = []
        
        for order_id, state_machine in context.get('state_machines', {}).items():
            signals = state_machine.process_bar(historical_bars, current_open_bar)
            if signals:
                generated_signals.extend(signals)
        
        # Store generated signals for later merge
        if generated_signals:
            if 'generated_signals' not in context:
                context['generated_signals'] = []
            context['generated_signals'].extend(generated_signals)
        
        return True
    
    def postprocess_signals(
        self,
        signals,
        historical_bars,
        current_open_bar,
        context,
        ticker,
        metadata
    ):
        """
        Process signals, handle complex orders, and merge with generated signals.
        
        Args:
            signals: The signals generated by the strategy (or None)
            historical_bars: Historical price data
            current_open_bar: Current open bar data
            context: Current strategy context
            ticker: The ticker symbol
            metadata: Additional context information
            
        Returns:
            Optional[List[TradeSignal]]: The processed signals
        """
        processed_signals = []
        
        # Process any complex orders in the signals
        for signal in signals or []:
            if signal.signal in (SignalType.BRACKET, SignalType.TRAILING_STOP):
                # Register this complex order
                order_id = self._register_complex_order(signal, context)
                
                # Convert to initial trade signals
                entry_signals = signal.to_simple_signals()
                for entry_signal in entry_signals:
                    # Add order ID to metadata
                    entry_signal.metadata['order_id'] = order_id
                    processed_signals.append(entry_signal)
            else:
                # Pass through regular signals
                processed_signals.append(signal)
        
        # Merge with any signals generated by state machines
        if 'generated_signals' in context and context['generated_signals']:
            processed_signals.extend(context['generated_signals'])
            context['generated_signals'] = []
        
        return processed_signals if processed_signals else None
    
    def _register_complex_order(self, signal, context):
        """
        Register a complex order and create its state machine.
        
        Args:
            signal: The complex trade signal
            context: Strategy context
            
        Returns:
            str: Order ID
        """
        # Generate an order ID
        order_id = str(context.get('next_order_id', 1))
        context['next_order_id'] = int(order_id) + 1
        
        # Store the order
        if 'complex_orders' not in context:
            context['complex_orders'] = {}
        context['complex_orders'][order_id] = signal
        
        # Create a state machine
        if 'state_machines' not in context:
            context['state_machines'] = {}
        context['state_machines'][order_id] = OrderStateMachine(order_id, signal)
        
        return order_id
    
    def cancel_order(self, order_id, context, current_price=None):
        """
        Cancel a complex order.
        
        Args:
            order_id: The order ID to cancel
            context: Strategy context
            current_price: Current price (for exit if already active)
            
        Returns:
            bool: True if order was found and cancelled
        """
        if 'state_machines' not in context or order_id not in context['state_machines']:
            return False
            
        # Get the state machine
        state_machine = context['state_machines'][order_id]
        
        # Cancel the order
        signals = state_machine._handle_event(
            OrderEvent.CANCEL_ORDER, 
            current_price or 0.0,
            None  # Timestamp would be set in a real implementation
        )
        
        # Add any generated signals
        if signals:
            if 'generated_signals' not in context:
                context['generated_signals'] = []
            context['generated_signals'].extend(signals)
        
        return True
