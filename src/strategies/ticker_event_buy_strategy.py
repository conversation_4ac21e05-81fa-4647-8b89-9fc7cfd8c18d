from typing import Optional, Tu<PERSON>, Dict, Any, List, TypedDict
from datetime import timedelta, time, datetime
import pandas as pd
import logging
from pytz import timezone

from strategies.trade_signals import TradeSignal, SignalType, TradeType
from trading_framework.indicators import OnDemandIndicatorMixin

logger = logging.getLogger(__name__)

class TickerEventContext(TypedDict):
    position_open: bool
    entry_price: Optional[float]
    entry_time: Optional[datetime]
    stop_loss_price: Optional[float]
    profit_target_price: Optional[float]
    time_threshold: Optional[datetime]
    event_timestamp: Optional[datetime]
    position_already_traded: bool

class TickerEventBuyStrategy(OnDemandIndicatorMixin, EnhancedIntradayStrategy):
    """
    Ticker Event Buy Strategy (EnhancedIntradayStrategy implementation)

    This strategy generates a buy signal based on a ticker event timestamp
    provided in the metadata. It executes a buy order at the market bar corresponding
    to the event's timestamp.
    
    Trade Management:
      - Entry: The long order is executed at the first bar at/after the ticker event's timestamp.
      - Stop Loss: Fixed at 3% below entry price.
      - Profit Target: Set at 6x risk (18%) above entry price. The trade is exited early if:
             a) the profit target is hit,
             b) it's the end of the trading day, or
             c) the bar is within 10 minutes of market close (assumed to be 16:00).
    """

    def __init__(self, risk_multiple: float = 6.0, stop_loss_percent: float = 0.03):
        """
        Initialize the TickerEventBuyStrategy.

        Parameters:
            risk_multiple (float): Profit target as multiple of risk (default is 6.0x).
            stop_loss_percent (float): The stop loss percentage below entry (fixed at 3%).
        """
        # Initialize with market hours
        super().__init__(market_open_time=time(9, 30), market_close_time=time(16, 0))
        
        self.risk_multiple = risk_multiple
        self.stop_loss_percent = stop_loss_percent
        # Calculate profit target based on risk multiple
        self.profit_target = self.stop_loss_percent * self.risk_multiple

    def prepare(self, position_manager=None) -> Tuple[timedelta, timedelta]:
        """
        Prepare the strategy and return required data parameters.
        
        Args:
            position_manager: Position manager for calculating position sizes
            
        Returns:
            Tuple containing:
                - interval: The time interval for bars (1 minute)
                - lookback_window: The historical data window needed (3 days)
        """
        # Store position manager for later use
        self.position_manager = position_manager
        
        return timedelta(minutes=1), timedelta(days=3)
    
    def _create_initial_context(self) -> TickerEventContext:
        """
        Create and return the initial context for the strategy.
        
        Returns:
            Initial context object
        """
        context = super()._create_initial_context()
        ticker_event_context = {
            'position_open': False,
            'entry_price': None,
            'entry_time': None,
            'stop_loss_price': None,
            'profit_target_price': None,
            'event_timestamp': None,
            'position_already_traded': False
        }
        context.update(ticker_event_context)
        return context
    
    def _process_metadata(self, context: TickerEventContext, metadata: Dict[str, Any]) -> None:
        """
        Process metadata to extract event timestamp.
        
        Args:
            context: The current context
            metadata: The metadata containing event information
        """
        # Get the event timestamp from metadata
        event_timestamp = metadata.get('timestamp')
        if not event_timestamp:
            logger.warning(f"No timestamp found in metadata")
            return
        
        context['event_timestamp'] = event_timestamp
    
    def _process_bar_intraday_impl(
        self, 
        historical_bars: pd.DataFrame, 
        current_open_bar: pd.DataFrame, 
        context: TickerEventContext,
        ticker: str,
        metadata: Dict[str, Any]
    ) -> Optional[List[TradeSignal]]:
        """
        Process a single bar to generate trading signals.
        
        Args:
            historical_bars: Historical price data
            current_open_bar: Current open bar data
            context: Current strategy context (will be updated in-place)
            ticker: The ticker symbol
            metadata: Additional context information
            
        Returns:
            Optional[List[TradeSignal]]: Trading signals or None
        """
        # Its okay if we don't have any historical bars, but we need a current open bar
        if current_open_bar.empty:
            return None
        
        # Process metadata if event_timestamp is not set
        if context['event_timestamp'] is None:
            self._process_metadata(context, metadata)
            if context['event_timestamp'] is None:
                return None
        
        # Use indicators to get current timestamp and price
        indicators = context['indicators']
        current_timestamp = indicators.get('current_timestamp')
        current_price = indicators.get('current_price')
        
        if current_timestamp is None or current_price is None:
            return None
        
        # Check if we've missed our entry window (and haven't opened a position yet)
        if not context['position_open'] and current_timestamp > context['event_timestamp'] + timedelta(minutes=15):
            logger.info(f"Entry window expired for {ticker}. Event time: {context['event_timestamp']}, Current time: {current_timestamp}")
            return None
        
        # Check if we should open a position or manage an existing one
        if not context['position_open']:
            # Only open a position if we've reached the event timestamp AND we haven't already traded this position
            if current_timestamp >= context['event_timestamp'] and not context['position_already_traded']:
                return self._generate_entry_signal(ticker, historical_bars, current_timestamp, current_price, context)
            else:
                # Not time to enter yet or we've already traded this position
                return None
        else:
            # We have an open position, check if we should close it
            return self._check_exit_conditions(ticker, current_timestamp, current_price, context)
    
    def _generate_entry_signal(self, ticker: str, hist_bars: pd.DataFrame, current_time: datetime, 
                              current_price: float, context: TickerEventContext) -> List[TradeSignal]:
        """
        Generate an entry signal and update position state.
        
        Args:
            ticker: The ticker symbol
            hist_bars: Historical bars dataframe
            current_time: Current bar timestamp
            current_price: Current price
            context: Dictionary tracking position state
            
        Returns:
            List[TradeSignal]: List containing the entry signal
        """
        # Calculate stop loss price
        stop_loss_price = current_price * (1 - self.stop_loss_percent)
        
        # Calculate profit target as 6x the risk
        profit_target_price = current_price * (1 + self.profit_target)
        
        # Update position state
        context['position_open'] = True
        context['position_already_traded'] = True
        context['entry_price'] = current_price
        context['entry_time'] = current_time
        context['stop_loss_price'] = stop_loss_price
        context['profit_target_price'] = profit_target_price
        
        # Use the position manager's calculate_position_size method
        quantity = self.position_manager.calculate_position_size(
            ticker=ticker,
            hist_data=hist_bars,
            price=current_price,
            stop_loss=stop_loss_price
        )
        
        # Create the OPEN signal with quantity
        open_signal = TradeSignal(
            signal=SignalType.OPEN,
            trade_type=TradeType.BUY,
            price=current_price,
            quantity=quantity,  # Add quantity to the signal
            metadata={
                'stop_loss': context['stop_loss_price'],
                'profit_target': context['profit_target_price'],
                'entry_time': current_time
            }
        )
        
        logger.info(f"Opening position for {ticker} at {current_price} with quantity {quantity}")
        return [open_signal]

    def _check_exit_conditions(self, ticker: str, current_time: datetime, 
                              current_price: float, context: TickerEventContext) -> Optional[List[TradeSignal]]:
        """
        Check exit conditions and generate exit signal if needed.
        
        Args:
            ticker: The ticker symbol
            current_time: Current bar timestamp
            current_price: Current price
            context: Dictionary tracking position state
            
        Returns:
            Optional[List[TradeSignal]]: List containing exit signal or None
        """
        exit_signal = None
        exit_reason = None
        
        # 1) Check for stop loss condition
        if current_price <= context['stop_loss_price']:
            exit_signal = TradeSignal(
                signal=SignalType.CLOSE,
                trade_type=TradeType.SELL,
                price=current_price,
                metadata={'reason': 'stop_loss'}
            )
            exit_reason = 'stop_loss'
        
        # 2) Check for profit target condition
        elif current_price >= context['profit_target_price']:
            exit_signal = TradeSignal(
                signal=SignalType.CLOSE,
                trade_type=TradeType.SELL,
                price=current_price,
                metadata={'reason': 'profit_target'}
            )
            exit_reason = 'profit_target'
        
        # 3) Check if we're at end of day
        # If we're past 3:30 PM, exit the position
        elif current_time.hour >= 15 and current_time.minute >= 30:
            exit_signal = TradeSignal(
                signal=SignalType.CLOSE,
                trade_type=TradeType.SELL,
                price=current_price,
                metadata={'reason': 'end_of_day'}
            )
            exit_reason = 'end_of_day'
        
        if exit_signal:
            # Reset position tracking
            context['position_open'] = False
            context['entry_price'] = None
            context['entry_time'] = None
            context['stop_loss_price'] = None
            context['profit_target_price'] = None
            
            logger.info(f"Closing position for {ticker} at {current_price} due to {exit_reason}")
            return [exit_signal]
        
        return None