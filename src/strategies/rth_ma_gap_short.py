from __future__ import annotations

"""
========== KNOWN ISSUES =================

1. [Minor] If order limit timeout fails, we don't reuse locates, but we should.


RTH Gap‑MA Short Strategy (live‑trading implementation)
-------------------------------------------------------------

* **Entry**  – First **RTH** 1‑minute bar (timestamp < 09:30 ET) whose
  *previous closed* 1‑minute bar is  
  ─ **red** (`close < open`) **and** its **close is below** a *n*‑period
  simple moving average of closes (default *n = 10*).
  * We still use the *current* forming bar's **open** as the execution price.
  * Stock must already be labelled as a **gap‑up ≥ `min_gap_up_pct`** by
    :meth:`_is_gap_up`.

* **Exit**   –
  * **Stop‑loss**: fixed `stop_loss_pct` (default 1000 % above entry.
  * **End‑of‑day**: close any open position at 15:55 ET.

* **Position sizing** – Delegated to the supplied `position_manager`.
* **Short locates**  – *Not required*; we rely on broker's automatic locate.

This class follows the same public interface as *OPBShortStrategy*, so it can
be dropped into the same trading framework.
"""
from typing import Optional, List, Any
from datetime import datetime, timedelta, time
import logging

import numpy as np
import pandas as pd
from pytz import timezone

from trading_framework.mixins import IntradayStrategy
from trading_framework.core import BarSlice
from trading_framework.execution_feedback import ExecutionReport, ExecOutcome

from strategies.trade_signals import TradeSignal, SignalType, TradeType
from tickers.iscancriteria import IScanCriteria

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class RTHGapMAShortStrategy(IntradayStrategy, IScanCriteria):
    """Live strategy that shorts pre‑market gap‑ups breaking below a 10‑bar SMA."""

    def __init__(
        self,
        ma_window: int = 10,
        min_gap_up_pct: float = 50.0,
        min_volume_dollars: float = 250_000,
        stop_loss_pct: float = 1000.0,
        exit_time: time = time(15, 55),
        min_price_threshold: float = 0.5,
        entry_start_time: time = time(9, 30),
        entry_end_time: time = time(12, 0),
    ) -> None:
        super().__init__()
        self.ma_window = int(ma_window)
        self.min_gap_up_pct = float(min_gap_up_pct)
        self.min_volume_dollars = float(min_volume_dollars)
        self.stop_loss_pct = float(stop_loss_pct)
        self.exit_time = exit_time
        self.market_open_time = time(9, 30)
        self.min_price_threshold = float(min_price_threshold)
        self.entry_start_time = entry_start_time
        self.entry_end_time = entry_end_time

        logger.info(
            "Initialized RTHGapMAShortStrategy: ma_window=%s, min_gap_up_pct=%s, "
            "min_volume_dollars=%s, stop_loss_pct=%s, exit_time=%s, min_price_threshold=%s",
            self.ma_window,
            self.min_gap_up_pct,
            self.min_volume_dollars,
            self.stop_loss_pct,
            self.exit_time,
            self.min_price_threshold,
        )

    # ──────────────────────────────────────────────────────────────────────────
    # Framework‑required methods
    # ------------------------------------------------------------------------
    def prepare(self, metadata: dict[str, Any]) -> tuple[timedelta, timedelta]:
        # 1‑minute bars, 3‑day look‑back for scanner / indicators
        return timedelta(minutes=1), timedelta(days=5)

    def process_bar(self, bars: BarSlice) -> Optional[List[TradeSignal]]:
        hist, latest = bars  # `hist` = closed bars; `latest` = current forming bar
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        indicators = self.ctx.get("ind")
        current_ts: datetime = indicators.get("current_timestamp")  # injected by framework

        logger.debug("%s - process_bar called at %s: hist %d bars, latest %d bars", ticker, current_ts, len(hist), len(latest))

        if hist.empty or latest.empty:
            logger.debug("%s - hist.empty=%s, latest.empty=%s - skipping processing", ticker, hist.empty, latest.empty)
            return None

        # ─── Exit check: end‑of‑day ──────────────────────────────────────────
        if self.in_pos(self.ctx) and current_ts.time() >= self.exit_time:
            sig = TradeSignal(
                symbol=ticker,
                signal=SignalType.CLOSE,
                trade_type=TradeType.BUY,
                price=latest["open"].iloc[0],
                quantity=day.pos.qty,
                metadata={"exit_reason": "END_OF_DAY"},
                source_bar=latest.iloc[0],
            )
            self.close_pos(self.ctx)
            day.custom["traded"] = True
            logger.info("%s: End‑of‑day exit", ticker)
            return [sig]

        # ─── Exit check: stop‑loss ───────────────────────────────────────────
        if self.in_pos(self.ctx):
            stop_price = day.pos.stop_px
            if day.pos.side == "SHORT" and hist["high"].iloc[-1] >= stop_price:
                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.CLOSE,
                    trade_type=TradeType.BUY,
                    price=stop_price,
                    quantity=day.pos.qty,
                    metadata={"exit_reason": "STOP_LOSS"},
                    source_bar=latest.iloc[0],
                )
                self.close_pos(self.ctx)
                day.custom["traded"] = True
                logger.info("%s: Stop‑loss hit (%.2f)", ticker, stop_price)
                return [sig]

        # ─── Only one trade per day ──────────────────────────────────────────
        if day.custom.get("traded"):
            return None

        # ─── Update stock status (gap‑up) every bar ─────────────────────────
        is_gap = self._is_gap_up(hist, latest)
        logger.debug("%s - is_gap=%s", ticker, is_gap)
        day.custom["stock_status"] = "GAP_UP" if is_gap else "NOT_GAP_UP"

        # ─── Entry logic (regular trading hours only) ────────────────────────
        if (
            not self.in_pos(self.ctx)
            and is_gap
            and self.entry_start_time <= current_ts.time() < self.entry_end_time
        ):
            logger.debug(
                "%s - evaluating entry: in_pos=%s, is_gap=%s, current_time=%s, entry_window=%s–%s",
                ticker,
                self.in_pos(self.ctx),
                is_gap,
                current_ts.time(),
                self.entry_start_time,
                self.entry_end_time,
            )
            # Use *closed* bars (hist) for all indicator calculations
            if len(hist) < self.ma_window:
                return None  # need at least ma_window closed bars

            sma = hist["close"].rolling(self.ma_window).mean().iloc[-1]
            last_close_bar = hist.iloc[-1]

            red_bar = last_close_bar["close"] < last_close_bar["open"]
            below_ma = last_close_bar["close"] < sma
            logger.debug("%s - sma=%.2f, last_close=%.2f, last_open=%.2f, red_bar=%s, below_ma=%s", ticker, sma, last_close_bar["close"], last_close_bar["open"], red_bar, below_ma)

            if red_bar and below_ma:
                entry_price = latest["open"].iloc[0]  # open of the forming bar
                stop_price = entry_price * (1 + self.stop_loss_pct / 100)
                qty = self.position_manager.calculate_position_size(
                    ticker=ticker,
                    hist_data=hist,
                    price=entry_price,
                    stop_loss=stop_price,
                )
                if qty <= 0:
                    logger.warning("%s: position size <= 0, skip", ticker)
                    return None

                sig = TradeSignal(
                    symbol=ticker,
                    signal=SignalType.OPEN,
                    trade_type=TradeType.SELL,
                    price=entry_price,
                    quantity=qty,
                    metadata={
                        "stop_price": stop_price,
                        "risk": stop_price - entry_price,
                        "entry_type": "RTH_MA_BREAK",
                        "timestamp": current_ts,
                    },
                    source_bar=latest.iloc[0],
                )

                self.open_pos(
                    self.ctx,
                    side="SHORT",
                    qty=qty,
                    entry_px=entry_price,
                    entry_ts=current_ts,
                    stop_px=stop_price,
                    target_px=0
                )
                day.custom["traded"] = True
                logger.info(
                    "%s: Shorted %s shares at %.2f (stop %.2f)",
                    ticker,
                    qty,
                    entry_price,
                    stop_price,
                )
                return [sig]

        return None

    # ──────────────────────────────────────────────────────────────────────────
    # Helper methods
    # ------------------------------------------------------------------------
    def _is_gap_up(self, hist: pd.DataFrame, latest: pd.DataFrame) -> bool:
        """Return **True** if today's *first* open so far ≥ `min_gap_up_pct` above
        yesterday's RTH close. Handles the case where `hist` contains no rows
        for *today* (e.g. at 04:00 on the very first bar of the new session)."""
        ticker = self.ctx.get("ticker")
        logger.debug("%s - _is_gap_up called: hist %d bars, latest %d bars", ticker, len(hist), len(latest))
        if hist.empty and latest.empty:
            logger.debug("%s - _is_gap_up: hist and latest empty => False", ticker)
            return False

        today = latest.index[-1].date()

        prev = hist[hist.index.map(lambda dt: dt.date() < today)]
        logger.debug("%s - prev bars count=%d", ticker, len(prev))
        if prev.empty:
            logger.debug("%s - _is_gap_up: prev empty => False", ticker)
            return False
        prev_rth = prev[(prev.index.time >= time(9, 30)) & (prev.index.time < time(16, 0))]
        logger.debug("%s - prev_rth count=%d", ticker, len(prev_rth))
        if prev_rth.empty:
            logger.debug("%s - _is_gap_up: prev_rth empty => False", ticker)
            return False
        prev_close = prev_rth["close"].iloc[-1]
        logger.debug("%s - prev_close=%.2f", ticker, prev_close)

        today_mask = hist.index.map(lambda dt: dt.date() == today)
        today_rows = hist[today_mask]
        logger.debug("%s - today_rows present, count=%d", ticker, len(today_rows))
        
        today_vol = today_rows["volume"]
        today_close_px = today_rows["close"]
        
        last_close = today_rows["close"].iloc[-1] if not today_rows.empty else 0
        
        gap_pct = (last_close - prev_close) / prev_close * 100
        logger.debug("%s - last_close=%.2f, gap_pct=%.2f, min_gap_up_pct=%.2f", ticker, last_close, gap_pct, self.min_gap_up_pct)
        if gap_pct < self.min_gap_up_pct:
            logger.debug("%s - gap_pct < threshold => False", ticker)
            return False

        dollars = (today_vol * today_close_px).sum()
        result = dollars >= self.min_volume_dollars
        logger.debug("%s - dollars=%.2f, min_vol=%.2f => %s", ticker, dollars, self.min_volume_dollars, result)
        return result

    # ──────────────────────────────────────────────────────────────────────────
    # Scanner                       
    # ------------------------------------------------------------------------
    def scan(self, historical_df: pd.DataFrame, current_day_df: Optional[pd.DataFrame] = None) -> List[str]:
        """Return tickers that meet our gap‑up and price criteria during RTH entry window."""
        current_time = datetime.now(timezone("US/Eastern"))
        # only scan between entry_start_time and entry_end_time
        ct = current_time.time()
        if not (self.entry_start_time <= ct < self.entry_end_time):
            return []

        df = historical_df.copy()
        if current_day_df is not None and not current_day_df.empty:
            df = pd.concat([df, current_day_df], ignore_index=True)

        df["date"] = pd.to_datetime(df["date"]).dt.date
        daily_df = (
            df.groupby(["symbol", "date"])
            .agg({"open": "first", "close": "last", "volume": "sum"})
            .reset_index()
        )

        matching: list[str] = []
        for ticker, tdf in daily_df.groupby("symbol"):
            tdf = tdf.sort_values("date").reset_index(drop=True)
            if len(tdf) < 2:
                continue
            prev_close = tdf["close"].iloc[-2]
            curr_price = tdf["close"].iloc[-1]
            gap_pct = (curr_price - prev_close) / prev_close * 100
            if (
                curr_price >= self.min_price_threshold
                and gap_pct >= self.min_gap_up_pct
            ):
                matching.append(ticker)
        logger.info("Scan found %d tickers: %s", len(matching), matching)
        return matching

    # ──────────────────────────────────────────────────────────────────────────
    # Execution‑report handler (no locate logic required)                      
    # ------------------------------------------------------------------------
    def on_exec(self, report: ExecutionReport) -> List[TradeSignal]:
        ticker = self.ctx.get("ticker")
        day = self.ctx["day"]
        
        # Exit if any execution outcome other than FILLED
        if report.outcome != ExecOutcome.FILLED:
            logger.warning("%s: Execution failed with outcome %s: %s. Exiting strategy.", 
                          ticker, report.outcome, report.broker_msg)
            
            # Reset the day context to allow the strategy to try again
            day.reset()
            # Don't exit the strategy, just reset the day context
            return [] 
        # No special handling needed for FILLED outcomes
        return []
