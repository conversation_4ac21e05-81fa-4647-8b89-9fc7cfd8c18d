{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import pandas as pd, numpy as np, matplotlib.pyplot as plt, logging\n", "from datetime import datetime, timedelta, time\n", "import pytz\n", "from pandas.tseries.offsets import BDay \n", "import logging\n", "\n", "logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "EST = pytz.timezone(\"America/New_York\")\n", "\n", "# ========== USER CONFIG =====================================================\n", "START_DT        = EST.localize(datetime(2010, 1, 1))\n", "END_DT          = EST.localize(datetime(2025, 1, 1))\n", "INTERVAL_SEC    = 30 * 60          # 30-minute bars\n", "N_QUANTILES     = 5                # quintiles\n", "GROSS_LEVERAGE  = 1.0              # 1 = 100 % long + 100 % short\n", "\n", "TC_BPS_PER_SIDE = 0                # 1 bps entry, 1 bps exit\n", "DEBUG           = False            # True ⇒ detailed per-day log\n", "# ============================================================================="]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["import os\n", "import dotenv\n", "from marketdata import MarketDataBuilder\n", "from universe.sp500_constituents import SP500Constituents\n", "\n", "os.chdir(os.path.expanduser('~/w/backtest'))\n", "\n", "dotenv.load_dotenv(\".env\", override=True)\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "market_data = (MarketDataBuilder()\n", "                 .with_trade_session(\"rth\") \n", "                 .with_adjusted(True)\n", "                 .with_period(\"intraday\")\n", "                 .build_market_data())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a2c50350bbaf492b90592648f89f2a92", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading:   0%|          | 0/813 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-28 00:13:16,444 - INFO - Downloaded AABA (10693 bars)\n", "2025-05-28 00:13:22,275 - INFO - Downloaded ACS (413 bars)\n", "2025-05-28 00:13:22,640 - INFO - Downloaded AAP (56949 bars)\n", "2025-05-28 00:13:24,255 - INFO - Downloaded ALB (62051 bars)\n", "2025-05-28 00:13:27,647 - INFO - Downloaded AAL (71594 bars)\n", "2025-05-28 00:13:27,662 - INFO - Downloaded A (56664 bars)\n", "2025-05-28 00:13:30,414 - INFO - Downloaded ABNB (27281 bars)\n", "2025-05-28 00:13:34,620 - INFO - Downloaded ADT (42301 bars)\n", "2025-05-28 00:13:35,157 - INFO - Downloaded AET (33865 bars)\n", "2025-05-28 00:13:35,335 - INFO - Downloaded ALGN (57493 bars)\n", "2025-05-28 00:13:38,833 - INFO - Downloaded ALLE (39586 bars)\n", "2025-05-28 00:13:39,600 - INFO - Downloaded AGN (40698 bars)\n", "2025-05-28 00:13:39,777 - INFO - Downloaded AIZ (53335 bars)\n", "2025-05-28 00:13:39,992 - INFO - Downloaded ADS (45108 bars)\n", "2025-05-28 00:13:40,955 - INFO - Downloaded AEE (54376 bars)\n", "2025-05-28 00:13:41,263 - INFO - Downloaded AES (58999 bars)\n", "2025-05-28 00:13:41,409 - INFO - Downloaded ABBV (58964 bars)\n", "2025-05-28 00:13:41,434 - INFO - Downloaded ABMD (46757 bars)\n", "2025-05-28 00:13:41,679 - INFO - Downloaded AMTM (1136 bars)\n", "2025-05-28 00:13:41,703 - INFO - Downloaded AMCR (22680 bars)\n", "2025-05-28 00:13:41,755 - INFO - Downloaded ABC (50208 bars)\n", "2025-05-28 00:13:42,528 - INFO - Downloaded AKS (52623 bars)\n", "2025-05-28 00:13:42,616 - INFO - Downloaded ALK (62318 bars)\n", "2025-05-28 00:13:43,038 - INFO - Downloaded AEP (59302 bars)\n", "2025-05-28 00:13:43,084 - WARNING - Skipping 'ANRZQ': no 'open'/'close' columns\n", "2025-05-28 00:13:43,721 - INFO - Downloaded ANDV (4416 bars)\n", "2025-05-28 00:13:43,971 - INFO - Downloaded ADP (58629 bars)\n", "2025-05-28 00:13:44,305 - INFO - Downloaded ADSK (60525 bars)\n", "2025-05-28 00:13:44,506 - INFO - Downloaded ALTR (49895 bars)\n", "2025-05-28 00:13:44,707 - INFO - Downloaded ADI (61177 bars)\n", "2025-05-28 00:13:45,006 - INFO - Downloaded ACN (58213 bars)\n", "2025-05-28 00:13:45,057 - INFO - Downloaded ABT (65981 bars)\n", "2025-05-28 00:13:45,176 - INFO - Downloaded ADM (61489 bars)\n", "2025-05-28 00:13:45,508 - INFO - Downloaded AJG (53493 bars)\n", "2025-05-28 00:13:45,794 - INFO - Downloaded AKAM (63468 bars)\n", "2025-05-28 00:13:46,032 - INFO - Downloaded ALL (56328 bars)\n", "2025-05-28 00:13:47,155 - INFO - Downloaded ACGL (54688 bars)\n", "2025-05-28 00:13:48,035 - INFO - Downloaded AFL (58442 bars)\n", "2025-05-28 00:13:50,139 - INFO - Downloaded ADBE (71327 bars)\n", "2025-05-28 00:13:50,427 - INFO - Downloaded AIV (55918 bars)\n", "2025-05-28 00:13:50,438 - INFO - Downloaded ALXN (45930 bars)\n", "2025-05-28 00:13:51,692 - INFO - Downloaded AIG (70539 bars)\n", "2025-05-28 00:13:52,694 - INFO - Downloaded ARG (22659 bars)\n", "2025-05-28 00:13:53,639 - INFO - Downloaded ANTM (28379 bars)\n", "2025-05-28 00:13:53,801 - INFO - Downloaded APOL (27151 bars)\n", "2025-05-28 00:13:54,064 - INFO - Downloaded ARNC (27522 bars)\n", "2025-05-28 00:13:55,491 - INFO - Downloaded ATGE (27011 bars)\n", "2025-05-28 00:13:56,251 - INFO - Downloaded APC (41127 bars)\n", "2025-05-28 00:13:56,296 - INFO - Downloaded APTV (27200 bars)\n", "2025-05-28 00:13:57,655 - INFO - Downloaded AMAT (80878 bars)\n", "2025-05-28 00:13:57,895 - INFO - Downloaded AYE (4180 bars)\n", "2025-05-28 00:13:57,987 - INFO - Downloaded ANET (44490 bars)\n", "2025-05-28 00:13:58,242 - INFO - Downloaded AMP (54068 bars)\n", "2025-05-28 00:14:00,047 - INFO - Downloaded AMG (52472 bars)\n", "2025-05-28 00:14:00,488 - INFO - Downloaded AME (53546 bars)\n", "2025-05-28 00:14:00,924 - INFO - Downloaded AN (54759 bars)\n", "2025-05-28 00:14:01,822 - INFO - Downloaded AMT (58234 bars)\n", "2025-05-28 00:14:02,274 - INFO - Downloaded AON (54190 bars)\n", "2025-05-28 00:14:02,442 - INFO - Downloaded APO (50509 bars)\n", "2025-05-28 00:14:03,245 - INFO - Downloaded APA (73308 bars)\n", "2025-05-28 00:14:03,593 - INFO - Downloaded ANSS (54001 bars)\n", "2025-05-28 00:14:03,779 - INFO - Downloaded ANF (61580 bars)\n", "2025-05-28 00:14:04,940 - INFO - Downloaded ARE (53901 bars)\n", "2025-05-28 00:14:05,185 - INFO - Downloaded BDK (747 bars)\n", "2025-05-28 00:14:05,258 - INFO - Downloaded AXON (29278 bars)\n", "2025-05-28 00:14:06,035 - INFO - Downloaded AOS (53576 bars)\n", "2025-05-28 00:14:06,116 - INFO - Downloaded ATI (56842 bars)\n", "2025-05-28 00:14:06,851 - INFO - Downloaded AMGN (67624 bars)\n", "2025-05-28 00:14:07,030 - INFO - Downloaded BALL (9911 bars)\n", "2025-05-28 00:14:07,418 - INFO - Downloaded AVB (54252 bars)\n", "2025-05-28 00:14:07,716 - INFO - Downloaded AVP (39508 bars)\n", "2025-05-28 00:14:08,265 - INFO - Downloaded BJS (1246 bars)\n", "2025-05-28 00:14:09,785 - INFO - Downloaded AVY (53406 bars)\n", "2025-05-28 00:14:09,808 - INFO - Downloaded APD (54855 bars)\n", "2025-05-28 00:14:10,640 - INFO - Downloaded AAPL (117652 bars)\n", "2025-05-28 00:14:11,276 - INFO - Downloaded BHGE (8969 bars)\n", "2025-05-28 00:14:11,288 - INFO - Downloaded BBWI (14315 bars)\n", "2025-05-28 00:14:12,555 - INFO - Downloaded APH (55106 bars)\n", "2025-05-28 00:14:13,176 - INFO - Downloaded AZO (53671 bars)\n", "2025-05-28 00:14:13,986 - INFO - Downloaded BCR (28087 bars)\n", "2025-05-28 00:14:14,570 - INFO - Downloaded BEAM (29729 bars)\n", "2025-05-28 00:14:15,277 - INFO - Downloaded BNI (552 bars)\n", "2025-05-28 00:14:15,506 - INFO - Downloaded AWK (54726 bars)\n", "2025-05-28 00:14:15,725 - INFO - Downloaded BHF (27222 bars)\n", "2025-05-28 00:14:16,462 - INFO - Downloaded ATO (52950 bars)\n", "2025-05-28 00:14:16,658 - INFO - Downloaded BBT (38006 bars)\n", "2025-05-28 00:14:17,223 - INFO - Downloaded ATVI (67721 bars)\n", "2025-05-28 00:14:17,456 - WARNING - Skipping 'BTUUQ': no 'open'/'close' columns\n", "2025-05-28 00:14:17,954 - INFO - Downloaded AMZN (102079 bars)\n", "2025-05-28 00:14:18,690 - INFO - Downloaded AYI (52834 bars)\n", "2025-05-28 00:14:20,687 - INFO - Downloaded AMD (104211 bars)\n", "2025-05-28 00:14:20,853 - INFO - Downloaded BAX (57174 bars)\n", "2025-05-28 00:14:21,159 - INFO - Downloaded BXLT (3791 bars)\n", "2025-05-28 00:14:21,700 - INFO - Downloaded AXP (71289 bars)\n", "2025-05-28 00:14:22,290 - INFO - Downloaded BKNG (26191 bars)\n", "2025-05-28 00:14:23,524 - INFO - Downloaded BDX (54999 bars)\n", "2025-05-28 00:14:23,907 - INFO - Downloaded BIO (51645 bars)\n", "2025-05-28 00:14:24,168 - INFO - Downloaded BKR (36099 bars)\n", "2025-05-28 00:14:25,126 - INFO - Downloaded BG (56326 bars)\n", "2025-05-28 00:14:25,466 - INFO - Downloaded BMC (14045 bars)\n", "2025-05-28 00:14:25,588 - INFO - Downloaded AVGO (69655 bars)\n", "2025-05-28 00:14:25,731 - INFO - Downloaded BIG (58078 bars)\n", "2025-05-28 00:14:26,174 - INFO - Downloaded BMS (33061 bars)\n", "2025-05-28 00:14:26,981 - INFO - Downloaded BF.B (53582 bars)\n", "2025-05-28 00:14:27,249 - INFO - Downloaded BBY (68981 bars)\n", "2025-05-28 00:14:27,537 - INFO - Downloaded BA (89711 bars)\n", "2025-05-28 00:14:28,425 - INFO - Downloaded BRCM (28126 bars)\n", "2025-05-28 00:14:28,426 - INFO - Downloaded BK (60316 bars)\n", "2025-05-28 00:14:30,118 - INFO - Downloaded <PERSON> (10297 bars)\n", "2025-05-28 00:14:30,135 - INFO - Downloaded BLDR (56379 bars)\n", "2025-05-28 00:14:30,306 - INFO - Downloaded CARR (21108 bars)\n", "2025-05-28 00:14:31,120 - INFO - Downloaded BIIB (63466 bars)\n", "2025-05-28 00:14:31,362 - INFO - Downloaded BBBY (63826 bars)\n", "2025-05-28 00:14:31,792 - INFO - Downloaded BEN (57105 bars)\n", "2025-05-28 00:14:32,354 - INFO - Downloaded CAM (23285 bars)\n", "2025-05-28 00:14:34,066 - INFO - Downloaded CEPH (6929 bars)\n", "2025-05-28 00:14:34,450 - INFO - Downloaded BR (52989 bars)\n", "2025-05-28 00:14:34,680 - INFO - Downloaded CA (34074 bars)\n", "2025-05-28 00:14:34,943 - INFO - Downloaded BLL (44976 bars)\n", "2025-05-28 00:14:36,073 - INFO - Downloaded CBRE (25162 bars)\n", "2025-05-28 00:14:36,233 - INFO - Downloaded BSX (62609 bars)\n", "2025-05-28 00:14:36,514 - INFO - Downloaded BAC (109958 bars)\n", "2025-05-28 00:14:36,731 - INFO - Downloaded CCEP (26201 bars)\n", "2025-05-28 00:14:37,946 - INFO - Downloaded CDAY (21078 bars)\n", "2025-05-28 00:14:38,818 - INFO - Downloaded CBS (38367 bars)\n", "2025-05-28 00:14:38,870 - INFO - Downloaded BRO (53328 bars)\n", "2025-05-28 00:14:40,620 - INFO - Downloaded CCE (34133 bars)\n", "2025-05-28 00:14:40,741 - INFO - Downloaded BMY (74737 bars)\n", "2025-05-28 00:14:41,444 - INFO - Downloaded BLK (56306 bars)\n", "2025-05-28 00:14:41,948 - INFO - Downloaded CFN (18409 bars)\n", "2025-05-28 00:14:42,322 - INFO - Downloaded CEG (20640 bars)\n", "2025-05-28 00:14:42,571 - INFO - Downloaded BWA (56210 bars)\n", "2025-05-28 00:14:42,776 - INFO - Downloaded BXP (55312 bars)\n", "2025-05-28 00:14:43,800 - INFO - Downloaded CB (55370 bars)\n", "2025-05-28 00:14:44,347 - INFO - Downloaded CAH (56771 bars)\n", "2025-05-28 00:14:45,249 - INFO - Downloaded BRK.B (81245 bars)\n", "2025-05-28 00:14:46,008 - INFO - Downloaded CAG (59232 bars)\n", "2025-05-28 00:14:46,694 - INFO - Downloaded BX (70965 bars)\n", "2025-05-28 00:14:47,053 - INFO - Downloaded CDW (41966 bars)\n", "2025-05-28 00:14:47,205 - INFO - Downloaded CBOE (53231 bars)\n", "2025-05-28 00:14:49,705 - INFO - Downloaded CDNS (57852 bars)\n", "2025-05-28 00:14:50,292 - INFO - Downloaded CCI (56982 bars)\n", "2025-05-28 00:14:50,543 - INFO - Downloaded CAT (82359 bars)\n", "2025-05-28 00:14:51,302 - INFO - Downloaded CE (54112 bars)\n", "2025-05-28 00:14:51,895 - INFO - Downloaded CMCSK (21812 bars)\n", "2025-05-28 00:14:52,918 - INFO - Downloaded CELG (47384 bars)\n", "2025-05-28 00:14:53,941 - INFO - Downloaded CHTR (53349 bars)\n", "2025-05-28 00:14:54,663 - INFO - Downloaded CFG (41313 bars)\n", "2025-05-28 00:14:56,802 - INFO - Downloaded CPAY (2867 bars)\n", "2025-05-28 00:14:57,014 - INFO - Downloaded C (100323 bars)\n", "2025-05-28 00:14:57,319 - INFO - Downloaded CCL (89589 bars)\n", "2025-05-28 00:14:57,763 - INFO - Downloaded CHRW (56313 bars)\n", "2025-05-28 00:14:57,833 - INFO - Downloaded CPGX (3617 bars)\n", "2025-05-28 00:14:58,543 - INFO - Downloaded CHD (54496 bars)\n", "2025-05-28 00:14:58,733 - INFO - Downloaded CINF (55265 bars)\n", "2025-05-28 00:14:59,250 - INFO - Downloaded COV (18492 bars)\n", "2025-05-28 00:15:00,675 - INFO - Downloaded CERN (48207 bars)\n", "2025-05-28 00:15:00,743 - INFO - Downloaded CMA (56699 bars)\n", "2025-05-28 00:15:00,901 - INFO - Downloaded CL (60025 bars)\n", "2025-05-28 00:15:01,188 - INFO - Downloaded CF (61098 bars)\n", "2025-05-28 00:15:02,313 - INFO - Downloaded CLX (58149 bars)\n", "2025-05-28 00:15:02,499 - INFO - Downloaded CI (56961 bars)\n", "2025-05-28 00:15:02,824 - INFO - Downloaded COL (31788 bars)\n", "2025-05-28 00:15:03,678 - INFO - Downloaded CNP (56260 bars)\n", "2025-05-28 00:15:04,694 - INFO - Downloaded CPWR (19404 bars)\n", "2025-05-28 00:15:05,125 - INFO - Downloaded COG (46548 bars)\n", "2025-05-28 00:15:05,203 - INFO - Downloaded CHK (78644 bars)\n", "2025-05-28 00:15:05,297 - INFO - Downloaded CMG (63563 bars)\n", "2025-05-28 00:15:05,369 - INFO - Downloaded CME (58345 bars)\n", "2025-05-28 00:15:05,580 - INFO - Downloaded CSRA (8451 bars)\n", "2025-05-28 00:15:06,655 - INFO - Downloaded CNX (59197 bars)\n", "2025-05-28 00:15:07,204 - INFO - Downloaded CMS (54162 bars)\n", "2025-05-28 00:15:08,034 - INFO - Downloaded COR (44211 bars)\n", "2025-05-28 00:15:09,229 - INFO - Downloaded COTY (50497 bars)\n", "2025-05-28 00:15:09,257 - INFO - Downloaded COO (53027 bars)\n", "2025-05-28 00:15:09,437 - INFO - Downloaded CMI (60026 bars)\n", "2025-05-28 00:15:09,856 - INFO - Downloaded CMCSA (70744 bars)\n", "2025-05-28 00:15:09,934 - INFO - Downloaded CPRI (26214 bars)\n", "2025-05-28 00:15:10,235 - INFO - Downloaded CLF (87043 bars)\n", "2025-05-28 00:15:10,410 - INFO - Downloaded CVH (11875 bars)\n", "2025-05-28 00:15:11,377 - INFO - Downloaded DAY (3405 bars)\n", "2025-05-28 00:15:12,776 - INFO - Downloaded COF (61500 bars)\n", "2025-05-28 00:15:13,628 - INFO - Downloaded CTVA (22575 bars)\n", "2025-05-28 00:15:15,050 - INFO - Downloaded COST (70629 bars)\n", "2025-05-28 00:15:15,236 - INFO - Downloaded CNC (57143 bars)\n", "2025-05-28 00:15:15,827 - INFO - Downloaded CVC (23682 bars)\n", "2025-05-28 00:15:17,129 - INFO - Downloaded CRWD (35785 bars)\n", "2025-05-28 00:15:17,304 - INFO - Downloaded CRL (52823 bars)\n", "2025-05-28 00:15:17,331 - INFO - Downloaded CTL (46728 bars)\n", "2025-05-28 00:15:17,348 - INFO - Downloaded CTRA (25939 bars)\n", "2025-05-28 00:15:17,418 - INFO - Downloaded CPT (53320 bars)\n", "2025-05-28 00:15:18,077 - INFO - Downloaded CPB (58083 bars)\n", "2025-05-28 00:15:18,230 - INFO - Downloaded CSGP (53695 bars)\n", "2025-05-28 00:15:19,639 - INFO - Downloaded COP (76934 bars)\n", "2025-05-28 00:15:20,853 - INFO - Downloaded CTLT (38207 bars)\n", "2025-05-28 00:15:22,999 - INFO - Downloaded CXO (40623 bars)\n", "2025-05-28 00:15:23,172 - INFO - Downloaded CTAS (55447 bars)\n", "2025-05-28 00:15:24,019 - INFO - Downloaded DF (37286 bars)\n", "2025-05-28 00:15:26,505 - INFO - Downloaded CPRT (56390 bars)\n", "2025-05-28 00:15:27,304 - INFO - Downloaded CZR (55571 bars)\n", "2025-05-28 00:15:28,068 - INFO - Downloaded CTSH (60713 bars)\n", "2025-05-28 00:15:28,676 - INFO - Downloaded CVS (73385 bars)\n", "2025-05-28 00:15:29,619 - INFO - Downloaded CSCO (95529 bars)\n", "2025-05-28 00:15:29,661 - INFO - Downloaded CTXS (50182 bars)\n", "2025-05-28 00:15:30,361 - INFO - Downloaded CSX (66270 bars)\n", "2025-05-28 00:15:31,619 - INFO - Downloaded DECK (57530 bars)\n", "2025-05-28 00:15:31,720 - INFO - Downloaded DG (60671 bars)\n", "2025-05-28 00:15:33,039 - INFO - Downloaded DNB (48133 bars)\n", "2025-05-28 00:15:33,102 - INFO - Downloaded DELL (49936 bars)\n", "2025-05-28 00:15:33,293 - INFO - Downloaded CVX (84401 bars)\n", "2025-05-28 00:15:35,489 - INFO - Downloaded DFS (59825 bars)\n", "2025-05-28 00:15:35,576 - INFO - Downloaded DGX (54658 bars)\n", "2025-05-28 00:15:36,024 - INFO - Downloaded DWDP (8483 bars)\n", "2025-05-28 00:15:36,767 - INFO - Downloaded D (60724 bars)\n", "2025-05-28 00:15:37,441 - INFO - Downloaded DO (49359 bars)\n", "2025-05-28 00:15:38,834 - INFO - Downloaded EG (5366 bars)\n", "2025-05-28 00:15:38,855 - INFO - Downloaded DTV (26921 bars)\n", "2025-05-28 00:15:39,126 - INFO - Downloaded DD (58198 bars)\n", "2025-05-28 00:15:39,156 - WARNING - Skipping 'EKDKQ': no 'open'/'close' columns\n", "2025-05-28 00:15:39,160 - INFO - Downloaded DAL (85136 bars)\n", "2025-05-28 00:15:40,181 - INFO - Downloaded DE (66377 bars)\n", "2025-05-28 00:15:40,268 - INFO - Downloaded DOV (54347 bars)\n", "2025-05-28 00:15:40,467 - INFO - Downloaded CRM (81483 bars)\n", "2025-05-28 00:15:40,765 - INFO - Downloaded DOC (42011 bars)\n", "2025-05-28 00:15:41,094 - INFO - Downloaded DNR (46237 bars)\n", "2025-05-28 00:15:42,508 - INFO - Downloaded DHR (58038 bars)\n", "2025-05-28 00:15:42,978 - INFO - Downloaded DLTR (62239 bars)\n", "2025-05-28 00:15:43,455 - INFO - Downloaded ELV (11185 bars)\n", "2025-05-28 00:15:43,836 - INFO - Downloaded DHI (62464 bars)\n", "2025-05-28 00:15:45,591 - INFO - Downloaded DLR (55830 bars)\n", "2025-05-28 00:15:45,763 - INFO - Downloaded DISCA (49572 bars)\n", "2025-05-28 00:15:46,203 - INFO - Downloaded DIS (89607 bars)\n", "2025-05-28 00:15:46,325 - INFO - Downloaded DRE (45988 bars)\n", "2025-05-28 00:15:47,107 - INFO - Downloaded DRI (57685 bars)\n", "2025-05-28 00:15:47,295 - INFO - Downloaded DOW (63738 bars)\n", "2025-05-28 00:15:48,178 - INFO - Downloaded DISCK (45541 bars)\n", "2025-05-28 00:15:48,559 - INFO - Downloaded DXC (31209 bars)\n", "2025-05-28 00:15:49,598 - INFO - Downloaded DISH (56077 bars)\n", "2025-05-28 00:15:50,507 - INFO - Downloaded DPZ (56397 bars)\n", "2025-05-28 00:15:50,547 - INFO - Downloaded DVA (55042 bars)\n", "2025-05-28 00:15:51,778 - INFO - Downloaded DTE (54620 bars)\n", "2025-05-28 00:15:52,713 - INFO - Downloaded EMC (36235 bars)\n", "2025-05-28 00:15:52,850 - INFO - Downloaded EP (17491 bars)\n", "2025-05-28 00:15:54,246 - INFO - Downloaded DUK (62428 bars)\n", "2025-05-28 00:15:54,669 - INFO - Downloaded EA (55423 bars)\n", "2025-05-28 00:15:56,266 - INFO - Downloaded DVN (74715 bars)\n", "2025-05-28 00:15:56,329 - INFO - Downloaded ECL (54832 bars)\n", "2025-05-28 00:15:56,947 - INFO - Downloaded ENDP (54771 bars)\n", "2025-05-28 00:15:57,420 - INFO - Downloaded ED (58268 bars)\n", "2025-05-28 00:15:57,708 - INFO - Downloaded DXCM (58010 bars)\n", "2025-05-28 00:15:58,075 - INFO - Downloaded EFX (53980 bars)\n", "2025-05-28 00:15:58,949 - INFO - Downloaded EVHC (18416 bars)\n", "2025-05-28 00:15:59,024 - INFO - Downloaded EIX (55233 bars)\n", "2025-05-28 00:15:59,184 - INFO - Downloaded ERIE (49972 bars)\n", "2025-05-28 00:15:59,184 - INFO - Downloaded EPAM (46071 bars)\n", "2025-05-28 00:16:00,270 - INFO - Downloaded EVRG (24389 bars)\n", "2025-05-28 00:16:01,795 - INFO - Downloaded EQR (54950 bars)\n", "2025-05-28 00:16:02,059 - INFO - Downloaded ENPH (61490 bars)\n", "2025-05-28 00:16:02,081 - INFO - Downloaded ES (48349 bars)\n", "2025-05-28 00:16:03,720 - INFO - Downloaded ESV (39517 bars)\n", "2025-05-28 00:16:03,923 - INFO - Downloaded EQIX (55210 bars)\n", "2025-05-28 00:16:03,968 - INFO - Downloaded EMN (54791 bars)\n", "2025-05-28 00:16:05,176 - INFO - Downloaded ESS (53290 bars)\n", "2025-05-28 00:16:05,382 - INFO - Downloaded EMR (58561 bars)\n", "2025-05-28 00:16:05,492 - INFO - Downloaded EOG (65743 bars)\n", "2025-05-28 00:16:05,566 - INFO - Downloaded ETSY (48950 bars)\n", "2025-05-28 00:16:05,764 - INFO - Downloaded ESRX (36276 bars)\n", "2025-05-28 00:16:06,112 - INFO - Downloaded EBAY (74983 bars)\n", "2025-05-28 00:16:06,654 - INFO - Downloaded FDO (20490 bars)\n", "2025-05-28 00:16:07,357 - INFO - Downloaded ETFC (44307 bars)\n", "2025-05-28 00:16:08,046 - INFO - Downloaded EL (58414 bars)\n", "2025-05-28 00:16:10,751 - INFO - Downloaded ETR (54750 bars)\n", "2025-05-28 00:16:12,124 - INFO - Downloaded FCPT (32477 bars)\n", "2025-05-28 00:16:12,307 - INFO - Downloaded ETN (58351 bars)\n", "2025-05-28 00:16:12,953 - INFO - Downloaded EXR (53897 bars)\n", "2025-05-28 00:16:14,643 - INFO - Downloaded FBHS (39999 bars)\n", "2025-05-28 00:16:15,158 - INFO - Downloaded FII (35315 bars)\n", "2025-05-28 00:16:16,418 - INFO - Downloaded EXC (62254 bars)\n", "2025-05-28 00:16:16,650 - INFO - Downloaded EQT (62244 bars)\n", "2025-05-28 00:16:17,137 - INFO - Downloaded FI (34588 bars)\n", "2025-05-28 00:16:17,212 - INFO - Downloaded EXPE (65641 bars)\n", "2025-05-28 00:16:18,070 - INFO - Downloaded FICO (52190 bars)\n", "2025-05-28 00:16:18,137 - INFO - Downloaded EXPD (55946 bars)\n", "2025-05-28 00:16:18,466 - INFO - Downloaded FB (76948 bars)\n", "2025-05-28 00:16:18,673 - INFO - Downloaded FLIR (42815 bars)\n", "2025-05-28 00:16:19,030 - INFO - Downloaded FE (57645 bars)\n", "2025-05-28 00:16:19,807 - INFO - Downloaded EW (57464 bars)\n", "2025-05-28 00:16:20,324 - INFO - Downloaded FRX (19369 bars)\n", "2025-05-28 00:16:20,327 - INFO - Downloaded FAST (59293 bars)\n", "2025-05-28 00:16:20,911 - INFO - Downloaded FANG (53299 bars)\n", "2025-05-28 00:16:21,166 - INFO - Downloaded FIS (55671 bars)\n", "2025-05-28 00:16:22,315 - INFO - Downloaded GEHC (9587 bars)\n", "2025-05-28 00:16:22,739 - INFO - Downloaded FHN (58064 bars)\n", "2025-05-28 00:16:22,792 - INFO - Downloaded FDX (68345 bars)\n", "2025-05-28 00:16:23,252 - INFO - Downloaded FOX (43695 bars)\n", "2025-05-28 00:16:23,536 - INFO - Downloaded FDS (52468 bars)\n", "2025-05-28 00:16:23,560 - INFO - Downloaded F (102766 bars)\n", "2025-05-28 00:16:24,427 - INFO - Downloaded GEV (4136 bars)\n", "2025-05-28 00:16:24,608 - INFO - Downloaded FFIV (59833 bars)\n", "2025-05-28 00:16:24,982 - INFO - Downloaded FLR (59662 bars)\n", "2025-05-28 00:16:24,995 - INFO - Downloaded FL (62381 bars)\n", "2025-05-28 00:16:25,420 - INFO - Downloaded GENZ (5295 bars)\n", "2025-05-28 00:16:26,102 - INFO - Downloaded GHC (29173 bars)\n", "2025-05-28 00:16:26,544 - INFO - Downloaded FISV (52982 bars)\n", "2025-05-28 00:16:27,512 - INFO - Downloaded FCX (96868 bars)\n", "2025-05-28 00:16:27,603 - INFO - Downloaded FMC (54987 bars)\n", "2025-05-28 00:16:28,407 - INFO - Downloaded FITB (63283 bars)\n", "2025-05-28 00:16:28,718 - INFO - Downloaded FOXA (46158 bars)\n", "2025-05-28 00:16:29,410 - INFO - Downloaded FTV (31219 bars)\n", "2025-05-28 00:16:29,539 - INFO - Downloaded GAS (22689 bars)\n", "2025-05-28 00:16:29,769 - INFO - Downloaded FLT (47058 bars)\n", "2025-05-28 00:16:31,023 - INFO - Downloaded FRT (53790 bars)\n", "2025-05-28 00:16:32,024 - INFO - Downloaded FLS (54051 bars)\n", "2025-05-28 00:16:32,710 - INFO - Downloaded GGP (31486 bars)\n", "2025-05-28 00:16:32,932 - INFO - Downloaded GDDY (36440 bars)\n", "2025-05-28 00:16:33,218 - INFO - Downloaded GL (19751 bars)\n", "2025-05-28 00:16:34,308 - INFO - Downloaded GR (9208 bars)\n", "2025-05-28 00:16:35,345 - INFO - Downloaded FOSL (58389 bars)\n", "2025-05-28 00:16:35,769 - INFO - Downloaded FTI (65329 bars)\n", "2025-05-28 00:16:36,673 - INFO - Downloaded FSLR (77081 bars)\n", "2025-05-28 00:16:37,396 - INFO - Downloaded GMCR (29921 bars)\n", "2025-05-28 00:16:37,738 - INFO - Downloaded GEN (39501 bars)\n", "2025-05-28 00:16:37,770 - INFO - Downloaded GD (57182 bars)\n", "2025-05-28 00:16:37,999 - INFO - Downloaded FTR (46590 bars)\n", "2025-05-28 00:16:38,806 - INFO - Downloaded FRC (44907 bars)\n", "2025-05-28 00:16:41,288 - INFO - Downloaded HAR (25517 bars)\n", "2025-05-28 00:16:42,832 - INFO - Downloaded HCBK (22759 bars)\n", "2025-05-28 00:16:45,208 - INFO - Downloaded GWW (53424 bars)\n", "2025-05-28 00:16:45,709 - INFO - Downloaded HNZ (12867 bars)\n", "2025-05-28 00:16:46,714 - INFO - Downloaded GPC (54109 bars)\n", "2025-05-28 00:16:46,715 - INFO - Downloaded GNRC (56322 bars)\n", "2025-05-28 00:16:46,839 - INFO - Downloaded GNW (63224 bars)\n", "2025-05-28 00:16:47,863 - INFO - Downloaded GPN (54952 bars)\n", "2025-05-28 00:16:48,361 - INFO - Downloaded GM (84545 bars)\n", "2025-05-28 00:16:49,421 - INFO - Downloaded GIS (62194 bars)\n", "2025-05-28 00:16:49,755 - INFO - Downloaded HCP (49658 bars)\n", "2025-05-28 00:16:50,045 - INFO - Downloaded GPS (65865 bars)\n", "2025-05-28 00:16:50,524 - INFO - Downloaded GT (65434 bars)\n", "2025-05-28 00:16:51,312 - INFO - Downloaded GLW (67925 bars)\n", "2025-05-28 00:16:51,542 - INFO - Downloaded FTNT (63134 bars)\n", "2025-05-28 00:16:51,713 - INFO - Downloaded GOOG (88115 bars)\n", "2025-05-28 00:16:53,306 - INFO - Downloaded HSH (7545 bars)\n", "2025-05-28 00:16:53,467 - INFO - Downloaded HCA (51892 bars)\n", "2025-05-28 00:16:53,922 - INFO - Downloaded GS (81948 bars)\n", "2025-05-28 00:16:54,705 - INFO - Downloaded HII (48396 bars)\n", "2025-05-28 00:16:55,536 - INFO - Downloaded GME (75863 bars)\n", "2025-05-28 00:16:56,019 - INFO - Downloaded HBI (60646 bars)\n", "2025-05-28 00:16:56,325 - INFO - Downloaded GRMN (56516 bars)\n", "2025-05-28 00:16:56,395 - INFO - Downloaded HBAN (68163 bars)\n", "2025-05-28 00:16:56,488 - INFO - Downloaded GILD (83094 bars)\n", "2025-05-28 00:16:56,527 - INFO - Downloaded HOT (25182 bars)\n", "2025-05-28 00:16:56,715 - INFO - Downloaded GOOGL (63538 bars)\n", "2025-05-28 00:16:56,990 - INFO - Downloaded HFC (42224 bars)\n", "2025-05-28 00:16:58,891 - INFO - Downloaded GE (99606 bars)\n", "2025-05-28 00:16:59,355 - INFO - Downloaded HLT (43211 bars)\n", "2025-05-28 00:17:00,148 - INFO - Downloaded HES (62059 bars)\n", "2025-05-28 00:17:00,156 - INFO - Downloaded HSP (20143 bars)\n", "2025-05-28 00:17:01,437 - INFO - Downloaded HAS (57654 bars)\n", "2025-05-28 00:17:01,470 - INFO - Downloaded HD (76488 bars)\n", "2025-05-28 00:17:01,795 - INFO - Downloaded HAL (80894 bars)\n", "2025-05-28 00:17:01,826 - INFO - Downloaded HPE (43469 bars)\n", "2025-05-28 00:17:02,470 - INFO - Downloaded HOG (59315 bars)\n", "2025-05-28 00:17:03,238 - INFO - Downloaded HWM (18935 bars)\n", "2025-05-28 00:17:03,854 - INFO - Downloaded HUBB (31831 bars)\n", "2025-05-28 00:17:04,645 - INFO - Downloaded INFO (20955 bars)\n", "2025-05-28 00:17:04,799 - INFO - Downloaded HIG (58614 bars)\n", "2025-05-28 00:17:05,994 - INFO - Downloaded HOLX (56901 bars)\n", "2025-05-28 00:17:06,851 - INFO - Downloaded HRS (33821 bars)\n", "2025-05-28 00:17:07,447 - INFO - Downloaded HP (57277 bars)\n", "2025-05-28 00:17:08,808 - INFO - Downloaded HRB (57239 bars)\n", "2025-05-28 00:17:09,331 - INFO - Downloaded HON (63071 bars)\n", "2025-05-28 00:17:09,655 - INFO - Downloaded JAVA (8717 bars)\n", "2025-05-28 00:17:10,920 - INFO - Downloaded INVH (30137 bars)\n", "2025-05-28 00:17:11,910 - INFO - Downloaded HPQ (76252 bars)\n", "2025-05-28 00:17:12,762 - INFO - Downloaded IEX (52374 bars)\n", "2025-05-28 00:17:13,051 - INFO - Downloaded ICE (55821 bars)\n", "2025-05-28 00:17:13,292 - INFO - Downloaded IQV (26341 bars)\n", "2025-05-28 00:17:13,428 - INFO - Downloaded IGT (57232 bars)\n", "2025-05-28 00:17:13,776 - INFO - Downloaded J (18598 bars)\n", "2025-05-28 00:17:15,082 - INFO - Downloaded INCY (57753 bars)\n", "2025-05-28 00:17:15,395 - INFO - Downloaded HSIC (54848 bars)\n", "2025-05-28 00:17:15,474 - INFO - Downloaded HSY (56332 bars)\n", "2025-05-28 00:17:15,530 - INFO - Downloaded HUM (56968 bars)\n", "2025-05-28 00:17:15,929 - INFO - Downloaded IFF (55236 bars)\n", "2025-05-28 00:17:17,509 - INFO - Downloaded HRL (56636 bars)\n", "2025-05-28 00:17:17,766 - INFO - Downloaded IPGP (53665 bars)\n", "2025-05-28 00:17:18,379 - INFO - Downloaded IDXX (54026 bars)\n", "2025-05-28 00:17:19,201 - INFO - Downloaded IPG (55960 bars)\n", "2025-05-28 00:17:19,674 - INFO - Downloaded IP (60573 bars)\n", "2025-05-28 00:17:20,857 - INFO - Downloaded KG (4385 bars)\n", "2025-05-28 00:17:21,063 - INFO - Downloaded HST (57143 bars)\n", "2025-05-28 00:17:21,292 - INFO - Downloaded JOY (21151 bars)\n", "2025-05-28 00:17:22,309 - INFO - Downloaded INTU (59172 bars)\n", "2025-05-28 00:17:22,378 - INFO - Downloaded JNS (26694 bars)\n", "2025-05-28 00:17:24,099 - INFO - Downloaded IT (53319 bars)\n", "2025-05-28 00:17:24,302 - INFO - Downloaded ILMN (59678 bars)\n", "2025-05-28 00:17:24,418 - INFO - Downloaded ITW (55273 bars)\n", "2025-05-28 00:17:24,481 - INFO - Downloaded IRM (59002 bars)\n", "2025-05-28 00:17:24,531 - INFO - Downloaded ITT (52809 bars)\n", "2025-05-28 00:17:25,871 - INFO - Downloaded IBM (81880 bars)\n", "2025-05-28 00:17:26,554 - INFO - Downloaded ISRG (59621 bars)\n", "2025-05-28 00:17:27,273 - INFO - Downloaded JEF (36701 bars)\n", "2025-05-28 00:17:27,846 - INFO - Downloaded JCP (53731 bars)\n", "2025-05-28 00:17:28,526 - INFO - Downloaded JBHT (55056 bars)\n", "2025-05-28 00:17:29,992 - INFO - Downloaded KEYS (38348 bars)\n", "2025-05-28 00:17:30,432 - INFO - Downloaded JNPR (62439 bars)\n", "2025-05-28 00:17:30,633 - INFO - Downloaded KVUE (7933 bars)\n", "2025-05-28 00:17:31,697 - INFO - Downloaded JKHY (53437 bars)\n", "2025-05-28 00:17:31,817 - INFO - Downloaded JEC (35744 bars)\n", "2025-05-28 00:17:32,639 - INFO - Downloaded JCI (59185 bars)\n", "2025-05-28 00:17:32,699 - INFO - Downloaded KDP (27230 bars)\n", "2025-05-28 00:17:33,074 - INFO - Downloaded KHC (50098 bars)\n", "2025-05-28 00:17:33,132 - INFO - Downloaded JBL (57929 bars)\n", "2025-05-28 00:17:34,296 - INFO - Downloaded INTC (104074 bars)\n", "2025-05-28 00:17:34,801 - INFO - Downloaded IVZ (58095 bars)\n", "2025-05-28 00:17:35,654 - INFO - Downloaded KORS (29749 bars)\n", "2025-05-28 00:17:36,700 - INFO - Downloaded K (59041 bars)\n", "2025-05-28 00:17:36,970 - INFO - Downloaded KRFT (10917 bars)\n", "2025-05-28 00:17:39,146 - INFO - Downloaded KEY (67082 bars)\n", "2025-05-28 00:17:39,668 - INFO - Downloaded KKR (56401 bars)\n", "2025-05-28 00:17:41,136 - INFO - Downloaded IR (55450 bars)\n", "2025-05-28 00:17:41,634 - INFO - Downloaded KIM (56759 bars)\n", "2025-05-28 00:17:42,371 - INFO - Downloaded JNJ (78456 bars)\n", "2025-05-28 00:17:42,378 - INFO - Downloaded JWN (66239 bars)\n", "2025-05-28 00:17:42,646 - INFO - Downloaded LDOS (41202 bars)\n", "2025-05-28 00:17:43,298 - INFO - Downloaded KLAC (60213 bars)\n", "2025-05-28 00:17:43,911 - INFO - Downloaded LIN (35039 bars)\n", "2025-05-28 00:17:44,192 - INFO - Downloaded LB (39081 bars)\n", "2025-05-28 00:17:44,881 - INFO - Downloaded KMI (69389 bars)\n", "2025-05-28 00:17:45,048 - INFO - Downloaded JPM (94020 bars)\n", "2025-05-28 00:17:45,989 - INFO - Downloaded L (54082 bars)\n", "2025-05-28 00:17:46,077 - INFO - Downloaded LLTC (27597 bars)\n", "2025-05-28 00:17:46,561 - INFO - Downloaded KSU (43566 bars)\n", "2025-05-28 00:17:47,056 - INFO - Downloaded LII (52454 bars)\n", "2025-05-28 00:17:47,465 - INFO - Downloaded KMB (58215 bars)\n", "2025-05-28 00:17:47,730 - INFO - Downloaded LHX (21106 bars)\n", "2025-05-28 00:17:48,087 - INFO - Downloaded LM (37909 bars)\n", "2025-05-28 00:17:48,137 - INFO - Downloaded LLL (37606 bars)\n", "2025-05-28 00:17:49,920 - INFO - Downloaded LO (20827 bars)\n", "2025-05-28 00:17:50,976 - INFO - Downloaded LEG (55413 bars)\n", "2025-05-28 00:17:51,888 - INFO - Downloaded KO (85965 bars)\n", "2025-05-28 00:17:53,233 - INFO - Downloaded LH (54227 bars)\n", "2025-05-28 00:17:53,668 - INFO - Downloaded LUMN (26696 bars)\n", "2025-05-28 00:17:54,139 - INFO - Downloaded KR (67296 bars)\n", "2025-05-28 00:17:55,000 - INFO - Downloaded LVLT (31129 bars)\n", "2025-05-28 00:17:55,684 - INFO - Downloaded LLY (67268 bars)\n", "2025-05-28 00:17:55,855 - INFO - Downloaded LIFE (49322 bars)\n", "2025-05-28 00:17:56,509 - INFO - Downloaded LKQ (46753 bars)\n", "2025-05-28 00:17:56,811 - INFO - Downloaded LEN (62898 bars)\n", "2025-05-28 00:17:58,159 - INFO - Downloaded MEE (6942 bars)\n", "2025-05-28 00:17:58,184 - INFO - Downloaded LW (30227 bars)\n", "2025-05-28 00:17:58,713 - INFO - Downloaded LNC (57300 bars)\n", "2025-05-28 00:17:58,922 - INFO - Downloaded KSS (68811 bars)\n", "2025-05-28 00:17:59,913 - INFO - Downloaded KMX (57642 bars)\n", "2025-05-28 00:18:00,158 - INFO - Downloaded LXK (24530 bars)\n", "2025-05-28 00:18:00,429 - INFO - Downloaded LMT (62362 bars)\n", "2025-05-28 00:18:00,502 - INFO - Downloaded MFE (4349 bars)\n", "2025-05-28 00:18:01,146 - INFO - Downloaded LSI (42297 bars)\n", "2025-05-28 00:18:03,554 - INFO - Downloaded MHS (9025 bars)\n", "2025-05-28 00:18:04,241 - INFO - Downloaded LYV (56966 bars)\n", "2025-05-28 00:18:04,521 - INFO - Downloaded LOW (67355 bars)\n", "2025-05-28 00:18:04,755 - INFO - Downloaded MIL (16262 bars)\n", "2025-05-28 00:18:05,061 - INFO - Downloaded LNT (54041 bars)\n", "2025-05-28 00:18:06,122 - INFO - Downloaded MAC (60065 bars)\n", "2025-05-28 00:18:06,701 - INFO - Downloaded LYB (54828 bars)\n", "2025-05-28 00:18:06,891 - INFO - Downloaded MDP (41886 bars)\n", "2025-05-28 00:18:07,262 - INFO - Downloaded LULU (69291 bars)\n", "2025-05-28 00:18:07,642 - INFO - Downloaded MI (11190 bars)\n", "2025-05-28 00:18:08,590 - INFO - Downloaded LRCX (66606 bars)\n", "2025-05-28 00:18:08,659 - INFO - Downloaded LVS (80375 bars)\n", "2025-05-28 00:18:09,790 - INFO - Downloaded MJN (26987 bars)\n", "2025-05-28 00:18:09,873 - INFO - Downloaded MAA (53151 bars)\n", "2025-05-28 00:18:10,298 - INFO - Downloaded MAS (55987 bars)\n", "2025-05-28 00:18:11,565 - INFO - Downloaded MDLZ (52979 bars)\n", "2025-05-28 00:18:13,179 - INFO - Downloaded MCD (76019 bars)\n", "2025-05-28 00:18:13,444 - INFO - Downloaded MOLX (14279 bars)\n", "2025-05-28 00:18:13,914 - INFO - Downloaded MAT (61389 bars)\n", "2025-05-28 00:18:14,120 - INFO - Downloaded LUV (76550 bars)\n", "2025-05-28 00:18:14,775 - INFO - Downloaded MNK (33869 bars)\n", "2025-05-28 00:18:14,979 - INFO - Downloaded META (23867 bars)\n", "2025-05-28 00:18:15,855 - INFO - Downloaded MCHP (62288 bars)\n", "2025-05-28 00:18:16,004 - INFO - Downloaded MCK (55817 bars)\n", "2025-05-28 00:18:16,555 - INFO - Downloaded M (80295 bars)\n", "2025-05-28 00:18:16,826 - INFO - Downloaded MAR (62066 bars)\n", "2025-05-28 00:18:18,609 - INFO - Downloaded MCO (54281 bars)\n", "2025-05-28 00:18:18,792 - INFO - Downloaded MHK (53942 bars)\n", "2025-05-28 00:18:18,881 - INFO - Downloaded MDT (63054 bars)\n", "2025-05-28 00:18:19,943 - INFO - Downloaded MLM (53561 bars)\n", "2025-05-28 00:18:19,956 - INFO - Downloaded MON (37845 bars)\n", "2025-05-28 00:18:20,164 - INFO - Downloaded MKC (55019 bars)\n", "2025-05-28 00:18:20,843 - INFO - Downloaded MET (62486 bars)\n", "2025-05-28 00:18:21,142 - INFO - Downloaded MKTX (52912 bars)\n", "2025-05-28 00:18:22,225 - INFO - Downloaded MMI (43982 bars)\n", "2025-05-28 00:18:22,514 - INFO - Downloaded MA (70132 bars)\n", "2025-05-28 00:18:25,157 - INFO - Downloaded MWV (19534 bars)\n", "2025-05-28 00:18:25,270 - INFO - Downloaded MGM (77285 bars)\n", "2025-05-28 00:18:26,561 - INFO - Downloaded MMC (54415 bars)\n", "2025-05-28 00:18:27,042 - INFO - Downloaded MWW (25077 bars)\n", "2025-05-28 00:18:27,203 - INFO - Downloaded MRNA (46359 bars)\n", "2025-05-28 00:18:27,941 - INFO - Downloaded MTD (51885 bars)\n", "2025-05-28 00:18:29,269 - INFO - Downloaded MNST (53719 bars)\n", "2025-05-28 00:18:29,956 - INFO - Downloaded MOH (53643 bars)\n", "2025-05-28 00:18:32,138 - INFO - Downloaded MPC (59487 bars)\n", "2025-05-28 00:18:32,672 - INFO - Downloaded MOS (69896 bars)\n", "2025-05-28 00:18:33,155 - INFO - Downloaded MSI (51048 bars)\n", "2025-05-28 00:18:33,617 - INFO - Downloaded MTB (54994 bars)\n", "2025-05-28 00:18:34,217 - INFO - Downloaded MSCI (51206 bars)\n", "2025-05-28 00:18:34,761 - INFO - Downloaded MYL (47783 bars)\n", "2025-05-28 00:18:36,014 - INFO - Downloaded NLOK (14380 bars)\n", "2025-05-28 00:18:36,039 - INFO - Downloaded MTCH (39863 bars)\n", "2025-05-28 00:18:36,865 - INFO - Downloaded NAVI (44584 bars)\n", "2025-05-28 00:18:37,186 - INFO - Downloaded MO (81425 bars)\n", "2025-05-28 00:18:37,346 - INFO - Downloaded NBL (41829 bars)\n", "2025-05-28 00:18:37,698 - INFO - Downloaded MRO (78972 bars)\n", "2025-05-28 00:18:38,197 - INFO - Downloaded NOVL (5429 bars)\n", "2025-05-28 00:18:38,294 - INFO - Downloaded MUR (59091 bars)\n", "2025-05-28 00:18:38,416 - INFO - Downloaded MRK (76360 bars)\n", "2025-05-28 00:18:40,124 - INFO - Downloaded MPWR (53780 bars)\n", "2025-05-28 00:18:40,274 - INFO - Downloaded NFX (33347 bars)\n", "2025-05-28 00:18:40,731 - INFO - Downloaded NCLH (62066 bars)\n", "2025-05-28 00:18:41,857 - INFO - Downloaded MS (81842 bars)\n", "2025-05-28 00:18:42,390 - INFO - Downloaded NBR (60476 bars)\n", "2025-05-28 00:18:43,536 - INFO - Downloaded NVLS (16327 bars)\n", "2025-05-28 00:18:43,760 - INFO - Downloaded NE (60605 bars)\n", "2025-05-28 00:18:44,002 - INFO - Downloaded MSFT (106416 bars)\n", "2025-05-28 00:18:44,465 - INFO - Downloaded MXIM (44381 bars)\n", "2025-05-28 00:18:44,851 - INFO - Downloaded MMM (68644 bars)\n", "2025-05-28 00:18:46,406 - INFO - Downloaded NLSN (43246 bars)\n", "2025-05-28 00:18:46,489 - INFO - Downloaded NDSN (53077 bars)\n", "2025-05-28 00:18:46,756 - INFO - Downloaded NSM (29118 bars)\n", "2025-05-28 00:18:48,361 - INFO - Downloaded NYX (15481 bars)\n", "2025-05-28 00:18:48,681 - INFO - Downloaded MU (102517 bars)\n", "2025-05-28 00:18:49,265 - INFO - Downloaded NEE (62591 bars)\n", "2025-05-28 00:18:49,813 - INFO - Downloaded NKTR (58701 bars)\n", "2025-05-28 00:18:50,160 - INFO - Downloaded OGN (15036 bars)\n", "2025-05-28 00:18:51,314 - INFO - Downloaded NI (55296 bars)\n", "2025-05-28 00:18:51,561 - INFO - Downloaded NDAQ (57739 bars)\n", "2025-05-28 00:18:52,353 - INFO - Downloaded NOW (51532 bars)\n", "2025-05-28 00:18:53,093 - INFO - Downloaded NVR (51798 bars)\n", "2025-05-28 00:18:54,605 - INFO - Downloaded NSC (56284 bars)\n", "2025-05-28 00:18:54,962 - INFO - Downloaded NEM (82863 bars)\n", "2025-05-28 00:18:55,032 - INFO - Downloaded PBG (604 bars)\n", "2025-05-28 00:18:55,886 - INFO - Downloaded PARA (18838 bars)\n", "2025-05-28 00:18:56,058 - INFO - Downloaded NOC (56674 bars)\n", "2025-05-28 00:18:57,886 - INFO - Downloaded NKE (79353 bars)\n", "2025-05-28 00:18:57,984 - INFO - Downloaded NWSA (57813 bars)\n", "2025-05-28 00:18:58,252 - INFO - Downloaded NOV (62120 bars)\n", "2025-05-28 00:18:58,798 - INFO - Downloaded OTIS (18215 bars)\n", "2025-05-28 00:18:58,858 - INFO - Downloaded NRG (58304 bars)\n", "2025-05-28 00:18:59,458 - INFO - Downloaded NTRS (55973 bars)\n", "2025-05-28 00:18:59,882 - INFO - Downloaded NTAP (61422 bars)\n", "2025-05-28 00:19:00,312 - INFO - Downloaded NFLX (97704 bars)\n", "2025-05-28 00:19:00,609 - INFO - Downloaded NUE (63643 bars)\n", "2025-05-28 00:19:01,584 - INFO - Downloaded NWL (59751 bars)\n", "2025-05-28 00:19:01,953 - INFO - Downloaded NYT (53973 bars)\n", "2025-05-28 00:19:03,665 - INFO - Downloaded O (68336 bars)\n", "2025-05-28 00:19:03,701 - INFO - Downloaded NWS (54343 bars)\n", "2025-05-28 00:19:04,424 - INFO - Downloaded PEAK (16455 bars)\n", "2025-05-28 00:19:04,969 - INFO - Downloaded PCL (22235 bars)\n", "2025-05-28 00:19:05,505 - INFO - Downloaded ON (48074 bars)\n", "2025-05-28 00:19:06,753 - INFO - Downloaded PETM (19198 bars)\n", "2025-05-28 00:19:06,973 - INFO - Downloaded PGN (14402 bars)\n", "2025-05-28 00:19:07,515 - INFO - Downloaded ORLY (55920 bars)\n", "2025-05-28 00:19:08,080 - INFO - Downloaded NXPI (62213 bars)\n", "2025-05-28 00:19:08,635 - INFO - Downloaded PBCT (47609 bars)\n", "2025-05-28 00:19:08,817 - INFO - Downloaded OI (54357 bars)\n", "2025-05-28 00:19:09,117 - INFO - Downloaded OKE (60504 bars)\n", "2025-05-28 00:19:10,325 - INFO - Downloaded OMC (55358 bars)\n", "2025-05-28 00:19:10,497 - INFO - Downloaded PCP (21664 bars)\n", "2025-05-28 00:19:11,862 - INFO - Downloaded ORCL (78311 bars)\n", "2025-05-28 00:19:12,205 - INFO - Downloaded PAYC (40214 bars)\n", "2025-05-28 00:19:13,489 - INFO - Downloaded PAYX (58822 bars)\n", "2025-05-28 00:19:14,322 - INFO - Downloaded PANW (56788 bars)\n", "2025-05-28 00:19:16,169 - INFO - Downloaded PCG (64896 bars)\n", "2025-05-28 00:19:17,238 - INFO - Downloaded PBI (58915 bars)\n", "2025-05-28 00:19:17,301 - INFO - Downloaded POM (22737 bars)\n", "2025-05-28 00:19:17,743 - INFO - Downloaded ODFL (54254 bars)\n", "2025-05-28 00:19:17,862 - INFO - Downloaded PLTR (34093 bars)\n", "2025-05-28 00:19:18,694 - INFO - Downloaded PEP (68381 bars)\n", "2025-05-28 00:19:18,818 - INFO - Downloaded ODP (57887 bars)\n", "2025-05-28 00:19:19,224 - INFO - Downloaded NVDA (97576 bars)\n", "2025-05-28 00:19:19,553 - INFO - Downloaded PEG (55529 bars)\n", "2025-05-28 00:19:19,805 - INFO - Downloaded PTV (3295 bars)\n", "2025-05-28 00:19:19,942 - INFO - Downloaded PCAR (58954 bars)\n", "2025-05-28 00:19:20,795 - INFO - Downloaded PENN (64613 bars)\n", "2025-05-28 00:19:21,194 - INFO - Downloaded PLL (43498 bars)\n", "2025-05-28 00:19:21,287 - INFO - Downloaded OXY (81903 bars)\n", "2025-05-28 00:19:21,654 - INFO - Downloaded PKI (47798 bars)\n", "2025-05-28 00:19:21,867 - INFO - Downloaded PDCO (54594 bars)\n", "2025-05-28 00:19:23,612 - INFO - Downloaded PKG (53569 bars)\n", "2025-05-28 00:19:23,972 - INFO - Downloaded PHM (62762 bars)\n", "2025-05-28 00:19:24,535 - INFO - Downloaded PFG (55071 bars)\n", "2025-05-28 00:19:26,089 - INFO - Downloaded PH (54566 bars)\n", "2025-05-28 00:19:26,353 - INFO - Downloaded PFE (94015 bars)\n", "2025-05-28 00:19:27,581 - INFO - Downloaded PNR (53740 bars)\n", "2025-05-28 00:19:27,657 - INFO - Downloaded POOL (53095 bars)\n", "2025-05-28 00:19:28,295 - INFO - Downloaded PG (75468 bars)\n", "2025-05-28 00:19:28,574 - INFO - Downloaded PGR (56421 bars)\n", "2025-05-28 00:19:30,550 - INFO - Downloaded PLD (56929 bars)\n", "2025-05-28 00:19:31,272 - INFO - Downloaded PNW (53900 bars)\n", "2025-05-28 00:19:32,502 - INFO - Downloaded PPG (55414 bars)\n", "2025-05-28 00:19:32,870 - INFO - Downloaded PM (65457 bars)\n", "2025-05-28 00:19:32,940 - INFO - Downloaded PX (43188 bars)\n", "2025-05-28 00:19:33,670 - INFO - Downloaded QRVO (42424 bars)\n", "2025-05-28 00:19:34,378 - INFO - Downloaded PTC (43103 bars)\n", "2025-05-28 00:19:34,780 - INFO - Downloaded PRU (60792 bars)\n", "2025-05-28 00:19:35,408 - INFO - Downloaded Q (21825 bars)\n", "2025-05-28 00:19:36,308 - INFO - Downloaded PNC (59167 bars)\n", "2025-05-28 00:19:36,333 - INFO - Downloaded PSX (55824 bars)\n", "2025-05-28 00:19:36,780 - INFO - Downloaded RAI (29107 bars)\n", "2025-05-28 00:19:37,035 - INFO - Downloaded QEP (41005 bars)\n", "2025-05-28 00:19:37,139 - WARNING - Skipping 'RSHCQ': no 'open'/'close' columns\n", "2025-05-28 00:19:38,795 - INFO - Downloaded PYPL (63007 bars)\n", "2025-05-28 00:19:38,916 - INFO - Downloaded QLGC (23937 bars)\n", "2025-05-28 00:19:38,932 - INFO - Downloaded PSA (54637 bars)\n", "2025-05-28 00:19:39,669 - INFO - Downloaded RHT (37031 bars)\n", "2025-05-28 00:19:40,253 - INFO - Downloaded PXD (59121 bars)\n", "2025-05-28 00:19:40,451 - INFO - Downloaded RX (604 bars)\n", "2025-05-28 00:19:40,559 - INFO - Downloaded PVH (56053 bars)\n", "2025-05-28 00:19:40,958 - INFO - Downloaded PRGO (63272 bars)\n", "2025-05-28 00:19:41,719 - INFO - Downloaded RVTY (5933 bars)\n", "2025-05-28 00:19:42,331 - INFO - Downloaded PPL (60214 bars)\n", "2025-05-28 00:19:43,925 - INFO - Downloaded REG (53708 bars)\n", "2025-05-28 00:19:44,098 - INFO - Downloaded PWR (55180 bars)\n", "2025-05-28 00:19:44,156 - INFO - Downloaded QCOM (88831 bars)\n", "2025-05-28 00:19:45,012 - INFO - Downloaded RTX (25524 bars)\n", "2025-05-28 00:19:45,375 - INFO - Downloaded R (53382 bars)\n", "2025-05-28 00:19:46,657 - INFO - Downloaded RE (47272 bars)\n", "2025-05-28 00:19:46,666 - INFO - Downloaded RDC (33533 bars)\n", "2025-05-28 00:19:48,132 - INFO - Downloaded PODD (53522 bars)\n", "2025-05-28 00:19:49,382 - INFO - Downloaded REGN (59371 bars)\n", "2025-05-28 00:19:49,610 - INFO - Downloaded RHI (53857 bars)\n", "2025-05-28 00:19:50,742 - INFO - Downloaded RF (67142 bars)\n", "2025-05-28 00:19:51,038 - INFO - Downloaded ROP (53338 bars)\n", "2025-05-28 00:19:51,214 - INFO - Downloaded SCG (32139 bars)\n", "2025-05-28 00:19:51,363 - INFO - Downloaded SII (17835 bars)\n", "2025-05-28 00:19:52,418 - INFO - Downloaded RCL (76570 bars)\n", "2025-05-28 00:19:53,259 - INFO - Downloaded RTN (39929 bars)\n", "2025-05-28 00:19:53,477 - INFO - Downloaded SIAL (21626 bars)\n", "2025-05-28 00:19:53,956 - INFO - Downloaded RL (56095 bars)\n", "2025-05-28 00:19:54,084 - INFO - Downloaded RRD (45786 bars)\n", "2025-05-28 00:19:55,104 - INFO - Downloaded RJF (53342 bars)\n", "2025-05-28 00:19:56,767 - INFO - Downloaded SOLV (3146 bars)\n", "2025-05-28 00:19:56,770 - INFO - Downloaded RIG (93059 bars)\n", "2025-05-28 00:19:57,301 - INFO - Downloaded RRC (63273 bars)\n", "2025-05-28 00:19:58,026 - INFO - Downloaded ROST (60104 bars)\n", "2025-05-28 00:19:59,358 - INFO - Downloaded ROK (54390 bars)\n", "2025-05-28 00:20:00,304 - INFO - Downloaded SEDG (44676 bars)\n", "2025-05-28 00:20:00,500 - INFO - Downloaded RMD (55545 bars)\n", "2025-05-28 00:20:00,613 - INFO - Downloaded SBAC (54891 bars)\n", "2025-05-28 00:20:00,614 - INFO - Downloaded SBNY (52651 bars)\n", "2025-05-28 00:20:00,667 - INFO - Downloaded SHLD (38714 bars)\n", "2025-05-28 00:20:01,416 - INFO - Downloaded ROL (53034 bars)\n", "2025-05-28 00:20:02,908 - INFO - Downloaded SIG (57439 bars)\n", "2025-05-28 00:20:03,094 - INFO - Downloaded SNI (28828 bars)\n", "2025-05-28 00:20:03,241 - INFO - Downloaded S (76742 bars)\n", "2025-05-28 00:20:03,444 - WARNING - Skipping 'SUNEQ': no 'open'/'close' columns\n", "2025-05-28 00:20:03,694 - INFO - Downloaded SNDK (30812 bars)\n", "2025-05-28 00:20:03,895 - INFO - Downloaded SE (69761 bars)\n", "2025-05-28 00:20:04,200 - INFO - Downloaded SCHW (65219 bars)\n", "2025-05-28 00:20:04,784 - INFO - Downloaded SW (2672 bars)\n", "2025-05-28 00:20:07,018 - INFO - Downloaded SPLS (30992 bars)\n", "2025-05-28 00:20:07,367 - INFO - Downloaded RSG (54555 bars)\n", "2025-05-28 00:20:07,452 - INFO - Downloaded SHW (54796 bars)\n", "2025-05-28 00:20:07,591 - INFO - Downloaded SEE (54177 bars)\n", "2025-05-28 00:20:08,487 - INFO - Downloaded SJM (54749 bars)\n", "2025-05-28 00:20:09,310 - INFO - Downloaded STJ (25744 bars)\n", "2025-05-28 00:20:09,400 - INFO - Downloaded SIVB (48062 bars)\n", "2025-05-28 00:20:10,246 - INFO - Downloaded SMCI (55725 bars)\n", "2025-05-28 00:20:12,106 - INFO - Downloaded SBUX (84670 bars)\n", "2025-05-28 00:20:13,461 - INFO - Downloaded STR (33024 bars)\n", "2025-05-28 00:20:13,670 - INFO - Downloaded SWY (19107 bars)\n", "2025-05-28 00:20:15,165 - INFO - Downloaded SNPS (56353 bars)\n", "2025-05-28 00:20:16,194 - INFO - Downloaded SNA (53042 bars)\n", "2025-05-28 00:20:16,495 - INFO - Downloaded SVU (33882 bars)\n", "2025-05-28 00:20:17,043 - INFO - Downloaded SO (62395 bars)\n", "2025-05-28 00:20:17,153 - INFO - Downloaded STE (52559 bars)\n", "2025-05-28 00:20:18,138 - INFO - Downloaded SLB (83635 bars)\n", "2025-05-28 00:20:18,384 - INFO - Downloaded SLM (55701 bars)\n", "2025-05-28 00:20:19,697 - INFO - Downloaded STT (57435 bars)\n", "2025-05-28 00:20:20,173 - INFO - Downloaded SYMC (40617 bars)\n", "2025-05-28 00:20:20,261 - INFO - Downloaded SUN (48883 bars)\n", "2025-05-28 00:20:21,639 - INFO - Downloaded SPGI (32255 bars)\n", "2025-05-28 00:20:22,771 - INFO - Downloaded SRE (54764 bars)\n", "2025-05-28 00:20:23,040 - INFO - Downloaded TEG (19318 bars)\n", "2025-05-28 00:20:23,422 - INFO - Downloaded STZ (58067 bars)\n", "2025-05-28 00:20:24,433 - INFO - Downloaded TE (23344 bars)\n", "2025-05-28 00:20:24,530 - INFO - Downloaded SLG (56301 bars)\n", "2025-05-28 00:20:24,670 - INFO - Downloaded SPG (62433 bars)\n", "2025-05-28 00:20:24,692 - INFO - Downloaded STLD (59023 bars)\n", "2025-05-28 00:20:24,988 - INFO - Downloaded TIE (12149 bars)\n", "2025-05-28 00:20:25,195 - INFO - Downloaded TDG (52990 bars)\n", "2025-05-28 00:20:25,578 - INFO - Downloaded SYF (42759 bars)\n", "2025-05-28 00:20:26,577 - INFO - Downloaded SRCL (53987 bars)\n", "2025-05-28 00:20:27,861 - INFO - Downloaded TDY (52556 bars)\n", "2025-05-28 00:20:27,956 - INFO - Downloaded SYK (56110 bars)\n", "2025-05-28 00:20:28,079 - INFO - Downloaded SWK (56692 bars)\n", "2025-05-28 00:20:28,456 - INFO - Downloaded STI (44451 bars)\n", "2025-05-28 00:20:28,671 - INFO - Downloaded TLAB (15426 bars)\n", "2025-05-28 00:20:29,324 - INFO - Downloaded STX (67266 bars)\n", "2025-05-28 00:20:29,856 - INFO - Downloaded TFC (26846 bars)\n", "2025-05-28 00:20:30,107 - INFO - Downloaded SWKS (70921 bars)\n", "2025-05-28 00:20:30,722 - INFO - Downloaded SYY (57435 bars)\n", "2025-05-28 00:20:32,412 - INFO - Downloaded TDC (55479 bars)\n", "2025-05-28 00:20:32,677 - INFO - Downloaded TFX (52242 bars)\n", "2025-05-28 00:20:32,734 - INFO - Downloaded TGNA (35045 bars)\n", "2025-05-28 00:20:33,777 - INFO - Downloaded TECH (52632 bars)\n", "2025-05-28 00:20:34,224 - INFO - Downloaded TPL (40941 bars)\n", "2025-05-28 00:20:34,720 - INFO - Downloaded TAP (56828 bars)\n", "2025-05-28 00:20:35,423 - INFO - Downloaded TMK (33729 bars)\n", "2025-05-28 00:20:35,483 - INFO - Downloaded TEL (54198 bars)\n", "2025-05-28 00:20:35,565 - INFO - Downloaded TPR (30524 bars)\n", "2025-05-28 00:20:35,953 - INFO - Downloaded TT (17849 bars)\n", "2025-05-28 00:20:36,386 - INFO - Downloaded TIF (42264 bars)\n", "2025-05-28 00:20:36,907 - INFO - Downloaded T (97735 bars)\n", "2025-05-28 00:20:38,577 - INFO - Downloaded SWN (72806 bars)\n", "2025-05-28 00:20:39,419 - INFO - Downloaded TMUS (51700 bars)\n", "2025-05-28 00:20:39,715 - INFO - Downloaded TWC (23622 bars)\n", "2025-05-28 00:20:39,754 - INFO - Downloaded TSS (34603 bars)\n", "2025-05-28 00:20:40,300 - INFO - Downloaded TER (58751 bars)\n", "2025-05-28 00:20:42,041 - INFO - Downloaded TGT (72473 bars)\n", "2025-05-28 00:20:43,323 - INFO - Downloaded TRV (58342 bars)\n", "2025-05-28 00:20:43,658 - INFO - Downloaded TWX (33622 bars)\n", "2025-05-28 00:20:45,218 - INFO - Downloaded TSN (61235 bars)\n", "2025-05-28 00:20:46,035 - INFO - Downloaded THC (58015 bars)\n", "2025-05-28 00:20:47,260 - INFO - Downloaded TJX (62427 bars)\n", "2025-05-28 00:20:47,551 - INFO - Downloaded TRIP (56500 bars)\n", "2025-05-28 00:20:48,360 - INFO - Downloaded UAA (40713 bars)\n", "2025-05-28 00:20:48,445 - INFO - Downloaded UBER (42099 bars)\n", "2025-05-28 00:20:49,118 - INFO - Downloaded TRMB (54801 bars)\n", "2025-05-28 00:20:51,048 - INFO - Downloaded TMO (56704 bars)\n", "2025-05-28 00:20:51,757 - INFO - Downloaded VIAC (14281 bars)\n", "2025-05-28 00:20:52,885 - INFO - Downloaded TRGP (51400 bars)\n", "2025-05-28 00:20:53,210 - INFO - Downloaded TROW (57485 bars)\n", "2025-05-28 00:20:53,405 - INFO - Downloaded UDR (54267 bars)\n", "2025-05-28 00:20:53,512 - INFO - Downloaded TXT (55694 bars)\n", "2025-05-28 00:20:54,112 - INFO - Downloaded UNM (55892 bars)\n", "2025-05-28 00:20:54,714 - INFO - Downloaded TSCO (57264 bars)\n", "2025-05-28 00:20:56,632 - INFO - Downloaded TYL (52191 bars)\n", "2025-05-28 00:20:56,746 - INFO - Downloaded ULTA (60141 bars)\n", "2025-05-28 00:20:57,247 - INFO - Downloaded VIAB (32071 bars)\n", "2025-05-28 00:20:57,377 - INFO - Downloaded URI (58452 bars)\n", "2025-05-28 00:20:57,407 - INFO - Downloaded UTX (42802 bars)\n", "2025-05-28 00:20:57,982 - INFO - Downloaded VLTO (4780 bars)\n", "2025-05-28 00:20:58,394 - INFO - Downloaded TWTR (64937 bars)\n", "2025-05-28 00:20:58,798 - INFO - Downloaded TTWO (60875 bars)\n", "2025-05-28 00:20:59,059 - INFO - Downloaded VAR (40141 bars)\n", "2025-05-28 00:20:59,061 - INFO - Downloaded UNH (66382 bars)\n", "2025-05-28 00:20:59,294 - INFO - Downloaded VNT (15418 bars)\n", "2025-05-28 00:20:59,775 - INFO - Downloaded UAL (75459 bars)\n", "2025-05-28 00:21:01,695 - INFO - Downloaded UHS (53489 bars)\n", "2025-05-28 00:21:01,714 - INFO - Downloaded UPS (66652 bars)\n", "2025-05-28 00:21:02,330 - INFO - Downloaded VICI (29668 bars)\n", "2025-05-28 00:21:02,935 - INFO - Downloaded UA (69249 bars)\n", "2025-05-28 00:21:03,036 - INFO - Downloaded VIAV (35031 bars)\n", "2025-05-28 00:21:03,651 - INFO - Downloaded TXN (67714 bars)\n", "2025-05-28 00:21:05,380 - INFO - Downloaded VTRS (20436 bars)\n", "2025-05-28 00:21:05,907 - INFO - Downloaded WBD (23698 bars)\n", "2025-05-28 00:21:06,102 - INFO - Downloaded URBN (59101 bars)\n", "2025-05-28 00:21:06,439 - INFO - Downloaded VST (32058 bars)\n", "2025-05-28 00:21:07,529 - INFO - Downloaded WELL (26259 bars)\n", "2025-05-28 00:21:08,132 - INFO - Downloaded UNP (60490 bars)\n", "2025-05-28 00:21:08,153 - INFO - Downloaded VMC (54662 bars)\n", "2025-05-28 00:21:08,722 - INFO - Downloaded V (81378 bars)\n", "2025-05-28 00:21:09,455 - INFO - Downloaded TSLA (100437 bars)\n", "2025-05-28 00:21:10,850 - INFO - Downloaded VRSK (54800 bars)\n", "2025-05-28 00:21:13,070 - INFO - Downloaded VNO (56028 bars)\n", "2025-05-28 00:21:13,326 - INFO - Downloaded WIN (38325 bars)\n", "2025-05-28 00:21:13,468 - INFO - Downloaded VFC (60238 bars)\n", "2025-05-28 00:21:13,550 - INFO - Downloaded USB (65843 bars)\n", "2025-05-28 00:21:13,908 - INFO - Downloaded WAT (53267 bars)\n", "2025-05-28 00:21:14,379 - INFO - Downloaded WCG (35431 bars)\n", "2025-05-28 00:21:15,892 - INFO - Downloaded VRTX (60570 bars)\n", "2025-05-28 00:21:16,229 - INFO - Downloaded WAB (53610 bars)\n", "2025-05-28 00:21:16,680 - INFO - Downloaded VTR (56717 bars)\n", "2025-05-28 00:21:17,463 - INFO - Downloaded WFM (29030 bars)\n", "2025-05-28 00:21:17,472 - INFO - Downloaded WYND (10080 bars)\n", "2025-05-28 00:21:18,399 - INFO - Downloaded VLO (72388 bars)\n", "2025-05-28 00:21:18,641 - INFO - Downloaded XTO (2014 bars)\n", "2025-05-28 00:21:18,846 - INFO - Downloaded WDAY (50982 bars)\n", "2025-05-28 00:21:19,507 - INFO - Downloaded WBA (53872 bars)\n", "2025-05-28 00:21:20,453 - INFO - Downloaded VRSN (55858 bars)\n", "2025-05-28 00:21:20,629 - INFO - Downloaded WLTW (22028 bars)\n", "2025-05-28 00:21:21,034 - INFO - Downloaded WHR (57470 bars)\n", "2025-05-28 00:21:21,426 - INFO - Downloaded WRK (35762 bars)\n", "2025-05-28 00:21:23,389 - INFO - Downloaded WDC (72255 bars)\n", "2025-05-28 00:21:23,625 - INFO - Downloaded WPX (34797 bars)\n", "2025-05-28 00:21:23,783 - INFO - Downloaded WST (52425 bars)\n", "2025-05-28 00:21:24,392 - INFO - Downloaded WEC (54331 bars)\n", "2025-05-28 00:21:24,709 - INFO - Downloaded VZ (89686 bars)\n", "2025-05-28 00:21:25,676 - INFO - Downloaded WM (58769 bars)\n", "2025-05-28 00:21:26,276 - INFO - Downloaded WFC (89836 bars)\n", "2025-05-28 00:21:27,138 - INFO - Downloaded XL (43395 bars)\n", "2025-05-28 00:21:28,791 - INFO - Downloaded WTW (46938 bars)\n", "2025-05-28 00:21:30,249 - INFO - Downloaded ZBH (34987 bars)\n", "2025-05-28 00:21:30,737 - INFO - Downloaded XEC (42793 bars)\n", "2025-05-28 00:21:30,839 - INFO - Downloaded WY (60764 bars)\n", "2025-05-28 00:21:31,592 - INFO - Downloaded WMT (82329 bars)\n", "2025-05-28 00:21:31,708 - INFO - Downloaded WMB (62402 bars)\n", "2025-05-28 00:21:31,857 - INFO - Downloaded WU (58721 bars)\n", "2025-05-28 00:21:32,104 - INFO - Downloaded WRB (53137 bars)\n", "2025-05-28 00:21:32,357 - INFO - Downloaded XYL (47840 bars)\n", "2025-05-28 00:21:33,013 - INFO - Downloaded XLNX (52639 bars)\n", "2025-05-28 00:21:33,356 - INFO - Downloaded WYNN (77580 bars)\n", "2025-05-28 00:21:34,136 - INFO - Downloaded XEL (56872 bars)\n", "2025-05-28 00:21:34,343 - INFO - Downloaded ZION (58932 bars)\n", "2025-05-28 00:21:34,637 - INFO - Downloaded ZTS (45289 bars)\n", "2025-05-28 00:21:34,940 - INFO - Downloaded ZBRA (53574 bars)\n", "2025-05-28 00:21:35,679 - INFO - Downloaded X (91012 bars)\n", "2025-05-28 00:21:36,264 - INFO - Downloaded XRAY (56218 bars)\n", "2025-05-28 00:21:37,730 - INFO - Downloaded YUM (59716 bars)\n", "2025-05-28 00:21:37,962 - INFO - Downloaded XOM (94661 bars)\n", "2025-05-28 00:21:38,248 - INFO - Downloaded XRX (61414 bars)\n"]}], "source": ["if DEBUG:\n", "    logging.basicConfig(level=logging.INFO, format=\"%(message)s\")\n", "\n", "def at(day, hhmm):\n", "    \"\"\"Return tz-aware timestamp for hh:mm America/New_York on `day`.\"\"\"\n", "    return EST.localize(datetime.combine(day, time(*hhmm)))\n", "\n", "def bar_open(df, ts):\n", "    \"\"\"Open price of bar whose label equals `ts`.\"\"\"\n", "    try:    return df.at[ts, \"open\"]\n", "    except KeyError: return np.nan\n", "\n", "def bar_close(df, ts):\n", "    \"\"\"Close price of bar whose label equals `ts`.\"\"\"\n", "    try:    return df.at[ts, \"close\"]\n", "    except KeyError: return np.nan\n", "\n", "# ---- universe --------------------------------------------------------------\n", "sp500 = SP500Constituents()\n", "all_dates = pd.bdate_range(START_DT, END_DT).date\n", "universe = set()\n", "for d in all_dates:\n", "    universe |= set(sp500.constituents_for(d))\n", "universe = sorted(universe)\n", "\n", "# ---- data download ---------------------------------------------------------\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "from tqdm.notebook import tqdm\n", "\n", "def fetch_bars(sym):\n", "    df = market_data.gather_historical_data(\n", "        sym,\n", "        START_DT - <PERSON><PERSON><PERSON>(days=5),\n", "        END_DT,\n", "        interval=INTERVAL_SEC\n", "    )\n", "    return sym, df[[\"open\", \"close\"]].sort_index()\n", "\n", "bars = {}\n", "with ThreadPoolExecutor(max_workers=32) as executor:\n", "    futures = {executor.submit(fetch_bars, sym): sym for sym in universe}\n", "    \n", "    for fut in tqdm(as_completed(futures), total=len(futures), desc=\"Downloading\"):\n", "        sym = futures[fut]\n", "        try:\n", "            sym, df = fut.result()\n", "        except KeyError:\n", "            logging.warning(f\"Skipping {sym!r}: no 'open'/'close' columns\")\n", "            continue\n", "        except Exception as e:\n", "            logging.warning(f\"Skipping {sym!r}: {e!r}\")\n", "            continue\n", "\n", "        bars[sym] = df\n", "        logging.info(f\"Downloaded {sym} ({len(df)} bars)\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Annualised mean return   : 2.58%\n", "Annualised volatility    : 1.27%\n", "Cumulative return factor : 1.46\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# ---- trading calendar ------------------------------------------------------\n", "trade_days = pd.to_datetime(\n", "    sorted({ts.normalize()        # tz-aware midnight of each session\n", "            for df in bars.values()\n", "            for ts in df.index    # all 30-min bar labels\n", "            if ts.time() >= time(15, 30)})  # ensure session has the 15:30 bar\n", ").date                                 # convert to plain date for at()\n", "\n", "records = []\n", "\n", "# start from index 1 so we always have a “previous session”\n", "for idx in range(1, len(trade_days)):\n", "    day, prev_day = trade_days[idx], trade_days[idx - 1]\n", "\n", "    # timestamps we need, given 30-min bars labelled by bar-start\n", "    ts_prev1530 = at(prev_day, (15, 30))   # prev session 15:30 → 16:00 close\n", "    ts_1430     = at(day,      (14, 30))   # this session 14:30 → 15:00 close\n", "    ts_1530     = at(day,      (15, 30))   # this session 15:30 open\n", "\n", "    rod3, lh = {}, {}\n", "\n", "    # ---------- collect prices per symbol -----------------------------------\n", "    for sym, df in bars.items():\n", "        p_close_prev = bar_close(df, ts_prev1530)  # ≈ 16:00 prev session\n", "        p_1500       = bar_close (df, ts_1430)      # 14:30 -> 15:00 close\n", "        p_1530       = bar_open (df, ts_1530)      # 15:30 open\n", "        p_1600       = bar_close(df, ts_1530)      # 15:59:59 close\n", "\n", "        if np.nan in (p_close_prev, p_1500, p_1530, p_1600):\n", "            continue\n", "\n", "        rod3[sym] = (p_1500 / p_close_prev) - 1.0\n", "        lh[sym]   = (p_1600 / p_1530)       - 1.0\n", "\n", "    if len(rod3) < N_QUANTILES * 2:\n", "        if DEBUG: logging.info(f\"{day}: skipped (only {len(rod3)} stocks)\")\n", "        continue\n", "\n", "    ranked   = pd.Series(rod3).sort_values()\n", "    qsize    = len(ranked) // N_QUANTILES\n", "    long_syms, short_syms = ranked.iloc[:qsize].index, ranked.iloc[-qsize:].index\n", "\n", "    w_long  =  (GROSS_LEVERAGE / 2) / len(long_syms)\n", "    w_short = -(GROSS_LEVERAGE / 2) / len(short_syms)\n", "\n", "    pnl_raw = (sum(w_long  * lh[s] for s in long_syms) +\n", "               sum(w_short * lh[s] for s in short_syms))\n", "\n", "    tc  = 2 * GROSS_LEVERAGE * (TC_BPS_PER_SIDE / 1e4)   # 5 bps each side\n", "    pnl = pnl_raw - tc\n", "\n", "    if DEBUG:\n", "        logging.info(f\"{day}: prev={prev_day}  raw={pnl_raw:+.4%}  \"\n", "                     f\"tc={tc:+.4%}  net={pnl:+.4%}\")\n", "\n", "    records.append({\"date\": day, \"pnl\": pnl})\n", "\n", "\n", "# ---- results & plot --------------------------------------------------------\n", "results = pd.DataFrame(records).set_index(\"date\")\n", "results[\"cum_ret\"] = (1 + results[\"pnl\"]).cumprod()\n", "\n", "print(f\"Annualised mean return   : {results['pnl'].mean()*252*100:.2f}%\")\n", "print(f\"Annualised volatility    : {results['pnl'].std()*np.sqrt(252)*100:.2f}%\")\n", "print(f\"Cumulative return factor : {results['cum_ret'].iloc[-1]:.2f}\")\n", "\n", "results[\"cum_ret\"].plot(title=\"Equal-Weighted Intraday Reversal (net 5 bps/side)\",\n", "                        linewidth=2)\n", "plt.ylabel(\"Growth of $1\")\n", "plt.show()\n", "\n", "# For SP500 2010-2025\n", "# Annualised mean return   : 2.58%\n", "# Annualised volatility    : 1.27%\n", "# Cumulative return factor : 1.46"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}