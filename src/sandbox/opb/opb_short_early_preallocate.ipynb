{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(start_date=datetime(2022, 1, 1))\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"Fetches daily data using the configured daily_market_data instance.\"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        print(\"Exception\", e)\n", "        return None\n", "\n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from tickers.ticker_helpers import TickerInfoStore\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2022, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 4, 4))\n", "\n", "ticker_info = TickerInfoStore()\n", "ticker_df = ticker_info.load_ticker_data(start_dt)\n", "if ticker_df.empty:\n", "    ticker_df = ticker_info.fetch_ticker_data(start_dt)\n", "\n", "min_market_cap = 1_000_000  # 1M\n", "max_market_cap = 500_000_000  # 500M\n", "\n", "# # Fetch all delisted tickers up to end_dt\n", "# delisted_ticker_df = ticker_info.fetch_ticker_data(end_dt, active=False)\n", "\n", "# # Filter to only include tickers delisted between start_dt and end_dt\n", "# delisted_ticker_df['delisted_date'] = pd.to_datetime(delisted_ticker_df['delisted_utc']).dt.date\n", "\n", "# # Keep only tickers delisted within the date range\n", "# delisted_in_range_df = delisted_ticker_df[\n", "#     (delisted_ticker_df['delisted_date'] >= start_dt.date()) & \n", "#     (delisted_ticker_df['delisted_date'] <= end_dt.date())\n", "# ]\n", "\n", "market_cap_filter = (\n", "            ticker_df[\"market_cap\"].notna() &\n", "            (ticker_df[\"market_cap\"] >= min_market_cap) &\n", "            (ticker_df[\"market_cap\"] <= max_market_cap)\n", "        )\n", "\n", "# delisted_tickers = set(delisted_in_range_df[\"ticker\"])\n", "active_small_cap_tickers = set(ticker_df.loc[market_cap_filter, \"ticker\"])\n", "small_cap_tickers = active_small_cap_tickers #.union(delisted_tickers)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0671655b76124c318d8321fd40176c49", "version_major": 2, "version_minor": 0}, "text/plain": ["Finding gap ups:   0%|          | 0/2480 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from datetime import timedelta, datetime, time\n", "from tqdm.notebook import tqdm\n", "import pandas as pd\n", "\n", "def find_gap_ups(\n", "    tickers,\n", "    start_date,\n", "    end_date,\n", "    min_gap_percentage=50,\n", "    spike_lookback_days=30,\n", "    spike_threshold_pct=50\n", "):\n", "    \"\"\"\n", "    Finds all gap‑up events > min_gap_percentage and flags\n", "    if there was any intraday spike > spike_threshold_pct\n", "    in the prior spike_lookback_days.\n", "    \"\"\"\n", "    events = []\n", "\n", "    for ticker in tqdm(tickers, desc=\"Finding gap ups\"):\n", "        df = get_daily_data(ticker, start_date, end_date)\n", "        if df is None or len(df) < 2:\n", "            continue\n", "\n", "        # 1) Compute prior‐day fields\n", "        df['prev_close']         = df['close'].shift(1)\n", "        df['prev_volume']        = df['volume'].shift(1)\n", "        df['prev_dollar_volume'] = df['prev_close'] * df['prev_volume']\n", "\n", "        # 2) Gap % filter\n", "        df['gap_up_pct'] = (df['open'] - df['prev_close']) / df['prev_close'] * 100\n", "        gap_days = df[df['gap_up_pct'] > min_gap_percentage].copy()\n", "        if gap_days.empty:\n", "            continue\n", "\n", "        # 3) For each gap day, look back for intraday spike\n", "        for event_date, row in gap_days.iterrows():\n", "            lb_start = event_date - timed<PERSON>ta(days=spike_lookback_days)\n", "            lb_end   = event_date - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "            hist = get_daily_data(\n", "                ticker,\n", "                eastern_tz.localize(datetime.combine(lb_start.date(), time())),\n", "                eastern_tz.localize(datetime.combine(lb_end.date(),   time()))\n", "            )\n", "\n", "            has_spike = False\n", "            if hist is not None and not hist.empty:\n", "                intraday_pct = (hist['high'] - hist['low']) / hist['low'] * 100\n", "                has_spike = intraday_pct.gt(spike_threshold_pct).any()\n", "\n", "            events.append({\n", "                'date':                event_date,\n", "                'stock':               ticker,\n", "                'gap_up_percentage':   row['gap_up_pct'],\n", "                'prev_close':          row['prev_close'],\n", "                'prev_volume':         row['prev_volume'],\n", "                'prev_dollar_volume':  row['prev_dollar_volume'],\n", "                'has_intraday_spike':  has_spike\n", "            })\n", "\n", "    return (\n", "        pd.<PERSON><PERSON>rame(events)\n", "          .sort_values('gap_up_percentage', ascending=False)\n", "          .reset_index(drop=True)\n", "    )\n", "\n", "\n", "# Run the analysis on the small cap tickers\n", "gap_up_df = find_gap_ups(small_cap_tickers, start_dt, end_dt, min_gap_percentage=50)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing found gap events...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f4261f2d60354720b96a757142f673b4", "version_major": 2, "version_minor": 0}, "text/plain": ["Processing Gap Events:   0%|          | 0/1595 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Retrieved minute data for 1593 out of 1595 gap events\n"]}], "source": ["def get_minute_data_for_gap_events(gap_events_df, pre_market_start_hour=4, post_market_end_hour=20):\n", "    \"\"\"\n", "    Retrieve per-minute data for each gap up event from pre-market (4:00 AM EST) \n", "    to post-market (8:00 PM EST).\n", "    \n", "    Args:\n", "        gap_events_df: DataFrame containing gap up events with date and stock columns\n", "        pre_market_start_hour: Hour to start collecting data (in EST, 24-hour format)\n", "        post_market_end_hour: Hour to end collecting data (in EST, 24-hour format)\n", "        \n", "    Returns:\n", "        Dictionary with keys as (date, ticker) tuples and values as DataFrames of minute data\n", "    \"\"\"\n", "    minute_data_dict = {}\n", "    \n", "    print(\"Processing found gap events...\")\n", "    for _, row in tqdm(gap_events_df.iterrows(), total=gap_events_df.shape[0], desc=\"Processing Gap Events\"):\n", "        ticker = row['stock']\n", "        event_date = row['date']\n", "        \n", "        # Create start and end datetime for the data request\n", "        # Start at pre-market (4:00 AM EST)\n", "        start_datetime = event_date.replace(hour=pre_market_start_hour, minute=0, second=0)\n", "        \n", "        # End at post-market (8:00 PM EST)\n", "        end_datetime = event_date.replace(hour=post_market_end_hour, minute=0, second=0)\n", "        \n", "        try:\n", "            # print(f\"Fetching minute data for {ticker} on {event_date.date()}\")\n", "            \n", "            # Get per-minute data using the existing function\n", "            minute_data = get_per_minute_data(ticker, start_datetime, end_datetime)\n", "            \n", "            if minute_data is not None and len(minute_data) > 0:\n", "                # Store the data with a tuple key for easy retrieval\n", "                key = (event_date.date(), ticker)\n", "                minute_data_dict[key] = minute_data\n", "                # print(f\"  Retrieved {len(minute_data)} minutes of data\")\n", "                \n", "        except Exception as e:\n", "            print(f\"  Error retrieving minute data for {ticker} on {event_date.date()}: {e}\")\n", "    \n", "    print(f\"Retrieved minute data for {len(minute_data_dict)} out of {len(gap_events_df)} gap events\")\n", "    return minute_data_dict\n", "\n", "# Retrieve minute data for the top 50 gap up events (to avoid fetching too much data at once)\n", "# top_gap_events = gap_up_df.head(50)\n", "minute_data = get_minute_data_for_gap_events(gap_up_df)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Simulating trades with fixed params: PreMarketVol=250000, StopLoss=1000.0%, PosSize=5.0% ---\n", "--- Simulation complete. Trades executed: 936 ---\n"]}], "source": ["def simulate_opb_short_strategy(gap_up_df, minute_data_dict, initial_capital=100000,\n", "                                # *** Arguments for fixed parameters ***\n", "                                min_premarket_volume_dollars_fixed = 250000,\n", "                                stop_loss_pct_fixed = 1000.0,\n", "                                position_size_pct_fixed = 0.05):\n", "    \"\"\"\n", "    Simulate the OPB Short Strategy on identified gap up events.\n", "    (KEEP YOUR EXISTING IMPLEMENTATION, just use the arguments for fixed params)\n", "    \"\"\"\n", "    from datetime import time\n", "    market_open_time = time(9, 30)\n", "    target_exit_time = time(15, 55)\n", "\n", "    trades = []\n", "    print(f\"--- Simulating trades with fixed params: PreMarketVol={min_premarket_volume_dollars_fixed}, StopLoss={stop_loss_pct_fixed}%, PosSize={position_size_pct_fixed*100}% ---\")\n", "\n", "    # Use the arguments inside the simulation logic\n", "    min_premarket_volume_dollars = min_premarket_volume_dollars_fixed\n", "    stop_loss_pct = stop_loss_pct_fixed\n", "    position_size_pct = position_size_pct_fixed\n", "\n", "    # --- Keep the rest of your simulation logic ---\n", "    # Make sure the parts below use the variables defined above from the arguments\n", "    for _, event in gap_up_df.iterrows():\n", "        ticker = event['stock']\n", "        event_date = event['date'].date()\n", "\n", "        if (event_date, ticker) not in minute_data_dict:\n", "            continue\n", "\n", "        minute_data = minute_data_dict[(event_date, ticker)]\n", "        premarket_data = minute_data[minute_data.index.time < market_open_time]\n", "\n", "        if premarket_data.empty:\n", "            continue\n", "\n", "        premarket_dollar_volume = (premarket_data['volume'] * premarket_data['close']).sum()\n", "\n", "        # *** Use the fixed parameter argument ***\n", "        if premarket_dollar_volume < min_premarket_volume_dollars:\n", "            continue\n", "\n", "        market_data = minute_data[minute_data.index.time >= market_open_time]\n", "        if market_data.empty:\n", "            continue\n", "\n", "        entry_time = market_data.index[0]\n", "        entry_price = market_data.iloc[0]['open']\n", "\n", "        # *** Use the fixed parameter argument ***\n", "        position_capital = initial_capital * position_size_pct\n", "        # Handle potential zero entry price\n", "        if entry_price <= 0: continue\n", "        entry_quantity = int(position_capital / entry_price)\n", "\n", "        if entry_quantity <= 0:\n", "            continue\n", "\n", "        # *** Use the fixed parameter argument ***\n", "        stop_price = entry_price * (1 + stop_loss_pct / 100.0)\n", "\n", "        trade = { \n", "            'stock': ticker, 'date': event_date, 'entry_time': entry_time,\n", "            'entry_price': entry_price, 'entry_quantity': entry_quantity,\n", "            'stop_price': stop_price, 'position_dollars': entry_price * entry_quantity,\n", "            'exit_time': None, 'exit_price': None, 'exit_reason': None,\n", "            'pnl': None, 'pnl_pct': None\n", "        }\n", "\n", "        exit_time = None\n", "        exit_price = None\n", "        exit_reason = None\n", "\n", "        for i in range(1, len(market_data)):\n", "            bar = market_data.iloc[i]\n", "            bar_time = market_data.index[i].time()\n", "\n", "            if bar['high'] >= stop_price:\n", "                exit_time = market_data.index[i]\n", "                exit_price = stop_price\n", "                exit_reason = \"STOP_LOSS\"\n", "                break\n", "\n", "            if bar_time >= target_exit_time:\n", "                exit_time = market_data.index[i]\n", "                exit_price = bar['close']\n", "                exit_reason = \"END_OF_DAY\"\n", "                break\n", "\n", "        if exit_time is None and len(market_data) > 0 : # Check if market_data was not empty\n", "             exit_time = market_data.index[-1]\n", "             exit_price = market_data.iloc[-1]['close']\n", "             exit_reason = \"END_OF_DAY\"\n", "        elif exit_time is None: # Handle cases where no market data was usable after entry attempt\n", "            continue # Skip this trade if no exit possible\n", "\n", "        # Ensure exit_price is valid before calculating PnL\n", "        if exit_price is None or pd.isna(exit_price):\n", "             # print(f\"Warning: Could not determine valid exit price for {ticker} on {event_date}. Skipping PnL calc.\")\n", "             continue # Skip trade if exit is invalid\n", "\n", "        pnl = (entry_price - exit_price) * entry_quantity\n", "        # Handle potential zero entry price again for percentage calculation\n", "        pnl_pct = ((entry_price - exit_price) / entry_price) * 100 if entry_price != 0 else 0\n", "\n", "        trade['exit_time'] = exit_time\n", "        trade['exit_price'] = exit_price\n", "        trade['exit_reason'] = exit_reason\n", "        trade['pnl'] = pnl\n", "        trade['pnl_pct'] = pnl_pct\n", "\n", "        trades.append(trade)\n", "\n", "    if not trades:\n", "        print(\"--- No trades executed in this simulation run. ---\")\n", "        return pd.DataFrame()\n", "\n", "    trades_df = pd.DataFrame(trades)\n", "    trades_df['cumulative_pnl'] = trades_df['pnl'].cumsum()\n", "    print(f\"--- Simulation complete. Trades executed: {len(trades_df)} ---\")\n", "    return trades_df\n", "\n", "# Run the simulation on the gap up events\n", "trades_df = simulate_opb_short_strategy(gap_up_df, minute_data)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["dollar_threshold          250000.000\n", "gap_threshold_%               50.000\n", "gap‑up events               1595.000\n", "locates triggered            970.000\n", "true positives               936.000\n", "false positives               34.000\n", "false negatives                0.000\n", "true negatives               625.000\n", "precision                      0.965\n", "recall                         1.000\n", "f1‑score                       0.982\n", "accuracy (within gaps)         0.979\n", "dtype: float64"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from datetime import time\n", "import pandas as pd\n", "\n", "def evaluate_early_locate(\n", "        gap_up_events: pd.DataFrame,\n", "        trades: pd.Data<PERSON>rame,\n", "        *,\n", "        dollar_threshold: float = 250_000,\n", "        gap_threshold: float    = 50,          # %\n", "        locates_trigger_col: str = \"predicted_locate\",\n", "    ) -> pd.Series:\n", "    \"\"\"\n", "    Treat a pre‑locate as *triggered* only when **both** conditions hold:\n", "        • pre‑market dollar‑volume  >= `dollar_threshold`\n", "        • actual gap‑up percentage >= `gap_threshold`\n", "    Metrics are computed inside the universe of ≥ gap_threshold gap‑ups.\n", "    \"\"\"\n", "    # ----- harmonise keys ------------------------------------------------\n", "    df = gap_up_events.copy()\n", "    df['date'] = df['date'].dt.date               # make it a plain `date`\n", "\n", "    # ----- pre‑market $‑volume for every event ---------------------------\n", "    def premarket_dv(row):\n", "        md = minute_data.get((row['date'], row['stock']))\n", "        if md is None:\n", "            return 0.0\n", "        pm = md[md.index.time < time(9, 30)]\n", "        return float((pm['close'] * pm['volume']).sum())\n", "\n", "    df['premarket_dv'] = df.apply(premarket_dv, axis=1)\n", "\n", "    # ----- locate decision (both rules) ----------------------------------\n", "    df[locates_trigger_col] = (\n", "        (df['premarket_dv']      >= dollar_threshold) &\n", "        (df['gap_up_percentage'] >= gap_threshold)\n", "    )\n", "\n", "    # ----- which gap‑ups actually became shorts --------------------------\n", "    executed = (trades[['date', 'stock']]\n", "                .drop_duplicates()\n", "                .assign(executed_trade=True))\n", "\n", "    merged = df.merge(executed, on=['date', 'stock'], how='left') \\\n", "               .assign(executed_trade=lambda d: d['executed_trade'].fillna(False))\n", "\n", "    # ----- confusion matrix counts ---------------------------------------\n", "    tp = ((merged[locates_trigger_col]) &  (merged['executed_trade'])).sum()\n", "    fp = ((merged[locates_trigger_col]) & ~(merged['executed_trade'])).sum()\n", "    fn = (~merged[locates_trigger_col]   &  merged['executed_trade']).sum()\n", "    tn = (~merged[locates_trigger_col]   & ~merged['executed_trade']).sum()\n", "\n", "    precision = tp / (tp + fp) if tp + fp else 0.0\n", "    recall    = tp / (tp + fn) if tp + fn else 0.0\n", "    f1        = 2 * precision * recall / (precision + recall) if precision + recall else 0.0\n", "    accuracy  = (tp + tn) / (tp + fp + fn + tn) if tp + fp + fn + tn else 0.0\n", "\n", "    return pd.Series({\n", "        \"dollar_threshold\":       dollar_threshold,\n", "        \"gap_threshold_%\":        gap_threshold,\n", "        \"gap‑up events\":          len(df),\n", "        \"locates triggered\":      int(merged[locates_trigger_col].sum()),\n", "        \"true positives\":         int(tp),\n", "        \"false positives\":        int(fp),\n", "        \"false negatives\":        int(fn),\n", "        \"true negatives\":         int(tn),\n", "        \"precision\":              round(precision, 3),\n", "        \"recall\":                 round(recall, 3),\n", "        \"f1‑score\":               round(f1, 3),\n", "        \"accuracy (within gaps)\": round(accuracy, 3),\n", "    })\n", "\n", "# run it\n", "metrics = evaluate_early_locate(gap_up_df, trades_df,\n", "                                dollar_threshold=250_000,\n", "                                gap_threshold=50)\n", "display(metrics)\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Triggered locates analysed : 970\n", "Median minutes early       : 171.5\n", "Mean minutes early         : 194.2\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "threshold_dollar  = 250_000\n", "threshold_pct = 50\n", "\n", "rows = []\n", "for _, r in gap_up_df.iterrows():\n", "    if r['gap_up_percentage'] < threshold_pct:\n", "        continue                       # fails gap‑% rule\n", "\n", "    key = (r['date'].date(), r['stock'])\n", "    md  = minute_data.get(key)\n", "    if md is None:\n", "        continue\n", "\n", "    pm = md[md.index.time < time(9, 30)]\n", "    if pm.empty:\n", "        continue\n", "\n", "    cum_dv = (pm['close'] * pm['volume']).cumsum()\n", "    if cum_dv.iloc[-1] < threshold_dollar:\n", "        continue                       # fails dollar rule\n", "\n", "    first_cross = cum_dv[cum_dv >= threshold_dollar].index[0]\n", "    open_dt     = first_cross.replace(hour=9, minute=30, second=0)\n", "    minutes_b4  = (open_dt - first_cross).total_seconds() / 60.0\n", "\n", "    rows.append({\"minutes_before_open\": minutes_b4})\n", "\n", "locate_df = pd.DataFrame(rows)\n", "\n", "if locate_df.empty:\n", "    print(\"No events satisfied **both** rules – nothing to plot.\")\n", "else:\n", "    fig, ax = plt.subplots()\n", "    locate_df['minutes_before_open'].plot(kind='hist', bins=30, ax=ax)\n", "    ax.set_xlabel('Minutes before the 9:30 AM open')\n", "    ax.set_ylabel('Number of gap‑ups')\n", "    ax.set_title(f'Earliest locate time (≥ ${threshold_dollar:,} AND ≥ {threshold_pct} % gap)')\n", "    fig.tight_layout()\n", "    print(f\"Triggered locates analysed : {len(locate_df)}\")\n", "    print(f\"Median minutes early       : {locate_df.minutes_before_open.median():.1f}\")\n", "    print(f\"Mean minutes early         : {locate_df.minutes_before_open.mean():.1f}\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (3091750060.py, line 9)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[18]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31m$thresh: float = 250_000,\u001b[39m\n    ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m invalid syntax\n"]}], "source": ["import random, numpy as np, pandas as pd\n", "from datetime import date, time\n", "from tqdm.auto import tqdm\n", "\n", "def sample_false_positives(\n", "        universe_tickers: list,\n", "        all_trading_days: list[date],\n", "        k_per_day: int = 40,\n", "        dollar_threshold: float = 250_000,\n", "        gap_thresh: float = 50\n", "    ) -> pd.Series:\n", "\n", "    random.seed(0)\n", "    tp = fp = fn = tn = 0\n", "\n", "    for d in tqdm(all_trading_days):\n", "        sample = random.sample(universe_tickers, k_per_day)\n", "\n", "        for tkr in sample:\n", "            md = get_minute_data_if_needed(tkr, d)       # your fetch wrapper\n", "            if md is None: \n", "                continue\n", "\n", "            pm    = md[md.index.time < time(9,30)]\n", "            dv    = float((pm['close']*pm['volume']).sum())\n", "            gap_pct  = compute_gap_pct(tkr, d)              # daily open vs prev close\n", "\n", "            locate_fired = (dv >= dollar_threshold) and (gap_pct >= gap_thresh)\n", "            traded       = check_if_trade_executed(tkr, d)   # from trades_df\n", "\n", "            tp += locate_fired and traded\n", "            fp += locate_fired and not traded\n", "            fn += (not locate_fired) and traded\n", "            tn += (not locate_fired) and (not traded)\n", "\n", "    precision = tp/(tp+fp) if tp+fp else 0\n", "    recall    = tp/(tp+fn) if tp+fn else 0\n", "    fp_rate   = fp/(fp+tn) if fp+tn else 0\n", "    # … add variance / CI calc here …\n", "\n", "    return pd.Series(dict(tp=tp, fp=fp, fn=fn, tn=tn,\n", "                          precision=precision, recall=recall,\n", "                          false_positive_rate=fp_rate))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1262593d07264d77885eb98d61f50ac2", "version_major": 2, "version_minor": 0}, "text/plain": ["Sampling:   0%|          | 0/850 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Sample size           : 34,000 observations  (40 tickers × 850 days)\n", "False‑positive rate   : 0.000%\n", "95 % confidence band  : 0.000%  →  0.000%\n", "Precision             : 100.000%\n", "Recall                : 80.000%\n"]}], "source": ["import random, pandas as pd, numpy as np\n", "from datetime import date, timedelta, time\n", "from tqdm.auto import tqdm\n", "\n", "# ---- parameters --------------------------------------------------------\n", "START = date(2022, 1, 1)\n", "END   = date(2025, 4, 4)\n", "trading_days = pd.bdate_range(START, END).date        # 750-ish\n", "k_per_day    = 40                                     # ➊ choose here\n", "DTHRESH      = 250_000\n", "GAP_THRESH   = 50                                     # %\n", "\n", "rng = random.Random(0)                                # reproducible\n", "\n", "# ---- executed‑trade lookup ---------------------------------------------\n", "traded_set = {(d, s) for d, s in zip(trades_df['date'], trades_df['stock'])}\n", "\n", "# ---- helpers -----------------------------------------------------------\n", "def gap_pct(tkr: str, d: date) -> float:\n", "    \"\"\"Compute open‑vs‑prev‑close gap% using your daily feed.\"\"\"\n", "    daily = get_daily_data(tkr,\n", "                           start_dt=eastern_tz.localize(datetime.combine(d - timedelta(days=1), time())),\n", "                           end_dt=eastern_tz.localize(datetime.combine(d, time())))\n", "    if daily is None or len(daily) < 2:\n", "        return np.nan\n", "    prev_close = daily.iloc[-2]['close']\n", "    open_px    = daily.iloc[-1]['open']\n", "    return (open_px - prev_close) / prev_close * 100\n", "\n", "def premarket_dv(tkr: str, d: date) -> float:\n", "    md = get_per_minute_data(tkr,\n", "                             start_dt=eastern_tz.localize(datetime.combine(d, time(4,0))),\n", "                             end_dt  =eastern_tz.localize(datetime.combine(d, time(9,29))))\n", "    if md is None or md.empty:\n", "        return 0.0\n", "    return float((md['close'] * md['volume']).sum())\n", "\n", "# ---- <PERSON><PERSON> loop ---------------------------------------------------\n", "tp = fp = fn = tn = 0\n", "\n", "sorted_small_cap_tickers = sorted(small_cap_tickers)\n", "for d in tqdm(trading_days, desc=\"Sampling\"):\n", "    sample = rng.sample(sorted_small_cap_tickers, k_per_day)\n", "\n", "    for tkr in sample:\n", "        dv   = premarket_dv(tkr, d)\n", "        gap_p = gap_pct(tkr, d)\n", "\n", "        locate_fired = (dv >= DTHRESH) and (gap_p >= GAP_THRESH)\n", "        traded       = (d, tkr) in traded_set\n", "\n", "        tp +=  locate_fired and  traded\n", "        fp +=  locate_fired and not traded\n", "        fn += (not locate_fired) and  traded\n", "        tn += (not locate_fired) and not traded\n", "\n", "# ---- metrics & CI -------------------------------------------------------\n", "n   = tp + fp + fn + tn\n", "p̂   = fp / (fp + tn) if fp + tn else 0            # false‑positive rate\n", "se  = np.sqrt(p̂ * (1 - p̂) / n)                   # std‑error\n", "ci95= (p̂ - 1.96*se, p̂ + 1.96*se)\n", "\n", "print(f\"Sample size           : {n:,d} observations  \"\n", "      f\"({k_per_day} tickers × {len(trading_days)} days)\")\n", "print(f\"False‑positive rate   : {p̂:.3%}\")\n", "print(f\"95 % confidence band  : {ci95[0]:.3%}  →  {ci95[1]:.3%}\")\n", "print(f\"Precision             : {tp/(tp+fp):.3%}\" if tp+fp else \"—\")\n", "print(f\"Recall                : {tp/(tp+fn):.3%}\" if tp+fn else \"—\")\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(33990)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["fp + tn "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}