{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "\n", "market_data = (MarketDataBuilder()\n", "                 .with_trade_session(\"full\") \n", "                 .with_period(\"intraday\")\n", "                 .build_market_data())\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Utils\n", "\n", "#@df_cache_datetime_est\n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>date</th>\n", "    </tr>\n", "    <tr>\n", "      <th>timestamp</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-02 06:27:00-05:00</th>\n", "      <td>4.1042</td>\n", "      <td>4.1042</td>\n", "      <td>4.1042</td>\n", "      <td>4.1042</td>\n", "      <td>11040.0</td>\n", "      <td>2015-01-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02 07:53:00-05:00</th>\n", "      <td>4.1125</td>\n", "      <td>4.1125</td>\n", "      <td>4.1125</td>\n", "      <td>4.1125</td>\n", "      <td>6792.0</td>\n", "      <td>2015-01-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02 07:58:00-05:00</th>\n", "      <td>4.1129</td>\n", "      <td>4.1129</td>\n", "      <td>4.1129</td>\n", "      <td>4.1129</td>\n", "      <td>2400.0</td>\n", "      <td>2015-01-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02 08:00:00-05:00</th>\n", "      <td>4.1142</td>\n", "      <td>4.1142</td>\n", "      <td>4.1117</td>\n", "      <td>4.1142</td>\n", "      <td>7200.0</td>\n", "      <td>2015-01-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02 08:06:00-05:00</th>\n", "      <td>4.1079</td>\n", "      <td>4.1079</td>\n", "      <td>4.1079</td>\n", "      <td>4.1079</td>\n", "      <td>14592.0</td>\n", "      <td>2015-01-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-31 19:55:00-05:00</th>\n", "      <td>79.1800</td>\n", "      <td>79.1800</td>\n", "      <td>79.1800</td>\n", "      <td>79.1800</td>\n", "      <td>5250.0</td>\n", "      <td>2024-12-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-31 19:56:00-05:00</th>\n", "      <td>79.1625</td>\n", "      <td>79.2100</td>\n", "      <td>79.1625</td>\n", "      <td>79.1800</td>\n", "      <td>3611.0</td>\n", "      <td>2024-12-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-31 19:57:00-05:00</th>\n", "      <td>79.1800</td>\n", "      <td>79.1900</td>\n", "      <td>79.1800</td>\n", "      <td>79.1900</td>\n", "      <td>1327.0</td>\n", "      <td>2024-12-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-31 19:58:00-05:00</th>\n", "      <td>79.2000</td>\n", "      <td>79.2000</td>\n", "      <td>79.1907</td>\n", "      <td>79.2000</td>\n", "      <td>835.0</td>\n", "      <td>2024-12-31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-31 19:59:00-05:00</th>\n", "      <td>79.2095</td>\n", "      <td>79.2200</td>\n", "      <td>79.1800</td>\n", "      <td>79.1800</td>\n", "      <td>5820.0</td>\n", "      <td>2024-12-31</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1768416 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                              open     high      low    close   volume  \\\n", "timestamp                                                                \n", "2015-01-02 06:27:00-05:00   4.1042   4.1042   4.1042   4.1042  11040.0   \n", "2015-01-02 07:53:00-05:00   4.1125   4.1125   4.1125   4.1125   6792.0   \n", "2015-01-02 07:58:00-05:00   4.1129   4.1129   4.1129   4.1129   2400.0   \n", "2015-01-02 08:00:00-05:00   4.1142   4.1142   4.1117   4.1142   7200.0   \n", "2015-01-02 08:06:00-05:00   4.1079   4.1079   4.1079   4.1079  14592.0   \n", "...                            ...      ...      ...      ...      ...   \n", "2024-12-31 19:55:00-05:00  79.1800  79.1800  79.1800  79.1800   5250.0   \n", "2024-12-31 19:56:00-05:00  79.1625  79.2100  79.1625  79.1800   3611.0   \n", "2024-12-31 19:57:00-05:00  79.1800  79.1900  79.1800  79.1900   1327.0   \n", "2024-12-31 19:58:00-05:00  79.2000  79.2000  79.1907  79.2000    835.0   \n", "2024-12-31 19:59:00-05:00  79.2095  79.2200  79.1800  79.1800   5820.0   \n", "\n", "                                 date  \n", "timestamp                              \n", "2015-01-02 06:27:00-05:00  2015-01-02  \n", "2015-01-02 07:53:00-05:00  2015-01-02  \n", "2015-01-02 07:58:00-05:00  2015-01-02  \n", "2015-01-02 08:00:00-05:00  2015-01-02  \n", "2015-01-02 08:06:00-05:00  2015-01-02  \n", "...                               ...  \n", "2024-12-31 19:55:00-05:00  2024-12-31  \n", "2024-12-31 19:56:00-05:00  2024-12-31  \n", "2024-12-31 19:57:00-05:00  2024-12-31  \n", "2024-12-31 19:58:00-05:00  2024-12-31  \n", "2024-12-31 19:59:00-05:00  2024-12-31  \n", "\n", "[1768416 rows x 6 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load data\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2015, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 1, 1))\n", "\n", "tqqq_data = get_per_minute_data(\"TQQQ\", start_dt, end_dt)\n", "\n", "tqqq_data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["         date direction   entry    exit     pnl_r exit_reason    risk\n", "0  2015-01-13      Long  4.0175  3.9588 -1.000000        stop  0.0587\n", "1  2015-01-14      Long  3.7709  3.7125 -1.000000        stop  0.0584\n", "2  2015-01-15     Short  3.7921  3.6354  2.611667         eod  0.0600\n", "3  2015-01-16      Long  3.6688  3.6113 -1.000000        stop  0.0575\n", "4  2015-01-22     Short  3.9481  4.0017 -1.000000        stop  0.0536\n", "Average PnL (R): 0.1259619454851264\n", "Total PnL: $216,780.51\n"]}], "source": ["# ORB Simple\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import time, timedelta\n", "import matplotlib.pyplot as plt\n", "\n", "def run_orb_strategy_simple(df, profit_r=10):\n", "    df = df.copy()\n", "    df['date'] = df.index.date\n", "\n", "    results = []\n", "    grouped = df.groupby('date')\n", "\n", "    for date, day_data in grouped:\n", "        day_data = day_data.between_time(\"09:30\", \"16:00\")\n", "        if len(day_data) < 6:\n", "            continue\n", "\n", "        # Define opening range: first 5 minutes\n", "        open_range = day_data.iloc[:5]\n", "        first_candle_open = open_range.iloc[0]['open']\n", "        first_candle_close = open_range.iloc[-1]['close']\n", "        first_candle_high = open_range['high'].max()\n", "        first_candle_low = open_range['low'].min()\n", "\n", "        # Determine direction based on first 5-minute candle\n", "        if first_candle_close > first_candle_open:\n", "            direction = 'Long'\n", "            stop_price = first_candle_low\n", "        elif first_candle_close < first_candle_open:\n", "            direction = 'Short'\n", "            stop_price = first_candle_high\n", "        else:\n", "            continue  # No direction\n", "\n", "        # Get the 6th bar (entry bar)\n", "        entry_bar = day_data.iloc[5]\n", "        entry_time = entry_bar.name\n", "        entry_price = entry_bar['open']\n", "\n", "        # Calculate risk and target\n", "        if direction == 'Long':\n", "            risk = entry_price - stop_price\n", "            if risk <= 0.05:\n", "                continue\n", "            target_price = entry_price + profit_r * risk\n", "        else:  # Short\n", "            risk = stop_price - entry_price\n", "            if risk <= 0.05:\n", "                continue\n", "            target_price = entry_price - profit_r * risk\n", "\n", "        # Analyze trade outcome\n", "        trade_data = day_data.loc[entry_time:]\n", "        exit_price = None\n", "        exit_reason = None\n", "\n", "        for ts, row in trade_data.iterrows():\n", "            high = row['high']\n", "            low = row['low']\n", "\n", "            if direction == 'Long':\n", "                if low <= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif high >= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "            else:  # Short\n", "                if high >= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif low <= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "\n", "        if exit_price is None:\n", "            exit_price = trade_data.iloc[-1]['close']\n", "            exit_reason = 'eod'\n", "\n", "        pnl_r = (exit_price - entry_price) / risk if direction == 'Long' else (entry_price - exit_price) / risk\n", "        pnl_r = min(pnl_r, profit_r) if pnl_r > 0 else pnl_r  # Cap at profit_r\n", "\n", "        results.append({\n", "            'date': date,\n", "            'direction': direction,\n", "            'entry': entry_price,\n", "            'exit': exit_price,\n", "            'pnl_r': pnl_r,\n", "            'exit_reason': exit_reason,\n", "            'risk': risk\n", "        })\n", "\n", "    return pd.DataFrame(results)\n", "\n", "\n", "\n", "df_orb = run_orb_strategy_simple(tqqq_data, profit_r=10)\n", "print(df_orb.head())\n", "print(\"Average PnL (R):\", df_orb['pnl_r'].mean())\n", "\n", "total_pnl = df_orb['pnl_r'].sum() * 1000\n", "print(f\"Total PnL: ${total_pnl:,.2f}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["         date direction   entry      exit  pnl_r exit_reason      risk  \\\n", "0  2015-01-23     Short  4.1196  4.130790   -1.0        stop  0.011190   \n", "1  2015-01-26     Short  4.0967  4.107515   -1.0        stop  0.010815   \n", "2  2015-01-27     Short  3.9117  3.807571   10.0      target  0.010413   \n", "3  2015-01-28     Short  3.9659  3.976734   -1.0        stop  0.010834   \n", "4  2015-01-29     Short  3.7367  3.748058   -1.0        stop  0.011358   \n", "\n", "        ATR  \n", "0  0.223800  \n", "1  0.216300  \n", "2  0.208257  \n", "3  0.216679  \n", "4  0.227157  \n", "Average PnL (R): 0.2926003683826438\n", "Total PnL (ORB ATR): $727,404.52\n"]}], "source": ["# Orb wit 14d atr\n", "\n", "def calculate_daily_atr(df_minute, period=14):\n", "    df_minute = df_minute.copy()\n", "\n", "    # Resample to daily bars\n", "    df_daily = df_minute.resample('1D').agg({\n", "        'high': 'max',\n", "        'low': 'min',\n", "        'close': 'last'\n", "    }).dropna()\n", "\n", "    # Calculate True Range\n", "    high_low = df_daily['high'] - df_daily['low']\n", "    high_close = np.abs(df_daily['high'] - df_daily['close'].shift())\n", "    low_close = np.abs(df_daily['low'] - df_daily['close'].shift())\n", "    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)\n", "    atr = tr.rolling(window=period).mean()\n", "    df_daily['ATR'] = atr.shift(1)\n", "\n", "    # Merge ATR back into minute data using full datetime index\n", "    df_minute['DateOnly'] = df_minute.index.normalize()  # normalize to midnight\n", "    df_minute = df_minute.merge(\n", "        df_daily[['ATR']], \n", "        left_on='DateOnly', \n", "        right_index=True, \n", "        how='left'\n", "    )\n", "    df_minute.drop(columns='DateOnly', inplace=True)\n", "\n", "    return df_minute\n", "\n", "def run_orb_atr_strategy(df, stop_pct=0.10, profit_r=10):\n", "    df = df.copy()\n", "    df['date'] = df.index.date\n", "\n", "    # Make sure ATR is available\n", "    if 'ATR' not in df.columns:\n", "        raise ValueError(\"ATR column not found. Please run calculate_daily_atr(df) first.\")\n", "\n", "    results = []\n", "    grouped = df.groupby('date')\n", "\n", "    for date, day_data in grouped:\n", "        day_data = day_data.between_time(\"09:30\", \"16:00\")\n", "        if len(day_data) < 6:\n", "            continue\n", "\n", "        # Skip if no ATR for the day\n", "        day_atr = day_data['ATR'].iloc[0]\n", "        if pd.isna(day_atr):\n", "            continue\n", "\n", "        atr_stop = stop_pct * day_atr\n", "\n", "        # Define opening range: first 5 minutes\n", "        open_range = day_data.iloc[:5]\n", "        first_candle_open = open_range.iloc[0]['open']\n", "        first_candle_close = open_range.iloc[-1]['close']\n", "\n", "        # Determine direction\n", "        if first_candle_close > first_candle_open:\n", "            direction = 'Long'\n", "        elif first_candle_close < first_candle_open:\n", "            direction = 'Short'\n", "        else:\n", "            continue  # No direction\n", "\n", "        # Entry at open of the 6th minute bar\n", "        entry_bar = day_data.iloc[5]\n", "        entry_time = entry_bar.name\n", "        entry_price = entry_bar['open']\n", "\n", "        # Define stop and target\n", "        if direction == 'Long':\n", "            stop_price = entry_price - atr_stop\n", "            target_price = entry_price + profit_r * atr_stop\n", "        else:\n", "            stop_price = entry_price + atr_stop\n", "            target_price = entry_price - profit_r * atr_stop\n", "\n", "        risk = atr_stop  # R defined as 1R = ATR stop\n", "\n", "        # Analyze trade outcome\n", "        trade_data = day_data.loc[entry_time:]\n", "        exit_price = None\n", "        exit_reason = None\n", "\n", "        for ts, row in trade_data.iterrows():\n", "            high = row['high']\n", "            low = row['low']\n", "\n", "            if direction == 'Long':\n", "                if low <= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif high >= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "            else:  # Short\n", "                if high >= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif low <= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "\n", "        if exit_price is None:\n", "            exit_price = trade_data.iloc[-1]['close']\n", "            exit_reason = 'eod'\n", "\n", "        pnl_r = (exit_price - entry_price) / risk if direction == 'Long' else (entry_price - exit_price) / risk\n", "        pnl_r = min(pnl_r, profit_r) if pnl_r > 0 else pnl_r  # Cap PnL\n", "\n", "        results.append({\n", "            'date': date,\n", "            'direction': direction,\n", "            'entry': entry_price,\n", "            'exit': exit_price,\n", "            'pnl_r': pnl_r,\n", "            'exit_reason': exit_reason,\n", "            'risk': risk,\n", "            'ATR': day_atr\n", "        })\n", "\n", "    return pd.DataFrame(results)\n", "\n", "\n", "df_with_atr = calculate_daily_atr(tqqq_data)\n", "df_orb_atr = run_orb_atr_strategy(df_with_atr, stop_pct=0.05, profit_r=10)\n", "print(df_orb_atr.head())\n", "print(\"Average PnL (R):\", df_orb_atr['pnl_r'].mean())\n", "\n", "total_pnl = df_orb_atr['pnl_r'].sum() * 1000\n", "print(f\"Total PnL (ORB ATR): ${total_pnl:,.2f}\")\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# # Orb 14day atr + heatmap of stop loss vs profit target\n", "# import seaborn as sns\n", "# import matplotlib.pyplot as plt\n", "\n", "# def simulate_orb_matrix(df, stop_list, profit_list):\n", "#     avg_r_matrix = pd.DataFrame(index=stop_list, columns=profit_list)\n", "#     total_r_matrix = pd.DataFrame(index=stop_list, columns=profit_list)\n", "#     trade_count_matrix = pd.DataFrame(index=stop_list, columns=profit_list)\n", "\n", "#     for stop_pct in stop_list:\n", "#         for profit_r in profit_list:\n", "#             df_with_atr = calculate_daily_atr(tqqq_data)\n", "#             result = run_orb_atr_strategy(df_with_atr, stop_pct=stop_pct, profit_r=profit_r)\n", "            \n", "#             # Store average R per trade\n", "#             avg_r_matrix.loc[stop_pct, profit_r] = result['pnl_r'].mean() if not result.empty else np.nan\n", "            \n", "#             # Store total R (sum of all returns)\n", "#             total_r_matrix.loc[stop_pct, profit_r] = result['pnl_r'].sum() if not result.empty else np.nan\n", "            \n", "#             # Store trade count\n", "#             trade_count_matrix.loc[stop_pct, profit_r] = len(result) if not result.empty else 0\n", "\n", "#     avg_r_matrix = avg_r_matrix.astype(float)\n", "#     total_r_matrix = total_r_matrix.astype(float)\n", "#     trade_count_matrix = trade_count_matrix.astype(int)\n", "    \n", "#     return avg_r_matrix, total_r_matrix, trade_count_matrix\n", "\n", "# def plot_orb_heatmaps(avg_r_matrix, total_r_matrix, trade_count_matrix):\n", "#     fig, axes = plt.subplots(1, 3, figsize=(20, 6))\n", "    \n", "#     # Plot average R heatmap\n", "#     sns.heatmap(avg_r_matrix, annot=True, fmt=\".2f\", cmap=\"coolwarm\", linewidths=0.5, ax=axes[0])\n", "#     axes[0].set_title(\"Average PnL (R) - ORB Strategy\")\n", "#     axes[0].set_xlabel(\"Profit Target (R)\")\n", "#     axes[0].set_ylabel(\"Stop Loss (% ATR)\")\n", "    \n", "#     # Plot total R heatmap\n", "#     sns.heatmap(total_r_matrix, annot=True, fmt=\".1f\", cmap=\"RdYlGn\", linewidths=0.5, ax=axes[1])\n", "#     axes[1].set_title(\"Total PnL (R) - ORB Strategy\")\n", "#     axes[1].set_xlabel(\"Profit Target (R)\")\n", "#     axes[1].set_ylabel(\"Stop Loss (% ATR)\")\n", "    \n", "#     # Plot trade count heatmap\n", "#     sns.heatmap(trade_count_matrix, annot=True, fmt=\"d\", cmap=\"YlGnBu\", linewidths=0.5, ax=axes[2])\n", "#     axes[2].set_title(\"Trade Count - ORB Strategy\")\n", "#     axes[2].set_xlabel(\"Profit Target (R)\")\n", "#     axes[2].set_ylabel(\"Stop Loss (% ATR)\")\n", "    \n", "#     plt.tight_layout()\n", "#     plt.show()\n", "\n", "# # Run simulation with the same parameters\n", "# stop_list = [0.05, 0.10, 0.15, 0.20, 0.25]  # 5% to 25% of ATR\n", "# profit_list = list(range(1, 11))           # 1R to 10R\n", "\n", "# avg_r_matrix, total_r_matrix, trade_count_matrix = simulate_orb_matrix(tqqq_data, stop_list, profit_list)\n", "\n", "# # Plot all three heatmaps\n", "# plot_orb_heatmaps(avg_r_matrix, total_r_matrix, trade_count_matrix)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# # R (return ) of orb vs orb atr vs orb atr + imbalance\n", "\n", "# import matplotlib.pyplot as plt\n", "\n", "# import seaborn as sns\n", "# import matplotlib.pyplot as plt\n", "\n", "# df_with_atr = calculate_daily_atr(tqqq_data)\n", "# orb_simple = run_orb_strategy_simple(df_with_atr, profit_r=10)\n", "# orb_atr = run_orb_atr_strategy(df_with_atr, stop_pct=0.10, profit_r=10)\n", "\n", "# def plot_pnl_distribution():\n", "#     plt.figure(figsize=(10, 6))\n", "\n", "#     sns.kdeplot(orb_simple['pnl_r'], label='ORB Simple', fill=True, alpha=0.4, linewidth=2)\n", "#     sns.kdeplot(orb_atr['pnl_r'], label='ORB ATR', fill=True, alpha=0.4, linewidth=2)\n", "\n", "#     plt.axvline(0, color='gray', linestyle='--', lw=1)\n", "#     plt.title(\"Distribution of Trade PnL (in R units)\")\n", "#     plt.xlabel(\"PnL (R)\")\n", "#     plt.ylabel(\"Density\")\n", "#     plt.legend()\n", "#     plt.grid(True)\n", "#     plt.tight_layout()\n", "#     plt.show()\n", "\n", "\n", "# plot_pnl_distribution()\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# # orb vs orb atr vs orb atr imbalance - cumulative r\n", "# def plot_cumulative_r():\n", "#     plt.figure(figsize=(12, 6))\n", "\n", "#     # Prepare cumulative R for each strategy\n", "#     orb_simple['cumulative_r'] = orb_simple['pnl_r'].cumsum()\n", "#     orb_atr['cumulative_r'] = orb_atr['pnl_r'].cumsum()\n", "\n", "#     # Sort by date to ensure clean cumulative line\n", "#     orb_simple_sorted = orb_simple.sort_values('date')\n", "#     orb_atr_sorted = orb_atr.sort_values('date')\n", "\n", "#     # Plot\n", "#     plt.plot(orb_simple_sorted['date'], orb_simple_sorted['cumulative_r'], label='ORB Simple', linewidth=2)\n", "#     plt.plot(orb_atr_sorted['date'], orb_atr_sorted['cumulative_r'], label='ORB ATR', linewidth=2)\n", "\n", "#     plt.title(\"Cumulative PnL in R Units\")\n", "#     plt.xlabel(\"Date\")\n", "#     plt.ylabel(\"Cumulative R\")\n", "#     plt.grid(True)\n", "#     plt.legend()\n", "#     plt.tight_layout()\n", "#     plt.show()\n", "\n", "# plot_cumulative_r()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}