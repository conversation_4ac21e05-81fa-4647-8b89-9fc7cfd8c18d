{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "from datetime import timedelta\n", "import yfinance as yf\n", "\n", "market_data = (MarketDataBuilder()\n", "                 .with_trade_session(\"full\") \n", "                 .with_period(\"intraday\")\n", "                 .build_market_data())\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# Utils\n", "\n", "def get_yfinance_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"\n", "    Fetch daily historical stock data for a given ticker using yfinance\n", "    Adjusted to handle timezone awareness better for yfinance compatibility\n", "    and ensure index becomes date objects.\n", "\n", "    Parameters:\n", "    ticker (str): The stock ticker symbol (e.g., 'AAPL', 'MSFT', '^VIX')\n", "    start_dt (datetime): Start date and time (timezone aware)\n", "    end_dt (datetime): End date and time (timezone aware)\n", "\n", "    Returns:\n", "    pandas.DataFrame: Daily historical data for the specified ticker\n", "                      Index is converted to Date objects.\n", "    \"\"\"\n", "    # yfinance usually works best with string dates or naive datetimes\n", "    start_str = start_dt.strftime('%Y-%m-%d')\n", "    end_str = end_dt.strftime('%Y-%m-%d')\n", "\n", "    # Fetch daily data\n", "    data = yf.download(\n", "        tickers=ticker,\n", "        start=start_str,\n", "        end=end_str,\n", "        interval='1d',\n", "        auto_adjust=True\n", "    )\n", "\n", "    # yfinance returns tz-aware UTC index by default for '1d'. Convert to Date objects.\n", "    if isinstance(data.index, pd.DatetimeIndex):\n", "         # Ensure conversion to date happens correctly\n", "         data.index = data.index.date\n", "         data.index.name = 'Date' # Assign a name for clarity after reset_index\n", "\n", "    return data\n", "\n", "\n", "#@df_cache_datetime_est\n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# Load data\n", "eastern_tz = pytz.timezone(\"US/Eastern\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Orb wit 14d atr\n", "\n", "def calculate_daily_atr(df_minute, period=14):\n", "    df_minute = df_minute.copy()\n", "    # Create a temporary date column for grouping daily data\n", "    df_minute['DateOnly'] = df_minute.index.normalize().date # Ensure this is date object\n", "\n", "    # Resample to daily bars using the DateOnly column\n", "    # Make sure aggregation functions handle potential non-numeric data if columns changed\n", "    df_daily = df_minute.groupby('DateOnly').agg(\n", "        high=('high', 'max'),\n", "        low=('low', 'min'),\n", "        close=('close', 'last'),\n", "        open=('open', 'first') # Needed for standard TR calc\n", "    ).dropna()\n", "\n", "    # Calculate True Range\n", "    high_low = df_daily['high'] - df_daily['low']\n", "    high_close = np.abs(df_daily['high'] - df_daily['close'].shift())\n", "    low_close = np.abs(df_daily['low'] - df_daily['close'].shift())\n", "\n", "    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)\n", "    atr = tr.rolling(window=period).mean()\n", "    df_daily['ATR'] = atr.shift(1) # Use previous ATR\n", "\n", "    # Create the 'date' column for merging later (consistent date objects)\n", "    df_minute['date'] = df_minute.index.date # Use the actual index date\n", "\n", "    # Merge ATR back into minute data using the 'DateOnly' temp column\n", "    df_minute = df_minute.merge(\n", "        df_daily[['ATR']],\n", "        left_on='DateOnly',\n", "        right_index=True, # Merging onto the DateOnly index of df_daily\n", "        how='left'\n", "    )\n", "    # We don't need <PERSON><PERSON>n<PERSON> anymore\n", "    df_minute.drop(columns='DateOnly', inplace=True)\n", "\n", "    return df_minute\n", "\n", "# df_with_atr = calculate_daily_atr(tqqq_data)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def run_orb_atr_imbalance_strategy(df, stop_pct=0.15, profit_r=10, imbalance_ratio=1.5):\n", "    \"\"\"\n", "    Runs ORB strategy with ATR stop/target, filtered by opening range volume imbalance.\n", "\n", "    Args:\n", "        df (pd.DataFrame): Minute data with 'ATR' column. Must have DatetimeIndex.\n", "        stop_pct (float): Percentage of ATR to use for stop-loss distance.\n", "        profit_r (int/float): Profit target distance in R multiples.\n", "        imbalance_ratio (float): Minimum ratio of confirming volume / opposing volume\n", "                                 required within the OR to take the trade.\n", "                                 (e.g., 1.5 means confirming vol must be >= 1.5x opposing vol)\n", "\n", "    Returns:\n", "        pd.DataFrame: Results of the backtest.\n", "    \"\"\"\n", "    df = df.copy()\n", "    if not isinstance(df.index, pd.DatetimeIndex):\n", "         raise TypeError(\"Input DataFrame must have a DatetimeIndex.\")\n", "    if 'date' not in df.columns:\n", "        df['date'] = df.index.date\n", "\n", "    if 'ATR' not in df.columns:\n", "        raise ValueError(\"ATR column not found. Please run calculate_daily_atr(df) first.\")\n", "    if 'volume' not in df.columns:\n", "         raise ValueError(\"Volume column not found. Needed for imbalance calculation.\")\n", "\n", "    results = []\n", "    grouped = df.groupby('date')\n", "\n", "    print(f\"Running ORB ATR Imbalance with stop_pct={stop_pct}, profit_r={profit_r}, imbalance_ratio={imbalance_ratio}\")\n", "\n", "    for date_obj, day_data in grouped:\n", "        try:\n", "            market_session_data = day_data.between_time(\"09:30\", \"16:00\", inclusive='left')\n", "        except TypeError as e:\n", "            # This might happen if grouping by date object leads to issues, though index restoration should prevent it.\n", "            print(f\"Skipping date {date_obj} due to index error in between_time: {e}\")\n", "            continue\n", "\n", "        if len(market_session_data) < 6: # Need 5 OR bars + 1 entry bar\n", "            continue\n", "\n", "        # --- Get Day's ATR ---\n", "        day_atr = market_session_data['ATR'].iloc[0]\n", "        if pd.isna(day_atr) or day_atr <= 0:\n", "            continue\n", "\n", "        atr_stop_distance = stop_pct * day_atr\n", "\n", "        # --- Define Opening Range & Determine Price Direction ---\n", "        open_range = market_session_data.iloc[:5]\n", "        first_candle_open = open_range.iloc[0]['open']\n", "        first_candle_close = open_range.iloc[-1]['close']\n", "\n", "        if first_candle_close > first_candle_open:\n", "            direction = 'Long'\n", "        elif first_candle_close < first_candle_open:\n", "            direction = 'Short'\n", "        else:\n", "            continue # No price direction\n", "\n", "        # --- Calculate Volume Imbalance within Opening Range ---\n", "        up_volume = 0\n", "        down_volume = 0\n", "        for i, bar in open_range.iterrows():\n", "            if bar['close'] > bar['open']:\n", "                up_volume += bar['volume']\n", "            elif bar['close'] < bar['open']:\n", "                down_volume += bar['volume']\n", "            # Ignore bars where close == open for volume calculation\n", "\n", "        # --- Apply Imbalance Filter ---\n", "        imbalance_ok = False\n", "        if direction == 'Long':\n", "            if down_volume == 0 and up_volume > 0: # All up bars (or flat bars) with some up volume\n", "                imbalance_ok = True\n", "            elif down_volume > 0 and up_volume >= down_volume * imbalance_ratio:\n", "                imbalance_ok = True\n", "        elif direction == 'Short':\n", "            if up_volume == 0 and down_volume > 0: # All down bars (or flat bars) with some down volume\n", "                imbalance_ok = True\n", "            elif up_volume > 0 and down_volume >= up_volume * imbalance_ratio:\n", "                imbalance_ok = True\n", "\n", "        if not imbalance_ok:\n", "            continue # Skip trade if imbalance condition is not met\n", "        # --- End Imbalance Filter ---\n", "\n", "\n", "        # --- Proceed with Trade Setup if Imbalance Filter Passed ---\n", "        entry_bar = market_session_data.iloc[5]\n", "        entry_time = entry_bar.name\n", "        entry_price = entry_bar['open']\n", "\n", "        # Define stop and target\n", "        if direction == 'Long':\n", "            stop_price = entry_price - atr_stop_distance\n", "            target_price = entry_price + profit_r * atr_stop_distance\n", "        else: # Short\n", "            stop_price = entry_price + atr_stop_distance\n", "            target_price = entry_price - profit_r * atr_stop_distance\n", "\n", "        risk = atr_stop_distance # R defined as 1R = ATR stop distance\n", "\n", "        # --- Analyze Trade Outcome ---\n", "        trade_data = market_session_data.loc[entry_time:]\n", "        exit_price = None\n", "        exit_reason = None\n", "        exit_time = None\n", "\n", "        for ts, row in trade_data.iterrows():\n", "            high = row['high']\n", "            low = row['low']\n", "            exit_time = ts # Keep track of timestamp\n", "\n", "            if direction == 'Long':\n", "                if low <= stop_price:\n", "                    exit_price = stop_price; exit_reason = 'stop'; break\n", "                elif high >= target_price:\n", "                    exit_price = target_price; exit_reason = 'target'; break\n", "            else: # Short\n", "                if high >= stop_price:\n", "                    exit_price = stop_price; exit_reason = 'stop'; break\n", "                elif low <= target_price:\n", "                    exit_price = target_price; exit_reason = 'target'; break\n", "\n", "        if exit_price is None:\n", "            if not trade_data.empty:\n", "                exit_price = trade_data.iloc[-1]['close']\n", "                exit_reason = 'eod'\n", "                exit_time = trade_data.index[-1]\n", "            else:\n", "                exit_price = entry_price # Should ideally not happen if len check passed\n", "                exit_reason = 'eod_immediate'\n", "                exit_time = entry_time\n", "\n", "        # --- Calculate PnL in R ---\n", "        if risk <= 0: continue # Avoid division by zero if ATR was somehow zero\n", "\n", "        pnl_r = (exit_price - entry_price) / risk if direction == 'Long' else (entry_price - exit_price) / risk\n", "        pnl_r = min(pnl_r, profit_r) if pnl_r > 0 else pnl_r # Cap PnL\n", "\n", "        results.append({\n", "            'date': date_obj,\n", "            'direction': direction,\n", "            'entry': entry_price,\n", "            'exit': exit_price,\n", "            'pnl_r': pnl_r,\n", "            'exit_reason': exit_reason,\n", "            'risk': risk,\n", "            'ATR': day_atr,\n", "            'stop_pct': stop_pct, # Store parameters used\n", "            'profit_r': profit_r,\n", "            'imbalance_ratio_used': imbalance_ratio,\n", "            'or_up_volume': up_volume, # Store imbalance info\n", "            'or_down_volume': down_volume\n", "        })\n", "\n", "    print(f\"Finished. Generated {len(results)} trades.\")\n", "    return pd.DataFrame(results)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing 8 tickers...\n", "\n", "Processing TQQQ...\n", "Running ORB ATR Imbalance Strategy for TQQQ (Stop: 10% ATR, Ratio: 1.5)...\n", "Running ORB ATR Imbalance with stop_pct=0.1, profit_r=10, imbalance_ratio=1.5\n", "Finished. Generated 42 trades.\n", "Generated 42 trades for TQQQ.\n", "Successfully calculated 'pct_return' for 42 trades.\n", "\n", "Processing TSLL...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1444282/3985490230.py:56: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Running ORB ATR Imbalance Strategy for TSLL (Stop: 10% ATR, Ratio: 1.5)...\n", "Running ORB ATR Imbalance with stop_pct=0.1, profit_r=10, imbalance_ratio=1.5\n", "Finished. Generated 38 trades.\n", "Generated 38 trades for TSLL.\n", "Successfully calculated 'pct_return' for 38 trades.\n", "\n", "Processing NVDL...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1444282/3985490230.py:56: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Running ORB ATR Imbalance Strategy for NVDL (Stop: 10% ATR, Ratio: 1.5)...\n", "Running ORB ATR Imbalance with stop_pct=0.1, profit_r=10, imbalance_ratio=1.5\n", "Finished. Generated 42 trades.\n", "Generated 42 trades for NVDL.\n", "Successfully calculated 'pct_return' for 42 trades.\n", "\n", "Processing SOXL...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1444282/3985490230.py:56: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Running ORB ATR Imbalance Strategy for SOXL (Stop: 10% ATR, Ratio: 1.5)...\n", "Running ORB ATR Imbalance with stop_pct=0.1, profit_r=10, imbalance_ratio=1.5\n", "Finished. Generated 43 trades.\n", "Generated 43 trades for SOXL.\n", "Successfully calculated 'pct_return' for 43 trades.\n", "\n", "Processing NVDA...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1444282/3985490230.py:56: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Running ORB ATR Imbalance Strategy for NVDA (Stop: 10% ATR, Ratio: 1.5)...\n", "Running ORB ATR Imbalance with stop_pct=0.1, profit_r=10, imbalance_ratio=1.5\n", "Finished. Generated 42 trades.\n", "Generated 42 trades for NVDA.\n", "Successfully calculated 'pct_return' for 42 trades.\n", "\n", "Processing TSLA...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1444282/3985490230.py:56: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Running ORB ATR Imbalance Strategy for TSLA (Stop: 10% ATR, Ratio: 1.5)...\n", "Running ORB ATR Imbalance with stop_pct=0.1, profit_r=10, imbalance_ratio=1.5\n", "Finished. Generated 39 trades.\n", "Generated 39 trades for TSLA.\n", "Successfully calculated 'pct_return' for 39 trades.\n", "\n", "Processing PLTR...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1444282/3985490230.py:56: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Running ORB ATR Imbalance Strategy for PLTR (Stop: 10% ATR, Ratio: 1.5)...\n", "Running ORB ATR Imbalance with stop_pct=0.1, profit_r=10, imbalance_ratio=1.5\n", "Finished. Generated 41 trades.\n", "Generated 41 trades for PLTR.\n", "Successfully calculated 'pct_return' for 41 trades.\n", "\n", "Processing PLTU...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1444282/3985490230.py:56: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Running ORB ATR Imbalance Strategy for PLTU (Stop: 10% ATR, Ratio: 1.5)...\n", "Running ORB ATR Imbalance with stop_pct=0.1, profit_r=10, imbalance_ratio=1.5\n", "Finished. Generated 38 trades.\n", "Generated 38 trades for PLTU.\n", "Successfully calculated 'pct_return' for 38 trades.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1444282/3985490230.py:56: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n"]}, {"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 1400x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Summary Statistics (Percentage Returns) ---\n", "TQQQ:\n", "  Trades: 42\n", "  Total Compounded Return: 1.27%\n", "  Final Capital (from 100): 101.27\n", "  Avg Arithmetic Return/Trade: 0.0451%\n", "  Avg Geometric Return/Trade: 0.0300%\n", "  Std Dev Return/Trade: 1.7805%\n", "  Sharpe Ratio (annualized, approx): 0.33\n", "TSLL:\n", "  Trades: 38\n", "  Total Compounded Return: 18.63%\n", "  Final Capital (from 100): 118.63\n", "  Avg Arithmetic Return/Trade: 0.5603%\n", "  Avg Geometric Return/Trade: 0.4506%\n", "  Std Dev Return/Trade: 4.9633%\n", "  Sharpe Ratio (annualized, approx): 1.42\n", "NVDL:\n", "  Trades: 42\n", "  Total Compounded Return: 32.21%\n", "  Final Capital (from 100): 132.21\n", "  Avg Arithmetic Return/Trade: 0.7306%\n", "  Avg Geometric Return/Trade: 0.6670%\n", "  Std Dev Return/Trade: 3.6906%\n", "  Sharpe Ratio (annualized, approx): 2.58\n", "SOXL:\n", "  Trades: 43\n", "  Total Compounded Return: -14.86%\n", "  Final Capital (from 100): 85.14\n", "  Avg Arithmetic Return/Trade: -0.3491%\n", "  Avg Geometric Return/Trade: -0.3734%\n", "  Std Dev Return/Trade: 2.2862%\n", "  <PERSON> (annualized, approx): -2.01\n", "NVDA:\n", "  Trades: 42\n", "  Total Compounded Return: 26.52%\n", "  Final Capital (from 100): 126.52\n", "  Avg Arithmetic Return/Trade: 0.6021%\n", "  Avg Geometric Return/Trade: 0.5617%\n", "  Std Dev Return/Trade: 2.9016%\n", "  Sharpe Ratio (annualized, approx): 2.71\n", "TSLA:\n", "  Trades: 39\n", "  Total Compounded Return: 2.79%\n", "  Final Capital (from 100): 102.79\n", "  Avg Arithmetic Return/Trade: 0.1286%\n", "  Avg Geometric Return/Trade: 0.0705%\n", "  Std Dev Return/Trade: 3.4797%\n", "  Sharpe Ratio (annualized, approx): 0.47\n", "PLTR:\n", "  Trades: 41\n", "  Total Compounded Return: 20.26%\n", "  Final Capital (from 100): 120.26\n", "  Avg Arithmetic Return/Trade: 0.4980%\n", "  Avg Geometric Return/Trade: 0.4509%\n", "  Std Dev Return/Trade: 3.1338%\n", "  Sharpe Ratio (annualized, approx): 2.04\n", "PLTU:\n", "  Trades: 38\n", "  Total Compounded Return: 20.57%\n", "  Final Capital (from 100): 120.57\n", "  Avg Arithmetic Return/Trade: 0.5884%\n", "  Avg Geometric Return/Trade: 0.4934%\n", "  Std Dev Return/Trade: 4.5551%\n", "  Sharpe Ratio (annualized, approx): 1.60\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import time, timedelta, datetime\n", "import pytz\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy.stats.mstats import gmean\n", "\n", "# --- Parameters ---\n", "# List of tickers to analyze\n", "tickers = [\"TQQQ\", \"TSLL\", \"NVDL\", \"SOXL\", \"NVDA\", \"TSLA\", \"PLTR\", \"PLTU\"]  # Add or modify tickers as needed\n", "start_dt = eastern_tz.localize(datetime(2025, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 4, 4))\n", "\n", "# Strategy parameters\n", "profit_r_target = 10\n", "stop_pct_atr = 0.10\n", "stop_pct_atr_viz_label = f\"{stop_pct_atr*100:.0f}%\"\n", "imbalance_ratio_val = 1.5\n", "initial_capital = 100  # Representing 100% or $100 for visualization\n", "\n", "# --- Functions ---\n", "\n", "def calculate_and_add_pct_return(df):\n", "    \"\"\"Calculates and adds 'pct_return' column to the DataFrame.\"\"\"\n", "    if df is None or df.empty:\n", "        print(\"Warning: DataFrame is None or empty, skipping pct_return calculation.\")\n", "        return df\n", "\n", "    required_cols = ['entry', 'exit', 'direction']\n", "    if not all(col in df.columns for col in required_cols):\n", "        print(f\"Warning: DataFrame missing required columns ({required_cols}). Cannot calculate pct_return.\")\n", "        df['pct_return'] = np.nan\n", "        return df\n", "\n", "    # Ensure numeric types\n", "    df['entry'] = pd.to_numeric(df['entry'], errors='coerce')\n", "    df['exit'] = pd.to_numeric(df['exit'], errors='coerce')\n", "    df.dropna(subset=['entry', 'exit', 'direction'], inplace=True)\n", "\n", "    if df.empty:\n", "        print(\"Warning: DataFrame became empty after cleaning NAs for pct_return calculation.\")\n", "        df['pct_return'] = np.nan\n", "        return df\n", "\n", "    # Vectorized calculation for efficiency\n", "    long_mask = df['direction'].str.lower() == 'long'\n", "    short_mask = df['direction'].str.lower() == 'short'\n", "\n", "    # Calculate returns, handle potential division by zero if entry price is 0\n", "    df['pct_return'] = 0.0  # Initialize\n", "    df.loc[long_mask, 'pct_return'] = (df.loc[long_mask, 'exit'] - df.loc[long_mask, 'entry']) / df.loc[long_mask, 'entry']\n", "    df.loc[short_mask, 'pct_return'] = (df.loc[short_mask, 'entry'] - df.loc[short_mask, 'exit']) / df.loc[short_mask, 'entry']\n", "\n", "    # Replace potential inf/-inf resulting from division by zero or near-zero entry price\n", "    df['pct_return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n", "\n", "    print(f\"Successfully calculated 'pct_return' for {len(df)} trades.\")\n", "    return df\n", "\n", "def add_cumulative_return_plot(df, ticker, color, initial_capital=1.0, **kwargs):\n", "    \"\"\"Plots compounded cumulative return starting from initial_capital.\"\"\"\n", "    required_cols = ['date', 'pct_return']\n", "    if df is not None and not df.empty and all(col in df.columns for col in required_cols):\n", "        # Ensure pct_return is numeric and handle NaNs\n", "        df['pct_return'] = pd.to_numeric(df['pct_return'], errors='coerce')\n", "        df.dropna(subset=['pct_return'], inplace=True)\n", "\n", "        if not df.empty:\n", "            df_sorted = df.sort_values('date')\n", "            # Ensure date is datetime for plotting\n", "            plot_dates = pd.to_datetime(df_sorted['date'])\n", "\n", "            # Calculate cumulative growth factor (1+r1)*(1+r2)*...\n", "            df_sorted['cumulative_growth'] = (1 + df_sorted['pct_return']).cumprod()\n", "            plot_values = initial_capital * df_sorted['cumulative_growth']\n", "\n", "            plt.plot(plot_dates, plot_values, label=f\"{ticker} - {len(df)} trades\", color=color, **kwargs)\n", "            # Add a starting point at initial capital before the first trade\n", "            if not plot_dates.empty:\n", "                start_date = plot_dates.iloc[0] - pd.Timedelta(days=1)\n", "                plt.plot([start_date, plot_dates.iloc[0]], [initial_capital, plot_values.iloc[0]], \n", "                         linestyle=kwargs.get('linestyle', '-'), color=color, alpha=kwargs.get('alpha', 1.0))\n", "        else:\n", "            print(f\"Skipping plot for {ticker}: No valid Pct Return data after cleaning.\")\n", "    else:\n", "        print(f\"Skipping plot for {ticker}: DataFrame is None, empty, or missing required columns ({required_cols}).\")\n", "\n", "def add_pct_return_dist_plot(df, ticker, color, **kwargs):\n", "    \"\"\"Adds percentage return distribution plot for a given ticker.\"\"\"\n", "    if df is not None and not df.empty and 'pct_return' in df.columns:\n", "        # Ensure pct_return is numeric and drop NaNs\n", "        df['pct_return'] = pd.to_numeric(df['pct_return'], errors='coerce')\n", "        df.dropna(subset=['pct_return'], inplace=True)\n", "        if not df.empty:\n", "            # Plotting as percentage for easier interpretation\n", "            sns.kdeplot(df['pct_return'] * 100, label=f\"{ticker} - {len(df)} trades\", fill=True, color=color, **kwargs)\n", "        else:\n", "            print(f\"Skipping KDE plot for {ticker}: No valid Pct Return data after cleaning.\")\n", "    else:\n", "        print(f\"Skipping KDE plot for {ticker}: DataFrame is None, empty, or missing 'pct_return'.\")\n", "\n", "def print_pct_return_summary(df, ticker, initial_capital=100):\n", "    \"\"\"Prints summary statistics for percentage returns.\"\"\"\n", "    required_cols = ['date', 'pct_return']\n", "    if df is not None and not df.empty and all(col in df.columns for col in required_cols):\n", "        # Ensure pct_return is numeric and drop NaNs\n", "        df['pct_return'] = pd.to_numeric(df['pct_return'], errors='coerce')\n", "        df.dropna(subset=['pct_return'], inplace=True)\n", "\n", "        if not df.empty:\n", "            count = len(df)\n", "            # Total Compounded Return\n", "            total_growth_factor = (1 + df['pct_return']).prod()\n", "            total_compounded_return_pct = (total_growth_factor - 1) * 100\n", "            final_capital = initial_capital * total_growth_factor\n", "\n", "            # Average (Arithmetic) Return per trade\n", "            avg_arithmetic_return_pct = df['pct_return'].mean() * 100\n", "\n", "            # Geometric Mean Return per trade (better for compounding)\n", "            valid_for_gmean = df['pct_return'][df['pct_return'] > -1.0] + 1\n", "            if len(valid_for_gmean) > 0:\n", "                avg_geometric_return_pct = (gmean(valid_for_gmean) - 1) * 100\n", "            else:\n", "                avg_geometric_return_pct = float('nan')\n", "\n", "            # Standard Deviation of Returns\n", "            std_dev_pct = df['pct_return'].std() * 100\n", "\n", "            # <PERSON> Ratio (Annualized, using arithmetic mean)\n", "            days_span = (pd.to_datetime(df['date']).max() - pd.to_datetime(df['date']).min()).days\n", "            trades_per_year = count / (days_span / 365.25) if days_span > 0 else 252\n", "            sharpe = (df['pct_return'].mean() / df['pct_return'].std()) * np.sqrt(trades_per_year) if df['pct_return'].std() > 0 else 0\n", "\n", "            print(f\"{ticker}:\")\n", "            print(f\"  Trades: {count}\")\n", "            print(f\"  Total Compounded Return: {total_compounded_return_pct:.2f}%\")\n", "            print(f\"  Final Capital (from {initial_capital}): {final_capital:.2f}\")\n", "            print(f\"  Avg Arithmetic Return/Trade: {avg_arithmetic_return_pct:.4f}%\")\n", "            print(f\"  Avg Geometric Return/Trade: {avg_geometric_return_pct:.4f}%\")\n", "            print(f\"  Std Dev Return/Trade: {std_dev_pct:.4f}%\")\n", "            print(f\"  <PERSON> (annualized, approx): {sharpe:.2f}\")\n", "        else:\n", "            print(f\"{ticker}: No valid trade data for statistics.\")\n", "    else:\n", "        print(f\"{ticker}: No data or missing required columns ({required_cols}).\")\n", "\n", "# --- Main Execution Logic ---\n", "def main():\n", "    plt.style.use('seaborn-v0_8-darkgrid')\n", "    \n", "    # Dictionary to store results for each ticker\n", "    results = {}\n", "    \n", "    # Colors for plotting\n", "    colors = ['green', 'red', 'blue', 'purple', 'orange', 'brown', 'pink', 'gray', 'olive', 'cyan']\n", "    \n", "    print(f\"Analyzing {len(tickers)} tickers...\")\n", "    \n", "    # Process each ticker\n", "    for i, ticker in enumerate(tickers):\n", "        print(f\"\\nProcessing {ticker}...\")\n", "        \n", "        # Step 1: Get data\n", "        ticker_data = get_per_minute_data(ticker, start_dt - timedelta(days=20), end_dt)\n", "        if ticker_data is None or ticker_data.empty:\n", "            print(f\"Skipping {ticker}: No data available\")\n", "            continue\n", "        \n", "        # Step 2: Calculate ATR\n", "        df_merged = calculate_daily_atr(ticker_data)\n", "        if df_merged is None or df_merged.empty:\n", "            print(f\"Skipping {ticker}: Could not calculate ATR\")\n", "            continue\n", "        \n", "        # Step 3: Run strategy\n", "        print(f\"Running ORB ATR Imbalance Strategy for {ticker} (Stop: {stop_pct_atr_viz_label} ATR, Ratio: {imbalance_ratio_val})...\")\n", "        df_results = run_orb_atr_imbalance_strategy(\n", "            df_merged,\n", "            stop_pct=stop_pct_atr,\n", "            profit_r=profit_r_target,\n", "            imbalance_ratio=imbalance_ratio_val\n", "        )\n", "        print(f\"Generated {len(df_results)} trades for {ticker}.\")\n", "        \n", "        # Step 4: Calculate percentage returns\n", "        df_results = calculate_and_add_pct_return(df_results)\n", "        \n", "        # Store results\n", "        results[ticker] = df_results\n", "    \n", "    # Plot cumulative returns\n", "    plt.figure(figsize=(14, 7))\n", "    for i, (ticker, df) in enumerate(results.items()):\n", "        color_idx = i % len(colors)\n", "        add_cumulative_return_plot(df, ticker, colors[color_idx], initial_capital=initial_capital, linewidth=2.0, alpha=0.9)\n", "    \n", "    plt.title(\"Cumulative Growth Factor Comparison (100% Capital per Trade, Compounded)\")\n", "    plt.xlabel(\"Date\")\n", "    plt.ylabel(f\"Portfolio Value (Starting from {initial_capital})\")\n", "    plt.yscale('log')\n", "    plt.legend(fontsize=10)\n", "    plt.grid(True, which='both', linestyle='--', alpha=0.6)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Plot PnL distribution\n", "    plt.figure(figsize=(10, 6))\n", "    for i, (ticker, df) in enumerate(results.items()):\n", "        color_idx = i % len(colors)\n", "        add_pct_return_dist_plot(df, ticker, colors[color_idx], alpha=0.4, linewidth=2.0)\n", "    \n", "    plt.axvline(0, color='gray', linestyle='--', lw=1)\n", "    plt.title(\"Distribution of Trade PnL (Percentage Return per Trade)\")\n", "    plt.xlabel(\"PnL (%)\")\n", "    plt.ylabel(\"Density\")\n", "    plt.legend(fontsize=10)\n", "    plt.grid(True, linestyle='--', alpha=0.6)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print summary statistics\n", "    print(\"\\n--- Summary Statistics (Percentage Returns) ---\")\n", "    for ticker, df in results.items():\n", "        print_pct_return_summary(df, ticker, initial_capital=initial_capital)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# df_orb_simple = run_orb_strategy_simple(df_merged, profit_r=profit_r_target)\n", "# df_orb_simple.info()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# # OFAT with imbalance_ratio\n", "\n", "# # Import necessary libraries (ensure these are already imported)\n", "# import pandas as pd\n", "# import numpy as np\n", "# from datetime import time, timedelta, datetime\n", "# import pytz\n", "# import yfinance as yf\n", "# import matplotlib.pyplot as plt\n", "# import seaborn as sns\n", "# import dotenv\n", "# import os\n", "\n", "# # Assuming necessary functions are defined from previous steps:\n", "# # get_yfinance_daily_data, get_per_minute_data, calculate_daily_atr,\n", "# # run_orb_strategy_simple, run_orb_atr_strategy, run_orb_atr_regime_based,\n", "# # run_orb_atr_imbalance_strategy\n", "\n", "# # --- Basic Setup & Data Loading ---\n", "# # (Assuming start_dt, end_dt are defined)\n", "# # (Assuming tqqq_data is loaded)\n", "\n", "# print(\"Using previously calculated df_with_atr.\")\n", "# # df_with_atr = calculate_daily_atr(tqqq_data) # Recalculate only if necessary\n", "# if 'df_with_atr' not in locals() or df_with_atr.empty:\n", "#      print(\"Recalculating ATR...\")\n", "#      df_with_atr = calculate_daily_atr(tqqq_data)\n", "\n", "# if not isinstance(df_with_atr.index, pd.DatetimeIndex):\n", "#      raise TypeError(\"df_with_atr must have a DatetimeIndex\")\n", "\n", "# # Filter df_with_atr to the analysis range *before* running strategies\n", "# print(f\"Filtering analysis data to range: {start_dt} to {end_dt}\")\n", "# df_analysis_imbalance = df_with_atr.loc[start_dt:end_dt].copy()\n", "# if df_analysis_imbalance.empty:\n", "#     raise ValueError(\"No data available for the specified analysis period.\")\n", "\n", "\n", "# # --- Define Parameters for Refined Imbalance Test ---\n", "# stop_pct_test = 0.10  # Fixed stop loss percentage\n", "# profit_r_test = 10    # Fixed profit target multiple\n", "\n", "# # Define the new, refined list of ratios\n", "# ratios_to_test = [round(x * 0.1, 1) for x in range(10, 21)] # Creates [1.0, 1.1, ..., 1.9, 2.0]\n", "# # Alternative explicit list: ratios_to_test = [1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0]\n", "\n", "# print(f\"Testing Imbalance Ratios: {ratios_to_test}\")\n", "\n", "# # --- Run Simulations for Different Ratios ---\n", "# imbalance_results = {} # Dictionary to store results for each ratio\n", "# summary_stats = []     # List to store summary stats for table display\n", "\n", "# print(\"\\n--- Running Refined Imbalance Ratio Simulations ---\")\n", "# for ratio in ratios_to_test:\n", "#     print(f\"\\nSimulating Imbalance Ratio: {ratio:.1f}\")\n", "#     results_df = run_orb_atr_imbalance_strategy(\n", "#         df_analysis_imbalance, # Use the analysis dataframe\n", "#         stop_pct=stop_pct_test,\n", "#         profit_r=profit_r_test,\n", "#         imbalance_ratio=ratio\n", "#     )\n", "#     imbalance_results[ratio] = results_df\n", "\n", "#     # Store summary stats\n", "#     if not results_df.empty:\n", "#         avg_r = results_df['pnl_r'].mean()\n", "#         sum_r = results_df['pnl_r'].sum()\n", "#         count = len(results_df)\n", "#         summary_stats.append({'Ratio': ratio, 'Trades': count, 'Avg R': avg_r, 'Total R': sum_r})\n", "#         print(f\"  Ratio {ratio:.1f}: Trades={count}, Avg R={avg_r:.4f}, Total R={sum_r:.2f}\")\n", "#     else:\n", "#         summary_stats.append({'Ratio': ratio, 'Trades': 0, 'Avg R': np.nan, 'Total R': 0})\n", "#         print(f\"  Ratio {ratio:.1f}: No trades generated.\")\n", "\n", "# # --- Run Baseline ATR Strategy for Comparison ---\n", "# print(\"\\nRunning Baseline ATR Strategy (Stop Pct = 0.10)...\")\n", "# df_orb_atr_baseline = run_orb_atr_strategy(\n", "#     df_analysis_imbalance,\n", "#     stop_pct=stop_pct_test,\n", "#     profit_r=profit_r_test\n", "# )\n", "# if not df_orb_atr_baseline.empty:\n", "#     avg_r_base = df_orb_atr_baseline['pnl_r'].mean()\n", "#     sum_r_base = df_orb_atr_baseline['pnl_r'].sum()\n", "#     count_base = len(df_orb_atr_baseline)\n", "#     print(f\"  Baseline ATR (0.10): Trades={count_base}, Avg R={avg_r_base:.4f}, Total R={sum_r_base:.2f}\")\n", "#     # Add baseline to summary stats\n", "#     summary_stats.append({'Ratio': '<PERSON>ine (No Filter)', 'Trades': count_base, 'Avg R': avg_r_base, 'Total R': sum_r_base})\n", "# else:\n", "#      print(\"  Baseline ATR (0.10): No trades generated.\")\n", "#      summary_stats.append({'Ratio': '<PERSON><PERSON> (No Filter)', 'Trades': 0, 'Avg R': np.nan, 'Total R': 0})\n", "\n", "# # --- Di<PERSON><PERSON> Summary Table ---\n", "# summary_df = pd.DataFrame(summary_stats).set_index('Ratio')\n", "# print(\"\\n--- Imbalance Ratio Performance Summary ---\")\n", "# print(summary_df.to_string(formatters={'Avg R': '{:.4f}'.format, 'Total R': '{:.2f}'.format}))\n", "\n", "\n", "# # --- Plot<PERSON> Comp<PERSON> ---\n", "\n", "# # Cumulative R Plot\n", "# plt.figure(figsize=(14, 8)) # Slightly larger figure\n", "\n", "# # Plot baseline ATR strategy first\n", "# add_cumulative_r_plot(df_orb_atr_baseline, f'Baseline ORB ATR (Stop {stop_pct_test:.2f})', linewidth=1.5, linestyle='--', color='black', alpha=0.8)\n", "\n", "# # Plot each imbalance ratio result\n", "# # Use a colormap to distinguish lines if there are many\n", "# num_ratios = len(imbalance_results)\n", "# # Ensure matplotlib is imported: import matplotlib.pyplot as plt\n", "# # Check if 'viridis' colormap exists, otherwise use a default like 'tab10'\n", "# try:\n", "#     cmap = plt.get_cmap('viridis', num_ratios)\n", "# except ValueError:\n", "#     cmap = plt.get_cmap('tab10', num_ratios)\n", "\n", "\n", "# ratio_keys = sorted(imbalance_results.keys()) # Plot in order\n", "# for i, ratio in enumerate(ratio_keys):\n", "#     result_df = imbalance_results[ratio]\n", "#     # Check if cmap is callable or provides colors via indexing\n", "#     try:\n", "#         line_color = cmap(i / (num_ratios -1 if num_ratios > 1 else 1)) # Normalize index for colormap\n", "#     except TypeError: # Handle case where cmap might return a list directly\n", "#         line_color = cmap.colors[i % len(cmap.colors)] if hasattr(cmap, 'colors') else None\n", "\n", "\n", "#     add_cumulative_r_plot(result_df, f'Ratio {ratio:.1f}', linewidth=2.0, alpha=0.8, color=line_color)\n", "\n", "\n", "# plt.title(f\"Cumulative PnL (R) - ORB ATR Imbalance Ratio Comparison (Stop={stop_pct_test:.2f})\")\n", "# plt.xlabel(\"Date\")\n", "# plt.ylabel(\"Cumulative R\")\n", "# plt.grid(True, linestyle='--', alpha=0.6)\n", "# plt.legend(loc='upper left', bbox_to_anchor=(1, 1)) # Move legend outside plot\n", "# plt.tight_layout(rect=[0, 0, 0.85, 1]) # Adjust layout to make space for legend\n", "# plt.show()\n", "\n", "# # PnL Distribution Plot (May get cluttered with many lines)\n", "# plt.figure(figsize=(12, 7))\n", "\n", "# # Plot baseline ATR distribution\n", "# add_pnl_dist_plot(df_orb_atr_baseline, f'Baseline ORB ATR (Stop {stop_pct_test:.2f})', alpha=0.25, linewidth=1.5, linestyle='--', color='black')\n", "\n", "# # Plot selected imbalance ratio distributions (e.g., baseline + a few interesting ones)\n", "# # Plotting all might be too messy. Let's plot 1.0, 1.5, 2.0 for example\n", "# selected_ratios_for_dist = [1.0, 1.5, 2.0]\n", "# for ratio in selected_ratios_for_dist:\n", "#     if ratio in imbalance_results:\n", "#          add_pnl_dist_plot(imbalance_results[ratio], f'Imbalance Ratio {ratio:.1f}', alpha=0.4, linewidth=1.5)\n", "\n", "# # Or plot all if preferred, using the colormap logic again:\n", "# # for i, ratio in enumerate(ratio_keys):\n", "# #     result_df = imbalance_results[ratio]\n", "# #     try: line_color = cmap(i / (num_ratios -1 if num_ratios > 1 else 1))\n", "# #     except TypeError: line_color = cmap.colors[i % len(cmap.colors)] if hasattr(cmap, 'colors') else None\n", "# #     add_pnl_dist_plot(result_df, f'Ratio {ratio:.1f}', alpha=0.35, linewidth=1.5, color=line_color)\n", "\n", "# plt.axvline(0, color='gray', linestyle='--', lw=1)\n", "# plt.title(f\"Distribution of Trade PnL (R) - Selected Imbalance Ratios (Stop={stop_pct_test:.2f})\")\n", "# plt.xlabel(\"PnL (R)\")\n", "# plt.ylabel(\"Density\")\n", "# plt.xlim(-2, profit_r_test + 1)\n", "# plt.legend()\n", "# plt.grid(True, linestyle='--', alpha=0.6)\n", "# plt.tight_layout()\n", "# plt.show()\n", "\n", "# print(\"\\nRefined imbalance ratio comparison finished.\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}