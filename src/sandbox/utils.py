import os
import pandas as pd
import functools
import re
from typing import Callable, Any, Dict
import pytz

EST = pytz.timezone("America/New_York")

def df_cache_datetime_est(func: Callable) -> Callable:
    """
    Decorator to cache pandas DataFrame results to CSV,
    enforcing DatetimeIndex in EST timezone.
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Dict[str, Any]) -> pd.DataFrame:
        module_name = func.__module__.replace('.', '/')
        func_name = func.__name__

        cache_base_dir = os.environ.get('DF_CACHE_DIR', 'cache/sandbox')
        cache_dir = os.path.join(cache_base_dir, module_name)
        os.makedirs(cache_dir, exist_ok=True)

        # Build cache filename
        params_str = [f"arg{i}_{str(arg)}" for i, arg in enumerate(args)]
        params_str += [f"{k}_{str(v)}" for k, v in sorted(kwargs.items())]
        params_key = re.sub(r'[^\w\-\.]', '_', "_".join(params_str))[:100]
        cache_file = os.path.join(cache_dir, f"{func_name}_{params_key}.csv")

        if os.path.exists(cache_file):
            print(f"Cache hit: Loading from {cache_file}")
            df = pd.read_csv(cache_file, index_col=0, parse_dates=True)
            if not isinstance(df.index, pd.DatetimeIndex):
                raise ValueError("Cached file does not have a DatetimeIndex.")
            df.index = df.index.tz_convert(EST) if df.index.tzinfo else df.index.tz_localize(EST)
            return df

        print(f"Cache miss: Executing function {func_name}")
        df = func(*args, **kwargs)
        if not isinstance(df, pd.DataFrame):
            raise TypeError("Function must return a pandas DataFrame.")
        if not isinstance(df.index, pd.DatetimeIndex):
            raise ValueError("Returned DataFrame must have a DatetimeIndex.")

        df.index = df.index.tz_convert(EST) if df.index.tzinfo else df.index.tz_localize(EST)
        df.to_csv(cache_file)
        print(f"Cached result to {cache_file}")
        return df

    return wrapper

def df_cache(func: Callable) -> Callable:
    """
    Decorator to cache pandas DataFrame results to CSV without
    modifying or enforcing the index type or timezone.
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Dict[str, Any]) -> pd.DataFrame:
        module_name = func.__module__.replace('.', '/')
        func_name = func.__name__

        cache_base_dir = os.environ.get('DF_CACHE_DIR', 'cache/sandbox')
        cache_dir = os.path.join(cache_base_dir, module_name)
        os.makedirs(cache_dir, exist_ok=True)

        # Build cache filename
        params_str = [f"arg{i}_{str(arg)}" for i, arg in enumerate(args)]
        params_str += [f"{k}_{str(v)}" for k, v in sorted(kwargs.items())]
        params_key = re.sub(r'[^\w\-\.]', '_', "_".join(params_str))[:100]
        cache_file = os.path.join(cache_dir, f"{func_name}_{params_key}.csv")

        if os.path.exists(cache_file):
            print(f"Cache hit: Loading from {cache_file}")
            return pd.read_csv(cache_file, index_col=0)

        print(f"Cache miss: Executing function {func_name}")
        df = func(*args, **kwargs)
        if not isinstance(df, pd.DataFrame):
            raise TypeError("Function must return a pandas DataFrame.")

        df.to_csv(cache_file)
        print(f"Cached result to {cache_file}")
        return df

    return wrapper
