{"cells": [{"cell_type": "code", "execution_count": 11, "id": "ae49f670", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Illiquidity Premium Backtest – **Daily K‑Lines (robust)**\n", "========================================================\n", "A Jupyter‑friendly notebook that now\n", "\n", "* **Skips special/odd pairs** such as expiries (`ETHUSDT_251226`) or\n", "  settlement markers (`XYZUSDTSETTLED`) and anything containing an\n", "  underscore.\n", "* Gracefully **drops symbols with no data** in the requested window so\n", "  those “[WARN] … Neither `start` nor `end` can be NaT” messages vanish.\n", "* Continues to de‑duplicate on **base asset**.\n", "\n", "Copy‑paste into a notebook—the `# %%` delimiters create runnable cells.\n", "Feel free to tweak the exclusion logic or add further error handling.\n", "\"\"\"\n", "\n", "# %%\n", "# Imports & Config\n", "from __future__ import annotations\n", "\n", "import re\n", "from datetime import datetime\n", "from pathlib import Path\n", "from typing import Dict, List\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pytz\n", "from bidask import edge_rolling\n", "\n", "from marketdata.binance_klines_market_data import BinanceKlinesHistMarketData\n", "\n", "# --- user‑adjustable parameters ---------------------------------------------\n", "BASE_DIR = Path(\"~/market_data/binance\").expanduser()  # CSV root\n", "MARKET   = \"futures\"        # \"spot\" or \"futures\"\n", "INTERVAL = \"1d\"             # daily klines\n", "\n", "START    = \"2021-01-01\"      # inclusive, US/Eastern\n", "END      = \"2025-03-31\"      # inclusive, US/Eastern\n", "\n", "WINDOW_DAYS  = 30           # spread history window\n", "DECILE       = 0.025         # top/bottom 10 %\n", "QUOTE_TOKENS = (\"USDT\", \"BUSD\", \"FDUSD\", \"USDC\", \"TUSD\", \"DAI\")\n", "\n", "# Exclude symbols that match this regex (e.g. expiries, SETTLED pairs)\n", "EXCLUDE_REGEX = re.compile(r\"_|SETTLED$\")  # underscore OR ends with SETTLED\n", "\n", "# %%\n", "# Helper functions – universe & data handling\n", "\n", "def _base_asset(symbol: str) -> str:\n", "    for q in QUOTE_TOKENS:\n", "        if symbol.endswith(q):\n", "            return symbol[: -len(q)]\n", "    return symbol\n", "\n", "\n", "def is_valid_symbol(sym: str) -> bool:\n", "    \"\"\"Return *False* for quarterly/settled contracts, or weird prefixes.\"\"\"\n", "    # Skip anything with underscore or ending in SETTLED, and tokens that start with a digit\n", "    return not (EXCLUDE_REGEX.search(sym) or sym[0].isdigit())\n", "\n", "\n", "def filter_universe(symbols: List[str]) -> List[str]:\n", "    \"\"\"Remove special pairs and keep one symbol per base asset.\"\"\"\n", "    seen = set()\n", "    uniq: List[str] = []\n", "    for sym in symbols:\n", "        if not is_valid_symbol(sym):\n", "            continue\n", "        base = _base_asset(sym)\n", "        if base not in seen:\n", "            seen.add(base)\n", "            uniq.append(sym)\n", "    return uniq\n", "\n", "\n", "def fetch_price_data(md, symbols: List[str], start, end) -> Dict[str, pd.DataFrame]:\n", "    \"\"\"Load OHLCV for each symbol – robust to corrupt CSVs or missing columns.\"\"\"\n", "    out: Dict[str, pd.DataFrame] = {}\n", "    for sym in symbols:\n", "        try:\n", "            df = md.gather_historical_data(sym, start, end)\n", "            if df.empty:\n", "                raise ValueError(\"no data in window\")\n", "            if \"open\" not in df.columns or \"high\" not in df.columns:\n", "                raise KeyError(\"missing OHLC columns\")\n", "\n", "            idx_min, idx_max = df.index.min(), df.index.max()\n", "            if pd.isna(idx_min) or pd.isna(idx_max):\n", "                raise ValueError(\"index missing\")\n", "\n", "            all_days = pd.date_range(\n", "                start=idx_min.floor(\"D\"),\n", "                end=idx_max.ceil(\"D\"),\n", "                freq=\"1D\",\n", "                tz=df.index.tz,\n", "            )\n", "            df = (\n", "                df.reindex(all_days, method=\"ffill\")\n", "                  [[\"open\", \"high\", \"low\", \"close\", \"volume\"]]\n", "            )\n", "            out[sym] = df\n", "        except Exception as e:\n", "            print(f\"[SKIP] {sym}: {e}\")\n", "    return out\n", "\n", "\n", "def compute_spread(df: pd.DataFrame) -> pd.Series:\n", "    return edge_rolling(df[[\"open\", \"high\", \"low\", \"close\"]], window=WINDOW_DAYS)\n", "\n", "\n", "def build_weights(spread: pd.DataFrame) -> pd.DataFrame:\n", "    rank = spread.rank(pct=True, axis=1)\n", "    long_m  = rank >= 1 - DECILE\n", "    short_m = rank <= DECILE\n", "\n", "    n_long  = long_m.sum(axis=1).replace(0, np.nan)\n", "    n_short = short_m.sum(axis=1).replace(0, np.nan)\n", "\n", "    long_w  =  long_m.div(n_long,  axis=0) * 0.5\n", "    short_w = -short_m.div(n_short, axis=0) * 0.5\n", "\n", "    return (long_w + short_w).shift(1).fillna(0)\n", "\n", "\n", "def backtest(price_data: Dict[str, pd.DataFrame]):\n", "    closes  = pd.concat({s: d[\"close\"] for s, d in price_data.items()}, axis=1).sort_index()\n", "    returns = closes.pct_change().fillna(0)\n", "\n", "    spread  = pd.concat({s: compute_spread(d) for s, d in price_data.items()}, axis=1)\n", "    spread  = spread.reindex_like(closes)\n", "\n", "    weights = build_weights(spread)\n", "    port_r  = (weights * returns).sum(axis=1)\n", "    equity  = (1 + port_r).cumprod()\n", "    return port_r, equity\n", "\n", "# %%\n", "# Load data & run the strategy\n", "est = pytz.timezone(\"US/Eastern\")\n", "start_dt = est.localize(datetime.fromisoformat(START))\n", "end_dt   = est.localize(datetime.fromisoformat(END))\n", "\n", "# Use *no* pre‑load to avoid pulling every CSV into memory up front.\n", "# This also sidesteps corrupt files that would otherwise halt instantiation.\n", "md = BinanceKlinesHistMarketData(\n", "    history_back_months=120,\n", ")\n"]}, {"cell_type": "code", "execution_count": 12, "id": "b8696b36", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Universe size after cleaning: 495 symbols\n", "[SKIP] AAVEUSDC: no data in window\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[SKIP] AGTUSDT: no data in window\n", "[SKIP] AIOTUSDT: no data in window\n", "[SKIP] ALPINEUSDT: no data in window\n", "[SKIP] ASRUSDT: no data in window\n", "[SKIP] ATHUSDT: no data in window\n", "[SKIP] AUSDT: no data in window\n", "[SKIP] AWEUSDT: no data in window\n", "[SKIP] B2USDT: no data in window\n", "[SKIP] BABYUSDT: no data in window\n", "[SKIP] BANKUSDT: no data in window\n", "[SKIP] BDXNUSDT: no data in window\n", "[SKIP] BUSDT: no data in window\n", "[SKIP] DEEPUSDT: no data in window\n", "[SKIP] DMCUSDT: no data in window\n", "[SKIP] DOLOUSDT: no data in window\n", "[SKIP] DOODUSDT: no data in window\n", "[SKIP] EPTUSDT: no data in window\n", "[SKIP] FHEUSDT: no data in window\n", "[SKIP] FISUSDT: no data in window\n", "[SKIP] FORTHUSDT: no data in window\n", "[SKIP] FUSDT: no data in window\n", "[SKIP] HAEDALUSDT: no data in window\n", "[SKIP] HOMEUSDT: no data in window\n", "[SKIP] HUMAUSDT: no data in window\n", "[SKIP] HUSDT: no data in window\n", "[SKIP] HYPERUSDT: no data in window\n", "[SKIP] HYPEUSDT: no data in window\n", "[SKIP] INITUSDT: no data in window\n", "[SKIP] JSTUSDT: no data in window\n", "[SKIP] KERNELUSDT: no data in window\n", "[SKIP] LAUSDT: no data in window\n", "[SKIP] LENDUSDT: no data in window\n", "[SKIP] MEMEFIUSDT: no data in window\n", "[SKIP] MERLUSDT: no data in window\n", "[SKIP] MILKUSDT: no data in window\n", "[SKIP] MYXUSDT: no data in window\n", "[SKIP] NEWTUSDT: no data in window\n", "[SKIP] NXPCUSDT: no data in window\n", "[SKIP] OBOLUSDT: no data in window\n", "[SKIP] OGUSDT: no data in window\n", "[SKIP] OLUSDT: no data in window\n", "[SKIP] PORT3USDT: no data in window\n", "[SKIP] PROMPTUSDT: no data in window\n", "[SKIP] PUFFERUSDT: no data in window\n", "[SKIP] PUMPBTCUSDT: no data in window\n", "[SKIP] PUMPUSDT: no data in window\n", "[SKIP] PUNDIXUSDT: no data in window\n", "[SKIP] RESOLVUSDT: no data in window\n", "[SKIP] SAHARAUSDT: no data in window\n", "[SKIP] SIGNUSDT: no data in window\n", "[SKIP] SKATEUSDT: no data in window\n", "[SKIP] SKYAIUSDT: no data in window\n", "[SKIP] SOONUSDT: no data in window\n", "[SKIP] SOPHUSDT: no data in window\n", "[SKIP] SPKUSDT: no data in window\n", "[SKIP] SQDUSDT: no data in window\n", "[SKIP] STOUSDT: no data in window\n", "[SKIP] SXTUSDT: no data in window\n", "[SKIP] SYRUPUSDT: no data in window\n", "[SKIP] TAIKOUSDT: no data in window\n", "[SKIP] WCTUSDT: no data in window\n", "[SKIP] XCNUSDT: no data in window\n", "[SKIP] ZKJUSDT: no data in window\n", "Fetched price data for 431 symbols – running backtest…\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_436878/1978701671.py:130: FutureWarning: The default fill_method='pad' in DataFrame.pct_change is deprecated and will be removed in a future version. Either fill in any non-leading NA values prior to calling pct_change or specify 'fill_method=None' to not fill NA values.\n", "  returns = closes.pct_change().fillna(0)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["CAGR   :  0.83%\n", "Sharpe :   0.23\n"]}], "source": ["universe_raw = md.list_all_pairs()\n", "universe     = filter_universe(universe_raw)\n", "print(f\"Universe size after cleaning: {len(universe)} symbols\")\n", "\n", "price_data = fetch_price_data(md, universe, start_dt, end_dt)\n", "print(f\"Fetched price data for {len(price_data)} symbols – running backtest…\")\n", "\n", "port_ret, equity = backtest(price_data)\n", "\n", "# %%\n", "# Plot & stats\n", "fig, ax = plt.subplots(figsize=(10, 4))\n", "equity.plot(ax=ax, lw=1.2)\n", "ax.set_ylabel(\"Equity\")\n", "ax.set_title(\"Illiquidity Premium – Daily\")\n", "ax.set_yscale(\"log\")\n", "ax.grid(True, which=\"both\", ls=\":\")\n", "plt.show()\n", "\n", "cagr   = equity.iloc[-1] ** (365 / len(port_ret)) - 1\n", "sharpe = port_ret.mean() / port_ret.std(ddof=0) * np.sqrt(365)\n", "\n", "print(f\"CAGR   : {cagr:6.2%}\\nSharpe : {sharpe:6.2f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}