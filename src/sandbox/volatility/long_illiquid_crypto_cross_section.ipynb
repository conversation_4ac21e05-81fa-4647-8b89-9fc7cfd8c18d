{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import ccxt\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime, timedelta\n", "\n", "from bidask import edge_rolling           # same helper as before\n", "# ----------------------------------------------------------------\n", "# 1. CONFIG\n", "START       = pd.Timestamp(\"2025-01-01\", tz=\"UTC\")\n", "END         = pd.Timestamp(\"2025-06-06\", tz=\"UTC\")\n", "TIMEFRAME   = \"1d\"\n", "WINDOW_DAYS = 30               # rolling window for bid–ask estimator\n", "DECILE      = 0.10\n", "REB_FREQ    = \"D\"              # rebalance every day at the close\n", "BASES       = [                # the token universe you care about\n", "    'BTC','ETH','XRP','BNB','SOL','DOGE','TRX','ADA','SUI','BCH','LINK',\n", "    'AVAX','SHIB','TON','LTC','HBAR','DOT','XMR','CRO','NEAR','ETC','ONDO',\n", "    'VET','KAS','FET','ARB','ATOM','FIL','IMX','SEI','JTO','STX','PENDLE',\n", "    'PYTH','CAKE','WLD','ENS','HNT','RUNE','LDO','FLOW','XTZ'\n", "]\n", "# ----------------------------------------------------------------\n", "# 2. EXCHANGES & MARKET SELECTION\n", "\n", "spot_ex   = ccxt.binanceus({'enableRateLimit': True})\n", "fut_ex    = ccxt.binanceusdm({'enableRateLimit': True})\n", "\n", "spot_markets = spot_ex.fetch_markets()\n", "fut_markets  = fut_ex.fetch_markets()\n", "\n", "def _volume(m):                         # helper: pull 24-h quote volume, safely\n", "    info = m.get('info', {})\n", "    for fld in ('quoteVolume', 'volume', 'baseVolume'):\n", "        v = info.get(fld)\n", "        if v not in (None, '', 0):\n", "            return float(v)\n", "    return 0.0\n", "\n", "def choose_market(base: str):\n", "    \"\"\"Return (exchange, symbol) for best market of *base* token.\"\"\"\n", "    # ---- futures first -----------------------------------------------------\n", "    futs = [m for m in fut_markets if m['base'] == base and m['active']]\n", "    if futs:\n", "        best = max(futs, key=_volume)\n", "        return fut_ex, best['symbol']\n", "    # ---- spot fallback -----------------------------------------------------\n", "    spots = [m for m in spot_markets if m['base'] == base and m['active']]\n", "    if not spots:\n", "        raise ValueError(f\"No active BinanceUS market for {base}\")\n", "    best = max(spots, key=_volume)\n", "    return spot_ex, best['symbol']\n", "\n", "# Build final tradable universe ------------------------------------------------\n", "UNIVERSE = {}\n", "for base in BASES:\n", "    try:\n", "        ex, sym = choose_market(base)\n", "        UNIVERSE[base] = (ex, sym)\n", "    except Exception as e:\n", "        print(f\"[skip] {base}: {e}\")\n", "\n", "print(f\"✓ {len(UNIVERSE)} tradable bases selected \"\n", "      f\"({sum(sym.endswith('PERP') for _, sym in UNIVERSE.values())} futures, \"\n", "      f\"{sum(not sym.endswith('PERP') for _, sym in UNIVERSE.values())} spot)\")\n"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"ename": "ExchangeNotAvailable", "evalue": "binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo 451  {\n  \"code\": 0,\n  \"msg\": \"Service unavailable from a restricted location according to 'b. Eligibility' in https://www.binance.com/en/terms. Please contact customer service if you believe you received this message in error.\"\n}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mHT<PERSON><PERSON>rror\u001b[39m                                 Traceback (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/ccxt/base/exchange.py:581\u001b[39m, in \u001b[36mExchange.fetch\u001b[39m\u001b[34m(self, url, method, headers, body)\u001b[39m\n\u001b[32m    580\u001b[39m     \u001b[38;5;28mself\u001b[39m.logger.debug(\u001b[33m\"\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m, Response: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, method, url, http_status_code, headers, http_response)\n\u001b[32m--> \u001b[39m\u001b[32m581\u001b[39m     \u001b[43mresponse\u001b[49m\u001b[43m.\u001b[49m\u001b[43mraise_for_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    583\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m Timeout \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/requests/models.py:1024\u001b[39m, in \u001b[36mResponse.raise_for_status\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1023\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m http_error_msg:\n\u001b[32m-> \u001b[39m\u001b[32m1024\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m HTTPError(http_error_msg, response=\u001b[38;5;28mself\u001b[39m)\n", "\u001b[31mHTTPError\u001b[39m: 451 Client Error:  for url: https://fapi.binance.com/fapi/v1/exchangeInfo", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mExchangeNotAvailable\u001b[39m                      Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[43]\u001b[39m\u001b[32m, line 49\u001b[39m\n\u001b[32m     45\u001b[39m         \u001b[38;5;66;03m# else: no active market → ignore\u001b[39;00m\n\u001b[32m     47\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m universe\n\u001b[32m---> \u001b[39m\u001b[32m49\u001b[39m UNIVERSE = \u001b[43mdiscover_bases\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[43]\u001b[39m\u001b[32m, line 14\u001b[39m, in \u001b[36mdiscover_bases\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m      9\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m     10\u001b[39m \u001b[33;03mReturn {base_token: (exchange, symbol)} where the symbol chosen\u001b[39;00m\n\u001b[32m     11\u001b[39m \u001b[33;03mis the single market (futures preferred) with the *highest* 24-h quote volume.\u001b[39;00m\n\u001b[32m     12\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m     13\u001b[39m spot_mkts = spot_ex.fetch_markets()\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m fut_mkts  = \u001b[43mfut_ex\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfetch_markets\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     16\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mvol\u001b[39m(m):\n\u001b[32m     17\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m fld \u001b[38;5;129;01min\u001b[39;00m (\u001b[33m'\u001b[39m\u001b[33mquoteVolume\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mvolume\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mbaseVolume\u001b[39m\u001b[33m'\u001b[39m):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/ccxt/binance.py:3045\u001b[39m, in \u001b[36mbinance.fetch_markets\u001b[39m\u001b[34m(self, params)\u001b[39m\n\u001b[32m   3043\u001b[39m         promisesRaw.append(\u001b[38;5;28mself\u001b[39m.sapiGetMarginIsolatedAllPairs(params))\n\u001b[32m   3044\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m marketType == \u001b[33m'\u001b[39m\u001b[33mlinear\u001b[39m\u001b[33m'\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m3045\u001b[39m     promisesRaw.append(\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfapiPublicGetExchangeInfo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[32m   3046\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m marketType == \u001b[33m'\u001b[39m\u001b[33minverse\u001b[39m\u001b[33m'\u001b[39m:\n\u001b[32m   3047\u001b[39m     promisesRaw.append(\u001b[38;5;28mself\u001b[39m.dapiPublicGetExchangeInfo(params))\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/ccxt/base/types.py:35\u001b[39m, in \u001b[36mEntry.__init__.<locals>.unbound_method\u001b[39m\u001b[34m(_self, params)\u001b[39m\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34munbound_method\u001b[39m(_self, params={}):\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_self\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mapi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/ccxt/binance.py:11336\u001b[39m, in \u001b[36mbinance.request\u001b[39m\u001b[34m(self, path, api, method, params, headers, body, config)\u001b[39m\n\u001b[32m  11335\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mrequest\u001b[39m(\u001b[38;5;28mself\u001b[39m, path, api=\u001b[33m'\u001b[39m\u001b[33mpublic\u001b[39m\u001b[33m'\u001b[39m, method=\u001b[33m'\u001b[39m\u001b[33mGET\u001b[39m\u001b[33m'\u001b[39m, params={}, headers=\u001b[38;5;28;01mNone\u001b[39;00m, body=\u001b[38;5;28;01mNone\u001b[39;00m, config={}):\n\u001b[32m> \u001b[39m\u001b[32m11336\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfetch2\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mapi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m  11337\u001b[39m     \u001b[38;5;66;03m# a workaround for {\"code\":-2015,\"msg\":\"Invalid API-key, IP, or permissions for action.\"}\u001b[39;00m\n\u001b[32m  11338\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m api == \u001b[33m'\u001b[39m\u001b[33mprivate\u001b[39m\u001b[33m'\u001b[39m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/ccxt/base/exchange.py:4462\u001b[39m, in \u001b[36mExchange.fetch2\u001b[39m\u001b[34m(self, path, api, method, params, headers, body, config)\u001b[39m\n\u001b[32m   4460\u001b[39m             \u001b[38;5;28mself\u001b[39m.sleep(retryDelay)\n\u001b[32m   4461\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m4462\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m   4463\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   4464\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/ccxt/base/exchange.py:4453\u001b[39m, in \u001b[36mExchange.fetch2\u001b[39m\u001b[34m(self, path, api, method, params, headers, body, config)\u001b[39m\n\u001b[32m   4451\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[32m0\u001b[39m, retries + \u001b[32m1\u001b[39m):\n\u001b[32m   4452\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m4453\u001b[39m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfetch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43murl\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mmethod\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mheaders\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mbody\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   4454\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m   4455\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e, OperationFailed):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/ccxt/base/exchange.py:599\u001b[39m, in \u001b[36mExchange.fetch\u001b[39m\u001b[34m(self, url, method, headers, body)\u001b[39m\n\u001b[32m    597\u001b[39m     skip_further_error_handling = \u001b[38;5;28mself\u001b[39m.handle_errors(http_status_code, http_status_text, url, method, headers, http_response, json_response, request_headers, request_body)\n\u001b[32m    598\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m skip_further_error_handling:\n\u001b[32m--> \u001b[39m\u001b[32m599\u001b[39m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mhandle_http_status_code\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhttp_status_code\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhttp_status_text\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhttp_response\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    600\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m ExchangeError(details) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m    602\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m requestsConnectionError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/ccxt/base/exchange.py:1695\u001b[39m, in \u001b[36mExchange.handle_http_status_code\u001b[39m\u001b[34m(self, code, reason, url, method, body)\u001b[39m\n\u001b[32m   1693\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m codeAsString \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.httpExceptions:\n\u001b[32m   1694\u001b[39m     ErrorClass = \u001b[38;5;28mself\u001b[39m.httpExceptions[codeAsString]\n\u001b[32m-> \u001b[39m\u001b[32m1695\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m ErrorClass(\u001b[38;5;28mself\u001b[39m.id + \u001b[33m'\u001b[39m\u001b[33m \u001b[39m\u001b[33m'\u001b[39m + method + \u001b[33m'\u001b[39m\u001b[33m \u001b[39m\u001b[33m'\u001b[39m + url + \u001b[33m'\u001b[39m\u001b[33m \u001b[39m\u001b[33m'\u001b[39m + codeAsString + \u001b[33m'\u001b[39m\u001b[33m \u001b[39m\u001b[33m'\u001b[39m + reason + \u001b[33m'\u001b[39m\u001b[33m \u001b[39m\u001b[33m'\u001b[39m + body)\n", "\u001b[31mExchangeNotAvailable\u001b[39m: binanceusdm GET https://fapi.binance.com/fapi/v1/exchangeInfo 451  {\n  \"code\": 0,\n  \"msg\": \"Service unavailable from a restricted location according to 'b. Eligibility' in https://www.binance.com/en/terms. Please contact customer service if you believe you received this message in error.\"\n}"]}], "source": []}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n"]}], "source": ["import ccxt\n", "ex = ccxt.binanceus()\n", "markets = ex.fetchMarkets()\n", "for m in markets:\n", "    print(m['future'])"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["binanceusdm = ccxt.binanceusdm()\n", "markets = ex.fetchMarkets()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n", "False\n"]}], "source": ["for m in markets:\n", "    print(m['future'])"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["markets = ex.load_markets()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["582"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["len(markets.items())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["import os\n", "\n", "from marketdata import ccxt_market_data\n", "os.chdir(os.path.expanduser('~/w/backtest'))\n", "\n", "import numpy as np\n", "from marketdata.market_data_loader_adapter import MarketDataLoaderAdapter\n", "from marketdata.ccxt_market_data import CcxtMarketData\n", "import dotenv\n", "\n", "dotenv.load_dotenv(os.path.expanduser('~/w/backtest/.env'))\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import argparse\n", "from datetime import datetime, timedelta\n", "from typing import List, Dict\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "from bidask import edge_rolling\n", "from marketdata.ccxt_market_data import CcxtMarketData\n", "\n", "\n", "# ---------------------------------------------------------------------------\n", "\n", "def fetch_universe(\n", "    md: CcxtMarketData,\n", "    symbols: List[str],\n", "    start: datetime,\n", "    end: datetime,\n", ") -> Dict[str, pd.DataFrame]:\n", "    \"\"\"\n", "    Download hourly OHLCV data for every symbol in *symbols*\n", "    between *start* and *end* (inclusive).\n", "\n", "    Returns\n", "    -------\n", "    dict\n", "        {symbol: ohlcv_dataframe}\n", "    \"\"\"\n", "    out: Dict[str, pd.DataFrame] = {}\n", "    for sym in symbols:\n", "        try:\n", "            df = md.gather_historical_data(\n", "                ticker=sym,\n", "                start_dt=start,\n", "                end_dt=end,\n", "                interval=INTERVAL_SECONDS,\n", "            )\n", "            # Ensure full hourly frequency\n", "            all_hours = pd.date_range(\n", "                start=df.index.min().floor(\"h\"),\n", "                end=df.index.max().ceil(\"h\"),\n", "                freq=\"1H\",\n", "                tz=\"UTC\",\n", "            )\n", "            df = (\n", "                df.reindex(all_hours, method=\"ffill\")\n", "                  .rename(columns=str.lower)[[\"open\", \"high\", \"low\", \"close\"]]\n", "            )\n", "            out[sym] = df\n", "        except Exception as e:\n", "            print(f\"[ERROR] {sym} failed: {e}\")\n", "    return out\n", "\n", "def compute_spread(df: pd.DataFrame) -> pd.Series:\n", "    \"\"\"Hourly bid-ask spread proxy (same as before).\"\"\"\n", "    return edge_rolling(df[[\"open\", \"high\", \"low\", \"close\"]], window=WINDOW_HOURS)\n", "\n", "def build_cross_section_signals(\n", "    price_data: Dict[str, pd.DataFrame],\n", ") -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame]:\n", "    \"\"\"\n", "    Return two DataFrames indexed like prices:\n", "    * long_short   →  +1 (long), -1 (short), 0 (flat)\n", "    * weights      →  portfolio weights that sum to 0 each bar\n", "    \"\"\"\n", "    # -------- 1. align hourly spreads for the whole universe ----------\n", "    spreads = pd.concat(\n", "        {sym: compute_spread(df) for sym, df in price_data.items()}, axis=1\n", "    ).dropna(how=\"all\")\n", "\n", "    # -------- 2. sample once a week -----------------------------------\n", "    weekly_spread = spreads.resample(REB_FREQ).last()\n", "\n", "    # -------- 3. cross-sectional ranking ------------------------------\n", "    ranks = weekly_spread.rank(axis=1, method=\"first\", pct=True)          # 0-1\n", "    long_mask  = ranks <= DECILE\n", "    short_mask = ranks >= 1 - DECILE\n", "    weekly_pos = long_mask.astype(int) - short_mask.astype(int)           # +1/-1/0\n", "\n", "    # -------- 4. hold position until next rebalance -------------------\n", "    pos = weekly_pos.reindex(spreads.index, method=\"ffill\").fillna(0)\n", "\n", "    # -------- 5. equal-weight long and short sides --------------------\n", "    n_long  = (pos ==  1).sum(axis=1)\n", "    n_short = (pos == -1).sum(axis=1)\n", "\n", "    w_long  = (pos ==  1).div(n_long.replace(0, np.nan), axis=0)\n", "    w_short = (pos == -1).div(n_short.replace(0, np.nan), axis=0).mul(-1)\n", "\n", "    weights = (w_long.fillna(0)) * 0.5 + w_short.fillna(0) * 0.5       # gross = 1\n", "    return pos, weights\n", "\n", "\n", "def backtest_cross_section(price_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:\n", "    \"\"\"\n", "    Long-short weekly‐rebalanced portfolio.\n", "    \"\"\"\n", "    prices = pd.concat(\n", "        {sym: df[\"close\"] for sym, df in price_data.items()}, axis=1\n", "    ).dropna(how=\"all\")\n", "\n", "    pos, weights = build_cross_section_signals(price_data)\n", "    weights = weights.reindex_like(prices).fillna(method=\"ffill\").fillna(0)\n", "\n", "    ret = prices.pct_change().fillna(0)\n", "\n", "    port_ret = (weights * ret).sum(axis=1)          # market-neutral L/S\n", "    out = pos.copy()\n", "    out[\"portfolio\"] = port_ret\n", "    return out\n", "\n", "\n", "def plot_equity(series: pd.Series, title: str, out_file: str):\n", "    \"\"\"Plot cumulative returns on a log‑scale.\"\"\"\n", "    equity = (1 + series).cumprod()\n", "    fig, ax = plt.subplots(figsize=(10, 4))\n", "    equity.plot(ax=ax, linewidth=1.2)\n", "    ax.set_ylabel(\"Equity ($)\")\n", "    ax.set_title(title)\n", "    ax.set_yscale(\"log\")\n", "    ax.grid(True, which=\"both\", linestyle=\":\")\n", "    fig.tight_layout()\n", "    fig.savefig(out_file, dpi=150)\n", "    plt.close(fig)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching data 2025‑01‑01 → 2025‑06‑06 ...\n", "✅ BTC via binanceus (BTC/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ETH via binanceus (ETH/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ XRP via binanceus (XRP/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ BNB via binanceus (BNB/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SOL via binanceus (SOL/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ DOGE via binanceus (DOGE/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ TRX via kraken (TRX/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ADA via binanceus (ADA/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ HYPE via kucoin (HYPE/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SUI via binanceus (SUI/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ BCH via binanceus (BCH/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LINK via binanceus (LINK/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LEO via okx (LEO/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ XLM via binanceus (XLM/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ AVAX via binanceus (AVAX/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SHIB via binanceus (SHIB/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ TON via kraken (TON/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LTC via binanceus (LTC/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ HBAR via binanceus (HBAR/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ DOT via binanceus (DOT/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ XMR via kraken (XMR/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ BGB via bitfinex (BGB/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ PEPE via binanceus (PEPE/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ PI via okx (PI/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ UNI via binanceus (UNI/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ AAVE via binanceus (AAVE/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ TAO via coinbase (TAO/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ APT via binanceus (APT/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ OKB via okx (OKB/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ICP via binanceus (ICP/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ CRO via coinbase (CRO/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ NEAR via binanceus (NEAR/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ETC via binanceus (ETC/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ONDO via coinbase (ONDO/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ TRUMP via binanceus (TRUMP/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ POL via binanceus (POL/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ GT via gate (GT/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ MNT via kraken (MNT/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ VET via binanceus (VET/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SKY via kraken (SKY/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ RENDER via binanceus (RENDER/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ KAS via kraken (KAS/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ FET via binanceus (FET/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ENA via kraken (ENA/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ KCS via kucoin (KCS/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ARB via binanceus (ARB/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SPX via kraken (SPX/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ WLD via coinbase (WLD/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ALGO via binanceus (ALGO/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ATOM via binanceus (ATOM/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ FIL via binanceus (FIL/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ FLR via coinbase (FLR/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ JUP via binanceus (JUP/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ BONK via binanceus (BONK/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ INJ via coinbase (INJ/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ FARTCOIN via kraken (FARTCOIN/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ TIA via coinbase (TIA/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ QNT via binanceus (QNT/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ VIRTUAL via binanceus (VIRTUAL/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ IP via coinbase (IP/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ FLOKI via binanceus (FLOKI/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ XAUt via okx (XAUT/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ GRT via binanceus (GRT/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ OP via binanceus (OP/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ XDC via bitstamp (XDC/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ WIF via binanceus (WIF/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ FORM via kucoin (FORM/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ PAXG via binanceus (PAXG/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ IMX via binanceus (IMX/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SEI via coinbase (SEI/USD)\n", "[ERROR] A failed: A not listed against ('USD', 'USDT', 'USDC', 'BUSD')\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ KAIA via kucoin (KAIA/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ JTO via binanceus (JTO/USDT)\n", "   ⚠️ bitstamp failed on NEXO/USD: bitstamp does not have market symbol NEXO/USD\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ NEXO via bitfinex (NEXO/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ CRV via binanceus (CRV/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ S via binanceus (S/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ STX via coinbase (STX/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ PENDLE via coinbase (PENDLE/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ PYTH via coinbase (PYTH/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ CAKE via kraken (CAKE/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ BSV via kucoin (BSV/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ WAL via kraken (WAL/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ENS via binanceus (ENS/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ HNT via coinbase (HNT/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ RUNE via kraken (RUNE/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LDO via binanceus (LDO/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SAND via binanceus (SAND/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ RAY via kraken (RAY/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ THETA via binanceus (THETA/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ZEC via binanceus (ZEC/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ DEXE via kucoin (DEXE/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ PENGU via binanceus (PENGU/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ GALA via binanceus (GALA/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ IOTA via binanceus (IOTA/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ FLOW via binanceus (FLOW/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ CORE via okx (CORE/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ BTT via kraken (BTT/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ XTZ via binanceus (XTZ/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ JASMY via coinbase (JASMY/USDC)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ZKJ via kucoin (ZKJ/USDT)\n", "Running backtest across 99 tradable symbols ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1908825/3885642403.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n", "/tmp/ipykernel_1908825/3885642403.py:104: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  weights = weights.reindex_like(prices).fillna(method=\"ffill\").fillna(0)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ Results saved to s.csv & s.png\n", "CAGR   : -2.23%\n", "Sharpe :   0.03\n"]}], "source": ["md = CcxtMarketData()\n", "\n", "print(f\"Fetching data {start:%Y‑%m‑%d} → {end:%Y‑%m‑%d} ...\")\n", "price_data = fetch_universe(md, UNIVERSE, start, end)\n", "\n", "print(f\"Running backtest across {len(price_data)} tradable symbols ...\")\n", "res = backtest_cross_section(price_data)\n", "\n", "csv_file = f\"s.csv\"\n", "png_file = f\"s.png\"\n", "res.to_csv(csv_file)\n", "plot_equity(res[\"portfolio\"], \"Illiquidity Premium Strategy\", png_file)\n", "\n", "cagr = res[\"portfolio\"].add(1).prod() ** (365 * 24 / len(res)) - 1\n", "sharpe = (\n", "    res[\"portfolio\"].mean() / res[\"portfolio\"].std(ddof=0) * np.sqrt(24 * 365)\n", ")\n", "\n", "print(f\"✓ Results saved to {csv_file} & {png_file}\")\n", "print(f\"CAGR   : {cagr:6.2%}\")\n", "print(f\"Sharpe : {sharpe:6.2f}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}