{"cells": [{"cell_type": "code", "execution_count": null, "id": "ae49f670", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["OutOfBounds converting market_data/binance/klines/spot/KLAYBTC/1d/KLAYBTC-1d-2024-10.csv with unit=ms: Out of bounds nanosecond timestamp: 56796-08-03 00:00:00\n", "OutOfBounds converting market_data/binance/klines/spot/KLAYUSDT/1d/KLAYUSDT-1d-2024-10.csv with unit=ms: Out of bounds nanosecond timestamp: 56796-08-03 00:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Universe size (spot + futures, filtered): 1887 instruments\n", "[SKIP] FUTURES:AAVEUSDC: no data\n", "[SKIP] FUTURES:AGTUSDT: no data\n", "[SKIP] FUTURES:AIOTUSDT: no data\n", "[SKIP] FUTURES:AKROUSDT: no data\n", "[SKIP] FUTURES:ALPINEUSDT: no data\n", "[SKIP] FUTURES:ANCUSDT: no data\n", "[SKIP] FUTURES:ASRUSDT: no data\n", "[SKIP] FUTURES:ATHUSDT: no data\n", "[SKIP] FUTURES:AUSDT: no data\n", "[SKIP] FUTURES:AWEUSDT: no data\n", "[SKIP] FUTURES:B2USDT: no data\n", "[SKIP] FUTURES:BABYUSDT: no data\n", "[SKIP] FUTURES:BANKUSDT: no data\n", "[SKIP] FUTURES:BDXNUSDT: no data\n", "[SKIP] FUTURES:BTTUSDT: no data\n", "[SKIP] FUTURES:BUSDT: no data\n", "[SKIP] FUTURES:BZRXUSDT: no data\n", "[SKIP] FUTURES:DEEPUSDT: no data\n", "[SKIP] FUTURES:DMCUSDT: no data\n", "[SKIP] FUTURES:DODOUSDT: no data\n", "[SKIP] FUTURES:DOLOUSDT: no data\n", "[SKIP] FUTURES:DOODUSDT: no data\n", "[SKIP] FUTURES:DOTECOUSDT: no data\n", "[SKIP] FUTURES:EPTUSDT: no data\n", "[SKIP] FUTURES:FHEUSDT: no data\n", "[SKIP] FUTURES:FISUSDT: no data\n", "[SKIP] FUTURES:FORTHUSDT: no data\n", "[SKIP] FUTURES:FUSDT: no data\n", "[SKIP] FUTURES:HAEDALUSDT: no data\n", "[SKIP] FUTURES:HOMEUSDT: no data\n", "[SKIP] FUTURES:HUMAUSDT: no data\n", "[SKIP] FUTURES:HUSDT: no data\n", "[SKIP] FUTURES:HYPERUSDT: no data\n", "[SKIP] FUTURES:HYPEUSDT: no data\n", "[SKIP] FUTURES:INITUSDT: no data\n", "[SKIP] FUTURES:JSTUSDT: no data\n", "[SKIP] FUTURES:KEEPUSDT: no data\n", "[SKIP] FUTURES:KERNELUSDT: no data\n", "[SKIP] FUTURES:LAUSDT: no data\n", "[SKIP] FUTURES:LENDUSDT: no data\n", "[SKIP] FUTURES:LUNABUSD: no data\n", "[SKIP] FUTURES:LUNAUSDT: no data\n", "[SKIP] FUTURES:MEMEFIUSDT: no data\n", "[SKIP] FUTURES:MERLUSDT: no data\n", "[SKIP] FUTURES:MILKUSDT: no data\n", "[SKIP] FUTURES:MYXUSDT: no data\n", "[SKIP] FUTURES:NEWTUSDT: no data\n", "[SKIP] FUTURES:NUUSDT: no data\n", "[SKIP] FUTURES:NXPCUSDT: no data\n", "[SKIP] FUTURES:OBOLUSDT: no data\n", "[SKIP] FUTURES:OGUSDT: no data\n", "[SKIP] FUTURES:OLUSDT: no data\n", "[SKIP] FUTURES:PORT3USDT: no data\n", "[SKIP] FUTURES:PROMPTUSDT: no data\n", "[SKIP] FUTURES:PUFFERUSDT: no data\n", "[SKIP] FUTURES:PUMPBTCUSDT: no data\n", "[SKIP] FUTURES:PUMPUSDT: no data\n", "[SKIP] FUTURES:PUNDIXUSDT: no data\n", "[SKIP] FUTURES:RESOLVUSDT: no data\n", "[SKIP] FUTURES:SAHARAUSDT: no data\n", "[SKIP] FUTURES:SIGNUSDT: no data\n", "[SKIP] FUTURES:SKATEUSDT: no data\n", "[SKIP] FUTURES:SKYAIUSDT: no data\n", "[SKIP] FUTURES:SOONUSDT: no data\n", "[SKIP] FUTURES:SOPHUSDT: no data\n", "[SKIP] FUTURES:SPKUSDT: no data\n", "[SKIP] FUTURES:SQDUSDT: no data\n", "[SKIP] FUTURES:STOUSDT: no data\n", "[SKIP] FUTURES:SXTUSDT: no data\n", "[SKIP] FUTURES:SYRUPUSDT: no data\n", "[SKIP] FUTURES:TAIKOUSDT: no data\n", "[SKIP] FUTURES:UNIUSDC: no data\n", "[SKIP] FUTURES:WCTUSDT: no data\n", "[SKIP] FUTURES:XCNUSDT: no data\n", "[SKIP] FUTURES:YFIIUSDT: no data\n", "[SKIP] FUTURES:ZKJUSDT: no data\n", "[SKIP] SPOT:AAVEDOWNUSDT: no data\n", "[SKIP] SPOT:AAVEUPUSDT: no data\n", "[SKIP] SPOT:ACHUSDC: no data\n", "[SKIP] SPOT:AFDUSD: no data\n", "[SKIP] SPOT:AIONBUSD: no data\n", "[SKIP] SPOT:ALGOTUSD: no data\n", "[SKIP] SPOT:ANCUSDT: no data\n", "[SKIP] SPOT:ANYBUSD: no data\n", "[SKIP] SPOT:ANYUSDT: no data\n", "[SKIP] SPOT:API3USDC: no data\n", "[SKIP] SPOT:ATOMTUSD: no data\n", "[SKIP] SPOT:AUCTIONUSDC: no data\n", "[SKIP] SPOT:AUDUSDC: no data\n", "[SKIP] SPOT:AUSDC: no data\n", "[SKIP] SPOT:AUSDT: no data\n", "[SKIP] SPOT:AWEUSDT: no data\n", "[SKIP] SPOT:BABYFDUSD: no data\n", "[SKIP] SPOT:BABYUSDC: no data\n", "[SKIP] SPOT:BABYUSDT: no data\n", "[SKIP] SPOT:BANANAUSDC: no data\n", "[SKIP] SPOT:BATTUSD: no data\n", "[SKIP] SPOT:BATUSDC: no data\n", "[SKIP] SPOT:BCHABUSD: no data\n", "[SKIP] SPOT:BCHDOWNUSDT: no data\n", "[SKIP] SPOT:BCHUPUSDT: no data\n", "[SKIP] SPOT:BEARBUSD: no data\n", "[SKIP] SPOT:BEARUSDT: no data\n", "[SKIP] SPOT:BGBPUSDC: no data\n", "[SKIP] SPOT:BIGTIMEUSDC: no data\n", "[SKIP] SPOT:BIGTIMEUSDT: no data\n", "[SKIP] SPOT:BKRWBUSD: no data\n", "[SKIP] SPOT:BKRWUSDT: no data\n", "[SKIP] SPOT:BNBBEARBUSD: no data\n", "[SKIP] SPOT:BNBBEARUSDT: no data\n", "[SKIP] SPOT:BNBBULLBUSD: no data\n", "[SKIP] SPOT:BNBBULLUSDT: no data\n", "[SKIP] SPOT:BOTBUSD: no data\n", "[SKIP] SPOT:BTCSTBUSD: no data\n", "[SKIP] SPOT:BTCSTUSDT: no data\n", "[SKIP] SPOT:BTGBUSD: no data\n", "[SKIP] SPOT:BTGUSDT: no data\n", "[SKIP] SPOT:BTSBUSD: no data\n", "[SKIP] SPOT:BTTBUSD: no data\n", "[SKIP] SPOT:BTTCUSDC: no data\n", "[SKIP] SPOT:BTTTUSD: no data\n", "[SKIP] SPOT:BTTUSDC: no data\n", "[SKIP] SPOT:BTTUSDT: no data\n", "[SKIP] SPOT:BULLBUSD: no data\n", "[SKIP] SPOT:BULLUSDT: no data\n", "[SKIP] SPOT:BZRXBUSD: no data\n", "[SKIP] SPOT:BZRXUSDT: no data\n", "[SKIP] SPOT:COVERBUSD: no data\n", "[SKIP] SPOT:DAIBUSD: no data\n", "[SKIP] SPOT:DAIUSDT: no data\n", "[SKIP] SPOT:DCRBUSD: no data\n", "[SKIP] SPOT:DNTBUSD: no data\n", "[SKIP] SPOT:DNTUSDT: no data\n", "[SKIP] SPOT:DOTDOWNUSDT: no data\n", "[SKIP] SPOT:DOTUPUSDT: no data\n", "[SKIP] SPOT:DUSKUSDC: no data\n", "[SKIP] SPOT:ENJUSDC: no data\n", "[SKIP] SPOT:EOSBEARBUSD: no data\n", "[SKIP] SPOT:EOSBEARUSDT: no data\n", "[SKIP] SPOT:EOSBULLBUSD: no data\n", "[SKIP] SPOT:EOSBULLUSDT: no data\n", "[SKIP] SPOT:EOSDOWNUSDT: no data\n", "[SKIP] SPOT:EOSTUSD: no data\n", "[SKIP] SPOT:EOSUPUSDT: no data\n", "[SKIP] SPOT:EPSBUSD: no data\n", "[SKIP] SPOT:EPSUSDT: no data\n", "[SKIP] SPOT:ERDBUSD: no data\n", "[SKIP] SPOT:ERDUSDT: no data\n", "[SKIP] SPOT:ETHBEARBUSD: no data\n", "[SKIP] SPOT:ETHBEARUSDT: no data\n", "[SKIP] SPOT:ETHBULLBUSD: no data\n", "[SKIP] SPOT:ETHBULLUSDT: no data\n", "[SKIP] SPOT:FILDOWNUSDT: no data\n", "[SKIP] SPOT:FILUPUSDT: no data\n", "[SKIP] SPOT:FLMBUSD: no data\n", "[SKIP] SPOT:FLUXUSDC: no data\n", "[SKIP] SPOT:GMTUSDC: no data\n", "[SKIP] SPOT:GNOBUSD: no data\n", "[SKIP] SPOT:GTOUSDT: no data\n", "[SKIP] SPOT:GUNUSDC: no data\n", "[SKIP] SPOT:GXSUSDT: no data\n", "[SKIP] SPOT:HAEDALFDUSD: no data\n", "[SKIP] SPOT:HAEDALUSDC: no data\n", "[SKIP] SPOT:HAEDALUSDT: no data\n", "[SKIP] SPOT:HCUSDT: no data\n", "[SKIP] SPOT:HEGICBUSD: no data\n", "[SKIP] SPOT:HNTUSDT: no data\n", "[SKIP] SPOT:HOMEFDUSD: no data\n", "[SKIP] SPOT:HOMEUSDC: no data\n", "[SKIP] SPOT:HOMEUSDT: no data\n", "[SKIP] SPOT:HUMAFDUSD: no data\n", "[SKIP] SPOT:HUMAUSDC: no data\n", "[SKIP] SPOT:HUMAUSDT: no data\n", "[SKIP] SPOT:HYPERFDUSD: no data\n", "[SKIP] SPOT:HYPERUSDC: no data\n", "[SKIP] SPOT:HYPERUSDT: no data\n", "[SKIP] SPOT:INITFDUSD: no data\n", "[SKIP] SPOT:INITUSDC: no data\n", "[SKIP] SPOT:INITUSDT: no data\n", "[SKIP] SPOT:IRISBUSD: no data\n", "[SKIP] SPOT:KEEPBUSD: no data\n", "[SKIP] SPOT:KEEPUSDT: no data\n", "[SKIP] SPOT:KERNELFDUSD: no data\n", "[SKIP] SPOT:KERNELUSDC: no data\n", "[SKIP] SPOT:KERNELUSDT: no data\n", "[SKIP] SPOT:KMDBUSD: no data\n", "[SKIP] SPOT:KMNOUSDC: no data\n", "[SKIP] SPOT:KMNOUSDT: no data\n", "[SKIP] SPOT:LENDBUSD: no data\n", "[SKIP] SPOT:LENDUSDT: no data\n", "[SKIP] SPOT:LTCDOWNUSDT: no data\n", "[SKIP] SPOT:LTCUPUSDT: no data\n", "[SKIP] SPOT:MASKUSDC: no data\n", "[SKIP] SPOT:NEWTFDUSD: no data\n", "[SKIP] SPOT:NEWTUSDC: no data\n", "[SKIP] SPOT:NEWTUSDT: no data\n", "[SKIP] SPOT:NXPCFDUSD: no data\n", "[SKIP] SPOT:NXPCUSDC: no data\n", "[SKIP] SPOT:NXPCUSDT: no data\n", "[SKIP] SPOT:ONDOUSDC: no data\n", "[SKIP] SPOT:ONDOUSDT: no data\n", "[SKIP] SPOT:PAXGUSDC: no data\n", "[SKIP] SPOT:PUNDIXUSDC: no data\n", "[SKIP] SPOT:QNTUSDC: no data\n", "[SKIP] SPOT:RESOLVFDUSD: no data\n", "[SKIP] SPOT:RESOLVUSDC: no data\n", "[SKIP] SPOT:RESOLVUSDT: no data\n", "[SKIP] SPOT:SAHARAFDUSD: no data\n", "[SKIP] SPOT:SAHARAUSDC: no data\n", "[SKIP] SPOT:SAHARAUSDT: no data\n", "[SKIP] SPOT:SIGNFDUSD: no data\n", "[SKIP] SPOT:SIGNUSDC: no data\n", "[SKIP] SPOT:SIGNUSDT: no data\n", "[SKIP] SPOT:SOPHFDUSD: no data\n", "[SKIP] SPOT:SOPHUSDC: no data\n", "[SKIP] SPOT:SOPHUSDT: no data\n", "[SKIP] SPOT:SPKFDUSD: no data\n", "[SKIP] SPOT:SPKUSDC: no data\n", "[SKIP] SPOT:SPKUSDT: no data\n", "[SKIP] SPOT:STOFDUSD: no data\n", "[SKIP] SPOT:STOUSDC: no data\n", "[SKIP] SPOT:STOUSDT: no data\n", "[SKIP] SPOT:SUSHIUSDC: no data\n", "[SKIP] SPOT:SXTFDUSD: no data\n", "[SKIP] SPOT:SXTUSDC: no data\n", "[SKIP] SPOT:SXTUSDT: no data\n", "[SKIP] SPOT:SYRUPUSDC: no data\n", "[SKIP] SPOT:SYRUPUSDT: no data\n", "[SKIP] SPOT:THETAUSDC: no data\n", "[SKIP] SPOT:USD1USDT: no data\n", "[SKIP] SPOT:VETUSDC: no data\n", "[SKIP] SPOT:VIRTUALUSDC: no data\n", "[SKIP] SPOT:VIRTUALUSDT: no data\n", "[SKIP] SPOT:WCTFDUSD: no data\n", "[SKIP] SPOT:WCTUSDC: no data\n", "[SKIP] SPOT:WCTUSDT: no data\n", "[SKIP] SPOT:ZENUSDC: no data\n", "Fetched price data for 1650 instruments – running backtest…\n"]}], "source": ["\"\"\"\n", "Illiquidity Premium Backtest – **Daily K‑Lines (robust)**\n", "========================================================\n", "A Jupyter‑friendly notebook that now\n", "\n", "* **Skips special/odd pairs** such as expiries (`ETHUSDT_251226`) or\n", "  settlement markers (`XYZUSDTSETTLED`) and anything containing an\n", "  underscore.\n", "* Gracefully **drops symbols with no data** in the requested window so\n", "  those “[WARN] … Neither `start` nor `end` can be NaT” messages vanish.\n", "* Continues to de‑duplicate on **base asset**.\n", "\n", "Copy‑paste into a notebook—the `# %%` delimiters create runnable cells.\n", "Feel free to tweak the exclusion logic or add further error handling.\n", "\"\"\"\n", "\n", "# %%\n", "# Imports & Config\n", "from __future__ import annotations\n", "\n", "import re\n", "from datetime import datetime\n", "from pathlib import Path\n", "from typing import Dict, List\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pytz\n", "from bidask import edge_rolling\n", "\n", "from marketdata.binance_klines_market_data import BinanceKlinesHistMarketData\n", "\n", "# --- user‑adjustable parameters ---------------------------------------------\n", "BASE_DIR = Path(\"~/backtest/market_data/binance\").expanduser()  # CSV root\n", "\n", "START    = \"2023-01-01\"      # inclusive, US/Eastern\n", "END      = \"2025-03-31\"      # inclusive, US/Eastern\n", "\n", "WINDOW_DAYS  = 30           # spread history window\n", "DECILE       = 0.1         # top/bottom 10 %\n", "QUOTE_TOKENS = (\"USDT\", \"USDC\", \"BUSD\", \"FDUSD\", \"TUSD\", \"DAI\")\n", "\n", "# Exclude symbols that match this regex (e.g. expiries, SETTLED pairs)\n", "EXCLUDE_REGEX = re.compile(r\"_|SETTLED$\")  # underscore OR ends with SETTLED\n", "\n", "MARKETS = (\"futures\", \"spot\")   # override single MARKET usage\n", "INTERVAL = \"1d\"\n", "\n", "def is_usd_quote(sym: str) -> bool:\n", "    return any(sym.endswith(q) for q in QUOTE_TOKENS)\n", "\n", "def is_valid_symbol(sym: str) -> bool:  # (override previous if still defined)\n", "    return (not (EXCLUDE_REGEX.search(sym) or sym[0].isdigit())) and is_usd_quote(sym)\n", "\n", "def build_combined_universe(md_by_market: Dict[str, BinanceKlinesHistMarketData]) -> Dict[str, str]:\n", "    \"\"\"\n", "    Return dict composite_symbol -> underlying raw symbol.\n", "    Composite symbol namespaced by market:\n", "        FUTURES:BTCUSDT  or  SPOT:BTCUSDT\n", "    We keep *all* (no base de-dup) after filtering regex + quote.\n", "    \"\"\"\n", "    combined = {}\n", "    for mkt, md in md_by_market.items():\n", "        prefix = \"FUTURES\" if mkt == \"futures\" else \"SPOT\"\n", "        for sym in md.list_all_pairs():\n", "            if is_valid_symbol(sym):\n", "                combined[f\"{prefix}:{sym}\"] = sym\n", "    return combined\n", "\n", "# %%\n", "def fetch_price_data_combined(\n", "    md_by_market: Dict[str, BinanceKlinesHistMarketData],\n", "    universe_map: Dict[str, str],\n", "    start, end\n", ") -> Dict[str, pd.DataFrame]:\n", "    out = {}\n", "    for comp_sym, raw_sym in universe_map.items():\n", "        # comp_sym looks like \"FUTURES:BTCUSDT\"\n", "        market = comp_sym.split(\":\", 1)[0].lower()\n", "        md = md_by_market[market]\n", "        try:\n", "            df = md.gather_historical_data(raw_sym, start, end)\n", "            if df.empty:\n", "                raise ValueError(\"no data\")\n", "            if \"open\" not in df.columns:\n", "                raise KeyError(\"missing OHLC columns\")\n", "            idx_min, idx_max = df.index.min(), df.index.max()\n", "            if pd.isna(idx_min) or pd.isna(idx_max):\n", "                raise ValueError(\"bad index\")\n", "\n", "            all_days = pd.date_range(\n", "                start=idx_min.floor(\"D\"),\n", "                end=idx_max.ceil(\"D\"),\n", "                freq=\"1D\",\n", "                tz=df.index.tz,\n", "            )\n", "            df = df.reindex(all_days, method=\"ffill\")[[\"open\",\"high\",\"low\",\"close\",\"volume\"]]\n", "            out[comp_sym] = df\n", "        except Exception as e:\n", "            print(f\"[SKIP] {comp_sym}: {e}\")\n", "    return out\n", "\n", "# %%\n", "def compute_spread(df: pd.DataFrame) -> pd.Series:\n", "    spread_raw = edge_rolling(df[[\"open\",\"high\",\"low\",\"close\"]], window=WINDOW_DAYS)\n", "    # Use yesterday's spread to rank today:\n", "    return spread_raw.shift(1)\n", "\n", "def build_weights(spread: pd.DataFrame, decile: float, futures_cols: set[str]) -> pd.DataFrame:\n", "    rank = spread.rank(pct=True, axis=1)\n", "\n", "    # Long: top decile (>= 1 - decile)\n", "    long_mask = rank >= 1 - decile\n", "\n", "    # Short candidates: bottom decile *and* futures only\n", "    short_mask_all = rank <= decile\n", "    # Restrict to futures columns\n", "    fut_cols = [c for c in spread.columns if c in futures_cols]\n", "    short_mask = short_mask_all.loc[:, fut_cols]\n", "    # Reindex back to all columns (missing spot short cols -> False)\n", "    short_mask = short_mask.reindex(columns=spread.columns, fill_value=False)\n", "\n", "    n_long = long_mask.sum(axis=1).replace(0, np.nan)\n", "    n_short = short_mask.sum(axis=1).replace(0, np.nan)\n", "\n", "    long_w = long_mask.div(n_long, axis=0)  # fully allocate +1 side\n", "    short_w = -short_mask.div(n_short, axis=0)  # fully allocate -1 side\n", "\n", "    long_w = long_w * 0.5\n", "    short_w = short_w * 0.5\n", "\n", "    weights = (long_w + short_w).shift(1).fillna(0)\n", "    return weights\n", "\n", "def backtest(price_data: Dict[str, pd.DataFrame], decile: float = DECILE):\n", "    closes  = pd.concat({s: d[\"close\"] for s, d in price_data.items()}, axis=1).sort_index()\n", "    rets    = closes.pct_change().fillna(0)\n", "\n", "    spread  = pd.concat({s: compute_spread(d) for s, d in price_data.items()}, axis=1)\n", "    spread  = spread.reindex_like(closes)\n", "\n", "    futures_cols = {c for c in closes.columns if c.startswith(\"FUTURES:\")}\n", "\n", "    weights = build_weights(spread, decile=decile, futures_cols=futures_cols)\n", "    port_r  = (weights * rets).sum(axis=1)\n", "    equity  = (1 + port_r).cumprod()\n", "    return port_r, equity, weights, spread\n", "\n", "\n", "# %%\n", "est = pytz.timezone(\"US/Eastern\")\n", "start_dt = est.localize(datetime.fromisoformat(START))\n", "end_dt   = est.localize(datetime.fromisoformat(END))\n", "\n", "md_by_market = {\n", "    mkt: BinanceKlinesHistMarketData(\n", "        market=mkt,\n", "        interval=INTERVAL,\n", "        history_back_months=120,\n", "    )\n", "    for mkt in MARKETS\n", "}\n", "\n", "universe_map = build_combined_universe(md_by_market)\n", "print(f\"Universe size (spot + futures, filtered): {len(universe_map)} instruments\")\n", "\n", "price_data = fetch_price_data_combined(md_by_market, universe_map, start_dt, end_dt)\n", "print(f\"Fetched price data for {len(price_data)} instruments – running backtest…\")"]}, {"cell_type": "code", "execution_count": 5, "id": "b8696b36", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1477605/1547131011.py:138: FutureWarning: The default fill_method='pad' in DataFrame.pct_change is deprecated and will be removed in a future version. Either fill in any non-leading NA values prior to calling pct_change or specify 'fill_method=None' to not fill NA values.\n", "  rets    = closes.pct_change().fillna(0)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["CAGR  : -10.02%\n", "Sharpe:  -0.37\n", "Avg long count: 20.390986601705237\n", "Avg short count (futures only): 8.583434835566383\n"]}], "source": ["# %%\n", "port_ret, equity, weights, spread = backtest(price_data, decile=0.025)#DECILE)\n", "\n", "fig, ax = plt.subplots(figsize=(10,4))\n", "equity.plot(ax=ax, lw=1.2)\n", "ax.set_title(\"Illiquidity Premium (Spot + Futures) – Daily\")\n", "ax.set_ylabel(\"Equity (log)\")\n", "ax.set_yscale(\"log\")\n", "ax.grid(True, which=\"both\", ls=\":\")\n", "plt.show()\n", "\n", "cagr   = equity.iloc[-1] ** (365 / len(port_ret)) - 1\n", "sharpe = port_ret.mean() / port_ret.std(ddof=0) * np.sqrt(365)\n", "print(f\"CAGR  : {cagr:6.2%}\\nSharpe: {sharpe:6.2f}\")\n", "print(\"Avg long count:\", (weights.gt(0)).sum(axis=1).mean())\n", "print(\"Avg short count (futures only):\", (weights.lt(0)).sum(axis=1).mean())\n"]}, {"cell_type": "code", "execution_count": 6, "id": "4179108c", "metadata": {}, "outputs": [], "source": ["# BASE_DIR = Path(\"/home/<USER>/w/backtest/market_data/binance/klines/\")\n", "# spot_file_pre  = BASE_DIR / \"spot/BTCUSDT/1d/BTCUSDT-1d-2024-12.csv\"  # ms\n", "# spot_file_post = BASE_DIR / \"spot/BTCUSDT/1d/BTCUSDT-1d-2025-01.csv\"  # us\n", "# fut_file_post  = BASE_DIR / \"futures/BTCUSDT/1d/BTCUSDT-1d-2025-01.csv\"  # ms\n", "\n", "# for f in [spot_file_pre, spot_file_post, fut_file_post]:\n", "#     first = int(pd.read_csv(f, nrows=1).iloc[0,0])\n", "#     print(f, len(str(first)))\n", "\n", "\n", "# md_spot = BinanceKlinesHistMarketData(market=\"spot\", interval=\"1d\", history_back_months=18)\n", "# md_fut  = BinanceKlinesHistMarketData(market=\"futures\", interval=\"1d\", history_back_months=18)\n", "\n", "# est = pytz.timezone(\"US/Eastern\")\n", "# start = est.localize(datetime(2025, 1, 1))\n", "# end   = est.localize(datetime(2025, 1, 5))\n", "# print(md_spot.gather_historical_data(\"BTCUSDT\", start, end).index[:3])\n", "# print(md_fut.gather_historical_data(\"BTCUSDT\", start, end).index[:3])\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}