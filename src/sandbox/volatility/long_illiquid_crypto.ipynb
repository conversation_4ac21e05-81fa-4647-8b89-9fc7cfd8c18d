{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from typing import List, Dict\n", "import pandas as pd\n", "\n", "# ---- configuration ---------------------------------------------------------\n", "\n", "UNIVERSE: List[str] = [\n", "    \"BTC\", \"ETH\", \"USDT\", \"XRP\", \"BNB\", \"SOL\", \"USDC\", \"DOGE\", \"ADA\", \"TRX\",\n", "    \"SUI\", \"LINK\", \"LEO\", \"AVAX\", \"XLM\", \"TON\", \"SHIB\", \"HBAR\", \"BCH\", \"HYPE\",\n", "]\n", "\n", "INTERVAL_SECONDS = 3600          # 1‑hour bars\n", "WINDOW_HOURS     = 7 * 24        # 90‑day window\n", "DECILE           = 0.10          # bottom decile threshold\n", "\n", "start = pd.Timestamp(\"2025-01-01\")\n", "end   = pd.Timestamp(\"2025-06-06\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["#!uv pip install bidask"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["import os\n", "\n", "from marketdata import ccxt_market_data\n", "os.chdir(os.path.expanduser('~/w/backtest'))\n", "\n", "import numpy as np\n", "from marketdata.market_data_loader_adapter import MarketDataLoaderAdapter\n", "from marketdata.ccxt_market_data import CcxtMarketData\n", "import dotenv\n", "\n", "dotenv.load_dotenv(os.path.expanduser('~/w/backtest/.env'))\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import argparse\n", "from datetime import datetime, timedelta\n", "from typing import List, Dict\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "from bidask import edge_rolling\n", "from marketdata.ccxt_market_data import CcxtMarketData\n", "\n", "\n", "# ---------------------------------------------------------------------------\n", "\n", "def fetch_universe(\n", "    md: CcxtMarketData,\n", "    symbols: List[str],\n", "    start: datetime,\n", "    end: datetime,\n", ") -> Dict[str, pd.DataFrame]:\n", "    \"\"\"\n", "    Download hourly OHLCV data for every symbol in *symbols*\n", "    between *start* and *end* (inclusive).\n", "\n", "    Returns\n", "    -------\n", "    dict\n", "        {symbol: ohlcv_dataframe}\n", "    \"\"\"\n", "    out: Dict[str, pd.DataFrame] = {}\n", "    for sym in symbols:\n", "        try:\n", "            df = md.gather_historical_data(\n", "                ticker=sym,\n", "                start_dt=start,\n", "                end_dt=end,\n", "                interval=INTERVAL_SECONDS,\n", "            )\n", "            # Ensure full hourly frequency\n", "            all_hours = pd.date_range(\n", "                start=df.index.min().floor(\"H\"),\n", "                end=df.index.max().ceil(\"H\"),\n", "                freq=\"1H\",\n", "                tz=\"UTC\",\n", "            )\n", "            df = (\n", "                df.reindex(all_hours, method=\"ffill\")\n", "                  .rename(columns=str.lower)[[\"open\", \"high\", \"low\", \"close\"]]\n", "            )\n", "            out[sym] = df\n", "        except Exception as e:\n", "            print(f\"[ERROR] {sym} failed: {e}\")\n", "    return out\n", "\n", "\n", "def prepare_spread(df: pd.DataFrame) -> pd.Series:\n", "    \"\"\"\n", "    Compute the rolling bid‑ask spread estimate and the trading signal.\n", "\n", "    Parameters\n", "    ----------\n", "    df : DataFrame\n", "        Hourly OHLC dataframe with columns 'open', 'high', 'low', 'close'.\n", "\n", "    Returns\n", "    -------\n", "    signal : Series[bool]\n", "        True when spread ≤ bottom decile of its 7‑day history.\n", "    \"\"\"\n", "    spread = edge_rolling(df[[\"open\", \"high\", \"low\", \"close\"]], window=WINDOW_HOURS)\n", "    q10 = spread.rolling(WINDOW_HOURS, min_periods=WINDOW_HOURS).quantile(DECILE)\n", "    signal = (spread <= q10).astype(int)\n", "    return signal.shift(1).fillna(0).astype(int)   # apply on next bar\n", "\n", "\n", "def backtest_portfolio(\n", "    price_data: Dict[str, pd.DataFrame],\n", ") -> pd.DataFrame:\n", "    \"\"\"\n", "    Combine symbol‑level signals into an equal‑weighted long‑only portfolio.\n", "\n", "    Returns\n", "    -------\n", "    DataFrame\n", "        Columns: each symbol's strategy return, 'portfolio'\n", "    \"\"\"\n", "    aligned_prices = pd.concat(\n", "        {sym: df[\"close\"] for sym, df in price_data.items()}, axis=1\n", "    ).dropna(how=\"all\")\n", "\n", "    aligned_signals = pd.concat(\n", "        {sym: prepare_spread(df) for sym, df in price_data.items()}, axis=1\n", "    ).reindex_like(aligned_prices).fillna(0)\n", "\n", "    # 1‑period simple returns\n", "    ret = aligned_prices.pct_change().fillna(0)\n", "\n", "    strat_ret = aligned_signals * ret\n", "\n", "    # Equal‑weight across active longs, cash to 0\n", "    weights = aligned_signals.div(aligned_signals.sum(axis=1).replace(0, np.nan), axis=0)\n", "    port_ret = (weights * ret).sum(axis=1).fillna(0)\n", "\n", "    out = strat_ret.copy()\n", "    out[\"portfolio\"] = port_ret\n", "    return out\n", "\n", "\n", "def plot_equity(series: pd.Series, title: str, out_file: str):\n", "    \"\"\"Plot cumulative returns on a log‑scale.\"\"\"\n", "    equity = (1 + series).cumprod()\n", "    fig, ax = plt.subplots(figsize=(10, 4))\n", "    equity.plot(ax=ax, linewidth=1.2)\n", "    ax.set_ylabel(\"Equity ($)\")\n", "    ax.set_title(title)\n", "    ax.set_yscale(\"log\")\n", "    ax.grid(True, which=\"both\", linestyle=\":\")\n", "    fig.tight_layout()\n", "    fig.savefig(out_file, dpi=150)\n", "    plt.close(fig)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching data 2021‑01‑01 → 2025‑06‑06 ...\n", "✅ BTC via binanceus (BTC/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ETH via binanceus (ETH/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ USDT via binanceus (USDT/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ XRP via binanceus (XRP/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ BNB via binanceus (BNB/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SOL via binanceus (SOL/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ USDC via binanceus (USDC/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ DOGE via binanceus (DOGE/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ ADA via binanceus (ADA/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ TRX via kraken (TRX/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SUI via binanceus (SUI/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LINK via binanceus (LINK/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LEO via okx (LEO/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ AVAX via binanceus (AVAX/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ XLM via binanceus (XLM/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ TON via kraken (TON/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ SHIB via binanceus (SHIB/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ HBAR via binanceus (HBAR/USD)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ BCH via binanceus (BCH/USDT)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["   ⚠️ bingx failed on HYPE/USDT: bingx {\"code\":100204,\"msg\":\"date of query is too wide.\",\"timestamp\":1750037602718}\n", "   ⚠️ cex failed on HYPE/USD: cex fetchOHLCV requires a parameter \"dataType\" to be either \"bestBid\" or \"bestAsk\"\n", "   ⚠️ cex failed on HYPE/USDC: cex fetchOHLCV requires a parameter \"dataType\" to be either \"bestBid\" or \"bestAsk\"\n", "   ⚠️ cex failed on HYPE/USDT: cex fetchOHLCV requires a parameter \"dataType\" to be either \"bestBid\" or \"bestAsk\"\n", "✅ HYPE via coinex (HYPE/USDT)\n", "Running backtest across 20 tradable symbols ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1906058/268650915.py:41: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  start=df.index.min().floor(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:42: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  end=df.index.max().ceil(\"H\"),\n", "/tmp/ipykernel_1906058/268650915.py:40: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  all_hours = pd.date_range(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ Results saved to s.csv & s.png\n", "CAGR   : 22.73%\n", "Sharpe :   0.54\n"]}], "source": ["md = CcxtMarketData()\n", "\n", "print(f\"Fetching data {start:%Y‑%m‑%d} → {end:%Y‑%m‑%d} ...\")\n", "price_data = fetch_universe(md, UNIVERSE, start, end)\n", "\n", "print(f\"Running backtest across {len(price_data)} tradable symbols ...\")\n", "res = backtest_portfolio(price_data)\n", "\n", "csv_file = f\"s.csv\"\n", "png_file = f\"s.png\"\n", "res.to_csv(csv_file)\n", "plot_equity(res[\"portfolio\"], \"Illiquidity Premium Strategy\", png_file)\n", "\n", "cagr = res[\"portfolio\"].add(1).prod() ** (365 * 24 / len(res)) - 1\n", "sharpe = (\n", "    res[\"portfolio\"].mean() / res[\"portfolio\"].std(ddof=0) * np.sqrt(24 * 365)\n", ")\n", "\n", "print(f\"✓ Results saved to {csv_file} & {png_file}\")\n", "print(f\"CAGR   : {cagr:6.2%}\")\n", "print(f\"Sharpe : {sharpe:6.2f}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}