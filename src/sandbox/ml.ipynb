{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Summary: Overall idea seems to be working, just need cleaner data\n", "# \n", "# [ ] Add turnover - probably the most crucial parameter\n", "\n", "\n", "from datetime import datetime, date, timedelta\n", "import pathlib\n", "import logging\n", "logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "\n", "start_dt = datetime(2024, 1, 1)\n", "end_dt = datetime(2024, 12, 31)\n", "\n", "from strategies.ml import HISTO<PERSON><PERSON>_<PERSON>AN,  TRAIN_YEARS_LOOKBACK, PROB_THRESHOLD, MAX_POSITIONS, HOLD_DAYS, STOP_LOSS_PCT, CACHE_DIR, LIQUIDITY_USD_MIN, LIQUIDITY_MIN_PRICE"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# !uv pip install xgboost\n", "\n", "from indicators.qpi import qpi_single"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "from universe.sp500_constituents import SP500Constituents\n", "from marketdata.market_data_builder import MarketDataBuilder\n", "\n", "os.chdir(os.path.expanduser('~/w/backtest'))\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_disk_data(start_date=start_dt - timedelta(days=(TRAIN_YEARS_LOOKBACK * 365) + HISTORY_SPAN))\n", "                        .build_market_data())\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d0f8fe44f63a4c46a9b6474d1f36fd93", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching prices:   0%|          | 0/2874 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "32dfb7cfbe564038b6e34ae9b9db8599", "version_major": 2, "version_minor": 0}, "text/plain": ["Featurizing:   0%|          | 0/2874 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pandas.arrays import TimedeltaArray\n", "from strategies.ml import MarketDataAdapter, FeatureEngineer, ModelTrainer\n", "from tqdm.notebook import tqdm\n", "from datetime import timedelta\n", "import pandas as pd\n", "\n", "\n", "fe  = FeatureEngineer()\n", "start_hist = start_dt - timed<PERSON>ta(days=365 * TRAIN_YEARS_LOOKBACK)\n", "end_hist   = end_dt + <PERSON><PERSON>ta(days=HOLD_DAYS)\n", "required_min_len_initial = max(fe.roc_windows) + 1\n", "\n", "# 1) Build the universe of all symbols ever in the S&P500 during our window\n", "# all_dates = pd.bdate_range(start_hist, end_dt).date\n", "# universe = set()\n", "# for d in all_dates:\n", "#     universe |= set(sp500.constituents_for(d))\n", "# universe = sorted(universe)\n", "\n", "from universe.stock_universe_selector import StockUniverseSelector\n", "from tickers.ticker_helpers import TickerInfoStore\n", "\n", "ticker_info_store = TickerInfoStore()\n", "\n", "universe_selector = StockUniverseSelector(\n", "    polygon_data=daily_market_data,\n", "    ticker_info_store=ticker_info_store,\n", ")\n", "\n", "universe_df = universe_selector.constituents_for(\n", "    pd.to_datetime(start_hist).tz_localize(\"EST\"),\n", "    pd.to_datetime(end_dt).tz_localize(\"EST\"),\n", "    min_mcap=100_000_000,\n", "    max_mcap=500_000_000\n", ")   \n", "\n", "def get_snapshot_before(ts) -> pd.Timestamp:\n", "        \"\"\"\n", "        Returns the most recent snapshot date <= ts.\n", "        \"\"\"\n", "        ts = pd.to_datetime(ts)\n", "        if ts.tz is not None:\n", "            ts = ts.tz_convert(None)\n", "        ts = ts.normalize().date()\n", "\n", "        dates = pd.to_datetime(universe_df.index.unique()).date\n", "        # Find rightmost date <= ts\n", "        valid_dates = [d for d in dates if d <= ts]\n", "        if not valid_dates:\n", "            raise ValueError(f\"No snapshot available on or before {ts}\")\n", "        return max(valid_dates)\n", "    \n", "def constituents_for(ts):\n", "        \"\"\"\n", "        Returns the list of S&P 500 symbols valid at the given timestamp.\n", "        \"\"\"\n", "        snap = get_snapshot_before(ts)\n", "        subset = (\n", "            universe_df.loc[snap]\n", "            .sort_values(\"rank\")\n", "        )\n", "        return subset[\"symbol\"].tolist()\n", "\n", "universe = sorted(universe_df[\"ticker\"].unique())\n", "\n", "# 2) Fetch & cache every symbol’s daily bars _once_\n", "prices = {}\n", "for sym in tqdm(universe, desc=\"Fetching prices\"):\n", "    prices[sym] = daily_market_data.gather_historical_data(sym, start_hist - timedelta(days=HISTORY_SPAN), end_hist)\n", "\n", "# 3) Compute features for each symbol across its entire history\n", "feat_list = []\n", "for sym, px in tqdm(prices.items(), desc=\"Featurizing\"):\n", "    px = px.assign(dollar_vol = px[\"close\"] * px[\"volume\"])\n", "    px = px[px[\"dollar_vol\"] >= LIQUIDITY_USD_MIN]\n", "    px = px[px['close'] > LIQUIDITY_MIN_PRICE]\n", "\n", "    if len(px) < required_min_len_initial:\n", "        continue\n", "    \n", "    fdf = fe.transform(px).dropna()  # Multi-column DF, indexed by date\n", "    # attach symbol as a column so we can stack later\n", "    fdf = fdf.assign(symbol=sym)\n", "    feat_list.append(fdf)\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0c5c0e7cd5d9408aa843a816be27ccfb", "version_major": 2, "version_minor": 0}, "text/plain": ["Computing targets:   0%|          | 0/2874 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3172400/2618025895.py:20: FutureWarning: The behavior of pd.concat with len(keys) != len(objs) is deprecated. In a future version this will raise instead of truncating to the smaller of the two sequences\n", "  pd.concat(targets, keys=universe, names=[\"symbol\",\"date\"])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024 2023-01-01 2023-12-31\n"]}], "source": ["\n", "# 4) Concatenate into one big Multi-indexed DataFrame\n", "all_features = (\n", "    pd.concat(feat_list)\n", "      .reset_index()                  # date from index → column\n", "      .rename(columns={\"index\":\"date\"})\n", "      .set_index([\"date\",\"symbol\"])\n", "      .sort_index()\n", ")\n", "\n", "# 5) Compute the 5-day ahead “bounce” target for each symbol\n", "# 1 (buy on open) 2 3 4 5 6 (sell on close)\n", "targets = []\n", "for sym, px in tqdm(prices.items(), desc=\"Computing targets\"):\n", "    if len(px) < 6: continue\n", "    fut = (px[\"close\"].shift(-5) / px[\"close\"]) - 1\n", "    t = (fut > 0).astype(int).rename(\"target\")\n", "    targets.append(t)\n", "\n", "all_targets = (\n", "    pd.concat(targets, keys=universe, names=[\"symbol\",\"date\"])\n", "      .swaplevel(0,1)\n", "      .sort_index()\n", ")\n", "\n", "# 6) Merge features with targets, drop any NA rows\n", "all_df = (\n", "    all_features\n", "      .join(all_targets, how=\"inner\")\n", "      .reset_index()\n", "      .set_index([\"date\",\"symbol\"])\n", "      .sort_index()\n", ")\n", "\n", "all_df[\"qpi_3\"] = (\n", "    all_df.groupby(\"symbol\")[\"close\"]\n", "          .transform(lambda s: qpi_single(s.values, window=3, lookback_years=1))\n", ")\n", "\n", "\n", "# 7) Train & back-test exactly as before\n", "feature_cols = fe.feature_names()\n", "\n", "# keep only tail events the model was designed for\n", "train_df = (\n", "    all_df[(all_df[\"qpi_3\"] < 15) &\n", "           (all_df[\"dollar_vol\"] >= LIQUIDITY_USD_MIN)]\n", "          .dropna(subset=feature_cols)\n", ")\n", "\n", "trainer = ModelTrainer(feature_cols=feature_cols)\n", "models  = trainer.yearly_models(\n", "    train_df, first_year=start_dt.year, last_year=end_dt.year\n", ")\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["date                       symbol\n", "2018-12-03 00:00:00-05:00  AMCX      89.005679\n", "                           AMRS      99.715424\n", "                           BGS       57.123231\n", "                           BIOS      59.194436\n", "                           BZH       29.347226\n", "                                       ...    \n", "2025-01-06 00:00:00-05:00  WPRT      50.199203\n", "                           WRLD      65.582830\n", "                           WS              NaN\n", "                           WSBF      13.155653\n", "                           WSR       16.873682\n", "Name: qpi_3, Length: 655525, dtype: float64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["all_df[\"qpi_3\"]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["universe_df = universe_selector.constituents_for(\n", "    pd.to_datetime(start_hist).tz_localize(\"EST\"),\n", "    pd.to_datetime(end_dt).tz_localize(\"EST\"),\n", "    min_mcap=1_000_000,\n", "    max_mcap=500_000_000\n", ")   \n", "\n", "# 1) parse into a single Series\n", "dates = pd.to_datetime(universe_df['date'])\n", "\n", "# 2) pick your “official” zone\n", "target_tz = \"US/Eastern\"   # DST‐aware; you can also use \"America/New_York\"\n", "\n", "# 3) localize naïve or convert aware\n", "if dates.dt.tz is None:\n", "    dates = dates.dt.tz_localize(target_tz)\n", "else:\n", "    dates = dates.dt.tz_convert(target_tz)\n", "\n", "# 4) assign back and re‐index\n", "universe_df = (\n", "    universe_df\n", "    .assign(date=dates)\n", "    .set_index(\"date\")\n", "    .sort_index()\n", ")\n", "\n", "def get_snapshot_before(ts) -> pd.Timestamp:\n", "    \"\"\"\n", "    Return the latest universe_df.index entry (a Timestamp) that is <= ts.\n", "    \"\"\"\n", "    # 1) make sure we have a Timestamp\n", "    ts = pd.to_datetime(ts)\n", "\n", "    # 2) if the index is timezone-aware, give ts the same tz\n", "    tz = getattr(universe_df.index, \"tz\", None)\n", "    if tz is not None:\n", "        # if ts is naive, localize it; if it's tz-aware, convert it\n", "        if ts.tzinfo is None:\n", "            ts = ts.tz_localize(tz)\n", "        else:\n", "            ts = ts.tz_convert(tz)\n", "\n", "    # 3) boolean mask against the DatetimeIndex\n", "    mask = universe_df.index <= ts\n", "    if not mask.any():\n", "        raise ValueError(f\"No snapshot on or before {ts!r}\")\n", "    return universe_df.index[mask].max()\n", "\n", "def constituents_for(ts):\n", "    snap_ts = get_snapshot_before(ts)\n", "    subset  = universe_df.loc[snap_ts]\n", "    return subset[\"ticker\"].tolist()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-27 00:21:33,113 - INFO - all_df date span: 2018-12-03 00:00:00-05:00 → 2025-01-06 00:00:00-05:00 (tz=US/Eastern)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-27 00:21:35,833 - INFO - Backtest finished – 252 bars\n"]}], "source": ["from strategies.ml import SimpleBacktester, MarketDataAdapter\n", "\n", "bt = SimpleBacktester(\n", "    all_df=all_df.sort_index(),\n", "    models=models,\n", "    feature_cols=feature_cols,\n", "    p_thresh=0.6,\n", "    max_positions=20,\n", ")\n", "\n", "equity = bt.run(start_dt, end_dt, constituents_for)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Done! Entire pipeline ran in batch, and equity curve saved.\n"]}], "source": ["equity.to_csv(\"equity_curve.csv\")\n", "\n", "print(\"✅ Done! Entire pipeline ran in batch, and equity curve saved.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}