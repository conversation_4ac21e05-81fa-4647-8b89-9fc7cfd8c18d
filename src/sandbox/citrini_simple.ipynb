{"cells": [{"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime, timedelta\n", "\n", "start_dt = datetime(2025, 1, 1, 9, 30, tzinfo=pytz.timezone(\"America/New_York\"))\n", "end_dt   = datetime(2025, 8, 13, 16, 0, tzinfo=pytz.timezone(\"America/New_York\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["from typing import Callable\n", "\n", "import pandas as pd\n", "import numpy as np\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "\n", "market_data = (MarketDataBuilder()\n", "                 .with_trade_session(\"full\") \n", "                 .with_period(\"intraday\")\n", "                 .build_market_data())\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data\n", "\n", "\n", "import math\n", "import pytz\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(start_date=start_dt.replace(tzinfo=None) - timed<PERSON><PERSON>(days=30))\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"Fetches daily data using the configured daily_market_data instance.\"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        print(\"Exception\", e)\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["\n", "# class SimpleEODSimulator:\n", "#     \"\"\"Simulate taking a long immediately after a signal and closing at EOD.\n", "\n", "#     * No stop‑loss or profit target – just raw edge measurement.\n", "#     * Entry price = open of the bar **after** the signal.\n", "#     * Exit price  = last close of the regular session (16:00 ET).\n", "#     \"\"\"\n", "\n", "#     def __init__(self, ticker_source, market_data_func: Callable[[str, datetime, datetime], pd.DataFrame]):\n", "#         self.ticker_source = ticker_source\n", "#         self.market_data_func = market_data_func\n", "#         self.est = pytz.timezone(\"America/New_York\")\n", "\n", "#     # ------------------------------------------------------------------\n", "#     # Single‑trade simulation\n", "#     # ------------------------------------------------------------------\n", "#     def simulate_trade(self, ticker: str, entry_time: datetime) -> dict:\n", "#         # normalize to Eastern\n", "#         entry_time = entry_time.astimezone(self.est)\n", "\n", "#         # define session bounds\n", "#         open_time = datetime.strptime(\"09:30:00\", \"%H:%M:%S\").time()\n", "#         close_time = datetime.strptime(\"16:00:00\", \"%H:%M:%S\").time()\n", "#         session_open = self.est.localize(datetime.combine(entry_time.date(), open_time))\n", "#         mkt_close    = self.est.localize(datetime.combine(entry_time.date(), close_time))\n", "\n", "#         # 1) compute stop_loss level = low of the day up to the signal\n", "#         pre_data = self.market_data_func(\n", "#             ticker=ticker,\n", "#             start_dt=session_open,\n", "#             end_dt=entry_time\n", "#         )\n", "#         stop_level = pre_data[\"low\"].min() if not pre_data.empty else None\n", "\n", "#         # 2) pull all bars from signal → market close\n", "#         day_data = self.market_data_func(\n", "#             ticker=ticker,\n", "#             start_dt=entry_time,\n", "#             end_dt=mkt_close\n", "#         )\n", "#         if day_data.empty:\n", "#             return {\n", "#                 \"ticker\": ticker,\n", "#                 \"entry_time\": entry_time,\n", "#                 \"result\": \"no_data\",\n", "#                 \"profit_pct\": 0.0\n", "#             }\n", "\n", "#         # 3) pick the bar *after* the signal as your entry\n", "#         entry_bars = day_data[day_data.index >= entry_time]\n", "#         if len(entry_bars) < 2:\n", "#             return {\n", "#                 \"ticker\": ticker,\n", "#                 \"entry_time\": entry_time,\n", "#                 \"result\": \"insufficient_bars\",\n", "#                 \"profit_pct\": 0.0\n", "#             }\n", "\n", "#         entry_bar         = entry_bars.iloc[1]\n", "#         entry_price       = entry_bar[\"open\"]\n", "#         entry_time_actual = entry_bar.name\n", "\n", "#         # 4) scan for stop‐loss hits\n", "#         if stop_level is not None:\n", "#             # only consider bars at-or-after actual entry\n", "#             post_entry = day_data[day_data.index >= entry_time_actual]\n", "#             hits = post_entry[post_entry[\"low\"] <= stop_level]\n", "#             if not hits.empty:\n", "#                 exit_time  = hits.index[0]\n", "#                 exit_price = stop_level\n", "#                 profit_pct = (exit_price - entry_price) / entry_price\n", "#                 return {\n", "#                     \"ticker\": ticker,\n", "#                     \"entry_time\": entry_time_actual,\n", "#                     \"exit_time\": exit_time,\n", "#                     \"entry_price\": entry_price,\n", "#                     \"exit_price\": exit_price,\n", "#                     \"result\": \"stop_loss\",\n", "#                     \"profit_pct\": profit_pct,\n", "#                 }\n", "\n", "#         # 5) no stop hit → exit at EOD close\n", "#         exit_bar   = day_data.iloc[-1]\n", "#         exit_price = exit_bar[\"close\"]\n", "#         profit_pct = (exit_price - entry_price) / entry_price\n", "\n", "#         return {\n", "#             \"ticker\": ticker,\n", "#             \"entry_time\": entry_time_actual,\n", "#             \"exit_time\": exit_bar.name,\n", "#             \"entry_price\": entry_price,\n", "#             \"exit_price\": exit_price,\n", "#             \"result\": \"eod_exit\",\n", "#             \"profit_pct\": profit_pct,\n", "#         }\n", "\n", "#     # ------------------------------------------------------------------\n", "#     # Batch simulation across all signals in a date range\n", "#     # ------------------------------------------------------------------\n", "#     def run_simulations(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:\n", "#         \"\"\"Run EOD simulations for every (ticker, signal_time) in ticker_source.\"\"\"\n", "#         try:\n", "#             tickers_df = self.ticker_source.get_tickers(start_time, end_time)\n", "#         except Exception as e:\n", "#             print(f\"Error retrieving tickers: {e}\")\n", "#             return pd.DataFrame()\n", "\n", "#         results = []\n", "#         for _, row in tickers_df.iterrows():\n", "#             try:\n", "#                 results.append(self.simulate_trade(row[\"ticker\"], row[\"created_at\"]))\n", "#             except Exception as e:\n", "#                 print(f\"Sim error {row['ticker']}: {e}\")\n", "#         return pd.DataFrame(results)\n", "\n", "\n", "# # -----------------------------------------------------------------------------\n", "# # Basic analysis helpers\n", "# # -----------------------------------------------------------------------------\n", "\n", "# def analyze_results(results_df: pd.DataFrame) -> None:\n", "#     \"\"\"Print edge stats: win‑rate, average return, median return.\"\"\"\n", "#     if results_df.empty:\n", "#         print(\"No results to analyze.\")\n", "#         return\n", "\n", "#     total = len(results_df)\n", "#     win_rate = (results_df[\"profit_pct\"] > 0).mean() * 100\n", "#     avg_ret = results_df[\"profit_pct\"].mean() * 100\n", "#     med_ret = results_df[\"profit_pct\"].median() * 100\n", "\n", "#     print(f\"Total trades : {total}\")\n", "#     print(f\"Win rate     : {win_rate:.2f}%\")\n", "#     print(f\"Average ret  : {avg_ret:.2f}%\")\n", "#     print(f\"Median ret   : {med_ret:.2f}%\")\n", "\n", "#     # Optional quick distribution plot (requires matplotlib)\n", "#     try:\n", "#         import matplotlib.pyplot as plt\n", "#         plt.figure(figsize=(8, 4))\n", "#         results_df[\"profit_pct\"].multiply(100).hist(bins=50)\n", "#         plt.axvline(avg_ret, linestyle=\"--\")\n", "#         plt.title(\"Distribution of End‑of‑Day Returns\")\n", "#         plt.xlabel(\"Return (%)\")\n", "#         plt.ylabel(\"Frequency\")\n", "#         plt.tight_layout()\n", "#         plt.show()\n", "#     except Exception:\n", "#         pass\n", "\n", "\n", "# # -----------------------------------------------------------------------------\n", "# # Example usage (remove if importing as a module)\n", "# # -----------------------------------------------------------------------------\n", "# from tickers.substack_ticker_source import SubstackTickerSource\n", "\n", "# tsrc = SubstackTickerSource(chat_id=\"836125\")\n", "\n", "# sim = SimpleEODSimulator(tsrc, get_per_minute_data)\n", "# df_results = sim.run_simulations(start_dt, end_dt)\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# df_results['entry_time'] = pd.to_datetime(\n", "#     df_results['entry_time'], utc=True\n", "# ).dt.tz_convert('America/New_York')\n", "\n", "# # build a boolean mask: hour > 9 OR (hour == 9 AND minute >= 30)\n", "# et = df_results['entry_time']\n", "# mask = (\n", "#     (et.dt.hour > 9) |\n", "#     ((et.dt.hour == 9) & (et.dt.minute >= 30))\n", "# )\n", "\n", "# results_filtered = df_results[mask].copy()\n", "# analyze_results(results_filtered)"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [], "source": ["# =============================\n", "# Tunables / feature switches\n", "# =============================\n", "EST = pytz.timezone(\"America/New_York\")\n", "\n", "# Liquidity guardrail: average $/min from open -> signal must be >= this\n", "MIN_DOLLAR_VOL = 1000\n", "\n", "# Position sizing budget (fraction of equity)\n", "TARGET_RISK_FRACTION = 0.1\n", "\n", "# Vol targeting mode: \"atr\" or \"intraday\"\n", "VOL_TARGET_MODE = \"intraday\"          # \"atr\" (daily ATR14) or \"intraday\" (minutes from open->signal)\n", "ATR_LOOKBACK = 14\n", "DAILY_BUFFER_DAYS = 60\n", "INITIAL_EQUITY = 100_000\n", "MAX_LEVERAGE = 1.0\n", "\n", "# Stop-loss at Low-of-Day up to entry?\n", "ENABLE_STOP_LOD = True\n", "\n", "# =============================\n", "# Signals (unchanged)\n", "# =============================\n", "def get_signals(ticker_source, start_time: datetime, end_time: datetime) -> pd.DataFrame:\n", "    try:\n", "        df = ticker_source.get_tickers(start_time, end_time).copy()\n", "        df[\"created_at\"] = pd.to_datetime(df[\"created_at\"], utc=True).dt.tz_convert(EST)\n", "        return df\n", "    except Exception as e:\n", "        print(f\"Error retrieving tickers: {e}\")\n", "        return pd.DataFrame(columns=[\"ticker\", \"created_at\"])\n", "\n", "# =============================\n", "# Cache\n", "# =============================\n", "class MarketDataCache:\n", "    def __init__(self, minute_func, daily_func):\n", "        self.minute_func = minute_func\n", "        self.daily_func  = daily_func\n", "        self._min_cache: dict[tuple, pd.DataFrame] = {}\n", "        self._day_cache: dict[tuple, pd.DataFrame] = {}\n", "        self._atr_cache: dict[tuple, float] = {}\n", "\n", "    def minute(self, ticker: str, start_dt: datetime, end_dt: datetime) -> pd.DataFrame:\n", "        key = (ticker, start_dt.isoformat(), end_dt.isoformat(), 60)\n", "        if key not in self._min_cache:\n", "            self._min_cache[key] = self.minute_func(ticker, start_dt, end_dt)\n", "        return self._min_cache[key]\n", "\n", "    def daily(self, ticker: str, start_dt: datetime, end_dt: datetime) -> pd.DataFrame:\n", "        key = (ticker, start_dt.date().isoformat(), end_dt.date().isoformat(), 86400)\n", "        if key not in self._day_cache:\n", "            self._day_cache[key] = self.daily_func(ticker, start_dt, end_dt)\n", "        return self._day_cache[key]\n", "\n", "    def atr14(self, ticker: str, ref_dt: datetime) -> float | None:\n", "        key = (ticker, ref_dt.date().isoformat(), ATR_LOOKBACK, DAILY_BUFFER_DAYS)\n", "        if key in self._atr_cache:\n", "            return self._atr_cache[key]\n", "        start = (ref_dt - timed<PERSON>ta(days=DAILY_BUFFER_DAYS)).replace(tzinfo=None)\n", "        end   = ref_dt.replace(tzinfo=None)\n", "        d = self.daily(ticker, start, end)\n", "        if d is None or len(d) < ATR_LOOKBACK + 1:\n", "            val = None\n", "        else:\n", "            high, low, close = d['high'], d['low'], d['close']\n", "            prev_close = close.shift(1)\n", "            tr = pd.concat([(high-low), (high-prev_close).abs(), (low-prev_close).abs()], axis=1).max(axis=1)\n", "            rv = tr.rolling(ATR_LOOKBACK).mean().iloc[-1]\n", "            val = float(rv) if pd.notna(rv) else None\n", "        self._atr_cache[key] = val\n", "        return val\n", "\n", "# =============================\n", "# Helpers\n", "# =============================\n", "def _session_bounds(day_dt: datetime):\n", "    open_t  = datetime.strptime(\"09:30:00\", \"%H:%M:%S\").time()\n", "    close_t = datetime.strptime(\"15:55:00\", \"%H:%M:%S\").time()\n", "    return (\n", "        EST.localize(datetime.combine(day_dt.date(), open_t)),\n", "        EST.localize(datetime.combine(day_dt.date(), close_t)),\n", "    )\n", "\n", "def _avg_dollar_vol(df: pd.DataFrame) -> float:\n", "    \"\"\"Average per-minute dollar volume since open.\"\"\"\n", "    if df is None or df.empty or 'volume' not in df.columns or 'close' not in df.columns:\n", "        return 0.0\n", "    return float((df['close'] * df['volume']).mean())\n", "\n", "def _intraday_sigma_price(pre_minutes: pd.DataFrame, entry_price: float) -> float | None:\n", "    \"\"\"\n", "    Realized vol estimate using minutes from open->signal, scaled to full day.\n", "    - Compute minute log returns r_i = ln(P_t/P_{t-1}) on 'close'.\n", "    - Daily RV estimate ≈ (390/n) * sum(r_i^2); sigma = sqrt(RV).\n", "    - Convert to price-vol: sigma_price ≈ entry_price * sigma_return.\n", "    \"\"\"\n", "    if pre_minutes is None or pre_minutes.empty or 'close' not in pre_minutes.columns:\n", "        return None\n", "    # need at least a few minutes to be meaningful\n", "    if len(pre_minutes) < 5:\n", "        return None\n", "    px = pre_minutes['close'].astype(float)\n", "    r = np.log(px / px.shift(1)).dropna()\n", "    n = len(r)\n", "    if n == 0:\n", "        return None\n", "    rv_daily = (390.0 / n) * float((r**2).sum())\n", "    if rv_daily <= 0 or not np.isfinite(rv_daily):\n", "        return None\n", "    sigma_return = np.sqrt(rv_daily)\n", "    return float(entry_price * sigma_return)\n", "\n", "# =============================\n", "# Simulator (adds stop LOD + intraday vol target + avg $/min)\n", "# =============================\n", "class SimpleEODVolTargetSimulator:\n", "    \"\"\"\n", "    - Entry: open of the bar after the signal\n", "    - Liquidity: average $/min from open -> signal >= MIN_DOLLAR_VOL\n", "    - Sizing:\n", "        * VOL_TARGET_MODE == \"atr\": shares ≈ (equity * TARGET_RISK_FRACTION) / ATR(14)\n", "        * VOL_TARGET_MODE == \"intraday\": shares ≈ (equity * TARGET_RISK_FRACTION) / sigma_price(open->signal)\n", "    - Stop (optional): low-of-day from open -> entry (EXIT at that price at first touch)\n", "    - Exit: at stop hit (if enabled) else EOD close\n", "    - Returns: list[trade_dict]\n", "    \"\"\"\n", "\n", "    def __init__(self, md_cache: MarketDataCache, initial_equity=INITIAL_EQUITY):\n", "        self.md = md_cache\n", "        self.initial_equity = float(initial_equity)\n", "\n", "    def simulate(self, signals_df: pd.DataFrame) -> list[dict]:\n", "        if signals_df is None or signals_df.empty:\n", "            return []\n", "\n", "        trades = []\n", "        for _, row in signals_df.iterrows():\n", "            try:\n", "                ticker = row[\"ticker\"]\n", "                sig_ts = row[\"created_at\"]  # EST\n", "\n", "                session_open, session_close = _session_bounds(sig_ts)\n", "                if sig_ts < session_open or sig_ts >= session_close:\n", "                    continue\n", "                \n", "                sig_ts = sig_ts + <PERSON><PERSON><PERSON>(minutes=5)\n", "\n", "                # ---------- Pre-signal minutes (liquidity & stop-level & intraday vol) ----------\n", "                pre_end = sig_ts - <PERSON><PERSON><PERSON>(minutes=6)\n", "                pre = self.md.minute(ticker, session_open, pre_end)\n", "                if pre is None or pre.empty:\n", "                    continue\n", "\n", "                avg_dv = _avg_dollar_vol(pre)\n", "                if avg_dv < MIN_DOLLAR_VOL:\n", "                    continue\n", "\n", "                # ---------- Post-signal minutes (entry + potential stop scan + exit) ----------\n", "                day = self.md.minute(ticker, sig_ts, session_close)\n", "                if day is None or day.empty:\n", "                    continue\n", "\n", "                entry_candidates = day[day.index >= sig_ts]\n", "                if len(entry_candidates) < 2:\n", "                    continue\n", "\n", "                entry_bar  = entry_candidates.iloc[1]\n", "                entry_time = entry_bar.name\n", "                entry_px   = float(entry_bar[\"open\"])\n", "\n", "                # ---------- Size by chosen volatility target ----------\n", "                if VOL_TARGET_MODE == \"intraday\":\n", "                    sigma_price = _intraday_sigma_price(pre, entry_px)\n", "                    per_share_risk = sigma_price\n", "                else:  # \"atr\"\n", "                    atr14 = self.md.atr14(ticker, sig_ts)\n", "                    per_share_risk = atr14\n", "                    \n", "                if not per_share_risk or per_share_risk <= 0 or not np.isfinite(per_share_risk):\n", "                    per_share_risk = 1\n", "\n", "                shares = int((self.initial_equity * TARGET_RISK_FRACTION) / per_share_risk)\n", "\n", "                # ---- Max leverage cap ----\n", "\n", "                max_shares_allowed = int((MAX_LEVERAGE * self.initial_equity) / entry_px)\n", "                if shares > max_shares_allowed:\n", "                    shares = max_shares_allowed\n", "\n", "                if shares <= 0:\n", "                    continue\n", "\n", "\n", "                # ---------- Optional stop-loss at Low-of-Day to entry ----------\n", "                stop_level = None\n", "                result_tag = \"eod_exit\"\n", "                exit_time  = None\n", "                exit_px    = None\n", "\n", "                if ENABLE_STOP_LOD:\n", "                    lod_to_entry = float(pre['low'].min())\n", "                    stop_level = lod_to_entry\n", "                    # after actual entry, scan for first bar whose low <= stop\n", "                    post = day[day.index >= entry_time]\n", "                    hit_stop = post[post['low'] <= stop_level]\n", "                    if not hit_stop.empty:\n", "                        exit_time = hit_stop.index[0]\n", "                        exit_px   = stop_level\n", "                        result_tag = \"stop_loss\"\n", "\n", "                # ---------- If no stop hit, exit at EOD close ----------\n", "                if exit_px is None:\n", "                    eod_bar   = day.iloc[-1]\n", "                    exit_time = eod_bar.name\n", "                    exit_px   = float(eod_bar[\"close\"])\n", "\n", "                pnl = (exit_px - entry_px) * shares\n", "                ret_pct = pnl / self.initial_equity\n", "\n", "                trades.append({\n", "                    \"ticker\": ticker,\n", "                    \"signal_time\": sig_ts,\n", "                    \"entry_time\": entry_time,\n", "                    \"exit_time\": exit_time,\n", "                    \"entry_px\": entry_px,\n", "                    \"exit_px\": exit_px,\n", "                    \"shares\": shares,\n", "                    \"per_share_risk\": float(per_share_risk),\n", "                    \"sizing_mode\": VOL_TARGET_MODE,\n", "                    \"avg_dollar_vol\": float(avg_dv),\n", "                    \"stop_level_lod\": float(stop_level) if stop_level is not None else None,\n", "                    \"pnl\": float(pnl),\n", "                    \"ret_pct\": float(ret_pct),\n", "                    \"result\": result_tag,\n", "                })\n", "            except Exception as e:\n", "                print(f\"Sim error {row.get('ticker','?')}: {e}\")\n", "                continue\n", "\n", "        return trades\n", "\n", "# =============================\n", "# Equity curve (unchanged)\n", "# =============================\n", "def equity_curve_from_trades(trades: list[dict], initial_equity: float = INITIAL_EQUITY) -> pd.DataFrame:\n", "    if not trades:\n", "        return pd.DataFrame(columns=[\"time\", \"equity\"]).set_index(\"time\")\n", "    tdf = pd.DataFrame(trades).copy()\n", "    tdf[\"time\"] = pd.to_datetime(tdf[\"exit_time\"]).dt.tz_convert(\"America/New_York\")\n", "    tdf = tdf.sort_values(\"time\")\n", "    tdf[\"equity\"] = initial_equity + tdf[\"pnl\"].cumsum()\n", "    return tdf[[\"time\", \"equity\"]].set_index(\"time\")\n", "\n", "def plot_equity_curve(eq_df: pd.DataFrame, title: str = \"Equity Curve\"):\n", "    if eq_df is None or eq_df.empty:\n", "        print(\"No trades / equity to plot.\")\n", "        return\n", "    import matplotlib.pyplot as plt\n", "    plt.figure(figsize=(10, 4))\n", "    eq_df[\"equity\"].plot()\n", "    plt.title(title)\n", "    plt.xlabel(\"Time\")\n", "    plt.ylabel(\"Equity ($)\")\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["# 1) Build caches once\n", "md_cache = MarketDataCache(minute_func=get_per_minute_data, daily_func=get_daily_data)\n", "\n", "# 2) Fetch signals once (and reuse)\n", "signals = get_signals(tsrc, start_dt, end_dt)"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 3) Run with current params\n", "sim = SimpleEODVolTargetSimulator(md_cache, initial_equity=INITIAL_EQUITY)\n", "trades_v1 = sim.simulate(signals)\n", "\n", "eq1 = equity_curve_from_trades(trades_v1, INITIAL_EQUITY)\n", "plot_equity_curve(eq1, \"Equity Curve – v1\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}