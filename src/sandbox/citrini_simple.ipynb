import pytz
from datetime import datetime, timedelta

start_dt = datetime(2025, 1, 1, 9, 30, tzinfo=pytz.timezone("America/New_York"))
end_dt   = datetime(2025, 8, 13, 16, 0, tzinfo=pytz.timezone("America/New_York"))

from typing import Callable

import pandas as pd
import numpy as np

import dotenv
dotenv.load_dotenv(".env", override=True)

from sandbox.utils import *
from marketdata import MarketDataBuilder
import pandas as pd
import numpy as np
from datetime import datetime
import pytz
import os
import matplotlib.pyplot as plt
import seaborn as sns
import sys

market_data = (MarketDataBuilder()
                 .with_trade_session("full") 
                 .with_period("intraday")
                 .build_market_data())

%load_ext autoreload
%autoreload 2

def get_per_minute_data(ticker, start_dt, end_dt):
    # Fetch per-minute data (interval=60 seconds)
    data = market_data.gather_historical_data(
        ticker=ticker,
        start_dt=start_dt,
        end_dt=end_dt,
        interval=60
    )
    return data


import math
import pytz
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

daily_market_data = (MarketDataBuilder()
                        .with_period("daily") 
                        .with_disk_data(start_date=start_dt.replace(tzinfo=None) - timedelta(days=30))
                        .build_market_data())

def get_daily_data(ticker, start_dt, end_dt):
    """Fetches daily data using the configured daily_market_data instance."""
    try:
        return daily_market_data.gather_historical_data(
            ticker=ticker,
            start_dt=start_dt,
            end_dt=end_dt,
            interval=86400 # Daily interval
        )
    except Exception as e:
        print("Exception", e)
        return None



# class SimpleEODSimulator:
#     """Simulate taking a long immediately after a signal and closing at EOD.

#     * No stop‑loss or profit target – just raw edge measurement.
#     * Entry price = open of the bar **after** the signal.
#     * Exit price  = last close of the regular session (16:00 ET).
#     """

#     def __init__(self, ticker_source, market_data_func: Callable[[str, datetime, datetime], pd.DataFrame]):
#         self.ticker_source = ticker_source
#         self.market_data_func = market_data_func
#         self.est = pytz.timezone("America/New_York")

#     # ------------------------------------------------------------------
#     # Single‑trade simulation
#     # ------------------------------------------------------------------
#     def simulate_trade(self, ticker: str, entry_time: datetime) -> dict:
#         # normalize to Eastern
#         entry_time = entry_time.astimezone(self.est)

#         # define session bounds
#         open_time = datetime.strptime("09:30:00", "%H:%M:%S").time()
#         close_time = datetime.strptime("16:00:00", "%H:%M:%S").time()
#         session_open = self.est.localize(datetime.combine(entry_time.date(), open_time))
#         mkt_close    = self.est.localize(datetime.combine(entry_time.date(), close_time))

#         # 1) compute stop_loss level = low of the day up to the signal
#         pre_data = self.market_data_func(
#             ticker=ticker,
#             start_dt=session_open,
#             end_dt=entry_time
#         )
#         stop_level = pre_data["low"].min() if not pre_data.empty else None

#         # 2) pull all bars from signal → market close
#         day_data = self.market_data_func(
#             ticker=ticker,
#             start_dt=entry_time,
#             end_dt=mkt_close
#         )
#         if day_data.empty:
#             return {
#                 "ticker": ticker,
#                 "entry_time": entry_time,
#                 "result": "no_data",
#                 "profit_pct": 0.0
#             }

#         # 3) pick the bar *after* the signal as your entry
#         entry_bars = day_data[day_data.index >= entry_time]
#         if len(entry_bars) < 2:
#             return {
#                 "ticker": ticker,
#                 "entry_time": entry_time,
#                 "result": "insufficient_bars",
#                 "profit_pct": 0.0
#             }

#         entry_bar         = entry_bars.iloc[1]
#         entry_price       = entry_bar["open"]
#         entry_time_actual = entry_bar.name

#         # 4) scan for stop‐loss hits
#         if stop_level is not None:
#             # only consider bars at-or-after actual entry
#             post_entry = day_data[day_data.index >= entry_time_actual]
#             hits = post_entry[post_entry["low"] <= stop_level]
#             if not hits.empty:
#                 exit_time  = hits.index[0]
#                 exit_price = stop_level
#                 profit_pct = (exit_price - entry_price) / entry_price
#                 return {
#                     "ticker": ticker,
#                     "entry_time": entry_time_actual,
#                     "exit_time": exit_time,
#                     "entry_price": entry_price,
#                     "exit_price": exit_price,
#                     "result": "stop_loss",
#                     "profit_pct": profit_pct,
#                 }

#         # 5) no stop hit → exit at EOD close
#         exit_bar   = day_data.iloc[-1]
#         exit_price = exit_bar["close"]
#         profit_pct = (exit_price - entry_price) / entry_price

#         return {
#             "ticker": ticker,
#             "entry_time": entry_time_actual,
#             "exit_time": exit_bar.name,
#             "entry_price": entry_price,
#             "exit_price": exit_price,
#             "result": "eod_exit",
#             "profit_pct": profit_pct,
#         }

#     # ------------------------------------------------------------------
#     # Batch simulation across all signals in a date range
#     # ------------------------------------------------------------------
#     def run_simulations(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
#         """Run EOD simulations for every (ticker, signal_time) in ticker_source."""
#         try:
#             tickers_df = self.ticker_source.get_tickers(start_time, end_time)
#         except Exception as e:
#             print(f"Error retrieving tickers: {e}")
#             return pd.DataFrame()

#         results = []
#         for _, row in tickers_df.iterrows():
#             try:
#                 results.append(self.simulate_trade(row["ticker"], row["created_at"]))
#             except Exception as e:
#                 print(f"Sim error {row['ticker']}: {e}")
#         return pd.DataFrame(results)


# # -----------------------------------------------------------------------------
# # Basic analysis helpers
# # -----------------------------------------------------------------------------

# def analyze_results(results_df: pd.DataFrame) -> None:
#     """Print edge stats: win‑rate, average return, median return."""
#     if results_df.empty:
#         print("No results to analyze.")
#         return

#     total = len(results_df)
#     win_rate = (results_df["profit_pct"] > 0).mean() * 100
#     avg_ret = results_df["profit_pct"].mean() * 100
#     med_ret = results_df["profit_pct"].median() * 100

#     print(f"Total trades : {total}")
#     print(f"Win rate     : {win_rate:.2f}%")
#     print(f"Average ret  : {avg_ret:.2f}%")
#     print(f"Median ret   : {med_ret:.2f}%")

#     # Optional quick distribution plot (requires matplotlib)
#     try:
#         import matplotlib.pyplot as plt
#         plt.figure(figsize=(8, 4))
#         results_df["profit_pct"].multiply(100).hist(bins=50)
#         plt.axvline(avg_ret, linestyle="--")
#         plt.title("Distribution of End‑of‑Day Returns")
#         plt.xlabel("Return (%)")
#         plt.ylabel("Frequency")
#         plt.tight_layout()
#         plt.show()
#     except Exception:
#         pass


# # -----------------------------------------------------------------------------
# # Example usage (remove if importing as a module)
# # -----------------------------------------------------------------------------
# from tickers.substack_ticker_source import SubstackTickerSource

# tsrc = SubstackTickerSource(chat_id="836125")

# sim = SimpleEODSimulator(tsrc, get_per_minute_data)
# df_results = sim.run_simulations(start_dt, end_dt)


# df_results['entry_time'] = pd.to_datetime(
#     df_results['entry_time'], utc=True
# ).dt.tz_convert('America/New_York')

# # build a boolean mask: hour > 9 OR (hour == 9 AND minute >= 30)
# et = df_results['entry_time']
# mask = (
#     (et.dt.hour > 9) |
#     ((et.dt.hour == 9) & (et.dt.minute >= 30))
# )

# results_filtered = df_results[mask].copy()
# analyze_results(results_filtered)

# =============================
# Tunables / feature switches
# =============================
EST = pytz.timezone("America/New_York")

# Liquidity guardrail: average $/min from open -> signal must be >= this
MIN_DOLLAR_VOL = 1000

# Position sizing budget (fraction of equity)
TARGET_RISK_FRACTION = 0.1

# Vol targeting mode: "atr" or "intraday"
VOL_TARGET_MODE = "intraday"          # "atr" (daily ATR14) or "intraday" (minutes from open->signal)
ATR_LOOKBACK = 14
DAILY_BUFFER_DAYS = 60
INITIAL_EQUITY = 100_000
MAX_LEVERAGE = 1.0

# Stop-loss at Low-of-Day up to entry?
ENABLE_STOP_LOD = True

# =============================
# Signals (unchanged)
# =============================
def get_signals(ticker_source, start_time: datetime, end_time: datetime) -> pd.DataFrame:
    try:
        df = ticker_source.get_tickers(start_time, end_time).copy()
        df["created_at"] = pd.to_datetime(df["created_at"], utc=True).dt.tz_convert(EST)
        return df
    except Exception as e:
        print(f"Error retrieving tickers: {e}")
        return pd.DataFrame(columns=["ticker", "created_at"])

# =============================
# Cache
# =============================
class MarketDataCache:
    def __init__(self, minute_func, daily_func):
        self.minute_func = minute_func
        self.daily_func  = daily_func
        self._min_cache: dict[tuple, pd.DataFrame] = {}
        self._day_cache: dict[tuple, pd.DataFrame] = {}
        self._atr_cache: dict[tuple, float] = {}

    def minute(self, ticker: str, start_dt: datetime, end_dt: datetime) -> pd.DataFrame:
        key = (ticker, start_dt.isoformat(), end_dt.isoformat(), 60)
        if key not in self._min_cache:
            self._min_cache[key] = self.minute_func(ticker, start_dt, end_dt)
        return self._min_cache[key]

    def daily(self, ticker: str, start_dt: datetime, end_dt: datetime) -> pd.DataFrame:
        key = (ticker, start_dt.date().isoformat(), end_dt.date().isoformat(), 86400)
        if key not in self._day_cache:
            self._day_cache[key] = self.daily_func(ticker, start_dt, end_dt)
        return self._day_cache[key]

    def atr14(self, ticker: str, ref_dt: datetime) -> float | None:
        key = (ticker, ref_dt.date().isoformat(), ATR_LOOKBACK, DAILY_BUFFER_DAYS)
        if key in self._atr_cache:
            return self._atr_cache[key]
        start = (ref_dt - timedelta(days=DAILY_BUFFER_DAYS)).replace(tzinfo=None)
        end   = ref_dt.replace(tzinfo=None)
        d = self.daily(ticker, start, end)
        if d is None or len(d) < ATR_LOOKBACK + 1:
            val = None
        else:
            high, low, close = d['high'], d['low'], d['close']
            prev_close = close.shift(1)
            tr = pd.concat([(high-low), (high-prev_close).abs(), (low-prev_close).abs()], axis=1).max(axis=1)
            rv = tr.rolling(ATR_LOOKBACK).mean().iloc[-1]
            val = float(rv) if pd.notna(rv) else None
        self._atr_cache[key] = val
        return val

# =============================
# Helpers
# =============================
def _session_bounds(day_dt: datetime):
    open_t  = datetime.strptime("09:30:00", "%H:%M:%S").time()
    close_t = datetime.strptime("15:55:00", "%H:%M:%S").time()
    return (
        EST.localize(datetime.combine(day_dt.date(), open_t)),
        EST.localize(datetime.combine(day_dt.date(), close_t)),
    )

def _avg_dollar_vol(df: pd.DataFrame) -> float:
    """Average per-minute dollar volume since open."""
    if df is None or df.empty or 'volume' not in df.columns or 'close' not in df.columns:
        return 0.0
    return float((df['close'] * df['volume']).mean())

def _intraday_sigma_price(pre_minutes: pd.DataFrame, entry_price: float) -> float | None:
    """
    Realized vol estimate using minutes from open->signal, scaled to full day.
    - Compute minute log returns r_i = ln(P_t/P_{t-1}) on 'close'.
    - Daily RV estimate ≈ (390/n) * sum(r_i^2); sigma = sqrt(RV).
    - Convert to price-vol: sigma_price ≈ entry_price * sigma_return.
    """
    if pre_minutes is None or pre_minutes.empty or 'close' not in pre_minutes.columns:
        return None
    # need at least a few minutes to be meaningful
    if len(pre_minutes) < 5:
        return None
    px = pre_minutes['close'].astype(float)
    r = np.log(px / px.shift(1)).dropna()
    n = len(r)
    if n == 0:
        return None
    rv_daily = (390.0 / n) * float((r**2).sum())
    if rv_daily <= 0 or not np.isfinite(rv_daily):
        return None
    sigma_return = np.sqrt(rv_daily)
    return float(entry_price * sigma_return)

# =============================
# Simulator (adds stop LOD + intraday vol target + avg $/min)
# =============================
class SimpleEODVolTargetSimulator:
    """
    - Entry: open of the bar after the signal
    - Liquidity: average $/min from open -> signal >= MIN_DOLLAR_VOL
    - Sizing:
        * VOL_TARGET_MODE == "atr": shares ≈ (equity * TARGET_RISK_FRACTION) / ATR(14)
        * VOL_TARGET_MODE == "intraday": shares ≈ (equity * TARGET_RISK_FRACTION) / sigma_price(open->signal)
    - Stop (optional): low-of-day from open -> entry (EXIT at that price at first touch)
    - Exit: at stop hit (if enabled) else EOD close
    - Returns: list[trade_dict]
    """

    def __init__(self, md_cache: MarketDataCache, initial_equity=INITIAL_EQUITY):
        self.md = md_cache
        self.initial_equity = float(initial_equity)

    def simulate(self, signals_df: pd.DataFrame) -> list[dict]:
        if signals_df is None or signals_df.empty:
            return []

        trades = []
        for _, row in signals_df.iterrows():
            try:
                ticker = row["ticker"]
                sig_ts = row["created_at"]  # EST

                session_open, session_close = _session_bounds(sig_ts)
                if sig_ts < session_open or sig_ts >= session_close:
                    continue
                
                sig_ts = sig_ts + timedelta(minutes=5)

                # ---------- Pre-signal minutes (liquidity & stop-level & intraday vol) ----------
                pre_end = sig_ts - timedelta(minutes=6)
                pre = self.md.minute(ticker, session_open, pre_end)
                if pre is None or pre.empty:
                    continue

                avg_dv = _avg_dollar_vol(pre)
                if avg_dv < MIN_DOLLAR_VOL:
                    continue

                # ---------- Post-signal minutes (entry + potential stop scan + exit) ----------
                day = self.md.minute(ticker, sig_ts, session_close)
                if day is None or day.empty:
                    continue

                entry_candidates = day[day.index >= sig_ts]
                if len(entry_candidates) < 2:
                    continue

                entry_bar  = entry_candidates.iloc[1]
                entry_time = entry_bar.name
                entry_px   = float(entry_bar["open"])

                # ---------- Size by chosen volatility target ----------
                if VOL_TARGET_MODE == "intraday":
                    sigma_price = _intraday_sigma_price(pre, entry_px)
                    per_share_risk = sigma_price
                else:  # "atr"
                    atr14 = self.md.atr14(ticker, sig_ts)
                    per_share_risk = atr14
                    
                if not per_share_risk or per_share_risk <= 0 or not np.isfinite(per_share_risk):
                    per_share_risk = 1

                shares = int((self.initial_equity * TARGET_RISK_FRACTION) / per_share_risk)

                # ---- Max leverage cap ----

                max_shares_allowed = int((MAX_LEVERAGE * self.initial_equity) / entry_px)
                if shares > max_shares_allowed:
                    shares = max_shares_allowed

                if shares <= 0:
                    continue


                # ---------- Optional stop-loss at Low-of-Day to entry ----------
                stop_level = None
                result_tag = "eod_exit"
                exit_time  = None
                exit_px    = None

                if ENABLE_STOP_LOD:
                    lod_to_entry = float(pre['low'].min())
                    stop_level = lod_to_entry
                    # after actual entry, scan for first bar whose low <= stop
                    post = day[day.index >= entry_time]
                    hit_stop = post[post['low'] <= stop_level]
                    if not hit_stop.empty:
                        exit_time = hit_stop.index[0]
                        exit_px   = stop_level
                        result_tag = "stop_loss"

                # ---------- If no stop hit, exit at EOD close ----------
                if exit_px is None:
                    eod_bar   = day.iloc[-1]
                    exit_time = eod_bar.name
                    exit_px   = float(eod_bar["close"])

                pnl = (exit_px - entry_px) * shares
                ret_pct = pnl / self.initial_equity

                trades.append({
                    "ticker": ticker,
                    "signal_time": sig_ts,
                    "entry_time": entry_time,
                    "exit_time": exit_time,
                    "entry_px": entry_px,
                    "exit_px": exit_px,
                    "shares": shares,
                    "per_share_risk": float(per_share_risk),
                    "sizing_mode": VOL_TARGET_MODE,
                    "avg_dollar_vol": float(avg_dv),
                    "stop_level_lod": float(stop_level) if stop_level is not None else None,
                    "pnl": float(pnl),
                    "ret_pct": float(ret_pct),
                    "result": result_tag,
                })
            except Exception as e:
                print(f"Sim error {row.get('ticker','?')}: {e}")
                continue

        return trades

# =============================
# Equity curve (unchanged)
# =============================
def equity_curve_from_trades(trades: list[dict], initial_equity: float = INITIAL_EQUITY) -> pd.DataFrame:
    if not trades:
        return pd.DataFrame(columns=["time", "equity"]).set_index("time")
    tdf = pd.DataFrame(trades).copy()
    tdf["time"] = pd.to_datetime(tdf["exit_time"]).dt.tz_convert("America/New_York")
    tdf = tdf.sort_values("time")
    tdf["equity"] = initial_equity + tdf["pnl"].cumsum()
    return tdf[["time", "equity"]].set_index("time")

def plot_equity_curve(eq_df: pd.DataFrame, title: str = "Equity Curve"):
    if eq_df is None or eq_df.empty:
        print("No trades / equity to plot.")
        return
    import matplotlib.pyplot as plt
    plt.figure(figsize=(10, 4))
    eq_df["equity"].plot()
    plt.title(title)
    plt.xlabel("Time")
    plt.ylabel("Equity ($)")
    plt.tight_layout()
    plt.show()


# 1) Build caches once
md_cache = MarketDataCache(minute_func=get_per_minute_data, daily_func=get_daily_data)

# 2) Fetch signals once (and reuse)
signals = get_signals(tsrc, start_dt, end_dt)

# 3) Run with current params
sim = SimpleEODVolTargetSimulator(md_cache, initial_equity=INITIAL_EQUITY)
trades_v1 = sim.simulate(signals)

eq1 = equity_curve_from_trades(trades_v1, INITIAL_EQUITY)
plot_equity_curve(eq1, "Equity Curve – v1")