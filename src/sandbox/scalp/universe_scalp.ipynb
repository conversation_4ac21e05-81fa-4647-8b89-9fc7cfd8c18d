{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "\n", "import argparse\n", "import datetime as dt\n", "from dataclasses import dataclass\n", "from itertools import product\n", "from typing import Iterable, List, Tuple\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from numpy.typing import NDArray\n", "from sklearn.model_selection import TimeSeriesSplit\n", "import pytz\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "\n", "START_DT = eastern_tz.localize(dt.datetime(2025, 1, 1))\n", "END_DT = eastern_tz.localize(dt.datetime(2025, 6, 1))\n", "\n", "UNIVERSE: List[str] = [\n", "    \"TQQQ\", \"SPXL\", \"SPY\", \"QQQ\", \"TSLA\", \"NVDA\", \"AMD\", \"AAPL\", \"MSFT\"\n", "]\n", "\n", "SLICES = {\n", "    \"open_drive\":  (dt.time(9, 35), dt.time(10, 45)),\n", "    \"lunch_lull\":  (dt.time(12, 0), dt.time(13, 30)),\n", "    \"power_hour\":  (dt.time(14, 0), dt.time(15, 45)),\n", "    \"open_drive_long\":  (dt.time(9, 35), dt.time(12, 00)),\n", "    \"lunch_lull_long\":  (dt.time(12, 0), dt.time(14, 00)),\n", "}\n", "\n", "H_GRID   = np.array([60, 120, 180, 300, 600, 900])          # seconds\n", "F_GRID = np.round(np.arange(0.30, 1.55, 0.15), 2)\n", "K_GRID = np.round(np.arange(0.60, 3.10, 0.20), 2)\n", "# F_GRID = np.linspace(0.4, 0.9, 5)         # ATR fractions\n", "# KF_RATIO = np.linspace(1.1, 3.0, 10)\n", "\n", "# ── trading‑cost constants (per share, USD) ───────────────────────────────────\n", "COMM      = 0.003\n", "ADD_REB   = -0.0020  # maker rebate is *negative* cost\n", "TAKE_FEE  = 0.0020\n", "SLIP_TICK = 0.005    # ±½‑tick on exits (assume $0.01 tick ETF, $0.10 tick stocks)\n", "\n", "# ── account-level constants ───────────────────────────\n", "ACCOUNT_EQUITY      = 100_000          # put your real figure here\n", "POSITION_RISK_PCT   = 0.005            # 0.5 % of equity per trade\n", "INTRADAY_MARGIN_MAX = 4               # 4× daytime leverage\n", "MAX_TRADES_PER_DAY  = int((1 / POSITION_RISK_PCT) * INTRADAY_MARGIN_MAX)   # ≈800\n", "TRADING_DAYS_PER_YR = 252"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# ──────────────────────────────────────────────────────────────────────────────\n", "# DATACLASS HELPERS\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "\n", "@dataclass\n", "class SimParams:\n", "    F: float\n", "    K: float\n", "    H: int  # seconds\n", "\n", "@dataclass\n", "class SliceDef:\n", "    name: str\n", "    t0: dt.time \n", "    t1: dt.time\n", "\n", "@dataclass\n", "class SimMetrics:\n", "    total: float\n", "    pf: float\n", "    sharpe: float\n", "    tstat: float\n", "    trades: int\n", "    dd: float\n", "    \n", "@dataclass\n", "class Trade:\n", "    entry_ts: pd.Timestamp\n", "    exit_ts : pd.Timestamp\n", "    entry_px: float\n", "    exit_px : float\n", "    pnl     : float   # net P&L in dollars (after fees & slippage)\n", "    capital : float   # buying‑power consumed for the trade"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                            .with_trade_session(\"full\")\n", "                            .with_adjusted(False)\n", "                            .with_period(\"intraday\")\n", "                            .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"Fetches daily data using the configured daily_market_data instance.\"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        print(\"Exception\", e)\n", "        return None\n", "\n", "_min_cache = {}\n", "\n", "def get_intraday_data(\n", "    ticker: str,\n", "    start_dt: dt.datetime,\n", "    end_dt: dt.datetime,\n", "    interval_secs: int = 1,\n", "    *,\n", "    use_cache: bool = True,\n", ") -> pd.DataFrame:\n", "    \"\"\"\n", "    Fetch intraday OHLCV with an arbitrary interval (seconds).\n", "    Results are memoised in RAM for the life of the notebook.\n", "    \"\"\"\n", "    key = (ticker, start_dt.isoformat(), end_dt.isoformat(), interval_secs)\n", "    if use_cache and key in _min_cache:\n", "        return _min_cache[key]\n", "\n", "    df = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=interval_secs\n", "    )\n", "    if use_cache:\n", "        _min_cache[key] = df\n", "        \n", "    # if df.empty:\n", "    #     return df\n", "\n", "    # # Make sure clock times line up with SLICES\n", "    # if df.index.tz is None:\n", "    #     df = df.tz_localize(\"UTC\")\n", "    # df = df.tz_convert(\"US/Eastern\")\n", "    \n", "    return df\n", "\n", "# ────────────────────────────────────────────────────────────\n", "# Canonical intrabar-range estimator\n", "# ────────────────────────────────────────────────────────────\n", "def ewma_high_low(df: pd.DataFrame, window: int) -> NDArray[np.float_]:\n", "    \"\"\"\n", "    R_t  =  EWMA { max(high) – min(low) }   over a rolling *window* (sec).\n", "    Same math for both CV and live trading.\n", "    \"\"\"\n", "    hp = df['high'].rolling(window=window, min_periods=1).max()\n", "    lp = df['low'] .rolling(window=window, min_periods=1).min()\n", "    raw = (hp - lp).to_numpy()\n", "\n", "    alpha = 1.0 - np.exp(np.log(0.5) / window)        # convert half-life\n", "    return pd.Series(raw).ewm(alpha=alpha).mean().to_numpy()\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def exit_cost(is_tp: bool) -> float:\n", "    \"\"\"Cost per share for the exit leg.\n", "    • TP assumed passive (maker) so rebate offsets commission; slip may be +/‑\n", "    • Stop assumed aggressive (taker)\n", "    \"\"\"\n", "    base = COMM + (TAKE_FEE if not is_tp else ADD_REB)\n", "    slip = SLIP_TICK if not is_tp else 0.0\n", "    return base + slip\n", "\n", "def bracket_sim(prc: np.n<PERSON><PERSON>,\n", "                R:   np.n<PERSON><PERSON>,\n", "                params: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "                ts: np.n<PERSON>ray) -> list[Trade]:\n", "    \"\"\"Return a list of *Trade* objects obeying the 0.5 % risk rule.\"\"\"\n", "    F, K = params.F, params.K\n", "    trades: list[Trade] = []\n", "\n", "    state = 0            # 0 = flat, 1 = long, −1 = short\n", "    bracket_set = False\n", "    anchor = upper = lower = np.nan\n", "    entry_px = tp = sl = np.nan\n", "    entry_ts = None\n", "    \n", "    prev_date = None \n", "\n", "    for i in range(1, len(prc)):\n", "        p, rng = prc[i], R[i]\n", "        if np.isnan(rng) or rng == 0:\n", "            continue\n", "        \n", "        curr_date = pd.Timestamp(ts[i]).date()\n", "        if prev_date is None or curr_date != prev_date:\n", "            state       = 0          # flat / no position\n", "            bracket_set = False      # drop any pending bracket\n", "        prev_date = curr_date\n", "\n", "        # ── flat: set/monitor bracket ────────────────────────────────────────\n", "        if state == 0:\n", "            if not bracket_set:\n", "                anchor = prc[i-1] if pd.Timestamp(ts[i-1]).date() == curr_date else p\n", "                upper  = anchor + rng * F / 2\n", "                lower  = anchor - rng * F / 2\n", "                bracket_set = True\n", "                continue\n", "\n", "            # ---- SHORT entry ------------------------------------------------\n", "            if p >= upper:\n", "                state    = -1\n", "                entry_px = upper\n", "                entry_ts = pd.Timestamp(ts[i])\n", "                tp       = entry_px - rng * F\n", "                sl       = entry_px + rng * (K - F) / 2\n", "                bracket_set = False\n", "\n", "            # ---- LONG entry -------------------------------------------------\n", "            elif p <= lower:\n", "                state    = 1\n", "                entry_px = lower\n", "                entry_ts = pd.Timestamp(ts[i])\n", "                tp       = entry_px + rng * F\n", "                sl       = entry_px - rng * (K - F) / 2\n", "                bracket_set = False\n", "\n", "        # ── LONG management ──────────────────────────────────────────────────\n", "        elif state == 1:\n", "            if p >= tp or p <= sl:\n", "                exit_px   = tp if p >= tp else sl\n", "                pnl_raw   = exit_px - entry_px\n", "                pnl_net   = pnl_raw - exit_cost(p >= tp)\n", "                stop_dist = entry_px - sl               # >0\n", "\n", "                risk_dol   = ACCOUNT_EQUITY * POSITION_RISK_PCT\n", "                shares     = risk_dol / stop_dist\n", "                shares = min(shares, ACCOUNT_EQUITY * INTRADAY_MARGIN_MAX / entry_px)\n", "                capital_bp = shares * entry_px / INTRADAY_MARGIN_MAX\n", "\n", "                trades.append(\n", "                    Trade(entry_ts, pd.Timestamp(ts[i]), entry_px, exit_px,\n", "                          shares * pnl_net, capital_bp)\n", "                )\n", "                state = 0\n", "\n", "        # ── SHORT management ─────────────────────────────────────────────────\n", "        elif state == -1:\n", "            if p <= tp or p >= sl:\n", "                exit_px   = tp if p <= tp else sl\n", "                pnl_raw   = entry_px - exit_px\n", "                pnl_net   = pnl_raw - exit_cost(p <= tp)\n", "                stop_dist = sl - entry_px               # >0\n", "\n", "                risk_dol   = ACCOUNT_EQUITY * POSITION_RISK_PCT\n", "                shares     = risk_dol / stop_dist\n", "                shares = min(shares, ACCOUNT_EQUITY * INTRADAY_MARGIN_MAX / entry_px)\n", "                capital_bp = shares * entry_px / INTRADAY_MARGIN_MAX\n", "\n", "                trades.append(\n", "                    Trade(entry_ts, pd.Timestamp(ts[i]), entry_px, exit_px,\n", "                          shares * pnl_net, capital_bp)\n", "                )\n", "                state = 0\n", "    return trades\n", "\n", "def calculate_penalty(trades_df: pd.DataFrame) -> float:\n", "    \"\"\"Calculates a penalty factor between 0.0 and 1.0.\"\"\"\n", "    daily_stats = trades_df.groupby(trades_df['entry_ts'].dt.date).agg(\n", "        trade_count=('pnl', 'count'),\n", "        capital_used=('capital', 'sum')\n", "    )\n", "\n", "    trade_count_breach = max(0, (daily_stats['trade_count'] - MAX_TRADES_PER_DAY).sum())\n", "    capital_breach = max(0, (daily_stats['capital_used'] - (ACCOUNT_EQUITY * INTRADAY_MARGIN_MAX)).sum())\n", "\n", "    # Normalize the penalties to be less sensitive to one-off large breaches\n", "    # These scaling factors may need tuning\n", "    trade_penalty = 1.0 - (1.0 / (1 + 0.01 * trade_count_breach))\n", "    capital_penalty = 1.0 - (1.0 / (1 + 0.00001 * capital_breach))\n", "\n", "    # The total penalty is the larger of the two\n", "    return max(trade_penalty, capital_penalty)\n", "\n", "def metrics_from_pnl(pnl: NDArray[np.float_]) -> SimMetrics:\n", "    if len(pnl) < 10:\n", "        return SimMetrics(total=0.0, pf=np.nan, sharpe=np.nan,\n", "                          tstat=np.nan, trades=len(pnl), dd=np.nan)\n", "\n", "    cum = pnl.cumsum()\n", "    dd  = (cum - np.maximum.accumulate(cum)).min()\n", "    total = cum[-1]                 # ← NEW\n", "    avg, sd = pnl.mean(), pnl.std(ddof=1)\n", "    sharpe  = np.sqrt(252*6.5*60) * avg / sd\n", "    tstat   = avg / (sd / np.sqrt(len(pnl)))\n", "    pf      = pnl[pnl > 0].sum() / abs(pnl[pnl < 0].sum()) if (pnl < 0).any() else np.inf\n", "    return SimMetrics(total=total, pf=pf, sharpe=sharpe,\n", "                      tstat=tstat, trades=len(pnl), dd=dd)\n", "\n", "def metrics_from_trades(trades):\n", "    pnl = np.array([t.pnl for t in trades], float)\n", "    return metrics_from_pnl(pnl)\n", "\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "# CV + WALK FORWARD\n", "# ──────────────────────────────────────────────────────────────────────────────\n", "\n", "def cross_val_rank(bar: pd.DataFrame,\n", "                   params_grid: List[SimParams],\n", "                   *,\n", "                   min_total_trades: int = 5) -> SimParams | None:\n", "    \"\"\"Pick the parameter‑set that maximises *capital‑efficient CAGR*.\"\"\"\n", "    tscv = TimeSeriesSplit(n_splits=5)\n", "    prc  = bar['close'].to_numpy()\n", "    ts   = bar.index.to_numpy()\n", "\n", "    best, best_score = None, -np.inf\n", "\n", "    for p in params_grid:\n", "        oos_trades: list[Trade] = []\n", "\n", "        for train_idx, test_idx in tscv.split(bar):\n", "            purge = p.H                           # purge one‑H window\n", "            train_idx = train_idx[train_idx < train_idx.max() - purge]\n", "            test_idx  = test_idx[test_idx >= test_idx.min() + purge]\n", "            if len(test_idx) == 0:\n", "                continue\n", "\n", "            lookback = max(0, test_idx.min() - p.H * 2)\n", "            idx_for_rng = slice(lookback, test_idx.max() + 1)\n", "\n", "            R_full = ewma_high_low(bar.iloc[idx_for_rng], p.H)\n", "            R_test = R_full[test_idx - lookback]\n", "            trades = bracket_sim(prc[test_idx], R_test, p, ts=ts[test_idx])\n", "            oos_trades.extend(trades)\n", "\n", "        if len(oos_trades) < min_total_trades:\n", "            continue\n", "\n", "        metrics = metrics_from_trades(oos_trades)\n", "        score = metrics.total * (1 - calculate_penalty(pd.DataFrame(oos_trades)))\n", "        \n", "        if np.isfinite(score) and score > best_score:\n", "            best_score, best = score, p\n", "                        \n", "    return best\n", "\n", "# ── walk‑forward evaluation (returns trades & CAGR) ──────────────────────────\n", "\n", "def calculate_capital_efficient_cagr(\n", "    trades: List[Trade],\n", "    min_trades: int = 10\n", ") -> float:\n", "    if len(trades) < min_trades:\n", "        return -np.inf\n", "\n", "    trades_df = pd.DataFrame(trades)\n", "    trades_df['date'] = trades_df['entry_ts'].dt.normalize()\n", "\n", "    # --- Feasibility Test per Trading Day ---\n", "    daily_usage = trades_df.groupby('date').agg(\n", "        trade_count=('pnl', 'count'),\n", "        capital_used=('capital', 'sum')\n", "    )\n", "\n", "    max_allowed_capital = ACCOUNT_EQUITY * INTRADAY_MARGIN_MAX\n", "   \n", "    # --- Annualized CAGR Calculation ---\n", "    total_pnl = trades_df['pnl'].sum()\n", "    \n", "    # Ensure the period is at least one day to avoid division by zero\n", "    if trades_df.empty:\n", "        return 0.0\n", "        \n", "    start_date = trades_df['entry_ts'].min()\n", "    end_date = trades_df['exit_ts'].max()\n", "    span_days = (end_date - start_date).days + 1\n", "    \n", "    # Avoid division by zero for very short periods\n", "    if span_days <= 0 or TRADING_DAYS_PER_YR <= 0:\n", "        return 0.0\n", "\n", "    span_years = span_days / TRADING_DAYS_PER_YR\n", "    \n", "    # Calculate final equity and CAGR\n", "    final_equity = ACCOUNT_EQUITY + total_pnl\n", "    if final_equity <= 0: # Cannot take a root of a negative number\n", "        return -1.0 # Represents a 100% loss\n", "\n", "    cagr = (final_equity / ACCOUNT_EQUITY) ** (1 / span_years) - 1\n", "    \n", "    return cagr\n", "\n", "\n", "def walk_forward(bar: pd.DataFrame, params: SimParams):\n", "    prc, ts = bar['close'].to_numpy(), bar.index.to_numpy()\n", "    R       = ewma_high_low(bar, params.H)\n", "    trades  = bracket_sim(prc, R, params, ts)\n", "    cagr    = calculate_capital_efficient_cagr(trades, min_trades=5)\n", "    mets    = metrics_from_trades(trades)     # SimMetrics-style stats\n", "    return trades, cagr, mets                 # three-item tuple\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[TQQQ | open_drive] … trades 129 cagr 0.***************** mets SimMetrics(total=np.float64(1604.*************), pf=np.float64(1.****************), sharpe=np.float64(4.***************), tstat=np.float64(0.****************), trades=129, dd=np.float64(-6729.************))\n", "x no edge found\n", "[TQQQ | lunch_lull] … trades 87 cagr -0.298432231585841 mets SimMetrics(total=np.float64(-6000.992419631714), pf=np.float64(0.6655692156348515), sharpe=np.float64(-59.05905566971816), tstat=np.float64(-1.7571690730962364), trades=87, dd=np.float64(-6753.832081938344))\n", "x no edge found\n", "[TQQQ | power_hour] … trades 264 cagr 0.5005281813474414 mets SimMetrics(total=np.float64(7342.767787997162), pf=np.float64(1.1201676534335347), sharpe=np.float64(17.68663196666762), tstat=np.float64(0.9166731872652316), trades=264, dd=np.float64(-5970.982123716762))\n", "x no edge found\n", "[TQQQ | open_drive_long] … trades 190 cagr 0.15453248648000195 mets SimMetrics(total=np.float64(2540.7083576769596), pf=np.float64(1.1035733027050754), sharpe=np.float64(13.711230045996835), tstat=np.float64(0.6028657952520851), trades=190, dd=np.float64(-2790.6694211851045))\n", "x no edge found\n", "[TQQQ | lunch_lull_long] … trades 139 cagr 0.17048479665963634 mets SimMetrics(total=np.float64(2786.6901987519996), pf=np.float64(1.2101952949377277), sharpe=np.float64(24.6344185226219), tstat=np.float64(0.9264396669064223), trades=139, dd=np.float64(-2524.306109045253))\n", "x no edge found\n", "[SPXL | open_drive] … trades 151 cagr 0.08301113956000683 mets SimMetrics(total=np.float64(1402.1161695378692), pf=np.float64(1.031344404365154), sharpe=np.float64(4.7197149164064145), tstat=np.float64(0.18499997367462137), trades=151, dd=np.float64(-9577.217353802702))\n", "x no edge found\n", "[SPXL | lunch_lull] … trades 58 cagr -0.46689410455920954 mets SimMetrics(total=np.float64(-10401.494177364675), pf=np.float64(0.5766602319988811), sharpe=np.float64(-62.469859849212334), tstat=np.float64(-1.5175812582572468), trades=58, dd=np.float64(-14365.56243464774))\n", "x no edge found\n", "[SPXL | power_hour] … trades 97 cagr -0.13093170843824997 mets SimMetrics(total=np.float64(-2420.4932948891287), pf=np.float64(0.8658608644667309), sharpe=np.float64(-21.23478417191525), tstat=np.float64(-0.6671156737814756), trades=97, dd=np.float64(-4257.7110246002185))\n", "x no edge found\n", "[SPXL | open_drive_long] … trades 238 cagr 0.04451389797319116 mets SimMetrics(total=np.float64(763.3234705150638), pf=np.float64(1.0097534196209723), sharpe=np.float64(1.452651530374113), tstat=np.float64(0.07148539982811533), trades=238, dd=np.float64(-21057.7995982184))\n", "x no edge found\n", "[SPXL | lunch_lull_long] … trades 16 cagr -0.1637852323415866 mets SimMetrics(total=np.float64(-3074.857757677248), pf=np.float64(0.0), sharpe=np.float64(-924.5934340899385), tstat=np.float64(-11.797180449702244), trades=16, dd=np.float64(-2998.156846651972))\n", "x no edge found\n", "[SPY | open_drive] … trades 96 cagr 0.42154569641045536 mets SimMetrics(total=np.float64(6334.091523343145), pf=np.float64(1.3180480067380478), sharpe=np.float64(42.78860249427312), tstat=np.float64(1.337307083404545), trades=96, dd=np.float64(-2977.5554843398995))\n", "✓ edge found {'symbol': 'SPY', 'slice': 'open_drive', 'F': np.float64(0.6), 'K': np.float64(1.8), 'H': np.int64(600), 'trades': 96, 'sharpe': np.float64(42.78860249427312), 'pf': np.float64(1.3180480067380478), 'tstat': np.float64(1.337307083404545), 'dd': np.float64(-2977.5554843398995), 'total': np.float64(6334.091523343145), 'cagr': np.float64(0.42154569641045536)}\n", "[SPY | lunch_lull] … trades 52 cagr 0.05932668372733074 mets SimMetrics(total=np.float64(1011.3794631987149), pf=np.float64(1.167563983664546), sharpe=np.float64(21.1026099176817), tstat=np.float64(0.48540614388095116), trades=52, dd=np.float64(-1279.8006425984734))\n", "x no edge found\n", "[SPY | power_hour] … trades 102 cagr 0.4209044962908861 mets SimMetrics(total=np.float64(6325.7154920828), pf=np.float64(1.4144219521431265), sharpe=np.float64(52.567600657612836), tstat=np.float64(1.6935018170137757), trades=102, dd=np.float64(-2494.8633026125026))\n", "✓ edge found {'symbol': 'SPY', 'slice': 'power_hour', 'F': np.float64(0.6), 'K': np.float64(2.4), 'H': np.int64(600), 'trades': 102, 'sharpe': np.float64(52.567600657612836), 'pf': np.float64(1.4144219521431265), 'tstat': np.float64(1.6935018170137757), 'dd': np.float64(-2494.8633026125026), 'total': np.float64(6325.7154920828), 'cagr': np.float64(0.4209044962908861)}\n", "[SPY | open_drive_long] … trades 86 cagr 0.33804368077640934 mets SimMetrics(total=np.float64(5216.079262876698), pf=np.float64(1.484393850319432), sharpe=np.float64(58.365999981706565), tstat=np.float64(1.726539753399473), trades=86, dd=np.float64(-3037.504234140534))\n", "✓ edge found {'symbol': 'SPY', 'slice': 'open_drive_long', 'F': np.float64(0.6), 'K': np.float64(3.0), 'H': np.int64(900), 'trades': 86, 'sharpe': np.float64(58.365999981706565), 'pf': np.float64(1.484393850319432), 'tstat': np.float64(1.726539753399473), 'dd': np.float64(-3037.504234140534), 'total': np.float64(5216.079262876698), 'cagr': np.float64(0.33804368077640934)}\n", "[SPY | lunch_lull_long] … trades 59 cagr -0.01848381225972484 mets SimMetrics(total=np.float64(-325.22317140410723), pf=np.float64(0.9676792720698824), sharpe=np.float64(-4.808180331719714), tstat=np.float64(-0.11780783626088426), trades=59, dd=np.float64(-2024.4783652833426))\n", "x no edge found\n", "[QQQ | open_drive] … "]}], "source": ["def run_symbol_slice(sym: str, slc: SliceDef, start: dt.datetime, end: dt.datetime) -> dict | None:\n", "    df = get_intraday_data(sym, start, end)\n", "    if df.empty:\n", "        print(f\"x no data {sym} {slc.name}\")\n", "        return None\n", "    bar = df.between_time(slc.t0, slc.t1)\n", "    if bar.empty:\n", "        print(f\"x no data {sym} {slc.name}\")\n", "        return None\n", "\n", "    grid = [SimParams(F,K,H) for F,K,H in product(F_GRID, K_GRID, H_GRID) if K>F]\n", "    best = cross_val_rank(bar, grid)\n", "    if best is None:\n", "        print(f\"x no best {sym} {slc.name}\")\n", "        return None\n", "\n", "    oos_start = end - dt.<PERSON><PERSON><PERSON>(days=45)\n", "    trades, cagr, mets = walk_forward(bar[bar.index >= oos_start], best)\n", "    \n", "    print(\"trades\", len(trades), \"cagr\", cagr, \"mets\", mets)\n", "    \n", "    return dict(symbol=sym, slice=slc.name,\n", "                F=best.F, K=best.K, H=best.H,\n", "                trades=len(trades),\n", "                sharpe=mets.sharpe, pf=mets.pf,\n", "                tstat=mets.tstat, dd=mets.dd,\n", "                total=mets.total, cagr=cagr)\n", "rows = []\n", "for sym in UNIVERSE:\n", "    for name,(t0,t1) in SLICES.items():\n", "        sd = SliceDef(name,t0,t1)\n", "        print(f\"[{sym} | {name}] …\", end=\" \")\n", "        try:\n", "            row = run_symbol_slice(sym, sd, START_DT, END_DT)\n", "            if row and row['sharpe'] > 0.7 and row['pf'] > 1.3:\n", "                rows.append(row)\n", "                print(f\"✓ edge found {row}\")\n", "            else:\n", "                print(\"x no edge found\")\n", "        except Exception as e:\n", "            print(f\"⚠ {e}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== OOS edges ===\n", "symbol           slice    F   K   H  trades sharpe   pf tstat       dd        total     cagr\n", "  TSLA      lunch_lull 1.35 2.2 900      48 123.15 2.46  2.72  -2016.3 19764.356953 1.809335\n", "  SPXL lunch_lull_long 1.50 1.8 300      45  84.64 2.51  1.81  -7252.6 26820.880234 2.899435\n", "  SPXL open_drive_long 1.35 1.6 600      58  75.76 2.39  1.84 -11988.9 31110.717289 3.717871\n", "  MSFT open_drive_long 1.50 1.8 600     111  61.17 1.99  2.06  -4259.8 25314.826712 2.641554\n", "   QQQ      lunch_lull 1.50 2.4 900      38  54.08 1.51  1.06  -3538.1  6466.104908 0.431683\n", "  NVDA      power_hour 1.50 1.8 900      76  51.19 1.83  1.42  -8383.4 19330.383048 1.751529\n", "  AAPL      power_hour 0.75 1.4 900     108  50.34 1.43  1.67  -3767.8 12837.146022 0.997135\n", "  AAPL open_drive_long 1.20 1.6 900     140  46.85 1.55  1.77  -5939.9 26001.190865 2.757275\n", "  NVDA      open_drive 0.90 2.0 600      83  46.20 1.36  1.34  -3453.9  8034.420989 0.556753\n", "  SPXL      power_hour 0.60 0.8 900      69  44.67 1.54  1.18  -6308.0 12443.178652 0.957527\n", "  TQQQ lunch_lull_long 1.35 2.4 900      45  44.47 1.37  0.95  -5412.8  5565.310042 0.363680\n", "  SPXL      open_drive 1.35 1.6 180     105  41.04 1.59  1.34  -7021.2 23483.238768 2.347064\n", "  AAPL      lunch_lull 1.20 1.4 600     116  37.22 1.69  1.28  -6918.5 13792.342912 1.095920\n", "  TSLA      open_drive 1.50 1.6 900      74  35.75 1.95  0.98 -12225.6 20750.890466 1.944478\n", "   AMD open_drive_long 0.90 1.4 900     161  35.66 1.32  1.44  -9450.8 18768.240378 1.678114\n", "   SPY      open_drive 1.20 1.6 900      67  32.84 1.38  0.86  -6966.6  6935.225628 0.468192\n", "  MSFT      lunch_lull 1.50 1.8 900      44  28.32 1.41  0.60  -5389.4  5101.556594 0.329724\n", "  SPXL      lunch_lull 1.35 1.4 900      29  14.69 1.35  0.25  -6856.2  2975.841683 0.182875\n"]}], "source": ["if rows:\n", "    df = pd.DataFrame(rows).sort_values(['sharpe','pf'], ascending=False)\n", "    edges_oos = df.copy()\n", "    pd.set_option('display.max_columns', None)\n", "    print(\"\\n=== OOS edges ===\")\n", "    print(df.to_string(index=False, formatters={'sharpe':\"{:.2f}\".format,\n", "                                                    'pf':\"{:.2f}\".format,\n", "                                                    'tstat':\"{:.2f}\".format,\n", "                                                    'dd':\"{:.1f}\".format}))\n", "else:\n", "    print(\"\\nNo tradable edges found for the given date range.\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== FULL-RANGE performance 2025-01-01 → 2025-06-01 ===\n", "symbol           slice    F   K   H  trades sharpe   pf tstat       dd         total      cagr\n", "  SPXL open_drive_long 1.35 1.6 600     190  34.04 1.47  1.50 -23103.2  39699.971878  0.766958\n", "  TSLA      lunch_lull 1.35 2.2 900     171  32.73 1.28  1.37 -15865.6  16870.284876  0.304007\n", "  SPXL lunch_lull_long 1.50 1.8 300     179  30.52 1.40  1.30 -28481.6  31005.843060  0.578959\n", "  TSLA      open_drive 1.50 1.6 900     227  20.54 1.48  0.99 -20401.7  38087.140645  0.725988\n", "  SPXL      open_drive 1.35 1.6 180     319  15.79 1.20  0.90 -29652.3  25620.302001  0.474584\n", "  SPXL      power_hour 0.60 0.8 900     206  13.52 1.13  0.62 -16742.1  10958.781304  0.192287\n", "  NVDA      power_hour 1.50 1.8 900     249  10.29 1.12  0.52 -19343.6  11232.803865  0.198729\n", "   SPY      open_drive 1.20 1.6 900     217   7.83 1.08  0.37 -10686.9   5108.066963  0.088528\n", "  AAPL open_drive_long 1.20 1.6 900     467   4.77 1.04  0.33 -31810.4   7827.573431  0.135939\n", "  AAPL      lunch_lull 1.20 1.4 600     386   4.27 1.06  0.27 -17973.5   4786.909495  0.082293\n", "  MSFT      lunch_lull 1.50 1.8 900     139  -0.70 0.99 -0.03 -14889.3   -393.876930 -0.006697\n", "  TQQQ lunch_lull_long 1.35 2.4 900     166  -1.67 0.99 -0.07 -16479.4   -708.938182 -0.012041\n", "  AAPL      power_hour 0.75 1.4 900     359  -5.36 0.96 -0.32 -19545.2  -4271.664694 -0.071174\n", "  MSFT open_drive_long 1.50 1.8 600     390  -5.50 0.94 -0.35 -38054.5  -6641.455839 -0.110428\n", "   AMD open_drive_long 0.90 1.4 900     601  -6.48 0.95 -0.51 -46854.9 -11816.400856 -0.191582\n", "   QQQ      lunch_lull 1.50 2.4 900     134 -12.30 0.91 -0.45 -17455.1  -4555.517004 -0.076320\n", "  NVDA      open_drive 0.90 2.0 600     297 -25.02 0.85 -1.38 -25376.8 -14911.188311 -0.240385\n", "  SPXL      lunch_lull 1.35 1.4 900      85 -51.88 0.41 -1.53 -29394.7 -18331.936251 -0.291645\n"]}], "source": ["# Full period stats\n", "\n", "full_rows = []\n", "SHIFT_DAYS = 0\n", "\n", "for _, row in edges_oos.iterrows():\n", "    sym            = row['symbol']\n", "    slc            = row['slice']\n", "    F, K, H        = row['F'], row['K'], row['H']\n", "    t0, t1         = SLICES[slc]            # same dict you already have\n", "    params         = SimParams(F, K, H)\n", "\n", "\n", "    bar = (get_intraday_data(sym, START_DT - dt.timedelta(days=SHIFT_DAYS), END_DT - dt.timedelta(days=SHIFT_DAYS))\n", "           .between_time(t0, t1))\n", "    if bar.empty:\n", "        print(f\"⚠ no data for {sym} {slc}\")\n", "        continue\n", "\n", "    trades, cagr, mets = walk_forward(bar, params)\n", "\n", "    full_rows.append(dict(\n", "        symbol=sym, slice=slc, F=F, K=K, H=H,\n", "        trades=len(trades), sharpe=mets.sharpe, pf=mets.pf,\n", "        tstat=mets.tstat, dd=mets.dd, total=mets.total, cagr=cagr\n", "    ))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== FULL-RANGE performance 2025-01-01 → 2025-06-01 ===\n", "symbol           slice    F   K   H  trades sharpe   pf tstat      dd        total     cagr\n", "  AAPL lunch_lull_long 0.45 3.0 900     246  60.90 1.56  3.05 -2785.8 12507.862900 0.220575\n", "  NVDA      power_hour 0.30 2.4 900     452  43.98 1.40  2.98 -3122.5 15298.560092 0.274288\n", "   QQQ open_drive_long 0.45 2.8 900     385  43.16 1.36  2.70 -4545.1 15163.024171 0.271739\n", "   SPY open_drive_long 0.60 3.0 900     269  42.43 1.33  2.22 -4033.5 12138.645279 0.213808\n", "  NVDA open_drive_long 0.45 2.8 900     525  42.08 1.35  3.08 -3373.6 20291.663316 0.369674\n", "  MSFT open_drive_long 0.30 2.0 900     651  37.06 1.31  3.02 -3801.8 21253.903409 0.388382\n", "   SPY      power_hour 0.60 2.4 600     339  32.87 1.24  1.93 -5061.6 13448.051979 0.237875\n", "  MSFT      power_hour 0.30 2.6 900     214  32.82 1.30  1.53 -2179.9  5244.185740 0.090293\n", "  AAPL      lunch_lull 0.45 2.8 600     266  32.10 1.26  1.67 -3278.1  7922.121487 0.138616\n", "   SPY      open_drive 0.60 1.8 600     321  30.02 1.21  1.72 -6203.1 14872.493908 0.266281\n", "  MSFT      open_drive 0.60 3.0 180     569  22.06 1.16  1.68 -5677.8 13794.782392 0.246119\n", "  AAPL      power_hour 0.45 3.0 300     722  14.06 1.11  1.21 -7415.4  9373.284553 0.163616\n"]}], "source": ["\n", "# ---- pretty print -----------------------------------------------------------\n", "if full_rows:\n", "    full_df = (pd.DataFrame(full_rows)\n", "               .sort_values(['sharpe', 'pf'], ascending=False))\n", "\n", "    pd.set_option('display.max_columns', None)\n", "    print(\"\\n=== FULL-RANGE performance \"\n", "          f\"{START_DT.date()} → {END_DT.date()} ===\")\n", "    print(full_df.to_string(index=False, formatters={\n", "        'sharpe': \"{:.2f}\".format,\n", "        'pf'    : \"{:.2f}\".format,\n", "        'tstat' : \"{:.2f}\".format,\n", "        'dd'    : \"{:.1f}\".format,\n", "    }))\n", "else:\n", "    print(\"No edges to evaluate on the full range.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}