{"cells": [{"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pytz\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2025, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 6, 5))\n", "\n", "TICKER = \"SPY\"\n", "\n", "ACCOUNT_EQUITY      = 100_000          # Starting equity\n", "POSITION_RISK_PCT   = 0.001            # 0.1 % of equity per trade\n", "INTRADAY_MARGIN_MAX = 4                # 4. daytime leverage\n", "\n", "COMM      = 0.000\n", "ADD_REB   = -0.0020  # Maker rebate is *negative* cost\n", "TAKE_FEE  = 0.0020\n", "SLIP_TICK = 0.000    # Assumed slippage on aggressive fills (stops)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["\n", "# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "from numpy.typing import NDArray\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                            .with_trade_session(\"full\")\n", "                            .with_adjusted(False)\n", "                            .with_period(\"intraday\")\n", "                            .build_market_data())\n", "\n", "# ------------------------------------------------------------------\n", "# 0)  global cache dict  →  {(ticker, start_ts, end_ts): DataFrame}\n", "#     lives at module scope so every call can see it (threads share it;\n", "#     each forked *process* will build its own – still fine).\n", "# ------------------------------------------------------------------\n", "_sec_cache: dict = {}\n", "\n", "def get_per_second_data(ticker: str, start_dt, end_dt, *, use_cache=True):\n", "    \"\"\"\n", "    Fetch 1-second OHLCV, memoising results in RAM during the notebook run.\n", "    Key = (ticker, start-iso, end-iso)\n", "    \"\"\"\n", "    key = (ticker,\n", "           pd.Timestamp(start_dt).isoformat(),\n", "           pd.Timestamp(end_dt).isoformat())\n", "\n", "    if use_cache and key in _sec_cache:\n", "        return _sec_cache[key]\n", "\n", "    df = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=1\n", "    )\n", "    \n", "    if df.index.tz is None:\n", "        df = df.tz_localize(\"UTC\")\n", "    df = df.tz_convert(\"US/Eastern\")\n", "\n", "    if use_cache:\n", "        _sec_cache[key] = df\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor, as_completed\n", "from datetime import datetime, timedelta, time\n", "import pandas as pd\n", "import pytz\n", "from dateutil.relativedelta import relativedelta\n", "import numpy as np\n", "from pandas.tseries.offsets import BDay\n", "\n", "def get_all_sessions(start_dt, end_dt):\n", "    \"\"\"\n", "    Generates a DataFrame containing all trading sessions (business days)\n", "    between a start and end date.\n", "    \"\"\"\n", "    trading_days = pd.bdate_range(start=start_dt, end=end_dt)\n", "\n", "    return pd.DataFrame({\n", "        'date': trading_days,\n", "        'stock': TICKER\n", "    })"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["events_df = get_all_sessions(start_dt, end_dt)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["\n", "# ────────────────────────────────────────────────────────────\n", "# Canonical intrabar-range estimator\n", "# ────────────────────────────────────────────────────────────\n", "def ewma_high_low(df: pd.DataFrame, window: int) -> NDArray[np.float64]:\n", "    \"\"\"\n", "    R_t  =  EWMA { max(high) – min(low) }   over a rolling *window* (sec).\n", "    Same math for both CV and live trading.\n", "    \"\"\"\n", "    hp = df['high'].rolling(window=window, min_periods=1).max()\n", "    lp = df['low'] .rolling(window=window, min_periods=1).min()\n", "    raw = (hp - lp).to_numpy()\n", "\n", "    alpha = 1.0 - np.exp(np.log(0.5) / window)        # convert half-life\n", "    return pd.Series(raw).ewm(alpha=alpha).mean().to_numpy()\n", "\n", "\n", "def exit_cost(is_tp: bool) -> float:\n", "    \"\"\"Cost per share for the exit leg, including commission, fees, and slippage.\"\"\"\n", "    # Take-profit (TP) is assumed to be a passive limit order (maker rebate)\n", "    # Stop-loss (SL) is assumed to be an aggressive order (taker fee + slippage)\n", "    base = COMM + (TAKE_FEE if not is_tp else ADD_REB)\n", "    slip = SLIP_TICK if not is_tp else 0.0\n", "    return base + slip\n", "\n", "# # ------------------------------------------------------------------\n", "# # 2) Core simulator\n", "# # ------------------------------------------------------------------\n", "\n", "def simulate_bracket_scalper(\n", "        sec_df: pd.DataFrame,\n", "        F: float = 0.50,\n", "        K: float = 0.90,\n", "        ewma_halflife_sec: int = 360,\n", "):\n", "    \"\"\"\n", "    Bracket-scalper with previous-tick anchoring and\n", "    a `pos` flag (-1 short, +1 long, 0 flat) carried through to the trade log.\n", "    \"\"\"\n", "    price = sec_df['close'].values\n", "    idx   = sec_df.index\n", "    R     = ewma_high_low(sec_df, ewma_halflife_sec)\n", "\n", "    trades      = []\n", "    state       = \"FLAT\"\n", "    bracket_set = False\n", "    pos         = 0\n", "    \n", "    prev_date = None        \n", "\n", "    for t in range(len(price)):\n", "        ts, p, r = idx[t], price[t], R[t]\n", "\n", "        curr_date = ts.date()\n", "        if prev_date is None or curr_date != prev_date:\n", "            state       = \"FLAT\"     # flatten any overnight carry\n", "            bracket_set = False      # clear pending bracket\n", "        prev_date = curr_date\n", "\n", "\n", "        # ── FLAT: set / monitor the entry bracket ───────────────────────────\n", "        if state == \"FLAT\":\n", "            if np.isnan(r) or r == 0:\n", "                continue\n", "\n", "            if not bracket_set:\n", "                anchor  = (\n", "                   price[t-1]\n", "                   if t > 0 and idx[t-1].date() == curr_date\n", "                   else p\n", "                )\n", "\n", "                upper   = anchor + r * F / 2\n", "                lower   = anchor - r * F / 2\n", "                bracket_set = True\n", "                continue\n", "\n", "            # ---- SHORT entry ----------------------------------------------\n", "            if p >= upper:\n", "                state     = \"SHORT\"\n", "                pos       = -1\n", "                entry_px  = upper\n", "                ts_entry  = ts\n", "                tp        = entry_px - r * F\n", "                sl        = entry_px + r * (K - F) / 2\n", "                bracket_set = False\n", "\n", "            # ---- LONG entry -----------------------------------------------\n", "            elif p <= lower:\n", "                state     = \"LONG\"\n", "                pos       = +1\n", "                entry_px  = lower\n", "                ts_entry  = ts\n", "                tp        = entry_px + r * F\n", "                sl        = entry_px - r * (K - F) / 2\n", "                bracket_set = False\n", "\n", "        # ── LONG: manage open long position ────────────────────────────────\n", "        elif state == \"LONG\":\n", "            if p >= tp or p <= sl:\n", "                is_tp   = p >= tp\n", "                exit_px = tp if is_tp else sl\n", "\n", "                pnl_raw         = exit_px - entry_px\n", "                pnl_net_per_sh  = pnl_raw - exit_cost(is_tp)\n", "                stop_dist       = entry_px - sl\n", "\n", "                risk_dol = ACCOUNT_EQUITY * POSITION_RISK_PCT\n", "                shares   = (risk_dol / stop_dist) if stop_dist > 0 else 0\n", "                shares   = min(shares, ACCOUNT_EQUITY * INTRADAY_MARGIN_MAX / entry_px)\n", "                pnl_tot  = shares * pnl_net_per_sh\n", "\n", "                trades.append(dict(\n", "                    ts_entry=ts_entry, px_entry=entry_px,\n", "                    ts_exit=ts,    px_exit=exit_px,\n", "                    dir=pos,       reason='TP' if is_tp else 'SL',\n", "                    shares=shares, pnl=pnl_tot\n", "                ))\n", "                state, pos = \"FLAT\", 0\n", "\n", "        # ── SHORT: manage open short position ──────────────────────────────\n", "        elif state == \"SHORT\":\n", "            if p <= tp or p >= sl:\n", "                is_tp   = p <= tp\n", "                exit_px = tp if is_tp else sl\n", "\n", "                pnl_raw         = entry_px - exit_px\n", "                pnl_net_per_sh  = pnl_raw - exit_cost(is_tp)\n", "                stop_dist       = sl - entry_px\n", "\n", "                risk_dol = ACCOUNT_EQUITY * POSITION_RISK_PCT\n", "                shares   = (risk_dol / stop_dist) if stop_dist > 0 else 0\n", "                shares   = min(shares, ACCOUNT_EQUITY * INTRADAY_MARGIN_MAX / entry_px)\n", "                pnl_tot  = shares * pnl_net_per_sh\n", "\n", "                trades.append(dict(\n", "                    ts_entry=ts_entry, px_entry=entry_px,\n", "                    ts_exit=ts,    px_exit=exit_px,\n", "                    dir=pos,       reason='TP' if is_tp else 'SL',\n", "                    shares=shares, pnl=pnl_tot\n", "                ))\n", "                state, pos = \"FLAT\", 0\n", "\n", "    return trades\n", "\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["# ------------------------------------------------------------------\n", "# 3) Full experiment on 50 random gap‑events\n", "# ------------------------------------------------------------------\n", "def run_experiment(\n", "        events_df: pd.DataFrame,\n", "        **sim_kwargs\n", "):\n", "    results = []\n", "    for _, ev in tqdm(events_df.iterrows(), total=len(events_df),\n", "                      desc=\"Simulating events\"):\n", "        ticker, d = ev.stock, ev.date.date()\n", "        start_ts  = ev.date.replace(hour=9, minute=35, second=0)\n", "        end_ts    = ev.date.replace(hour=10, minute=45, second=0)\n", "        \n", "        try:\n", "            sec_df = get_per_second_data(ticker, start_ts, end_ts)\n", "            if sec_df.empty:\n", "                print(f\"[{ticker} {d}] No data found\")\n", "                continue\n", "            trades = simulate_bracket_scalper(sec_df, **sim_kwargs)\n", "            for tr in trades:\n", "                tr.update(stock=ticker, event_date=d)\n", "            results.extend(trades)\n", "        except Exception as e:\n", "            print(f\"[{ticker} {d}] {e}\")\n", "\n", "    return pd.DataFrame(results)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "501be528a7464853bdb9417edaddf1eb", "version_major": 2, "version_minor": 0}, "text/plain": ["Simulating events:   0%|          | 0/112 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[SPY 2025-01-01] 'RangeIndex' object has no attribute 'tz'\n", "[SPY 2025-01-09] 'RangeIndex' object has no attribute 'tz'\n", "[SPY 2025-01-20] 'RangeIndex' object has no attribute 'tz'\n", "[SPY 2025-02-17] 'RangeIndex' object has no attribute 'tz'\n", "[SPY 2025-04-18] 'RangeIndex' object has no attribute 'tz'\n", "[SPY 2025-05-26] 'RangeIndex' object has no attribute 'tz'\n"]}], "source": ["# trades_df = run_experiment(\n", "#     spy_events_df,\n", "#     n_events=len(spy_events_df),      # you can keep or drop this arg now\n", "#     F=0.75, K=0.87,\n", "#     ewma_halflife_sec=900,\n", "\n", "# === FULL-RANGE performance 2025-01-01 → 2025-06-01 ===\n", "# symbol           slice    F   K   H  trades sharpe   pf tstat      dd        total     cagr\n", "#   AAPL lunch_lull_long 0.45 3.0 900     246  60.90 1.56  3.05 -2785.8 12507.862900 0.220575\n", "#   NVDA      power_hour 0.30 2.4 900     452  43.98 1.40  2.98 -3122.5 15298.560092 0.274288\n", "#    QQQ open_drive_long 0.45 2.8 900     385  43.16 1.36  2.70 -4545.1 15163.024171 0.271739\n", "#    SPY open_drive_long 0.60 3.0 900     269  42.43 1.33  2.22 -4033.5 12138.645279 0.213808\n", "#   NVDA open_drive_long 0.45 2.8 900     525  42.08 1.35  3.08 -3373.6 20291.663316 0.369674\n", "#   MSFT open_drive_long 0.30 2.0 900     651  37.06 1.31  3.02 -3801.8 21253.903409 0.388382\n", "#    SPY      power_hour 0.60 2.4 600     339  32.87 1.24  1.93 -5061.6 13448.051979 0.237875\n", "#   MSFT      power_hour 0.30 2.6 900     214  32.82 1.30  1.53 -2179.9  5244.185740 0.090293\n", "#   AAPL      lunch_lull 0.45 2.8 600     266  32.10 1.26  1.67 -3278.1  7922.121487 0.138616\n", "#    SPY      open_drive 0.60 1.8 600     321  30.02 1.21  1.72 -6203.1 14872.493908 0.266281\n", "#   MSFT      open_drive 0.60 3.0 180     569  22.06 1.16  1.68 -5677.8 13794.782392 0.246119\n", "#   AAPL      power_hour 0.45 3.0 300     722  14.06 1.11  1.21 -7415.4  9373.284553 0.163616\n", "\n", "trades_df = run_experiment(\n", "    events_df,\n", "    F=0.6, K=1.8,\n", "    ewma_halflife_sec=600\n", ")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["def to_plotting_format(raw_trades: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Converts raw trades (which now include sizing and P&L) into the\n", "    schema required for plotting and analysis.\n", "    \"\"\"\n", "    if raw_trades.empty:\n", "        return pd.DataFrame()\n", "\n", "    df = raw_trades.copy()\n", "\n", "    df = df.rename(columns={\n", "        'ts_entry': 'entry_time',\n", "        'px_entry': 'entry_price',\n", "        'ts_exit':  'exit_time',\n", "        'px_exit':  'exit_price',\n", "        'reason':   'exit_reason',\n", "        'shares':   'entry_quantity'  # Use the calculated shares as the quantity\n", "    })\n", "\n", "    # P&L is now calculated in the simulator, so we just calculate the percentage\n", "    df['pnl_pct'] = df['pnl'] / (df['entry_price'] * abs(df['entry_quantity'])) * 100\n", "    df['stop_price'] = np.nan\n", "    df['date'] = pd.to_datetime(df['entry_time']).dt.normalize()\n", "\n", "    cols_order = [\n", "        'stock', 'date', 'entry_time', 'entry_price', 'entry_quantity',\n", "        'stop_price', 'exit_time',  'exit_price',  'exit_reason', 'pnl', 'pnl_pct'\n", "    ]\n", "    return df[cols_order].reindex(columns=cols_order)\n", "\n", "tr1 = to_plotting_format(trades_df)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Total Trades</th>\n", "      <th>Win Rate (%)</th>\n", "      <th>Avg PnL ($)</th>\n", "      <th>Sum PnL ($)</th>\n", "      <th>Avg PnL (%)</th>\n", "      <th>Profit Factor</th>\n", "      <th>Payoff Ratio</th>\n", "      <th>Expectancy ($)</th>\n", "      <th>CAGR (%)</th>\n", "      <th>Volatility (%)</th>\n", "      <th><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Max Drawdown (%)</th>\n", "      <th><PERSON><PERSON></th>\n", "    </tr>\n", "    <tr>\n", "      <th>Strategy</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>OriginalSPY</th>\n", "      <td>1479</td>\n", "      <td>45.23</td>\n", "      <td>-6.99</td>\n", "      <td>-10335.41</td>\n", "      <td>-0.0</td>\n", "      <td>0.86</td>\n", "      <td>1.04</td>\n", "      <td>-6.99</td>\n", "      <td>-22.93</td>\n", "      <td>4.81</td>\n", "      <td>-3.86</td>\n", "      <td>-4.89</td>\n", "      <td>12.45</td>\n", "      <td>-0.83</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Total Trades  Win Rate (%)  Avg PnL ($)  Sum PnL ($)  \\\n", "Strategy                                                            \n", "OriginalSPY          1479         45.23        -6.99    -10335.41   \n", "\n", "             Avg PnL (%)  Profit Factor  Payoff Ratio  Expectancy ($)  \\\n", "Strategy                                                                \n", "OriginalSPY         -0.0           0.86          1.04           -6.99   \n", "\n", "             CAGR (%)  Volatility (%)  Sharpe Ratio  Sortino Ratio  \\\n", "Strategy                                                             \n", "OriginalSPY    -22.93            4.81         -3.86          -4.89   \n", "\n", "             Max Drawdown (%)  Calmar Ratio  \n", "Strategy                                     \n", "OriginalSPY             12.45         -0.83  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAHqCAYAAAAZLi26AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAoIhJREFUeJzs3Xd0VNXax/HvTHrvFUISinTpQhBBBEVBEUVFLICAqBdULoqK7doL2FCvcq1IsysiKBgRQSBSQu8EAqGlQCrpycz7B2ZeYyiTMMlMyO+zVhbMOfvs85xhJ+GZ3Qxms9mMiIiIiIiIiNic0d4BiIiIiIiIiFyolHSLiIiIiIiI1BIl3SIiIiIiIiK1REm3iIiIiIiISC1R0i0iIiIiIiJSS5R0i4iIiIiIiNQSJd0iIiIiIiIitURJt4iIiIiIiEgtUdItIiIiIiIiUkuUdIuIiFTDM888g8FgqNG1M2fOxGAwcODAAdsG9TcHDhzAYDAwc+bMWruHvfz+++8Y<PERSON><PERSON>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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Equity Curve with price comparisions\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "from typing import Dict, List\n", "\n", "# ---- CONFIG ----\n", "initial_capital = 100_000\n", "\n", "# ---- Helper functions --------------------------------------------------------\n", "def build_equity_curve_by_date(trades_df: pd.DataFrame,\n", "                               initial_capital: float = 100_000,\n", "                               pnl_col: str = 'pnl') -> pd.Series:\n", "    \"\"\"\n", "    Time-indexed equity curve – one data-point per calendar day.\n", "    \"\"\"\n", "    if trades_df.empty:\n", "        return pd.Series(dtype=float)\n", "\n", "    # aggregate PnL by calendar date\n", "    pnl_by_date = trades_df.groupby('date')[pnl_col].sum()\n", "    pnl_by_date.index = pd.to_datetime(pnl_by_date.index)\n", "\n", "    # full date range (fill missing days with 0 PnL)\n", "    full_idx = pd.date_range(pnl_by_date.index.min(), pnl_by_date.index.max())\n", "    pnl_by_date = pnl_by_date.reindex(full_idx, fill_value=0)\n", "\n", "    return initial_capital + pnl_by_date.cumsum()\n", "\n", "\n", "def calculate_drawdowns(equity_curve: pd.Series) -> pd.Series:\n", "    running_max = equity_curve.cummax()\n", "    return (running_max - equity_curve) / running_max * 100\n", "\n", "\n", "def compute_metrics(trades_df: pd.DataFrame,\n", "                    initial_capital: float = 100_000,\n", "                    rf: float = 0.0) -> Dict[str, float]:\n", "    \"\"\"\n", "    Metrics based on daily returns of the equity curve.\n", "    \"\"\"\n", "    if trades_df.empty:\n", "        return {}\n", "\n", "    wins   = trades_df[trades_df.pnl > 0]\n", "    losses = trades_df[trades_df.pnl <= 0]\n", "\n", "    eq_curve = build_equity_curve_by_date(trades_df, initial_capital)\n", "    daily_ret = eq_curve.pct_change().dropna()\n", "\n", "    # time span in years for CAGR\n", "    years = (eq_curve.index[-1] - eq_curve.index[0]).days / 365.25 or np.nan\n", "\n", "    vol_ann = daily_ret.std() * np.sqrt(252)\n", "    neg_vol_ann = daily_ret[daily_ret < 0].std() * np.sqrt(252)\n", "\n", "    running_max = eq_curve.cummax()\n", "    max_dd_pct = ((running_max - eq_curve) / running_max).max() * 100\n", "\n", "    return {\n", "        'Total Trades':       len(trades_df),\n", "        'Win Rate (%)':       len(wins) / len(trades_df) * 100,\n", "        'Avg PnL ($)':        trades_df.pnl.mean(),\n", "        'Sum PnL ($)':        trades_df.pnl.sum(),\n", "        'Avg PnL (%)':        trades_df.pnl_pct.mean(),\n", "        'Profit Factor':      wins.pnl.sum() / abs(losses.pnl.sum()) if len(losses) else np.nan,\n", "        'Payoff Ratio':       wins.pnl.mean() / abs(losses.pnl.mean()) if len(losses) else np.nan,\n", "        'Expectancy ($)':     (len(wins)/len(trades_df)) * wins.pnl.mean()\n", "                              - (len(losses)/len(trades_df)) * abs(losses.pnl.mean()),\n", "        'CAGR (%)':           ((eq_curve.iloc[-1] / initial_capital) ** (1/years) - 1) * 100\n", "                              if years and years > 0 else np.nan,\n", "        'Volatility (%)':     vol_ann * 100,\n", "        'Sharpe Ratio':       ((daily_ret.mean()*252 - rf) / vol_ann) if vol_ann else np.nan,\n", "        'So<PERSON><PERSON>':      ((daily_ret.mean()*252 - rf) / neg_vol_ann) if neg_vol_ann else np.nan,\n", "        'Max Drawdown (%)':   max_dd_pct,\n", "        'Calmar Ratio':       (((eq_curve.iloc[-1]/initial_capital)-1) / (max_dd_pct/100))\n", "                              if max_dd_pct else np.nan\n", "    }\n", "\n", "# ---- Main computation --------------------------------------------------------\n", "\n", "# The notebook must already contain DataFrames `tr1` (Original)\n", "strategies = {'Original': tr1}\n", "\n", "# containers for plotting\n", "equity_curves = {s: {} for s in strategies}   # strategy -> threshold -> equity curve\n", "drawdowns     = {s: {} for s in strategies}\n", "\n", "metrics_rows = []\n", "\n", "for name, df in strategies.items():\n", "    filt = df.copy()\n", "    if filt.empty:\n", "        continue  # skip if no trades after filter\n", "\n", "    # equity / dd\n", "    eq = build_equity_curve_by_date(filt, initial_capital)\n", "    dd = calculate_drawdowns(eq)\n", "    equity_curves[name][0] = eq\n", "    drawdowns[name][0]     = dd\n", "\n", "    # metrics\n", "    met = compute_metrics(filt, initial_capital)\n", "    met['Strategy']            = name + TICKER\n", "    metrics_rows.append(met)\n", "\n", "# Create metrics DataFrame\n", "metrics_df = pd.DataFrame(metrics_rows)\n", "metrics_df.set_index(['Strategy'], inplace=True)\n", "metrics_df = metrics_df.round(2).sort_index()\n", "\n", "# ---- Display metrics table ---------------------------------------------------\n", "from IPython.display import display\n", "\n", "display(metrics_df)\n", "\n", "# ---- Plotting (one figure per strategy per metric) ---------------------------\n", "for strat in strategies:\n", "    # Equity curves\n", "    plt.figure(figsize=(10,5))\n", "    for thresh, eq in equity_curves[strat].items():\n", "        plt.plot(eq.index, eq.values, label=f'>= ${thresh}')\n", "    plt.title(f\"{strat} – Equity Curve\")\n", "    plt.xlabel(\"Date\")\n", "    plt.ylabel(\"Portfolio Value ($)\")\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # Drawdown curves\n", "    plt.figure(figsize=(10,5))\n", "    for thresh, dd in drawdowns[strat].items():\n", "        plt.plot(dd.index, dd.values, label=f'>= ${thresh}')\n", "    plt.title(f\"{strat} – Drawdown %\")\n", "    plt.xlabel(\"Date\")\n", "    plt.ylabel(\"Drawdown (%)\")\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x900 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "## ── 1) Cost parameters ─────────────────────────────────────────────────\n", "commission_per_share = 0.000      # $0.001 *per side*\n", "locate_fee_pct       = 0.00      # 0.5 % of entry notional (shorts only)\n", "slippage_pct         = (0.00/100)/100\n", "initial_capital      = 100_000\n", "\n", "# ----------------------------------------------------------------------\n", "tr = tr1.copy()        # keep raw PnL intact\n", "\n", "# ── 2) Commissions ────────────────────────────────────────────────────\n", "tr['commission'] = abs(tr.entry_quantity) * commission_per_share * 2\n", "\n", "# ── 3) <PERSON><PERSON> (shorts only) ───────────────────────────────────────────\n", "qty_short = np.where(tr.entry_quantity < 0, abs(tr.entry_quantity), 0)\n", "tr['locate_fee'] = tr.entry_price * qty_short * locate_fee_pct\n", "\n", "# ── 4) Slippage (both sides, long & short) ────────────────────────────\n", "tr['slippage_cost'] = (abs(tr.entry_price) + abs(tr.exit_price)) \\\n", "                      * abs(tr.entry_quantity) * slippage_pct\n", "\n", "# ── 5) Net PnL ────────────────────────────────────────────────────────\n", "tr['total_cost'] = tr[['commission', 'locate_fee', 'slippage_cost']].sum(axis=1)\n", "tr['net_pnl']    = tr['pnl'] - tr['total_cost']   # keep BOTH columns!\n", "\n", "# ---------------------------------------------------------------------\n", "# Equity-curve helpers stay unchanged\n", "\n", "# ── 6) Curves & plots ────────────────────────────────────────────────\n", "eq_raw = build_equity_curve_by_date(tr, initial_capital, 'pnl')\n", "eq_net = build_equity_curve_by_date(tr, initial_capital, 'net_pnl')\n", "\n", "dd_raw = calculate_drawdowns(eq_raw)\n", "dd_net = calculate_drawdowns(eq_net)\n", "\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 9), sharex=True)\n", "\n", "ax1.plot(eq_raw, label='Raw Equity')\n", "ax1.plot(eq_net, label='Net Equity (cost-adjusted)')\n", "ax1.set_ylabel('Portfolio ($)'); ax1.set_title('Equity Curves')\n", "ax1.legend(); ax1.grid(alpha=.3)\n", "\n", "ax2.plot(dd_raw, label='Raw DD')\n", "ax2.plot(dd_net, label='Net DD')\n", "ax2.set_ylabel('Drawdown %'); ax2.set_title('Drawdowns')\n", "ax2.legend(); ax2.grid(alpha=.3)\n", "\n", "plt.tight_layout(); plt.show()"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "\n", "# Assuming your get_daily_data function is defined as in your provided code\n", "\n", "def create_portwine_results_from_trades(\n", "    trades_df: pd.DataFrame,\n", "    initial_capital: float = 100_000,\n", "    benchmark_ticker: str | None = 'SPY' # Optional: Ticker for benchmark comparison\n", ") -> dict:\n", "    \"\"\"\n", "    Converts a trades DataFrame into a results dictionary compatible\n", "    with portwine analyzers.\n", "\n", "    Args:\n", "        trades_df: DataFrame with trade details, must include 'date' and 'pnl' columns.\n", "                   'date' should be the date of the trade's exit or realization.\n", "        initial_capital: The starting capital for the simulation.\n", "        benchmark_ticker: Optional ticker symbol for a benchmark (e.g., 'SPY').\n", "                          If provided, fetches daily data and calculates benchmark returns.\n", "\n", "    Returns:\n", "        Dictionary containing:\n", "        - 'strategy_returns': pd.Series of daily strategy returns.\n", "        - 'benchmark_returns': pd.Series of daily benchmark returns (if requested).\n", "    \"\"\"\n", "    if trades_df.empty:\n", "        print(\"Warning: Trades DataFrame is empty. Returning empty results.\")\n", "        return {\n", "            'strategy_returns': pd.Series(dtype=float),\n", "            'benchmark_returns': pd.Series(dtype=float)\n", "        }\n", "\n", "    # --- Strategy Returns Calculation ---\n", "\n", "    # 1. Sort trades by date (use 'exit_time' if available and more precise, else 'date')\n", "    sort_col = 'exit_time' if 'exit_time' in trades_df.columns else 'date'\n", "    trades_sorted = trades_df.sort_values(sort_col).copy()\n", "\n", "    # Ensure 'date' column exists and is datetime-like (use exit date for daily P&L)\n", "    if 'exit_time' in trades_df.columns:\n", "         trades_sorted['pnl_date'] = pd.to_datetime(trades_sorted['exit_time'].dt.date)\n", "    elif 'date' in trades_df.columns:\n", "         trades_sorted['pnl_date'] = pd.to_datetime(trades_sorted['date'])\n", "    else:\n", "        raise ValueError(\"Trades DataFrame must have a 'date' or 'exit_time' column.\")\n", "\n", "    # 2. Aggregate P&L by date\n", "    daily_pnl = trades_sorted.groupby('pnl_date')['pnl'].sum()\n", "\n", "    # 3. Create full date range and reindex P&L\n", "    if daily_pnl.empty:\n", "         print(\"Warning: No PnL data after grouping by date. Returning empty results.\")\n", "         return {\n", "            'strategy_returns': pd.Series(dtype=float),\n", "            'benchmark_returns': pd.Series(dtype=float)\n", "         }\n", "\n", "    start_date = daily_pnl.index.min()\n", "    end_date = daily_pnl.index.max()\n", "    # Use business days or daily frequency depending on your data\n", "    # date_range = pd.bdate_range(start=start_date, end=end_date) # Business days\n", "    date_range = pd.date_range(start=start_date, end=end_date, freq='D') # Calendar days\n", "\n", "    full_pnl = daily_pnl.reindex(date_range, fill_value=0.0)\n", "\n", "    # 4. Calculate Daily Equity Curve\n", "    equity_curve = initial_capital + full_pnl.cumsum()\n", "\n", "    # 5. Calculate Daily Returns\n", "    strategy_returns = equity_curve.pct_change().fillna(0.0)\n", "    strategy_returns.name = \"strategy\" # Good practice to name the series\n", "\n", "    # --- <PERSON><PERSON><PERSON> Returns Calculation (Optional) ---\n", "    benchmark_returns = pd.Series(dtype=float)\n", "    if benchmark_ticker:\n", "        print(f\"Fetching benchmark data for {benchmark_ticker}...\")\n", "        try:\n", "            # Adjust start/end dates slightly to ensure coverage\n", "            bench_start_dt = start_date - pd.Timedelta(days=5)\n", "            bench_end_dt = end_date + pd.Timedelta(days=1)\n", "\n", "            # Ensure start/end are timezone-naive or consistent timezone\n", "            # Assuming get_daily_data expects timezone-naive datetime objects\n", "            if isinstance(bench_start_dt, pd.Timestamp) and bench_start_dt.tz:\n", "                 bench_start_dt_naive = bench_start_dt.tz_localize(None)\n", "            else:\n", "                 bench_start_dt_naive = bench_start_dt\n", "\n", "            if isinstance(bench_end_dt, pd.Timestamp) and bench_end_dt.tz:\n", "                 bench_end_dt_naive = bench_end_dt.tz_localize(None)\n", "            else:\n", "                 bench_end_dt_naive = bench_end_dt\n", "\n", "            # Fetch benchmark daily data using your existing function\n", "            bench_data = get_daily_data(benchmark_ticker, bench_start_dt_naive, bench_end_dt_naive)\n", "\n", "            if bench_data is not None and not bench_data.empty:\n", "                # Ensure index is DatetimeIndex and calculate daily returns\n", "                bench_data.index = pd.to_datetime(bench_data.index)\n", "                # Ensure the index is timezone-naive to match strategy_returns\n", "                if bench_data.index.tz:\n", "                    bench_data.index = bench_data.index.tz_localize(None)\n", "\n", "                bench_daily_returns = bench_data['close'].pct_change().fillna(0.0)\n", "\n", "                # Align benchmark returns to strategy returns index\n", "                benchmark_returns = bench_daily_returns.reindex(strategy_returns.index).fillna(0.0)\n", "                benchmark_returns.name = benchmark_ticker\n", "                print(f\"Successfully calculated benchmark returns for {benchmark_ticker}.\")\n", "            else:\n", "                print(f\"Warning: Could not retrieve or process benchmark data for {benchmark_ticker}.\")\n", "        except Exception as e:\n", "            print(f\"Error fetching or processing benchmark data for {benchmark_ticker}: {e}\")\n", "\n", "    return {\n", "        'strategy_returns': strategy_returns,\n", "        'benchmark_returns': benchmark_returns\n", "        # Note: Other keys like 'signals_df', 'tickers_returns' are not generated\n", "        # by this adapter but might be needed for specific analyzers.\n", "    }"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'tr_min_price' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[45]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      5\u001b[39m sim_initial_capital = \u001b[32m100_000\u001b[39m\n\u001b[32m      7\u001b[39m \u001b[38;5;66;03m# --- Create the results dictionary using the adapter ---\u001b[39;00m\n\u001b[32m      8\u001b[39m portwine_results = create_portwine_results_from_trades(\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m     trades_df=\u001b[43mtr_min_price\u001b[49m,\n\u001b[32m     10\u001b[39m     initial_capital=sim_initial_capital,\n\u001b[32m     11\u001b[39m     benchmark_ticker=\u001b[33m'\u001b[39m\u001b[33mSPY\u001b[39m\u001b[33m'\u001b[39m \u001b[38;5;66;03m# Or None if you don't want a benchmark\u001b[39;00m\n\u001b[32m     12\u001b[39m )\n\u001b[32m     14\u001b[39m \u001b[38;5;66;03m# --- Now use portwine analyzers ---\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mportwine\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01manalyzers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     16\u001b[39m     EquityDrawdownAnalyzer,\n\u001b[32m     17\u001b[39m     MonteCarloAnalyzer,\n\u001b[32m   (...)\u001b[39m\u001b[32m     25\u001b[39m     \u001b[38;5;66;03m# Add others as needed\u001b[39;00m\n\u001b[32m     26\u001b[39m )\n", "\u001b[31mNameError\u001b[39m: name 'tr_min_price' is not defined"]}], "source": ["# Assuming tr1 is your DataFrame from simulate_premarket_gap_strategy\n", "# tr1 = simulate_premarket_gap_strategy(gap_up_df, minute_data, ...)\n", "\n", "# Define initial capital used in the simulation\n", "sim_initial_capital = 100_000\n", "\n", "# --- Create the results dictionary using the adapter ---\n", "portwine_results = create_portwine_results_from_trades(\n", "    trades_df=tr_min_price,\n", "    initial_capital=sim_initial_capital,\n", "    benchmark_ticker='SPY' # Or None if you don't want a benchmark\n", ")\n", "\n", "# --- Now use portwine analyzers ---\n", "from portwine.analyzers import (\n", "    EquityDrawdownAnalyzer,\n", "    Monte<PERSON>ar<PERSON><PERSON><PERSON><PERSON>,\n", "    Seasonality<PERSON><PERSON><PERSON><PERSON>,\n", "    TrainTestEquityDrawdownAnalyzer,\n", "    TransactionCostAnalyzer, # Note: This specific one might need more data (signals_df)\n", "    CorrelationA<PERSON>y<PERSON>, # Note: This needs 'tickers_returns' which we don't have\n", "    StrategyComparisonAnalyzer,\n", "    NoiseRobustnessAnalyzer, # Note: Needs a base_loader and strategy class\n", "    RegimeChangeAnalyzer\n", "    # Add others as needed\n", ")\n", "\n", "\n", "# Example 1: Equity Drawdown Analyzer\n", "if EquityDrawdownAnalyzer and portwine_results['strategy_returns'] is not None and not portwine_results['strategy_returns'].empty:\n", "    print(\"\\n--- Running EquityDrawdownAnalyzer ---\")\n", "    equity_analyzer = EquityDrawdownAnalyzer()\n", "    # Plot requires both strategy and benchmark returns (if benchmark was requested)\n", "    if portwine_results['benchmark_returns'] is not None and not portwine_results['benchmark_returns'].empty:\n", "         equity_analyzer.plot(portwine_results, benchmark_label='SPY')\n", "         # Generate report\n", "         report = equity_analyzer.generate_report(portwine_results, benchmark_label='SPY')\n", "         print(\"\\nEquity Drawdown Report:\")\n", "         print(report)\n", "    else:\n", "        print(\"Skipping EquityDrawdownAnalyzer plot/report as benchmark returns are missing.\")\n", "        # You might still be able to call analyze if it works without benchmark\n", "        try:\n", "            analysis_data = equity_analyzer.analyze(portwine_results)\n", "            print(\"EquityDrawdownAnalyzer analysis (without benchmark):\")\n", "            print(analysis_data)\n", "        except Exception as e:\n", "            print(f\"Could not run analyze: {e}\")\n", "\n", "# Example 2: <PERSON>\n", "if MonteCarloAnalyzer and portwine_results['strategy_returns'] is not None and not portwine_results['strategy_returns'].empty:\n", "    print(\"\\n--- Running MonteCarloAnalyzer ---\")\n", "    mc_analyzer = MonteCarloAnalyzer(frequency='ME') # Analyze monthly returns\n", "    # The plot method in the provided snippet expects benchmark_returns to be present in results\n", "    if portwine_results['benchmark_returns'] is not None and not portwine_results['benchmark_returns'].empty:\n", "        mc_analyzer.plot(portwine_results, title=\"Monte Carlo Simulation (Monthly Returns)\")\n", "    else:\n", "        print(\"Skipping <PERSON><PERSON><PERSON><PERSON> plot as benchmark returns are missing.\")\n", "        # Try running the analysis part if needed\n", "        try:\n", "             mc_results = mc_analyzer.mc_with_replacement(mc_analyzer.get_periodic_returns(portwine_results))\n", "             print(\"Monte Carlo analysis computed (stats not plotted).\")\n", "        except Exception as e:\n", "            print(f\"Could not run <PERSON> analysis: {e}\")\n", "\n", "\n", "# Example 3: Seasonality Analyzer\n", "if SeasonalityAnalyzer and portwine_results['strategy_returns'] is not None and not portwine_results['strategy_returns'].empty:\n", "    print(\"\\n--- Running SeasonalityAnalyzer ---\")\n", "    season_analyzer = SeasonalityAnalyzer()\n", "    # Plot requires benchmark returns in the provided snippet implementation\n", "    if portwine_results['benchmark_returns'] is not None and not portwine_results['benchmark_returns'].empty:\n", "        season_analyzer.plot(portwine_results)\n", "        # Generate report\n", "        season_report = season_analyzer.generate_report(portwine_results)\n", "        print(\"\\nSeasonality Report:\")\n", "        print(season_report)\n", "    else:\n", "        print(\"Skipping Seasonality plot/report as benchmark returns are missing.\")\n", "        try:\n", "            season_analysis = season_analyzer.analyze(portwine_results)\n", "            print(\"Seasonality analysis computed (report/plot skipped).\")\n", "            # You could potentially print parts of season_analysis here\n", "        except Exception as e:\n", "            print(f\"Could not run Seasonality analysis: {e}\")\n", "\n", "\n", "# Example 4: Train/Test Split Analyzer\n", "if TrainTestEquityDrawdownAnalyzer and portwine_results['strategy_returns'] is not None and not portwine_results['strategy_returns'].empty:\n", "    print(\"\\n--- Running TrainTestEquityDrawdownAnalyzer ---\")\n", "    traintest_analyzer = TrainTestEquityDrawdownAnalyzer()\n", "    # Plot requires benchmark returns\n", "    if portwine_results['benchmark_returns'] is not None and not portwine_results['benchmark_returns'].empty:\n", "        traintest_analyzer.plot(portwine_results, split=0.7, benchmark_label='SPY')\n", "    else:\n", "        print(\"Skipping TrainTest plot as benchmark returns are missing.\")\n", "\n", "\n", "# Add more analyzers as needed...\n", "# Example: RegimeChangeAnalyzer\n", "if RegimeChangeAnalyzer and portwine_results['strategy_returns'] is not None and not portwine_results['strategy_returns'].empty:\n", "    print(\"\\n--- Running RegimeChangeAnalyzer ---\")\n", "    regime_analyzer = RegimeChangeAnalyzer()\n", "    if portwine_results['benchmark_returns'] is not None and not portwine_results['benchmark_returns'].empty:\n", "        try:\n", "            regime_analyzer.plot(portwine_results)\n", "            regime_report = regime_analyzer.generate_report(portwine_results)\n", "            print(\"\\nRegime Change Report:\")\n", "            print(regime_report)\n", "        except Exception as e:\n", "            print(f\"Error running RegimeChangeAnalyzer plot/report: {e}\")\n", "    else:\n", "        print(\"Skipping RegimeChangeAnalyzer plot/report as benchmark returns are missing.\")\n", "        try:\n", "            regime_analysis = regime_analyzer.analyze(portwine_results)\n", "            print(\"Regime Change analysis computed (report/plot skipped).\")\n", "            # You could potentially print parts of regime_analysis here\n", "        except Exception as e:\n", "            print(f\"Could not run RegimeChangeAnalyzer analysis: {e}\")"]}, {"cell_type": "code", "execution_count": 289, "metadata": {}, "outputs": [], "source": ["pnl_by_exit_reason = tr_min_price.groupby('exit_reason')['pnl'].sum()\n", "\n", "# Print the result\n", "print(\"PnL by Exit Reason:\")\n", "print(pnl_by_exit_reason)\n"]}, {"cell_type": "code", "execution_count": 290, "metadata": {}, "outputs": [], "source": ["\n", "# Assuming portwine_extras is in your Python path\n", "from src.sandbox.portwine_extras.analyzers.bootstrap import BootstrapAnalyzer\n", "from src.sandbox.portwine_extras.analyzers.drawdownflattener import DrawdownFlattenAnalyzer\n", "from src.sandbox.portwine_extras.analyzers.edgedecay import EdgeDecayAnalyzer\n", "from src.sandbox.portwine_extras.analyzers.optimalleverage import OptimalLeverageAnalyzer\n", "from src.sandbox.portwine_extras.analyzers.positionsizing import PositionSizingAnalyzer\n", "from src.sandbox.portwine_extras.analyzers.shock import ShockAnalyzer\n", "from src.sandbox.portwine_extras.analyzers.downsidecorrelation import DownsideCorrelationAnalyzer\n", "from src.sandbox.portwine_extras.analyzers.walkforward import WalkForwardAnalyzer\n", "\n", "\n", "# --- Use Core Portwine Analyzers (Examples) ---\n", "if EquityDrawdownAnalyzer and 'strategy_returns' in portwine_results and not portwine_results['strategy_returns'].empty:\n", "    print(\"\\n--- Running EquityDrawdownAnalyzer ---\")\n", "    equity_analyzer = EquityDrawdownAnalyzer()\n", "    if 'benchmark_returns' in portwine_results and not portwine_results['benchmark_returns'].empty:\n", "        equity_analyzer.plot(portwine_results, benchmark_label='SPY')\n", "        report = equity_analyzer.generate_report(portwine_results, benchmark_label='SPY')\n", "        print(\"\\nEquity Drawdown Report:\")\n", "        print(report)\n", "    else:\n", "        print(\"Skipping EquityDrawdown plot/report (benchmark missing).\")\n", "\n", "\n", "# --- Use Portwine Extras Analyzers (Examples) ---\n", "\n", "# Example 1: Optimal Leverage Analyzer\n", "if OptimalLeverageAnalyzer and 'strategy_returns' in portwine_results and not portwine_results['strategy_returns'].empty \\\n", "   and 'benchmark_returns' in portwine_results and not portwine_results['benchmark_returns'].empty:\n", "    print(\"\\n--- Running OptimalLeverageAnalyzer ---\")\n", "    optlev_analyzer = OptimalLeverageAnalyzer(start_leverage=0.5, end_leverage=2.5, step=0.1)\n", "    try:\n", "        optlev_analyzer.plot(portwine_results)\n", "    except Exception as e:\n", "        print(f\"Error plotting OptimalLeverageAnalyzer: {e}\")\n", "\n", "# Example 2: <PERSON>\n", "if ShockAnalyzer and 'strategy_returns' in portwine_results and not portwine_results['strategy_returns'].empty:\n", "    print(\"\\n--- Running ShockAnalyzer ---\")\n", "    # You can use default periods or define your own\n", "    # custom_periods = {\"My Crisis\": (\"2025-02-01\", \"2025-03-01\")}\n", "    # shock_analyzer = ShockAnalyzer(stress_periods=custom_periods)\n", "    shock_analyzer = ShockAnalyzer() # Uses defaults\n", "    try:\n", "        shock_analyzer.plot(portwine_results)\n", "    except Exception as e:\n", "        print(f\"Error plotting ShockAnalyzer: {e}\")\n", "\n", "\n", "# Example 3: Drawdown <PERSON><PERSON> Analyzer\n", "if DrawdownFlattenAnalyzer and 'strategy_returns' in portwine_results and not portwine_results['strategy_returns'].empty:\n", "     print(\"\\n--- Running DrawdownFlattenAnalyzer ---\")\n", "     # Flatten fully if drawdown exceeds 15%\n", "     dd_flatten_analyzer = DrawdownFlattenAnalyzer(max_dd=0.15, scale_factor=0.25)\n", "     try:\n", "         dd_flatten_analyzer.plot(portwine_results)\n", "     except Exception as e:\n", "         print(f\"Error plotting DrawdownFlattenAnalyzer: {e}\")\n", "\n", "# Example 4: <PERSON><PERSON><PERSON>\n", "if BootstrapAnalyzer and 'strategy_returns' in portwine_results and not portwine_results['strategy_returns'].empty \\\n", "   and 'benchmark_returns' in portwine_results and not portwine_results['benchmark_returns'].empty:\n", "     print(\"\\n--- Running BootstrapAnalyzer ---\")\n", "     bootstrap_analyzer = BootstrapAnalyzer()\n", "     try:\n", "        # Ensure lengths match if needed by analyzer\n", "        min_len = min(len(portwine_results['strategy_returns']), len(portwine_results['benchmark_returns']))\n", "        temp_results = {\n", "            'strategy_returns': portwine_results['strategy_returns'].iloc[-min_len:],\n", "            'benchmark_returns': portwine_results['benchmark_returns'].iloc[-min_len:]\n", "        }\n", "        if len(temp_results['strategy_returns']) == len(temp_results['benchmark_returns']) and len(temp_results['strategy_returns']) > 10: # Need enough data\n", "             bootstrap_analyzer.plot(temp_results, n_sims=500, n_days=252, block_size=5)\n", "        else:\n", "             print(\"Skipping Bootstrap plot due to length mismatch or insufficient data after alignment.\")\n", "     except Exception as e:\n", "         print(f\"Error plotting BootstrapAnalyzer: {e}\")\n", "\n", "\n", "# Example 5: Position Sizing Analyzer\n", "if PositionSizingAnalyzer and 'strategy_returns' in portwine_results and not portwine_results['strategy_returns'].empty:\n", "    print(\"\\n--- Running PositionSizingAnalyzer ---\")\n", "    pos_sizing_analyzer = PositionSizingAnalyzer()\n", "    try:\n", "        # Plot results for position sizes from 0.5 to 3.0, optimizing for Sharpe\n", "        pos_sizing_analyzer.plot(\n", "            results=portwine_results,\n", "            position_size_start=0.5,\n", "            position_size_step=0.1,\n", "            max_position_size=3.0,\n", "            objective='sharpe',\n", "            stop_drawdown_threshold=0.9 # Stop if drawdown hits 90%\n", "        )\n", "    except Exception as e:\n", "        print(f\"Error plotting PositionSizingAnalyzer: {e}\")\n", "\n", "# Example 6: <PERSON> Decay Analyzer\n", "print(\"\\n--- Running EdgeDecayAnalyzer ---\")\n", "edge_analyzer = EdgeDecayAnalyzer()\n", "try:\n", "    edge_analyzer.plot(portwine_results, rolling_window=120) # Use a 120-day window\n", "except Exception as e:\n", "    print(f\"Error plotting EdgeDecayAnalyzer: {e}\")\n", "    \n", "# Downside Correlation\n", "print(\"\\n--- Running DownsideCorrelationAnalyzer ---\")\n", "downside_correlation_analyzer = DownsideCorrelationAnalyzer()\n", "try:\n", "    downside_correlation_analyzer.plot(portwine_results) # Use a 120-day window\n", "except Exception as e:\n", "    print(f\"Error plotting DownsideCorrelationAnalyzer: {e}\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}