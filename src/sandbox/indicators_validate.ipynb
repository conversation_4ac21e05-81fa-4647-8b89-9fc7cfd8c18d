{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "from pytz import timezone\n", "import os\n", "\n", "market_data = (MarketDataBuilder()\n", "                 .with_trade_session(\"full\") \n", "                 .with_period(\"intraday\")\n", "                 .build_market_data())\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>timestamp</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-05-31 19:55:00-04:00</th>\n", "      <td>62.4400</td>\n", "      <td>62.45</td>\n", "      <td>62.4400</td>\n", "      <td>62.4497</td>\n", "      <td>3054</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-31 19:56:00-04:00</th>\n", "      <td>62.4401</td>\n", "      <td>62.48</td>\n", "      <td>62.4401</td>\n", "      <td>62.4609</td>\n", "      <td>9975</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-31 19:57:00-04:00</th>\n", "      <td>62.4600</td>\n", "      <td>62.46</td>\n", "      <td>62.4500</td>\n", "      <td>62.4600</td>\n", "      <td>1398</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-31 19:58:00-04:00</th>\n", "      <td>62.4600</td>\n", "      <td>62.48</td>\n", "      <td>62.4500</td>\n", "      <td>62.4700</td>\n", "      <td>2603</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-31 19:59:00-04:00</th>\n", "      <td>62.4500</td>\n", "      <td>62.48</td>\n", "      <td>62.4500</td>\n", "      <td>62.4600</td>\n", "      <td>2062</td>\n", "      <td>44</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              open   high      low    close  volume  count\n", "timestamp                                                                 \n", "2024-05-31 19:55:00-04:00  62.4400  62.45  62.4400  62.4497    3054     20\n", "2024-05-31 19:56:00-04:00  62.4401  62.48  62.4401  62.4609    9975     14\n", "2024-05-31 19:57:00-04:00  62.4600  62.46  62.4500  62.4600    1398     15\n", "2024-05-31 19:58:00-04:00  62.4600  62.48  62.4500  62.4700    2603     25\n", "2024-05-31 19:59:00-04:00  62.4500  62.48  62.4500  62.4600    2062     44"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "def get_market_data():\n", "    return (MarketDataBuilder()\n", "                .with_trade_session(\"full\") \n", "                .with_period(\"intraday\")\n", "                .build_market_data())\n", "\n", "def fetch_data(ticker, start_dt, end_dt):\n", "    market_data = get_market_data()\n", "    return market_data.gather_historical_data(ticker, start_dt, end_dt, interval=60)\n", "\n", "# Example usage\n", "ticker = \"TQQQ\"\n", "\n", "# Define the timezone\n", "eastern = timezone(\"US/Eastern\")\n", "\n", "# Ticker and timezone-aware start/end\n", "ticker = \"TQQQ\"\n", "start_dt = eastern.localize(datetime(2024, 3, 1, 9, 30))\n", "end_dt = eastern.localize(datetime(2024, 6, 1, 16, 0))\n", "\n", "df = fetch_data(ticker, start_dt, end_dt)\n", "df.tail()\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from trading_framework.indicators import OnDemandIndicatorMixin\n", "\n", "# Mimicking how EnhancedIntradayStrategy sets up context and indicators\n", "mixin = OnDemandIndicatorMixin()\n", "context = {}\n", "mixin.initialize_context(context)\n", "\n", "# You may want to set this to an actual open bar or mock one\n", "current_open_bar = df.iloc[[-1]].copy()\n", "current_open_bar.index = pd.DatetimeIndex([df.index[-1]])\n", "\n", "# Feed bars + open bar to preprocess like your strategy does\n", "mixin.preprocess_bar(df, current_open_bar, context, ticker, metadata={})\n", "\n", "registry = context[\"indicators\"]\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["\n", "def compute_true_range(df):\n", "    \"\"\"\n", "    Compute True Range (TR) for each row.\n", "    \"\"\"\n", "    high = df['high']\n", "    low = df['low']\n", "    close_prev = df['close'].shift(1)\n", "    \n", "    tr1 = high - low\n", "    tr2 = (high - close_prev).abs()\n", "    tr3 = (low - close_prev).abs()\n", "    \n", "    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)\n", "    return tr\n", "\n", "def compute_raw_14_day_atr(df):\n", "    \"\"\"\n", "    Computes raw ATR(14) from intraday data by using daily high-low range.\n", "    \"\"\"\n", "    df = df.copy()\n", "    df['date'] = df.index.date\n", "\n", "    # Compute daily true range: high - low\n", "    daily_range = df.groupby('date').apply(lambda x: x['high'].max() - x['low'].min())\n", "\n", "    # 14-day rolling average of daily range (i.e., ATR)\n", "    atr = daily_range.rolling(window=14).mean()\n", "\n", "    return atr\n", "\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_46415/3690573874.py:24: DeprecationWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  daily_range = df.groupby('date').apply(lambda x: x['high'].max() - x['low'].min())\n", "100%|██████████| 64/64 [00:00<00:00, 85.37it/s] \n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from tqdm import tqdm  # for progress bar\n", "import matplotlib.pyplot as plt\n", "import trading_framework.indicators\n", "import importlib\n", "\n", "importlib.reload(trading_framework.indicators)\n", "\n", "# Then re-import the updated class\n", "from trading_framework.indicators import OnDemandIndicatorMixin\n", "importlib.reload(trading_framework.indicators)\n", "\n", "def compute_indicator_atr_series(df, period=14):\n", "    mixin = OnDemandIndicatorMixin()\n", "    context = {}\n", "    mixin.initialize_context(context)\n", "    registry = context['indicators']\n", "\n", "    atr_values = {}\n", "\n", "    trading_days = sorted(df.index.normalize().unique())\n", "\n", "    for day in tqdm(trading_days):\n", "        # Convert naive date into timezone-aware datetime at 23:59\n", "        day_end = eastern.localize(datetime.combine(day, datetime.max.time().replace(microsecond=0)))\n", "        \n", "        # Only include data up to that moment\n", "        current_day_bars = df[df.index <= day_end]\n", "        if current_day_bars.empty:\n", "            continue\n", "\n", "        current_open_bar = current_day_bars.iloc[[-1]]\n", "        mixin.preprocess_bar(current_day_bars, current_open_bar, context, ticker, metadata={})\n", "        atr = registry.get(\"atr\", period=period)\n", "        if atr is not None:\n", "            atr_values[day] = atr\n", "    \n", "    return pd.Series(atr_values)\n", "\n", "\n", "raw_atr = compute_raw_14_day_atr(df)\n", "indicator_atr = compute_indicator_atr_series(df, period=14)\n", "# Ensure both have a pure `datetime.date` index (naive)\n", "raw_atr.index = pd.to_datetime(raw_atr.index).date\n", "indicator_atr.index = pd.to_datetime(indicator_atr.index).date\n", "\n", "# Combine and plot\n", "combined = pd.DataFrame({\n", "    \"Raw ATR(14)\": raw_atr,\n", "    \"Indicator ATR(14)\": indicator_atr\n", "}).dropna()\n", "\n", "# Plot\n", "plt.figure(figsize=(14, 6))\n", "combined.plot(ax=plt.gca())\n", "plt.title(\"14-Day ATR: Raw vs Indicator\")\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"ATR Value\")\n", "plt.grid(True)\n", "plt.legend()\n", "plt.show()\n", "\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}