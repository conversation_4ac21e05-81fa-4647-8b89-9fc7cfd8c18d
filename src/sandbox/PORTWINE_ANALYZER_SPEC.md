## **Portwine Analyzer Input Dictionary Specification**

This document specifies the required structure and content of the Python dictionary that serves as the primary input for most portwine analyzers when generated outside the standard portwine.backtester.Backtester. Adhering to this specification ensures compatibility with analyzers like EquityDrawdownAnalyzer, MonteCarloAnalyzer, SeasonalityAnalyzer, TrainTestEquityDrawdownAnalyzer, StudentsTTestAnalyzer, etc.

### **1\. Overall Structure**

The required input is a Python dictionary containing specific keys mapped to pandas DataFrames or Series.

analyzer\_input \= {  
    'signals\_df':        pd.DataFrame, \# Optional but needed for some analyzers (e.g., TransactionCost)  
    'tickers\_returns':   pd.DataFrame, \# Required for benchmark calculations and some analyzers (e.g., Correlation)  
    'strategy\_returns':  pd.Series,    \# Essential for most performance analysis  
    'benchmark\_returns': pd.Series     \# Optional, for comparison plots and stats  
}

### **2\. Component Specifications**

#### **2.1. strategy\_returns (pd.Series) \- Essential**

* **Type**: pandas.Series  
* **Index**:  
  * Must be a pandas.DatetimeIndex.  
  * Should represent the **complete, regular frequency** of the backtest period (e.g., every minute within trading hours, or every trading day).  
  * Must be **sorted chronologically**.  
  * Should ideally have a name (e.g., 'date' or 'timestamp'), though not strictly enforced by all analyzers.  
* **Values**:  
  * Floating-point numbers (float).  
  * Represent the **period-over-period percentage return** of the *entire strategy portfolio* for the interval ending at the index timestamp.  
  * Example: If the index is daily, the value at 2023-01-05 is the return achieved by the portfolio from the close of 2023-01-04 to the close of 2023-01-05. If the index is per-minute, the value at 09:31:00 is the return from 09:30:00 to 09:31:00.  
* **Handling Missing Data**: Should contain 0.0 for periods where the strategy was flat or had no PnL. NaN values should generally be avoided or filled appropriately (e.g., with 0\) before passing to analyzers, unless an analyzer specifically handles them.  
* **Importance**: This is the **most critical** component, used by nearly all performance analyzers to calculate equity curves, drawdowns, Sharpe ratios, etc.

#### **2.2. benchmark\_returns (pd.Series) \- Optional**

* **Type**: pandas.Series  
* **Index**:  
  * Must be a pandas.DatetimeIndex.  
  * Must be **aligned** with the strategy\_returns index (same frequency, same start/end dates, same timestamps). Use reindex and appropriate filling (like fillna(0.0) or ffill()) if necessary before creating the dictionary.  
* **Values**:  
  * Floating-point numbers (float).  
  * Represent the **period-over-period percentage return** of the chosen benchmark (e.g., SPY buy-and-hold, equal-weight portfolio) for the interval ending at the index timestamp.  
* **Handling Missing Data**: Should contain 0.0 for periods with no benchmark return. NaN values should be filled.  
* **Importance**: Used for comparison plots (equity curves, drawdowns) and relative performance statistics. If None or absent, benchmark-related analysis will be skipped.

#### **2.3. tickers\_returns (pd.DataFrame) \- Required for Benchmarks/Some Analyzers**

* **Type**: pandas.DataFrame  
* **Index**:  
  * Must be a pandas.DatetimeIndex.  
  * Must be **aligned** with the strategy\_returns index.  
* **Columns**:  
  * Column names must be the **ticker symbols** of the primary assets traded or considered by the strategy (e.g., 'AAPL', 'MSFT', 'TQQQ').  
* **Values**:  
  * Floating-point numbers (float).  
  * Each cell \[timestamp, ticker\] contains the **period-over-period percentage return** for that specific ticker for the interval ending at timestamp.  
* **Handling Missing Data**: Should contain 0.0 where an individual ticker had no return (e.g., before it started trading, during a halt if price is forward-filled). NaN values should generally be filled.  
* **Importance**: Required for calculating standard benchmarks like 'equal\_weight' or 'markowitz' *if* you were using the portwine backtester's benchmark functionality (less relevant if you pre-calculate benchmark\_returns). Also required by analyzers like CorrelationAnalyzer. If you only need analyzers that work solely off strategy\_returns (like EquityDrawdown, MonteCarlo), you might provide None, but it's safer to include it if possible.

#### **2.4. signals\_df (pd.DataFrame) \- Optional (Required for Specific Analyzers)**

* **Type**: pandas.DataFrame  
* **Index**:  
  * Must be a pandas.DatetimeIndex.  
  * Must be **aligned** with the strategy\_returns index.  
* **Columns**:  
  * Column names must be the **ticker symbols** of the primary assets traded by the strategy.  
* **Values**:  
  * Floating-point numbers (float).  
  * Each cell \[timestamp, ticker\] contains the **target allocation weight** for that ticker as determined by the strategy *at* that timestamp. These are the weights intended to be held during the *following* period (consistent with shift\_signals=True behavior). See Section 3 of the "Portwine Strategy step Method Specification" for details on weight interpretation.  
* **Handling Missing Data**: Should contain 0.0 for periods where a ticker was not held. NaN should generally be avoided.  
* **Importance**: Not used by most basic performance analyzers but **required** by analyzers that depend on portfolio composition or turnover, such as TransactionCostAnalyzer. If not needed for the specific analyzers you intend to run, this can be set to None.

### **3\. Consistency Notes**

* **Index Alignment**: It is crucial that all provided Series and DataFrames share the exact same DatetimeIndex (frequency, start, end, specific timestamps). Use df.reindex(target\_index, ...) to align data before creating the final dictionary.  
* **Frequency**: The frequency of the index (e.g., daily, hourly, minutely) determines the interpretation of the returns and the granularity of the analysis. Ensure all components use the same intended frequency.  
* **Completeness**: The index should cover the *entire* backtest period without gaps, even on days/times with no trading activity (where returns would typically be 0.0).

By generating a dictionary that conforms to this specification, you can effectively use portwine's analysis tools on the results produced by your custom Jupyter notebook simulations.