# %%
from datetime import datetime, timedelta
# TODO
# [ ] Fix bug in benchmark - its calculating equal weight over the entire set of tickers, not just point in time
# [ ] Lower churn by monthly rebalancing instead of daily
# [ ] Why does data from 2017 to 2020 not work
# [ ] Transaction costs are not acconted - 10 bps is too optimistic

start_dt = datetime(2023, 1, 1)
end_dt = datetime(2025, 1, 1)

# %%
import os

# from marketdata import ccxt_market_data  # deprecated – switched to local Binance data
from marketdata.binance_klines_market_data import BinanceKlinesHistMarketData
from marketdata.ccxt_market_data import CcxtMarketData
from marketdata.imarketdata import IMarketData
from typing import Optional
import pytz
import logging
import numpy as np
import pandas as pd
from marketdata.market_data_loader_adapter import MarketDataLoaderAdapter
import dotenv
from universe.crypto_universe_selector import CryptoUniverseSelector as HistoricalCMC

dotenv.load_dotenv(os.path.expanduser('~/w/backtest/.env'))

# Ensure working directory is project root (for data paths)
os.chdir(os.path.expanduser('~/w/backtest'))

# Keeping autoreload magic
%load_ext autoreload
%autoreload 2

# Construct Top50Snapshot
from pathlib import Path
import pandas as pd
from datetime import datetime, date
from typing import List, Dict, Any, Union

SNAPSHOT_STORE = Path("data/top50_snapshots.parquet")


def _download_snapshots(
    start: Union[str, date] = date(2017, 1, 1),
    stop: Union[str, date, None] = None,
    force: bool = False,
) -> pd.DataFrame:
    """
    Scrape CMC snapshots between *start* and *stop* (inclusive) and return
    a tidy DataFrame with one row per coin per snapshot.

    If SNAPSHOT_STORE already exists, only missing dates are scraped
    unless *force* is True.
    """
    start = pd.to_datetime(start).date()
    stop = pd.to_datetime(stop or date.today()).date()

    cmc = HistoricalCMC()
    available: List[date] = cmc.get_available_dates()

    wanted = [d for d in available if start <= d <= stop]

    # reuse anything we've already scraped
    if SNAPSHOT_STORE.exists() and not force:
        existing = pd.read_parquet(SNAPSHOT_STORE)
        done_dates = set(existing.index.unique())
        wanted = [d for d in wanted if d not in done_dates]
    else:
        existing = pd.DataFrame()

    rows: List[Dict[str, Any]] = []
    for snap_date in wanted:
        coins = cmc.get_top_coins(snap_date, top=50)
        for c in coins:
            rows.append(
                {
                    "snapshot_date": snap_date,
                    "rank": c["rank"],
                    "symbol": c["symbol"],
                    "name": c["name"],
                    "market_cap": c["market_cap"],
                }
            )
        print(f"✔ fetched {snap_date} – {len(coins)} coins")

    new_df = pd.DataFrame(rows).set_index("snapshot_date")
    full = (
        pd.concat([existing, new_df], axis=0)
        .sort_index()
        .sort_values(["snapshot_date", "rank"])
    )
    full.to_parquet(SNAPSHOT_STORE, index=True)
    return full


def load_index(start: Union[str, date] = "2017-01-01", force_download=False) -> pd.DataFrame:
    """
    Ensure index exists on disk and return it as a DataFrame.
    """
    if not SNAPSHOT_STORE.exists() or force_download:
        _download_snapshots(start=start, force=force_download)
    return pd.read_parquet(SNAPSHOT_STORE)


# ============================================================
# Helpers for your strategy / back-test
# ============================================================

def get_snapshot_before(ts) -> pd.Timestamp:
    """
    Return the most recent snapshot date ≤ *ts*.
    Works for tz-aware or tz-naïve inputs.
    """
    idx = load_index()

    # ── NEW: strip timezone, keep only the date part ────────────────────────
    ts = pd.to_datetime(ts)              # make sure it’s a Timestamp
    if ts.tz is not None:                # if tz-aware (e.g. UTC)
        ts = ts.tz_convert(None)         # drop the timezone
    ts = ts.normalize()                  # set hour/min/sec = 0
    # ----------------------------------------------------------------------

    dates = idx.index.unique()           # DatetimeIndex (naïve)
    snap_date = dates[dates.get_indexer([ts], method="pad")[0]]
    return snap_date

def constituents_for(ts: Union[str, pd.Timestamp, datetime]) -> List[str]:
    """
    List of 50 symbols valid at *ts*, but only updated once per calendar month.
    We first snap ts to the 1st of its month, then get the most recent snapshot
    on or before that date.
    """
    # ensure it’s a pandas Timestamp
    ts = pd.Timestamp(ts)
    # move to the first day of that month (at midnight)
    month_start = ts.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    idx = load_index()
    # now only ever get the snapshot as of the month's start
    snap_date = get_snapshot_before(month_start)

    return (
        idx.loc[snap_date]
           .sort_values("rank")
           .head(50)["symbol"]
           .tolist()
    )
    
# ---------------------------------------------------------------------------
# Binance market data wrapper with spot-then-futures fallback
# ---------------------------------------------------------------------------


class BinanceSpotMarketData(IMarketData):
    """
    Thin wrapper around `BinanceKlinesHistMarketData` that:
      • restricts to *spot* market CSVs
      • chooses the first available USDC/USDT quote for a given base asset
      • transparently converts naive datetimes to US/Eastern tz (required by provider)
    """

    def __init__(
        self,
        base_dir: str = "market_data/binance",
        quotes: tuple[str, ...] = ("USDC", "USDT"),
        interval: str = "1d",
    ):
        # logger setup
        self._log = logging.getLogger(self.__class__.__name__)

        # Spot provider (primary)
        self._spot = BinanceKlinesHistMarketData(
            base_dir=base_dir,
            market="spot",
            interval=interval,
        )

        # Futures provider (fallback)
        try:
            self._fut = BinanceKlinesHistMarketData(
                base_dir=base_dir,
                market="futures",
                interval=interval,
            )
        except RuntimeError as e:
            # If futures CSVs not present, keep None – we’ll skip
            self._log.warning("Futures CSVs unavailable: %s", e)
            self._fut = None

        self._quotes = [q.upper() for q in quotes]
        self._tz = pytz.timezone("US/Eastern")

        # CCXT remote provider (last resort)
        try:
            self._ccxt = CcxtMarketData(exchanges=("binance", "binanceus", "kucoin"), quotes=tuple(self._quotes))
        except Exception as e:
            self._log.warning("Unable to initialize CcxtMarketData fallback: %s", e)
            self._ccxt = None

    # -------------- helpers -------------------------------------------------
    def _to_est(self, ts: Optional[datetime]):
        if ts is None:
            return None
        return self._tz.localize(ts) if ts.tzinfo is None else ts.astimezone(self._tz)

    # -------------- IMarketData interface -----------------------------------
    def gather_historical_data(
        self,
        ticker: str,
        start_dt: datetime,
        end_dt: Optional[datetime] = None,
        interval: int = 60,
    ):
        start_dt_est = self._to_est(start_dt)
        end_dt_est = self._to_est(end_dt)

        markets = [("SPOT", self._spot), ("FUTURES", self._fut), ("CCXT", self._ccxt)]

        for quote in self._quotes:
            symbol = f"{ticker.upper()}{quote}"
            for mkt_label, provider in markets:
                if provider is None:
                    continue
                try:
                    df = provider.gather_historical_data(
                        symbol,
                        start_dt_est,
                        end_dt_est,
                        interval,
                    )
                except KeyError:
                    continue  # pair truly absent in that market

                if df is None or df.empty:
                    self._log.info(
                        "[%s] %s – EMPTY dataframe (%s rows)", mkt_label, symbol, 0
                    )
                    continue

                self._log.debug("Loaded %s %s rows=%d", symbol, mkt_label, len(df))
                return df

        raise KeyError(
            f"No Binance data (spot or futures) found for {ticker} quoted in {self._quotes}."
        )

# Instantiate the market data source & loader
binance_market_data = BinanceSpotMarketData()
loader = MarketDataLoaderAdapter(binance_market_data, start_dt, end_dt)

# %%
# Construct Top50Snapshot

from pathlib import Path
import pandas as pd
from datetime import datetime, date
from typing import List, Dict, Any, Union
import numpy as np
from collections import deque
from portwine.strategies.base import StrategyBase

class DynamicOLMARStrategy(StrategyBase):
    """
    OLMAR that updates its universe at every step.

      universe_fn(date)  ->  ["BTC", "ETH", ...]   # max 20 symbols

    • Coins enter with weight 0 and start building history.
    • Coins leaving the universe are liquidated that day (weight 0)
      and ignored thereafter until they re-enter.
    """

    def __init__(self, universe_fn, eps=10.0, window=20):
        # initialise with the FIRST universe so StrategyBase is happy
        first_date = None          # Backtester will set current_date before first step
        self.universe_fn = universe_fn
        self.eps = eps
        self.window = window
        self.tickers = []          # filled in _sync_universe()
        self.b_t = np.array([])    # weights
        self.recent_closes = {}    # symbol -> deque
        self.prev_close = {}       # yesterday’s close

    # --------------------------------------------------------------------- #
    # StrategyBase interface
    # --------------------------------------------------------------------- #
    def step(self, current_date, daily_data):
        self._sync_universe(current_date)

        # ---- 1. price relatives ------------------------------------------------
        price_relatives = np.ones(len(self.tickers))
        valid_today = []

        for i, sym in enumerate(self.tickers):
            bar = daily_data.get(sym)
            if not bar or bar.get("close") is None:
                price_relatives[i] = 0.0
                continue

            px = bar["close"]
            self.recent_closes[sym].append(px)
            if sym in self.prev_close and self.prev_close[sym] > 0:
                price_relatives[i] = px / self.prev_close[sym]

            self.prev_close[sym] = px
            valid_today.append(i)

        # ---- 2. pick symbols with ≥window bars of history ----------------------
        valid_idxs = [
            i for i, sym in enumerate(self.tickers)
            if len(self.recent_closes[sym]) >= self.window
        ]

        if len(valid_idxs) >= 10:                    # run OLMAR normally
            b_sub = self.b_t[valid_idxs]
            x_pred_sub = np.array([
                np.mean(self.recent_closes[self.tickers[i]]) /
                self.recent_closes[self.tickers[i]][-1]
                for i in valid_idxs
            ])

            b_sub_new = self._olmar_update(b_sub, x_pred_sub)
            b_temp = np.zeros_like(self.b_t)
            b_temp[valid_idxs] = b_sub_new
            self.b_t = self._filter_missing_and_normalize(b_temp, price_relatives)

        else:                                        # fall-back equal weight
            self.b_t[:] = 0.0
            if valid_today:
                self.b_t[valid_today] = 1.0 / len(valid_today)

        # ---- 3. convert to {symbol: weight} ------------------------------------
        return {sym: float(self.b_t[i]) for i, sym in enumerate(self.tickers)}

    # ------------------------------------------------------------------ helpers
    def _sync_universe(self, current_date):
        """Grow/shrink internal arrays to match today’s top-20."""
        today_universe = list(self.universe_fn(current_date))
        if today_universe == self.tickers:
            return                                   # nothing to do

        # --- add any *new* symbols
        for sym in today_universe:
            if sym not in self.tickers:
                self.tickers.append(sym)
                self.b_t = np.append(self.b_t, 0.0)
                self.recent_closes[sym] = deque(maxlen=self.window)

        # --- zero-weight & optionally purge symbols that fell out
        for i, sym in reversed(list(enumerate(self.tickers))):
            if sym not in today_universe:
                self.b_t[i] = 0.0                    # liquidate
                # keep history so a coin can re-enter later

        # Keep tickers order stable for reproducibility
        order = np.argsort(self.tickers)
        self.tickers = [self.tickers[i] for i in order]
        self.b_t = self.b_t[order]

    # --------------- (unchanged math helpers) ----------------------------------
    def _olmar_update(self, b_t, x_pred):
        x_mean = x_pred.mean()
        gap = self.eps - b_t.dot(x_pred)
        denom = ((x_pred - x_mean) ** 2).sum()
        if denom < 1e-12:
            return b_t
        lam = max(0.0, gap / denom)
        return self._simplex_proj(b_t + lam * (x_pred - x_mean))

    def _filter_missing_and_normalize(self, w, price_rel):
        w = np.where(price_rel == 0.0, 0.0, w)
        s = w.sum()
        return w / s if s > 1e-12 else w

    def _simplex_proj(self, v):
        if v.size == 0:
            return v
        u = np.sort(v)[::-1]
        cssv = np.cumsum(u)
        rho = np.where(u - (cssv - 1) / (np.arange(len(u)) + 1) > 0)[0][-1]
        theta = (cssv[rho] - 1) / (rho + 1)
        w = np.maximum(v - theta, 0)
        return w / w.sum() if w.sum() > 0 else w



# %%
from datetime      import datetime, timedelta
from marketdata.market_data_loader_adapter import MarketDataLoaderAdapter
import numpy as np
from collections import deque
from portwine.backtester import Backtester

# 1) figure out the full superset of all symbols ever in top-20
idx_df   = load_index(start=start_dt)              # this is a DataFrame with a 'symbol' column
all_symbols = idx_df["symbol"].unique().tolist()   # e.g. ["BTC", "ETH", "XRP", ...]

# 2) instantiate your dynamic strategy
strat = DynamicOLMARStrategy(
    universe_fn=constituents_for,
    eps=2.0,
    window=25,
)

# 3) seed its internals so PortWine will fetch data for them
strat.tickers        = all_symbols
strat.b_t            = np.zeros(len(all_symbols))
strat.recent_closes  = { sym: deque(maxlen=strat.window) for sym in all_symbols }
strat.prev_close     = {}


# %%
results = Backtester(loader).run_backtest(
    strat,
    verbose=True,
    require_all_history=False
)

# %%
from portwine import analyzers

analyzer = analyzers.EquityDrawdownAnalyzer()
analyzer.generate_report(results)
analyzer.plot(results, benchmark_label='Top 20 Market Cap Coin - Equal Weighted Portfolio')


# %%
def apply_leverage_to_results(results: dict, leverage: float = 1.0) -> dict:
    """
    Returns a copy of the results dictionary with strategy returns scaled by leverage.

    Parameters
    ----------
    results : dict
        Dictionary containing at minimum a 'strategy_returns' key with a pd.Series of daily returns.
    leverage : float
        Scalar leverage to apply to the strategy returns (e.g., 2.0 for 2x leverage).

    Returns
    -------
    dict
        New dictionary with 'strategy_returns' scaled. All other keys are preserved.
    """
    results_copy = results.copy()
    results_copy['strategy_returns'] = results['strategy_returns'] * leverage
    return results_copy


results_leveraged = apply_leverage_to_results(results, 0.2)
analyzer = analyzers.EquityDrawdownAnalyzer()
analyzer.generate_report(results_leveraged)
analyzer.plot(results_leveraged, benchmark_label='Top 20 Market Cap Coin - Equal Weighted Portfolio')


# %%
analyzers.TransactionCostAnalyzer().plot(results_leveraged)

# 