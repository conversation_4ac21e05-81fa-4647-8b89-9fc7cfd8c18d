{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pytz\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2023, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 5, 1))\n", "\n", "WINDOW_MINUTES = 23 * 60 * 15 "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# !uv pip install pykalman"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "nq_data = pd.read_csv(\"market_data/static/nq_data_2010_06_07_to_2025_05_01.csv\", index_col=0, parse_dates=True)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["nq_data = nq_data.reset_index()\n", "# Convert ts_event from UTC to EST\n", "nq_data['timestamp'] = nq_data['ts_event'].dt.tz_convert('US/Eastern')\n", "nq_data = nq_data.set_index('timestamp')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["\n", "# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "# from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "from typing import List, Dict\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(start_date=start_dt.replace(tzinfo=None) - timed<PERSON><PERSON>(days=30))\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"Fetches daily data using the configured daily_market_data instance.\"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        print(\"Exception\", e)\n", "        return None\n", "\n", "def get_per_minute_data(ticker, start_dt, end_dt) -> pd.DataFrame:\n", "    \"\"\"\n", "        Output is a pandas dataframe\n", "        \n", "        DatetimeIndex with timezone ( 2010-02-11 09:43:00-05:00 to 2025-04-22 19:59:00-04:00)\n", "        Data columns (total 6 columns):\n", "        #   Column  Dtype  \n", "        ---  ------  -----  \n", "        0   open    float64\n", "        1   high    float64\n", "        2   low     float64\n", "        3   close   float64\n", "        4   volume  float64\n", "        5   date    object \n", "        dtypes: float64(5), object(1)\n", "\n", "    \"\"\"\n", "    if ticker == \"NQ\":\n", "        return nq_data[start_dt:end_dt]\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data\n", "\n", "def get_hourly_data(ticker, start_dt, end_dt) -> pd.DataFrame:\n", "    \"\"\"\n", "        Output is a pandas dataframe\n", "        \n", "        DatetimeIndex with timezone ( 2010-02-11 09:43:00-05:00 to 2025-04-22 19:59:00-04:00)\n", "        Data columns (total 6 columns):\n", "        #   Column  Dtype  \n", "        ---  ------  -----  \n", "        0   open    float64\n", "        1   high    float64\n", "        2   low     float64\n", "        3   close   float64\n", "        4   volume  float64\n", "        5   date    object \n", "        dtypes: float64(5), object(1)\n", "\n", "    \"\"\"\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=3600\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["\n", "import numpy as np\n", "import pandas as pd\n", "from pykalman import KalmanFilter\n", "\n", "\n", "def compute_kalman_filters(\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    *,\n", "    process_var: float = 1e-5,\n", "    meas_var_fast: float = 1e-3,\n", "    meas_var_slow: float = 1.0,\n", ") -> pd.DataFrame:\n", "    \"\"\"Attach **kf_fast** and **kf_slow** columns to a copy of *df*.\"\"\"\n", "\n", "    price = df[\"close\"].values.astype(float)\n", "    transition = np.array([[1, 1], [0, 1]])\n", "    observation = np.array([[1, 0]])\n", "    trans_cov = process_var * np.eye(2)\n", "\n", "    kf_fast = KalmanFilter(\n", "        transition_matrices=transition,\n", "        observation_matrices=observation,\n", "        transition_covariance=trans_cov,\n", "        observation_covariance=meas_var_fast,\n", "        initial_state_mean=[price[0], 0.0],\n", "    )\n", "    kf_slow = KalmanFilter(\n", "        transition_matrices=transition,\n", "        observation_matrices=observation,\n", "        transition_covariance=trans_cov,\n", "        observation_covariance=meas_var_slow,\n", "        initial_state_mean=[price[0], 0.0],\n", "    )\n", "\n", "    fast_state, _ = kf_fast.filter(price)\n", "    slow_state, _ = kf_slow.filter(price)\n", "\n", "    out = df.copy()\n", "    out[\"kf_fast\"] = fast_state[:, 0]\n", "    out[\"kf_slow\"] = slow_state[:, 0]\n", "    return out\n", "\n", "# ----------------------------------------------------------------------\n", "#  2.  Global percentile‑mapped QTI\n", "# ----------------------------------------------------------------------\n", "\n", "from typing import Literal, Optional\n", "import pandas as pd\n", "import numpy as np\n", "\n", "def compute_qti(\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    *,\n", "    mode: Literal[\"expanding\", \"rolling\"] = \"rolling\",\n", "    window: Optional[int] = None,          # e.g. 5_000 for rolling\n", ") -> pd.DataFrame:\n", "    \"\"\"\n", "    Attach a `qti` column (−100 … +100) without look-ahead bias.\n", "\n", "    Parameters\n", "    ----------\n", "    mode   : \"expanding\"  – rank vs. *all* past bars (default)  \n", "             \"rolling\"    – rank vs. the last `window` bars\n", "    window : size of the rolling window (required if mode=\"rolling\")\n", "    \"\"\"\n", "\n", "    out = df.copy()\n", "    out[\"pct_diff\"] = (out[\"kf_fast\"] - out[\"kf_slow\"]) / out[\"kf_slow\"] * 100\n", "\n", "    # -------- helper: convert a Series of pct_diffs -> percentile of *last* value\n", "    def _tail_percentile(series: pd.Series) -> float:\n", "        # rank(method=\"max\") so ties map to the *highest* rank seen so far\n", "        r = series.rank(method=\"max\").iloc[-1]\n", "        return (r - 1) / (len(series) - 1) * 200 - 100\n", "\n", "    if mode == \"expanding\":\n", "        out[\"qti\"] = (\n", "            out[\"pct_diff\"]\n", "            .expanding(min_periods=300)      # wait until we have enough data\n", "            .apply(_tail_percentile, raw=False)\n", "        )\n", "\n", "    elif mode == \"rolling\":\n", "        if window is None:\n", "            raise ValueError(\"`window` must be set when mode='rolling'\")\n", "        out[\"qti\"] = (\n", "            out[\"pct_diff\"]\n", "            .rolling(window=window, min_periods=window)\n", "            .apply(_tail_percentile, raw=False)\n", "        )\n", "    else:\n", "        raise ValueError(\"mode must be 'expanding' or 'rolling'\")\n", "\n", "    return out\n", "\n", "# ----------------------------------------------------------------------\n", "#  3.  Simulator\n", "# ----------------------------------------------------------------------\n", "\n", "\n", "def simulate_qti_strategy(\n", "    ticker: str,\n", "    start_dt,\n", "    end_dt,\n", "    *,\n", "    meas_var_fast: float = 1e-3,\n", "    meas_var_slow: float = 1.0,\n", "    rolling_window: int = None,\n", "    long_entry: float = 5.0,\n", "    long_target: float = 35.0,\n", "    long_stop: float = 5.0,\n", "    short_entry: float = -90.0,\n", "    short_target: float = -95.0,\n", "    short_stop: float = -90.0,\n", "    multiplier: float = 20.0,\n", ") -> pd.DataFrame:\n", "    \"\"\"\n", "    Same logic as before, but lets you feed in KF measurement variances\n", "    and a rolling-window length for QTI.\n", "    \"\"\"\n", "    df = get_per_minute_data(ticker, start_dt, end_dt).copy()\n", "    if df.index.tz is None:\n", "        df.index = df.index.tz_localize(\"US/Eastern\")\n", "    else:\n", "        df.index = df.index.tz_convert(\"US/Eastern\")\n", "    df.sort_index(inplace=True)\n", "\n", "    # --- <PERSON><PERSON> filters with user-chosen R\n", "    df = compute_kalman_filters(\n", "        df,\n", "        process_var=1e-5,\n", "        meas_var_fast=meas_var_fast,\n", "        meas_var_slow=meas_var_slow,\n", "    )\n", "\n", "    # --- Q<PERSON> without look-ahead\n", "    if rolling_window is None:\n", "        df = compute_qti(df, mode=\"expanding\")\n", "    else:\n", "        df = compute_qti(df, mode=\"rolling\", window=rolling_window)\n", "\n", "    # --- trading loop (unchanged) ---\n", "    trades: List[Dict] = []\n", "    position = 0\n", "    entry_px = entry_qti = None\n", "    entry_ts = None\n", "\n", "    prev_qti = df[\"qti\"].iloc[0]\n", "\n", "    for row in df.itertuples():\n", "        ts     = row.Index\n", "        price  = row.close\n", "        qti    = row.qti\n", "\n", "        # exits\n", "        if position == 1 and (qti >= long_target or (prev_qti >= long_stop and qti < long_stop)):\n", "            trades.append(dict(\n", "                entry_time=entry_ts, exit_time=ts, side=\"LONG\",\n", "                entry_px=entry_px, exit_px=price,\n", "                entry_qti=entry_qti, exit_qti=qti,\n", "                pnl_usd=(price - entry_px) * multiplier,\n", "            ))\n", "            position = 0\n", "\n", "        elif position == -1 and (qti <= short_target or (prev_qti <= short_stop and qti > short_stop)):\n", "            trades.append(dict(\n", "                entry_time=entry_ts, exit_time=ts, side=\"SHORT\",\n", "                entry_px=entry_px, exit_px=price,\n", "                entry_qti=entry_qti, exit_qti=qti,\n", "                pnl_usd=(entry_px - price) * multiplier,\n", "            ))\n", "            position = 0\n", "\n", "        # entries\n", "        if position == 0:\n", "            if prev_qti <= long_entry < qti:        # cross ↑ 5\n", "                position, entry_px, entry_qti, entry_ts = 1, price, qti, ts\n", "            elif prev_qti >= short_entry > qti:     # cross ↓ –90\n", "                position, entry_px, entry_qti, entry_ts = -1, price, qti, ts\n", "\n", "        prev_qti = qti\n", "\n", "    return pd.<PERSON><PERSON><PERSON>e(trades)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# from datetime import datetime\n", "# import pytz\n", "\n", "# trades = simulate_qti_strategy('NQ', start_dt, end_dt, rolling_window = WINDOW_MINUTES,)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# print(trades.head())\n", "# print(f\"Total PnL: {trades['pnl_usd'].sum():.2f}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# import numpy as np\n", "# import seaborn as sns\n", "# import matplotlib.pyplot as plt\n", "\n", "# FAST_GRID  = np.array([1e-5, 3e-5, 1e-4, 3e-4, 1e-3])\n", "# SLOW_GRID  = np.array([1e-1, 3e-1, 1e0, 3e0, 1e1, 3e1, 1e2])\n", "\n", "# records = []\n", "# for R_fast in FAST_GRID:\n", "#     for R_slow in SLOW_GRID:\n", "#         if R_slow / R_fast < 1_000:   # keep ≥ 10³× separation\n", "#             continue\n", "#         trades = simulate_qti_strategy(\n", "#             \"NQ\",\n", "#             start_dt,\n", "#             end_dt,\n", "#             meas_var_fast = R_fast,\n", "#             meas_var_slow = R_slow,\n", "#             rolling_window = WINDOW_MINUTES,\n", "#         )\n", "#         pnl = trades[\"pnl_usd\"].sum()\n", "#         records.append({\"R_fast\": R_fast, \"R_slow\": R_slow, \"PnL\": pnl})\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["32\n"]}], "source": ["import numpy as np\n", "from concurrent.futures import ProcessPoolExecutor, as_completed\n", "\n", "FAST_GRID = np.array([1e-5, 3e-5, 1e-4, 3e-4, 1e-3])\n", "SLOW_GRID = np.array([1e-1,   3e-1, 1e0, 3e0, 1e1,  3e1, 1e2])\n", "\n", "def run_one(R_fast, R_slow):\n", "    trades = simulate_qti_strategy(\n", "        \"NQ\",\n", "        start_dt,\n", "        end_dt,\n", "        meas_var_fast=R_fast,\n", "        meas_var_slow=R_slow,\n", "        rolling_window=WINDOW_MINUTES,\n", "    )\n", "    return {\"R_fast\": R_fast, \"R_slow\": R_slow, \"PnL\": trades[\"pnl_usd\"].sum()}\n", "\n", "tasks = [\n", "    (Rf, Rs)\n", "    for Rf in FAST_GRID\n", "    for Rs in SLOW_GRID\n", "    if Rs / Rf >= 1_000\n", "]\n", "\n", "print(len(tasks))\n", "\n", "records = []\n", "with ProcessPoolExecutor(max_workers=4) as exe:\n", "    futures = {exe.submit(run_one, Rf, Rs): (Rf, Rs) for Rf, Rs in tasks}\n", "    for fut in as_completed(futures):\n", "        rec = fut.result()\n", "        records.append(rec)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 900x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "grid_df = (pd.DataFrame(records)\n", "             .pivot(index=\"R_slow\", columns=\"R_fast\", values=\"PnL\")\n", "             .sort_index(ascending=False))    # nicest orientation\n", "\n", "\n", "# ------------------------------------------------------------\n", "# 3.  Visualise\n", "# ------------------------------------------------------------\n", "plt.figure(figsize=(9, 6))\n", "sns.heatmap(grid_df,\n", "            annot=True, fmt=\".0f\",\n", "            linewidths=.5, cmap=\"viridis\")\n", "plt.title(\"Total PnL (USD) — 15-day rolling QTI\\nR_slow vs R_fast\")\n", "plt.xlabel(\"R_fast (measurement variance)\")\n", "plt.ylabel(\"R_slow\")\n", "plt.yticks(rotation=0)\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}