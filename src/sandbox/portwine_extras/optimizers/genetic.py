"""
Genetic Algorithm Optimizer for vectorized backtesting strategies.
Modified to work with WalkForwardOptimizer.
"""

import numpy as np
import pandas as pd
import random
from tqdm import tqdm
import time


class GeneticAlgorithmOptimizer:
    """
    A genetic algorithm optimizer for vectorized backtesting strategies,
    adapted to work with WalkForwardOptimizer.

    This optimizer uses genetic algorithms to efficiently search through a large parameter space
    by evolving a population of parameter sets toward better performance.
    """

    def __init__(self, population_size=20, generations=10, mutation_rate=0.2,
                 num_parents=10, elite_size=2, early_stopping=3, fitness_function=None):
        """
        Initialize the genetic algorithm optimizer.

        Parameters
        ----------
        population_size : int
            Size of the population in each generation
        generations : int
            Number of generations to evolve
        mutation_rate : float
            Probability of mutation for each gene
        num_parents : int
            Number of parents to select for breeding
        elite_size : int
            Number of top individuals to carry over unchanged to next generation
        early_stopping : int
            Stop if no improvement after this many generations
        fitness_function : callable, optional
            Function that takes backtest results dictionary and returns a fitness score (float).
            If None, a default fitness function based on Sharpe ratio will be used.
        """
        # GA hyperparameters
        self.population_size = population_size
        self.generations = generations
        self.mutation_rate = mutation_rate
        self.num_parents = num_parents
        self.elite_size = elite_size
        self.early_stopping = early_stopping
        self.fitness_function = fitness_function

        # Results tracking
        self.best_individual = None
        self.best_score = -np.inf
        self.history = []

    def _default_fitness_function(self, results):
        """
        Default fitness function based on Sharpe ratio with adjustments.

        Parameters
        ----------
        results : dict
            Backtest results dictionary containing 'strategy_returns'

        Returns
        -------
        float
            Fitness score and metrics dictionary
        """
        if not results or 'strategy_returns' not in results:
            return -np.inf, None

        returns = results['strategy_returns']
        if len(returns) < 10:  # Ensure we have enough data points
            return -np.inf, None

        # Calculate Sharpe ratio as fitness
        ann_factor = 252.0
        mean_return = returns.mean() * ann_factor
        std_return = returns.std() * np.sqrt(ann_factor)
        sharpe = mean_return / std_return if std_return > 1e-8 else 0.0

        # Additional metrics
        total_return = (1 + returns).prod() - 1
        win_rate = (returns > 0).mean()
        max_drawdown = (returns.cumsum() - returns.cumsum().cummax()).min()

        # Combined fitness score
        # We weight Sharpe ratio higher but also consider other metrics
        fitness = sharpe * 0.7 + total_return * 0.2 + win_rate * 0.1

        # Penalty for excessive drawdown
        if max_drawdown < -0.3:  # More than 30% drawdown
            fitness *= 0.5

        metrics = {
            'sharpe': sharpe,
            'total_return': total_return,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'results': results  # Include full backtest results
        }

        return fitness, metrics

    def _get_param_types(self, param_grid):
        """Identify parameter types for encoding/decoding genes"""
        param_types = {}
        for param, values in param_grid.items():
            if param == 'tickers':
                param_types[param] = 'categorical'
            elif all(isinstance(v, (int, float)) for v in values):
                if all(isinstance(v, int) for v in values):
                    param_types[param] = 'integer'
                else:
                    param_types[param] = 'float'
            else:
                param_types[param] = 'categorical'
        return param_types

    def _initialize_population(self, param_grid):
        """Create an initial random population of individuals"""
        population = []
        for _ in range(self.population_size):
            individual = {}
            for param, values in param_grid.items():
                individual[param] = random.choice(values)
            population.append(individual)
        return population

    def _evaluate_individual(self, individual, backtester, strategy_class, start_date, end_date, benchmark):
        """Evaluate the fitness of an individual (parameter set)"""
        try:
            strategy = strategy_class(**individual)
            results = backtester.run_backtest(
                strategy=strategy,
                start_date=start_date,
                end_date=end_date,
                benchmark=benchmark,
                verbose=False
            )

            # If no custom fitness function, use default calculation
            if self.fitness_function is None:
                fitness, metrics = self._calculate_default_fitness(results)
            else:
                # Use the custom fitness function (should return a single number)
                fitness = self.fitness_function(results)
                metrics = {
                    'fitness': fitness,
                    'results': results
                }

            return fitness, metrics

        except Exception as e:
            print(f"Error evaluating individual: {e}")
            return -np.inf, None

    def _calculate_default_fitness(self, results):
        """Calculate default fitness based on Sharpe ratio with adjustments"""
        if not results or 'strategy_returns' not in results:
            return -np.inf, None

        returns = results['strategy_returns']
        if len(returns) < 10:  # Ensure we have enough data points
            return -np.inf, None

        # Calculate Sharpe ratio as fitness
        ann_factor = 252.0
        mean_return = returns.mean() * ann_factor
        std_return = returns.std() * np.sqrt(ann_factor)
        sharpe = mean_return / std_return if std_return > 1e-8 else 0.0

        # Additional metrics
        total_return = (1 + returns).prod() - 1
        win_rate = (returns > 0).mean()
        max_drawdown = (returns.cumsum() - returns.cumsum().cummax()).min()

        # Combined fitness score
        # We weight Sharpe ratio higher but also consider other metrics
        fitness = sharpe * 0.7 + total_return * 0.2 + win_rate * 0.1

        # Penalty for excessive drawdown
        if max_drawdown < -0.3:  # More than 30% drawdown
            fitness *= 0.5

        metrics = {
            'sharpe': sharpe,
            'total_return': total_return,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'results': results
        }

        return fitness, metrics

    def _select_parents(self, population, scores):
        """Select parents using tournament selection"""
        parents = []
        for _ in range(self.num_parents):
            # Tournament selection - pick k random individuals and select the best
            k = 3  # Tournament size
            candidates = random.sample(list(zip(population, scores)), k)
            best_candidate = max(candidates, key=lambda x: x[1])
            parents.append(best_candidate[0])
        return parents

    def _crossover(self, parent1, parent2, param_grid):
        """Create a child by combining genes from two parents"""
        child = {}
        for param in param_grid.keys():
            # 50% chance to inherit from each parent
            if random.random() < 0.5:
                child[param] = parent1[param]
            else:
                child[param] = parent2[param]
        return child

    def _mutate(self, individual, param_grid):
        """Randomly mutate genes of an individual based on mutation rate"""
        mutated = individual.copy()
        for param, values in param_grid.items():
            # Apply mutation with probability mutation_rate
            if random.random() < self.mutation_rate:
                mutated[param] = random.choice(values)
        return mutated

    def optimize_function(self, backtester, strategy_class, start_date, end_date, benchmark, verbose, param_grid):
        """
        Optimization function that conforms to the signature expected by WalkForwardOptimizer.

        Parameters
        ----------
        backtester : VectorizedBacktester
            The backtester used to evaluate strategies
        strategy_class : class
            The vectorized strategy class to optimize
        start_date : datetime
            Start date for backtesting
        end_date : datetime
            End date for backtesting
        benchmark : str, optional
            Benchmark symbol or function
        verbose : bool
            Whether to display progress information
        param_grid : dict
            Dictionary mapping parameter names to lists of possible values

        Returns
        -------
        tuple
            (best_params, train_results)
        """
        # Reset tracking for this optimization run
        best_individual = None
        best_score = -np.inf
        best_metrics = None
        generations_without_improvement = 0
        generation_results = []

        # Get parameter types for this run
        param_types = self._get_param_types(param_grid)

        # Initialize population
        population = self._initialize_population(param_grid)

        # Start evolution
        if verbose:
            print(f"Starting genetic algorithm with {self.population_size} individuals over {self.generations} generations")
            print(f"Time period: {start_date} to {end_date}")

        for generation in range(self.generations):
            gen_start_time = time.time()

            # Evaluate population
            fitness_scores = []
            metrics_list = []

            if verbose:
                eval_iter = tqdm(population, desc=f"Generation {generation + 1}/{self.generations}")
            else:
                eval_iter = population

            for individual in eval_iter:
                fitness, metrics = self._evaluate_individual(
                    individual,
                    backtester,
                    strategy_class,
                    start_date,
                    end_date,
                    benchmark
                )
                fitness_scores.append(fitness)
                metrics_list.append(metrics)

            # Track best individual
            gen_best_idx = np.argmax(fitness_scores)
            gen_best_individual = population[gen_best_idx]
            gen_best_score = fitness_scores[gen_best_idx]
            gen_best_metrics = metrics_list[gen_best_idx]

            # Update global best if improved
            if gen_best_score > best_score:
                best_individual = gen_best_individual.copy()
                best_score = gen_best_score
                best_metrics = gen_best_metrics
                generations_without_improvement = 0
            else:
                generations_without_improvement += 1

            # Store generation stats
            generation_results.append({
                'generation': generation + 1,
                'best_fitness': gen_best_score,
                'avg_fitness': np.mean(fitness_scores),
                'best_individual': gen_best_individual,
                'best_metrics': gen_best_metrics,
                'time': time.time() - gen_start_time
            })

            if verbose:
                print(f"Generation {generation + 1}: Best fitness = {gen_best_score:.4f}, " +
                      f"Avg fitness = {np.mean(fitness_scores):.4f}")
                if gen_best_metrics:
                    metrics_str = ", ".join([f"{k}={v:.4f}" for k, v in {k: v for k, v in gen_best_metrics.items()
                                                                         if k != 'results'}.items()])
                    print(f"Best individual metrics: {metrics_str}")

            # Check early stopping
            if self.early_stopping and generations_without_improvement >= self.early_stopping:
                if verbose:
                    print(f"Early stopping after {generation + 1} generations with no improvement")
                break

            # Create next generation
            next_population = []

            # Elitism - carry over best individuals
            sorted_indices = np.argsort(fitness_scores)[::-1]
            elites = [population[i].copy() for i in sorted_indices[:self.elite_size]]
            next_population.extend(elites)

            # Select parents and create offspring
            parents = self._select_parents(population, fitness_scores)

            # Create children until we refill the population
            while len(next_population) < self.population_size:
                parent1, parent2 = random.sample(parents, 2)
                child = self._crossover(parent1, parent2, param_grid)
                child = self._mutate(child, param_grid)
                next_population.append(child)

            # Update population for next generation
            population = next_population

        # Store history for this optimization run
        self.history = generation_results
        self.best_individual = best_individual
        self.best_score = best_score

        # Extract train results from best metrics
        train_results = best_metrics['results'] if best_metrics and 'results' in best_metrics else None

        # Return the best parameters and training results as expected by WalkForwardOptimizer
        return best_individual, train_results

    def plot_evolution(self):
        """Plot the evolution of fitness over generations"""
        if not self.history:
            print("No optimization history to plot")
            return

        try:
            import matplotlib.pyplot as plt

            history_df = pd.DataFrame(self.history)

            plt.figure(figsize=(10, 6))
            plt.plot(history_df['generation'], history_df['best_fitness'], 'b-', label='Best Fitness')
            plt.plot(history_df['generation'], history_df['avg_fitness'], 'r--', label='Avg Fitness')
            plt.xlabel('Generation')
            plt.ylabel('Fitness')
            plt.title('Genetic Algorithm Evolution')
            plt.legend()
            plt.grid(True)
            plt.show()
        except ImportError:
            print("Matplotlib is required for plotting")