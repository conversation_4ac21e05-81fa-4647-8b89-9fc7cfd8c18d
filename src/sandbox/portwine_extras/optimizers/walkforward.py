"""
Simplified Walk Forward Optimization implementation for vectorized backtesting strategies.

This optimizer implements a simple, chronological walk-forward analysis by:
1. Training on a fixed-length window (e.g., 5 years)
2. Testing on a subsequent window (e.g., 1 year)
3. Moving forward by the test window length
4. Repeating until the end of the data

Supports parallel processing using joblib for faster execution.
"""

import numpy as np
import pandas as pd
from tqdm import tqdm
import time
import warnings
from joblib import Parallel, delayed
from joblib_progress import joblib_progress


class WalkForwardOptimizer:
    """
    A walk-forward optimizer for vectorized backtesting strategies.

    This optimizer implements a straightforward walk-forward analysis that trains
    on a fixed window and tests on the subsequent window, then steps forward and repeats.
    This ensures complete coverage with no gaps or overlaps in the test periods.

    Supports parallel processing for faster execution when n_jobs > 1.
    """

    def __init__(self, backtester, strategy_class):
        """
        Initialize the walk-forward optimizer.

        Parameters
        ----------
        backtester : VectorizedBacktester
            The backtester used to evaluate strategies
        strategy_class : class
            The vectorized strategy class to optimize
        """
        self.backtester = backtester
        self.strategy_class = strategy_class

    def _evaluate_strategy(self, parameters, train_start_date, train_end_date, test_start_date, test_end_date,
                           benchmark=None):
        """
        Evaluate a set of parameters by running a backtest.
        For stateful strategies, this runs the backtest from the beginning of training
        to properly initialize the state, but only evaluates performance on the test period.

        Parameters
        ----------
        parameters : dict
            Strategy parameters to evaluate
        train_start_date : datetime
            Start date for the training period (to initialize state)
        train_end_date : datetime
            End date for the training period
        test_start_date : datetime
            Start date for the test period
        test_end_date : datetime
            End date for the test period
        benchmark : str, optional
            Benchmark symbol

        Returns
        -------
        results : dict
            Backtest results containing only test period performance
        """
        try:
            # Create strategy with given parameters
            strategy = self.strategy_class(**parameters)

            # Run backtest from the beginning of training period to the end of testing period
            # This allows stateful strategies to properly initialize their state
            full_results = self.backtester.run_backtest(
                strategy=strategy,
                start_date=train_start_date,
                end_date=test_end_date,
                benchmark=benchmark,
                verbose=False
            )

            # If we have results, extract only the test period returns
            if full_results and 'strategy_returns' in full_results:
                # Get the test period returns by slicing the full returns series
                test_results = {}

                # Extract all series from full_results (strategy returns, benchmark returns, etc.)
                for key in full_results:
                    if isinstance(full_results[key], pd.Series) or isinstance(full_results[key], pd.DataFrame):
                        # Extract only the test period
                        mask = (full_results[key].index >= test_start_date) & (full_results[key].index <= test_end_date)
                        test_results[key] = full_results[key].loc[mask]
                    else:
                        # For non-time series data, copy as is
                        test_results[key] = full_results[key]

                return test_results

            return None
        except Exception as e:
            print(f"Error evaluating parameters: {e}")
            return None

    def _process_window(self, step, train_start_date, train_end_date, test_start_date, test_end_date,
                        optimizer_fn, param_grid, benchmark, verbose):
        """
        Process a single walk-forward window.
        This method is designed to be parallelized with joblib.

        Parameters
        ----------
        step : int
            Window step number
        train_start_date : datetime
            Start date for training window
        train_end_date : datetime
            End date for training window
        test_start_date : datetime
            Start date for test window
        test_end_date : datetime
            End date for test window
        optimizer_fn : callable
            Optimization function
        param_grid : dict
            Parameter grid for optimization
        benchmark : str, optional
            Benchmark symbol
        verbose : bool
            Whether to show verbose output

        Returns
        -------
        tuple
            (train_key, train_period_data, test_key, test_period_data)
        """
        if verbose:
            print(f"Processing window {step + 1}: Train {train_start_date.date()} to {train_end_date.date()}, "
                  f"Test {test_start_date.date()} to {test_end_date.date()}")

        # Run optimization on training window
        start_time = time.time()
        best_params, train_results = optimizer_fn(
            self.backtester,
            self.strategy_class,
            train_start_date,
            train_end_date,
            benchmark,
            verbose,
            param_grid
        )
        opt_time = time.time() - start_time

        if verbose:
            print(f"Window {step + 1} optimization completed in {opt_time:.2f} seconds")

        # Evaluate on test window
        test_results = self._evaluate_strategy(
            best_params,
            train_start_date,
            train_end_date,
            test_start_date,
            test_end_date,
            benchmark
        )

        # Create keys for storage
        train_key = train_start_date.strftime('%Y-%m-%d')
        test_key = test_start_date.strftime('%Y-%m-%d')

        # Create result dictionaries
        train_period_data = {
            'start_date': train_start_date,
            'end_date': train_end_date,
            'best_params': best_params,
            'results': train_results,
            'step': step
        }

        test_period_data = {
            'start_date': test_start_date,
            'end_date': test_end_date,
            'best_params': best_params,  # Same params used for both train and test
            'results': test_results,
            'step': step
        }

        return train_key, train_period_data, test_key, test_period_data

    def optimize(self, optimizer_fn, param_grid, train_years=5, test_years=1,
                 start_date=None, end_date=None, benchmark=None, anchored=False,
                 verbose=True, n_jobs=1):
        """
        Run walk-forward optimization.

        Parameters
        ----------
        optimizer_fn : callable
            Function that takes (backtester, strategy_class, start_date, end_date, benchmark, verbose, param_grid)
            and returns best_parameters, backtest_results
        param_grid : dict
            Dictionary mapping parameter names to lists of possible values
        train_years : float
            Length of the training window in years
        test_years : float
            Length of the testing window in years
        start_date : datetime, optional
            Start date for the entire backtest period
        end_date : datetime, optional
            End date for the entire backtest period
        benchmark : str, optional
            Benchmark symbol
        anchored : bool
            If True, use anchored walk-forward testing where the training window always starts
            at the beginning of data and grows with each step. If False, use a sliding window
            of fixed length for training.
        verbose : bool
            Whether to display progress information
        n_jobs : int
            Number of parallel jobs to run. If -1, use all available processors.
            If 1, don't use parallel processing.

        Returns
        -------
        dict
            Optimization results with clear train and test period data
        """
        # Convert years to days (approximate)
        train_days = int(train_years * 252)  # ~252 trading days per year
        test_days = int(test_years * 252)

        # Create date range of business days
        date_range = pd.date_range(start_date, end_date, freq='B')
        total_days = len(date_range)

        # Check if we have enough data
        if total_days < train_days + test_days:
            raise ValueError(
                f"Not enough data for walk-forward analysis. Need at least {train_days + test_days} days, but only have {total_days} days.")

        # Determine the number of walk-forward steps
        max_steps = (total_days - train_days) // test_days + 1

        if verbose:
            print(f"Walk-Forward Optimization Settings:")
            print(f"  Period: {start_date} to {end_date} ({total_days} trading days)")
            print(
                f"  {'Anchored' if anchored else 'Sliding'} windows with {train_years} year training and {test_years} year testing")
            print(f"  Total steps: {max_steps}")
            print(f"  Parallel jobs: {n_jobs}")

        # Prepare window parameters for all steps
        windows = []
        for step in range(max_steps):
            # Calculate current window indices
            if anchored:
                # Anchored approach: training always starts at the beginning
                train_start_idx = 0
            else:
                # Sliding window approach: move the training window forward
                train_start_idx = step * test_days

            # If we've reached the end of the data, break
            if train_start_idx >= total_days:
                break

            # Calculate end of training window
            if anchored:
                # For anchored approach, the training window grows with each step
                train_end_idx = train_days + step * test_days - 1
            else:
                # For sliding window, training window is fixed length
                train_end_idx = train_start_idx + train_days - 1

            # Adjust train_end_idx if it would exceed the available data
            train_end_idx = min(train_end_idx, total_days - 1)

            test_start_idx = train_end_idx + 1
            # If we've reached the end of the data, break
            if test_start_idx >= total_days:
                break

            test_end_idx = test_start_idx + test_days - 1
            # Adjust test_end_idx if it would exceed the available data
            test_end_idx = min(test_end_idx, total_days - 1)

            # Get the actual dates
            train_start_date = date_range[train_start_idx]
            train_end_date = date_range[train_end_idx]
            test_start_date = date_range[test_start_idx]
            test_end_date = date_range[test_end_idx]

            # Store window parameters
            windows.append((step, train_start_date, train_end_date, test_start_date, test_end_date))

        # Process all windows
        train_periods = {}
        test_periods = {}

        if n_jobs == 1:
            # Serial processing
            if verbose:
                iter_windows = tqdm(windows, desc="Walk-Forward Steps")
            else:
                iter_windows = windows

            for step, train_start_date, train_end_date, test_start_date, test_end_date in iter_windows:
                train_key, train_data, test_key, test_data = self._process_window(
                    step, train_start_date, train_end_date, test_start_date, test_end_date,
                    optimizer_fn, param_grid, benchmark, verbose
                )
                train_periods[train_key] = train_data
                test_periods[test_key] = test_data
        else:
            # Parallel processing with joblib
            if verbose:
                print(f"Using parallel processing with {n_jobs} jobs")

            # Suppress joblib warnings about nested parallelism
            warnings.filterwarnings("ignore",
                                    message="Loky-backed parallel loops cannot be called in a multiprocessing, setting n_jobs=1")

            # Use joblib_progress to show progress bar
            progress_message = f"Processing {len(windows)} walk-forward windows..."
            with joblib_progress(progress_message, total=len(windows)):
                results = Parallel(n_jobs=n_jobs, verbose=0)(
                    delayed(self._process_window)(
                        step, train_start_date, train_end_date, test_start_date, test_end_date,
                        optimizer_fn, param_grid, benchmark, False  # Set verbose to False in parallel to avoid messy output
                    )
                    for step, train_start_date, train_end_date, test_start_date, test_end_date in windows
                )

            # Combine results
            for train_key, train_data, test_key, test_data in results:
                train_periods[train_key] = train_data
                test_periods[test_key] = test_data

        # Store and return results in a clean format that could be used across different optimizers
        results = {
            'train_periods': train_periods,
            'test_periods': test_periods,
            'optimizer_type': 'walk_forward',
            'train_years': train_years,
            'test_years': test_years,
            'anchored': anchored,
            'start_date': start_date,
            'end_date': end_date,
            'param_grid': param_grid
        }

        return results