"""
Sentinel‑aware Learning‑to‑Rank gap‑short framework **v3**
─────────────────────────────────────────────────────────
▷ Adds a **UniverseSelector** with disk caching and optional inactive
  tickers.
▷ Expands the feature set with `log_mcap` plus previous‑day / rolling
  statistics (gap & after‑hours metrics).
▷ Keeps a pluggable registry so new features can be injected with one
  line.

<PERSON><PERSON><PERSON>
───────
 • UniverseSelector  – builds the small‑cap ticker list with caching
 • GapExtractor      – >50 %‑ATR gap detector (unchanged logic)
 • FeatureExtractor  – snapshot‑level feature builder (now extensible)
 • LTRTrainer / LTRRanker – as before
 • StrategyRunner    – rolling 5y train / 1y test orchestrator

Replace the `DataBackend` stub with your concrete implementation.
"""

from __future__ import annotations

import abc
import datetime as dt
from pathlib import Path
from typing import List, Dict, Tuple, Callable, Any
import math

import numpy as np
import pandas as pd
import lightgbm as lgb
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

logger = logging.getLogger(__name__)

###############################################################################
# 0 Helpers
###############################################################################

def cumprod_equity(daily_ret: pd.Series) -> pd.Series:
    return (1 + daily_ret).cumprod()

def max_drawdown(equity: pd.Series) -> float:
    peak = equity.expanding().max()
    dd = (equity - peak) / peak
    return dd.min()

TIME_BUCKETS = [
    dt.time(9, 30), dt.time(10, 0), dt.time(10, 30), dt.time(11, 0),
    dt.time(11, 30), dt.time(12, 0), dt.time(12, 30), dt.time(13, 0),
    dt.time(13, 30), dt.time(14, 0), dt.time(14, 30), dt.time(15, 0),
    dt.time(15, 30),
]


###############################################################################
# 2 Data abstraction
###############################################################################

class DataBackend(abc.ABC):
    @abc.abstractmethod
    def get_daily_bars(self, ticker: str, start: dt.datetime, end: dt.datetime) -> pd.DataFrame: ...

    @abc.abstractmethod
    def get_bucket_bars(self, ticker: str, day: dt.date) -> pd.DataFrame: ...

###############################################################################
# 3 GapExtractor (same logic, now also returns log_mcap)
###############################################################################

import pandas as pd
import numpy as np
import datetime as dt

class GapExtractor:
    def __init__(
        self,
        backend: DataBackend,
        ticker_info_store,
        atr_window: int = 14,
        gap_atr_thresh: float = 5,
        min_mcap: float = None,
        max_mcap: float = None,
    ):
        self.backend = backend
        self.tinfo = ticker_info_store
        self.atr_window = atr_window
        self.gap_atr_thresh = gap_atr_thresh
        self.min_mcap = min_mcap
        self.max_mcap = max_mcap

    def _add_atr(self, df: pd.DataFrame) -> pd.DataFrame:
        high_low = df['high'] - df['low']
        high_prev_close = (df['high'] - df['close'].shift()).abs()
        low_prev_close = (df['low'] - df['close'].shift()).abs()

        tr = pd.Series(
            np.maximum.reduce([
                high_low.values,
                high_prev_close.values,
                low_prev_close.values
            ]),
            index=df.index,
            name='tr'
        )

        df['atr'] = tr.rolling(window=self.atr_window).mean()
        return df

    def extract(
        self,
        ticker: str,
        start: dt.datetime,
        end: dt.datetime,
    ) -> pd.DataFrame:
        # 0) grab ticker details (optional)
        details = self.tinfo.get_ticker_details(ticker, start.date())
        info_missing = False
        use_details_mc = False
        if details is None:
            info_missing = True
            wso = mc_details = np.nan
        else:
            wso = details.get('weighted_shares_outstanding', np.nan)
            mc_details = details.get('market_cap', np.nan)
            # prefer market_cap if no valid weighted shares
            if pd.isna(wso) or wso <= 0:
                if not pd.isna(mc_details) and mc_details > 0:
                    print(f"No weighted_shares_outstanding for {ticker} on {start.date()}, using market_cap from details")
                    use_details_mc = True
                else:
                    info_missing = True
        # 1) load bars & compute ATR
        ddf = self.backend.get_daily_bars(ticker, start, end)
        if ddf is None or ddf.empty:
            return pd.DataFrame()
        ddf = ddf.copy()
        ddf = self._add_atr(ddf)

        # 2) compute gap in ATR units, enforce only gap-ups
        prev_close = ddf['close'].shift()
        raw_gap = ddf['open'] / prev_close - 1
        gap_atr = raw_gap / ddf['atr']
        
        up_mask = ddf['open'] > prev_close
        thresh_mask = gap_atr > self.gap_atr_thresh
        mask = up_mask & thresh_mask
        
        out = ddf.loc[mask, ['open', 'close', 'atr']].copy()
        if out.empty:
            return pd.DataFrame()

        # 3) rename & attach gap_atr
        out.rename(columns={'open': 'open_0400', 'close': 'close_prev'}, inplace=True)
        out['gap_atr'] = gap_atr.loc[mask]

        # 4) compute market cap (skip if info missing)
        if info_missing:
            out['weighted_shares_outstanding'] = np.nan
            out['market_cap'] = np.nan
        elif use_details_mc:
            out['weighted_shares_outstanding'] = np.nan
            out['market_cap'] = mc_details
        else:
            out['weighted_shares_outstanding'] = wso
            out['market_cap'] = wso * out['close_prev']

        # attach total employees and listing date
        if details is None:
            out['total_employees'] = np.nan
            out['list_date'] = pd.NaT
        else:
            out['total_employees'] = details.get('total_employees', np.nan)
            ld = details.get('list_date')
            if ld is not None:
                out['list_date'] = pd.to_datetime(ld)
            else:
                out['list_date'] = pd.NaT

        # 5) apply market cap filters only when we have valid cap info
        if not info_missing:
            if self.min_mcap is not None:
                out = out.loc[out['market_cap'] >= self.min_mcap]
            if self.max_mcap is not None:
                out = out.loc[out['market_cap'] <= self.max_mcap]
            if out.empty:
                return pd.DataFrame()

        # 6) final touches
        out['log_mcap'] = np.log(out['market_cap'] + 1)
        return out


###############################################################################
# 4 FeatureExtractor (extensible registry)
###############################################################################

class FeatureExtractor:
    def __init__(self, backend: DataBackend):
        self.backend = backend
        self.registry: Dict[str, Callable[[pd.DataFrame, pd.Series, dt.time], Any]] = {
            'gap_atr': lambda df, drow, t: drow['gap_atr'],
            'log_mcap': lambda df, drow, t: drow['log_mcap'],
            'mfe_atr': lambda df, drow, t: (df['high'].max() - drow['open_0400']) / drow['atr'],
            'vwap_dist_atr': lambda df, drow, t: (df['close'].iloc[-1] - (df['close'] * df['volume']).cumsum().iloc[-1] / df['volume'].cumsum().iloc[-1]) / drow['atr'],
            'pct_above_vwap': lambda df, drow, t: (df['close'] > (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()).mean(),
            'rel_volume': lambda df, drow, t: df['volume'].sum() / df['volume'].rolling(10).mean().iloc[-1],
            'time_frac': lambda df, drow, t: (df.index[-1].hour * 60 + df.index[-1].minute - 570) / 390,
            'weekday': lambda df, drow, t: df.index[-1].weekday(),
            'total_employees': lambda df, drow, t: drow['total_employees'],
            'listing_age': lambda df, drow, t: (drow.name.date() - drow['list_date'].date()).days if pd.notna(drow['list_date']) else np.nan,
        }

    def add_feature(self, name: str, func: Callable[[pd.DataFrame, pd.Series, dt.time], Any]):
        self.registry[name] = func

    def build_snapshot(self, ticker: str, day: dt.date, t: dt.time, daily_row: pd.Series, df: pd.DataFrame | None = None) -> Dict:
        df = df[df.index.time <= t]
        if df.empty:
            return {}
        return {k: f(df, daily_row, t) for k, f in self.registry.items()}

###############################################################################
# 5 LTRTrainer & LTRRanker (unchanged)
###############################################################################

class LTRTrainer:
    """
    LambdaRank-based trainer that converts each day's *continuous*
    forward-return `label` into *integer* relevance grades:

        grade = 0        → sentinel (“cash” baseline)
        grade = 1 … k    → real tickers split into equal-mass buckets
    """
    def __init__(self,
                 feature_cols: List[str],
                 n_grades: int = 10):        # quintiles by default
        self.feature_cols = feature_cols
        self.n_grades    = n_grades        # TOTAL buckets incl. sentinel
        self.model: lgb.Booster | None = None
        
    def _prep(self, rows):
        df = pd.DataFrame(rows)

        # sentinel rows already have grade 0
        sentinel_mask = df['is_sentinel']

        # ------------------------------------------------------------------
        # 1.  grade the real rows day-by-day
        def _bucket(labels: pd.Series) -> np.ndarray:
            # labels is already the day's label‐series
            r = labels.rank(method='first', ascending=True)
            if len(labels) < self.n_grades:
                g = np.floor((r-1) / len(labels) * (self.n_grades-1) + 1)
            else:
                g = pd.qcut(r,
                            q=self.n_grades-1,
                            labels=False,
                            duplicates='drop') + 1
            return g

        real_mask = ~sentinel_mask
        df.loc[real_mask, 'grade'] = (
            df.loc[real_mask]
            .groupby('day')['label']
            .transform(_bucket)
            .astype('Int8')      
        )

        # ------------------------------------------------------------------
        # 2.  final clean-up / consolidation
        df['grade'] = df['grade'].fillna(0).astype(np.int8)

        groups = df.groupby('day').size().values
        return df, groups


    def fit(self, rows: List[Dict]):
        df, grp = self._prep(rows)
        dtrain  = lgb.Dataset(df[self.feature_cols],
                              label=df['grade'],
                              group=grp)
        self.model = lgb.train(dict(objective='lambdarank', metric='ndcg', learning_rate=0.05,
                                    num_leaves=63, min_data_in_leaf=30, ndcg_eval_at=[10]),
                               dtrain, num_boost_round=300)
        return self

class LTRRanker:
    def __init__(self, model: lgb.Booster, feature_cols: List[str]):
        self.model = model; self.feature_cols = feature_cols

    def tradable_shorts(self, snap: pd.DataFrame) -> pd.DataFrame:
        if 'SENTINEL' not in snap['ticker'].values:
            raise ValueError("Snapshot missing sentinel row")

        scores = self.model.predict(snap[self.feature_cols])
        snap = snap.assign(score=scores)
        sentinel = snap.loc[snap['ticker'] == 'SENTINEL', 'score'].iloc[0]
        return snap[(snap['ticker'] != 'SENTINEL') & (snap['score'] > sentinel)].sort_values('score', ascending=False)

###############################################################################
# 6 StrategyRunner
###############################################################################

class StrategyRunner:
    def __init__(self, backend: DataBackend, ticker_info_store, start_year: int = 2015, end_year: int | None = None):
        self.backend = backend
        self.tinfo = ticker_info_store
        self.start_year = start_year
        self.end_year = end_year or dt.datetime.now().year - 1
        self.fx = FeatureExtractor(backend)
        self.gapx = GapExtractor(backend, ticker_info_store, 
                                min_mcap=1_000_000, # 1 M
                                max_mcap=500_000_000) # 500 M
        self.feature_cols = list(self.fx.registry.keys())

    def _collect(self, tickers: List[str], start: dt.datetime, end: dt.datetime) -> List[Dict]:
        rows: List[Dict] = []
        print(f"Collecting rows for {len(tickers)} tickers from {start} to {end}")
        with ThreadPoolExecutor() as executor:
            # 1) parallel gap extraction per ticker
            future_gaps = {executor.submit(self.gapx.extract, tkr, start, end): tkr for tkr in tickers}
            gaps_map: Dict[str, pd.DataFrame] = {}
            for future in tqdm(as_completed(future_gaps),
                               total=len(future_gaps),
                               desc="Extracting gaps"):
                tkr = future_gaps[future]
                try:
                    gaps_map[tkr] = future.result()
                except Exception as e:
                    print(f"Error extracting gaps for {tkr}: {e}")

            # 2) build list of all (ticker, day) pairs
            ticker_day_pairs = [
                (tkr, day) for tkr, gaps in gaps_map.items() for day, _ in gaps.iterrows()
            ]
            
            # 3) parallel get_bucket_bars per ticker-day
            future_bars = {
                executor.submit(self.backend.get_bucket_bars, tkr, day.date()): (tkr, day)
                for tkr, day in ticker_day_pairs
            }
            
            bars_map: Dict[Tuple[str, pd.Timestamp], pd.DataFrame] = {}
            for future in tqdm(as_completed(future_bars),
                               total=len(future_bars),
                               desc="Fetching bucket bars"):
                tkr, day = future_bars[future]
                try:
                    bars_map[(tkr, day)] = future.result()
                except Exception as e:
                    print(f"Error fetching bucket bars for {tkr} on {day}: {e}")
                    
            print(f"Ticker day pairs: {len(ticker_day_pairs)}")

            # 4) assemble rows using pre-fetched gaps and bucket bars
            for tkr, gaps in tqdm(gaps_map.items(),
                                   total=len(gaps_map),
                                   desc="Assembling rows"):
                for day, drow in gaps.iterrows():
                    bb = bars_map.get((tkr, day))
                    for j, tb in enumerate(TIME_BUCKETS):
                        feat = self.fx.build_snapshot(tkr, day.date(), tb, drow, bb)
                        if not feat or bb is None:
                            continue
                        try:
                            px_t = bb[bb.index.time == tb]['close'].iloc[-1]
                            px_c = bb.between_time('16:00', '16:00')['close'].iloc[0]
                        except IndexError:
                            # print(f"Should be rare: IndexError for {tkr} on {day} at {tb}")
                            continue
                        rows.append({
                            **feat,
                            'ticker': tkr,
                            'day': day,
                            't_bucket': j,
                            'label': -(px_c / px_t - 1),
                            'is_sentinel': False,
                        })
                    rows.append({
                        **{c: 0 for c in self.feature_cols},
                        'ticker': 'SENTINEL',
                        'day': day,
                        't_bucket': -1,
                        'label': 0,
                        'is_sentinel': True, 
                    })
                    
        print(f"Total rows: {len(rows)}")
        print(f"Rows {rows[:10]}")
        return rows

    def run(self, universe: List[str]) -> Tuple[pd.Series, pd.DataFrame, Dict]:
        equity_curve = pd.Series(dtype=float)
        trade_log = []
        
        def clamp(x, lo, hi):
            """Keep x between lo and hi (inclusive)."""
            return max(lo, min(x, hi))

        pct_to_trade = 0.1 # Top top 10%, unless we have too few names
        min_names    = 1
        max_names    = 5

        for year in range(self.start_year, self.end_year + 1):
            train_start, train_end = dt.datetime(year - 5, 1, 1), dt.datetime(year - 1, 12, 31)
            test_start, test_end   = dt.datetime(year, 1, 1), dt.datetime(year, 12, 31)
            trainer = LTRTrainer(self.feature_cols, n_grades=10) \
                        .fit(self._collect(universe, train_start, train_end))
            test_rows = self._collect(universe, test_start, test_end)
            test_df, _ = trainer._prep(test_rows)
            ranker = LTRRanker(trainer.model, self.feature_cols)
            
            for day, grp in test_df.groupby('day'):
                shorts = ranker.tradable_shorts(grp)
                if shorts.empty:
                    continue
                
                desired = math.ceil(len(shorts) * pct_to_trade)
                k = clamp(desired, min_names, max_names)
                
                leg = shorts.head(k)
                daily_ret = -leg['label'].mean()  # remember label = -fwd_ret
                equity_curve.loc[day] = daily_ret
                trade_log.append({'day': day, 'year': year, 'ret': daily_ret, 'n': len(leg)})
        eq = cumprod_equity(equity_curve.sort_index())
        stats = {
            'CAGR': eq.iloc[-1] ** (252 / len(eq)) - 1,
            'Sharpe': equity_curve.mean() * np.sqrt(252) / equity_curve.std(),
            'MaxDD': max_drawdown(eq),
        }
        return eq, pd.DataFrame(trade_log), stats