{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Install dependencies\n", "# !uv pip install pandas numpy lightgbm scikit-learn ta tqdm\n", "# !uv pip install lightgbm\n", "\n", "import pandas as pd\n", "\n", "START_DT = pd.Timestamp(\"2018-01-01\", tz=\"US/Eastern\")\n", "END_DT   = pd.Timestamp(\"2025-01-01\", tz=\"US/Eastern\")\n", "\n", "DISK_DATA_DATE = pd.Timestamp(\"2015-01-01\", tz=\"US/Eastern\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_adjusted(False)\n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        # Already unadjusted since its disk data\n", "                        .with_disk_data(start_date=DISK_DATA_DATE.replace(tzinfo=None) - pd.Timedelta(days=30))\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"Fetches daily data using the configured daily_market_data instance.\"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400\n", "        )\n", "    except Exception as e:\n", "        # print(\"Exception\", e)\n", "        return None\n", "\n", "def get_per_minute_data(ticker, start_dt, end_dt) -> pd.DataFrame:\n", "    \"\"\"\n", "        Output is a pandas dataframe\n", "        \n", "        DatetimeIndex with timezone ( 2010-02-11 09:43:00-05:00 to 2025-04-22 19:59:00-04:00)\n", "        Data columns (total 6 columns):\n", "        #   Column  Dtype  \n", "        ---  ------  -----  \n", "        0   open    float64\n", "        1   high    float64\n", "        2   low     float64\n", "        3   close   float64\n", "        4   volume  float64\n", "        5   date    object \n", "        dtypes: float64(5), object(1)\n", "\n", "    \"\"\"\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data\n", "\n", "def get_hourly_data(ticker, start_dt, end_dt) -> pd.DataFrame:\n", "    \"\"\"\n", "        Output is a pandas dataframe\n", "        \n", "        DatetimeIndex with timezone ( 2010-02-11 09:43:00-05:00 to 2025-04-22 19:59:00-04:00)\n", "        Data columns (total 6 columns):\n", "        #   Column  Dtype  \n", "        ---  ------  -----  \n", "        0   open    float64\n", "        1   high    float64\n", "        2   low     float64\n", "        3   close   float64\n", "        4   volume  float64\n", "        5   date    object \n", "        dtypes: float64(5), object(1)\n", "\n", "    \"\"\"\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=3600\n", "    )\n", "    return data\n", "\n", "def get_30_minute_data(ticker, start_dt, end_dt) -> pd.DataFrame:\n", "    \"\"\"\n", "        Output is a pandas dataframe\n", "        \n", "        DatetimeIndex with timezone ( 2010-02-11 09:43:00-05:00 to 2025-04-22 19:59:00-04:00)\n", "        Data columns (total 6 columns):\n", "        #   Column  Dtype  \n", "        ---  ------  -----  \n", "        0   open    float64\n", "        1   high    float64\n", "        2   low     float64\n", "        3   close   float64\n", "        4   volume  float64\n", "        5   date    object \n", "        dtypes: float64(5), object(1)\n", "\n", "    \"\"\"\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=1800\n", "    )\n", "    if not isinstance(data.index, pd.DatetimeIndex):\n", "        data.index = pd.to_datetime(data.index)\n", "    return data\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from sandbox.ltr_sentinel import DataBackend\n", "import datetime as dt\n", "\n", "class MarketDataBackend(DataBackend):\n", "    def get_daily_bars(self, ticker: str, start: dt.datetime, end: dt.datetime) -> pd.DataFrame:\n", "        return get_daily_data(ticker, start, end)\n", "\n", "    def get_bucket_bars(self, ticker: str, day: dt.date) -> pd.DataFrame:\n", "        start = dt.datetime.combine(day, dt.time(0, 0))\n", "        end   = dt.datetime.combine(day, dt.time(23, 59))\n", "        return get_30_minute_data(ticker, start, end)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sandbox.ltr_sentinel import StrategyRunner\n", "from tickers.ticker_helpers import TickerInfoStore\n", "from universe.stock_universe_selector import StockUniverseSelector\n", "\n", "ticker_info_store = TickerInfoStore()\n", "universe_selector = StockUniverseSelector(daily_market_data, ticker_info_store)\n", "universe_df = universe_selector.select(START_DT, END_DT)\n", "\n", "backend = MarketDataBackend()\n", "runner = StrategyRunner(backend, ticker_info_store, start_year=2023, end_year=2025)\n", "equity, log_df, stats = runner.run(universe_df['ticker'].tolist())\n", "equity.plot(title=\"Sentinel‑LTR equity curve\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# a = runner.gapx.extract('ELY', START_DT, END_DT)\n", "# print(a)\n", "\n", "# tickers = universe_df['ticker'].tolist()\n", "# valid = []\n", "# for tkr in tickers:\n", "#     if ticker_info_store.get_ticker_details(tkr, dt.date(2015,1,1)) is not None:\n", "#         valid.append(tkr)\n", "# print(f\"{len(valid)}/{len(tickers)} tickers have metadata on 2015-01-01\")\n", "\n", "df = backend.get_daily_bars(\"AAPL\", datetime(2005,1,1), datetime(2019,12,31))\n", "print(df.head(), df.tail())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}