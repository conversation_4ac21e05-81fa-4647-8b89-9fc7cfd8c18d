{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["import os, logging, dotenv, pytz\n", "from datetime import datetime, timedelta\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt\n", "from pandas.tseries.offsets import BDay, MonthEnd\n", "\n", "# ---------- logging ----------\n", "logging.basicConfig(level=logging.INFO,\n", "                    format=\"%(asctime)s  %(levelname)s  %(message)s\")\n", "\n", "# ---------- timezone & user config ----------\n", "EST             = pytz.timezone(\"America/New_York\")\n", "START_DT        = EST.localize(datetime(2020, 1, 1))\n", "END_DT          = EST.localize(datetime(2025, 1, 1))\n", "\n", "N_QUANTILES     = 10         # deciles\n", "GROSS_LEVERAGE  = 1.0        # 100 % long + 100 % short\n", "TC_BPS_PER_SIDE = 0          # per *rebalance*\n", "DEBUG           = True"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# ---------- project paths / env ----------\n", "os.chdir(os.path.expanduser(\"~/w/backtest\"))\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "# ------------------------------------------------\n", "# 1) Market-data helpers\n", "# ------------------------------------------------\n", "from marketdata import MarketDataBuilder\n", "from universe.stock_universe_selector import StockUniverseSelector\n", "from tickers.ticker_helpers import TickerInfoStore\n", "\n", "daily_md = (MarketDataBuilder()\n", "            .with_period(\"daily\")\n", "            .with_disk_data(start_date=START_DT - timedelta(days=500))\n", "            .build_market_data())\n", "\n", "selector    = StockUniverseSelector(daily_md, TickerInfoStore())\n", "universe_df = selector.select_by_price_and_volume(\n", "                  window_start=START_DT, window_end=END_DT,\n", "                  min_price=1.0, min_dollar_volume=1e7)\n", "\n", "# {date → Index[tickers]}\n", "universe_by_date = {\n", "    ts.date(): grp.index.get_level_values(\"ticker\").unique()\n", "    for ts, grp in universe_df.groupby(level=\"date\")\n", "}\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# ------------------------------------------------\n", "# 2) <PERSON><PERSON>-close helper (forces EST tz)\n", "# ------------------------------------------------\n", "# ================================================================\n", "# Revised fetch_daily_close: drop duplicated timestamps BEFORE tz handling\n", "# ================================================================\n", "def fetch_daily_close(sym, md, start, end):\n", "    \"\"\"\n", "    Return a pandas Series (name=sym) of close prices,\n", "    with an EST-aware, UNIQUE-DATETIME DatetimeIndex.\n", "    If the fetched DataFrame index isn’t a proper DatetimeIndex,\n", "    or if there are duplicates, we coerce/cast and then drop duplicates.\n", "    \"\"\"\n", "    df = md.gather_historical_data(sym, start, end)\n", "\n", "    # 1) Always coerce the index to datetime first\n", "    df.index = pd.to_datetime(df.index, errors=\"coerce\")\n", "\n", "    # 2) If, for some reason, df.index is not a true DatetimeIndex, cast it\n", "    if not isinstance(df.index, pd.DatetimeIndex):\n", "        df.index = pd.DatetimeIndex(df.index)\n", "\n", "    # 3) Sort the index and drop any exact-duplicate timestamps\n", "    df = df.sort_index()\n", "    df = df[~df.index.duplicated(keep=\"first\")]\n", "\n", "    # 4) Extract the 'close' column as a Series\n", "    s = df[\"close\"].rename(sym)\n", "\n", "    # 5) Now handle tz: if tz-naive, localize; if tz-aware, convert\n", "    tzinfo = getattr(s.index, \"tz\", None)\n", "    if tzinfo is None:\n", "        s.index = s.index.tz_localize(EST)\n", "    else:\n", "        s.index = s.index.tz_convert(EST)\n", "\n", "    return s\n", "\n", "\n", "# ------------------------------------------------\n", "# 3) Monthly weekday signal\n", "# ------------------------------------------------\n", "def monthly_weekday_signal(ticks, md, m_end, lookback=11):\n", "    \"\"\"Return DataFrame index=ticker, cols=['mon'…'fri'] (EST).\"\"\"\n", "    lo = (m_end - MonthEnd(lookback + 1)) + MonthEnd(1)\n", "    hi =  (m_end - MonthEnd(1))\n", "\n", "    price_frames = []\n", "    for sym in ticks:\n", "        try:\n", "            s = fetch_daily_close(sym, md, lo, hi)\n", "            price_frames.append(s)\n", "        except Exception as e:\n", "            if DEBUG:\n", "                logging.warning(f\"{sym}: fetch {lo.date()}–{hi.date()} failed → {e}\")\n", "\n", "    if not price_frames:\n", "        return pd.DataFrame(columns=list(\"mon tue wed thu fri\".split()))\n", "\n", "    px   = pd.concat(price_frames, axis=1).sort_index()\n", "    rets = np.log(px / px.shift(1)).dropna(how=\"all\")\n", "\n", "    by_wk = {d: rets[rets.index.weekday == d].mean()\n", "             for d in range(5)}\n", "    sig   = pd.concat(by_wk, axis=1)\n", "    sig.columns = [\"mon\", \"tue\", \"wed\", \"thu\", \"fri\"]\n", "    return sig\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:47:54,239  INFO  2020-12: signal on 2539 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:47:57,131  INFO  2021-01: signal on 3123 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:00,848  INFO  2021-02: signal on 3313 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:03,751  INFO  2021-03: signal on 3144 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:06,384  INFO  2021-04: signal on 2856 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:06,385  INFO  2021-05: empty universe – skip\n", "2025-05-31 21:48:09,175  INFO  2021-06: signal on 3028 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:11,653  INFO  2021-07: signal on 2700 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:14,980  INFO  2021-08: signal on 2856 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:17,664  INFO  2021-09: signal on 2915 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:20,299  INFO  2021-10: signal on 2882 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:23,373  INFO  2021-11: signal on 3301 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:25,776  INFO  2021-12: signal on 2603 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:28,590  INFO  2022-01: signal on 3013 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:32,138  INFO  2022-02: signal on 3069 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:34,860  INFO  2022-03: signal on 2962 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:37,505  INFO  2022-04: signal on 2851 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:40,324  INFO  2022-05: signal on 3068 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:42,909  INFO  2022-06: signal on 2805 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:45,389  INFO  2022-07: signal on 2701 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:48,541  INFO  2022-08: signal on 2648 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:51,110  INFO  2022-09: signal on 2797 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:53,621  INFO  2022-10: signal on 2701 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:56,292  INFO  2022-11: signal on 2918 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:48:58,554  INFO  2022-12: signal on 2478 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:01,115  INFO  2023-01: signal on 2804 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:03,652  INFO  2023-02: signal on 2777 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:06,842  INFO  2023-03: signal on 2785 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:09,143  INFO  2023-04: signal on 2517 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:11,708  INFO  2023-05: signal on 2791 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:14,113  INFO  2023-06: signal on 2625 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:16,473  INFO  2023-07: signal on 2579 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:18,760  INFO  2023-08: signal on 2519 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:21,105  INFO  2023-09: signal on 2626 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:24,049  INFO  2023-10: signal on 2503 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:26,501  INFO  2023-11: signal on 2683 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:28,895  INFO  2023-12: signal on 2614 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:31,398  INFO  2024-01: signal on 2791 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:34,035  INFO  2024-02: signal on 2895 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:34,036  INFO  2024-03: empty universe – skip\n", "2025-05-31 21:49:36,526  INFO  2024-04: signal on 2720 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:39,853  INFO  2024-05: signal on 2847 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:42,987  INFO  2024-06: signal on 3472 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:45,612  INFO  2024-07: signal on 2949 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:48,017  INFO  2024-08: signal on 2666 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:50,574  INFO  2024-09: signal on 2849 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:53,114  INFO  2024-10: signal on 2842 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:56,012  INFO  2024-11: signal on 2381 stocks\n", "/tmp/ipykernel_3842643/2528047015.py:8: UserWarning: Converting to Period representation will drop timezone information.\n", "  period  = m_end.to_period(\"M\")\n", "2025-05-31 21:49:58,693  INFO  2024-12: signal on 2938 stocks\n"]}], "source": ["# ------------------------------------------------\n", "# 4) Pre-compute monthly signals\n", "# ------------------------------------------------\n", "all_bm_ends = pd.date_range(START_DT, END_DT, freq=\"BME\").tz_convert(EST)\n", "\n", "signals_by_month = {}\n", "for m_end in all_bm_ends[11:]:            # need 11-month look-back\n", "    period  = m_end.to_period(\"M\")\n", "    tickers = universe_by_date.get(m_end.date(), pd.Index([]))\n", "    if tickers.empty:\n", "        if DEBUG:\n", "            logging.info(f\"{period}: empty universe – skip\")\n", "        continue\n", "\n", "    sig = monthly_weekday_signal(tickers, daily_md, m_end)\n", "    if sig.empty:\n", "        if DEBUG:\n", "            logging.info(f\"{period}: empty signal – skip\")\n", "        continue\n", "\n", "    signals_by_month[period] = sig\n", "    if DEBUG:\n", "        logging.info(f\"{period}: signal on {len(sig)} stocks\")\n", "\n", "# ------------------------------------------------\n", "# 5) Build master daily-return table (EST-aware)\n", "# ------------------------------------------------\n", "all_syms = sorted({t for lst in universe_by_date.values() for t in lst})\n", "\n", "price_frames = []\n", "for sym in all_syms:\n", "    try:\n", "        px = fetch_daily_close(sym, daily_md,\n", "                               START_DT - <PERSON><PERSON><PERSON>(days=400), END_DT)\n", "        price_frames.append(px)\n", "    except Exception as e:\n", "        if DEBUG:\n", "            logging.warning(f\"{sym}: {e}\")\n", "\n", "px_all   = pd.concat(price_frames, axis=1).sort_index()\n", "rets_all = np.log(px_all / px_all.shift(1)).dropna(how=\"all\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3842643/1405630513.py:9: UserWarning: Converting to Period representation will drop timezone information.\n", "  period = current_day.to_period(\"M\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                                pnl   cum_ret\n", "date                                         \n", "2024-12-24 00:00:00-05:00 -0.005393  1.249696\n", "2024-12-26 00:00:00-05:00 -0.002776  1.246227\n", "2024-12-27 00:00:00-05:00 -0.001025  1.244949\n", "2024-12-30 00:00:00-05:00 -0.008829  1.233957\n", "2024-12-31 00:00:00-05:00  0.003858  1.238718\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# ------------------------------------------------\n", "# 6) Daily back-test loop\n", "# ------------------------------------------------\n", "records     = []\n", "current_sig = None\n", "last_period = None\n", "\n", "for current_day, rets_today in rets_all.loc[START_DT:END_DT].iterrows():\n", "    period = current_day.to_period(\"M\")\n", "\n", "    # refresh positions once/month\n", "    if period != last_period:\n", "        current_sig = signals_by_month.get(period - 1) # Previous month's returns\n", "        apply_tc    = True\n", "        last_period = period\n", "    else:\n", "        apply_tc = False\n", "\n", "    if current_sig is None or current_sig.empty:\n", "        continue\n", "\n", "    wk  = current_day.weekday()                     # 0–4\n", "    col = [\"mon\",\"tue\",\"wed\",\"thu\",\"fri\"][wk]\n", "\n", "    ranked = current_sig[col].dropna().sort_values()\n", "    qsize  = len(ranked) // N_QUANTILES\n", "    if qsize == 0:\n", "        continue\n", "\n", "    longs,  shorts = ranked.iloc[-qsize:].index, ranked.iloc[:qsize].index\n", "    \n", "    long_mask  = rets_today.loc[longs].notna()\n", "    short_mask = rets_today.loc[shorts].notna()\n", "    if long_mask.sum() == 0 or short_mask.sum() == 0:\n", "        continue\n", "\n", "    w_long  = +(GROSS_LEVERAGE/2) / long_mask.sum()\n", "    w_short = -(GROSS_LEVERAGE/2) / short_mask.sum()\n", "\n", "    pnl = (\n", "        w_long  * rets_today.loc[longs][long_mask].sum() +\n", "        w_short * rets_today.loc[shorts][short_mask].sum()\n", "    )\n", "    if apply_tc:\n", "        pnl -= 2 * GROSS_LEVERAGE * (TC_BPS_PER_SIDE / 1e4)\n", "\n", "    records.append({\"date\": current_day, \"pnl\": pnl})\n", "\n", "# ------------------------------------------------\n", "# 7) Results\n", "# ------------------------------------------------\n", "results = pd.DataFrame(records).set_index(\"date\")\n", "results[\"cum_ret\"] = (1 + results[\"pnl\"]).cumprod()\n", "\n", "print(results.tail())\n", "results[\"cum_ret\"].plot(title=\"Same-Weekday Momentum (gross, cost-adjusted)\",\n", "                        linewidth=2)\n", "plt.ylabel(\"Growth of $1\")\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}