import numpy as np
import pandas as pd
from portwine.strategies.base import StrategyBase

class CooldownLowVolBondStrategy(StrategyBase):
    """
    A short‑term momentum + SMA‑filtered strategy over a small universe of bond funds,
    with a 30‑day cooldown after selling any fund.
    """

    def __init__(self,
                 tickers,
                 momentum_lookback_days=15,
                 sma_filter_days=10,
                 cash_ticker=None,
                 cooldown_days=30):
        """
        Parameters
        ----------
        tickers : list of str
            The universe of bond funds.
        momentum_lookback_days : int
            Short‑term momentum lookback.
        sma_filter_days : int
            SMA filter lookback.
        cash_ticker : str or None
            If specified, allocate to this ticker when top pick fails.
        cooldown_days : int
            Number of days after a sale before a fund is eligible again.
        """
        super().__init__(tickers)
        self.momentum_lookback = momentum_lookback_days
        self.sma_filter = sma_filter_days
        self.cash_ticker = cash_ticker
        self.cooldown_days = cooldown_days

        # Rolling price history for each ticker
        self.price_history = {t: [] for t in tickers}
        # Current weights
        self.current_weights = {t: 0.0 for t in tickers}
        # Cooldown expiry dates: ticker -> pd.Timestamp
        self.cooldowns = {}

    def step(self, current_date, daily_data):
        # 1) Expire any cooldowns
        for t, expiry in list(self.cooldowns.items()):
            if current_date >= expiry:
                del self.cooldowns[t]

        # 2) Update price history
        lookback = max(self.momentum_lookback, self.sma_filter) + 1
        for t in self.tickers:
            close = (daily_data.get(t) or {}).get('close', np.nan)
            self.price_history[t].append(close)
            if len(self.price_history[t]) > lookback:
                self.price_history[t].pop(0)

        # 3) Compute momentum returns for eligible tickers
        returns = {}
        eligible = [t for t in self.tickers if t not in self.cooldowns]
        for t in self.tickers:
            if t not in eligible:
                returns[t] = -np.inf
                continue
            prices = self.price_history[t]
            if len(prices) < self.momentum_lookback + 1 or any(np.isnan(prices[-(self.momentum_lookback+1):])):
                returns[t] = -np.inf
                continue
            window = prices[-(self.momentum_lookback+1):]
            returns[t] = window[-1] / window[0] - 1.0

        # 4) Pick the best ticker
        best = max(returns, key=returns.get)
        if returns[best] == -np.inf:
            # no valid pick => flat or cash
            prev_weights = self.current_weights.copy()
            for t in self.tickers:
                self.current_weights[t] = 0.0
            if self.cash_ticker in self.tickers:
                self.current_weights[self.cash_ticker] = 1.0
        else:
            # 5) SMA filter on the top pick
            hist = self.price_history[best]
            if len(hist) < self.sma_filter + 1 or any(np.isnan(hist[-self.sma_filter:])):
                passes = False
            else:
                recent = hist[-self.sma_filter:]
                passes = recent[-1] > np.mean(recent)

            prev_weights = self.current_weights.copy()
            for t in self.tickers:
                self.current_weights[t] = 0.0
            if passes:
                self.current_weights[best] = 1.0
            elif self.cash_ticker in self.tickers:
                self.current_weights[self.cash_ticker] = 1.0

        # 6) Set cooldown for any ticker we just sold
        for t in self.tickers:
            if prev_weights.get(t, 0.0) > 0.0 and self.current_weights.get(t, 0.0) == 0.0:
                self.cooldowns[t] = current_date + pd.Timedelta(days=self.cooldown_days)

        return dict(self.current_weights)