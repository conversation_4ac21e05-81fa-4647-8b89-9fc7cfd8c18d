import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from matplotlib.gridspec import GridSpec

class EnhancedEquityAnalyzer:
    """
    Enhanced analyzer with comprehensive trading metrics and tearsheet visualization.
    Builds on EquityDrawdownAnalyzer with additional metrics and visualization.
    """

    def compute_drawdown(self, equity_series):
        """
        Computes percentage drawdown for a given equity curve.

        Parameters
        ----------
        equity_series : pd.Series
            The cumulative equity values over time (e.g., starting at 1.0).

        Returns
        -------
        drawdown : pd.Series
            The percentage drawdown at each point in time.
        """
        rolling_max = equity_series.cummax()
        drawdown = (equity_series - rolling_max) / rolling_max
        return drawdown
    
    def compute_drawdown_stats(self, equity_series):
        """
        Computes detailed drawdown statistics.
        
        Parameters
        ----------
        equity_series : pd.Series
            The cumulative equity values over time.
            
        Returns
        -------
        dict
            Dictionary containing drawdown metrics.
        """
        drawdown = self.compute_drawdown(equity_series)
        
        # Identify drawdown periods - create purely boolean series to avoid warnings
        is_drawdown = drawdown < 0
        
        # Create shifted series without using fillna or replace
        shifted = pd.Series(False, index=is_drawdown.index)
        if len(is_drawdown) > 1:
            shifted.iloc[1:] = is_drawdown.iloc[:-1].values
        
        # Now use these boolean Series for identifying start and end points
        drawdown_start = is_drawdown & (~shifted)
        drawdown_end = (~is_drawdown) & shifted
        
        # Ensure we have matching starts and ends
        if is_drawdown.iloc[-1]:
            # If we're in a drawdown at the end, add an artificial end point
            drawdown_end.iloc[-1] = True
            
        start_dates = drawdown_start[drawdown_start].index
        end_dates = drawdown_end[drawdown_end].index
        
        # Ensure we have equal number of starts and ends
        min_length = min(len(start_dates), len(end_dates))
        start_dates = start_dates[:min_length]
        end_dates = end_dates[:min_length]
        
        drawdown_periods = []
        for start, end in zip(start_dates, end_dates):
            period_dd = drawdown.loc[start:end]
            max_dd = period_dd.min()
            duration = (end - start).days
            drawdown_periods.append({
                'start_date': start,
                'end_date': end,
                'max_drawdown': max_dd,
                'duration_days': duration
            })
        
        if not drawdown_periods:
            return {
                'max_drawdown': 0.0,
                'avg_drawdown': 0.0,
                'max_duration': 0,
                'avg_duration': 0,
                'recovery_rate': 0.0,
                'drawdown_periods': []
            }
            
        # Calculate summary statistics
        max_dd = min(p['max_drawdown'] for p in drawdown_periods)
        avg_dd = np.mean([p['max_drawdown'] for p in drawdown_periods])
        max_duration = max(p['duration_days'] for p in drawdown_periods)
        avg_duration = np.mean([p['duration_days'] for p in drawdown_periods])
        
        # Calculate recovery rate (average % recovery per day)
        recovery_rates = []
        for p in drawdown_periods:
            if p['duration_days'] > 0 and p['max_drawdown'] < 0:
                recovery_rates.append(abs(p['max_drawdown']) / p['duration_days'])
        
        recovery_rate = np.mean(recovery_rates) if recovery_rates else 0.0
        
        return {
            'max_drawdown': max_dd,
            'avg_drawdown': avg_dd,
            'max_duration': max_duration,
            'avg_duration': avg_duration,
            'recovery_rate': recovery_rate,
            'drawdown_periods': drawdown_periods
        }

    def compute_risk_metrics(self, returns, risk_free_rate=0.0, ann_factor=252):
        """
        Compute additional risk metrics including Sortino ratio.
        
        Parameters
        ----------
        returns : pd.Series
            Daily returns series.
        risk_free_rate : float
            Annual risk-free rate.
        ann_factor : int
            Annualization factor (252 for daily returns).
            
        Returns
        -------
        dict
            Dictionary with risk metrics.
        """
        if len(returns) < 2:
            return {}
            
        # Convert annual risk-free rate to daily
        daily_rf = (1 + risk_free_rate) ** (1 / ann_factor) - 1
        
        # Calculate excess returns
        excess_returns = returns - daily_rf
        
        # Calculate downside returns (negative returns only)
        downside_returns = returns[returns < 0]
        downside_std = downside_returns.std() * np.sqrt(ann_factor) if not downside_returns.empty else 0.0
        
        # Calculate upside returns (positive returns only)
        upside_returns = returns[returns > 0]
        upside_std = upside_returns.std() * np.sqrt(ann_factor) if not upside_returns.empty else 0.0
        
        # Calculate Sortino Ratio
        ann_return = (1 + returns.mean()) ** ann_factor - 1
        sortino = (ann_return - risk_free_rate) / downside_std if downside_std > 1e-9 else 0.0
        
        # Calculate Calmar Ratio (annualized return / max drawdown)
        equity_curve = (1 + returns).cumprod()
        drawdown = self.compute_drawdown(equity_curve)
        max_dd = abs(drawdown.min()) if len(drawdown) > 0 else 0.0
        calmar = ann_return / max_dd if max_dd > 1e-9 else 0.0
        
        # Calculate Omega Ratio (probability-weighted ratio of gains vs. losses)
        threshold = daily_rf  # Use risk-free rate as threshold
        gains = excess_returns[excess_returns > 0].sum()
        losses = abs(excess_returns[excess_returns < 0].sum())
        omega = gains / losses if losses > 1e-9 else float('inf')
        
        # Information Ratio - requires benchmark returns to compute
        
        return {
            'Sortino': sortino,
            'Calmar': calmar,
            'Omega': omega,
            'UpsideVol': upside_std,
            'DownsideVol': downside_std,
        }
    
    def compute_trade_metrics(self, returns):
        """
        Compute trade-based metrics including win rate, profit factor.
        
        Parameters
        ----------
        returns : pd.Series
            Daily returns series.
            
        Returns
        -------
        dict
            Dictionary with trade metrics.
        """
        # Treating each day as a separate "trade" for this example
        # For actual trade metrics, you'd need trade-level data
        
        wins = returns[returns > 0]
        losses = returns[returns < 0]
        
        win_count = len(wins)
        loss_count = len(losses)
        total_trades = win_count + loss_count
        
        win_rate = win_count / total_trades if total_trades > 0 else 0.0
        
        gross_profits = wins.sum()
        gross_losses = abs(losses.sum())
        
        profit_factor = gross_profits / gross_losses if gross_losses > 1e-9 else float('inf')
        
        avg_win = wins.mean() if win_count > 0 else 0.0
        avg_loss = losses.mean() if loss_count > 0 else 0.0
        
        win_loss_ratio = abs(avg_win / avg_loss) if avg_loss < -1e-9 else float('inf')
        
        expectancy = (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
        
        return {
            'WinRate': win_rate,
            'ProfitFactor': profit_factor,
            'WinLossRatio': win_loss_ratio,
            'AvgWin': avg_win,
            'AvgLoss': avg_loss,
            'Expectancy': expectancy,
            'GrossProfits': gross_profits,
            'GrossLosses': gross_losses,
        }
    
    def compute_timeframe_returns(self, returns):
        """
        Calculate returns over different timeframes.
        
        Parameters
        ----------
        returns : pd.Series
            Daily returns.
            
        Returns
        -------
        dict
            Dictionary with returns over different periods.
        """
        if len(returns) < 2:
            return {}
            
        equity_curve = (1 + returns).cumprod()
        
        # Calculate returns over different periods
        mtd = 0.0
        qtd = 0.0
        ytd = 0.0
        
        if not returns.empty:
            # Month to date
            current_month = returns.index[-1].month
            month_start = returns[returns.index.month == current_month].index[0]
            # Fix FutureWarning by using iloc instead of [-1]
            mtd = equity_curve.iloc[-1] / equity_curve.loc[month_start] - 1
            
            # Quarter to date
            current_quarter = (returns.index[-1].month - 1) // 3 + 1
            quarter_months = [m for m in range(1, 13) if (m - 1) // 3 + 1 == current_quarter]
            quarter_data = returns[returns.index.month.isin(quarter_months)]
            if not quarter_data.empty:
                quarter_start = quarter_data.index[0]
                # Fix FutureWarning by using iloc instead of [-1]
                qtd = equity_curve.iloc[-1] / equity_curve.loc[quarter_start] - 1
            
            # Year to date
            current_year = returns.index[-1].year
            year_data = returns[returns.index.year == current_year]
            if not year_data.empty:
                year_start = year_data.index[0]
                # Fix FutureWarning by using iloc instead of [-1]
                ytd = equity_curve.iloc[-1] / equity_curve.loc[year_start] - 1
        
        # Calculate returns over fixed periods
        periods = {
            '1M': 21,  # Approx trading days in a month
            '3M': 63,  # Approx trading days in a quarter
            '6M': 126, # Approx trading days in 6 months
            '1Y': 252, # Approx trading days in a year
        }
        
        period_returns = {}
        for period_name, days in periods.items():
            if len(returns) >= days:
                # Fix FutureWarning by using iloc instead of [-1] and [-days]
                period_returns[period_name] = equity_curve.iloc[-1] / equity_curve.iloc[-days] - 1
            else:
                period_returns[period_name] = None
        
        return {
            'MTD': mtd,
            'QTD': qtd,
            'YTD': ytd,
            **period_returns
        }
    
    def analyze_returns(self, daily_returns, benchmark_returns=None, risk_free_rate=0.0, ann_factor=252):
        """
        Comprehensive analysis of returns with enhanced metrics.

        Parameters
        ----------
        daily_returns : pd.Series
            Daily returns of a strategy.
        benchmark_returns : pd.Series, optional
            Daily returns of a benchmark for comparison.
        risk_free_rate : float
            Annual risk-free rate.
        ann_factor : int
            Annualization factor, typically 252 for daily data.

        Returns
        -------
        stats : dict
            Comprehensive dictionary of performance metrics.
        """
        dr = daily_returns.dropna()
        if len(dr) < 2:
            return {}

        # Basic performance metrics
        total_ret = (1 + dr).prod() - 1.0
        n_days = len(dr)
        years = n_days / ann_factor
        cagr = (1 + total_ret) ** (1 / years) - 1.0

        ann_vol = dr.std() * np.sqrt(ann_factor)
        sharpe = (cagr - risk_free_rate) / ann_vol if ann_vol > 1e-9 else 0.0

        eq = (1 + dr).cumprod()
        
        # Add enhanced metrics
        risk_metrics = self.compute_risk_metrics(dr, risk_free_rate, ann_factor)
        trade_metrics = self.compute_trade_metrics(dr)
        drawdown_stats = self.compute_drawdown_stats(eq)
        timeframe_returns = self.compute_timeframe_returns(dr)
        
        # Calculate comparative metrics if benchmark is provided
        comparative_metrics = {}
        if benchmark_returns is not None:
            benchmark_returns = benchmark_returns.reindex(dr.index).dropna()
            
            if len(benchmark_returns) > 0:
                # Beta calculation
                covar = np.cov(dr, benchmark_returns)[0][1]
                benchmark_var = benchmark_returns.var()
                beta = covar / benchmark_var if benchmark_var > 1e-9 else 0.0
                
                # Alpha calculation (CAPM)
                benchmark_ret = (1 + benchmark_returns).prod() - 1.0
                benchmark_cagr = (1 + benchmark_ret) ** (1 / years) - 1.0
                expected_return = risk_free_rate + beta * (benchmark_cagr - risk_free_rate)
                alpha = cagr - expected_return
                
                # Tracking error
                tracking_diff = dr - benchmark_returns
                tracking_error = tracking_diff.std() * np.sqrt(ann_factor)
                
                # Information ratio
                information_ratio = (cagr - benchmark_cagr) / tracking_error if tracking_error > 1e-9 else 0.0
                
                # Correlation
                correlation = dr.corr(benchmark_returns)
                
                # Up/Down capture
                up_market = benchmark_returns > 0
                down_market = benchmark_returns < 0
                
                up_capture = dr[up_market].mean() / benchmark_returns[up_market].mean() if len(benchmark_returns[up_market]) > 0 else 0.0
                down_capture = dr[down_market].mean() / benchmark_returns[down_market].mean() if len(benchmark_returns[down_market]) > 0 else 0.0
                
                comparative_metrics = {
                    'Alpha': alpha,
                    'Beta': beta,
                    'TrackingError': tracking_error,
                    'InformationRatio': information_ratio,
                    'Correlation': correlation,
                    'UpCapture': up_capture,
                    'DownCapture': down_capture,
                }
        
        return {
            # Basic metrics
            'TotalReturn': total_ret,
            'CAGR': cagr,
            'AnnualVol': ann_vol,
            'Sharpe': sharpe,
            'MaxDrawdown': drawdown_stats['max_drawdown'],
            
            # Risk metrics
            'RiskMetrics': risk_metrics,
            
            # Trade metrics
            'TradeMetrics': trade_metrics,
            
            # Drawdown metrics
            'DrawdownMetrics': drawdown_stats,
            
            # Timeframe returns
            'TimeframeReturns': timeframe_returns,
            
            # Comparative metrics
            'ComparativeMetrics': comparative_metrics,
        }

    def analyze(self, results, risk_free_rate=0.0, ann_factor=252):
        """
        Analyze strategy and benchmark results.
        
        Parameters
        ----------
        results : dict
            Dictionary containing strategy_returns and benchmark_returns.
        risk_free_rate : float
            Annual risk-free rate.
        ann_factor : int
            Annualization factor.
            
        Returns
        -------
        dict
            Dictionary with analysis results.
        """
        strategy_stats = self.analyze_returns(
            results['strategy_returns'], 
            results['benchmark_returns'], 
            risk_free_rate, 
            ann_factor
        )
        
        benchmark_stats = self.analyze_returns(
            results['benchmark_returns'], 
            None,
            risk_free_rate, 
            ann_factor
        )

        return {
            'strategy_stats': strategy_stats,
            'benchmark_stats': benchmark_stats
        }

    def plot(self, results, benchmark_label="Benchmark", tearsheet=True):
        """
        Plots the strategy equity curve (and benchmark if given) plus drawdowns.
        Optionally includes a comprehensive tearsheet with metrics.

        Parameters
        ----------
        results : dict
            Results from the backtest. Will have signals_df, tickers_returns,
            strategy_returns, benchmark_returns, which are all Pandas DataFrames

        benchmark_label : str
            Label to use for benchmark in plot legend and summary stats.
            
        tearsheet : bool, default=True
            If True, adds a comprehensive performance metrics table to the plot.
        """
        if tearsheet:
            # Use the create_tearsheet method to generate a complete figure with tearsheet
            fig = self.create_tearsheet(results, benchmark_label=benchmark_label)
            plt.show()
            return
        
        # If tearsheet=False, just plot the basic equity and drawdown charts
        fig, (ax1, ax2) = plt.subplots(nrows=2, ncols=1, figsize=(10, 8), sharex=True)

        strategy_equity_curve = (1.0 + results['strategy_returns']).cumprod()
        benchmark_equity_curve = (1.0 + results['benchmark_returns']).cumprod()

        # Plot equity curves with specified colors and line widths
        ax1.plot(
            strategy_equity_curve.index,
            strategy_equity_curve.values,
            label="Strategy",
            color='mediumblue',   # deeper blue
            linewidth=1,         # a bit thicker
            alpha=0.6
        )
        ax1.plot(
            benchmark_equity_curve.index,
            benchmark_equity_curve.values,
            label=benchmark_label,
            color='black',      # black
            linewidth=0.5,         # a bit thinner
            alpha=0.5
        )
        ax1.set_title("Equity Curve (relative, starts at 1.0)")
        ax1.legend(loc='best')
        ax1.grid(True)

        # Fill between the strategy and benchmark lines
        ax1.fill_between(
            strategy_equity_curve.index,
            strategy_equity_curve.values,
            benchmark_equity_curve.values,
            where=(strategy_equity_curve.values >= benchmark_equity_curve.values),
            interpolate=True,
            color='green',
            alpha=0.1
        )
        ax1.fill_between(
            strategy_equity_curve.index,
            strategy_equity_curve.values,
            benchmark_equity_curve.values,
            where=(strategy_equity_curve.values < benchmark_equity_curve.values),
            interpolate=True,
            color='red',
            alpha=0.1
        )

        # Plot drawdowns
        strat_dd = self.compute_drawdown(strategy_equity_curve) * 100.0
        bm_dd = self.compute_drawdown(benchmark_equity_curve) * 100.0

        ax2.plot(
            strat_dd.index,
            strat_dd.values,
            label="Strategy DD (%)",
            color='mediumblue',   # deeper blue
            linewidth=1,         # a bit thicker
            alpha=0.6
        )
        ax2.plot(
            bm_dd.index,
            bm_dd.values,
            label=f"{benchmark_label} DD (%)",
            color='black',      # black
            linewidth=0.5,         # a bit thinner
            alpha=0.5
        )
        ax2.set_title("Drawdown (%)")
        ax2.legend(loc='best')
        ax2.grid(True)

        # Fill between drawdown lines: red where strategy is below, green where strategy is above
        ax2.fill_between(
            strat_dd.index,
            strat_dd.values,
            bm_dd.values,
            where=(strat_dd.values <= bm_dd.values),
            interpolate=True,
            color='red',
            alpha=0.1
        )
        ax2.fill_between(
            strat_dd.index,
            strat_dd.values,
            bm_dd.values,
            where=(strat_dd.values > bm_dd.values),
            interpolate=True,
            color='green',
            alpha=0.1
        )

        plt.tight_layout()
        plt.show()
        
    def create_tearsheet(self, results, risk_free_rate=0.0, ann_factor=252, benchmark_label="Benchmark"):
        """
        Creates a comprehensive tearsheet with all metrics organized by category.
        
        Parameters
        ----------
        results : dict
            Dictionary containing strategy_returns and benchmark_returns.
        risk_free_rate : float
            Annual risk-free rate.
        ann_factor : int
            Annualization factor.
        benchmark_label : str
            Label for the benchmark.
            
        Returns
        -------
        fig : matplotlib.figure.Figure
            The figure containing the tearsheet.
        """
        # --- 1) Run analysis ---
        stats = self.analyze(results, risk_free_rate, ann_factor)
        strategy_stats = stats['strategy_stats']
        benchmark_stats = stats['benchmark_stats']
        
        # --- 2) Create figure with grid layout ---
        fig = plt.figure(figsize=(12, 14))
        gs = GridSpec(4, 2, figure=fig, height_ratios=[1, 1.5, 1.5, 1])
        
        # --- 3) Plot equity curve and drawdown ---
        ax_equity = fig.add_subplot(gs[0, :])
        ax_drawdown = fig.add_subplot(gs[1, :])
        
        strategy_equity_curve = (1.0 + results['strategy_returns']).cumprod()
        benchmark_equity_curve = (1.0 + results['benchmark_returns']).cumprod()
        
        # Plot equity curves
        ax_equity.plot(
            strategy_equity_curve.index,
            strategy_equity_curve.values,
            label="Strategy",
            color='mediumblue',
            linewidth=1,
            alpha=0.6
        )
        ax_equity.plot(
            benchmark_equity_curve.index,
            benchmark_equity_curve.values,
            label=benchmark_label,
            color='black',
            linewidth=0.5,
            alpha=0.5
        )
        ax_equity.set_title("Equity Curve (relative, starts at 1.0)")
        ax_equity.legend(loc='best')
        ax_equity.grid(True)
        
        # Add fill between
        ax_equity.fill_between(
            strategy_equity_curve.index,
            strategy_equity_curve.values,
            benchmark_equity_curve.values,
            where=(strategy_equity_curve.values >= benchmark_equity_curve.values),
            interpolate=True,
            color='green',
            alpha=0.1
        )
        ax_equity.fill_between(
            strategy_equity_curve.index,
            strategy_equity_curve.values,
            benchmark_equity_curve.values,
            where=(strategy_equity_curve.values < benchmark_equity_curve.values),
            interpolate=True,
            color='red',
            alpha=0.1
        )
        
        # Plot drawdowns
        strat_dd = self.compute_drawdown(strategy_equity_curve) * 100.0
        bm_dd = self.compute_drawdown(benchmark_equity_curve) * 100.0
        
        ax_drawdown.plot(
            strat_dd.index,
            strat_dd.values,
            label="Strategy DD (%)",
            color='mediumblue',
            linewidth=1,
            alpha=0.6
        )
        ax_drawdown.plot(
            bm_dd.index,
            bm_dd.values,
            label=f"{benchmark_label} DD (%)",
            color='black',
            linewidth=0.5,
            alpha=0.5
        )
        ax_drawdown.set_title("Drawdown (%)")
        ax_drawdown.legend(loc='best')
        ax_drawdown.grid(True)
        
        # Fill between drawdown lines
        ax_drawdown.fill_between(
            strat_dd.index,
            strat_dd.values,
            bm_dd.values,
            where=(strat_dd.values <= bm_dd.values),
            interpolate=True,
            color='red',
            alpha=0.1
        )
        ax_drawdown.fill_between(
            strat_dd.index,
            strat_dd.values,
            bm_dd.values,
            where=(strat_dd.values > bm_dd.values),
            interpolate=True,
            color='green',
            alpha=0.1
        )
        
        # --- 4) Add table with metrics ---
        ax_table = fig.add_subplot(gs[2:, :])
        ax_table.axis('tight')
        ax_table.axis('off')
        
        # Dictionary of metrics indicating if positive difference is good
        positive_is_good = {
            # Performance Metrics
            'TotalReturn': True,
            'CAGR': True,
            'AnnualVol': False,
            'Sharpe': True,
            'MaxDrawdown': True,  # For drawdown, LESS negative is better, so a POSITIVE difference is good
            
            # Risk Metrics
            'Sortino': True,
            'Calmar': True,
            'Omega': True,
            'DownsideVol': False,
            
            # Drawdown Metrics
            'max_drawdown': True,  # For drawdown, LESS negative is better, so a POSITIVE difference is good
            'avg_drawdown': True,  # For drawdown, LESS negative is better, so a POSITIVE difference is good
            'max_duration': False,  # Shorter is better
            'avg_duration': False,  # Shorter is better
            'recovery_rate': True,  # Higher recovery rate is better
            
            # Trade Metrics
            'WinRate': True,
            'ProfitFactor': True,
            'WinLossRatio': True,
            'Expectancy': True,
            
            # Timeframe Returns
            'MTD': True,
            'QTD': True,
            'YTD': True,
            '1M': True,
            '3M': True,
            '6M': True,
            '1Y': True,
        }
        
        # Metric name mapping for better display
        renames = {
            # Performance Metrics
            'TotalReturn': 'Total Return',
            'CAGR': 'CAGR',
            'AnnualVol': 'Annual Volatility',
            'Sharpe': 'Sharpe Ratio',
            'MaxDrawdown': 'Max Drawdown',

            # Risk Metrics
            'Sortino': 'Sortino Ratio',
            'Calmar': 'Calmar Ratio',
            'Omega': 'Omega Ratio',
            'DownsideVol': 'Downside Volatility',

            # Drawdown Metrics
            'max_drawdown': 'Max Drawdown',
            'avg_drawdown': 'Average Drawdown',
            'max_duration': 'Max Duration',
            'avg_duration': 'Average Duration',
            'recovery_rate': 'Recovery Rate',

            # Trade Metrics
            'WinRate': 'Win Rate',
            'ProfitFactor': 'Profit Factor',
            'WinLossRatio': 'Win/Loss Ratio',
            'Expectancy': 'Expectancy',

            # Timeframe Returns
            'MTD': 'MTD',
            'QTD': 'QTD',
            'YTD': 'YTD',
            '1M': '1M',
            '3M': '3M',
            '6M': '6M',
            '1Y': '1Y',
            
            # Comparative Metrics
            'Alpha': 'Alpha',
            'Beta': 'Beta',
            'TrackingError': 'Tracking Error',
            'InformationRatio': 'Information Ratio',
            'Correlation': 'Correlation',
            'UpCapture': 'Up Capture',
            'DownCapture': 'Down Capture',
        }
        
        # Prepare table data
        table_data = []
        cell_colors = []  # To store cell colors for each row
        
        # Add header
        table_data.append(["Metric", "Strategy", benchmark_label, "Difference"])
        cell_colors.append([None, None, None, None])  # No special color for header
        
        # Add performance metrics
        table_data.append(["Performance Metrics", "", "", ""])
        cell_colors.append([None, None, None, None])  # No special color for section header
        
        for key in ['TotalReturn', 'CAGR', 'AnnualVol', 'Sharpe']:  # Removed MaxDrawdown as requested
            strat_val = strategy_stats.get(key, "N/A")
            bench_val = benchmark_stats.get(key, "N/A")
            
            # Use the renamed metric for display
            display_name = renames.get(key, key)
            
            if isinstance(strat_val, (int, float)) and isinstance(bench_val, (int, float)):
                diff = strat_val - bench_val
                if key in ["TotalReturn", "CAGR", "AnnualVol"]:
                    strat_val = f"{strat_val:.2%}"
                    bench_val = f"{bench_val:.2%}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.2%}"
                    else:
                        diff_text = f"{diff:.2%}"
                else:
                    strat_val = f"{strat_val:.2f}"
                    bench_val = f"{bench_val:.2f}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.2f}"
                    else:
                        diff_text = f"{diff:.2f}"
                
                # Determine if the difference is good or bad
                is_good = (diff > 0 and positive_is_good.get(key, True)) or (diff < 0 and not positive_is_good.get(key, True))
                cell_color = '#d8f3dc' if is_good else '#ffcccb' if diff != 0 else None  # Pastel green or red
            else:
                diff_text = "N/A"
                cell_color = None
                
            table_data.append([display_name, strat_val, bench_val, diff_text])
            cell_colors.append([None, None, None, cell_color])
        
        # Add risk metrics
        table_data.append(["Risk Metrics", "", "", ""])
        cell_colors.append([None, None, None, None])
        
        risk_metrics = strategy_stats.get('RiskMetrics', {})
        benchmark_risk = benchmark_stats.get('RiskMetrics', {})
        for key in ['Sortino', 'Calmar', 'Omega', 'DownsideVol']:
            strat_val = risk_metrics.get(key, "N/A")
            bench_val = benchmark_risk.get(key, "N/A")
            
            # Use the renamed metric for display
            display_name = renames.get(key, key)
            
            if isinstance(strat_val, (int, float)) and isinstance(bench_val, (int, float)):
                diff = strat_val - bench_val
                if key in ["DownsideVol"]:
                    strat_val = f"{strat_val:.2%}"
                    bench_val = f"{bench_val:.2%}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.2%}"
                    else:
                        diff_text = f"{diff:.2%}"
                else:
                    strat_val = f"{strat_val:.2f}"
                    bench_val = f"{bench_val:.2f}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.2f}"
                    else:
                        diff_text = f"{diff:.2f}"
                
                # Determine if the difference is good or bad
                is_good = (diff > 0 and positive_is_good.get(key, True)) or (diff < 0 and not positive_is_good.get(key, True))
                cell_color = '#d8f3dc' if is_good else '#ffcccb' if diff != 0 else None
            else:
                diff_text = "N/A"
                cell_color = None
                
            table_data.append([display_name, strat_val, bench_val, diff_text])
            cell_colors.append([None, None, None, cell_color])
        
        # Add drawdown metrics (added max_drawdown back)
        table_data.append(["Drawdown Metrics", "", "", ""])
        cell_colors.append([None, None, None, None])
        
        dd_metrics = strategy_stats.get('DrawdownMetrics', {})
        bench_dd = benchmark_stats.get('DrawdownMetrics', {})
        for key in ['max_drawdown', 'avg_drawdown', 'max_duration', 'avg_duration', 'recovery_rate']:
            strat_val = dd_metrics.get(key, "N/A")
            bench_val = bench_dd.get(key, "N/A")
            
            # Use the renamed metric for display
            display_name = renames.get(key, key)
            
            if isinstance(strat_val, (int, float)) and isinstance(bench_val, (int, float)):
                diff = strat_val - bench_val
                if key in ['max_drawdown', 'avg_drawdown']:
                    strat_val = f"{strat_val:.2%}"
                    bench_val = f"{bench_val:.2%}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.2%}"
                    else:
                        diff_text = f"{diff:.2%}"
                elif key in ['max_duration', 'avg_duration']:
                    strat_val = f"{strat_val:.0f} days"
                    bench_val = f"{bench_val:.0f} days"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.0f} days"
                    else:
                        diff_text = f"{diff:.0f} days"
                else:
                    strat_val = f"{strat_val:.4f}"
                    bench_val = f"{bench_val:.4f}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.4f}"
                    else:
                        diff_text = f"{diff:.4f}"
                
                # Determine if the difference is good or bad
                is_good = (diff > 0 and positive_is_good.get(key, True)) or (diff < 0 and not positive_is_good.get(key, True))
                cell_color = '#d8f3dc' if is_good else '#ffcccb' if diff != 0 else None
            else:
                diff_text = "N/A"
                cell_color = None
                
            table_data.append([display_name, strat_val, bench_val, diff_text])
            cell_colors.append([None, None, None, cell_color])
        
        # Add trade metrics
        table_data.append(["Trade Metrics", "", "", ""])
        cell_colors.append([None, None, None, None])
        
        trade_metrics = strategy_stats.get('TradeMetrics', {})
        bench_trade = benchmark_stats.get('TradeMetrics', {})
        for key in ['WinRate', 'ProfitFactor', 'WinLossRatio', 'Expectancy']:
            strat_val = trade_metrics.get(key, "N/A")
            bench_val = bench_trade.get(key, "N/A")
            
            # Use the renamed metric for display
            display_name = renames.get(key, key)
            
            if isinstance(strat_val, (int, float)) and isinstance(bench_val, (int, float)):
                diff = strat_val - bench_val
                if key == 'WinRate':
                    strat_val = f"{strat_val:.2%}"
                    bench_val = f"{bench_val:.2%}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.2%}"
                    else:
                        diff_text = f"{diff:.2%}"
                elif key == 'Expectancy':
                    strat_val = f"{strat_val:.4%}"
                    bench_val = f"{bench_val:.4%}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.4%}"
                    else:
                        diff_text = f"{diff:.4%}"
                else:
                    strat_val = f"{strat_val:.2f}"
                    bench_val = f"{bench_val:.2f}"
                    # Add + sign for positive differences
                    if diff > 0:
                        diff_text = f"+{diff:.2f}"
                    else:
                        diff_text = f"{diff:.2f}"
                
                # Determine if the difference is good or bad
                is_good = (diff > 0 and positive_is_good.get(key, True)) or (diff < 0 and not positive_is_good.get(key, True))
                cell_color = '#d8f3dc' if is_good else '#ffcccb' if diff != 0 else None
            else:
                diff_text = "N/A"
                cell_color = None
                
            table_data.append([display_name, strat_val, bench_val, diff_text])
            cell_colors.append([None, None, None, cell_color])
        
        # Add comparative metrics
        table_data.append(["Comparative Metrics", "", "", ""])
        cell_colors.append([None, None, None, None])
        
        comp_metrics = strategy_stats.get('ComparativeMetrics', {})
        for key in ['Alpha', 'Beta', 'InformationRatio', 'Correlation', 'UpCapture', 'DownCapture']:
            val = comp_metrics.get(key, "N/A")
            
            # Use the renamed metric for display
            display_name = renames.get(key, key)
            
            if isinstance(val, (int, float)):
                if key == 'Alpha':
                    val = f"{val:.2%}"
                else:
                    val = f"{val:.2f}"
                
                # Leave benchmark and difference columns empty instead of N/A
                table_data.append([display_name, val, "", ""])
            else:
                table_data.append([display_name, val, "", ""])
            
            cell_colors.append([None, None, None, None])  # No coloring for comparative metrics
        
        # Add timeframe returns
        table_data.append(["Timeframe Returns", "", "", ""])
        cell_colors.append([None, None, None, None])
        
        tf_returns = strategy_stats.get('TimeframeReturns', {})
        bench_tf = benchmark_stats.get('TimeframeReturns', {})
        for key in ['MTD', 'QTD', 'YTD', '1M', '3M', '6M', '1Y']:
            strat_val = tf_returns.get(key, "N/A")
            bench_val = bench_tf.get(key, "N/A")
            
            # Use the renamed metric for display
            display_name = renames.get(key, key)
            
            if isinstance(strat_val, (int, float)) and isinstance(bench_val, (int, float)):
                diff = strat_val - bench_val
                strat_val = f"{strat_val:.2%}"
                bench_val = f"{bench_val:.2%}"
                # Add + sign for positive differences
                if diff > 0:
                    diff_text = f"+{diff:.2%}"
                else:
                    diff_text = f"{diff:.2%}"
                
                # For returns, higher is always better
                is_good = diff > 0
                cell_color = '#d8f3dc' if is_good else '#ffcccb' if diff != 0 else None
            else:
                diff_text = "N/A"
                cell_color = None
                if isinstance(strat_val, (int, float)):
                    strat_val = f"{strat_val:.2%}"
                if isinstance(bench_val, (int, float)):
                    bench_val = f"{bench_val:.2%}"
                
            table_data.append([display_name, strat_val, bench_val, diff_text])
            cell_colors.append([None, None, None, cell_color])
        
        # Create the table
        table = ax_table.table(
            cellText=table_data,
            cellLoc='left',
            loc='center',
            bbox=[0, 0, 1, 1]
        )
        
        # Style the table
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        
        # Create a reverse lookup for the renamed metrics
        reverse_renames = {v: k for k, v in renames.items()}
        
        # Import FontProperties for bold text
        from matplotlib.font_manager import FontProperties
        
        # Set cell properties including colors
        for (i, j), cell in table.get_celld().items():
            if i == 0:  # Header row
                cell.set_text_props(weight='bold', color='white')
                cell.set_facecolor('darkblue')
            elif j == 0:  # Metric column
                cell.set_text_props(weight='bold')
            
            # Apply custom cell colors for difference column (j=3)
            if j == 3 and i > 0 and i < len(cell_colors) and cell_colors[i][j] is not None:
                cell.set_facecolor(cell_colors[i][j])
                # Don't add bold to difference column
            
            # Category headers
            if j == 0 and table_data[i][j] in [
                "Performance Metrics", 
                "Risk Metrics", 
                "Drawdown Metrics", 
                "Trade Metrics", 
                "Comparative Metrics",
                "Timeframe Returns"
            ]:
                cell.set_text_props(weight='bold', color='white')
                cell.set_facecolor('gray')
                
                # Make category headers span all columns
                for col in range(1, 4):
                    table[i, col].set_text_props(weight='bold', color='white')
                    table[i, col].set_facecolor('gray')
        
        # Add asterisks and bold formatting to indicate better values
        for i in range(len(table_data)):
            if i <= 1:  # Skip header row and first category header
                continue
                
            if i < len(table_data) and len(table_data[i]) > 3:
                row_data = table_data[i]
                display_name = row_data[0]
                
                # Skip category headers
                if display_name in [
                    "Performance Metrics", 
                    "Risk Metrics", 
                    "Drawdown Metrics", 
                    "Trade Metrics", 
                    "Comparative Metrics",
                    "Timeframe Returns"
                ]:
                    continue
                    
                # For comparative metrics, there's no difference column, just skip
                if row_data[3] == "":
                    continue
                
                # Convert display name back to the original key for lookup in positive_is_good
                original_key = reverse_renames.get(display_name, display_name)
                
                # Only process if we have a difference value
                if row_data[3] != "N/A" and row_data[3] != "":
                    # Try to get the numeric difference to determine which cell to highlight
                    try:
                        diff_text = row_data[3]
                        if diff_text.startswith('+'):
                            diff_text = diff_text[1:]  # Remove the + sign for parsing
                            
                        if diff_text.endswith('%'):
                            diff_value = float(diff_text.strip('%')) / 100
                        elif diff_text.endswith(' days'):
                            diff_value = float(diff_text.split()[0])
                        else:
                            diff_value = float(diff_text)
                            
                        # Look up in the original positive_is_good dictionary using the original key
                        is_good = (diff_value > 0 and positive_is_good.get(original_key, True)) or \
                                 (diff_value < 0 and not positive_is_good.get(original_key, True))
                        
                        if diff_value != 0:  # Only highlight if there's a difference
                            if is_good:
                                # Strategy is better - add asterisk to strategy cell
                                cell = table.get_celld()[(i, 1)]
                                current_text = cell.get_text().get_text()
                                # Make sure we're not duplicating asterisks
                                clean_text = current_text.replace("*", "")
                                cell.get_text().set_text(f"{clean_text}*")
                                cell.set_text_props(fontproperties=FontProperties(weight='bold'))
                            else:
                                # Benchmark is better - add asterisk to benchmark cell
                                cell = table.get_celld()[(i, 2)]
                                current_text = cell.get_text().get_text()
                                # Make sure we're not duplicating asterisks
                                clean_text = current_text.replace("*", "")
                                cell.get_text().set_text(f"{clean_text}*")
                                cell.set_text_props(fontproperties=FontProperties(weight='bold'))
                    except (ValueError, TypeError):
                        # Skip cells where we can't parse the difference
                        pass
        
        # Adjust layout
        plt.tight_layout()
        
        return fig