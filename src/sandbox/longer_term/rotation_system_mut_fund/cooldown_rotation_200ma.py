import numpy as np
import pandas as pd
from portwine.strategies.base import StrategyBase

class CooldownLowVolBondStrategy200DayMA(StrategyBase):
    """
    A short‑term momentum + SMA‑filtered strategy over a small universe of bond funds,
    with a 30‑day cooldown after selling any fund, and a 200‑day SPY filter:
      - If SPY > its 200‑day SMA, go 100% SPY.
      - <PERSON><PERSON>, run the mutual‑fund momentum+SMA+cooldown logic.
    """

    def __init__(self,
                 tickers,
                 momentum_lookback_days=15,
                 sma_filter_days=10,
                 cash_ticker=None,
                 cooldown_days=30,
                 spy_ticker="SPY"):
        """
        Parameters
        ----------
        tickers : list of str
            Your universe of bond fund tickers.
        momentum_lookback_days : int
            Days for short‑term momentum.
        sma_filter_days : int
            Days for the fund SMA filter.
        cash_ticker : str or None
            Fallback if the fund SMA fails.
        cooldown_days : int
            Days after selling before re‑eligibility.
        spy_ticker : str
            Ticker symbol for SPY (must be in your data feed).
        """
        # ensure <PERSON><PERSON> is in the universe
        self.asset_tickers = tickers.copy()
        self.spy_ticker = spy_ticker
        all_tickers = tickers.copy()
        if spy_ticker not in all_tickers:
            all_tickers.append(spy_ticker)

        super().__init__(all_tickers)

        self.momentum_lookback = momentum_lookback_days
        self.sma_filter = sma_filter_days
        self.cash_ticker = cash_ticker
        self.cooldown_days = cooldown_days

        # keep rolling history for funds + SPY
        lookback = max(momentum_lookback_days, sma_filter_days, 200) + 1
        self.price_history = {t: [] for t in all_tickers}

        # track weights and cooldowns for all tickers
        self.current_weights = {t: 0.0 for t in all_tickers}
        self.cooldowns = {}  # ticker -> pd.Timestamp

    def step(self, current_date, daily_data):
        prev_weights = self.current_weights.copy()

        # 1) Expire cooldowns
        for t, expiry in list(self.cooldowns.items()):
            if current_date >= expiry:
                del self.cooldowns[t]

        # 2) Update price history for all tickers
        lookback = max(self.momentum_lookback, self.sma_filter, 200) + 1
        for t in self.tickers:
            price = (daily_data.get(t) or {}).get('close', np.nan)
            self.price_history[t].append(price)
            if len(self.price_history[t]) > lookback:
                self.price_history[t].pop(0)

        # 3) Check SPY 200‑day moving average
        spy_hist = self.price_history.get(self.spy_ticker, [])
        if len(spy_hist) >= 200 and not any(np.isnan(spy_hist[-200:])):
            spy_ma200 = np.mean(spy_hist[-200:])
            spy_price = spy_hist[-1]
            if spy_price > spy_ma200:
                # go 100% SPY
                for t in self.tickers:
                    self.current_weights[t] = 0.0
                self.current_weights[self.spy_ticker] = 1.0
                # cooldown any fund sold today
                for t in self.asset_tickers:
                    if prev_weights.get(t, 0.0) > 0.0:
                        self.cooldowns[t] = current_date + pd.Timedelta(days=self.cooldown_days)
                return dict(self.current_weights)

        # 4) SPY is below MA200: run original mutual‑fund logic
        # 4a) Compute momentum returns for eligible funds
        returns = {}
        eligible = [t for t in self.asset_tickers if t not in self.cooldowns]
        for t in self.asset_tickers:
            if t not in eligible:
                returns[t] = -np.inf
                continue
            prices = self.price_history[t]
            if len(prices) < self.momentum_lookback + 1 or any(np.isnan(prices[-(self.momentum_lookback+1):])):
                returns[t] = -np.inf
                continue
            window = prices[-(self.momentum_lookback+1):]
            returns[t] = window[-1] / window[0] - 1.0

        # 4b) Pick the best fund
        best = max(returns, key=returns.get)
        if returns[best] == -np.inf:
            # no valid fund => flat or cash
            for t in self.tickers:
                self.current_weights[t] = 0.0
            if self.cash_ticker in self.asset_tickers:
                self.current_weights[self.cash_ticker] = 1.0
        else:
            # 5) SMA filter on that fund
            hist = self.price_history[best]
            if len(hist) < self.sma_filter + 1 or any(np.isnan(hist[-self.sma_filter:])):
                passes = False
            else:
                recent = hist[-self.sma_filter:]
                passes = recent[-1] > np.mean(recent)

            for t in self.tickers:
                self.current_weights[t] = 0.0
            if passes:
                self.current_weights[best] = 1.0
            elif self.cash_ticker in self.asset_tickers:
                self.current_weights[self.cash_ticker] = 1.0

        # 6) Assign cooldown to any fund sold today
        for t in self.asset_tickers:
            if prev_weights.get(t, 0.0) > 0.0 and self.current_weights.get(t, 0.0) == 0.0:
                self.cooldowns[t] = current_date + pd.Timedelta(days=self.cooldown_days)

        # ensure SPY weight is zero when not in SPY regime
        self.current_weights[self.spy_ticker] = 0.0
        return dict(self.current_weights)
