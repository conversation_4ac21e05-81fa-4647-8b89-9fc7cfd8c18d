{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(start_date=datetime(2025, 1, 1))\n", "                        .build_market_data())\n", "    \n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"\n", "    Fetches daily market data for a specified ticker and date range using yfinance.\n", "    \n", "    Parameters:\n", "    -----------\n", "    ticker : str\n", "        The stock ticker symbol (e.g., 'JNK', 'SHY').\n", "    start_dt : datetime\n", "        Start date for the data request.\n", "    end_dt : datetime\n", "        End date for the data request.\n", "    \n", "    Returns:\n", "    --------\n", "    pandas.DataFrame\n", "        A DataFrame containing daily market data with columns:\n", "        open, high, low, close, volume\n", "    \"\"\"\n", "    try:\n", "        import yfinance as yf\n", "        \n", "        # Get data with dividend adjustment\n", "        stock_data = yf.Ticker(ticker)\n", "        hist = stock_data.history(start=start_dt, end=end_dt, auto_adjust=True)\n", "        \n", "        # Rename columns to match the expected format\n", "        hist.columns = [col.lower() for col in hist.columns]\n", "        \n", "        # Make sure we have the expected columns\n", "        if 'close' not in hist.columns and 'adj close' in hist.columns:\n", "            hist['close'] = hist['adj close']\n", "            \n", "        return hist\n", "    except Exception as e:\n", "        print(f\"Exception fetching data for {ticker}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# # %% [markdown]\n", "# # # High Yield Bond Moving Day Average Strategy Implementation\n", "# # \n", "# # This notebook implements the High Yield Bond MDA Strategy using daily market data.\n", "# # We'll be using JNK (SPDR Barclays High Yield Bond ETF) as our high yield bond ETF and SHY as our cash/safety asset.\n", "\n", "# # %% [markdown]\n", "# # ## Strategy Implementation\n", "\n", "# # %%\n", "# # Strategy class implementation\n", "# class HighYieldBondMDAStrategy:\n", "#     \"\"\"\n", "#     From \"Unique Application Of Moving Day Average To High Yield Bond ETFs\":\n", "#     - We take a short moving-day-average approach on high-yield bond ETFs,\n", "#       e.g. JNK or HYG, to quickly exit on small drawdowns.\n", "#     - Rules: short MA < long MA => cash. Short MA > long MA => invest fully in bond ETF.\n", "#     - This can be done for multiple tickers if desired, or just 1 at a time.\n", "    \n", "#     Parameters\n", "#     ----------\n", "#     bond_ticker : str\n", "#         Ticker symbol for the high yield bond ETF (default: \"JNK\")\n", "#     short_window : int\n", "#         Number of days for the short moving average (default: 3)\n", "#     long_window : int\n", "#         Number of days for the long moving average (default: 12)\n", "#     cash_ticker : str\n", "#         Ticker symbol for the cash/safety asset (default: \"SHY\")\n", "#     \"\"\"\n", "    \n", "#     def __init__(self, bond_ticker=\"JNK\", short_window=3, long_window=12, cash_ticker=\"SHY\"):\n", "#         \"\"\"Initialize the High Yield Bond MDA strategy.\"\"\"\n", "#         # Store parameters\n", "#         self.bond_ticker = bond_ticker\n", "#         self.cash_ticker = cash_ticker\n", "#         self.short_window = short_window\n", "#         self.long_window = long_window\n", "        \n", "#         # Initialize price history for the bond ticker\n", "#         self.price_history = []\n", "        \n", "#         # Current allocation - start in cash until we have enough history\n", "#         self.current_allocation = {\n", "#             bond_ticker: 0.0,\n", "#             cash_ticker: 1.0\n", "#         }\n", "    \n", "#     def update(self, price):\n", "#         \"\"\"\n", "#         Update price history and determine allocations based on moving average crossovers.\n", "        \n", "#         Parameters\n", "#         ----------\n", "#         price : float\n", "#             Current close price of the bond ETF\n", "            \n", "#         Returns\n", "#         -------\n", "#         dict\n", "#             Dictionary of {ticker: allocation} for each ticker\n", "#         \"\"\"\n", "#         # Update price history\n", "#         self.price_history.append(price)\n", "        \n", "#         # Need at least long_window days of data to compute MAs\n", "#         if len(self.price_history) < self.long_window:\n", "#             # Not enough data yet, stay in cash\n", "#             return self.current_allocation.copy()\n", "            \n", "#         # Calculate short and long moving averages\n", "#         prices = self.price_history\n", "        \n", "#         # Compute short MA (most recent short_window days)\n", "#         short_window_prices = prices[-self.short_window:]\n", "#         short_ma = sum(short_window_prices) / self.short_window\n", "        \n", "#         # Compute long MA (most recent long_window days)\n", "#         long_window_prices = prices[-self.long_window:]\n", "#         long_ma = sum(long_window_prices) / self.long_window\n", "        \n", "#         # Apply MA crossover logic\n", "#         new_allocation = {\n", "#             self.bond_ticker: 0.0,\n", "#             self.cash_ticker: 0.0\n", "#         }\n", "        \n", "#         if short_ma > long_ma:\n", "#             # Short MA above Long MA: Bullish signal, invest in bonds\n", "#             new_allocation[self.bond_ticker] = 1.0\n", "#         else:\n", "#             # Short MA below Long MA: Bearish signal, move to cash\n", "#             new_allocation[self.cash_ticker] = 1.0\n", "            \n", "#         # Update and return the new allocation\n", "#         self.current_allocation = new_allocation\n", "#         return new_allocation.copy()\n", "\n", "# # %% [markdown]\n", "# # ## Fetch Market Data\n", "\n", "# # %%\n", "# # Define tickers and parameters\n", "# bond_ticker = \"JNK\"  # High Yield Bond ETF\n", "# cash_ticker = \"SHY\"  # Cash/Safety Asset\n", "# short_window = 3     # Short MA window\n", "# long_window = 12     # Long MA window\n", "\n", "# # Set the date range for backtesting\n", "# start_date = datetime(2008, 1, 1)\n", "# end_date = datetime(2025, 4, 22)  # Using a date within the knowledge cutoff\n", "\n", "# # Get historical data\n", "# bond_data = get_daily_data(bond_ticker, start_date, end_date)\n", "# cash_data = get_daily_data(cash_ticker, start_date, end_date)\n", "\n", "# # Display first few rows of data\n", "# print(f\"{bond_ticker} Data:\")\n", "# display(bond_data.head())\n", "# print(f\"\\n{cash_ticker} Data:\")\n", "# display(cash_data.head())\n", "\n", "# # %% [markdown]\n", "# # ## Backtest the Strategy\n", "\n", "# # %%\n", "# # Initialize the strategy\n", "# strategy = HighYieldBondMDAStrategy(\n", "#     bond_ticker=bond_ticker,\n", "#     short_window=short_window,\n", "#     long_window=long_window,\n", "#     cash_ticker=cash_ticker\n", "# )\n", "\n", "# # Prepare DataFrames for backtesting\n", "# bond_df = bond_data.copy()\n", "# cash_df = cash_data.copy()\n", "\n", "# # Ensure both DataFrames have the same index\n", "# common_dates = bond_df.index.intersection(cash_df.index)\n", "# bond_df = bond_df.loc[common_dates]\n", "# cash_df = cash_df.loc[common_dates]\n", "\n", "# # Create a DataFrame to store results\n", "# results = pd.DataFrame(index=common_dates)\n", "# results[f'{bond_ticker}_Close'] = bond_df['close']\n", "# results[f'{cash_ticker}_Close'] = cash_df['close']\n", "\n", "# # Normalize prices to start at 100\n", "# results[f'{bond_ticker}_Norm'] = results[f'{bond_ticker}_Close'] / results[f'{bond_ticker}_Close'].iloc[0] * 100\n", "# results[f'{cash_ticker}_Norm'] = results[f'{cash_ticker}_Close'] / results[f'{cash_ticker}_Close'].iloc[0] * 100\n", "\n", "# # Initialize columns for strategy results\n", "# results[f'Allocation_{bond_ticker}'] = 0.0\n", "# results[f'Allocation_{cash_ticker}'] = 1.0  # Start in cash\n", "# results['Portfolio_Value'] = 100.0  # Start with $100\n", "\n", "# # Run the backtest\n", "# for i in range(len(results)):\n", "#     # Update strategy with current price\n", "#     current_price = results[f'{bond_ticker}_Close'].iloc[i]\n", "#     allocation = strategy.update(current_price)\n", "    \n", "#     # Store allocations\n", "#     results[f'Allocation_{bond_ticker}'].iloc[i] = allocation[bond_ticker]\n", "#     results[f'Allocation_{cash_ticker}'].iloc[i] = allocation[cash_ticker]\n", "    \n", "#     # Calculate portfolio value (starting from day after first allocation change)\n", "#     if i > 0:\n", "#         prev_bond_alloc = results[f'Allocation_{bond_ticker}'].iloc[i-1]\n", "#         prev_cash_alloc = results[f'Allocation_{cash_ticker}'].iloc[i-1]\n", "        \n", "#         # Calculate returns based on allocations\n", "#         bond_return = results[f'{bond_ticker}_Close'].iloc[i] / results[f'{bond_ticker}_Close'].iloc[i-1] - 1\n", "#         cash_return = results[f'{cash_ticker}_Close'].iloc[i] / results[f'{cash_ticker}_Close'].iloc[i-1] - 1\n", "        \n", "#         # Calculate portfolio return\n", "#         portfolio_return = (prev_bond_alloc * bond_return) + (prev_cash_alloc * cash_return)\n", "        \n", "#         # Update portfolio value\n", "#         results['Portfolio_Value'].iloc[i] = results['Portfolio_Value'].iloc[i-1] * (1 + portfolio_return)\n", "\n", "# # Calculate daily returns for performance metrics\n", "# results['Daily_Return'] = results['Portfolio_Value'].pct_change()\n", "\n", "# # %% [markdown]\n", "# # ## Visualize Results\n", "\n", "# # %%\n", "# # Calculate drawdown series for plotting\n", "# def calculate_drawdown(returns):\n", "#     \"\"\"Calculate drawdown series from returns.\"\"\"\n", "#     # Calculate cumulative returns\n", "#     cum_returns = (1 + returns.fillna(0)).cumprod()\n", "    \n", "#     # Calculate drawdowns\n", "#     running_max = np.maximum.accumulate(cum_returns)\n", "#     drawdown_series = (cum_returns / running_max) - 1\n", "    \n", "#     return drawdown_series\n", "\n", "# # %%\n", "# # Create drawdown series\n", "# drawdown = calculate_drawdown(results['Daily_Return'])\n", "\n", "# # Create figure with two subplots\n", "# fig, axes = plt.subplots(2, 1, figsize=(14, 10))\n", "\n", "# # Plot 1: Equity Curve\n", "# axes[0].plot(results.index, results['Portfolio_Value'], 'b-', linewidth=2, label='Strategy Portfolio Value')\n", "# axes[0].plot(results.index, results[f'{bond_ticker}_Norm'], 'r--', alpha=0.7, label=f'{bond_ticker} (Buy & Hold)')\n", "# axes[0].plot(results.index, results[f'{cash_ticker}_Norm'], 'g--', alpha=0.7, label=f'{cash_ticker} (Buy & Hold)')\n", "# axes[0].set_title(f'High Yield Bond MDA Strategy: {bond_ticker} vs {cash_ticker}', fontsize=16)\n", "# axes[0].set_ylabel('Value (Starting at 100)', fontsize=12)\n", "# axes[0].legend(loc='best')\n", "# axes[0].grid(True)\n", "\n", "# # Plot 2: Drawdown\n", "# axes[1].fill_between(results.index, 0, drawdown, color='red', alpha=0.5)\n", "# axes[1].set_title('Strategy Drawdown', fontsize=16)\n", "# axes[1].set_ylabel('Drawdown', fontsize=12)\n", "# axes[1].set_xlabel('Date', fontsize=12)\n", "# axes[1].grid(True)\n", "\n", "# # Adjust layout\n", "# plt.tight_layout()\n", "# plt.show()\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["JNK Data:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>dividends</th>\n", "      <th>stock splits</th>\n", "      <th>capital gains</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-02 00:00:00-05:00</th>\n", "      <td>94.173264</td>\n", "      <td>94.232273</td>\n", "      <td>93.996227</td>\n", "      <td>94.173264</td>\n", "      <td>3681500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03 00:00:00-05:00</th>\n", "      <td>94.350297</td>\n", "      <td>94.399477</td>\n", "      <td>94.281450</td>\n", "      <td>94.369965</td>\n", "      <td>1540400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06 00:00:00-05:00</th>\n", "      <td>94.527329</td>\n", "      <td>94.586339</td>\n", "      <td>94.389635</td>\n", "      <td>94.478149</td>\n", "      <td>2800100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-07 00:00:00-05:00</th>\n", "      <td>94.527337</td>\n", "      <td>94.625689</td>\n", "      <td>94.143760</td>\n", "      <td>94.192932</td>\n", "      <td>3764900</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-08 00:00:00-05:00</th>\n", "      <td>93.996220</td>\n", "      <td>94.320787</td>\n", "      <td>93.996220</td>\n", "      <td>94.291283</td>\n", "      <td>3348200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                open       high        low      close  \\\n", "Date                                                                    \n", "2025-01-02 00:00:00-05:00  94.173264  94.232273  93.996227  94.173264   \n", "2025-01-03 00:00:00-05:00  94.350297  94.399477  94.281450  94.369965   \n", "2025-01-06 00:00:00-05:00  94.527329  94.586339  94.389635  94.478149   \n", "2025-01-07 00:00:00-05:00  94.527337  94.625689  94.143760  94.192932   \n", "2025-01-08 00:00:00-05:00  93.996220  94.320787  93.996220  94.291283   \n", "\n", "                            volume  dividends  stock splits  capital gains  \n", "Date                                                                        \n", "2025-01-02 00:00:00-05:00  3681500        0.0           0.0            0.0  \n", "2025-01-03 00:00:00-05:00  1540400        0.0           0.0            0.0  \n", "2025-01-06 00:00:00-05:00  2800100        0.0           0.0            0.0  \n", "2025-01-07 00:00:00-05:00  3764900        0.0           0.0            0.0  \n", "2025-01-08 00:00:00-05:00  3348200        0.0           0.0            0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "SHY Data:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>dividends</th>\n", "      <th>stock splits</th>\n", "      <th>capital gains</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-02 00:00:00-05:00</th>\n", "      <td>81.201927</td>\n", "      <td>81.211832</td>\n", "      <td>81.132609</td>\n", "      <td>81.162315</td>\n", "      <td>3948300</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03 00:00:00-05:00</th>\n", "      <td>81.201927</td>\n", "      <td>81.211832</td>\n", "      <td>81.142513</td>\n", "      <td>81.162315</td>\n", "      <td>2804100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06 00:00:00-05:00</th>\n", "      <td>81.162319</td>\n", "      <td>81.192026</td>\n", "      <td>81.142517</td>\n", "      <td>81.182129</td>\n", "      <td>2324300</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-07 00:00:00-05:00</th>\n", "      <td>81.172218</td>\n", "      <td>81.192020</td>\n", "      <td>81.102900</td>\n", "      <td>81.132607</td>\n", "      <td>4035500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-08 00:00:00-05:00</th>\n", "      <td>81.152415</td>\n", "      <td>81.192026</td>\n", "      <td>81.152415</td>\n", "      <td>81.182129</td>\n", "      <td>2747600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                open       high        low      close  \\\n", "Date                                                                    \n", "2025-01-02 00:00:00-05:00  81.201927  81.211832  81.132609  81.162315   \n", "2025-01-03 00:00:00-05:00  81.201927  81.211832  81.142513  81.162315   \n", "2025-01-06 00:00:00-05:00  81.162319  81.192026  81.142517  81.182129   \n", "2025-01-07 00:00:00-05:00  81.172218  81.192020  81.102900  81.132607   \n", "2025-01-08 00:00:00-05:00  81.152415  81.192026  81.152415  81.182129   \n", "\n", "                            volume  dividends  stock splits  capital gains  \n", "Date                                                                        \n", "2025-01-02 00:00:00-05:00  3948300        0.0           0.0            0.0  \n", "2025-01-03 00:00:00-05:00  2804100        0.0           0.0            0.0  \n", "2025-01-06 00:00:00-05:00  2324300        0.0           0.0            0.0  \n", "2025-01-07 00:00:00-05:00  4035500        0.0           0.0            0.0  \n", "2025-01-08 00:00:00-05:00  2747600        0.0           0.0            0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total number of trades: 7\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Type</th>\n", "      <th>Ticker</th>\n", "      <th>Price</th>\n", "      <th>Allocation</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-21 00:00:00-05:00</th>\n", "      <td>BUY</td>\n", "      <td>JNK</td>\n", "      <td>95.137123</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-11 00:00:00-05:00</th>\n", "      <td>SELL</td>\n", "      <td>SHY</td>\n", "      <td>81.462280</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-14 00:00:00-05:00</th>\n", "      <td>BUY</td>\n", "      <td>JNK</td>\n", "      <td>95.617622</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-06 00:00:00-05:00</th>\n", "      <td>SELL</td>\n", "      <td>SHY</td>\n", "      <td>82.111145</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-20 00:00:00-04:00</th>\n", "      <td>BUY</td>\n", "      <td>JNK</td>\n", "      <td>95.324875</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-28 00:00:00-04:00</th>\n", "      <td>SELL</td>\n", "      <td>SHY</td>\n", "      <td>82.390198</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-16 00:00:00-04:00</th>\n", "      <td>BUY</td>\n", "      <td>JNK</td>\n", "      <td>93.120003</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           Type Ticker      Price  Allocation\n", "Date                                                         \n", "2025-01-21 00:00:00-05:00   BUY    JNK  95.137123         1.0\n", "2025-02-11 00:00:00-05:00  SELL    SHY  81.462280         1.0\n", "2025-02-14 00:00:00-05:00   BUY    JNK  95.617622         1.0\n", "2025-03-06 00:00:00-05:00  SELL    SHY  82.111145         1.0\n", "2025-03-20 00:00:00-04:00   BUY    JNK  95.324875         1.0\n", "2025-03-28 00:00:00-04:00  SELL    SHY  82.390198         1.0\n", "2025-04-16 00:00:00-04:00   BUY    JNK  93.120003         1.0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Trades saved to jnk_shy_mda_trades.csv\n", "Number of BUY trades: 4\n", "Number of SELL trades: 3\n", "Average holding period: 16.33 days\n", "Full results saved to jnk_shy_mda_results.csv\n"]}, {"data": {"image/png": "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*******************************************+UV2C5Fs2zfLkO16tSBJ56QnbHffAPZ2dafd/T2ZP/gdxk2uRJ3huT9GtqmDfwy6wixw6LYc8SNnWfkhfr8eRn2+dpr0l99xAgZjGn04LfCio6WhOnmzXI+IED62RZHewIHB3jySWldMWSI9W80YoS08nB2vvrP26s77pDnFEj/X03aFoO4OJlK6OYmH3CVKkIJCfIdCaTiXz/LFozJbNTEk6s4c+YMP/zwA9u2bSMyMpKUlBTc3d0JCQmhcePG3HHHHYSFFX3POFuJj4/H19eXuLg4fErabk4gIyODX375hR49rAmFEi8xUV5FtOWGfXj6adml/sgjZAQFlb7nWxkTFSUDMSxJ2l278t/W0SMBn2GDCQwyExQI/gFQN7gWrcNb0zq8NRW8Lh9IkpAgVTyzZ19+2yaTVGY8//zlE6/Ts9L5+/jfBHsG51RPrjy8kpkbZgJQ3bcOi1/rzsm1bSDLhSeflGWGV5KVJUlOS/XM+PEy6Op6FPY1NiMrg8jEyJx2C5bDg00epF5wPQB+P/o776x/BwBfV19G3TSKNuFt8iQsMzLky7Zl6esff9juC1F2NjRtav1i+b//QbOOpynvVT4naZuckczmM5tzWhgEuAfkSWJnZsrzYMqUy2cX3nGHJFOKc1jYvn1SYbZ3L1B5NY5t3+amm8z0bnIz49uML1Q/4qT0JJ5aMY7VW06xf6cn/DkZLtTl7rtl4FBRDlQvle/pym6VxufbfffJ/yXA3Lmy4+5GnDkjCeCPP5aBLbm5uEhi9OGHpZe10YlLs1mW3r/8svRMzY+/vyRcH39cTudrwQLZe9iqFTt6Pce0aZKk+++***************************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", "text/plain": ["<Figure size 1400x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Trades saved to JNK_SHY_mda_trades.csv\n", "Number of BUY trades: 4\n", "Number of SELL trades: 3\n", "Full results saved to JNK_SHY_mda_results.csv\n"]}], "source": ["# %% [markdown]\n", "# # High Yield Bond Moving Day Average Strategy Implementation\n", "# \n", "# This notebook implements the High Yield Bond MDA Strategy using daily market data.\n", "# We'll be using JNK (SPDR Barclays High Yield Bond ETF) as our high yield bond ETF and SHY as our cash/safety asset.\n", "\n", "# %% [markdown]\n", "# ## Strategy Implementation\n", "\n", "# Calculate drawdown series for plotting\n", "def calculate_drawdown(returns):\n", "    \"\"\"Calculate drawdown series from returns.\"\"\"\n", "    # Calculate cumulative returns\n", "    cum_returns = (1 + returns.fillna(0)).cumprod()\n", "    \n", "    # Calculate drawdowns\n", "    running_max = np.maximum.accumulate(cum_returns)\n", "    drawdown_series = (cum_returns / running_max) - 1\n", "    \n", "    return drawdown_series\n", "\n", "# %%\n", "# Strategy class implementation\n", "class HighYieldBondMDAStrategy:\n", "    \"\"\"\n", "    From \"Unique Application Of Moving Day Average To High Yield Bond ETFs\":\n", "    - We take a short moving-day-average approach on high-yield bond ETFs,\n", "      e.g. JNK or HYG, to quickly exit on small drawdowns.\n", "    - Rules: short MA < long MA => cash. Short MA > long MA => invest fully in bond ETF.\n", "    - This can be done for multiple tickers if desired, or just 1 at a time.\n", "    \n", "    Parameters\n", "    ----------\n", "    bond_ticker : str\n", "        Ticker symbol for the high yield bond ETF (default: \"JNK\")\n", "    short_window : int\n", "        Number of days for the short moving average (default: 3)\n", "    long_window : int\n", "        Number of days for the long moving average (default: 12)\n", "    cash_ticker : str\n", "        Ticker symbol for the cash/safety asset (default: \"SHY\")\n", "    \"\"\"\n", "    \n", "    def __init__(self, bond_ticker=\"JNK\", short_window=3, long_window=12, cash_ticker=\"SHY\"):\n", "        \"\"\"Initialize the High Yield Bond MDA strategy.\"\"\"\n", "        # Store parameters\n", "        self.bond_ticker = bond_ticker\n", "        self.cash_ticker = cash_ticker\n", "        self.short_window = short_window\n", "        self.long_window = long_window\n", "        \n", "        # Initialize price history for the bond ticker\n", "        self.price_history = []\n", "        \n", "        # Current allocation - start in cash until we have enough history\n", "        self.current_allocation = {\n", "            bond_ticker: 0.0,\n", "            cash_ticker: 1.0\n", "        }\n", "    \n", "    def update(self, price):\n", "        \"\"\"\n", "        Update price history and determine allocations based on moving average crossovers.\n", "        \n", "        Parameters\n", "        ----------\n", "        price : float\n", "            Current close price of the bond ETF\n", "            \n", "        Returns\n", "        -------\n", "        dict\n", "            Dictionary of {ticker: allocation} for each ticker\n", "        \"\"\"\n", "        # Update price history\n", "        self.price_history.append(price)\n", "        \n", "        # Need at least long_window days of data to compute MAs\n", "        if len(self.price_history) < self.long_window:\n", "            # Not enough data yet, stay in cash\n", "            return self.current_allocation.copy()\n", "            \n", "        # Calculate short and long moving averages\n", "        prices = self.price_history\n", "        \n", "        # Compute short MA (most recent short_window days)\n", "        short_window_prices = prices[-self.short_window:]\n", "        short_ma = sum(short_window_prices) / self.short_window\n", "        \n", "        # Compute long MA (most recent long_window days)\n", "        long_window_prices = prices[-self.long_window:]\n", "        long_ma = sum(long_window_prices) / self.long_window\n", "        \n", "        # Apply MA crossover logic\n", "        new_allocation = {\n", "            self.bond_ticker: 0.0,\n", "            self.cash_ticker: 0.0\n", "        }\n", "        \n", "        if short_ma > long_ma:\n", "            # Short MA above Long MA: Bullish signal, invest in bonds\n", "            new_allocation[self.bond_ticker] = 1.0\n", "        else:\n", "            # Short MA below Long MA: Bearish signal, move to cash\n", "            new_allocation[self.cash_ticker] = 1.0\n", "            \n", "        # Update and return the new allocation\n", "        self.current_allocation = new_allocation\n", "        return new_allocation.copy()\n", "\n", "# %% [markdown]\n", "# ## Fetch Market Data\n", "\n", "# %%\n", "# Define tickers and parameters\n", "bond_ticker = \"JNK\"  # High Yield Bond ETF\n", "cash_ticker = \"SHY\"  # Cash/Safety Asset\n", "short_window = 3     # Short MA window\n", "long_window = 12     # Long MA window\n", "\n", "# Set the date range for backtesting\n", "start_date = datetime(2025, 1, 1)\n", "end_date = datetime(2025, 4, 21)\n", "\n", "# Get historical data\n", "bond_data = get_daily_data(bond_ticker, start_date, end_date)\n", "cash_data = get_daily_data(cash_ticker, start_date, end_date)\n", "\n", "# Display first few rows of data\n", "print(f\"{bond_ticker} Data:\")\n", "display(bond_data.head())\n", "print(f\"\\n{cash_ticker} Data:\")\n", "display(cash_data.head())\n", "\n", "# %% [markdown]\n", "# ## Backtest the Strategy\n", "\n", "# %%\n", "# Initialize the strategy\n", "strategy = HighYieldBondMDAStrategy(\n", "    bond_ticker=bond_ticker,\n", "    short_window=short_window,\n", "    long_window=long_window,\n", "    cash_ticker=cash_ticker\n", ")\n", "\n", "# Prepare DataFrames for backtesting\n", "bond_df = bond_data.copy()\n", "cash_df = cash_data.copy()\n", "\n", "# Ensure both DataFrames have the same index\n", "common_dates = bond_df.index.intersection(cash_df.index)\n", "bond_df = bond_df.loc[common_dates]\n", "cash_df = cash_df.loc[common_dates]\n", "\n", "# Create a DataFrame to store results\n", "results = pd.DataFrame(index=common_dates)\n", "results[f'{bond_ticker}_Close'] = bond_df['close']\n", "results[f'{cash_ticker}_Close'] = cash_df['close']\n", "\n", "# Normalize prices to start at 100\n", "results[f'{bond_ticker}_Norm'] = results[f'{bond_ticker}_Close'] / results[f'{bond_ticker}_Close'].iloc[0] * 100\n", "results[f'{cash_ticker}_Norm'] = results[f'{cash_ticker}_Close'] / results[f'{cash_ticker}_Close'].iloc[0] * 100\n", "\n", "# Initialize columns for strategy results\n", "results[f'Allocation_{bond_ticker}'] = 0.0\n", "results[f'Allocation_{cash_ticker}'] = 1.0  # Start in cash\n", "results['Portfolio_Value'] = 100.0  # Start with $100\n", "\n", "# List to store trade information\n", "trades = []\n", "prev_allocation = {bond_ticker: 0.0, cash_ticker: 1.0}  # Start in cash\n", "\n", "# Run the backtest\n", "for i in range(len(results)):\n", "    # Get current date\n", "    current_date = results.index[i]\n", "    \n", "    # Update strategy with current price\n", "    current_price = results[f'{bond_ticker}_Close'].iloc[i]\n", "    allocation = strategy.update(current_price)\n", "    \n", "    # Store allocations\n", "    results[f'Allocation_{bond_ticker}'].iloc[i] = allocation[bond_ticker]\n", "    results[f'Allocation_{cash_ticker}'].iloc[i] = allocation[cash_ticker]\n", "    \n", "    # Check if allocation changed (trade occurred)\n", "    if allocation[bond_ticker] != prev_allocation[bond_ticker]:\n", "        trade_type = \"BUY\" if allocation[bond_ticker] > prev_allocation[bond_ticker] else \"SELL\"\n", "        trades.append({\n", "            'Date': current_date,\n", "            'Type': trade_type,\n", "            'Ticker': bond_ticker if trade_type == \"BUY\" else cash_ticker,\n", "            'Price': current_price if trade_type == \"BUY\" else results[f'{cash_ticker}_Close'].iloc[i],\n", "            'Allocation': allocation[bond_ticker] if trade_type == \"BUY\" else allocation[cash_ticker]\n", "        })\n", "    \n", "    # Update previous allocation\n", "    prev_allocation = allocation.copy()\n", "    \n", "    # Calculate portfolio value (starting from day after first allocation change)\n", "    if i > 0:\n", "        prev_bond_alloc = results[f'Allocation_{bond_ticker}'].iloc[i-1]\n", "        prev_cash_alloc = results[f'Allocation_{cash_ticker}'].iloc[i-1]\n", "        \n", "        # Calculate returns based on allocations\n", "        bond_return = results[f'{bond_ticker}_Close'].iloc[i] / results[f'{bond_ticker}_Close'].iloc[i-1] - 1\n", "        cash_return = results[f'{cash_ticker}_Close'].iloc[i] / results[f'{cash_ticker}_Close'].iloc[i-1] - 1\n", "        \n", "        # Calculate portfolio return\n", "        portfolio_return = (prev_bond_alloc * bond_return) + (prev_cash_alloc * cash_return)\n", "        \n", "        # Update portfolio value\n", "        results['Portfolio_Value'].iloc[i] = results['Portfolio_Value'].iloc[i-1] * (1 + portfolio_return)\n", "\n", "# Calculate daily returns for performance metrics\n", "results['Daily_Return'] = results['Portfolio_Value'].pct_change()\n", "\n", "# Convert trades list to DataFrame\n", "trades_df = pd.DataFrame(trades)\n", "if not trades_df.empty:\n", "    trades_df.set_index('Date', inplace=True)\n", "    \n", "# Display trades\n", "if not trades_df.empty:\n", "    print(f\"Total number of trades: {len(trades_df)}\")\n", "    display(trades_df)\n", "\n", "# %%\n", "# Export trades to CSV\n", "if not trades_df.empty:\n", "    # Save trades to CSV\n", "    trades_csv_path = 'jnk_shy_mda_trades.csv'\n", "    trades_df.to_csv(trades_csv_path)\n", "    print(f\"Trades saved to {trades_csv_path}\")\n", "    \n", "    # Calculate some trade statistics\n", "    buy_trades = trades_df[trades_df['Type'] == 'BUY']\n", "    sell_trades = trades_df[trades_df['Type'] == 'SELL']\n", "    \n", "    print(f\"Number of BUY trades: {len(buy_trades)}\")\n", "    print(f\"Number of SELL trades: {len(sell_trades)}\")\n", "    \n", "    # Calculate average holding period (if we have both buys and sells)\n", "    if not buy_trades.empty and not sell_trades.empty:\n", "        # Get the dates\n", "        buy_dates = buy_trades.index.tolist()\n", "        sell_dates = sell_trades.index.tolist()\n", "        \n", "        # Make sure we have the same number of periods\n", "        min_periods = min(len(buy_dates), len(sell_dates))\n", "        \n", "        holding_periods = []\n", "        for i in range(min_periods):\n", "            # Make sure we don't go out of bounds\n", "            if i >= len(buy_dates) or i >= len(sell_dates):\n", "                break\n", "                \n", "            # Only consider if buy is before sell\n", "            if buy_dates[i] < sell_dates[i]:\n", "                days_held = (sell_dates[i] - buy_dates[i]).days\n", "                holding_periods.append(days_held)\n", "        \n", "        if holding_periods:\n", "            avg_holding_period = sum(holding_periods) / len(holding_periods)\n", "            print(f\"Average holding period: {avg_holding_period:.2f} days\")\n", "            \n", "# Export full results to CSV\n", "results_csv_path = 'jnk_shy_mda_results.csv'\n", "results.to_csv(results_csv_path)\n", "print(f\"Full results saved to {results_csv_path}\")\n", "\n", "# %%\n", "# Create drawdown series\n", "drawdown = calculate_drawdown(results['Daily_Return'])\n", "\n", "# Create figure with two subplots\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10))\n", "\n", "# Plot 1: Equity Curve\n", "axes[0].plot(results.index, results['Portfolio_Value'], 'b-', linewidth=2, label='Strategy Portfolio Value')\n", "axes[0].plot(results.index, results[f'{bond_ticker}_Norm'], 'r--', alpha=0.7, label=f'{bond_ticker} (Buy & Hold)')\n", "axes[0].plot(results.index, results[f'{cash_ticker}_Norm'], 'g--', alpha=0.7, label=f'{cash_ticker} (Buy & Hold)')\n", "axes[0].set_title(f'High Yield Bond MDA Strategy: {bond_ticker} vs {cash_ticker}', fontsize=16)\n", "axes[0].set_ylabel('Value (Starting at 100)', fontsize=12)\n", "axes[0].legend(loc='best')\n", "axes[0].grid(True)\n", "\n", "# Plot 2: Drawdown\n", "axes[1].fill_between(results.index, 0, drawdown, color='red', alpha=0.5)\n", "axes[1].set_title('Strategy Drawdown', fontsize=16)\n", "axes[1].set_ylabel('Drawdown', fontsize=12)\n", "axes[1].set_xlabel('Date', fontsize=12)\n", "axes[1].grid(True)\n", "\n", "# Adjust layout\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Export trades and results to CSV\n", "if not trades_df.empty:\n", "    # Save trades to CSV\n", "    trades_csv_path = f'{bond_ticker}_{cash_ticker}_mda_trades.csv'\n", "    trades_df.to_csv(trades_csv_path)\n", "    print(f\"Trades saved to {trades_csv_path}\")\n", "    \n", "    # Calculate some trade statistics\n", "    buy_trades = trades_df[trades_df['Type'] == 'BUY']\n", "    sell_trades = trades_df[trades_df['Type'] == 'SELL']\n", "    \n", "    print(f\"Number of BUY trades: {len(buy_trades)}\")\n", "    print(f\"Number of SELL trades: {len(sell_trades)}\")\n", "    \n", "# Export full results to CSV\n", "results_csv_path = f'{bond_ticker}_{cash_ticker}_mda_results.csv'\n", "results.to_csv(results_csv_path)\n", "print(f\"Full results saved to {results_csv_path}\")\n", "\n", "# %% [markdown]\n", "# ## Summary\n", "# \n", "# This notebook implements a parameterizable High Yield Bond Moving Day Average Strategy. \n", "# \n", "# Current parameters:\n", "# - Bond ETF: {bond_ticker}\n", "# - Cash/Safety Asset: {cash_ticker}\n", "# - Short MA window: {short_window} days\n", "# - Long MA window: {long_window} days\n", "# \n", "# You can easily modify the parameters at the beginning of the notebook to test with different tickers and window sizes.\n", "# \n", "# All trades and daily results have been exported to CSV files for further analysis."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}