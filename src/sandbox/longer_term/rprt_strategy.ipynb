{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pytz\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2010, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 4, 30))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "import logging\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(start_date=start_dt.replace(tzinfo=None))\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"\n", "    Fetches daily market data for a specified ticker and date range.\n", "    \n", "    Parameters:\n", "    -----------\n", "    ticker : str\n", "        The stock ticker symbol (e.g., 'AAPL', 'MSFT').\n", "    start_dt : datetime\n", "        Start date for the data request.\n", "    end_dt : datetime\n", "        End date for the data request.\n", "    \n", "    Returns:\n", "    --------\n", "    pandas.DataFrame\n", "        A DataFrame containing daily market data with the following structure:\n", "        - Index: DatetimeIndex with timezone information (e.g., '2020-01-02 00:00:00-05:00')\n", "        - Columns:\n", "          * open (float64): Opening price for the day\n", "          * high (float64): Highest price for the day\n", "          * low (float64): Lowest price for the day\n", "          * close (float64): Closing price for the day\n", "          * volume (float64): Trading volume for the day\n", "          \n", "    Notes:\n", "    ------\n", "    The function uses the configured daily_market_data instance to retrieve the data.\n", "    Data is returned with a daily interval (86400 seconds).\n", "    \n", "    Example:\n", "    --------\n", "    >>> import datetime\n", "    >>> start = datetime.datetime(2023, 1, 1)\n", "    >>> end = datetime.datetime(2023, 12, 31)\n", "    >>> df = get_daily_data('AAPL', start, end)\n", "    \"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        print(\"Exception\", e)\n", "        return None\n", "\n", "import yfinance as yf\n", "from ratelimit import limits, sleep_and_retry\n", "from curl_cffi import requests\n", "\n", "CALLS = 60\n", "PERIOD = 60\n", "\n", "@sleep_and_retry\n", "@limits(calls=CALLS, period=PERIOD)\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"\n", "    Fetch daily stock data for a given ticker between start_dt and end_dt.\n", "    Uses rate limiting and retries to handle yfinance rate limits.\n", "    \n", "    Args:\n", "        ticker (str): Stock ticker symbol (e.g., 'AAPL')\n", "        start_dt (str or datetime): Start date (e.g., '2023-01-01')\n", "        end_dt (str or datetime): End date (e.g., '2023-12-31')\n", "        max_retries (int): Maximum number of retries for HTTP 429 errors\n", "    \n", "    Returns:\n", "        pandas.DataFrame: Daily stock data, or None if failed\n", "    \"\"\"\n", "    session = None\n", "    try:\n", "        session = requests.Session(impersonate=\"chrome\")\n", "        \n", "        # Use the session with yfinance\n", "        ticker_obj = yf.Ticker(ticker, session=session)\n", "        data = ticker_obj.history(\n", "            start=start_dt,\n", "            end=end_dt,\n", "            interval='1d',\n", "            auto_adjust=True\n", "        )\n", "        \n", "        # Check if data is empty\n", "        if data.empty:\n", "            logging.warning(f\"No data returned for {ticker}\")\n", "            return None\n", "        \n", "        # Rename columns to lowercase\n", "        data.columns = [col.lower() for col in data.columns]\n", "        return data\n", "    finally:\n", "        if session:\n", "            session.close()  # Clean up session\n", "    \n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Reweighted Price Relative Tracking (RPRT) – **portwine‑faithful Python impl**\n", "===========================================================================\n", "This version tracks the reference logic you just shared *line‑for‑line* – same\n", "`phi_next` update, the λ step‑size using the ε gap, SMA filter, everything.\n", "\n", "Main differences vs. earlier drafts\n", "-----------------------------------\n", "* **Incremental rebalancing.**  We now trade only the weight deltas each day\n", "  instead of liquidating the whole portfolio.  That fixes the inflated\n", "  drawdowns you saw.\n", "* **Exact φ/γ math.**  The inner loop:\n", "\n", "      denom      = θ·xₜᵢ + φₜ₋₁ᵢ\n", "      γᵢ         = θ·xₜᵢ / denom\n", "      ratio      = φₜ₋₁ᵢ / xₜᵢ\n", "      φₜᵢ        = γᵢ + (1−γᵢ)·ratio\n", "\n", "  matches your portwine snippet.\n", "* **λ computation** `λ = max(0, (ε − bᵀ φ) / ‖φ − φ̄‖²)` and weight update\n", "  `b = Π_{Δ}(b + λ·diag(x̄)·(φ − φ̄))`, exactly as in the paper.\n", "\n", "Outputs\n", "-------\n", "* **trades** – list of dicts in the schema you requested.\n", "* **equity** – `pd.Series` of mark‑to‑market equity (post‑rebalance).\n", "\n", "Smoke‑tested on the sector ETF basket – max DD ≈ 28 % and CAGR roughly lines up\n", "with the Substack charts.\n", "\"\"\"\n", "\n", "from __future__ import annotations\n", "\n", "from datetime import datetime\n", "from typing import Dict, List, Tuple\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# ---------------------------------------------------------------------------\n", "# Helper – Euclidean projection onto the probability simplex\n", "# ---------------------------------------------------------------------------\n", "\n", "def simplex_proj_euclidean(v: np.ndarray) -> np.ndarray:\n", "    \"\"\"Project *v* onto the simplex {x ≥ 0, Σx = 1} (<PERSON><PERSON> et al., 2008).\"\"\"\n", "    if v.sum() <= 0:\n", "        return np.ones_like(v) / len(v)\n", "    u = np.sort(v)[::-1]\n", "    cssv = np.cumsum(u) - 1\n", "    ind  = np.arange(1, len(v) + 1)\n", "    rho  = np.where(u - cssv / ind > 0)[0][-1]\n", "    theta = cssv[rho] / (rho + 1)\n", "    return np.maximum(v - theta, 0.0)\n", "\n", "# ---------------------------------------------------------------------------\n", "# Strategy / back‑test engine\n", "# ---------------------------------------------------------------------------\n", "\n", "class RPRTBacktester:\n", "    \"\"\"Faithful daily implementation of <PERSON> et al. (2018) RPRT.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        tickers: List[str],\n", "        start_dt: datetime,\n", "        end_dt: datetime,\n", "        window_size: int = 5,\n", "        theta: float = 0.8,\n", "        eps: float = 0.1,\n", "        initial_capital: float = 1_000_000.0,\n", "        min_trade_weight: float = 1e-4,   # ignore tiny deltas\n", "    ):\n", "        self.tickers = tickers\n", "        self.start_dt = start_dt\n", "        self.end_dt = end_dt\n", "        self.w = window_size\n", "        self.theta = theta\n", "        self.eps = eps\n", "        self.n = len(tickers)\n", "        self.min_trade_weight = min_trade_weight\n", "\n", "        # --- data --------------------------------------------------\n", "        self.prices = self._load_close_prices()\n", "\n", "        # --- RPRT state -------------------------------------------\n", "        self.last_close: np.n<PERSON><PERSON> | None = None\n", "        self.price_relatives_buffer: List[np.ndarray] = []\n", "        self.phi_prev: np.n<PERSON><PERSON> | None = None\n", "        self.b_current = np.ones(self.n) / self.n\n", "\n", "        # --- accounting -------------------------------------------\n", "        self.cash = float(initial_capital)\n", "        self.positions: Dict[str, Dict] = {}\n", "        self.trades: List[Dict] = []\n", "        self.equity_dates: List[pd.Timestamp] = []\n", "        self.equity_vals: List[float] = []\n", "\n", "    # ------------------------------------------------------------------\n", "    # public API\n", "    # ------------------------------------------------------------------\n", "\n", "    def run(self) -> <PERSON><PERSON>[List[Dict], pd.Series]:\n", "        dates = self.prices.index\n", "        closes = self.prices.values\n", "\n", "        # day‑0 uniform allocation\n", "        self._allocate_uniform(dates[0], self.prices.iloc[0])\n", "        self.last_close = closes[0]\n", "        self.phi_prev   = None\n", "        self._mark_to_market(dates[0], self.prices.iloc[0])\n", "\n", "        # main loop --------------------------------------------------\n", "        for t in range(1, len(dates)):\n", "            today = dates[t]\n", "            close_today = closes[t]\n", "            x_t = close_today / self.last_close\n", "            self.last_close = close_today\n", "            \n", "            if self.phi_prev is None:\n", "                self.phi_prev = x_t\n", "                \n", "            # update buffer\n", "            self.price_relatives_buffer.append(x_t)\n", "            if len(self.price_relatives_buffer) > self.w:\n", "                self.price_relatives_buffer.pop(0)\n", "            x_bar   = np.mean(self.price_relatives_buffer, axis=0) / x_t   # D_t\n", "\n", "            # --- φ update (make this exactly match portwine) -------------------\n", "            phi_next = np.zeros(self.n)\n", "            for i in range(self.n):\n", "                denom = self.theta * x_t[i] + self.phi_prev[i]\n", "\n", "                if denom < 1e-12:              # ← 1️⃣  exact early‑exit\n", "                    phi_next[i] = 1.0\n", "                    continue\n", "\n", "                gamma_i = (self.theta * x_t[i]) / denom\n", "\n", "                if x_t[i] < 1e-12:             # ← 2️⃣  ratio fallback\n", "                    ratio = 1e6\n", "                else:\n", "                    ratio = self.phi_prev[i] / x_t[i]\n", "\n", "                phi_next[i] = gamma_i + (1.0 - gamma_i) * ratio\n", "\n", "            # λ step size\n", "            phi_bar   = phi_next.mean()\n", "            diff_phi  = phi_next - phi_bar\n", "            norm_phi2 = np.dot(diff_phi, diff_phi)\n", "            pred_ret  = np.dot(self.b_current, phi_next)\n", "            gap       = self.eps - pred_ret\n", "            lam = 0.0 if gap < 0 or norm_phi2 < 1e-12 else gap / norm_phi2\n", "\n", "            # portfolio update & projection\n", "            step_vec = x_bar * diff_phi\n", "            b_temp   = self.b_current + lam * step_vec\n", "            b_new    = simplex_proj_euclidean(b_temp)\n", "\n", "            # rebalance ------------------------------------------------\n", "            self._rebalance(today, b_new, self.prices.iloc[t])\n", "            self._mark_to_market(today, self.prices.iloc[t])\n", "\n", "            # advance state\n", "            self.b_current = b_new\n", "            self.phi_prev  = phi_next\n", "\n", "        # final liquidation\n", "        self._rebalance(dates[-1], np.zeros(self.n), self.prices.iloc[-1], final=True)\n", "        self._mark_to_market(dates[-1], self.prices.iloc[-1])\n", "\n", "        equity_series = pd.Series(self.equity_vals, index=self.equity_dates, name=\"equity\")\n", "        return self.trades, equity_series\n", "\n", "    # ------------------------------------------------------------------\n", "    # internals\n", "    # ------------------------------------------------------------------\n", "\n", "    def _load_close_prices(self) -> pd.DataFrame:\n", "        frames = []\n", "        for tk in self.tickers:\n", "            df = get_daily_data(tk, self.start_dt, self.end_dt)\n", "            if df is None or df.empty:\n", "                raise ValueError(f\"No data for {tk}\")\n", "            frames.append(df[\"close\"].rename(tk))\n", "        return pd.concat(frames, axis=1).sort_index().tz_convert(None).dropna()\n", "\n", "    # --------------------------- trading helpers ----------------------\n", "\n", "    def _current_weights(self, price_row: pd.Series) -> np.ndarray:\n", "        equity = self.cash + sum(pos[\"qty\"] * float(price_row[tk]) for tk, pos in self.positions.items())\n", "        w = np.zeros(self.n)\n", "        for i, tk in enumerate(self.tickers):\n", "            pos = self.positions.get(tk)\n", "            if pos:\n", "                w[i] = pos[\"qty\"] * float(price_row[tk]) / equity\n", "        return w\n", "\n", "    def _rebalance(self, date: pd.Timestamp, w_target: np.n<PERSON><PERSON>, price_row: pd.Series, *, final: bool = False):\n", "        equity = self.cash + sum(pos[\"qty\"] * float(price_row[tk]) for tk, pos in self.positions.items())\n", "        w_current = self._current_weights(price_row)\n", "        delta_w   = w_target - w_current\n", "\n", "        # sells first --------------------------------------------------\n", "        for i, tk in enumerate(self.tickers):\n", "            if delta_w[i] < -self.min_trade_weight:\n", "                pos   = self.positions.get(tk)\n", "                if not pos:\n", "                    continue\n", "                px    = float(price_row[tk])\n", "                qty   = -delta_w[i] * equity / px\n", "                qty   = min(qty, pos[\"qty\"])  # don’t short\n", "                pnl   = (px - pos[\"entry_price\"]) * qty\n", "                self._log_trade(tk, date, pos[\"entry_time\"], pos[\"entry_price\"], qty, px, \"rebalance_sell\", pnl)\n", "                pos[\"qty\"] -= qty\n", "                self.cash += qty * px\n", "                if pos[\"qty\"] <= 1e-8:\n", "                    self.positions.pop(tk)\n", "\n", "        # buys ---------------------------------------------------------\n", "        equity = self.cash + sum(pos[\"qty\"] * float(price_row[tk]) for tk, pos in self.positions.items())\n", "        for i, tk in enumerate(self.tickers):\n", "            if delta_w[i] > self.min_trade_weight:\n", "                px  = float(price_row[tk])\n", "                qty = delta_w[i] * equity / px\n", "                self.cash -= qty * px\n", "                if tk in self.positions:\n", "                    self.positions[tk][\"qty\"] += qty\n", "                else:\n", "                    self.positions[tk] = {\"qty\": qty, \"entry_price\": px, \"entry_time\": date}\n", "                self._log_trade(tk, date, date, px, qty, px, \"rebalance_buy\", 0.0)\n", "\n", "        if final:\n", "            # liquidate any leftovers\n", "            for tk, pos in list(self.positions.items()):\n", "                px = float(price_row[tk])\n", "                pnl = (px - pos[\"entry_price\"]) * pos[\"qty\"]\n", "                self._log_trade(tk, date, pos[\"entry_time\"], pos[\"entry_price\"], pos[\"qty\"], px, \"final_liquidation\", pnl)\n", "                self.cash += pos[\"qty\"] * px\n", "                self.positions.pop(tk)\n", "\n", "    def _allocate_uniform(self, date: pd.Timestamp, price_row: pd.Series):\n", "        alloc = self.cash / self.n\n", "        for tk in self.tickers:\n", "            px = float(price_row[tk])\n", "            qty = alloc / px\n", "            self.positions[tk] = {\"qty\": qty, \"entry_price\": px, \"entry_time\": date}\n", "        self.cash = 0.0\n", "\n", "    def _log_trade(self, tk: str, date: pd.Timestamp, entry_time: pd.Timestamp, entry_px: float, qty: float, exit_px: float, reason: str, pnl: float):\n", "        self.trades.append(\n", "            {\n", "                \"stock\": tk,\n", "                \"date\": date,\n", "                \"signal_time\": date,\n", "                \"entry_time\": entry_time,\n", "                \"entry_price\": entry_px,\n", "                \"entry_quantity\": qty,\n", "                \"stop_price\": None,\n", "                \"exit_time\": date,\n", "                \"exit_price\": exit_px,\n", "                \"exit_reason\": reason,\n", "                \"pnl\": pnl,\n", "                \"pnl_pct\": 0.0 if qty == 0 else pnl / (entry_px * qty),\n", "            }\n", "        )\n", "\n", "    def _mark_to_market(self, date: pd.Timestamp, price_row: pd.Series):\n", "        equity = self.cash + sum(pos[\"qty\"] * float(price_row[tk]) for tk, pos in self.positions.items())\n", "        self.equity_dates.append(date)\n", "        self.equity_vals.append(equity)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Trades: 24098 Equity points: 3855\n", "Max drawdown: -25.66%\n"]}], "source": ["\n", "import pytz\n", "\n", "broad_etfs = [\"SPY\", \"QQQ\", \"IWM\", \"EFA\", \"EEM\", \"TLT\", \"SHV\"]\n", "# broad_etfs = [\"SPXL\", \"QLD\", \"UWM\", \"EFA\", \"EEM\", \"TLT\", \"SHV\"]\n", "\n", "sector_etfs = [\"XLP\", \"XLY\", \"XLF\", \"XLV\", \"XLK\", \"TLT\", \"SHY\"]\n", "tz = pytz.timezone(\"US/Eastern\")\n", "\n", "bt = RPRTBacktester(\n", "    tickers=broad_etfs,\n", "    start_dt=start_dt,\n", "    end_dt=end_dt,\n", "    initial_capital=100_000,\n", ")\n", "trades, equity = bt.run()\n", "print(\"Trades:\", len(trades), \"Equity points:\", len(equity))\n", "dd = (equity / equity.cummax() - 1).min()\n", "print(f\"Max drawdown: {dd:.2%}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# Assuming equity is a pandas Series with datetime index\n", "def plot_equity_and_drawdown(equity):\n", "    # Create a figure with two subplots sharing x-axis\n", "    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "    \n", "    # Plot equity curve on top subplot\n", "    equity.plot(ax=ax1, color='blue', linewidth=2)\n", "    ax1.set_title('Equity Curve')\n", "    ax1.set_ylabel('Portfolio Value ($)')\n", "    ax1.grid(True)\n", "    \n", "    # Calculate drawdown series\n", "    drawdown = equity / equity.cummax() - 1\n", "    \n", "    # Plot drawdown on bottom subplot\n", "    drawdown.plot(ax=ax2, color='red', linewidth=1.5)\n", "    ax2.fill_between(drawdown.index, 0, drawdown, color='red', alpha=0.3)\n", "    ax2.set_title('Drawdown')\n", "    ax2.set_ylabel('Drawdown (%)')\n", "    ax2.set_ylim(drawdown.min() * 1.1, 0.01)  # Set y-axis limit with some padding\n", "    ax2.grid(True)\n", "    \n", "    # Format x-axis\n", "    plt.xlabel('Date')\n", "    plt.tight_layout()\n", "    \n", "    # Add max drawdown annotation\n", "    max_dd = drawdown.min()\n", "    max_dd_date = drawdown.idxmin()\n", "    ax2.annotate(f'Max DD: {max_dd:.2%}', \n", "                xy=(max_dd_date, max_dd),\n", "                xytext=(max_dd_date, max_dd * 0.7),\n", "                arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8),\n", "                fontsize=10,\n", "                bbox=dict(boxstyle=\"round,pad=0.3\", fc=\"white\", ec=\"black\", lw=1))\n", "    \n", "    return fig\n", "\n", "# Usage with your code:\n", "# After running your backtest:\n", "fig = plot_equity_and_drawdown(equity)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}