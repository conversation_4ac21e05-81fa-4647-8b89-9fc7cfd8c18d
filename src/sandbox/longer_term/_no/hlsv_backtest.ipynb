{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# HLSV (Hedged Long Short VIX) Strategy Backtest (Using Local VX1 Futures Data)\n", "\n", "This notebook implements and backtests the Hedged Long Short VIX (HLSV) strategy.\n", "\n", "**Key Change:** Uses a local CSV file (`cboe_vx1.csv`) for front-month VIX futures data instead of a `yfinance` proxy.\n", "\n", "**Strategy Overview:**\n", "\n", "1.  **Signal:** VIX term structure (Contango vs. Backwardation) calculated using VIX spot (`^VIX`) and front-month VIX futures (from CSV).\n", "2.  **Positioning:**\n", "    *   *Backwardation (Basis < 0):* Long VIX exposure (VIXY) + Long S&P 500 exposure (SPY) hedge.\n", "    *   *Contango (Basis >= 0):* Short VIX exposure (SVXY) + Short S&P 500 exposure (SPY) hedge.\n", "3.  **Instruments:** ETFs (VIXY, SVXY, SPY) and indices (^VIX).\n", "4.  **Hedging:** 50% allocation to the VIX component and 50% to the SPY hedge component.\n", "5.  **Execution:** Trades are initiated at the market open based on the signal determined at that open. Returns are measured Open-to-Open."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Configuration"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import yfinance as yf\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "\n", "# --- Configuration ---\n", "# Local VIX Futures CSV Path\n", "# IMPORTANT: Make sure 'cboe_vx1.csv' is in the same directory as this notebook, or provide the full path.\n", "vix_futures_csv_path = '/home/<USER>/w/backtest/market_data/static/cboe_vx1.csv'\n", "\n", "# Tickers for yfinance download (excluding VIX futures)\n", "vix_spot_ticker = '^VIX'\n", "long_vix_etf = 'VIXY'  # ProShares VIX Short-Term Futures ETF\n", "short_vix_etf = 'SVXY' # ProShares Short VIX Short-Term Futures ETF (Inverse exposure)\n", "sp500_etf = 'SPY'     # SPDR S&P 500 ETF Trust\n", "\n", "yf_tickers = [vix_spot_ticker, long_vix_etf, short_vix_etf, sp500_etf]\n", "\n", "# Dates (Define a broad range; the actual backtest period will be the intersection of available data)\n", "start_date = backtest_start_date_str = '2011-10-11' # Align with potential start of CSV data\n", "end_date = backtest_end_date_str = '2024-04-15'   # Go past potential end of CSV data\n", "# Define the specific backtest period you want to analyze (within the available intersection)\n", "# backtest_start_date_str = '2011-10-11' # Original paper start date\n", "# backtest_end_date_str = '2020-03-31'   # Original paper end date\n", "\n", "# Strategy Parameters\n", "brokerage_fee_pct = 0.0015  # 0.15% per trade\n", "slippage_pct = 0.0004      # 0.04% per trade\n", "annual_management_fee_pct = 0.0085  # 0.85% annually\n", "\n", "# --- Constants ---\n", "TRADING_DAYS_PER_YEAR = 252"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Acquisition & Merging\n", "\n", "Load the local VIX futures CSV and fetch the remaining data using `yfinance`. Then merge them into a single DataFrame."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["            VIX_Future\n", "Date                  \n", "2004-03-26       21.24\n", "2004-03-29       19.90\n", "2004-03-30       19.86\n", "2004-03-31       19.67\n", "2004-04-01       19.87\n", "datetime64[ns]\n", "YF.download() has changed argument auto_adjust default to True\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  4 of 4 completed"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Successfully downloaded yfinance data.\n", "Yfinance Data Sample:\n", "Ticker            SPY     SVXY      VIXY       ^VIX\n", "Date                                               \n", "2011-10-11  93.061309  12.0625  809440.0  33.950001\n", "2011-10-12  94.415701  12.7900  761280.0  31.570000\n", "2011-10-13  93.977312  13.0000  745600.0  31.990000\n", "2011-10-14  95.441278  13.8475  703440.0  29.070000\n", "2011-10-17  95.503915  13.7250  696000.0  30.350000\n", "\n", "Merged Data Sample:\n", "                  SPY     SVXY      VIXY   VIX_Spot  VIX_Future\n", "Date                                                           \n", "2011-10-11  93.061309  12.0625  809440.0  33.950001       33.65\n", "2011-10-12  94.415701  12.7900  761280.0  31.570000       33.10\n", "2011-10-13  93.977312  13.0000  745600.0  31.990000       31.80\n", "2011-10-14  95.441278  13.8475  703440.0  29.070000       30.75\n", "2011-10-17  95.503915  13.7250  696000.0  30.350000       29.60\n", "\n", "Merged Data Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 3116 entries, 2011-10-11 to 2024-04-12\n", "Data columns (total 5 columns):\n", " #   Column      Non-Null Count  Dtype  \n", "---  ------      --------------  -----  \n", " 0   SPY         3116 non-null   float64\n", " 1   SVXY        3116 non-null   float64\n", " 2   VIXY        3116 non-null   float64\n", " 3   VIX_Spot    3116 non-null   float64\n", " 4   VIX_Future  3116 non-null   float64\n", "dtypes: float64(5)\n", "memory usage: 146.1 KB\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "/tmp/ipykernel_1889642/1505614505.py:48: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  data_yf[vix_spot_ticker].fillna(method='ffill', inplace=True)\n", "/tmp/ipykernel_1889642/1505614505.py:48: FutureWarning: Series.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  data_yf[vix_spot_ticker].fillna(method='ffill', inplace=True)\n"]}], "source": ["# --- Load Local VIX Futures Data ---\n", "# ---------------------------------------------------------------------------\n", "#  VX1  ── read + fix index   (run immediately after pd.read_csv)\n", "# ---------------------------------------------------------------------------\n", "vx1 = pd.read_csv(\"/home/<USER>/w/backtest/market_data/static/cboe_vx1.csv\")\n", "\n", "# 1)  Unix‑seconds → UTC timestamp\n", "vx1[\"Date\"] = pd.to_datetime(vx1[\"time\"], unit=\"s\", utc=True)\n", "\n", "# 2)  Convert to US/Eastern, then shift to the *next* trading day\n", "vx1[\"Date\"] = (\n", "    vx1[\"Date\"]\n", "        .dt.tz_convert(\"US/Eastern\")   # 23:00 UTC  → 18:00 ET\n", "        .dt.floor(\"D\")                 # → 00:00 ET of the *same* day\n", "        + pd.<PERSON><PERSON><PERSON>(days=1)         # → next cash‑session day\n", ")\n", "\n", "# 3)  Make the index tz‑naive so it lines up with Yahoo data\n", "vx1.set_index(\"Date\", inplace=True)\n", "vx1.index = vx1.index.tz_localize(None)\n", "\n", "# 4)  Keep only the open price and rename the column\n", "vx1 = (\n", "    vx1[[\"open\"]]\n", "        .rename(columns={\"open\": \"VIX_Future\"})\n", "        .sort_index()\n", "        .loc[~vx1.index.duplicated()]      # drop any accidental dups\n", ")\n", "\n", "print(vx1.head())          # quick sanity‑check\n", "print(vx1.index.dtype)     # should say datetime64[ns]\n", "\n", "vix_future_df = vx1\n", "# ---------------------------------------------------------------------------\n", "#  now continue with your merge:\n", "#     data = data_yf.join(vx1, how=\"inner\")\n", "# ---------------------------------------------------------------------------\n", "\n", "    \n", "# --- Fetch yfinance Data ---\n", "data_yf = pd.DataFrame()\n", "if not vix_future_df.empty: # Only proceed if futures loaded successfully\n", "    try:\n", "        # Fetch Open prices for the remaining tickers\n", "        raw_data_yf = yf.download(yf_tickers, start=start_date, end=end_date, interval='1d')\n", "        data_yf = raw_data_yf['Open'].copy()\n", "        # Forward fill missing VIX spot opens if necessary\n", "        data_yf[vix_spot_ticker].fillna(method='ffill', inplace=True)\n", "        # Drop rows with any missing data points for essential ETFs\n", "        data_yf.dropna(subset=[long_vix_etf, short_vix_etf, sp500_etf, vix_spot_ticker], inplace=True)\n", "        data_yf.index = data_yf.index.normalize()\n", "        print(\"\\nSuccessfully downloaded yfinance data.\")\n", "        print(\"Yfinance Data Sample:\")\n", "        print(data_yf.head())\n", "\n", "    except Exception as e:\n", "        print(f\"Error downloading yfinance data: {e}\")\n", "\n", "# --- <PERSON><PERSON> ---\n", "data = pd.DataFrame()\n", "if not data_yf.empty and not vix_future_df.empty:\n", "    # Perform an inner join on the index (Date) to keep only overlapping dates\n", "    data = data_yf.join(vix_future_df, how='inner')\n", "    \n", "    # Rename columns for clarity\n", "    data.rename(columns={\n", "        vix_spot_ticker: 'VIX_Spot',\n", "        long_vix_etf: 'VIXY',\n", "        short_vix_etf: 'SVXY',\n", "        sp500_etf: 'SPY'\n", "        # VIX_Future is already named correctly from the CSV processing\n", "    }, inplace=True)\n", "    \n", "    # Filter to the desired backtest period *after* merging\n", "    data = data.loc[backtest_start_date_str : backtest_end_date_str]\n", "\n", "    if data.empty:\n", "        print(f\"\\nError: No overlapping data found for the period {backtest_start_date_str} to {backtest_end_date_str}.\")\n", "        print(f\"Check CSV date range ({vix_future_df.index.min()} to {vix_future_df.index.max()})\")\n", "        print(f\"and yfinance data range ({data_yf.index.min()} to {data_yf.index.max()}).\")\n", "    else:\n", "        print(\"\\nMerged Data Sample:\")\n", "        print(data.head())\n", "        print(\"\\nMerged Data Info:\")\n", "        data.info()\n", "else:\n", "    print(\"\\nSkipping merge because VIX futures or yfinance data failed to load.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Signal Calculation (Basis and Regime)\n", "\n", "Calculate the VIX term structure basis using VIX spot and the loaded front-month future data. Determine the market regime."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Data with Signal (Using VX1 Futures):\n", "             VIX_Spot  VIX_Future     Basis         Regime  \\\n", "Date                                                         \n", "2011-10-12  31.570000       33.10  0.048464       Contango   \n", "2011-10-13  31.990000       31.80 -0.005939  Backwardation   \n", "2011-10-14  29.070000       30.75  0.057792       Contango   \n", "2011-10-17  30.350000       29.60 -0.024712  Backwardation   \n", "2011-10-18  33.369999       33.25 -0.003596  Backwardation   \n", "\n", "           Signal_Trade_At_Open  \n", "Date                             \n", "2011-10-12        Backwardation  \n", "2011-10-13             Contango  \n", "2011-10-14        Backwardation  \n", "2011-10-17             Con<PERSON><PERSON>  \n", "2011-10-18        Backwardation  \n"]}], "source": ["if not data.empty:\n", "    # Ensure required columns exist\n", "    if 'VIX_Future' not in data.columns or 'VIX_Spot' not in data.columns:\n", "        print(\"Error: Required columns 'VIX_Future' or 'VIX_Spot' not found in merged data.\")\n", "        data = pd.DataFrame() # Prevent further execution\n", "    else:\n", "        # Calculate the Basis\n", "        data['Basis'] = (data['VIX_Future'] / data['VIX_Spot']) - 1\n", "\n", "        # Determine the Regime based on the Basis calculated at the Open\n", "        data['Regime'] = np.where(data['Basis'] < 0, 'Backwardation', 'Contango')\n", "\n", "        # Signal for trade execution at Today's Open is based on Yesterday's Regime\n", "        data['Signal_Trade_At_Open'] = data['Regime'].shift(1)\n", "\n", "        # Drop first row where shifted signal is NaN\n", "        data.dropna(subset=['Signal_Trade_At_Open'], inplace=True)\n", "\n", "        print(\"\\nData with Signal (Using VX1 Futures):\")\n", "        print(data[['VIX_Spot', 'VIX_Future', 'Basis', 'Regime', 'Signal_Trade_At_Open']].head())\n", "else:\n", "    print(\"Skipping signal calculation due to missing merged data.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Position Management and Return Calculation\n", "\n", "Simulate holding positions, calculate Open-to-Open log returns, and apply costs."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Results Sample (Log Returns):\n", "           Signal_Determined_Position  VIXY_LogRet  SVXY_LogRet  SPY_LogRet  \\\n", "Date                                                                          \n", "2011-10-12                   Contango    -0.020812     0.016286   -0.004654   \n", "2011-10-13              Backwardation    -0.058207     0.063155    0.015458   \n", "2011-10-14                   Contango    -0.010633    -0.008886    0.000656   \n", "2011-10-17              Backwardation     0.096459    -0.103541   -0.015281   \n", "2011-10-18              Backwardation    -0.046247     0.045031    0.018473   \n", "\n", "            HLSV_LogRet_Raw  HLSV_LogRet  TradeExecutionDay  \n", "Date                                                         \n", "2011-10-12         0.010470     0.006636               True  \n", "2011-10-13        -0.021374    -0.025208               True  \n", "2011-10-14        -0.004771    -0.008605               True  \n", "2011-10-17         0.040589     0.036755               True  \n", "2011-10-18        -0.013887    -0.017720               True  \n", "\n", "Number of trade days: 430\n"]}], "source": ["if not data.empty:\n", "    # Calculate daily log returns using Open prices\n", "    # ------------------------------------------------------------------\n", "    # 1.  Open‑to‑next‑open log returns  (what the position really earns)\n", "    # ------------------------------------------------------------------\n", "    opens = data[['VIXY', 'SVXY', 'SPY']]\n", "    log_returns = np.log(opens.shift(-1) / opens).iloc[:-1]   # last row is NaN\n", "\n", "    # ------------------------------------------------------------------\n", "    # 2.  Signal that governs the period t → t+1 is TODAY’s Regime\n", "    # ------------------------------------------------------------------\n", "    aligned_signal = data['Regime'].iloc[:-1]                 # ← no .shift(1)\n", "\n", "    # ------------------------------------------------------------------\n", "    # 3.  Build the strategy return (50 % VIX leg, 50 % SPY hedge)\n", "    # ------------------------------------------------------------------\n", "    vix_leg = np.where(aligned_signal == 'Backwardation',\n", "                    log_returns['VIXY'],          # long VIXY\n", "                    log_returns['SVXY'])          # short (actually long SVXY)\n", "\n", "    spy_leg = np.where(aligned_signal == 'Backwardation',\n", "                    0.5 * log_returns['SPY'],      # long SPY\n", "                    -0.5 * log_returns['SPY'])      # short SPY\n", "\n", "    strategy_log_returns = 0.5 * vix_leg + spy_leg\n", "    strategy_log_returns = pd.Series(strategy_log_returns,\n", "                                    index=log_returns.index,\n", "                                    name='HLSV_LogRet_Raw')\n", "\n", "    # ------------------------------------------------------------------\n", "    # 4.  Trade‑day lookup for cost deduction\n", "    # ------------------------------------------------------------------\n", "    trade_signals  = aligned_signal\n", "    trade_days     = trade_signals != trade_signals.shift(1)\n", "    trade_days.iloc[0] = True                               # first day is a trade\n", "\n", "\n", "    # --- Apply Costs ---\n", "    # Trade occurs at Open(t) if Signal_Trade_At_Open(t) != Signal_Trade_At_Open(t-1)\n", "    trade_signals = data['Signal_Trade_At_Open'].reindex(strategy_log_returns.index)\n", "    trade_days = trade_signals != trade_signals.shift(1)\n", "    trade_days.iloc[0] = True # Assume first day involves a trade\n", "\n", "    transaction_cost_pct = brokerage_fee_pct + slippage_pct\n", "    total_transaction_cost_impact = 2 * transaction_cost_pct\n", "    daily_management_fee = annual_management_fee_pct / TRADING_DAYS_PER_YEAR\n", "\n", "    strategy_log_returns_costs = strategy_log_returns.copy()\n", "    strategy_log_returns_costs[trade_days] = strategy_log_returns_costs[trade_days] - total_transaction_cost_impact\n", "    strategy_log_returns_costs = strategy_log_returns_costs - daily_management_fee\n", "\n", "    # Final results DataFrame\n", "    results = pd.DataFrame({\n", "        'Signal_Determined_Position': aligned_signal,\n", "        'VIXY_LogRet': log_returns['VIXY'],\n", "        'SVXY_LogRet': log_returns['SVXY'],\n", "        'SPY_LogRet': log_returns['SPY'],\n", "        'HLSV_LogRet_Raw': strategy_log_returns,\n", "        'HLSV_LogRet': strategy_log_returns_costs,\n", "        'TradeExecutionDay': trade_days\n", "    })\n", "    \n", "    # Drop any potential NaNs introduced during calculation\n", "    results.dropna(subset=['HLSV_LogRet'], inplace=True)\n", "\n", "    print(\"\\nResults Sample (Log Returns):\")\n", "    print(results.head())\n", "    print(f\"\\nNumber of trade days: {results['TradeExecutionDay'].sum()}\")\n", "else:\n", "    print(\"Skipping return calculation due to missing data.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Performance Analysis\n", "\n", "Calculate key performance metrics and plot the strategy's equity curve against the SPY benchmark."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Performance Summary (Using VX1 Futures) ---\n", "Period Analyzed: 2011-10-12 to 2024-04-11 (12.50 years)\n", "Strategy: HLSV (After Costs)\n", "  Total Return: -49.10%\n", "  Annualized Return (Geometric): -5.26%\n", "  Annualized Volatility: 25.02%\n", "  <PERSON> (Rf=0%): -0.21\n", "  Maximum Drawdown: -82.05%\n", "\n", "Benchmark: SPY (Buy & Hold)\n", "  Total Return: 437.95%\n", "  Annualized Return (Geometric): 14.41%\n", "  Annualized Volatility: 16.00%\n", "  <PERSON> (Rf=0%): 0.90\n", "  Maximum Drawdown: -32.05%\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1400x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if 'results' in locals() and not results.empty:\n", "    # Calculate Cumulative Returns (using returns *after* costs)\n", "    results['HLSV_CumLogRet'] = results['HLSV_LogRet'].cumsum()\n", "    results['HLSV_CumRet'] = np.exp(results['HLSV_CumLogRet'])\n", "\n", "    # Benchmark SPY\n", "    # Realign SPY returns to the final results index\n", "    spy_log_ret_aligned = results['SPY_LogRet']\n", "    results['SPY_CumLogRet'] = spy_log_ret_aligned.cumsum()\n", "    results['SPY_CumRet'] = np.exp(results['SPY_CumLogRet'])\n", "\n", "    # --- Performance Metrics ---\n", "    start_value = 1\n", "    end_value_halsv = results['HLSV_CumRet'].iloc[-1]\n", "    end_value_spy = results['SPY_CumRet'].iloc[-1]\n", "\n", "    total_return_halsv = end_value_halsv - start_value\n", "    total_return_spy = end_value_spy - start_value\n", "\n", "    years = (results.index[-1] - results.index[0]).days / 365.25\n", "    # Handle cases where years might be zero or negative if date range is small\n", "    if years <= 0:\n", "        annualized_return_halsv = 0\n", "        annualized_return_spy = 0\n", "    else:\n", "        annualized_return_halsv = (end_value_halsv / start_value)**(1/years) - 1\n", "        annualized_return_spy = (end_value_spy / start_value)**(1/years) - 1\n", "\n", "    annualized_volatility_halsv = results['HLSV_LogRet'].std() * np.sqrt(TRADING_DAYS_PER_YEAR)\n", "    annualized_volatility_spy = spy_log_ret_aligned.std() * np.sqrt(TRADING_DAYS_PER_YEAR)\n", "\n", "    risk_free_rate = 0\n", "    sharpe_ratio_halsv = (annualized_return_halsv - risk_free_rate) / annualized_volatility_halsv if annualized_volatility_halsv != 0 else 0\n", "    sharpe_ratio_spy = (annualized_return_spy - risk_free_rate) / annualized_volatility_spy if annualized_volatility_spy != 0 else 0\n", "\n", "    def calculate_max_drawdown(cumulative_returns):\n", "        if cumulative_returns.empty:\n", "            return 0\n", "        # Ensure input starts from 1\n", "        if cumulative_returns.iloc[0] != 1:\n", "             cumulative_returns = cumulative_returns / cumulative_returns.iloc[0]\n", "        # Prepend 1.0 if the series doesn't start at the conceptual beginning\n", "        if cumulative_returns.index[0] > pd.Timestamp(backtest_start_date_str):\n", "             start_row = pd.Series([1.0], index=[cumulative_returns.index[0] - pd.Timedelta(days=1)])\n", "             cumulative_returns = pd.concat([start_row, cumulative_returns])\n", "        elif cumulative_returns.iloc[0] != 1.0:\n", "             # If it starts at the right date but not 1, rebase\n", "             cumulative_returns = cumulative_returns / cumulative_returns.iloc[0]\n", "\n", "        rolling_max = cumulative_returns.cummax()\n", "        drawdown = (cumulative_returns - rolling_max) / rolling_max\n", "        max_drawdown = drawdown.min()\n", "        return max_drawdown if not pd.isna(max_drawdown) else 0\n", "\n", "    max_drawdown_halsv = calculate_max_drawdown(results['HLSV_CumRet'].copy())\n", "    max_drawdown_spy = calculate_max_drawdown(results['SPY_CumRet'].copy())\n", "\n", "    # --- Output ---\n", "    print(\"\\n--- Performance Summary (Using VX1 Futures) ---\")\n", "    print(f\"Period Analyzed: {results.index[0].date()} to {results.index[-1].date()} ({years:.2f} years)\")\n", "    print(f\"Strategy: HLSV (After Costs)\")\n", "    print(f\"  Total Return: {total_return_halsv:.2%}\")\n", "    print(f\"  Annualized Return (Geometric): {annualized_return_halsv:.2%}\")\n", "    print(f\"  Annualized Volatility: {annualized_volatility_halsv:.2%}\")\n", "    print(f\"  Sharpe <PERSON> (Rf=0%): {sharpe_ratio_halsv:.2f}\")\n", "    print(f\"  Maximum Drawdown: {max_drawdown_halsv:.2%}\")\n", "    print(f\"\\nBenchmark: SPY (Buy & Hold)\")\n", "    print(f\"  Total Return: {total_return_spy:.2%}\")\n", "    print(f\"  Annualized Return (Geometric): {annualized_return_spy:.2%}\")\n", "    print(f\"  Annualized Volatility: {annualized_volatility_spy:.2%}\")\n", "    print(f\"  <PERSON> (Rf=0%): {sharpe_ratio_spy:.2f}\")\n", "    print(f\"  Maximum Drawdown: {max_drawdown_spy:.2%}\")\n", "\n", "    # --- Plotting ---\n", "    plt.style.use('seaborn-v0_8-darkgrid')\n", "    fig, ax = plt.subplots(figsize=(14, 7))\n", "\n", "    ax.plot(results.index, results['HLSV_CumRet'], label='HLSV Strategy (After Costs)', color='mediumblue', linewidth=2)\n", "    ax.plot(results.index, results['SPY_CumRet'], label='SPY Benchmark', color='orangered', linestyle='--', linewidth=2)\n", "\n", "    ax.set_title('HLSV Strategy (VX1 Data) vs. SPY Benchmark Cumulative Returns', fontsize=16)\n", "    ax.set_xlabel('Date', fontsize=12)\n", "    ax.set_ylabel('Cumulative Return (Log Scale)', fontsize=12)\n", "    ax.set_yscale('log')\n", "    ax.legend(fontsize=11)\n", "    ax.grid(True, which='both', linestyle='--', linewidth=0.5)\n", "    plt.xticks(fontsize=10, rotation=45)\n", "    plt.yticks(fontsize=10)\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"Skipping performance analysis due to missing results data.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}