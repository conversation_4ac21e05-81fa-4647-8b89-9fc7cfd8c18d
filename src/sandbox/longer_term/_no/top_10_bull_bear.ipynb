{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#from datetime import datetime\n", "import pytz\n", "from datetime import datetime\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2010, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 5, 2))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["#  Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(start_date=datetime(2010, 1, 1))\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"\n", "    Fetches daily market data for a specified ticker and date range.\n", "    \n", "    Parameters:\n", "    -----------\n", "    ticker : str\n", "        The stock ticker symbol (e.g., 'AAPL', 'MSFT').\n", "    start_dt : datetime\n", "        Start date for the data request.\n", "    end_dt : datetime\n", "        End date for the data request.\n", "    \n", "    Returns:\n", "    --------\n", "    pandas.DataFrame\n", "        A DataFrame containing daily market data with the following structure:\n", "        - Index: DatetimeIndex with timezone information (e.g., '2020-01-02 00:00:00-05:00')\n", "        - Columns:\n", "          * open (float64): Opening price for the day\n", "          * high (float64): Highest price for the day\n", "          * low (float64): Lowest price for the day\n", "          * close (float64): Closing price for the day\n", "          * volume (float64): Trading volume for the day\n", "          \n", "    Notes:\n", "    ------\n", "    The function uses the configured daily_market_data instance to retrieve the data.\n", "    Data is returned with a daily interval (86400 seconds).\n", "    \n", "    Example:\n", "    --------\n", "    >>> import datetime\n", "    >>> start = datetime.datetime(2023, 1, 1)\n", "    >>> end = datetime.datetime(2023, 12, 31)\n", "    >>> df = get_daily_data('AAPL', start, end)\n", "    \"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        print(\"Exception\", e)\n", "        return None\n", "    \n", "    \n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 3774/3774 [01:14<00:00, 50.51it/s]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Strategy CAGR: -4.41%\n", "SPY CAGR: 11.59%\n", "Strategy Sharpe Ratio: -0.01\n", "SPY Sharpe Ratio: 0.73\n", "Strategy Max Drawdown: -66.17%\n", "SPY Max Drawdown: -34.10%\n"]}], "source": ["\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm # tqdm is good for loops\n", "\n", "# %%\n", "# Load S&P 500 Historical Constituents (assuming sp500_hist_df and get_sp500_constituents_for_date are defined)\n", "constituents_file = 'S&P 500 Historical Components & Changes(03-10-2025).csv'\n", "sp500_hist_df = pd.read_csv(constituents_file)\n", "sp500_hist_df['date'] = pd.to_datetime(sp500_hist_df['date'])\n", "sp500_hist_df = sp500_hist_df.set_index('date')\n", "sp500_hist_df['tickers'] = sp500_hist_df['tickers'].apply(lambda x: sorted(x.split(',')) if isinstance(x, str) else [])\n", "\n", "def get_sp500_constituents_for_date(target_date, constituents_df):\n", "    if not isinstance(target_date, pd.Timestamp): target_date = pd.to_datetime(target_date)\n", "    relevant_df = constituents_df[constituents_df.index <= target_date]\n", "    if relevant_df.empty: return constituents_df.iloc[0]['tickers'] if not constituents_df.empty else []\n", "    return relevant_df.iloc[-1]['tickers']\n", "\n", "\n", "# %%\n", "# (Previous imports and function definitions)\n", "\n", "# Global Parameters\n", "# Define the target timezone (ensure this is defined before use)\n", "EASTERN_TZ = 'US/Eastern'\n", "\n", "# Make BACKTEST_START_DATE and BACKTEST_END_DATE timezone-aware\n", "BACKTEST_START_DATE = pd.Timestamp(datetime(2000, 1, 1), tz=EASTERN_TZ)\n", "BACKTEST_END_DATE = pd.Timestamp(datetime(2024, 12, 31), tz=EASTERN_TZ)\n", "\n", "SPY_TICKER = 'SPY'\n", "MOVING_AVERAGE_WINDOW = 200\n", "PRE_FILTER_LOOKBACK_YEARS = 10\n", "SCORE_LOOKBACK_YEARS = 8\n", "TOP_N_PREFILTER = 50\n", "TOP_N_FINAL_SELECTION = 10\n", "\n", "# %%\n", "# Load S&P 500 Historical Constituents AND LOCALIZE ITS INDEX\n", "constituents_file = 'S&P 500 Historical Components & Changes(03-10-2025).csv'\n", "sp500_hist_df = pd.read_csv(constituents_file)\n", "sp500_hist_df['date'] = pd.to_datetime(sp500_hist_df['date']) # Creates naive datetimes\n", "sp500_hist_df = sp500_hist_df.set_index('date')\n", "\n", "# This assumes the dates in your CSV represent US/Eastern local time.\n", "# If they are UTC, use .tz_localize('UTC').tz_convert(EASTERN_TZ)\n", "sp500_hist_df.index = sp500_hist_df.index.tz_localize(EASTERN_TZ)\n", "\n", "# The get_sp500_constituents_for_date function itself should be fine,\n", "# as it will now receive a tz-aware target_date and compare it with a tz-aware constituents_df.index.\n", "def get_sp500_constituents_for_date(target_date, constituents_df):\n", "    # Ensure target_date is a pandas Timestamp (it should be if coming from selection_cutoff_date)\n", "    if not isinstance(target_date, pd.Timestamp):\n", "        # This conversion should ideally ensure tz-awareness if needed,\n", "        # but it's better if target_date is already correctly tz-aware.\n", "        target_date = pd.Timestamp(target_date, tz=constituents_df.index.tz)\n", "\n", "    # Now both constituents_df.index and target_date should be tz-aware 'US/Eastern'\n", "    relevant_df = constituents_df[constituents_df.index <= target_date]\n", "    if relevant_df.empty:\n", "        # Handle edge case where target_date is before any date in constituents_df\n", "        if not constituents_df.empty and target_date < constituents_df.index.min():\n", "             # print(f\"Warning: target_date {target_date} is before earliest constituents data. Using earliest available.\")\n", "             return constituents_df.iloc[0]['tickers']\n", "        return [] # Or handle as error\n", "    return relevant_df.iloc[-1]['tickers']\n", "\n", "# %%\n", "# --- Prepare SPY data for regime definition ---\n", "spy_data_fetch_start = BACKTEST_START_DATE - pd.DateOffset(years=PRE_FILTER_LOOKBACK_YEARS) - pd.DateOffset(days=MOVING_AVERAGE_WINDOW + 260)\n", "spy_data_full = get_daily_data(SPY_TICKER, spy_data_fetch_start, BACKTEST_END_DATE)\n", "\n", "spy_data_full['200_MA'] = spy_data_full['close'].rolling(window=MOVING_AVERAGE_WINDOW).mean()\n", "spy_data_full['spy_regime'] = np.where(spy_data_full['close'] > spy_data_full['200_MA'], 1, -1)\n", "spy_daily_regime_info = spy_data_full[['spy_regime']].dropna()\n", "\n", "# (The rest of your select_new_basket function and the main loop should follow)\n", "\n", "# (The rest of your code, including select_new_basket and the main loop, should now work correctly\n", "# with these timezone-aware date boundaries, as Pandas operations like indexing and DateOffset\n", "# will respect the timezone information.)\n", "\n", "# %%\n", "# --- Helper function for the selection logic (called on regime change) ---\n", "def select_new_basket(selection_cutoff_date, new_regime_for_basket_selection, # The regime that has just started\n", "                      constituents_df_hist, spy_regime_history_for_scoring_calc):\n", "    \"\"\"\n", "    Selects the top N stocks for the new_regime_for_basket_selection.\n", "    All data used for selection is up to and including selection_cutoff_date.\n", "    \"\"\"\n", "    pre_filter_end = selection_cutoff_date\n", "    pre_filter_start = pre_filter_end - pd.DateOffset(years=PRE_FILTER_LOOKBACK_YEARS)\n", "    score_period_end = selection_cutoff_date\n", "    score_period_start = score_period_end - pd.DateOffset(years=SCORE_LOOKBACK_YEARS)\n", "\n", "    initial_constituents = get_sp500_constituents_for_date(selection_cutoff_date, constituents_df_hist)\n", "    if not initial_constituents: return []\n", "\n", "    # Pre-filter to Top N (e.g., 50)\n", "    top_prefilter_candidates = []\n", "    for ticker in initial_constituents:\n", "        stock_data_Nyr = get_daily_data(ticker, pre_filter_start, pre_filter_end)\n", "        # Assuming data is clean and sufficient for calculation\n", "        if len(stock_data_Nyr) >= 0.7 * 252 * PRE_FILTER_LOOKBACK_YEARS and not stock_data_Nyr['close'].empty: # Min data check\n", "            start_price = stock_data_Nyr['close'].iloc[0]\n", "            end_price = stock_data_Nyr['close'].iloc[-1]\n", "            if start_price > 0: # Avoid division by zero or issues with bad data\n", "                total_return_Nyr = (end_price - start_price) / start_price\n", "                top_prefilter_candidates.append({'ticker': ticker, 'total_return': total_return_Nyr})\n", "    \n", "    selection_universe_tickers = []\n", "    if top_prefilter_candidates:\n", "        top_prefilter_df = pd.DataFrame(top_prefilter_candidates)\n", "        selection_universe_tickers = top_prefilter_df.nlargest(TOP_N_PREFILTER, 'total_return')['ticker'].tolist()\n", "\n", "    if not selection_universe_tickers: return []\n", "\n", "    # Calculate Bull/Bear Scores\n", "    stock_scores_list = []\n", "    # Use spy_regime_history_for_scoring_calc which is already sliced for the score_period\n", "    relevant_spy_regime_for_scoring = spy_regime_history_for_scoring_calc.loc[score_period_start:score_period_end]\n", "\n", "    for ticker in selection_universe_tickers:\n", "        stock_data_score = get_daily_data(ticker, score_period_start, score_period_end)\n", "        if len(stock_data_score) >= 0.7 * 252 * SCORE_LOOKBACK_YEARS and not stock_data_score['close'].empty: # Min data check\n", "            stock_data_score['daily_return'] = stock_data_score['close'].pct_change().fillna(0) # fillna(0) for first day\n", "            merged_data = stock_data_score.join(relevant_spy_regime_for_scoring, how='inner')\n", "            \n", "            bull_score = merged_data[merged_data['spy_regime'] == 1]['daily_return'].mean()\n", "            bear_score = merged_data[merged_data['spy_regime'] == -1]['daily_return'].mean()\n", "            \n", "            stock_scores_list.append({\n", "                'ticker': ticker,\n", "                'bull_score': bull_score if pd.notna(bull_score) else -np.inf, # Handle cases with no days in regime\n", "                'bear_score': bear_score if pd.notna(bear_score) else -np.inf\n", "            })\n", "\n", "    if not stock_scores_list: return []\n", "    stock_scores_df = pd.DataFrame(stock_scores_list)\n", "\n", "    # Select Top N Final Stocks\n", "    if new_regime_for_basket_selection == 1: # Bull basket\n", "        return stock_scores_df.nlargest(TOP_N_FINAL_SELECTION, 'bull_score')['ticker'].tolist()\n", "    elif new_regime_for_basket_selection == -1: # Bear basket\n", "        return stock_scores_df.nlargest(TOP_N_FINAL_SELECTION, 'bear_score')['ticker'].tolist()\n", "    return []\n", "\n", "# %%\n", "# --- Main Daily Backtesting Loop ---\n", "portfolio_daily_log = []\n", "current_held_tickers = []\n", "# Regime governing *yesterday's* investment choice, to detect change for *today's* choice\n", "previous_investment_regime_signal = 0 # Undefined initially\n", "\n", "# Iterate through each trading day in the defined backtest period\n", "# Ensure spy_daily_regime_info covers dates slightly before BACKTEST_START_DATE for the first iteration\n", "loop_start_date = spy_daily_regime_info.index[spy_daily_regime_info.index >= BACKTEST_START_DATE][0]\n", "\n", "for current_simulation_day in tqdm(spy_daily_regime_info.loc[loop_start_date:BACKTEST_END_DATE].index):\n", "    # selection_cutoff_date is the day whose close determines the regime FOR current_simulation_day's investment\n", "    selection_cutoff_date = current_simulation_day - pd.Timedelta(days=1)\n", "    \n", "    # Ensure selection_cutoff_date is valid and exists in our regime data\n", "    if selection_cutoff_date not in spy_daily_regime_info.index:\n", "        # This happens if current_simulation_day is the first day and selection_cutoff_date is before spy_daily_regime_info starts\n", "        # Or if it's a non-trading day (though loop iterates on trading_days_in_backtest)\n", "        # For \"clean code\", assume data is available. If not, maintain previous state or hold cash.\n", "        # Here, we effectively skip taking action if prior day's regime is unknown.\n", "        # The portfolio return will be 0 if current_held_tickers is empty.\n", "        current_investment_regime_signal = previous_investment_regime_signal # Carry over\n", "    else:\n", "        current_investment_regime_signal = spy_daily_regime_info.loc[selection_cutoff_date, 'spy_regime']\n", "\n", "    # If regime has changed OR it's effectively the first day (previous_investment_regime_signal is 0)\n", "    # AND current_investment_regime_signal is not 0 (i.e., regime is now defined)\n", "    if (current_investment_regime_signal != previous_investment_regime_signal) and (current_investment_regime_signal != 0) :\n", "        # print(f\"{current_simulation_day.date()}: Regime changed/initialized to {current_investment_regime_signal}. Selecting new basket.\")\n", "        \n", "        # Slice the historical SPY regime data needed for scoring (score_lookback_yrs)\n", "        # Data up to selection_cutoff_date is used for selection\n", "        score_period_spy_hist_start = selection_cutoff_date - pd.DateOffset(years=SCORE_LOOKBACK_YEARS) - pd.DateOffset(days=252) # extra buffer for safety\n", "        spy_regime_hist_for_selection = spy_daily_regime_info.loc[score_period_spy_hist_start : selection_cutoff_date]\n", "\n", "        current_held_tickers = select_new_basket(\n", "            selection_cutoff_date=selection_cutoff_date,\n", "            new_regime_for_basket_selection=current_investment_regime_signal,\n", "            constituents_df_hist=sp500_hist_df,\n", "            spy_regime_history_for_scoring_calc=spy_regime_hist_for_selection\n", "        )\n", "        # if not current_held_tickers:\n", "            # print(f\"  -> No tickers selected for {current_simulation_day.date()}. Holding cash-equivalent.\")\n", "\n", "    # Simulate holding the `current_held_tickers` for `current_simulation_day`\n", "    # Calculate returns based on price change from previous close to current_simulation_day close\n", "    daily_portfolio_return = 0.0\n", "    if current_held_tickers:\n", "        returns_for_current_day = []\n", "        for ticker in current_held_tickers:\n", "            # Fetch data for current_simulation_day and one day prior for pct_change\n", "            # Using selection_cutoff_date (yesterday) and current_simulation_day (today)\n", "            stock_price_data = get_daily_data(ticker, selection_cutoff_date, current_simulation_day)\n", "            if len(stock_price_data) == 2 and 'close' in stock_price_data.columns: # Ensure we have two days of prices\n", "                # Return for current_simulation_day is (price_today / price_yesterday) - 1\n", "                ticker_return = stock_price_data['close'].pct_change().iloc[-1]\n", "                returns_for_current_day.append(ticker_return if pd.notna(ticker_return) else 0.0) # if somehow NaN, treat as 0\n", "            # else:\n", "                # print(f\"  -> Insufficient data for {ticker} on {current_simulation_day.date()} for return calc.\")\n", "\n", "        if returns_for_current_day:\n", "            daily_portfolio_return = np.mean(returns_for_current_day) # Equal weighted\n", "\n", "    portfolio_daily_log.append({\n", "        'date': current_simulation_day,\n", "        'invest_regime': current_investment_regime_signal, # Regime that dictated today's holding\n", "        'held_tickers': current_held_tickers.copy(),\n", "        'daily_return': daily_portfolio_return\n", "    })\n", "    \n", "    previous_investment_regime_signal = current_investment_regime_signal\n", "\n", "# %%\n", "# --- Post-Processing and Analysis ---\n", "if portfolio_daily_log:\n", "    log_df = pd.DataFrame(portfolio_daily_log).set_index('date')\n", "    strategy_returns_daily = log_df['daily_return'].fillna(0.0) # Ensure no NaNs in final returns\n", "\n", "    # Align SPY returns for comparison (using the same dates as strategy)\n", "    spy_returns_for_comparison = spy_data_full['close'].pct_change().reindex(strategy_returns_daily.index).fillna(0.0)\n", "    \n", "    # Cumulative Returns\n", "    strategy_cumulative_returns = (1 + strategy_returns_daily).cumprod()\n", "    spy_cumulative_returns = (1 + spy_returns_for_comparison).cumprod()\n", "\n", "    plt.figure(figsize=(14, 7))\n", "    strategy_cumulative_returns.plot(label='Strategy')\n", "    spy_cumulative_returns.plot(label='SPY Buy & Hold')\n", "    plt.title('Strategy vs. SPY - Cumulative Returns (Daily Regime Check)')\n", "    plt.xlabel(\"Date\")\n", "    plt.ylabel(\"Cumulative Returns\")\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.show()\n", "\n", "    # CAGR\n", "    if not strategy_returns_daily.empty:\n", "        total_days = (strategy_returns_daily.index[-1] - strategy_returns_daily.index[0]).days\n", "        years = total_days / 365.25 if total_days > 0 else 1 # Avoid division by zero if very short period\n", "        \n", "        strategy_cagr = (strategy_cumulative_returns.iloc[-1])**(1/years) - 1 if years > 0 and len(strategy_cumulative_returns)>0 else 0.0\n", "        spy_cagr = (spy_cumulative_returns.iloc[-1])**(1/years) - 1 if years > 0 and len(spy_cumulative_returns)>0 else 0.0\n", "        print(f\"Strategy CAGR: {strategy_cagr:.2%}\")\n", "        print(f\"SPY CAGR: {spy_cagr:.2%}\")\n", "\n", "        # <PERSON> (approximate, RFR=0)\n", "        strategy_sharpe = (strategy_returns_daily.mean() * 252) / (strategy_returns_daily.std() * np.sqrt(252)) if strategy_returns_daily.std() != 0 else 0.0\n", "        spy_sharpe = (spy_returns_for_comparison.mean() * 252) / (spy_returns_for_comparison.std() * np.sqrt(252)) if spy_returns_for_comparison.std() != 0 else 0.0\n", "        print(f\"Strategy Sharpe Ratio: {strategy_sharpe:.2f}\")\n", "        print(f\"SPY <PERSON>: {spy_sharpe:.2f}\")\n", "        \n", "        # Max Drawdown\n", "        strategy_roll_max = strategy_cumulative_returns.cummax()\n", "        strategy_drawdown = (strategy_cumulative_returns / strategy_roll_max - 1.0)\n", "        strategy_max_drawdown = strategy_drawdown.min() if not strategy_drawdown.empty else 0.0\n", "\n", "        spy_roll_max = spy_cumulative_returns.cummax()\n", "        spy_drawdown_spy = (spy_cumulative_returns / spy_roll_max - 1.0)\n", "        spy_max_drawdown = spy_drawdown_spy.min() if not spy_drawdown_spy.empty else 0.0\n", "        print(f\"Strategy Max Drawdown: {strategy_max_drawdown:.2%}\")\n", "        print(f\"SPY Max Drawdown: {spy_max_drawdown:.2%}\")\n", "else:\n", "    print(\"No portfolio log generated. Backtest did not run or produce results.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}