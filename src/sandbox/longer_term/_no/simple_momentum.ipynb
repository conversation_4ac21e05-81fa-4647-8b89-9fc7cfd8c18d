{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "from tqdm.notebook import tqdm \n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(preload_months=12*5, source=\"polygon\")  # Only available since 2020-04-01\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"Fetches daily data using the configured daily_market_data instance.\"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        print(\"Exception\", e)\n", "        return None\n", "\n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from tickers.ticker_helpers import TickerInfoStore\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2024, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 1, 1))\n", "\n", "ticker_info = TickerInfoStore()\n", "ticker_df = ticker_info.load_ticker_data(start_dt)\n", "if ticker_df.empty:\n", "    ticker_df = ticker_info.fetch_ticker_data(start_dt)\n", "\n", "min_market_cap = 50_000_000  # 5M\n", "# max_market_cap = 500_000_000  # 500M\n", "\n", "# Fetch all delisted tickers up to end_dt\n", "# delisted_ticker_df = ticker_info.fetch_ticker_data(end_dt, active=False)\n", "\n", "# # Filter to only include tickers delisted between start_dt and end_dt\n", "# delisted_ticker_df['delisted_date'] = pd.to_datetime(delisted_ticker_df['delisted_utc']).dt.date\n", "\n", "# # Keep only tickers delisted within the date range\n", "# delisted_in_range_df = delisted_ticker_df[\n", "#     (delisted_ticker_df['delisted_date'] >= start_dt.date()) & \n", "#     (delisted_ticker_df['delisted_date'] <= end_dt.date())\n", "# ]\n", "\n", "market_cap_filter = (\n", "            ticker_df[\"market_cap\"].notna() &\n", "            (ticker_df[\"market_cap\"] >= min_market_cap)\n", "            # & (ticker_df[\"market_cap\"] <= max_market_cap)\n", "        )\n", "active_ticker_universe = set(ticker_df.loc[market_cap_filter, \"ticker\"])\n", "\n", "# delisted_tickers = set(delisted_in_range_df[\"ticker\"])\n", "ticker_universe = active_ticker_universe  # .union(delisted_tickers)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def generate_momentum_universe(ticker_universe, start_dt, end_dt, min_pct_change=30):\n", "    \"\"\"\n", "    Generate a momentum universe by calculating top performers over different time periods.\n", "    \n", "    Parameters:\n", "    -----------\n", "    ticker_universe : set\n", "        Set of tickers to analyze\n", "    start_dt : datetime\n", "        Start date for the analysis period\n", "    end_dt : datetime\n", "        End date for the analysis period\n", "    min_pct_change : float, optional\n", "        Minimum percentage change required for inclusion, default 30%\n", "        \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary with dates (not timestamps) as keys and DataFrames of top performers as values\n", "    \"\"\"\n", "    # Track performance for each date in the analysis period\n", "    momentum_universe = {}\n", "    dates = pd.date_range(start=start_dt, end=end_dt, freq='B')  # Business days\n", "    \n", "    # Process each ticker\n", "    all_results = []\n", "    for ticker in tqdm(ticker_universe):\n", "        # Get daily data for the ticker with additional lookback for calculations\n", "        lookback_months = 13  # Need extra month for 12-month lookback\n", "        lookback_dt = start_dt - pd.DateOffset(months=lookback_months)\n", "        df = get_daily_data(ticker, lookback_dt, end_dt)\n", "        \n", "        if df is None or df.empty or len(df) < 30:  # Skip tickers with insufficient data\n", "            continue\n", "        \n", "        # Calculate performance metrics for different timeframes\n", "        df['price'] = df['close']\n", "        df['ticker'] = ticker\n", "        \n", "        # Calculate ROC (Rate of Change) for different periods\n", "        df['roc_1m'] = df['price'].pct_change(21) * 100  # ~1 month (21 trading days)\n", "        df['roc_3m'] = df['price'].pct_change(63) * 100  # ~3 months (63 trading days)\n", "        df['roc_6m'] = df['price'].pct_change(126) * 100  # ~6 months (126 trading days)\n", "        df['roc_12m'] = df['price'].pct_change(252) * 100  # ~12 months (252 trading days)\n", "        \n", "        # Only include dates within our analysis period\n", "        df = df[(df.index >= start_dt) & (df.index <= end_dt)]\n", "        \n", "        # Filter for minimum percent change in any of the periods\n", "        mask = (\n", "            (df['roc_1m'] >= min_pct_change) | \n", "            (df['roc_3m'] >= min_pct_change) | \n", "            (df['roc_6m'] >= min_pct_change) | \n", "            (df['roc_12m'] >= min_pct_change)\n", "        )\n", "        \n", "        filtered_df = df[mask]\n", "        if not filtered_df.empty:\n", "            all_results.append(filtered_df)\n", "    \n", "    if not all_results:\n", "        return {}\n", "    \n", "    # Combine all results\n", "    combined_results = pd.concat(all_results)\n", "    \n", "    # Create daily rankings for each performance period\n", "    for timestamp in dates:\n", "        date_data = combined_results[combined_results.index == timestamp]\n", "        if date_data.empty:\n", "            continue\n", "        \n", "        # Extract date as a Python date object to use as key\n", "        date_key = timestamp.date()\n", "        \n", "        # Initialize dictionary for this date if not exists\n", "        if date_key not in momentum_universe:\n", "            momentum_universe[date_key] = {}\n", "        \n", "        # Rank stocks for each timeframe\n", "        for period in ['roc_1m', 'roc_3m', 'roc_6m', 'roc_12m']:\n", "            period_df = date_data.sort_values(by=period, ascending=False)\n", "            period_df = period_df[period_df[period] >= min_pct_change]\n", "            \n", "            if not period_df.empty:\n", "                # Add rank column\n", "                period_df[f'{period}_rank'] = range(1, len(period_df) + 1)\n", "                \n", "                # Store top 100 performers\n", "                top_performers = period_df.head(100)\n", "                if not top_performers.empty:\n", "                    momentum_universe[date_key][period] = top_performers\n", "    \n", "    return momentum_universe"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, time, timedelta\n", "import pytz\n", "\n", "def high_beta_momentum_strategy(ticker_universe, start_dt, end_dt, \n", "                               min_pct_change=30, \n", "                               consolidation_threshold=0.03,\n", "                               lookback_days=30):\n", "    \"\"\"\n", "    Implements a high beta momentum strategy:\n", "    1. Identify top momentum stocks based on rolling monthly returns\n", "    2. Wait for consolidation (20MA gets closer to 10MA)\n", "    3. Enter when price breaks 30-day high in first 30 min of market open\n", "    4. Stop loss at low of the day\n", "    \n", "    Parameters:\n", "    -----------\n", "    ticker_universe : set\n", "        Set of tickers to analyze\n", "    start_dt : datetime\n", "        Start date for the analysis period\n", "    end_dt : datetime\n", "        End date for the analysis period\n", "    min_pct_change : float\n", "        Minimum percentage change required for momentum inclusion\n", "    consolidation_threshold : float\n", "        Maximum percentage difference between 10MA and 20MA to consider as consolidation\n", "    lookback_days : int\n", "        Number of days to look back for high calculation\n", "    \n", "    Returns:\n", "    --------\n", "    pandas.DataFrame\n", "        DataFrame with trading signals and performance\n", "    \"\"\"\n", "    eastern_tz = pytz.timezone(\"US/Eastern\")\n", "    market_open = time(9, 30, 0)  # 9:30 AM ET\n", "    market_open_plus_30 = time(10, 0, 0)  # 10:00 AM ET (30 min after open)\n", "    \n", "    # Get momentum universe\n", "    momentum_universe = generate_momentum_universe(\n", "        ticker_universe=ticker_universe,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        min_pct_change=min_pct_change\n", "    )\n", "    \n", "    # Store trade signals\n", "    all_signals = []\n", "    \n", "    # Process each date in the momentum universe\n", "    for date, period_data in momentum_universe.items():\n", "        # Convert date to datetime for processing\n", "        current_date = datetime.combine(date, datetime.min.time())\n", "        current_date = eastern_tz.localize(current_date)\n", "        \n", "        # Get top momentum candidates (combining all time periods)\n", "        momentum_candidates = set()\n", "        for period in ['roc_1m', 'roc_3m', 'roc_6m', 'roc_12m']:\n", "            if period in period_data:\n", "                momentum_candidates.update(period_data[period]['ticker'].tolist())\n", "        \n", "        # Process each momentum candidate\n", "        for ticker in momentum_candidates:\n", "            # Get daily data for moving averages and previous high calculation\n", "            lookback_start = current_date - timedelta(days=lookback_days*2)  # Extra buffer for calculations\n", "            daily_data = get_daily_data(ticker, lookback_start, current_date)\n", "            \n", "            if daily_data is None or daily_data.empty or len(daily_data) < lookback_days:\n", "                continue\n", "                \n", "            # Calculate moving averages\n", "            daily_data['ma10'] = daily_data['close'].rolling(window=10).mean()\n", "            daily_data['ma20'] = daily_data['close'].rolling(window=20).mean()\n", "            \n", "            # Check if we have data for the current date\n", "            if current_date not in daily_data.index:\n", "                continue\n", "                \n", "            # Calculate 30-day high (excluding current date)\n", "            previous_data = daily_data[daily_data.index < current_date].tail(lookback_days)\n", "            if len(previous_data) < lookback_days:\n", "                continue\n", "                \n", "            thirty_day_high = previous_data['high'].max()\n", "            \n", "            # Check if consolidation is happening (20MA getting closer to 10MA)\n", "            latest_data = daily_data.loc[current_date]\n", "            \n", "            if pd.isna(latest_data['ma10']) or pd.isna(latest_data['ma20']):\n", "                continue\n", "                \n", "            # Calculate MA difference as percentage\n", "            ma_diff_pct = abs(latest_data['ma10'] - latest_data['ma20']) / latest_data['ma10']\n", "            \n", "            if ma_diff_pct <= consolidation_threshold:\n", "                # Consolidation detected, now check for breakout in first 30 min\n", "                # Get minute data for current day\n", "                day_start = datetime.combine(date, time(0, 0, 0))\n", "                day_start = eastern_tz.localize(day_start)\n", "                day_end = day_start + timedelta(days=1) - timed<PERSON>ta(seconds=1)\n", "                \n", "                minute_data = get_per_minute_data(ticker, day_start, day_end)\n", "                \n", "                if minute_data is None or minute_data.empty:\n", "                    continue\n", "                    \n", "                # Filter for first 30 minutes of trading\n", "                market_open_dt = datetime.combine(date, market_open)\n", "                market_open_dt = eastern_tz.localize(market_open_dt)\n", "                \n", "                market_open_plus_30_dt = datetime.combine(date, market_open_plus_30)\n", "                market_open_plus_30_dt = eastern_tz.localize(market_open_plus_30_dt)\n", "                \n", "                first_30_min = minute_data[\n", "                    (minute_data.index >= market_open_dt) & \n", "                    (minute_data.index <= market_open_plus_30_dt)\n", "                ]\n", "                \n", "                if first_30_min.empty:\n", "                    continue\n", "                \n", "                # Check if price breaks above 30-day high in first 30 min\n", "                breakout = (first_30_min['high'] > thirty_day_high).any()\n", "                \n", "                if breakout:\n", "                    # Find the exact minute of breakout\n", "                    breakout_minute = first_30_min[first_30_min['high'] > thirty_day_high].index[0]\n", "                    \n", "                    # Find the day's low for stop loss (using all available minute data)\n", "                    day_low = minute_data['low'].min()\n", "                    \n", "                    # Entry price - we'll assume entry at the close of the breakout minute\n", "                    entry_price = first_30_min.loc[breakout_minute, 'close']\n", "                    \n", "                    # Record the trade signal\n", "                    signal = {\n", "                        'date': date,\n", "                        'ticker': ticker,\n", "                        'entry_time': breakout_minute,\n", "                        'entry_price': entry_price,\n", "                        'stop_loss': day_low,\n", "                        'risk_pct': (entry_price - day_low) / entry_price * 100,\n", "                        'thirty_day_high': thirty_day_high,\n", "                        'ma10': latest_data['ma10'],\n", "                        'ma20': latest_data['ma20'],\n", "                        'ma_diff_pct': ma_diff_pct * 100\n", "                    }\n", "                    \n", "                    all_signals.append(signal)\n", "    \n", "    # Convert signals to DataFrame\n", "    if all_signals:\n", "        signals_df = pd.DataFrame(all_signals)\n", "        return signals_df\n", "    else:\n", "        return pd.DataFrame()\n", "\n", "\n", "def backtest_momentum_strategy(signals_df, exit_days=5):\n", "    \"\"\"\n", "    Backtest the momentum strategy by calculating returns for each trade.\n", "    \n", "    Parameters:\n", "    -----------\n", "    signals_df : pandas.DataFrame\n", "        DataFrame containing trade signals\n", "    exit_days : int\n", "        Number of days to hold the position before exit if stop loss not hit\n", "    \n", "    Returns:\n", "    --------\n", "    pandas.DataFrame\n", "        DataFrame with trade signals and performance metrics\n", "    \"\"\"\n", "    if signals_df.empty:\n", "        return pd.DataFrame()\n", "    \n", "    eastern_tz = pytz.timezone(\"US/Eastern\")\n", "    \n", "    # Add columns for results\n", "    signals_df['exit_price'] = np.nan\n", "    signals_df['exit_date'] = np.nan\n", "    signals_df['stop_hit'] = False\n", "    signals_df['return_pct'] = np.nan\n", "    signals_df['holding_days'] = np.nan\n", "    \n", "    # Process each signal\n", "    for idx, row in signals_df.iterrows():\n", "        ticker = row['ticker']\n", "        entry_date = row['date']\n", "        entry_price = row['entry_price']\n", "        stop_loss = row['stop_loss']\n", "        \n", "        # Calculate exit date (trading days)\n", "        exit_date = entry_date + pd.Timedelta(days=30)  # Adding buffer days to account for weekends/holidays\n", "        \n", "        # Get minute data from entry date to exit date\n", "        start_dt = datetime.combine(entry_date, time(0, 0, 0))\n", "        start_dt = eastern_tz.localize(start_dt)\n", "        \n", "        end_dt = datetime.combine(exit_date, time(23, 59, 59))\n", "        end_dt = eastern_tz.localize(end_dt)\n", "        \n", "        minute_data = get_per_minute_data(ticker, start_dt, end_dt)\n", "        \n", "        if minute_data is None or minute_data.empty:\n", "            continue\n", "            \n", "        # Find the first entry time in the data\n", "        entry_time = row['entry_time']\n", "        \n", "        # Filter data after entry\n", "        after_entry = minute_data[minute_data.index > entry_time]\n", "        \n", "        if after_entry.empty:\n", "            continue\n", "            \n", "        # Check if stop loss was hit\n", "        stop_hit = (after_entry['low'] <= stop_loss).any()\n", "        \n", "        if stop_hit:\n", "            # Find when stop loss was hit\n", "            stop_time = after_entry[after_entry['low'] <= stop_loss].index[0]\n", "            exit_price = stop_loss  # Assume exit at stop loss level\n", "            exit_date = stop_time.date()\n", "            holding_days = (stop_time.date() - entry_date).days\n", "            \n", "            if holding_days == 0:\n", "                holding_days = 0.5  # Partial day\n", "        else:\n", "            # Exit after specified number of trading days\n", "            # For simplicity, we'll use calendar days and daily data\n", "            daily_start = start_dt\n", "            daily_end = start_dt + pd.Timedelta(days=exit_days+5)  # Add buffer for weekends/holidays\n", "            \n", "            daily_data = get_daily_data(ticker, daily_start, daily_end)\n", "            \n", "            if daily_data is None or daily_data.empty:\n", "                continue\n", "                \n", "            # Filter data for trading days after entry date\n", "            trading_days_after = daily_data[daily_data.index.date > entry_date]\n", "            \n", "            if len(trading_days_after) < exit_days:\n", "                # Not enough data to determine exit\n", "                continue\n", "                \n", "            # Exit at the close of the exit_days trading day\n", "            exit_day = trading_days_after.iloc[exit_days-1]\n", "            exit_price = exit_day['close']\n", "            exit_date = exit_day.name.date()\n", "            holding_days = exit_days\n", "        \n", "        # Calculate return\n", "        return_pct = (exit_price - entry_price) / entry_price * 100\n", "        \n", "        # Update the signal with results\n", "        signals_df.loc[idx, 'exit_price'] = exit_price\n", "        signals_df.loc[idx, 'exit_date'] = exit_date\n", "        signals_df.loc[idx, 'stop_hit'] = stop_hit\n", "        signals_df.loc[idx, 'return_pct'] = return_pct\n", "        signals_df.loc[idx, 'holding_days'] = holding_days\n", "    \n", "    # Remove incomplete trades\n", "    signals_df = signals_df.dropna(subset=['return_pct'])\n", "    \n", "    # Calculate additional performance metrics\n", "    if not signals_df.empty:\n", "        signals_df['cumulative_return'] = (1 + signals_df['return_pct']/100).cumprod() - 1\n", "        signals_df['win'] = signals_df['return_pct'] > 0\n", "        \n", "        # Calculate win rate\n", "        win_rate = signals_df['win'].mean() * 100\n", "        avg_win = signals_df.loc[signals_df['win'], 'return_pct'].mean() if not signals_df.loc[signals_df['win']].empty else 0\n", "        avg_loss = signals_df.loc[~signals_df['win'], 'return_pct'].mean() if not signals_df.loc[~signals_df['win']].empty else 0\n", "        \n", "        print(f\"Strategy Performance:\")\n", "        print(f\"Total Trades: {len(signals_df)}\")\n", "        print(f\"Win Rate: {win_rate:.2f}%\")\n", "        print(f\"Average Win: {avg_win:.2f}%\")\n", "        print(f\"Average Loss: {avg_loss:.2f}%\")\n", "        print(f\"Profit Factor: {abs(avg_win/avg_loss) if avg_loss != 0 else 'N/A'}\")\n", "        print(f\"Final Cumulative Return: {signals_df['cumulative_return'].iloc[-1]*100:.2f}%\")\n", "    \n", "    return signals_df\n", "\n", "\n", "def visualize_strategy_performance(signals_df):\n", "    \"\"\"\n", "    Visualize the performance of the momentum strategy.\n", "    \n", "    Parameters:\n", "    -----------\n", "    signals_df : pandas.DataFrame\n", "        DataFrame with trade signals and performance\n", "    \"\"\"\n", "    if signals_df.empty:\n", "        print(\"No trades to visualize\")\n", "        return\n", "    \n", "    # Set up plotting\n", "    plt.figure(figsize=(14, 8))\n", "    \n", "    # Plot equity curve\n", "    plt.subplot(2, 2, 1)\n", "    plt.plot(signals_df['date'], (1 + signals_df['cumulative_return']) * 100)\n", "    plt.title('Equity Curve')\n", "    plt.xlabel('Date')\n", "    plt.ylabel('Portfolio Value (%)')\n", "    plt.grid(True)\n", "    \n", "    # Plot trade returns\n", "    plt.subplot(2, 2, 2)\n", "    plt.bar(range(len(signals_df)), signals_df['return_pct'], \n", "            color=signals_df['return_pct'].apply(lambda x: 'green' if x > 0 else 'red'))\n", "    plt.title('Individual Trade Returns')\n", "    plt.xlabel('Trade #')\n", "    plt.ylabel('Return (%)')\n", "    plt.grid(True)\n", "    \n", "    # Plot win/loss distribution\n", "    plt.subplot(2, 2, 3)\n", "    plt.hist(signals_df['return_pct'], bins=20, color='blue', alpha=0.7)\n", "    plt.axvline(x=0, color='black', linestyle='--')\n", "    plt.title('Return Distribution')\n", "    plt.xlabel('Return (%)')\n", "    plt.ylabel('Frequency')\n", "    plt.grid(True)\n", "    \n", "    # Plot drawdown\n", "    plt.subplot(2, 2, 4)\n", "    cumulative_returns = (1 + signals_df['cumulative_return'])\n", "    rolling_max = cumulative_returns.cummax()\n", "    drawdown = (cumulative_returns / rolling_max - 1) * 100\n", "    plt.plot(signals_df['date'], drawdown)\n", "    plt.title('Drawdown')\n", "    plt.xlabel('Date')\n", "    plt.ylabel('Drawdown (%)')\n", "    plt.grid(True)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Additional analysis - monthly returns\n", "    signals_df['year_month'] = signals_df['date'].apply(lambda x: f\"{x.year}-{x.month:02d}\")\n", "    monthly_returns = signals_df.groupby('year_month')['return_pct'].sum().reset_index()\n", "    \n", "    plt.figure(figsize=(12, 6))\n", "    plt.bar(monthly_returns['year_month'], monthly_returns['return_pct'],\n", "            color=monthly_returns['return_pct'].apply(lambda x: 'green' if x > 0 else 'red'))\n", "    plt.title('Monthly Returns')\n", "    plt.xlabel('Month')\n", "    plt.ylabel('Return (%)')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "\n", "# Run the strategy\n", "def run_high_beta_momentum_backtest(start_dt, end_dt, ticker_universe, \n", "                                    min_pct_change=30, \n", "                                    consolidation_threshold=0.03,\n", "                                    lookback_days=30,\n", "                                    exit_days=5):\n", "    \"\"\"\n", "    Run the complete high beta momentum strategy backtest.\n", "    \"\"\"\n", "    # Generate signals\n", "    signals_df = high_beta_momentum_strategy(\n", "        ticker_universe=ticker_universe,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        min_pct_change=min_pct_change,\n", "        consolidation_threshold=consolidation_threshold,\n", "        lookback_days=lookback_days\n", "    )\n", "    \n", "    if signals_df.empty:\n", "        print(\"No trading signals generated.\")\n", "        return None\n", "    \n", "    # Backtest the strategy\n", "    results_df = backtest_momentum_strategy(signals_df, exit_days=exit_days)\n", "    \n", "    # Visualize results\n", "    visualize_strategy_performance(results_df)\n", "    \n", "    return results_df"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "390d42da162f420e815b858bcf230f41", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Strategy Performance:\n", "Total Trades: 368\n", "Win Rate: 12.77%\n", "Average Win: 8.05%\n", "Average Loss: -2.96%\n", "Profit Factor: 2.7170161756130695\n", "Final Cumulative Return: -99.82%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Define parameters\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2024, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 1, 1))\n", "\n", "# # Generate momentum universe\n", "# momentum_universe = generate_momentum_universe(\n", "#     ticker_universe=ticker_universe,\n", "#     start_dt=start_dt,\n", "#     end_dt=end_dt,\n", "#     min_pct_change=30\n", "# )\n", "\n", "# # Access example - get top performers for a specific date and timeframe\n", "# sample_date = list(momentum_universe.keys())[0]  # First date with data\n", "# top_1m_performers = momentum_universe[sample_date]['roc_1m']\n", "# top_3m_performers = momentum_universe[sample_date]['roc_3m']\n", "\n", "# Run the backtest\n", "results = run_high_beta_momentum_backtest(\n", "    start_dt=start_dt,\n", "    end_dt=end_dt,\n", "    ticker_universe=list(ticker_universe)[:100], # Pick just 100\n", "    min_pct_change=30,          # Minimum monthly % change for momentum\n", "    consolidation_threshold=0.03, # Maximum difference between 10MA and 20MA (3%)\n", "    lookback_days=30,           # Days to look back for high calculation\n", "    exit_days=5                 # Hold for 5 trading days if stop not hit\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}