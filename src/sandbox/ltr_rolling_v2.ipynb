{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Common notebook settings\n", "\n", "import warnings\n", "import pandas as pd\n", "import logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "# %% helpers & imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from functools import lru_cache\n", "\n", "# ---- gap-strategy modules ----\n", "from strategies.ltr.gap_extractor import GapExtractor\n", "from strategies.ltr.feature_extractor import FeatureExtractor\n", "from strategies.ltr.ltr_trainer import LTRModelTrainer, TrainerConfig\n", "from strategies.ltr.ltr_ranker import LTRModelRanker\n", "from strategies.ltr.backtest import BacktestRunner\n", "\n", "# ---- tiny utilities --------------------------------------------------\n", "def add_atr14(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Add a 14-day ATR column (percentage of close).\"\"\"\n", "    tr = np.maximum.reduce([\n", "        df['high'] - df['low'],\n", "        (df['high'] - df['close'].shift()).abs(),\n", "        (df['low']  - df['close'].shift()).abs()\n", "    ])\n", "    df['atr14'] = pd.Series(tr, index=df.index).rolling(window=14).mean()\n", "    return df\n", "\n", "def create_intraday_fetcher(get_hourly_data):\n", "    @lru_cache(maxsize=None)\n", "    def fetch(tkr: str, day_str: str):\n", "        day = pd.Timestamp(day_str)\n", "        return get_hourly_data(\n", "            tkr,\n", "            day.replace(hour=0,  minute=0),\n", "            day.replace(hour=23, minute=59)\n", "        )\n", "    return fetch\n", "\n", "# ---- features we feed the model --------------------------------------\n", "feature_cols = [\n", "    'gap_atr', 'prev_gap_atr','max_gap30_atr',\n", "    'log_mcap', 'log_listing_age', 'weekday',\n", "    'prev_day_return_atr', 'prev_vol_ratio',\n", "    'dist_ma5_atr', 'dist_ma10_atr', 'dist_ma20_atr', 'dist_ma50_atr', 'qpi'\n", "]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Universe size: 5356\n", "Downloading daily bars ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 5356/5356 [00:07<00:00, 705.78it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Fetched 5356 tickers with valid data\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# %% data download (as provided in the prompt – only wrapped in a cell)\n", "from marketdata import MarketDataBuilder\n", "from tickers.ticker_helpers import TickerInfoStore\n", "from universe.stock_universe_selector import StockUniverseSelector\n", "from tqdm import tqdm\n", "\n", "START_DT = pd.Timestamp(\"2020-01-01\", tz=\"US/Eastern\")\n", "END_DT   = pd.Timestamp(\"2025-05-17\", tz=\"US/Eastern\")\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                     .with_period(\"daily\")\n", "                     .with_disk_data(\n", "                         start_date=START_DT.replace(tzinfo=None) -\n", "                         pd.<PERSON><PERSON><PERSON>(days=365 * 5 + 365 + 30))\n", "                     .build_market_data())\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\")\n", "                        .with_adjusted(False)\n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker, start_dt=start_dt, end_dt=end_dt, interval=86400)\n", "    except Exception as e:\n", "        print(f\"[daily] {ticker}: {e}\")\n", "        return None\n", "\n", "def get_hourly_data(ticker, start_dt, end_dt):\n", "    try:\n", "        return intraday_market_data.gather_historical_data(\n", "            ticker=ticker, start_dt=start_dt, end_dt=end_dt, interval=3600)\n", "    except Exception as e:\n", "        print(f\"[hourly] {ticker}: {e}\")\n", "        return None\n", "\n", "ticker_info_store  = TickerInfoStore()\n", "universe_selector  = StockUniverseSelector(daily_market_data, ticker_info_store)\n", "TICKERS            = universe_selector.select_by_mcap_range(\n", "    START_DT,\n", "    END_DT,\n", "    min_mcap=500_000_000,\n", "    max_mcap=5000_000_000\n", ")['ticker'].tolist()\n", "\n", "# import random\n", "# sampler = random.Random()\n", "# sampler.seed(42)\n", "# TICKERS = sampler.sample(TICKERS, 500)\n", "\n", "print(f\"Universe size: {len(TICKERS)}\")\n", "\n", "# ---- fetch daily price data ------------------------------------------\n", "daily_bars = {}\n", "print(\"Downloading daily bars ...\")\n", "for tkr in tqdm(TICKERS):\n", "    df = get_daily_data(tkr,\n", "                        START_DT - pd.<PERSON><PERSON><PERSON>(days=365*6),  # 6-yr buffer\n", "                        END_DT)\n", "    if df is not None and not df.empty:\n", "        df = add_atr14(df)\n", "        df.index = df.index.tz_localize(None).normalize()\n", "        daily_bars[tkr] = df\n", "print(f\"Fetched {len(daily_bars)} tickers with valid data\")\n", "\n", "intraday_fetcher = create_intraday_fetcher(get_hourly_data)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-26 23:08:55,557 - INFO - [_make_feature_df] start=2020-01-01 00:00:00, end=2025-01-01 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-26 23:09:47,028 - INFO - GapExtractor (total days=6985767): price_fail=108501 (1.6%) | volume_fail=457897 (6.6%) | gap_pct_fail=6793777 (97.3%) | gap_atr_fail=6438889 (92.2%) | passed=49113 (0.7%)\n", "2025-05-26 23:09:47,196 - INFO - [_make_feature_df] extracted gaps shape=(49113, 10), columns=['ticker', 'day', 'gap_pct', 'qpi', 'prev_close', 'close', 'open', 'high', 'low', 'volume']\n", "2025-05-26 23:10:05,829 - INFO - [_make_feature_df] after add_market_cap shape=(49113, 14), columns=['ticker', 'day', 'gap_pct', 'qpi', 'prev_close', 'close', 'open', 'high', 'low', 'volume', 'total_shares', 'total_employees', 'list_date', 'market_cap']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Filtered out 0 events based on market cap criteria\n", "Remaining: 49113 events\n", "Processing 3673 tickers using 16 cores...\n", "Completed batch with 229 tickers, got 2878 rows\n", "Completed batch with 229 tickers, got 2868 rows\n", "Completed batch with 230 tickers, got 2909 rows\n", "Completed batch with 230 tickers, got 3084 rows\n", "Completed batch with 230 tickers, got 2967 rows\n", "Completed batch with 230 tickers, got 3148 rows\n", "Completed batch with 230 tickers, got 3035 rows\n", "Completed batch with 229 tickers, got 3092 rows\n", "Completed batch with 229 tickers, got 3203 rows\n", "Completed batch with 229 tickers, got 2936 rows\n", "Completed batch with 230 tickers, got 3032 rows\n", "Completed batch with 230 tickers, got 3156 rows\n", "Completed batch with 229 tickers, got 3183 rows\n", "Completed batch with 230 tickers, got 3180 rows\n", "Completed batch with 230 tickers, got 3267 rows\n", "Completed batch with 229 tickers, got 3175 rows\n", "Row-filter summary: Counter({'approximated_max_gap30': 1050})\n", "Total rows retained: 49113\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-26 23:11:04,575 - INFO - [_make_feature_df] built features shape=(49113, 18), columns=['ticker', 'day', 'fwd_ret', 'gap_atr', 'qpi', 'prev_gap_atr', 'max_gap30_atr', 'log_mcap', 'log_listing_age', 'weekday', 'prev_day_return_atr', 'prev_vol_ratio', 'dist_ma5_atr', 'dist_ma10_atr', 'dist_ma20_atr', 'dist_ma50_atr', 'decile', 'ltr_target']\n", "2025-05-26 23:11:04,576 - INFO - [_make_feature_df] expected columns for dropna: ['gap_atr', 'prev_gap_atr', 'max_gap30_atr', 'log_mcap', 'log_listing_age', 'weekday', 'prev_day_return_atr', 'prev_vol_ratio', 'dist_ma5_atr', 'dist_ma10_atr', 'dist_ma20_atr', 'dist_ma50_atr', 'qpi', 'ltr_target']\n", "2025-05-26 23:11:04,576 - INFO - [_make_feature_df] missing columns: set()\n", "2025-05-26 23:11:04,577 - INFO - [_make_feature_df] rows before dropna for ltr_target: 49113\n", "2025-05-26 23:11:04,580 - INFO - [_make_feature_df] Rows dropped due to NaN ltr_target: 30377\n", "2025-05-26 23:11:04,581 - INFO - [_make_feature_df] rows after dropna (on ltr_target only): 18736\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000269 seconds.\n", "You can set `force_col_wise=true` to remove the overhead.\n", "[LightGBM] [Info] Total Bins 3062\n", "[LightGBM] [Info] Number of data points in the train set: 15381, number of used features: 13\n", "Training until validation scores don't improve for 20 rounds\n", "Early stopping, best iteration is:\n", "[3]\tvalid_0's ndcg@10: 0.632235\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-05-26 23:11:52,727 - INFO - GapExtractor (total days=6985767): price_fail=108501 (1.6%) | volume_fail=457897 (6.6%) | gap_pct_fail=6793777 (97.3%) | gap_atr_fail=6438889 (92.2%) | passed=7087 (0.1%)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Filtered out 0 events based on market cap criteria\n", "Remaining: 7087 events\n", "Processing 1705 tickers using 16 cores...\n", "Completed batch with 107 tickers, got 440 rows\n", "Completed batch with 107 tickers, got 462 rows\n", "Completed batch with 106 tickers, got 418 rows\n", "Completed batch with 107 tickers, got 433 rows\n", "Completed batch with 107 tickers, got 369 rows\n", "Completed batch with 107 tickers, got 457 rows\n", "Completed batch with 107 tickers, got 428 rows\n", "Completed batch with 107 tickers, got 432 rows\n", "Completed batch with 107 tickers, got 485 rows\n", "Completed batch with 106 tickers, got 432 rows\n", "Completed batch with 106 tickers, got 456 rows\n", "Completed batch with 106 tickers, got 460 rows\n", "Completed batch with 106 tickers, got 470 rows\n", "Completed batch with 106 tickers, got 434 rows\n", "Completed batch with 107 tickers, got 453 rows\n", "Completed batch with 106 tickers, got 458 rows\n", "Row-filter summary: Counter({'approximated_max_gap30': 169})\n", "Total rows retained: 7087\n", "2025 Ranked: return -62.9%, sharpe -12.18\n", "2025 Ranked vs Random Comparison (n_sims=500):\n", "                     ranked  random_mean    random_std   p_value\n", "total_return      -0.629042    -0.659076  3.076434e-02  0.159681\n", "sharpe           -12.176118   -12.592209  1.689048e+00          \n", "max_dd            -0.646250    -0.668404  2.892917e-02          \n", "total_trades     389.000000   389.000000  0.000000e+00          \n", "commission_paid    0.778000     0.778000  2.222670e-16          \n", "win_rate_net       0.408740     0.407476  1.858497e-02          \n", "\n", "Total return 2020-2025: -62.90%\n", "Total trades: 389\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>day</th>\n", "      <th>symbol</th>\n", "      <th>dir</th>\n", "      <th>gross_ret</th>\n", "      <th>commission</th>\n", "      <th>net_ret</th>\n", "      <th>score</th>\n", "      <th>year</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-01-02</td>\n", "      <td>STI</td>\n", "      <td>long</td>\n", "      <td>0.065226</td>\n", "      <td>0.002</td>\n", "      <td>0.063226</td>\n", "      <td>-0.019636</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-01-02</td>\n", "      <td>DTIL</td>\n", "      <td>long</td>\n", "      <td>0.160891</td>\n", "      <td>0.002</td>\n", "      <td>0.158891</td>\n", "      <td>-0.033803</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-01-02</td>\n", "      <td>GMM</td>\n", "      <td>long</td>\n", "      <td>-0.064634</td>\n", "      <td>0.002</td>\n", "      <td>-0.066634</td>\n", "      <td>-0.057060</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-01-02</td>\n", "      <td>SES</td>\n", "      <td>long</td>\n", "      <td>-0.030612</td>\n", "      <td>0.002</td>\n", "      <td>-0.032612</td>\n", "      <td>-0.122962</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-01-03</td>\n", "      <td>SES</td>\n", "      <td>long</td>\n", "      <td>-0.019608</td>\n", "      <td>0.002</td>\n", "      <td>-0.021608</td>\n", "      <td>-0.042993</td>\n", "      <td>2025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         day symbol   dir  gross_ret  commission   net_ret     score  year\n", "0 2025-01-02    STI  long   0.065226       0.002  0.063226 -0.019636  2025\n", "1 2025-01-02   DTIL  long   0.160891       0.002  0.158891 -0.033803  2025\n", "2 2025-01-02    GMM  long  -0.064634       0.002 -0.066634 -0.057060  2025\n", "3 2025-01-02    SES  long  -0.030612       0.002 -0.032612 -0.122962  2025\n", "4 2025-01-03    SES  long  -0.019608       0.002 -0.021608 -0.042993  2025"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# %% build trainer / ranker / back-tester and run\n", "gap_extractor   = GapExtractor()\n", "feature_extr    = FeatureExtractor(max_workers = 16)\n", "\n", "trainer_cfg     = TrainerConfig(feature_cols=feature_cols)\n", "trainer         = LTRModelTrainer(\n", "    gap_extractor, feature_extr,\n", "    daily_bars, ticker_info_store,\n", "    intraday_fetcher, trainer_cfg\n", ")\n", "\n", "# factory so BacktestRunner can rebuild rankers each year\n", "ranker_factory  = lambda mdl: LTRModelRanker(\n", "    mdl, gap_extractor, feature_extr, feature_cols\n", ")\n", "\n", "bt = BacktestRunner(\n", "    trainer, ranker_factory,\n", "    commission_per_trade=0.002,   # 20 bps per side\n", "    k_frac=0.1, min_k=4,\n", "    short_alloc=0.00, long_alloc=0.25\n", ")\n", "\n", "years   = list(range(2025, 2026)) \n", "equity, trades = bt.run_yearly(\n", "    years, daily_bars, ticker_info_store, intraday_fetcher, random_sims = 500\n", ")\n", "\n", "# ---- quick results ----------------------------------------------------\n", "print(f\"\\nTotal return 2020-2025: {(equity.iloc[-1]-1):.2%}\")\n", "print(f\"Total trades: {len(trades)}\")\n", "\n", "equity.plot(figsize=(12,6))\n", "plt.title(\"Equity Curve – Gap Strategy 2020-2025\")\n", "plt.ylabel(\"Portfolio value (starting 1.0)\")\n", "plt.grid(True)\n", "plt.show()\n", "\n", "trades.head()\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>day</th>\n", "      <th>symbol</th>\n", "      <th>dir</th>\n", "      <th>gross_ret</th>\n", "      <th>commission</th>\n", "      <th>net_ret</th>\n", "      <th>score</th>\n", "      <th>year</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-01-02</td>\n", "      <td>STI</td>\n", "      <td>long</td>\n", "      <td>0.065226</td>\n", "      <td>0.002</td>\n", "      <td>0.063226</td>\n", "      <td>-0.019636</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-01-02</td>\n", "      <td>DTIL</td>\n", "      <td>long</td>\n", "      <td>0.160891</td>\n", "      <td>0.002</td>\n", "      <td>0.158891</td>\n", "      <td>-0.033803</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-01-02</td>\n", "      <td>GMM</td>\n", "      <td>long</td>\n", "      <td>-0.064634</td>\n", "      <td>0.002</td>\n", "      <td>-0.066634</td>\n", "      <td>-0.057060</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-01-02</td>\n", "      <td>SES</td>\n", "      <td>long</td>\n", "      <td>-0.030612</td>\n", "      <td>0.002</td>\n", "      <td>-0.032612</td>\n", "      <td>-0.122962</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-01-03</td>\n", "      <td>SES</td>\n", "      <td>long</td>\n", "      <td>-0.019608</td>\n", "      <td>0.002</td>\n", "      <td>-0.021608</td>\n", "      <td>-0.042993</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-01-03</td>\n", "      <td>CRNC</td>\n", "      <td>long</td>\n", "      <td>0.791474</td>\n", "      <td>0.002</td>\n", "      <td>0.789474</td>\n", "      <td>-0.043940</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-01-03</td>\n", "      <td>NUS</td>\n", "      <td>long</td>\n", "      <td>-0.018253</td>\n", "      <td>0.002</td>\n", "      <td>-0.020253</td>\n", "      <td>-0.052004</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-01-03</td>\n", "      <td>ZYME</td>\n", "      <td>long</td>\n", "      <td>-0.088092</td>\n", "      <td>0.002</td>\n", "      <td>-0.090092</td>\n", "      <td>-0.075287</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2025-01-06</td>\n", "      <td>STEM</td>\n", "      <td>long</td>\n", "      <td>0.044248</td>\n", "      <td>0.002</td>\n", "      <td>0.042248</td>\n", "      <td>-0.067016</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2025-01-06</td>\n", "      <td>PLUG</td>\n", "      <td>long</td>\n", "      <td>0.105263</td>\n", "      <td>0.002</td>\n", "      <td>0.103263</td>\n", "      <td>-0.073588</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2025-01-06</td>\n", "      <td>CMTL</td>\n", "      <td>long</td>\n", "      <td>0.004484</td>\n", "      <td>0.002</td>\n", "      <td>0.002484</td>\n", "      <td>-0.079615</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2025-01-06</td>\n", "      <td>PERF</td>\n", "      <td>long</td>\n", "      <td>0.111111</td>\n", "      <td>0.002</td>\n", "      <td>0.109111</td>\n", "      <td>-0.082746</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2025-01-07</td>\n", "      <td>FRO</td>\n", "      <td>long</td>\n", "      <td>0.032468</td>\n", "      <td>0.002</td>\n", "      <td>0.030468</td>\n", "      <td>-0.086689</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2025-01-07</td>\n", "      <td>STOK</td>\n", "      <td>long</td>\n", "      <td>-0.051935</td>\n", "      <td>0.002</td>\n", "      <td>-0.053935</td>\n", "      <td>-0.051262</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2025-01-07</td>\n", "      <td>SSTK</td>\n", "      <td>long</td>\n", "      <td>-0.068575</td>\n", "      <td>0.002</td>\n", "      <td>-0.070575</td>\n", "      <td>-0.040025</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2025-01-07</td>\n", "      <td>DHT</td>\n", "      <td>long</td>\n", "      <td>0.021429</td>\n", "      <td>0.002</td>\n", "      <td>0.019429</td>\n", "      <td>-0.040025</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2025-01-08</td>\n", "      <td>STEM</td>\n", "      <td>long</td>\n", "      <td>-0.178621</td>\n", "      <td>0.002</td>\n", "      <td>-0.180621</td>\n", "      <td>-0.081731</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2025-01-08</td>\n", "      <td>WIMI</td>\n", "      <td>long</td>\n", "      <td>-0.172414</td>\n", "      <td>0.002</td>\n", "      <td>-0.174414</td>\n", "      <td>-0.081731</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2025-01-08</td>\n", "      <td>NOVA</td>\n", "      <td>long</td>\n", "      <td>-0.073286</td>\n", "      <td>0.002</td>\n", "      <td>-0.075286</td>\n", "      <td>-0.081731</td>\n", "      <td>2025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2025-01-08</td>\n", "      <td>SES</td>\n", "      <td>long</td>\n", "      <td>0.019737</td>\n", "      <td>0.002</td>\n", "      <td>0.017737</td>\n", "      <td>-0.081731</td>\n", "      <td>2025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          day symbol   dir  gross_ret  commission   net_ret     score  year\n", "0  2025-01-02    STI  long   0.065226       0.002  0.063226 -0.019636  2025\n", "1  2025-01-02   DTIL  long   0.160891       0.002  0.158891 -0.033803  2025\n", "2  2025-01-02    GMM  long  -0.064634       0.002 -0.066634 -0.057060  2025\n", "3  2025-01-02    SES  long  -0.030612       0.002 -0.032612 -0.122962  2025\n", "4  2025-01-03    SES  long  -0.019608       0.002 -0.021608 -0.042993  2025\n", "5  2025-01-03   CRNC  long   0.791474       0.002  0.789474 -0.043940  2025\n", "6  2025-01-03    NUS  long  -0.018253       0.002 -0.020253 -0.052004  2025\n", "7  2025-01-03   ZYME  long  -0.088092       0.002 -0.090092 -0.075287  2025\n", "8  2025-01-06   STEM  long   0.044248       0.002  0.042248 -0.067016  2025\n", "9  2025-01-06   PLUG  long   0.105263       0.002  0.103263 -0.073588  2025\n", "10 2025-01-06   CMTL  long   0.004484       0.002  0.002484 -0.079615  2025\n", "11 2025-01-06   PERF  long   0.111111       0.002  0.109111 -0.082746  2025\n", "15 2025-01-07    FRO  long   0.032468       0.002  0.030468 -0.086689  2025\n", "14 2025-01-07   STOK  long  -0.051935       0.002 -0.053935 -0.051262  2025\n", "12 2025-01-07   SSTK  long  -0.068575       0.002 -0.070575 -0.040025  2025\n", "13 2025-01-07    DHT  long   0.021429       0.002  0.019429 -0.040025  2025\n", "16 2025-01-08   STEM  long  -0.178621       0.002 -0.180621 -0.081731  2025\n", "17 2025-01-08   WIMI  long  -0.172414       0.002 -0.174414 -0.081731  2025\n", "18 2025-01-08   NOVA  long  -0.073286       0.002 -0.075286 -0.081731  2025\n", "19 2025-01-08    SES  long   0.019737       0.002  0.017737 -0.081731  2025"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["trades.sort_values(by='day', ascending=True).head(20)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>year</th>\n", "      <th>win_rate</th>\n", "      <th>total_trades</th>\n", "      <th>avg_net_ret</th>\n", "      <th>total_net_ret</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025</td>\n", "      <td>0.40874</td>\n", "      <td>389</td>\n", "      <td>-0.010509</td>\n", "      <td>-4.087903</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   year  win_rate  total_trades  avg_net_ret  total_net_ret\n", "0  2025   0.40874           389    -0.010509      -4.087903"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import calendar\n", "from IPython.display import display\n", "\n", "df = trades.copy()\n", "\n", "# ensure 'day' is datetime and extract year/month/day-of-year\n", "df['day'] = pd.to_datetime(df['day'])\n", "df['year'] = df['day'].dt.year\n", "df['month'] = df['day'].dt.month\n", "df['doy'] = df['day'].dt.dayofyear\n", "\n", "years = list(range(2025, 2026))\n", "\n", "# 1) Cumulative PnL aligned by Day-of-Year\n", "fig, ax = plt.subplots(figsize=(10,6))\n", "for yr in years:\n", "    d = df[df['year']==yr]\n", "    if d.empty: continue\n", "    daily = d.groupby('doy')['net_ret'].sum().sort_index()\n", "    cum = daily.cumsum()\n", "    ax.plot(cum.index, cum, label=str(yr))\n", "ax.set_title('Cumulative PnL by Day of Year')\n", "ax.set_xlabel('Day of Year (1=Jan 1)')\n", "ax.set_ylabel('Cumulative Net PnL')\n", "ax.legend(title='Year')\n", "ax.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 2) Monthly PnL per Year\n", "# pivot table: index=month, columns=year, values=net_ret sum\n", "monthly = (\n", "    df\n", "    .groupby(['year','month'])['net_ret']\n", "    .sum()\n", "    .unstack(0)\n", "    .reindex(range(1,13))  # ensure all months appear\n", ")\n", "# map month numbers to names\n", "monthly.index = [calendar.month_abbr[m] for m in monthly.index]\n", "\n", "from IPython.display import display\n", "\n", "df = trades.copy()\n", "# ensure 'day' is a datetime\n", "df['day'] = pd.to_datetime(df['day'])\n", "\n", "years = range(2020, 2026)\n", "stats = []\n", "\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "for year in years:\n", "    # select trades in that year\n", "    df_year = df[df['day'].dt.year == year]\n", "    if df_year.empty:\n", "        continue\n", "\n", "    # daily PnL\n", "    daily_pnl = (\n", "        df_year\n", "        .groupby(df_year['day'].dt.date)['net_ret']\n", "        .sum()\n", "        .sort_index()\n", "    )\n", "    \n", "    # cumulative PnL\n", "    cum_pnl = daily_pnl.cumsum()\n", "    \n", "    # plot\n", "    ax.plot(cum_pnl.index, cum_pnl, label=str(year))\n", "    \n", "    # yearly stats\n", "    win_rate = (df_year['net_ret'] > 0).mean()\n", "    total_trades = len(df_year)\n", "    avg_ret = df_year['net_ret'].mean()\n", "    total_ret = df_year['net_ret'].sum()\n", "    \n", "    stats.append({\n", "        'year': year,\n", "        'win_rate': win_rate,\n", "        'total_trades': total_trades,\n", "        'avg_net_ret': avg_ret,\n", "        'total_net_ret': total_ret\n", "    })\n", "\n", "ax.set_title('Cumulative PnL by Year')\n", "ax.set_xlabel('Date')\n", "ax.set_ylabel('Cumulative PnL')\n", "ax.legend(title='Year')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# assemble and display stats table\n", "stats_df = pd.DataFrame(stats)\n", "display(stats_df)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3165225/2456191989.py:25: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  ax.legend(title='Year')\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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******************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# prepare fields\n", "df['day'] = pd.to_datetime(df['day'])\n", "df['year'] = df['day'].dt.year\n", "df['doy']  = df['day'].dt.dayofyear\n", "\n", "years = range(2020, 2026)\n", "\n", "for direction in ['long', 'short']:\n", "    fig, ax = plt.subplots(figsize=(10, 6))\n", "    for yr in years:\n", "        d = df[(df['year'] == yr) & (df['dir'] == direction)]\n", "        if d.empty:\n", "            continue\n", "        # sum net_ret per day-of-year and cumsum\n", "        daily = d.groupby('doy')['net_ret'].sum().sort_index()\n", "        cum   = daily.cumsum()\n", "        ax.plot(cum.index, cum, label=str(yr))\n", "    \n", "    ax.set_title(f'Cumulative PnL by Day-of-Year ({direction.capitalize()})')\n", "    ax.set_xlabel('Day of Year (1=Jan 1)')\n", "    ax.set_ylabel('Cumulative Net PnL')\n", "    ax.legend(title='Year')\n", "    ax.grid(True)\n", "    plt.tight_layout()\n", "\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'year': 2025,\n", "  'win_rate': np.float64(0.4087403598971722),\n", "  'total_trades': 389,\n", "  'avg_net_ret': np.float64(-0.010508748747638227),\n", "  'total_net_ret': np.float64(-4.08790326283127)}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["stats"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}