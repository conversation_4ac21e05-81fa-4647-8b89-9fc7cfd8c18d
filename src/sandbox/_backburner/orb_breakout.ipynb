{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "\n", "market_data = (MarketDataBuilder()\n", "                 .with_trade_session(\"full\") \n", "                 .with_period(\"intraday\")\n", "                 .build_market_data())\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Utils\n", "\n", "#@df_cache_datetime_est\n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>timestamp</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02 04:00:00-05:00</th>\n", "      <td>50.8100</td>\n", "      <td>50.83</td>\n", "      <td>50.55</td>\n", "      <td>50.8100</td>\n", "      <td>31949</td>\n", "      <td>235</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 04:01:00-05:00</th>\n", "      <td>50.8100</td>\n", "      <td>50.81</td>\n", "      <td>50.78</td>\n", "      <td>50.7900</td>\n", "      <td>8545</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 04:02:00-05:00</th>\n", "      <td>50.7800</td>\n", "      <td>50.79</td>\n", "      <td>50.76</td>\n", "      <td>50.7700</td>\n", "      <td>8805</td>\n", "      <td>147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 04:03:00-05:00</th>\n", "      <td>50.7600</td>\n", "      <td>50.77</td>\n", "      <td>50.75</td>\n", "      <td>50.7500</td>\n", "      <td>25930</td>\n", "      <td>95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-02 04:04:00-05:00</th>\n", "      <td>50.7500</td>\n", "      <td>50.75</td>\n", "      <td>50.71</td>\n", "      <td>50.7100</td>\n", "      <td>14100</td>\n", "      <td>138</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-28 19:55:00-04:00</th>\n", "      <td>56.5400</td>\n", "      <td>56.55</td>\n", "      <td>56.51</td>\n", "      <td>56.5400</td>\n", "      <td>13603</td>\n", "      <td>124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-28 19:56:00-04:00</th>\n", "      <td>56.5300</td>\n", "      <td>56.55</td>\n", "      <td>56.53</td>\n", "      <td>56.5402</td>\n", "      <td>14506</td>\n", "      <td>46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-28 19:57:00-04:00</th>\n", "      <td>56.5500</td>\n", "      <td>56.55</td>\n", "      <td>56.51</td>\n", "      <td>56.5100</td>\n", "      <td>9410</td>\n", "      <td>96</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-28 19:58:00-04:00</th>\n", "      <td>56.5197</td>\n", "      <td>56.54</td>\n", "      <td>56.51</td>\n", "      <td>56.5400</td>\n", "      <td>10811</td>\n", "      <td>158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-28 19:59:00-04:00</th>\n", "      <td>56.5397</td>\n", "      <td>56.54</td>\n", "      <td>56.50</td>\n", "      <td>56.5300</td>\n", "      <td>14664</td>\n", "      <td>213</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>297075 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                              open   high    low    close  volume  count\n", "timestamp                                                               \n", "2024-01-02 04:00:00-05:00  50.8100  50.83  50.55  50.8100   31949    235\n", "2024-01-02 04:01:00-05:00  50.8100  50.81  50.78  50.7900    8545    150\n", "2024-01-02 04:02:00-05:00  50.7800  50.79  50.76  50.7700    8805    147\n", "2024-01-02 04:03:00-05:00  50.7600  50.77  50.75  50.7500   25930     95\n", "2024-01-02 04:04:00-05:00  50.7500  50.75  50.71  50.7100   14100    138\n", "...                            ...    ...    ...      ...     ...    ...\n", "2025-03-28 19:55:00-04:00  56.5400  56.55  56.51  56.5400   13603    124\n", "2025-03-28 19:56:00-04:00  56.5300  56.55  56.53  56.5402   14506     46\n", "2025-03-28 19:57:00-04:00  56.5500  56.55  56.51  56.5100    9410     96\n", "2025-03-28 19:58:00-04:00  56.5197  56.54  56.51  56.5400   10811    158\n", "2025-03-28 19:59:00-04:00  56.5397  56.54  56.50  56.5300   14664    213\n", "\n", "[297075 rows x 6 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load data\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2024, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 3, 28))\n", "\n", "tqqq_data = get_per_minute_data(\"TQQQ\", start_dt, end_dt)\n", "\n", "tqqq_data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["         Date Direction    Entry       Exit  PnL_R ExitReason\n", "0  2024-01-23     Short  54.4500  54.689605   -1.0       stop\n", "1  2024-01-24     Short  56.5300  56.755712   -1.0       stop\n", "2  2024-01-25     Short  56.8600  57.080891   -1.0       stop\n", "3  2024-01-26      <PERSON>  55.7300  55.502894   -1.0       stop\n", "4  2024-01-29      <PERSON>  55.6755  55.451394   -1.0       stop\n", "Average PnL (R): 0.2976138316705942\n"]}], "source": ["# Orb wit 14d atr\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import time, timedelta\n", "import matplotlib.pyplot as plt\n", "\n", "def calculate_daily_atr(df_minute, period=14):\n", "    df_minute = df_minute.copy()\n", "\n", "    # Resample to daily bars\n", "    df_daily = df_minute.resample('1D').agg({\n", "        'high': 'max',\n", "        'low': 'min',\n", "        'close': 'last'\n", "    }).dropna()\n", "\n", "    # Calculate True Range\n", "    high_low = df_daily['high'] - df_daily['low']\n", "    high_close = np.abs(df_daily['high'] - df_daily['close'].shift())\n", "    low_close = np.abs(df_daily['low'] - df_daily['close'].shift())\n", "    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)\n", "    atr = tr.rolling(window=period).mean()\n", "    df_daily['ATR'] = atr.shift(1)\n", "\n", "    # Merge ATR back into minute data using full datetime index\n", "    df_minute['DateOnly'] = df_minute.index.normalize()  # normalize to midnight\n", "    df_minute = df_minute.merge(\n", "        df_daily[['ATR']], \n", "        left_on='DateOnly', \n", "        right_index=True, \n", "        how='left'\n", "    )\n", "    df_minute.drop(columns='DateOnly', inplace=True)\n", "\n", "    return df_minute\n", "\n", "\n", "def run_orb_atr_strategy(df, stop_pct=0.10, profit_r=10):\n", "    df = df.copy()\n", "    df['Date'] = df.index.date\n", "    df = calculate_daily_atr(df)\n", "\n", "    results = []\n", "    grouped = df.groupby('Date')\n", "\n", "    for date, day_data in grouped:\n", "        day_data = day_data.between_time(\"09:30\", \"16:00\")\n", "        if len(day_data) < 10:  # not enough data\n", "            continue\n", "\n", "        atr_14 = day_data['ATR'].iloc[0]\n", "        if np.isnan(atr_14):\n", "            continue\n", "\n", "        # Get the first 5-minute candles (opening range)\n", "        open_range = day_data.iloc[:5]\n", "        first_open = open_range['open'].iloc[0]\n", "        first_close = open_range['close'].iloc[-1]\n", "        range_high = open_range['high'].max()\n", "        range_low = open_range['low'].min()\n", "\n", "        # <PERSON><PERSON>: skip\n", "        if np.isclose(first_open, first_close):\n", "            continue\n", "\n", "        is_long = first_close > first_open\n", "        \n", "        # Look for breakout after opening range\n", "        post_range = day_data.iloc[5:]\n", "        breakout_bar = None\n", "        breakout_type = None\n", "\n", "        # Find breakout\n", "        for ts, row in post_range.iterrows():\n", "            if is_long:  # If first candle is bullish, look for upward breakout\n", "                if row['high'] > range_high:\n", "                    breakout_bar = ts\n", "                    breakout_type = 'Long'\n", "                    break\n", "            else:  # If first candle is bearish, look for downward breakout\n", "                if row['low'] < range_low:\n", "                    breakout_bar = ts\n", "                    breakout_type = 'Short'\n", "                    break\n", "                    \n", "        if breakout_bar is None:\n", "            continue  # No breakout found\n", "            \n", "        # Get the bar index where the breakout happened\n", "        breakout_idx = post_range.index.get_loc(breakout_bar)\n", "        \n", "        # Get the NEXT bar after the breakout for entry\n", "        if breakout_idx + 1 >= len(post_range):\n", "            continue  # No next bar available for entry\n", "            \n", "        entry_bar = post_range.index[breakout_idx + 1]\n", "        entry_price = post_range.loc[entry_bar, 'open']  # Use next bar's open price\n", "        \n", "        risk = atr_14 * stop_pct\n", "        if risk <= 0.05:\n", "            continue\n", "\n", "        stop_price = entry_price - risk if breakout_type == 'Long' else entry_price + risk\n", "        target_price = entry_price + profit_r * risk if breakout_type == 'Long' else entry_price - profit_r * risk\n", "\n", "        # Trade analysis starts from the entry bar\n", "        trade_data = day_data.loc[entry_bar:]\n", "\n", "        exit_price = None\n", "        exit_reason = None\n", "\n", "        for ts, row in trade_data.iterrows():\n", "            high = row['high']\n", "            low = row['low']\n", "\n", "            if breakout_type == 'Long':\n", "                if low <= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif high >= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "            else:  # Short\n", "                if high >= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif low <= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "\n", "        if exit_price is None:\n", "            # Exit EOD\n", "            exit_price = trade_data.iloc[-1]['close']\n", "            exit_reason = 'eod'\n", "\n", "        pnl_r = (exit_price - entry_price) / risk if breakout_type == 'Long' else (entry_price - exit_price) / risk\n", "        pnl_r = min(pnl_r, profit_r) if pnl_r > 0 else pnl_r\n", "        results.append({\n", "            'Date': date,\n", "            'Direction': breakout_type,\n", "            'Entry': entry_price,\n", "            'Exit': exit_price,\n", "            'PnL_R': pnl_r,\n", "            'ExitReason': exit_reason\n", "        })\n", "\n", "    return pd.DataFrame(results)\n", "\n", "df_orb_atr = run_orb_atr_strategy(tqqq_data, stop_pct=0.10, profit_r=10)\n", "print(df_orb_atr.head())\n", "print(\"Average PnL (R):\", df_orb_atr['PnL_R'].mean())"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 2000x600 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Orb 14day atr + heatmap of stop loss vs profit target\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "def simulate_orb_matrix(df, stop_list, profit_list):\n", "    avg_r_matrix = pd.DataFrame(index=stop_list, columns=profit_list)\n", "    total_r_matrix = pd.DataFrame(index=stop_list, columns=profit_list)\n", "    trade_count_matrix = pd.DataFrame(index=stop_list, columns=profit_list)\n", "\n", "    for stop_pct in stop_list:\n", "        for profit_r in profit_list:\n", "            result = run_orb_atr_strategy(df, stop_pct=stop_pct, profit_r=profit_r)\n", "            \n", "            # Store average R per trade\n", "            avg_r_matrix.loc[stop_pct, profit_r] = result['PnL_R'].mean() if not result.empty else np.nan\n", "            \n", "            # Store total R (sum of all returns)\n", "            total_r_matrix.loc[stop_pct, profit_r] = result['PnL_R'].sum() if not result.empty else np.nan\n", "            \n", "            # Store trade count\n", "            trade_count_matrix.loc[stop_pct, profit_r] = len(result) if not result.empty else 0\n", "\n", "    avg_r_matrix = avg_r_matrix.astype(float)\n", "    total_r_matrix = total_r_matrix.astype(float)\n", "    trade_count_matrix = trade_count_matrix.astype(int)\n", "    \n", "    return avg_r_matrix, total_r_matrix, trade_count_matrix\n", "\n", "def plot_orb_heatmaps(avg_r_matrix, total_r_matrix, trade_count_matrix):\n", "    fig, axes = plt.subplots(1, 3, figsize=(20, 6))\n", "    \n", "    # Plot average R heatmap\n", "    sns.heatmap(avg_r_matrix, annot=True, fmt=\".2f\", cmap=\"coolwarm\", linewidths=0.5, ax=axes[0])\n", "    axes[0].set_title(\"Average PnL (R) - ORB Strategy\")\n", "    axes[0].set_xlabel(\"Profit Target (R)\")\n", "    axes[0].set_ylabel(\"Stop Loss (% ATR)\")\n", "    \n", "    # Plot total R heatmap\n", "    sns.heatmap(total_r_matrix, annot=True, fmt=\".1f\", cmap=\"RdYlGn\", linewidths=0.5, ax=axes[1])\n", "    axes[1].set_title(\"Total PnL (R) - ORB Strategy\")\n", "    axes[1].set_xlabel(\"Profit Target (R)\")\n", "    axes[1].set_ylabel(\"Stop Loss (% ATR)\")\n", "    \n", "    # Plot trade count heatmap\n", "    sns.heatmap(trade_count_matrix, annot=True, fmt=\"d\", cmap=\"YlGnBu\", linewidths=0.5, ax=axes[2])\n", "    axes[2].set_title(\"Trade Count - ORB Strategy\")\n", "    axes[2].set_xlabel(\"Profit Target (R)\")\n", "    axes[2].set_ylabel(\"Stop Loss (% ATR)\")\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Run simulation with the same parameters\n", "stop_list = [0.05, 0.10, 0.15, 0.20, 0.25]  # 5% to 25% of ATR\n", "profit_list = list(range(1, 11))           # 1R to 10R\n", "\n", "avg_r_matrix, total_r_matrix, trade_count_matrix = simulate_orb_matrix(tqqq_data, stop_list, profit_list)\n", "\n", "# Plot all three heatmaps\n", "plot_orb_heatmaps(avg_r_matrix, total_r_matrix, trade_count_matrix)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Avg PnL (R): 0.6719992421102908\n"]}], "source": ["# ORB with ATR + Imbalance, using next bar open as entry\n", "def run_orb_strategy_with_imbalance(df, stop_pct=0.10, profit_r=10, min_body_strength=0.6, min_clv=0.8):\n", "    df = df.copy()\n", "    df['date'] = df.index.date\n", "    df = calculate_daily_atr(df)  # Make sure this function is defined\n", "\n", "    results = []\n", "    grouped = df.groupby('date')\n", "\n", "    for date, day_data in grouped:\n", "        day_data = day_data.between_time(\"09:30\", \"16:00\")\n", "        if len(day_data) < 10:\n", "            continue\n", "\n", "        atr_14 = day_data['ATR'].iloc[0]\n", "        if np.isnan(atr_14):\n", "            continue\n", "\n", "        # First 5-min candles (opening range)\n", "        open_range = day_data.iloc[:5]\n", "        range_high = open_range['high'].max()\n", "        range_low = open_range['low'].min()\n", "        \n", "        # Analyze first candle for imbalance\n", "        first_open = open_range['open'].iloc[0]\n", "        first_close = open_range['close'].iloc[0]\n", "        first_high = open_range['high'].iloc[0]\n", "        first_low = open_range['low'].iloc[0]\n", "        \n", "        first_range_size = first_high - first_low\n", "        first_body_size = abs(first_close - first_open)\n", "        first_body_strength = first_body_size / first_range_size if first_range_size != 0 else 0\n", "        \n", "        is_first_bullish = first_close > first_open\n", "        if first_open == first_close:\n", "            continue  # doji\n", "            \n", "        # Calculate CLV for first candle\n", "        first_clv = ((first_close - first_low) / first_range_size) if is_first_bullish else \\\n", "                   ((first_high - first_close) / first_range_size)\n", "                   \n", "        # Filter for imbalance in first candle\n", "        if first_body_strength < min_body_strength or first_clv < min_clv:\n", "            continue\n", "\n", "        # Find breakout after opening range\n", "        post_range = day_data.iloc[5:]\n", "        breakout_bar = None\n", "        breakout_type = None\n", "\n", "        for ts, row in post_range.iterrows():\n", "            if row['high'] > range_high:\n", "                breakout_bar = ts\n", "                breakout_type = 'Long'\n", "                break\n", "            elif row['low'] < range_low:\n", "                breakout_bar = ts\n", "                breakout_type = 'Short'\n", "                break\n", "                \n", "        if breakout_bar is None:\n", "            continue  # No breakout found\n", "            \n", "        # Check if breakout direction aligns with first candle direction\n", "        if (breakout_type == 'Long' and not is_first_bullish) or \\\n", "           (breakout_type == 'Short' and is_first_bullish):\n", "            continue  # Breakout doesn't match first candle direction\n", "            \n", "        # Get the bar index where the breakout happened\n", "        breakout_idx = post_range.index.get_loc(breakout_bar)\n", "        \n", "        # Get the NEXT bar after the breakout for entry\n", "        if breakout_idx + 1 >= len(post_range):\n", "            continue  # No next bar available for entry\n", "            \n", "        entry_bar = post_range.index[breakout_idx + 1]\n", "        entry_price = post_range.loc[entry_bar, 'open']  # Use next bar's open price\n", "        \n", "        # Calculate risk based on ATR\n", "        risk = atr_14 * stop_pct\n", "        if risk <= 0.05:  # Minimum risk filter\n", "            continue\n", "            \n", "        # Calculate stop and target\n", "        if breakout_type == 'Long':\n", "            stop_price = entry_price - risk\n", "            target_price = entry_price + profit_r * risk\n", "            direction = 'Long'\n", "        else:  # Short\n", "            stop_price = entry_price + risk\n", "            target_price = entry_price - profit_r * risk\n", "            direction = 'Short'\n", "\n", "        # Analyze trade outcome from entry bar onwards\n", "        trade_data = day_data.loc[entry_bar:]\n", "        exit_price = None\n", "        exit_reason = None\n", "\n", "        for ts, row in trade_data.iterrows():\n", "            high = row['high']\n", "            low = row['low']\n", "\n", "            if direction == 'Long':\n", "                if low <= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif high >= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "            else:  # Short\n", "                if high >= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif low <= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "\n", "        if exit_price is None:\n", "            # Exit at EOD\n", "            exit_price = trade_data.iloc[-1]['close']\n", "            exit_reason = 'eod'\n", "\n", "        # Calculate P&L in R multiples\n", "        pnl_r = (exit_price - entry_price) / risk if direction == 'Long' else (entry_price - exit_price) / risk\n", "        pnl_r = min(pnl_r, profit_r) if pnl_r > 0 else pnl_r  # Cap at profit_r\n", "\n", "        # Add trade to results\n", "        results.append({\n", "            'date': date,\n", "            'direction': direction,\n", "            'entry': entry_price,\n", "            'exit': exit_price,\n", "            'pnl_r': pnl_r,\n", "            'exit_reason': exit_reason,\n", "            'body_strength': round(first_body_strength, 2),\n", "            'clv': round(first_clv, 2),\n", "            'risk': risk\n", "        })\n", "\n", "    return pd.DataFrame(results)\n", "\n", "df_orb_imbalance = run_orb_strategy_with_imbalance(tqqq_data, stop_pct=0.10, profit_r=10, min_body_strength=0.6, min_clv=0.6)\n", "print(\"Avg PnL (R):\", df_orb_imbalance['pnl_r'].mean())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x600 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Heatmap of ORB ATR + IMbalance - clv vs body strength\n", "def simulate_imbalance_matrix(df, stop_pct=0.10, profit_r=10,\n", "                              body_strength_vals=[0.2, 0.4, 0.6, 0.8],\n", "                              clv_vals=[0.6, 0.7, 0.8, 0.9]):\n", "    pnl_matrix = pd.DataFrame(index=body_strength_vals, columns=clv_vals)\n", "    trade_count_matrix = pd.DataFrame(index=body_strength_vals, columns=clv_vals)\n", "    total_r_matrix = pd.DataFrame(index=body_strength_vals, columns=clv_vals)\n", "\n", "    for body_th in body_strength_vals:\n", "        for clv_th in clv_vals:\n", "            result = run_orb_strategy_with_imbalance(\n", "                df, stop_pct=stop_pct, profit_r=profit_r,\n", "                min_body_strength=body_th, min_clv=clv_th\n", "            )\n", "            pnl_matrix.loc[body_th, clv_th] = result['pnl_r'].mean() if not result.empty else np.nan\n", "            trade_count_matrix.loc[body_th, clv_th] = len(result)\n", "            # Calculate total R - the sum of all returns\n", "            total_r_matrix.loc[body_th, clv_th] = result['pnl_r'].sum() if not result.empty else np.nan\n", "\n", "    pnl_matrix = pnl_matrix.astype(float)\n", "    trade_count_matrix = trade_count_matrix.astype(int)\n", "    total_r_matrix = total_r_matrix.astype(float)\n", "    return pnl_matrix, trade_count_matrix, total_r_matrix\n", "\n", "def plot_imbalance_heatmaps(pnl_matrix, trade_matrix, total_r_matrix):\n", "    fig, ax = plt.subplots(1, 3, figsize=(18, 6))\n", "\n", "    sns.heatmap(pnl_matrix, annot=True, fmt=\".2f\", cmap=\"coolwarm\", ax=ax[0])\n", "    ax[0].set_title(\"Avg PnL (R) by Imbalance Filters\")\n", "    ax[0].set_xlabel(\"Min CLV\")\n", "    ax[0].set_ylabel(\"Min Body Strength\")\n", "\n", "    sns.heatmap(total_r_matrix, annot=True, fmt=\".1f\", cmap=\"RdYlGn\", ax=ax[1])\n", "    ax[1].set_title(\"Total PnL (R) by Imbalance Filters\")\n", "    ax[1].set_xlabel(\"Min CLV\")\n", "    ax[1].set_ylabel(\"Min Body Strength\")\n", "\n", "    sns.heatmap(trade_matrix, annot=True, fmt=\"d\", cmap=\"YlGnBu\", ax=ax[2])\n", "    ax[2].set_title(\"Trade Count by Imbalance Filters\")\n", "    ax[2].set_xlabel(\"Min CLV\")\n", "    ax[2].set_ylabel(\"Min Body Strength\")\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Run the updated simulation\n", "pnl_matrix, trade_matrix, total_r_matrix = simulate_imbalance_matrix(\n", "    tqqq_data, stop_pct=0.10, profit_r=10\n", ")\n", "\n", "# Plot the results\n", "plot_imbalance_heatmaps(pnl_matrix, trade_matrix, total_r_matrix)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Avg PnL (R): 0.14544232325369089\n", "Trades taken: 311\n"]}], "source": ["# ORB Simple\n", "\n", "def run_orb_strategy_simple(df, profit_r=10):\n", "    df = df.copy()\n", "    df['date'] = df.index.date\n", "\n", "    results = []\n", "    grouped = df.groupby('date')\n", "\n", "    for date, day_data in grouped:\n", "        day_data = day_data.between_time(\"09:30\", \"16:00\")\n", "        if len(day_data) < 10:\n", "            continue\n", "\n", "        # Opening range\n", "        open_range = day_data.iloc[:5]\n", "        range_high = open_range['high'].max()\n", "        range_low = open_range['low'].min()\n", "\n", "        post_range = day_data.iloc[5:]\n", "        breakout_found = False\n", "        breakout_row_idx = None\n", "        breakout_type = None\n", "\n", "        # First find the breakout\n", "        for i, (ts, row) in enumerate(post_range.iterrows()):\n", "            if not breakout_found:\n", "                if row['high'] > range_high:\n", "                    # Long breakout detected\n", "                    breakout_found = True\n", "                    breakout_row_idx = i\n", "                    breakout_type = 'Long'\n", "                    break\n", "\n", "                elif row['low'] < range_low:\n", "                    # Short breakout detected\n", "                    breakout_found = True\n", "                    breakout_row_idx = i\n", "                    breakout_type = 'Short'\n", "                    break\n", "\n", "        if not breakout_found or breakout_row_idx + 1 >= len(post_range):\n", "            continue  # No breakout or no next bar for entry\n", "\n", "        # Get the next bar for entry\n", "        next_bar_index = breakout_row_idx + 1\n", "        next_bar = post_range.iloc[next_bar_index]\n", "        entry_time = next_bar.name\n", "        entry_price = next_bar['open']  # Use next bar's open price for entry\n", "        \n", "        # Calculate stop and target\n", "        if breakout_type == 'Long':\n", "            stop_price = range_low\n", "            risk = entry_price - stop_price\n", "            if risk <= 0.05:  # Minimum risk filter\n", "                continue\n", "            target_price = entry_price + profit_r * risk\n", "            direction = 'Long'\n", "        else:  # Short\n", "            stop_price = range_high\n", "            risk = stop_price - entry_price\n", "            if risk <= 0.05:  # Minimum risk filter\n", "                continue\n", "            target_price = entry_price - profit_r * risk\n", "            direction = 'Short'\n", "\n", "        # Analyze trade outcome\n", "        trade_data = day_data.loc[entry_time:]\n", "        exit_price = None\n", "        exit_reason = None\n", "\n", "        for ts, row in trade_data.iterrows():\n", "            high = row['high']\n", "            low = row['low']\n", "\n", "            if direction == 'Long':\n", "                if low <= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif high >= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "            else:  # Short\n", "                if high >= stop_price:\n", "                    exit_price = stop_price\n", "                    exit_reason = 'stop'\n", "                    break\n", "                elif low <= target_price:\n", "                    exit_price = target_price\n", "                    exit_reason = 'target'\n", "                    break\n", "\n", "        if exit_price is None:\n", "            # Exit at EOD\n", "            exit_price = trade_data.iloc[-1]['close']\n", "            exit_reason = 'eod'\n", "\n", "        # Calculate P&L in R multiples\n", "        pnl_r = (exit_price - entry_price) / risk if direction == 'Long' else (entry_price - exit_price) / risk\n", "        pnl_r = min(pnl_r, profit_r) if pnl_r > 0 else pnl_r  # Cap at profit_r\n", "\n", "        # Add trade to results\n", "        results.append({\n", "            'date': date,\n", "            'direction': direction,\n", "            'entry': entry_price,\n", "            'exit': exit_price,\n", "            'pnl_r': pnl_r,\n", "            'exit_reason': exit_reason,\n", "            'risk': risk\n", "        })\n", "\n", "    return pd.DataFrame(results)\n", "\n", "simple_results = run_orb_strategy_simple(tqqq_data, profit_r=10)\n", "print(\"Avg PnL (R):\", simple_results['pnl_r'].mean())\n", "print(f\"Trades taken: {len(simple_results)}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# R (return ) of orb vs orb atr vs orb atr + imbalance\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "orb_simple = run_orb_strategy_simple(tqqq_data, profit_r=10)\n", "orb_imbalance = run_orb_strategy_with_imbalance(tqqq_data, stop_pct=0.10, profit_r=10, min_body_strength=0.6, min_clv=0.6)\n", "orb_atr = run_orb_strategy(tqqq_data, stop_pct=0.10, profit_r=10)\n", "\n", "def plot_pnl_distribution():\n", "    plt.figure(figsize=(10, 6))\n", "\n", "    sns.kdeplot(orb_simple['pnl_r'], label='ORB Simple', fill=True, alpha=0.4, linewidth=2)\n", "    sns.kdeplot(orb_imbalance['pnl_r'], label='ORB ATR+Imbalance', fill=True, alpha=0.4, linewidth=2)\n", "    sns.kdeplot(orb_atr['PnL_R'], label='ORB ATR', fill=True, alpha=0.4, linewidth=2)\n", "\n", "    plt.axvline(0, color='gray', linestyle='--', lw=1)\n", "    plt.title(\"Distribution of Trade PnL (in R units)\")\n", "    plt.xlabel(\"PnL (R)\")\n", "    plt.ylabel(\"Density\")\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "\n", "plot_pnl_distribution()\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# orb vs orb atr vs orb atr imbalance - cumulative r\n", "def plot_cumulative_r():\n", "    plt.figure(figsize=(12, 6))\n", "\n", "    # Prepare cumulative R for each strategy\n", "    orb_simple['cumulative_r'] = orb_simple['pnl_r'].cumsum()\n", "    orb_imbalance['cumulative_r'] = orb_imbalance['pnl_r'].cumsum()\n", "    orb_atr['cumulative_r'] = orb_atr['PnL_R'].cumsum()\n", "\n", "    # Sort by date to ensure clean cumulative line\n", "    orb_simple_sorted = orb_simple.sort_values('date')\n", "    orb_imbalance_sorted = orb_imbalance.sort_values('date')\n", "    orb_atr_sorted = orb_atr.sort_values('Date')\n", "\n", "    # Plot\n", "    plt.plot(orb_simple_sorted['date'], orb_simple_sorted['cumulative_r'], label='ORB Simple', linewidth=2)\n", "    plt.plot(orb_imbalance_sorted['date'], orb_imbalance_sorted['cumulative_r'], label='ORB ATR+Imbalance', linewidth=2)\n", "    plt.plot(orb_atr_sorted['Date'], orb_atr_sorted['cumulative_r'], label='ORB ATR', linewidth=2)\n", "\n", "    plt.title(\"Cumulative PnL in R Units\")\n", "    plt.xlabel(\"Date\")\n", "    plt.ylabel(\"Cumulative R\")\n", "    plt.grid(True)\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "plot_cumulative_r()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}