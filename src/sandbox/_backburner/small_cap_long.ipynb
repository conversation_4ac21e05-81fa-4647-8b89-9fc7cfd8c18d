{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Libraries\n", "import os\n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(preload_months=12*5, source=\"polygon\")  # Only available since 2020-04-01\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"Fetches daily data using the configured daily_market_data instance.\"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        return None\n", "\n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>active</th>\n", "      <th>address</th>\n", "      <th>branding</th>\n", "      <th>cik</th>\n", "      <th>composite_figi</th>\n", "      <th>currency_name</th>\n", "      <th>currency_symbol</th>\n", "      <th>base_currency_name</th>\n", "      <th>base_currency_symbol</th>\n", "      <th>delisted_utc</th>\n", "      <th>...</th>\n", "      <th>phone_number</th>\n", "      <th>primary_exchange</th>\n", "      <th>share_class_figi</th>\n", "      <th>share_class_shares_outstanding</th>\n", "      <th>sic_code</th>\n", "      <th>sic_description</th>\n", "      <th>ticker</th>\n", "      <th>total_employees</th>\n", "      <th>type</th>\n", "      <th>weighted_shares_outstanding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>True</td>\n", "      <td>{'address1': '5301 STEVENS CREEK BLVD', 'addre...</td>\n", "      <td>{'accent_color': None, 'dark_color': None, 'ic...</td>\n", "      <td>1090872.0</td>\n", "      <td>BBG000C2V3D6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>(*************</td>\n", "      <td>XNYS</td>\n", "      <td>BBG001SCTQY4</td>\n", "      <td>285600000.0</td>\n", "      <td>3826.0</td>\n", "      <td>LABORATORY ANALYTICAL INSTRUMENTS</td>\n", "      <td>A</td>\n", "      <td>17900.0</td>\n", "      <td>CS</td>\n", "      <td>285595302.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>{'address1': '201 ISABELLA STREET', 'address2'...</td>\n", "      <td>{'accent_color': None, 'dark_color': None, 'ic...</td>\n", "      <td>1675149.0</td>\n", "      <td>BBG00B3T3HD3</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>************</td>\n", "      <td>XNYS</td>\n", "      <td>BBG00B3T3HF1</td>\n", "      <td>258360000.0</td>\n", "      <td>3334.0</td>\n", "      <td>PRIMARY PRODUCTION OF ALUMINUM</td>\n", "      <td>AA</td>\n", "      <td>300.0</td>\n", "      <td>CS</td>\n", "      <td>258354828.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>True</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>BBG01B0JRCS6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>ARCX</td>\n", "      <td>BBG01B0JRCT5</td>\n", "      <td>1300000.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>AAA</td>\n", "      <td>NaN</td>\n", "      <td>ETF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>True</td>\n", "      <td>{'address1': '240 GREENWICH STREET, 8TH FLOOR'...</td>\n", "      <td>None</td>\n", "      <td>1708646.0</td>\n", "      <td>BBG00LPXX872</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>************</td>\n", "      <td>BATS</td>\n", "      <td>BBG00LPXX8Z1</td>\n", "      <td>34730000.0</td>\n", "      <td>6221.0</td>\n", "      <td>COMMODITY CONTRACTS BROKERS &amp; DEALERS</td>\n", "      <td>AAAU</td>\n", "      <td>NaN</td>\n", "      <td>ETF</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>True</td>\n", "      <td>None</td>\n", "      <td>{'accent_color': None, 'dark_color': None, 'ic...</td>\n", "      <td>1420529.0</td>\n", "      <td>BBG000V2S3P6</td>\n", "      <td>usd</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>XNAS</td>\n", "      <td>BBG001T125S9</td>\n", "      <td>63930000.0</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>AACG</td>\n", "      <td>581.0</td>\n", "      <td>ADRC</td>\n", "      <td>31473874.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 29 columns</p>\n", "</div>"], "text/plain": ["   active                                            address  \\\n", "0    True  {'address1': '5301 STEVENS CREEK BLVD', 'addre...   \n", "1    True  {'address1': '201 ISABELLA STREET', 'address2'...   \n", "2    True                                               None   \n", "3    True  {'address1': '240 GREENWICH STREET, 8TH FLOOR'...   \n", "4    True                                               None   \n", "\n", "                                            branding        cik  \\\n", "0  {'accent_color': None, 'dark_color': None, 'ic...  1090872.0   \n", "1  {'accent_color': None, 'dark_color': None, 'ic...  1675149.0   \n", "2                                               None        NaN   \n", "3                                               None  1708646.0   \n", "4  {'accent_color': None, 'dark_color': None, 'ic...  1420529.0   \n", "\n", "  composite_figi currency_name  currency_symbol  base_currency_name  \\\n", "0   BBG000C2V3D6           usd              NaN                 NaN   \n", "1   BBG00B3T3HD3           usd              NaN                 NaN   \n", "2   BBG01B0JRCS6           usd              NaN                 NaN   \n", "3   BBG00LPXX872           usd              NaN                 NaN   \n", "4   BBG000V2S3P6           usd              NaN                 NaN   \n", "\n", "   base_currency_symbol  delisted_utc  ...    phone_number primary_exchange  \\\n", "0                   NaN           NaN  ...  (*************             XNYS   \n", "1                   NaN           NaN  ...    ************             XNYS   \n", "2                   NaN           NaN  ...            None             ARCX   \n", "3                   NaN           NaN  ...    ************             BATS   \n", "4                   NaN           NaN  ...            None             XNAS   \n", "\n", "  share_class_figi share_class_shares_outstanding sic_code  \\\n", "0     BBG001SCTQY4                    285600000.0   3826.0   \n", "1     BBG00B3T3HF1                    258360000.0   3334.0   \n", "2     BBG01B0JRCT5                      1300000.0      NaN   \n", "3     BBG00LPXX8Z1                     34730000.0   6221.0   \n", "4     BBG001T125S9                     63930000.0      NaN   \n", "\n", "                         sic_description ticker  total_employees  type  \\\n", "0      LABORATORY ANALYTICAL INSTRUMENTS      A          17900.0    CS   \n", "1         PRIMARY PRODUCTION OF ALUMINUM     AA            300.0    CS   \n", "2                                   None    AAA              NaN   ETF   \n", "3  COMMODITY CONTRACTS BROKERS & DEALERS   AAAU              NaN   ETF   \n", "4                                   None   AACG            581.0  ADRC   \n", "\n", "  weighted_shares_outstanding  \n", "0                 285595302.0  \n", "1                 258354828.0  \n", "2                         NaN  \n", "3                         NaN  \n", "4                  31473874.0  \n", "\n", "[5 rows x 29 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from tickers.ticker_helpers import load_cached_ticker_data\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2024, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 3, 31))\n", "\n", "ticker_df = load_cached_ticker_data(start_dt)\n", "\n", "min_market_cap = 2_000_000  # 2M\n", "max_market_cap = 100_000_000  # 100M\n", "\n", "market_cap_filter = (\n", "            ticker_df[\"market_cap\"].notna() &\n", "            (ticker_df[\"market_cap\"] >= min_market_cap) &\n", "            (ticker_df[\"market_cap\"] <= max_market_cap)\n", "        )\n", "small_cap_tickers = set(ticker_df.loc[market_cap_filter, \"ticker\"])\n", "\n", "ticker_df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def find_large_gap_ups(tickers, start_dt, end_dt, min_gap_percent=100):\n", "    \"\"\"\n", "    Find stocks that gapped up by more than min_gap_percent.\n", "    \n", "    Args:\n", "        tickers (list): List of tickers to analyze\n", "        start_dt (datetime): Start date for analysis\n", "        end_dt (datetime): End date for analysis\n", "        min_gap_percent (float): Minimum gap up percentage to include\n", "        \n", "    Returns:\n", "        DataFrame: Contains ticker, date, gap_up_pct, prev_close, and other data\n", "    \"\"\"\n", "    results = []\n", "    \n", "    for ticker in tickers:\n", "        # Get daily data for the ticker\n", "        daily_data = get_daily_data(ticker, start_dt, end_dt)\n", "        \n", "        if daily_data is None or len(daily_data) < 4:  # Need at least 4 days for 3 previous days\n", "            continue\n", "        \n", "        # Calculate gap up percentage between previous day's close and current day's open\n", "        daily_data['prev_close'] = daily_data['close'].shift(1)\n", "        daily_data['gap_up_pct'] = (daily_data['open'] - daily_data['prev_close']) / daily_data['prev_close'] * 100\n", "        \n", "        # Calculate previous day's gap percentages\n", "        daily_data['prev_1_day_gap'] = daily_data['gap_up_pct'].shift(1)\n", "        daily_data['prev_2_day_gap'] = daily_data['gap_up_pct'].shift(2)\n", "        daily_data['prev_3_day_gap'] = daily_data['gap_up_pct'].shift(3)\n", "        \n", "        # Filter for gaps greater than min_gap_percent\n", "        gap_events = daily_data[daily_data['gap_up_pct'] > min_gap_percent].copy()\n", "        \n", "        if len(gap_events) > 0:\n", "            gap_events['ticker'] = ticker\n", "            gap_events.reset_index(inplace=True)  # Move date to column\n", "            results.append(gap_events[['ticker', 'date', 'gap_up_pct', 'prev_close', 'open', \n", "                                      'prev_1_day_gap', 'prev_2_day_gap', 'prev_3_day_gap']])\n", "    \n", "    if len(results) > 0:\n", "        # Combine all results and sort by gap_up_pct descending\n", "        all_gaps = pd.concat(results)\n", "        return all_gaps.sort_values('gap_up_pct', ascending=False)\n", "    else:\n", "        return pd.DataFrame(columns=['ticker', 'date', 'gap_up_pct', 'prev_close', 'open', \n", "                                    'prev_1_day_gap', 'prev_2_day_gap', 'prev_3_day_gap'])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 249 gap up events > 100%\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ticker</th>\n", "      <th>date</th>\n", "      <th>gap_up_pct</th>\n", "      <th>prev_close</th>\n", "      <th>open</th>\n", "      <th>prev_1_day_gap</th>\n", "      <th>prev_2_day_gap</th>\n", "      <th>prev_3_day_gap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NITO</td>\n", "      <td>2025-01-03</td>\n", "      <td>105.607477</td>\n", "      <td>1.0700</td>\n", "      <td>2.2000</td>\n", "      <td>77.379032</td>\n", "      <td>-3.169014</td>\n", "      <td>0.040016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>JL</td>\n", "      <td>2024-02-29</td>\n", "      <td>105.468750</td>\n", "      <td>12.8000</td>\n", "      <td>26.3000</td>\n", "      <td>0.744417</td>\n", "      <td>-0.676819</td>\n", "      <td>-0.152362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HOTH</td>\n", "      <td>2025-01-07</td>\n", "      <td>104.978038</td>\n", "      <td>0.8196</td>\n", "      <td>1.6800</td>\n", "      <td>1.935484</td>\n", "      <td>1.999740</td>\n", "      <td>1.122845</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>LGVN</td>\n", "      <td>2024-07-10</td>\n", "      <td>104.529617</td>\n", "      <td>2.8700</td>\n", "      <td>5.8700</td>\n", "      <td>4.830918</td>\n", "      <td>0.000000</td>\n", "      <td>-1.818182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>LYT</td>\n", "      <td>2025-02-19</td>\n", "      <td>102.156334</td>\n", "      <td>0.2226</td>\n", "      <td>0.4500</td>\n", "      <td>0.000000</td>\n", "      <td>1.960784</td>\n", "      <td>-2.572097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ZCAR</td>\n", "      <td>2024-12-26</td>\n", "      <td>102.097902</td>\n", "      <td>1.4300</td>\n", "      <td>2.8900</td>\n", "      <td>3.333333</td>\n", "      <td>4.487179</td>\n", "      <td>-9.842520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KWE</td>\n", "      <td>2024-06-10</td>\n", "      <td>101.800000</td>\n", "      <td>0.4000</td>\n", "      <td>0.8072</td>\n", "      <td>-0.210084</td>\n", "      <td>-1.829268</td>\n", "      <td>0.367197</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TCTM</td>\n", "      <td>2024-02-16</td>\n", "      <td>101.579385</td>\n", "      <td>0.9624</td>\n", "      <td>1.9400</td>\n", "      <td>-4.090000</td>\n", "      <td>3.626943</td>\n", "      <td>7.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AKAN</td>\n", "      <td>2024-11-14</td>\n", "      <td>101.192250</td>\n", "      <td>0.6710</td>\n", "      <td>1.3500</td>\n", "      <td>2.231821</td>\n", "      <td>-8.797386</td>\n", "      <td>-3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CNVS</td>\n", "      <td>2024-02-14</td>\n", "      <td>100.645161</td>\n", "      <td>1.5500</td>\n", "      <td>3.1100</td>\n", "      <td>2.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.699301</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  ticker       date  gap_up_pct  prev_close     open  prev_1_day_gap  \\\n", "0   NITO 2025-01-03  105.607477      1.0700   2.2000       77.379032   \n", "0     JL 2024-02-29  105.468750     12.8000  26.3000        0.744417   \n", "0   HOTH 2025-01-07  104.978038      0.8196   1.6800        1.935484   \n", "1   LGVN 2024-07-10  104.529617      2.8700   5.8700        4.830918   \n", "4    LYT 2025-02-19  102.156334      0.2226   0.4500        0.000000   \n", "1   ZCAR 2024-12-26  102.097902      1.4300   2.8900        3.333333   \n", "0    KWE 2024-06-10  101.800000      0.4000   0.8072       -0.210084   \n", "0   TCTM 2024-02-16  101.579385      0.9624   1.9400       -4.090000   \n", "3   AKAN 2024-11-14  101.192250      0.6710   1.3500        2.231821   \n", "0   CNVS 2024-02-14  100.645161      1.5500   3.1100        2.000000   \n", "\n", "   prev_2_day_gap  prev_3_day_gap  \n", "0       -3.169014        0.040016  \n", "0       -0.676819       -0.152362  \n", "0        1.999740        1.122845  \n", "1        0.000000       -1.818182  \n", "4        1.960784       -2.572097  \n", "1        4.487179       -9.842520  \n", "0       -1.829268        0.367197  \n", "0        3.626943        7.000000  \n", "3       -8.797386       -3.000000  \n", "0        0.000000        0.699301  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Convert small_cap_tickers set to list for processing\n", "small_cap_list = list(small_cap_tickers)[:500]\n", "\n", "# Find large gap ups\n", "large_gap_ups = find_large_gap_ups(small_cap_list, start_dt, end_dt)\n", "\n", "# Display the results\n", "print(f\"Found {len(large_gap_ups)} gap up events > 100%\")\n", "display(large_gap_ups.tail(10))\n", "\n", "# Optional: Save results to CSV\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def get_intraday_for_gap_events(gap_events_df, market_open_time=\"09:30\", market_close_time=\"20:00\"):\n", "    \"\"\"\n", "    Get intraday data for each gap-up event from 4:00am EST to 8:00pm EST.\n", "    \n", "    Args:\n", "        gap_events_df (DataFrame): DataFrame with gap-up events\n", "        market_open_time (str): Market pre-open time (default: \"04:00\")\n", "        market_close_time (str): Market after-hours close time (default: \"20:00\")\n", "        \n", "    Returns:\n", "        dict: Dictionary with ticker_date as key and intraday DataFrame as value\n", "    \"\"\"\n", "    intraday_data = {}\n", "    \n", "    for _, row in gap_events_df.iterrows():\n", "        ticker = row['ticker']\n", "        event_date = row['date']\n", "        \n", "        # Convert event_date to datetime if it's not already\n", "        if isinstance(event_date, str):\n", "            event_date = pd.to_datetime(event_date)\n", "        \n", "        # Create datetime objects for start and end of the trading day\n", "        # Start at 4:00 AM EST and end at 8:00 PM EST\n", "        start_time = eastern_tz.localize(\n", "            datetime.combine(event_date.date(), \n", "                            datetime.strptime(market_open_time, \"%H:%M\").time()))\n", "        \n", "        end_time = eastern_tz.localize(\n", "            datetime.combine(event_date.date(), \n", "                            datetime.strptime(market_close_time, \"%H:%M\").time()))\n", "        \n", "        # Get per-minute data for the event day\n", "        try:\n", "            minute_data = get_per_minute_data(ticker, start_time, end_time)\n", "            \n", "            if minute_data is not None and not minute_data.empty:\n", "                # Add a key that combines ticker and date for easy reference\n", "                key = f\"{ticker}_{event_date.strftime('%Y-%m-%d')}\"\n", "                intraday_data[key] = minute_data\n", "                print(f\"Successfully retrieved intraday data for {key}\")\n", "            else:\n", "                print(f\"No intraday data available for {ticker} on {event_date.strftime('%Y-%m-%d')}\")\n", "                \n", "        except Exception as e:\n", "            print(f\"Error retrieving data for {ticker} on {event_date.strftime('%Y-%m-%d')}: {str(e)}\")\n", "    \n", "    return intraday_data\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully retrieved intraday data for VERB_2024-10-09\n", "Successfully retrieved intraday data for SINT_2024-05-28\n", "Successfully retrieved intraday data for BCAN_2024-03-22\n", "Successfully retrieved intraday data for CRKN_2025-01-30\n", "Successfully retrieved intraday data for CYN_2025-02-18\n", "Successfully retrieved intraday data for CRKN_2024-06-25\n", "Successfully retrieved intraday data for XPON_2024-10-09\n", "Successfully retrieved intraday data for APLM_2024-11-25\n", "Successfully retrieved intraday data for CYN_2024-07-05\n", "Successfully retrieved intraday data for ZCAR_2024-10-22\n", "Successfully retrieved intraday data for TNXP_2025-02-05\n", "Successfully retrieved intraday data for SMX_2024-07-15\n", "Successfully retrieved intraday data for CISS_2024-04-12\n", "Successfully retrieved intraday data for ATCH_2025-01-02\n", "Successfully retrieved intraday data for ATXI_2024-04-26\n", "Successfully retrieved intraday data for BENF_2024-04-18\n", "Successfully retrieved intraday data for LYT_2024-02-23\n", "Successfully retrieved intraday data for JAGX_2024-05-23\n", "Successfully retrieved intraday data for APDN_2025-03-14\n", "Successfully retrieved intraday data for PHUN_2024-02-27\n", "Successfully retrieved intraday data for MTNB_2024-09-03\n", "Successfully retrieved intraday data for VCIG_2024-11-05\n", "Successfully retrieved intraday data for QLGN_2024-11-05\n", "Successfully retrieved intraday data for APVO_2024-03-06\n", "Successfully retrieved intraday data for BNZI_2024-09-19\n", "Successfully retrieved intraday data for NLSP_2024-09-27\n", "Successfully retrieved intraday data for APVO_2024-12-04\n", "Successfully retrieved intraday data for KNW_2025-03-05\n", "Successfully retrieved intraday data for GPUS_2024-11-25\n", "Successfully retrieved intraday data for KITT_2024-07-23\n", "Successfully retrieved intraday data for CETX_2024-11-26\n", "Successfully retrieved intraday data for NUWE_2024-06-28\n", "Successfully retrieved intraday data for BHIL_2024-07-19\n", "Successfully retrieved intraday data for NBY_2024-05-31\n", "Successfully retrieved intraday data for AKAN_2024-05-23\n", "Successfully retrieved intraday data for ATPC_2024-08-30\n", "Successfully retrieved intraday data for APTO_2025-02-26\n", "Successfully retrieved intraday data for NKLA_2024-06-25\n", "Successfully retrieved intraday data for ADN_2024-05-14\n", "Successfully retrieved intraday data for TOVX_2024-08-26\n", "Successfully retrieved intraday data for UAVS_2024-10-15\n", "Successfully retrieved intraday data for CNEY_2024-01-19\n", "Successfully retrieved intraday data for CETX_2024-10-03\n", "Successfully retrieved intraday data for GOEV_2024-03-08\n", "Successfully retrieved intraday data for FBRX_2024-08-28\n", "Successfully retrieved intraday data for ALUR_2025-01-03\n", "Successfully retrieved intraday data for CJET_2024-07-08\n", "Successfully retrieved intraday data for SMX_2025-01-15\n", "Successfully retrieved intraday data for JAGX_2025-03-24\n", "Successfully retrieved intraday data for ATRA_2024-06-20\n", "Successfully retrieved intraday data for TNXP_2024-06-10\n", "Successfully retrieved intraday data for LGMK_2024-11-19\n", "Successfully retrieved intraday data for HAO_2025-01-27\n", "Successfully retrieved intraday data for EDBL_2025-03-03\n", "Successfully retrieved intraday data for BDRX_2024-10-04\n", "Successfully retrieved intraday data for GLTO_2024-08-30\n", "Successfully retrieved intraday data for ICCT_2024-12-23\n", "Successfully retrieved intraday data for SVRE_2024-10-28\n", "Successfully retrieved intraday data for INM_2024-11-14\n", "Successfully retrieved intraday data for MLGO_2024-12-13\n", "Successfully retrieved intraday data for ZCAR_2025-03-24\n", "Successfully retrieved intraday data for BLUE_2024-12-13\n", "Successfully retrieved intraday data for PPBT_2024-09-17\n", "Successfully retrieved intraday data for EDBL_2024-04-05\n", "Successfully retrieved intraday data for RENT_2024-04-03\n", "Successfully retrieved intraday data for SNOA_2024-08-30\n", "Successfully retrieved intraday data for TTNP_2024-01-09\n", "Successfully retrieved intraday data for UPXI_2024-10-03\n", "Successfully retrieved intraday data for APDN_2024-04-25\n", "Successfully retrieved intraday data for NXU_2025-03-31\n", "Successfully retrieved intraday data for UAVS_2024-02-09\n", "Successfully retrieved intraday data for ME_2024-10-16\n", "Successfully retrieved intraday data for TCBP_2025-02-10\n", "Successfully retrieved intraday data for LUCY_2024-07-18\n", "Successfully retrieved intraday data for ZAPP_2024-04-23\n", "Successfully retrieved intraday data for GOEV_2024-12-24\n", "Successfully retrieved intraday data for SPCB_2024-08-22\n", "Successfully retrieved intraday data for BCAN_2024-08-26\n", "Successfully retrieved intraday data for AMIX_2024-10-25\n", "Successfully retrieved intraday data for UBXG_2024-11-27\n", "Successfully retrieved intraday data for TC_2024-01-26\n", "Successfully retrieved intraday data for OPTN_2024-12-31\n", "Successfully retrieved intraday data for BCTX_2025-01-29\n", "Successfully retrieved intraday data for CISO_2024-03-08\n", "Successfully retrieved intraday data for ABTS_2025-03-10\n", "Successfully retrieved intraday data for SAIH_2025-03-17\n", "Successfully retrieved intraday data for GMM_2024-11-26\n", "Successfully retrieved intraday data for BCDA_2024-05-30\n", "Successfully retrieved intraday data for OTRK_2024-09-23\n", "Successfully retrieved intraday data for SVRE_2025-02-21\n", "Successfully retrieved intraday data for MBRX_2024-03-22\n", "Successfully retrieved intraday data for ONVO_2025-03-21\n", "Successfully retrieved intraday data for EVOK_2024-08-01\n", "Successfully retrieved intraday data for KWE_2024-10-23\n", "Successfully retrieved intraday data for VMAR_2024-08-22\n", "Successfully retrieved intraday data for ATER_2024-03-22\n", "Successfully retrieved intraday data for MLGO_2024-03-22\n", "Successfully retrieved intraday data for JL_2024-12-10\n", "Successfully retrieved intraday data for DM_2024-06-11\n", "Successfully retrieved intraday data for VERO_2025-03-04\n", "Successfully retrieved intraday data for CMCT_2025-01-06\n", "Successfully retrieved intraday data for OLB_2024-05-06\n", "Successfully retrieved intraday data for GV_2024-05-29\n", "Successfully retrieved intraday data for ALZN_2024-07-16\n", "Successfully retrieved intraday data for HYFM_2025-02-13\n", "Successfully retrieved intraday data for CING_2024-08-09\n", "Successfully retrieved intraday data for OMH_2025-03-10\n", "Successfully retrieved intraday data for IPDN_2025-03-13\n", "Successfully retrieved intraday data for SNTI_2024-07-18\n", "Successfully retrieved intraday data for INO_2024-01-25\n", "Successfully retrieved intraday data for VMAR_2025-03-31\n", "Successfully retrieved intraday data for RETO_2024-03-01\n", "Successfully retrieved intraday data for EMKR_2024-04-02\n", "Successfully retrieved intraday data for STAF_2024-06-26\n", "Successfully retrieved intraday data for BQ_2025-01-21\n", "Successfully retrieved intraday data for HUBC_2025-03-31\n", "Successfully retrieved intraday data for BNR_2024-05-15\n", "Successfully retrieved intraday data for CTRM_2024-03-27\n", "Successfully retrieved intraday data for EJH_2024-09-24\n", "Successfully retrieved intraday data for CTHR_2024-05-17\n", "Successfully retrieved intraday data for RETO_2025-03-07\n", "Successfully retrieved intraday data for EVAX_2024-01-22\n", "Successfully retrieved intraday data for LIPO_2024-11-08\n", "Successfully retrieved intraday data for MKFG_2024-09-19\n", "Successfully retrieved intraday data for AFMD_2024-03-11\n", "Successfully retrieved intraday data for SLXN_2024-11-29\n", "Successfully retrieved intraday data for PHIO_2024-07-05\n", "Successfully retrieved intraday data for VMAR_2024-10-08\n", "Successfully retrieved intraday data for LGVN_2024-03-27\n", "Successfully retrieved intraday data for JNVR_2024-12-30\n", "Successfully retrieved intraday data for SMSI_2024-04-11\n", "Successfully retrieved intraday data for MYSZ_2024-04-23\n", "Successfully retrieved intraday data for MNDR_2025-03-10\n", "Successfully retrieved intraday data for GSUN_2024-04-19\n", "Successfully retrieved intraday data for ASNS_2024-06-05\n", "Successfully retrieved intraday data for SGLY_2024-02-12\n", "Successfully retrieved intraday data for IVDA_2024-09-17\n", "Successfully retrieved intraday data for LFWD_2024-03-15\n", "Successfully retrieved intraday data for REBN_2024-01-22\n", "Successfully retrieved intraday data for PLUR_2024-04-01\n", "Successfully retrieved intraday data for TCBP_2024-08-05\n", "Successfully retrieved intraday data for IMCC_2024-07-12\n", "Successfully retrieved intraday data for STEC_2024-10-16\n", "Successfully retrieved intraday data for VANI_2024-02-28\n", "Successfully retrieved intraday data for LDTC_2024-12-09\n", "Successfully retrieved intraday data for ELWS_2024-05-16\n", "Successfully retrieved intraday data for ASST_2024-07-02\n", "Successfully retrieved intraday data for HWH_2025-02-24\n", "Successfully retrieved intraday data for INM_2024-08-20\n", "Successfully retrieved intraday data for EJH_2024-02-14\n", "Successfully retrieved intraday data for STRR_2024-06-17\n", "Successfully retrieved intraday data for EVAX_2025-01-14\n", "Successfully retrieved intraday data for LYT_2024-03-07\n", "Successfully retrieved intraday data for MYSZ_2024-12-20\n", "Successfully retrieved intraday data for OMH_2024-02-13\n", "Successfully retrieved intraday data for ZEPP_2024-09-16\n", "Successfully retrieved intraday data for JXG_2025-01-08\n", "Successfully retrieved intraday data for STEC_2025-02-24\n", "Successfully retrieved intraday data for MEGL_2025-02-18\n", "Successfully retrieved intraday data for LRHC_2024-02-22\n", "Successfully retrieved intraday data for NXU_2024-12-30\n", "Successfully retrieved intraday data for ELWS_2024-02-14\n", "Successfully retrieved intraday data for LIPO_2024-07-29\n", "Successfully retrieved intraday data for HCWB_2025-02-03\n", "Successfully retrieved intraday data for ALZN_2024-08-19\n", "Successfully retrieved intraday data for ITP_2024-12-31\n", "Successfully retrieved intraday data for COCH_2024-02-29\n", "Successfully retrieved intraday data for ONVO_2025-02-25\n", "Successfully retrieved intraday data for AREN_2024-11-15\n", "Successfully retrieved intraday data for SNTI_2024-12-02\n", "Successfully retrieved intraday data for MLGO_2024-06-04\n", "Successfully retrieved intraday data for HCWB_2024-11-18\n", "Successfully retrieved intraday data for BCTX_2024-09-11\n", "Successfully retrieved intraday data for UPXI_2024-10-25\n", "Successfully retrieved intraday data for NXU_2024-10-24\n", "Successfully retrieved intraday data for CPOP_2024-02-16\n", "Successfully retrieved intraday data for JZ_2024-02-20\n", "Successfully retrieved intraday data for VERB_2024-03-15\n", "Successfully retrieved intraday data for STAF_2024-10-17\n", "Successfully retrieved intraday data for IMNN_2024-07-30\n", "Successfully retrieved intraday data for ONMD_2024-05-23\n", "Successfully retrieved intraday data for LYT_2024-12-20\n", "Successfully retrieved intraday data for WBUY_2024-11-07\n", "Successfully retrieved intraday data for MLGO_2025-02-21\n", "Successfully retrieved intraday data for OTRK_2024-03-19\n", "Successfully retrieved intraday data for RETO_2025-02-24\n", "Successfully retrieved intraday data for ALUR_2025-01-24\n", "Successfully retrieved intraday data for LIPO_2025-02-06\n", "Successfully retrieved intraday data for CNEY_2024-12-04\n", "Successfully retrieved intraday data for VERO_2024-06-07\n", "Successfully retrieved intraday data for NLSP_2024-06-18\n", "Successfully retrieved intraday data for CHNR_2024-02-15\n", "Successfully retrieved intraday data for CISS_2025-01-02\n", "Successfully retrieved intraday data for AMIX_2024-10-28\n", "Successfully retrieved intraday data for ASST_2025-01-21\n", "Successfully retrieved intraday data for PEV_2024-10-04\n", "Successfully retrieved intraday data for ICCT_2025-03-31\n", "Successfully retrieved intraday data for STAF_2024-08-22\n", "Successfully retrieved intraday data for PHIO_2025-01-13\n", "Successfully retrieved intraday data for SINT_2024-05-15\n", "Successfully retrieved intraday data for ONMD_2024-01-29\n", "Successfully retrieved intraday data for XYLO_2024-08-05\n", "Successfully retrieved intraday data for TNXP_2024-12-17\n", "Successfully retrieved intraday data for AIFF_2025-01-14\n", "Successfully retrieved intraday data for PLRZ_2024-12-18\n", "Successfully retrieved intraday data for CPIX_2024-12-10\n", "Successfully retrieved intraday data for AKAN_2024-05-17\n", "Successfully retrieved intraday data for CNEY_2024-09-11\n", "Successfully retrieved intraday data for ATPC_2024-07-23\n", "Successfully retrieved intraday data for BKYI_2024-10-29\n", "Successfully retrieved intraday data for ADIL_2024-04-10\n", "Successfully retrieved intraday data for PEV_2025-02-13\n", "Successfully retrieved intraday data for CING_2024-08-15\n", "Successfully retrieved intraday data for BDRX_2024-07-11\n", "Successfully retrieved intraday data for BDRX_2024-04-26\n", "Successfully retrieved intraday data for CCTG_2024-02-08\n", "Successfully retrieved intraday data for ADN_2024-11-04\n", "Successfully retrieved intraday data for WBUY_2024-12-16\n", "Successfully retrieved intraday data for XPON_2024-09-20\n", "Successfully retrieved intraday data for MEGL_2025-01-06\n", "Successfully retrieved intraday data for LYT_2025-03-27\n", "Successfully retrieved intraday data for CING_2024-08-16\n", "Successfully retrieved intraday data for BDRX_2025-02-10\n", "Successfully retrieved intraday data for CPOP_2024-05-09\n", "Successfully retrieved intraday data for AKAN_2024-05-24\n", "Successfully retrieved intraday data for WBUY_2024-09-18\n", "Successfully retrieved intraday data for TCTM_2025-01-31\n", "Successfully retrieved intraday data for BNZI_2024-10-09\n", "Successfully retrieved intraday data for UAVS_2024-10-01\n", "Successfully retrieved intraday data for HSDT_2024-12-20\n", "Successfully retrieved intraday data for MYSZ_2024-12-27\n", "Successfully retrieved intraday data for EVOK_2024-10-28\n", "Successfully retrieved intraday data for QLGN_2024-07-05\n", "Successfully retrieved intraday data for ASNS_2024-06-18\n", "Successfully retrieved intraday data for SLXN_2025-01-28\n", "Successfully retrieved intraday data for SINT_2024-09-09\n", "Successfully retrieved intraday data for ATCH_2024-09-25\n", "Successfully retrieved intraday data for LYT_2024-01-18\n", "Successfully retrieved intraday data for CCG_2024-02-14\n", "Successfully retrieved intraday data for NITO_2025-01-03\n", "Successfully retrieved intraday data for JL_2024-02-29\n", "Successfully retrieved intraday data for HOTH_2025-01-07\n", "Successfully retrieved intraday data for LGVN_2024-07-10\n", "Successfully retrieved intraday data for LYT_2025-02-19\n", "Successfully retrieved intraday data for ZCAR_2024-12-26\n", "Successfully retrieved intraday data for KWE_2024-06-10\n", "Successfully retrieved intraday data for TCTM_2024-02-16\n", "Successfully retrieved intraday data for AKAN_2024-11-14\n", "Successfully retrieved intraday data for CNVS_2024-02-14\n", "Retrieved intraday data for 249 out of 249 gap-up events\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.dates as mdates\n", "\n", "# Get intraday data for the top gap-up events\n", "# You can limit the number of events to process to save time\n", "top_events = large_gap_ups # .sample(n=20)  # Process top 20 events or adjust as needed\n", "\n", "# Set market hours - from 4:00 AM EST to 8:00 PM EST\n", "intraday_gap_data = get_intraday_for_gap_events(top_events, market_open_time=\"04:00\", market_close_time=\"20:00\")\n", "\n", "# Print summary\n", "print(f\"Retrieved intraday data for {len(intraday_gap_data)} out of {len(top_events)} gap-up events\")\n", "\n", "# Optional: Visualize the price action for one of the events\n", "def plot_intraday_gap(ticker_date, intraday_data_dict):\n", "    \"\"\"Plot the intraday price action for a specific gap-up event.\"\"\"\n", "    if ticker_date not in intraday_data_dict:\n", "        print(f\"No data found for {ticker_date}\")\n", "        return\n", "    \n", "    # Get the data for this event\n", "    data = intraday_data_dict[ticker_date]\n", "    \n", "    # Extract ticker and date from the key\n", "    ticker, date_str = ticker_date.split('_')\n", "    \n", "    # Create the plot\n", "    plt.figure(figsize=(12, 6))\n", "    plt.plot(data.index, data['close'], label='Close Price')\n", "    \n", "    # Add markers for open and close\n", "    plt.scatter(data.index[0], data['open'].iloc[0], color='green', s=100, label='Open', zorder=5)\n", "    plt.scatter(data.index[-1], data['close'].iloc[-1], color='red', s=100, label='Close', zorder=5)\n", "    \n", "    # Format the plot\n", "    plt.title(f\"Intraday Price Action for {ticker} on {date_str}\", fontsize=16)\n", "    plt.xlabel('Time')\n", "    plt.ylabel('Price ($)')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.legend()\n", "    \n", "    # Format x-axis to show hours\n", "    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Example: Plot the first event in our dictionary (if any)\n", "if intraday_gap_data:\n", "    first_key = list(intraday_gap_data.keys())[0]\n", "    plot_intraday_gap(first_key, intraday_gap_data)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["def calculate_vwap(data):\n", "    \"\"\"Calculate VWAP based on price and volume.\"\"\"\n", "    data = data.copy()\n", "    data['vwap'] = (data['close'] * data['volume']).cumsum() / data['volume'].cumsum()\n", "    return data\n", "\n", "def simulate_vwap_second_pullback_strategy(intraday_data_dict, vwap_momentum_threshold=5.0, \n", "                                          initial_capital=100000, position_size_pct=10):\n", "    \"\"\"\n", "    Simulate VWAP Second Pullback trading strategy with premarket volume filter:\n", "    - Require at least $500K in premarket volume (4:00AM-9:30AM EST)\n", "    - Identify stocks where VWAP has increased by at least 5% from open (momentum)\n", "    - Enter on the second pullback to VWAP with a green candle crossing back above VWAP\n", "    - Use low of the day since market open as stop loss\n", "    - Take profit at high of the day since market open or market close\n", "    - Fixed position size of 10% of account (starting with 100K = 10K per position)\n", "    \"\"\"\n", "    trades = []\n", "    account_value = initial_capital\n", "    \n", "    for ticker_date, minute_data in intraday_data_dict.items():\n", "        ticker, date_str = ticker_date.split('_')\n", "        \n", "        # Skip if data is empty\n", "        if minute_data is None or minute_data.empty:\n", "            continue\n", "        \n", "        # Identify premarket data (4:00AM to 9:30AM EST)\n", "        premarket_data = minute_data[\n", "            ((minute_data.index.hour == 4) & (minute_data.index.minute >= 0)) |\n", "            ((minute_data.index.hour > 4) & (minute_data.index.hour < 9)) |\n", "            ((minute_data.index.hour == 9) & (minute_data.index.minute < 30))\n", "        ]\n", "        \n", "        # Calculate premarket dollar volume\n", "        premarket_dollar_volume = (premarket_data['close'] * premarket_data['volume']).sum()\n", "        \n", "        # Skip if premarket dollar volume is less than $500K\n", "        if premarket_dollar_volume < 500000:\n", "            continue\n", "        \n", "        # Calculate VWAP for the entire day\n", "        minute_data = calculate_vwap(minute_data)\n", "        \n", "        # Add green candle indicator (close > open)\n", "        minute_data['green_candle'] = minute_data['close'] > minute_data['open']\n", "        \n", "        # Get market open data (9:30AM EST)\n", "        market_open_data = minute_data[(minute_data.index.hour == 9) & (minute_data.index.minute == 30)]\n", "        if market_open_data.empty:\n", "            continue\n", "            \n", "        # Get market close data (16:00 or last available data)\n", "        market_close_data = minute_data[(minute_data.index.hour == 16) & (minute_data.index.minute == 0)]\n", "        if market_close_data.empty:\n", "            close_times = minute_data[minute_data.index.hour < 16]\n", "            market_close_data = close_times.iloc[[-1]] if not close_times.empty else None\n", "            if market_close_data is None:\n", "                continue\n", "        \n", "        # Skip first 30 minutes to avoid early volatility\n", "        trading_start_time = market_open_data.index[0] + pd.Timedelta(minutes=30)\n", "        trading_data = minute_data[minute_data.index > trading_start_time]\n", "        if trading_data.empty:\n", "            continue\n", "        \n", "        # Regular trading hours data (since market open)\n", "        rth_data = minute_data[minute_data.index >= market_open_data.index[0]]\n", "        \n", "        # Initialize variables for our strategy\n", "        vwap_at_open = market_open_data['vwap'].iloc[0]\n", "        current_highest_vwap = vwap_at_open\n", "        momentum_established = True\n", "        pullback_count = 0\n", "        \n", "        entry_made = False\n", "        entry_price = None\n", "        entry_time = None\n", "        exit_price = None\n", "        exit_time = None\n", "        exit_type = None\n", "        shares = 0\n", "        \n", "        # Two states to track: \n", "        # 1. Price below VWAP (potential pullback)\n", "        # 2. Price above VWAP (normal state)\n", "        price_below_vwap = False\n", "        \n", "        # Process each minute of data sequentially\n", "        for i in range(1, len(trading_data)):\n", "            prev_row = trading_data.iloc[i-1]\n", "            curr_row = trading_data.iloc[i]\n", "            \n", "            current_time = trading_data.index[i]\n", "            current_price = curr_row['close']\n", "            current_vwap = curr_row['vwap']\n", "            is_green_candle = curr_row['green_candle']\n", "            \n", "            # Update highest VWAP seen so far\n", "            if current_vwap > current_highest_vwap:\n", "                current_highest_vwap = current_vwap\n", "            \n", "            # Check if VWAP has increased by our threshold percentage\n", "            vwap_increase_pct = (current_highest_vwap - vwap_at_open) / vwap_at_open * 100\n", "            \n", "            # Check if we've established momentum (5% VWAP increase)\n", "            if not momentum_established and vwap_increase_pct >= vwap_momentum_threshold:\n", "                momentum_established = True\n", "            \n", "            # If momentum is established, look for pullbacks\n", "            if momentum_established and not entry_made:\n", "                # Detect crossing below VWAP (start of pullback)\n", "                prev_above_vwap = prev_row['close'] >= prev_row['vwap']\n", "                curr_below_vwap = current_price < current_vwap\n", "                \n", "                # Detect crossing above VWAP (end of pullback)\n", "                prev_below_vwap = prev_row['close'] < prev_row['vwap']\n", "                curr_above_vwap = current_price >= current_vwap\n", "                \n", "                # Detect start of a pullback\n", "                if prev_above_vwap and curr_below_vwap:\n", "                    price_below_vwap = True\n", "                \n", "                # Detect end of a pullback with a green candle crossing back above VWAP\n", "                if price_below_vwap and prev_below_vwap and curr_above_vwap and is_green_candle:\n", "                    pullback_count += 1\n", "                    price_below_vwap = False  # Reset for next potential pullback\n", "                    \n", "                    # If this is the second pullback, enter the trade\n", "                    if pullback_count == 2:\n", "                        entry_made = True\n", "                        entry_price = current_price\n", "                        entry_time = current_time\n", "                        \n", "                        # Calculate position size (10% of account)\n", "                        position_dollars = account_value * (position_size_pct / 100)\n", "                        shares = int(position_dollars / entry_price)\n", "                        \n", "                        # Get current low since market open (for stop loss)\n", "                        current_low_since_open =  rth_data[rth_data.index <= current_time]['low'].min()\n", "                        \n", "                        # Get current high since market open (for reference)\n", "                        current_high_since_open = rth_data[rth_data.index <= current_time]['high'].max()\n", "            \n", "            # If we have an open position, check for exit conditions\n", "            elif entry_made and exit_price is None:\n", "                # Get updated low/high since market open up to current time\n", "                current_rth_data = rth_data[rth_data.index <= current_time]\n", "                current_low_since_open = current_rth_data['low'].min()\n", "                current_high_since_open = current_rth_data['high'].max()\n", "                \n", "                # Check for stop loss hit (low of the day since market open)\n", "                if current_price <= current_low_since_open:\n", "                    exit_price = current_price\n", "                    exit_time = current_time\n", "                    exit_type = \"Stop Loss\"\n", "                \n", "                # Check for take profit hit (high of the day since market open)\n", "                elif current_price >= current_high_since_open:\n", "                    exit_price = current_price\n", "                    exit_time = current_time\n", "                    exit_type = \"Take Profit\"\n", "        \n", "        # If we entered but haven't exited, exit at market close\n", "        if entry_made and exit_price is None:\n", "            exit_price = market_close_data['close'].iloc[0]\n", "            exit_time = market_close_data.index[0]\n", "            exit_type = \"Market Close\"\n", "            \n", "            # Final low/high since market open for reporting\n", "            final_low_since_open = rth_data['low'].min()\n", "            final_high_since_open = rth_data['high'].max()\n", "        \n", "        # Record the trade if we made one\n", "        if entry_made:\n", "            # Calculate P&L\n", "            pnl_pct = (exit_price - entry_price) / entry_price * 100\n", "            pnl_dollars = shares * (exit_price - entry_price)\n", "            \n", "            # Update account value\n", "            account_value += pnl_dollars\n", "            \n", "            # Final low/high since market open (for reporting)\n", "            final_low_since_open = rth_data[rth_data.index <= exit_time]['low'].min()\n", "            final_high_since_open = rth_data[rth_data.index <= exit_time]['high'].max()\n", "            \n", "            trade_data = {\n", "                'ticker': ticker,\n", "                'date': date_str,\n", "                'premarket_dollar_volume': premarket_dollar_volume,\n", "                'entry_time': f\"{entry_time.hour:02d}:{entry_time.minute:02d}\",\n", "                'entry_price': entry_price,\n", "                'shares': shares,\n", "                'position_value': shares * entry_price,\n", "                'vwap_at_open': vwap_at_open,\n", "                'vwap_momentum_pct': vwap_increase_pct,\n", "                'low_since_open': final_low_since_open,\n", "                'high_since_open': final_high_since_open,\n", "                'exit_time': f\"{exit_time.hour:02d}:{exit_time.minute:02d}\",\n", "                'exit_price': exit_price,\n", "                'exit_type': exit_type,\n", "                'pnl_dollars': pnl_dollars,\n", "                'pnl_pct': pnl_pct,\n", "                'account_value': account_value\n", "            }\n", "            \n", "            trades.append(trade_data)\n", "    \n", "    # Convert to DataFrame\n", "    trades_df = pd.DataFrame(trades)\n", "    \n", "    # Calculate strategy performance metrics\n", "    if not trades_df.empty:\n", "        total_trades = len(trades_df)\n", "        win_trades = len(trades_df[trades_df['pnl_pct'] > 0])\n", "        win_rate = win_trades / total_trades * 100\n", "        avg_win = trades_df[trades_df['pnl_pct'] > 0]['pnl_pct'].mean() if win_trades > 0 else 0\n", "        avg_loss = trades_df[trades_df['pnl_pct'] <= 0]['pnl_pct'].mean() if total_trades - win_trades > 0 else 0\n", "        \n", "        final_account_value = trades_df.iloc[-1]['account_value'] if not trades_df.empty else initial_capital\n", "        total_return_pct = (final_account_value - initial_capital) / initial_capital * 100\n", "        \n", "        print(f\"Strategy Performance:\")\n", "        print(f\"Starting Capital: ${initial_capital:,.2f}\")\n", "        print(f\"Final Account Value: ${final_account_value:,.2f}\")\n", "        print(f\"Total Return: {total_return_pct:.2f}%\")\n", "        print(f\"Total Trades: {total_trades}\")\n", "        print(f\"Win Rate: {win_rate:.2f}%\")\n", "        print(f\"Average Win: {avg_win:.2f}%\")\n", "        print(f\"Average Loss: {avg_loss:.2f}%\")\n", "        \n", "        profit_factor = \"N/A\"\n", "        if avg_loss != 0 and (total_trades - win_trades) > 0:\n", "            profit_factor = abs(avg_win * win_trades / (avg_loss * (total_trades - win_trades)))\n", "            \n", "        print(f\"Profit Factor: {profit_factor if isinstance(profit_factor, str) else profit_factor:.2f}\")\n", "        print(f\"Average Trade: {trades_df['pnl_pct'].mean():.2f}%\")\n", "        \n", "        # Exit type breakdown\n", "        exit_counts = trades_df['exit_type'].value_counts()\n", "        print(\"\\nExit Type Breakdown:\")\n", "        for exit_type, count in exit_counts.items():\n", "            print(f\"{exit_type}: {count} trades ({count/total_trades*100:.1f}%)\")\n", "        \n", "        # Analyze premarket volume impact\n", "        print(\"\\nPremarket Volume Analysis:\")\n", "        print(f\"Avg Premarket $ Volume: ${trades_df['premarket_dollar_volume'].mean():,.2f}\")\n", "        print(f\"Min Premarket $ Volume: ${trades_df['premarket_dollar_volume'].min():,.2f}\")\n", "        print(f\"Max Premarket $ Volume: ${trades_df['premarket_dollar_volume'].max():,.2f}\")\n", "        \n", "        # Account growth over time\n", "        print(\"\\nAccount Growth Metrics:\")\n", "        print(f\"Max Drawdown: ${trades_df['account_value'].max() - trades_df['account_value'].min():,.2f}\")\n", "        print(f\"Average P&L per Trade: ${trades_df['pnl_dollars'].mean():,.2f}\")\n", "        \n", "        # Calculate monthly returns if we have date information\n", "        if 'date' in trades_df.columns and not trades_df.empty:\n", "            try:\n", "                trades_df['date'] = pd.to_datetime(trades_df['date'])\n", "                trades_df['month'] = trades_df['date'].dt.strftime('%Y-%m')\n", "                monthly_returns = trades_df.groupby('month')['pnl_dollars'].sum()\n", "                \n", "                print(\"\\nMonthly Returns:\")\n", "                for month, pnl in monthly_returns.items():\n", "                    print(f\"{month}: ${pnl:,.2f}\")\n", "            except:\n", "                # In case date conversion fails\n", "                pass\n", "    \n", "    return trades_df"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Strategy Performance:\n", "Starting Capital: $100,000.00\n", "Final Account Value: $99,096.89\n", "Total Return: -0.90%\n", "Total Trades: 42\n", "Win Rate: 40.48%\n", "Average Win: 30.41%\n", "Average Loss: -20.21%\n", "Profit Factor: 1.02\n", "Average Trade: 0.28%\n", "\n", "Exit Type Breakdown:\n", "Market Close: 19 trades (45.2%)\n", "Stop Loss: 12 trades (28.6%)\n", "Take Profit: 11 trades (26.2%)\n", "\n", "Premarket Volume Analysis:\n", "Avg Premarket $ Volume: $66,349,939.07\n", "Min Premarket $ Volume: $741,703.70\n", "Max Premarket $ Volume: $226,289,365.34\n", "\n", "Account Growth Metrics:\n", "Max Drawdown: $18,760.34\n", "Average P&L per Trade: $-21.50\n", "\n", "Monthly Returns:\n", "2024-01: $-3,441.74\n", "2024-02: $-12,119.88\n", "2024-03: $14,113.03\n", "2024-04: $48.98\n", "2024-05: $6,225.96\n", "2024-06: $1,800.75\n", "2024-07: $-2,850.99\n", "2024-08: $3,417.95\n", "2024-09: $88.20\n", "2024-10: $-9,734.37\n", "2024-11: $-1,707.22\n", "2024-12: $-3,228.76\n", "2025-01: $1,749.27\n", "2025-02: $-238.65\n", "2025-03: $4,974.37\n"]}], "source": ["trades_df = simulate_vwap_second_pullback_strategy(intraday_gap_data)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "from matplotlib.patches import Rectangle\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "\n", "def visualize_vwap_second_pullback_trade(ticker_date, intraday_data_dict, trades_df):\n", "    \"\"\"\n", "    Visualize a specific trade with VWAP, entry, exit points, and pullbacks to VWAP.\n", "    Highlights the second pullback entry signal and shows the momentum threshold.\n", "    \"\"\"\n", "    if ticker_date not in intraday_data_dict:\n", "        print(f\"No data found for {ticker_date}\")\n", "        return\n", "        \n", "    # Get the data for this event\n", "    data = intraday_data_dict[ticker_date].copy()\n", "    \n", "    # Calculate VWAP\n", "    data = calculate_vwap(data)\n", "    \n", "    # Add green candle indicator\n", "    data['green_candle'] = data['close'] > data['open']\n", "    \n", "    # Extract ticker and date from the key\n", "    ticker, date_str = ticker_date.split('_')\n", "    \n", "    # Find this trade in the trades DataFrame\n", "    trade = trades_df[(trades_df['ticker'] == ticker) & (trades_df['date'] == date_str)]\n", "    \n", "    if trade.empty:\n", "        print(f\"No trade found for {ticker_date}\")\n", "        return\n", "    \n", "    # Extract trade details\n", "    entry_price = trade['entry_price'].iloc[0]\n", "    entry_time_str = trade['entry_time'].iloc[0]\n", "    exit_price = trade['exit_price'].iloc[0]\n", "    exit_time_str = trade['exit_time'].iloc[0]\n", "    exit_type = trade['exit_type'].iloc[0]\n", "    pnl_pct = trade['pnl_pct'].iloc[0]\n", "    vwap_at_open = trade['vwap_at_open'].iloc[0]\n", "    vwap_momentum_pct = trade['vwap_momentum_pct'].iloc[0]\n", "    low_of_day =  trade['low_since_open'].iloc[0]\n", "    high_of_day = trade['high_since_open'].iloc[0]\n", "    \n", "    # Create figure with 2 subplots (price chart and volume)\n", "    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), gridspec_kw={'height_ratios': [3, 1]}, sharex=True)\n", "    \n", "    # Plot price candlesticks in the main chart\n", "    for i in range(len(data)):\n", "        # Get candle data\n", "        o, h, l, c = data.iloc[i][['open', 'high', 'low', 'close']]\n", "        t = data.index[i]\n", "        \n", "        # Determine color based on if close > open (green) or not (red)\n", "        color = 'green' if c > o else 'red'\n", "        \n", "        # Plot candlestick body\n", "        body_height = max(0.01, abs(c - o))  # Ensure minimum visible size\n", "        ax1.add_patch(Rectangle((mdates.date2num(t) - 0.0002, min(o, c)),\n", "                                0.0004, body_height,\n", "                                facecolor=color, edgecolor='black', alpha=0.8))\n", "        \n", "        # Plot wicks\n", "        ax1.plot([mdates.date2num(t), mdates.date2num(t)], [l, min(o, c)], color='black', linewidth=1)\n", "        ax1.plot([mdates.date2num(t), mdates.date2num(t)], [max(o, c), h], color='black', linewidth=1)\n", "    \n", "    # Plot VWAP\n", "    ax1.plot(data.index, data['vwap'], label='VWAP', linewidth=1.5, color='purple', zorder=3)\n", "    \n", "    # Plot 5% VWAP momentum threshold line\n", "    vwap_threshold = vwap_at_open * 1.05\n", "    ax1.axhline(y=vwap_threshold, color='orange', linestyle='--', alpha=0.7, \n", "                label=f'5% VWAP Momentum Threshold ({vwap_threshold:.2f})')\n", "    \n", "    # Plot low of day (stop loss)\n", "    ax1.axhline(y=low_of_day, color='red', linestyle=':', alpha=0.7, \n", "                label=f'Low of Day - Stop Loss ({low_of_day:.2f})')\n", "    \n", "    # Plot high of day (take profit)\n", "    ax1.axhline(y=high_of_day, color='blue', linestyle=':', alpha=0.7, \n", "                label=f'High of Day - Take Profit ({high_of_day:.2f})')\n", "    \n", "    # Find pullbacks to VWAP\n", "    pullback_starts = []\n", "    pullback_ends = []\n", "    is_below_vwap = False\n", "    pullback_count = 0\n", "    \n", "    # Initialize variables\n", "    vwap_crossings = []\n", "    second_pullback_start = None\n", "    second_pullback_end = None\n", "    \n", "    # Get market open data (9:30)\n", "    market_open_data = data[(data.index.hour == 9) & (data.index.minute == 30)]\n", "    if market_open_data.empty:\n", "        print(\"Warning: No market open data found. Using first available data point.\")\n", "        market_open_idx = data.index[0]\n", "        market_open_vwap = data.loc[market_open_idx, 'vwap']\n", "    else:\n", "        market_open_idx = market_open_data.index[0]\n", "        market_open_vwap = data.loc[market_open_idx, 'vwap']\n", "    \n", "    # Skip first 30 minutes for strategy\n", "    start_idx = data.index.get_loc(market_open_idx) + 30 if len(data) > data.index.get_loc(market_open_idx) + 30 else data.index.get_loc(market_open_idx) + 1\n", "    \n", "    # Process each minute of data to identify pullbacks\n", "    for i in range(start_idx + 1, len(data)):\n", "        prev_row = data.iloc[i-1]\n", "        curr_row = data.iloc[i]\n", "        \n", "        current_time = data.index[i]\n", "        current_price = curr_row['close']\n", "        current_vwap = curr_row['vwap']\n", "        is_green_candle = curr_row['green_candle']\n", "        \n", "        # Check if VWAP has increased by our threshold percentage (5%)\n", "        current_vwap_pct = (current_vwap - market_open_vwap) / market_open_vwap * 100\n", "        momentum_established = current_vwap_pct >= 5.0\n", "        \n", "        if momentum_established:\n", "            # Detect crossing below VWAP (start of pullback)\n", "            prev_above_vwap = prev_row['close'] >= prev_row['vwap']\n", "            curr_below_vwap = current_price < current_vwap\n", "            \n", "            # Detect crossing above VWAP (end of pullback)\n", "            prev_below_vwap = prev_row['close'] < prev_row['vwap']\n", "            curr_above_vwap = current_price >= current_vwap\n", "            \n", "            # Record VWAP crossings for visualization\n", "            if (prev_above_vwap and curr_below_vwap) or (prev_below_vwap and curr_above_vwap):\n", "                vwap_crossings.append((current_time, current_price, \n", "                                        'down' if curr_below_vwap else 'up',\n", "                                        is_green_candle))\n", "            \n", "            # Detect start of a pullback\n", "            if prev_above_vwap and curr_below_vwap:\n", "                is_below_vwap = True\n", "                pullback_starts.append((current_time, current_price))\n", "            \n", "            # Detect end of a pullback with a green candle crossing back above VWAP\n", "            if is_below_vwap and prev_below_vwap and curr_above_vwap and is_green_candle:\n", "                pullback_count += 1\n", "                is_below_vwap = False  # Reset for next potential pullback\n", "                pullback_ends.append((current_time, current_price))\n", "                \n", "                # <PERSON> the second pullback specifically\n", "                if pullback_count == 2:\n", "                    second_pullback_start = pullback_starts[-1]\n", "                    second_pullback_end = (current_time, current_price)\n", "    \n", "    # Convert entry and exit times to datetime\n", "    entry_hour, entry_minute = map(int, entry_time_str.split(':'))\n", "    exit_hour, exit_minute = map(int, exit_time_str.split(':'))\n", "    \n", "    # Find entry and exit points in the data\n", "    entry_dt = None\n", "    exit_dt = None\n", "    \n", "    for idx in data.index:\n", "        if idx.hour == entry_hour and idx.minute == entry_minute:\n", "            entry_dt = idx\n", "        if idx.hour == exit_hour and idx.minute == exit_minute:\n", "            exit_dt = idx\n", "    \n", "    # If exact times not found, find closest\n", "    if entry_dt is None:\n", "        entry_dt = min(data.index, key=lambda x: abs((x.hour*60 + x.minute) - (entry_hour*60 + entry_minute)))\n", "    if exit_dt is None:\n", "        exit_dt = min(data.index, key=lambda x: abs((x.hour*60 + x.minute) - (exit_hour*60 + exit_minute)))\n", "    \n", "    # Highlight VWAP crossings\n", "    for time, price, direction, is_green in vwap_crossings:\n", "        marker = 'o'\n", "        color = 'green' if is_green else 'red'\n", "        alpha = 0.5\n", "        ax1.scatter(time, price, color=color, alpha=alpha, s=30, marker=marker)\n", "    \n", "    # Highlight pullbacks\n", "    for i, ((start_time, start_price), (end_time, end_price)) in enumerate(zip(pullback_starts, pullback_ends)):\n", "        # Different styling for the second pullback\n", "        if i + 1 == 2:  # Second pullback\n", "            ax1.plot([start_time, end_time], [start_price, end_price], 'g--', linewidth=2, alpha=0.7)\n", "            ax1.scatter(start_time, start_price, color='orange', s=80, marker='o', \n", "                        label='Second Pullback Start')\n", "            ax1.scatter(end_time, end_price, color='lime', s=100, marker='*', \n", "                        label='Second Pullback End (Entry Signal)')\n", "        else:\n", "            ax1.plot([start_time, end_time], [start_price, end_price], 'y--', linewidth=1, alpha=0.5)\n", "    \n", "    # Mark entry and exit points\n", "    if entry_dt:\n", "        ax1.scatter(entry_dt, entry_price, color='green', s=120, marker='^', \n", "                    label=f'Entry at {entry_time_str}', zorder=5)\n", "        ax1.annotate(f'Entry: ${entry_price:.2f}', xy=(entry_dt, entry_price), \n", "                     xytext=(10, 20), textcoords='offset points', \n", "                     arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=.2'))\n", "    \n", "    if exit_dt:\n", "        color = 'red' if pnl_pct < 0 else 'blue'\n", "        ax1.scatter(exit_dt, exit_price, color=color, s=120, marker='v', \n", "                    label=f'Exit ({exit_type}) at {exit_time_str}', zorder=5)\n", "        ax1.annotate(f'Exit: ${exit_price:.2f}\\nP&L: {pnl_pct:.2f}%', \n", "                     xy=(exit_dt, exit_price), xytext=(10, -30), \n", "                     textcoords='offset points', \n", "                     arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=.2'))\n", "    \n", "    # Connect entry and exit with a line\n", "    if entry_dt and exit_dt:\n", "        color = 'red' if pnl_pct < 0 else 'blue'\n", "        ax1.plot([entry_dt, exit_dt], [entry_price, exit_price], color=color, \n", "                 linestyle='--', alpha=0.7, linewidth=1.5)\n", "    \n", "    # Plot volume in the bottom subplot\n", "    volume_colors = ['green' if row['close'] >= row['open'] else 'red' for _, row in data.iterrows()]\n", "    ax2.bar(data.index, data['volume'], color=volume_colors, alpha=0.7, width=0.0004)\n", "    ax2.set_ylabel('Volume')\n", "    \n", "    # Format the plot\n", "    ax1.set_title(f\"VWAP Second Pullback Strategy: {ticker} on {date_str}\\n\"\n", "                  f\"P&L: {pnl_pct:.2f}% | VWAP Momentum: {vwap_momentum_pct:.2f}%\", fontsize=16)\n", "    ax1.set_ylabel('Price ($)')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.legend(loc='upper left')\n", "    \n", "    # Format x-axis to show hours\n", "    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))\n", "    plt.gcf().autofmt_xdate()\n", "    \n", "    # Adjust y-axis to provide some padding\n", "    price_range = max(data['high']) - min(data['low'])\n", "    ax1.set_ylim(min(data['low']) - 0.05 * price_range, max(data['high']) + 0.05 * price_range)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return fig\n", "\n", "# Example: Visualize the first few trades if any exist\n", "def visualize_sample_trades(intraday_data_dict, trades_df, num_trades=3):\n", "    \"\"\"Visualize a sample of trades to evaluate strategy performance.\"\"\"\n", "    if trades_df.empty:\n", "        print(\"No trades to visualize.\")\n", "        return\n", "    \n", "    # Sort trades by absolute PnL for more interesting visualization\n", "    sorted_trades = trades_df.copy()\n", "    sorted_trades['abs_pnl'] = sorted_trades['pnl_pct'].abs()\n", "    sorted_trades = sorted_trades.sort_values('abs_pnl', ascending=False)\n", "    \n", "    # Get a mix of profitable and unprofitable trades\n", "    profitable_trades = sorted_trades[sorted_trades['pnl_pct'] > 0].head(num_trades // 2 + num_trades % 2)\n", "    unprofitable_trades = sorted_trades[sorted_trades['pnl_pct'] <= 0].head(num_trades // 2)\n", "    \n", "    sample_trades = pd.concat([profitable_trades, unprofitable_trades]).head(num_trades)\n", "    \n", "    for _, trade in sample_trades.iterrows():\n", "        ticker_date = f\"{trade['ticker']}_{trade['date']}\"\n", "        print(f\"\\nVisualizing trade for {ticker_date}...\")\n", "        print(f\"Entry: {trade['entry_time']} at ${trade['entry_price']:.2f}\")\n", "        print(f\"Exit: {trade['exit_time']} at ${trade['exit_price']:.2f} ({trade['exit_type']})\")\n", "        print(f\"P&L: {trade['pnl_pct']:.2f}%\")\n", "        \n", "        visualize_vwap_second_pullback_trade(ticker_date, intraday_data_dict, trades_df)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Visualizing trade for LDTC_2024-12-09 00:00:00...\n", "Entry: 13:26 at $1.34\n", "Exit: 14:29 at $1.87 (Take Profit)\n", "P&L: 39.55%\n", "No data found for LDTC_2024-12-09 00:00:00\n", "\n", "Visualizing trade for KWE_2024-06-10 00:00:00...\n", "Entry: 12:11 at $0.86\n", "Exit: 17:12 at $1.16 (Take Profit)\n", "P&L: 34.88%\n", "No data found for KWE_2024-06-10 00:00:00\n", "\n", "Visualizing trade for CHNR_2024-02-15 00:00:00...\n", "Entry: 10:45 at $5.88\n", "Exit: 16:00 at $3.51 (Market Close)\n", "P&L: -40.31%\n", "No data found for CHNR_2024-02-15 00:00:00\n"]}], "source": ["# Visualize a sample of trades (mix of profitable and unprofitable)\n", "visualize_sample_trades(intraday_gap_data, trades_df, num_trades=3)\n", "\n", "# # Or visualize a specific trade\n", "# if not trades_df.empty:\n", "#     first_trade = trades_df.iloc[0]\n", "#     first_key = f\"{first_trade['ticker']}_{first_trade['date']}\"\n", "#     visualize_vwap_second_pullback_trade(first_key, intraday_data_dict, trades_df)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}