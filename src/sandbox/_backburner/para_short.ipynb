{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Libraries \n", "import os \n", "os.chdir('/home/<USER>/w/backtest')\n", "\n", "import dotenv\n", "dotenv.load_dotenv(\".env\", override=True)\n", "\n", "import pytz\n", "from sandbox.utils import *\n", "from marketdata import MarketDataBuilder, disk_market_data\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "import pytz\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sys\n", "import warnings\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Filter pandas SettingWithCopyWarning more broadly if needed\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "warnings.filterwarnings('ignore', message=\"The behavior of DataFrame concatenation with empty or all-NA entries is deprecated\")\n", "pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)\n", "\n", "intraday_market_data = (MarketDataBuilder()\n", "                        .with_trade_session(\"full\") \n", "                        .with_period(\"intraday\")\n", "                        .build_market_data())\n", "\n", "daily_market_data = (MarketDataBuilder()\n", "                        .with_period(\"daily\") \n", "                        .with_disk_data(preload_months=24)  # Only available since 2024-01-01\n", "                        .build_market_data())\n", "\n", "def get_daily_data(ticker, start_dt, end_dt):\n", "    \"\"\"Fetches daily data using the configured daily_market_data instance.\"\"\"\n", "    try:\n", "        return daily_market_data.gather_historical_data(\n", "            ticker=ticker,\n", "            start_dt=start_dt,\n", "            end_dt=end_dt,\n", "            interval=86400 # Daily interval\n", "        )\n", "    except Exception as e:\n", "        return None\n", "\n", "def get_per_minute_data(ticker, start_dt, end_dt):\n", "    # Fetch per-minute data (interval=60 seconds)\n", "    data = intraday_market_data.gather_historical_data(\n", "        ticker=ticker,\n", "        start_dt=start_dt,\n", "        end_dt=end_dt,\n", "        interval=60\n", "    )\n", "    return data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>symbol</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02</th>\n", "      <td>250.00</td>\n", "      <td>251.2500</td>\n", "      <td>244.4100</td>\n", "      <td>248.42</td>\n", "      <td>104342658</td>\n", "      <td>TSLA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-03</th>\n", "      <td>245.00</td>\n", "      <td>245.6800</td>\n", "      <td>236.3200</td>\n", "      <td>238.45</td>\n", "      <td>120654919</td>\n", "      <td>TSLA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04</th>\n", "      <td>239.15</td>\n", "      <td>242.7000</td>\n", "      <td>237.7300</td>\n", "      <td>237.93</td>\n", "      <td>102330512</td>\n", "      <td>TSLA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>236.95</td>\n", "      <td>240.1196</td>\n", "      <td>234.9001</td>\n", "      <td>237.49</td>\n", "      <td>92327144</td>\n", "      <td>TSLA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-08</th>\n", "      <td>235.98</td>\n", "      <td>241.2500</td>\n", "      <td>235.3000</td>\n", "      <td>240.45</td>\n", "      <td>84949666</td>\n", "      <td>TSLA</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              open      high       low   close     volume symbol\n", "date                                                            \n", "2024-01-02  250.00  251.2500  244.4100  248.42  104342658   TSLA\n", "2024-01-03  245.00  245.6800  236.3200  238.45  120654919   TSLA\n", "2024-01-04  239.15  242.7000  237.7300  237.93  102330512   TSLA\n", "2024-01-05  236.95  240.1196  234.9001  237.49   92327144   TSLA\n", "2024-01-08  235.98  241.2500  235.3000  240.45   84949666   TSLA"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "from tickers.ticker_helpers import load_cached_ticker_data\n", "\n", "eastern_tz = pytz.timezone(\"US/Eastern\")\n", "start_dt = eastern_tz.localize(datetime(2024, 1, 1))\n", "end_dt = eastern_tz.localize(datetime(2025, 3, 28))\n", "\n", "ticker_df = load_cached_ticker_data()\n", "\n", "# min_market_cap = 500_000_000  # 500M\n", "# max_market_cap = 5_000_000_000  # 5B\n", "\n", "min_market_cap = 500_000_000  # 500M\n", "max_market_cap = 5_000_000_000  # 5B\n", "\n", "market_cap_filter = (\n", "            ticker_df[\"market_cap\"].notna() &\n", "            (ticker_df[\"market_cap\"] >= min_market_cap) &\n", "            (ticker_df[\"market_cap\"] <= max_market_cap)\n", "        )\n", "small_cap_tickers = set(ticker_df.loc[market_cap_filter, \"ticker\"])\n", "\n", "get_daily_data('TSLA', start_dt, end_dt).head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def find_rapid_risers(tickers, start_dt, end_dt, min_pct_increase=50, min_consecutive_days=3):\n", "    \"\"\"\n", "    Find stocks that have risen rapidly over consecutive days.\n", "    \n", "    Parameters:\n", "    -----------\n", "    tickers : list\n", "        List of stock tickers to analyze\n", "    start_dt : datetime\n", "        Start date for analysis\n", "    end_dt : datetime\n", "        End date for analysis\n", "    min_pct_increase : float\n", "        Minimum overall percentage increase required (default: 50%)\n", "    min_consecutive_days : int\n", "        Minimum number of consecutive days the stock must rise (default: 2)\n", "        \n", "    Returns:\n", "    --------\n", "    pandas.DataFrame\n", "        DataFrame containing stock events with columns:\n", "        ['ticker', 'event_date', 'pct_change', 'consecutive_days']\n", "    \"\"\"\n", "    results = []\n", "    \n", "    for ticker in tickers:\n", "        # Get daily price data\n", "        daily_data = get_daily_data(ticker, start_dt, end_dt)\n", "        \n", "        if daily_data is None or daily_data.empty:\n", "            continue\n", "            \n", "        # Calculate daily returns\n", "        daily_data['pct_change'] = daily_data['close'].pct_change() * 100\n", "        \n", "        # Iterate through the data to find all possible streaks\n", "        for i in range(1, len(daily_data)):\n", "            # If this day had a positive return, check for streaks ending on this day\n", "            if daily_data['pct_change'].iloc[i] > 0:\n", "                # Look back to find start of streak\n", "                consecutive_days = 1  # Current day is already a positive day\n", "                \n", "                # Check previous days (up to a reasonable limit)\n", "                for j in range(i-1, max(0, i-20), -1):  # Look back up to 20 days\n", "                    if daily_data['pct_change'].iloc[j] > 0:\n", "                        consecutive_days += 1\n", "                    else:\n", "                        break  # Stop at first non-positive day\n", "                \n", "                # If we have enough consecutive days\n", "                if consecutive_days >= min_consecutive_days:\n", "                    # Calculate overall percentage change\n", "                    start_idx = i - consecutive_days + 1  # First day of the streak\n", "                    if start_idx > 0:  # Ensure we have a price before the streak started\n", "                        start_price = daily_data['close'].iloc[start_idx - 1]  # Price before streak\n", "                    else:\n", "                        start_price = daily_data['open'].iloc[start_idx]  # Use open price of first day\n", "                        \n", "                    end_price = daily_data['close'].iloc[i]  # Last day of streak\n", "                    \n", "                    # Calculate overall percentage change\n", "                    overall_pct_change = ((end_price / start_price) - 1) * 100\n", "                    \n", "                    if overall_pct_change >= min_pct_increase:\n", "                        event_date = daily_data.index[i].strftime('%Y-%m-%d')\n", "                        results.append({\n", "                            'ticker': ticker,\n", "                            'event_date': event_date,\n", "                            'pct_change': round(overall_pct_change, 2),\n", "                            'consecutive_days': consecutive_days\n", "                        })\n", "    \n", "    # Create DataFrame from results\n", "    result_df = pd.DataFrame(results)\n", "    \n", "    # Sort by percentage change (descending)\n", "    if not result_df.empty:\n", "        result_df = result_df.sort_values(['pct_change', 'consecutive_days'], ascending=[False, False])\n", "    \n", "    return result_df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet file size is 0 bytes\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet file size is 0 bytes\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet file size is 0 bytes\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet file size is 0 bytes\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet file size is 4 bytes, smaller than the minimum file footer (8 bytes)\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n", "Error reading file market_data/monthly_data/2025/03.parquet: Error creating dataset. Could not read schema from 'market_data/monthly_data/2025/03.parquet'. Is this a 'parquet' file?: Could not open Parquet input source 'market_data/monthly_data/2025/03.parquet': Parquet magic bytes not found in footer. Either the file is corrupted or this is not a parquet file.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Found 454 rapid riser events\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ticker</th>\n", "      <th>event_date</th>\n", "      <th>pct_change</th>\n", "      <th>consecutive_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>DNA</td>\n", "      <td>2024-08-21</td>\n", "      <td>4103.16</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>398</th>\n", "      <td>PGY</td>\n", "      <td>2024-03-08</td>\n", "      <td>1265.67</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>287</th>\n", "      <td>ROOT</td>\n", "      <td>2024-03-04</td>\n", "      <td>383.28</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>DXYZ</td>\n", "      <td>2024-04-08</td>\n", "      <td>370.26</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>286</th>\n", "      <td>ROOT</td>\n", "      <td>2024-03-01</td>\n", "      <td>351.10</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>FUBO</td>\n", "      <td>2025-01-07</td>\n", "      <td>336.80</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>NNE</td>\n", "      <td>2024-06-25</td>\n", "      <td>311.60</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FUBO</td>\n", "      <td>2025-01-06</td>\n", "      <td>304.80</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>QUBT</td>\n", "      <td>2024-12-18</td>\n", "      <td>303.77</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>SERV</td>\n", "      <td>2024-07-23</td>\n", "      <td>301.71</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ticker  event_date  pct_change  consecutive_days\n", "74     DNA  2024-08-21     4103.16                 3\n", "398    PGY  2024-03-08     1265.67                 3\n", "287   ROOT  2024-03-04      383.28                 8\n", "68    DXYZ  2024-04-08      370.26                 4\n", "286   ROOT  2024-03-01      351.10                 7\n", "4     FUBO  2025-01-07      336.80                 5\n", "249    NNE  2024-06-25      311.60                 7\n", "3     FUBO  2025-01-06      304.80                 4\n", "370   QUBT  2024-12-18      303.77                 4\n", "93    SERV  2024-07-23      301.71                 4"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Convert set to list for iteration\n", "import random\n", "random.seed(42)\n", "small_cap_list = list(small_cap_tickers)\n", "# Limit to 500 tickers\n", "# small_cap_list = random.sample(small_cap_list, 500)\n", "\n", "# Find rapid risers\n", "rapid_risers = find_rapid_risers(\n", "    tickers=small_cap_list,\n", "    start_dt=start_dt,\n", "    end_dt=end_dt,\n", "    min_pct_increase=50,\n", "    min_consecutive_days=3\n", ")\n", "\n", "# Exclude outliers \n", "# rapid_risers = rapid_risers[rapid_risers['pct_change'] < 500]\n", "\n", "# Display results\n", "print(f\"Found {len(rapid_risers)} rapid riser events\")\n", "rapid_risers.head(10)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["rapid_risers = rapid_risers[rapid_risers['consecutive_days'] >= 3]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 454 rapid riser events\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Display results\n", "print(f\"Found {len(rapid_risers)} rapid riser events\")\n", "rapid_risers.head(10)\n", "\n", "# Optional: Add a visualization\n", "plt.figure(figsize=(12, 6))\n", "sns.histplot(data=rapid_risers, x='consecutive_days', bins=range(2, 15), discrete=True)\n", "plt.title('Distribution of Consecutive Days in Rapid Riser Events')\n", "plt.xlabel('Consecutive Days')\n", "plt.ylabel('Count')\n", "plt.show()\n", "\n", "# Optional: Distribution of percentage changes\n", "plt.figure(figsize=(12, 6))\n", "sns.histplot(data=rapid_risers, x='pct_change', bins=20)\n", "plt.title('Distribution of Percentage Changes in Rapid Riser Events')\n", "plt.xlabel('Percentage Change')\n", "plt.ylabel('Count')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def get_intraday_data_for_events(events_df):\n", "    \"\"\"\n", "    Fetch 1-minute intraday data for each event in the events dataframe.\n", "    \n", "    Parameters:\n", "    -----------\n", "    events_df : pandas.DataFrame\n", "        DataFrame containing stock events with columns: ['ticker', 'event_date', 'pct_change', 'consecutive_days']\n", "        \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary with event keys and intraday data DataFrames as values\n", "    \"\"\"\n", "    eastern_tz = pytz.timezone(\"US/Eastern\")\n", "    event_data = {}\n", "    \n", "    for idx, event in events_df.iterrows():\n", "        ticker = event['ticker']\n", "        event_date_str = event['event_date']\n", "        \n", "        # Parse event date\n", "        event_date = datetime.strptime(event_date_str, '%Y-%m-%d')\n", "        event_date = eastern_tz.localize(event_date)\n", "        \n", "        # Set start time to 4:00 AM on event date\n", "        start_dt = event_date.replace(hour=4, minute=0, second=0, microsecond=0)\n", "        \n", "        # Set end time to 8:00 PM on event date\n", "        end_dt = event_date.replace(hour=20, minute=0, second=0, microsecond=0)\n", "        \n", "        # Create a unique key for this event\n", "        event_key = f\"{ticker}_{event_date_str}_{event['consecutive_days']}d_{event['pct_change']}pct\"\n", "        \n", "        try:\n", "            # Fetch 1-minute intraday data\n", "            intraday_data = get_per_minute_data(ticker, start_dt, end_dt)\n", "            \n", "            if intraday_data is not None and not intraday_data.empty:\n", "                # Store the data with the event key\n", "                event_data[event_key] = intraday_data\n", "                print(f\"Successfully fetched intraday data for {event_key}\")\n", "            else:\n", "                print(f\"No intraday data available for {event_key}\")\n", "        except Exception as e:\n", "            print(f\"Error fetching intraday data for {event_key}: {str(e)}\")\n", "    \n", "    return event_data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching intraday data for 454 events...\n", "Successfully fetched intraday data for DNA_2024-08-21_3d_4103.16pct\n", "Successfully fetched intraday data for PGY_2024-03-08_3d_1265.67pct\n", "Successfully fetched intraday data for ROOT_2024-03-04_8d_383.28pct\n", "Successfully fetched intraday data for DXYZ_2024-04-08_4d_370.26pct\n", "Successfully fetched intraday data for ROOT_2024-03-01_7d_351.1pct\n", "Successfully fetched intraday data for FUBO_2025-01-07_5d_336.8pct\n", "Successfully fetched intraday data for NNE_2024-06-25_7d_311.6pct\n", "Successfully fetched intraday data for FUBO_2025-01-06_4d_304.8pct\n", "Successfully fetched intraday data for QUBT_2024-12-18_4d_303.77pct\n", "Successfully fetched intraday data for SERV_2024-07-23_4d_301.71pct\n", "Successfully fetched intraday data for DXYZ_2024-11-11_5d_288.91pct\n", "Successfully fetched intraday data for SERV_2024-07-22_3d_274.79pct\n", "Successfully fetched intraday data for ROOT_2024-02-29_6d_270.36pct\n", "Successfully fetched intraday data for DXYZ_2024-11-08_4d_231.81pct\n", "Successfully fetched intraday data for ROOT_2024-02-28_5d_222.61pct\n", "Successfully fetched intraday data for QUBT_2024-11-25_5d_222.18pct\n", "Successfully fetched intraday data for NVAX_2024-05-14_3d_201.57pct\n", "Successfully fetched intraday data for NNE_2024-06-24_6d_195.19pct\n", "Successfully fetched intraday data for ROOT_2024-02-27_4d_190.66pct\n", "Successfully fetched intraday data for UXIN_2024-09-30_3d_185.53pct\n", "Successfully fetched intraday data for DXYZ_2024-04-05_3d_178.98pct\n", "Successfully fetched intraday data for TIGR_2024-10-04_5d_170.52pct\n", "Successfully fetched intraday data for EXOD_2025-01-29_10d_170.03pct\n", "Successfully fetched intraday data for VKTX_2024-02-28_5d_168.62pct\n", "Successfully fetched intraday data for QUBT_2024-12-17_3d_163.99pct\n", "Successfully fetched intraday data for ROOT_2024-02-26_3d_162.51pct\n", "Successfully fetched intraday data for CRNC_2025-01-06_3d_157.07pct\n", "Successfully fetched intraday data for QUBT_2024-11-22_4d_155.23pct\n", "Successfully fetched intraday data for KULR_2024-12-20_6d_154.31pct\n", "Successfully fetched intraday data for WRD_2025-02-18_4d_154.09pct\n", "Successfully fetched intraday data for MVST_2024-12-03_4d_143.1pct\n", "Successfully fetched intraday data for EXOD_2025-01-28_9d_142.39pct\n", "Successfully fetched intraday data for VKTX_2024-02-27_4d_141.76pct\n", "Successfully fetched intraday data for QBTS_2025-03-17_5d_141.47pct\n", "Successfully fetched intraday data for KULR_2024-11-25_4d_141.22pct\n", "Successfully fetched intraday data for DAVE_2024-11-13_6d_140.12pct\n", "Successfully fetched intraday data for PSNL_2024-07-19_4d_139.13pct\n", "Successfully fetched intraday data for RGTI_2024-12-11_5d_137.3pct\n", "Successfully fetched intraday data for STOK_2024-03-27_4d_136.56pct\n", "Successfully fetched intraday data for EBS_2024-05-08_6d_134.57pct\n", "Successfully fetched intraday data for EBS_2024-05-07_5d_134.04pct\n", "Successfully fetched intraday data for VERA_2024-02-02_7d_130.02pct\n", "Successfully fetched intraday data for QBTS_2024-12-18_4d_129.16pct\n", "Successfully fetched intraday data for RGTI_2024-12-27_5d_128.65pct\n", "Successfully fetched intraday data for NNE_2024-05-30_7d_128.46pct\n", "Successfully fetched intraday data for SERV_2024-07-29_3d_126.52pct\n", "Successfully fetched intraday data for EXOD_2025-01-27_8d_123.77pct\n", "Successfully fetched intraday data for SEZL_2024-01-19_4d_123.53pct\n", "Successfully fetched intraday data for VERA_2024-02-01_6d_123.27pct\n", "Successfully fetched intraday data for INOD_2024-11-12_6d_122.82pct\n", "Successfully fetched intraday data for NNE_2024-06-21_5d_122.49pct\n", "Successfully fetched intraday data for INOD_2024-11-11_5d_122.38pct\n", "Successfully fetched intraday data for ASTS_2024-05-20_3d_120.92pct\n", "Successfully fetched intraday data for LUNR_2024-02-20_3d_120.68pct\n", "Successfully fetched intraday data for QBTS_2025-03-14_4d_119.22pct\n", "Successfully fetched intraday data for NNE_2025-01-24_8d_118.29pct\n", "Successfully fetched intraday data for REAL_2024-03-05_4d_118.13pct\n", "Successfully fetched intraday data for ASTS_2024-05-29_3d_117.87pct\n", "Successfully fetched intraday data for NNE_2024-09-13_5d_117.57pct\n", "Successfully fetched intraday data for UP_2024-07-05_5d_117.39pct\n", "Successfully fetched intraday data for SEZL_2024-11-08_4d_115.93pct\n", "Successfully fetched intraday data for NNE_2024-06-20_4d_115.28pct\n", "Successfully fetched intraday data for QBTS_2024-12-17_3d_114.83pct\n", "Successfully fetched intraday data for VERA_2024-01-31_5d_113.98pct\n", "Successfully fetched intraday data for QBTS_2024-02-15_5d_112.9pct\n", "Successfully fetched intraday data for MYTE_2024-10-08_5d_112.33pct\n", "Successfully fetched intraday data for EBS_2024-05-06_4d_111.7pct\n", "Successfully fetched intraday data for RGTI_2024-11-25_5d_111.54pct\n", "Successfully fetched intraday data for GLAD_2024-04-11_5d_111.24pct\n", "Successfully fetched intraday data for GLAD_2024-04-10_4d_110.42pct\n", "Successfully fetched intraday data for NNE_2025-01-23_7d_109.61pct\n", "Successfully fetched intraday data for GLAD_2024-04-09_3d_108.78pct\n", "Successfully fetched intraday data for RGTI_2024-12-10_4d_108.68pct\n", "Successfully fetched intraday data for INOD_2024-11-08_4d_107.27pct\n", "Successfully fetched intraday data for CAPR_2024-09-25_4d_107.21pct\n", "Successfully fetched intraday data for EBS_2024-05-03_3d_106.91pct\n", "Successfully fetched intraday data for RGTI_2024-12-26_4d_106.69pct\n", "Successfully fetched intraday data for AKRO_2025-01-27_3d_106.67pct\n", "Successfully fetched intraday data for LX_2024-10-01_6d_106.59pct\n", "Successfully fetched intraday data for CLSK_2024-02-12_5d_106.25pct\n", "Successfully fetched intraday data for COMM_2024-08-20_9d_106.1pct\n", "Successfully fetched intraday data for WGS_2024-02-22_6d_105.12pct\n", "Successfully fetched intraday data for QBTS_2024-11-22_4d_103.47pct\n", "Successfully fetched intraday data for VERA_2024-01-30_4d_102.76pct\n", "Successfully fetched intraday data for RCAT_2024-11-25_4d_102.74pct\n", "Successfully fetched intraday data for ML_2024-11-12_7d_102.6pct\n", "Successfully fetched intraday data for DXYZ_2024-11-07_3d_102.57pct\n", "Successfully fetched intraday data for SLQT_2024-05-16_12d_101.99pct\n", "Successfully fetched intraday data for OUST_2024-04-03_5d_100.8pct\n", "Successfully fetched intraday data for TIGR_2024-10-03_4d_100.66pct\n", "Successfully fetched intraday data for SWIM_2024-08-16_8d_100.64pct\n", "Successfully fetched intraday data for HSAI_2024-11-29_9d_100.49pct\n", "Successfully fetched intraday data for OUST_2024-04-02_4d_100.2pct\n", "Successfully fetched intraday data for QUBT_2024-11-21_3d_99.16pct\n", "Successfully fetched intraday data for QBTS_2024-02-14_4d_98.92pct\n", "Successfully fetched intraday data for WRD_2025-02-14_3d_98.11pct\n", "Successfully fetched intraday data for OUST_2024-04-01_3d_97.99pct\n", "Successfully fetched intraday data for VERA_2024-01-29_3d_97.65pct\n", "Successfully fetched intraday data for SYRE_2024-03-06_9d_97.26pct\n", "Successfully fetched intraday data for AHG_2024-05-23_4d_94.92pct\n", "Successfully fetched intraday data for SLQT_2024-05-15_11d_94.7pct\n", "Successfully fetched intraday data for DJTWW_2024-04-19_3d_93.3pct\n", "Successfully fetched intraday data for KULR_2024-12-19_5d_93.1pct\n", "Successfully fetched intraday data for APGE_2024-03-06_4d_91.53pct\n", "Successfully fetched intraday data for CLOV_2024-08-19_8d_90.99pct\n", "Successfully fetched intraday data for SEZL_2024-05-10_3d_90.97pct\n", "Successfully fetched intraday data for ML_2024-11-11_6d_90.96pct\n", "Successfully fetched intraday data for QUBT_2025-03-18_6d_90.93pct\n", "Successfully fetched intraday data for SWIM_2024-08-15_7d_90.68pct\n", "Successfully fetched intraday data for IREN_2024-02-12_5d_89.86pct\n", "Successfully fetched intraday data for PCT_2024-09-16_4d_89.77pct\n", "Successfully fetched intraday data for ML_2024-11-08_5d_89.42pct\n", "Successfully fetched intraday data for RCAT_2024-11-22_3d_89.24pct\n", "Successfully fetched intraday data for MVST_2024-09-18_5d_89.06pct\n", "Successfully fetched intraday data for MESO_2024-04-02_3d_88.83pct\n", "Successfully fetched intraday data for RZLV_2024-12-18_3d_88.44pct\n", "Successfully fetched intraday data for PCT_2024-09-13_3d_88.06pct\n", "Successfully fetched intraday data for COMM_2024-08-19_8d_87.79pct\n", "Successfully fetched intraday data for RUM_2024-01-23_3d_87.32pct\n", "Successfully fetched intraday data for DAVE_2024-01-24_8d_87.23pct\n", "Successfully fetched intraday data for REAL_2024-03-04_3d_87.13pct\n", "Successfully fetched intraday data for GDS_2025-02-20_10d_86.9pct\n", "Successfully fetched intraday data for GEO_2024-11-11_5d_86.74pct\n", "Successfully fetched intraday data for IBRX_2024-04-29_3d_86.51pct\n", "Successfully fetched intraday data for ARQT_2024-01-29_6d_86.46pct\n", "Successfully fetched intraday data for RGTI_2024-12-17_3d_86.43pct\n", "Successfully fetched intraday data for SLQT_2024-05-14_10d_86.09pct\n", "Successfully fetched intraday data for KULR_2024-04-03_3d_86.08pct\n", "Successfully fetched intraday data for COMM_2024-08-16_7d_85.92pct\n", "Successfully fetched intraday data for RGTI_2025-01-16_3d_85.79pct\n", "Successfully fetched intraday data for AHG_2024-02-02_4d_85.71pct\n", "Successfully fetched intraday data for SLQT_2024-05-13_9d_85.43pct\n", "Successfully fetched intraday data for EXOD_2025-01-24_7d_85.21pct\n", "Successfully fetched intraday data for MYTE_2024-10-07_4d_85.21pct\n", "Successfully fetched intraday data for EXOD_2025-03-25_5d_85.19pct\n", "Successfully fetched intraday data for SYRE_2024-03-05_8d_84.47pct\n", "Successfully fetched intraday data for SMR_2024-03-18_5d_84.31pct\n", "Successfully fetched intraday data for DAO_2024-10-02_5d_84.21pct\n", "Successfully fetched intraday data for DJTWW_2024-06-26_4d_83.89pct\n", "Successfully fetched intraday data for BFLY_2024-09-05_9d_83.83pct\n", "Successfully fetched intraday data for ATEC_2024-11-07_11d_83.43pct\n", "Successfully fetched intraday data for MVST_2024-12-02_3d_82.76pct\n", "Successfully fetched intraday data for CAPR_2024-09-24_3d_82.36pct\n", "Successfully fetched intraday data for CXW_2024-11-11_5d_81.5pct\n", "Successfully fetched intraday data for EBS_2024-08-19_5d_81.41pct\n", "Successfully fetched intraday data for QS_2024-07-16_7d_80.99pct\n", "Successfully fetched intraday data for NNE_2024-05-29_6d_80.42pct\n", "Successfully fetched intraday data for HSAI_2024-11-27_8d_80.15pct\n", "Successfully fetched intraday data for CLSK_2024-11-12_5d_79.9pct\n", "Successfully fetched intraday data for CLSK_2024-02-09_4d_79.77pct\n", "Successfully fetched intraday data for CLSK_2024-11-11_4d_79.69pct\n", "Successfully fetched intraday data for ASPN_2024-05-13_8d_79.57pct\n", "Successfully fetched intraday data for ROOT_2024-11-01_3d_79.45pct\n", "Successfully fetched intraday data for CANG_2024-12-18_3d_79.37pct\n", "Successfully fetched intraday data for KULR_2024-12-18_4d_79.31pct\n", "Successfully fetched intraday data for GEO_2024-11-08_4d_78.84pct\n", "Successfully fetched intraday data for DAVE_2024-01-23_7d_78.83pct\n", "Successfully fetched intraday data for NNE_2025-01-22_6d_78.76pct\n", "Successfully fetched intraday data for SWIM_2024-08-14_6d_78.46pct\n", "Successfully fetched intraday data for IBRX_2024-10-28_3d_78.32pct\n", "Successfully fetched intraday data for TMCI_2024-11-11_7d_77.9pct\n", "Successfully fetched intraday data for EBS_2024-07-16_11d_77.57pct\n", "Successfully fetched intraday data for REPL_2024-06-11_5d_77.51pct\n", "Successfully fetched intraday data for ASPN_2024-05-10_7d_77.46pct\n", "Successfully fetched intraday data for QUBT_2025-03-17_5d_77.43pct\n", "Successfully fetched intraday data for REPL_2024-06-10_4d_77.31pct\n", "Successfully fetched intraday data for GYRE_2024-02-26_3d_77.28pct\n", "Successfully fetched intraday data for DDL_2024-05-10_12d_77.05pct\n", "Successfully fetched intraday data for SWIM_2024-08-13_5d_76.85pct\n", "Successfully fetched intraday data for UP_2024-07-03_4d_76.09pct\n", "Successfully fetched intraday data for AAOI_2024-11-12_4d_76.06pct\n", "Successfully fetched intraday data for TIGR_2024-10-02_3d_75.98pct\n", "Successfully fetched intraday data for PSNL_2024-12-26_5d_75.84pct\n", "Successfully fetched intraday data for SENS_2024-12-13_5d_75.7pct\n", "Successfully fetched intraday data for ASPN_2024-05-09_6d_75.49pct\n", "Successfully fetched intraday data for ASPN_2024-05-08_5d_74.97pct\n", "Successfully fetched intraday data for EWTX_2024-09-19_3d_74.97pct\n", "Successfully fetched intraday data for AAOI_2024-11-11_3d_74.52pct\n", "Successfully fetched intraday data for IOVA_2024-02-22_3d_74.21pct\n", "Successfully fetched intraday data for FUBO_2024-08-21_7d_74.14pct\n", "Successfully fetched intraday data for EXOD_2025-03-24_4d_73.96pct\n", "Successfully fetched intraday data for SWIM_2024-08-12_4d_73.95pct\n", "Successfully fetched intraday data for WGS_2024-02-21_5d_73.75pct\n", "Successfully fetched intraday data for SLQT_2024-05-10_8d_73.51pct\n", "Successfully fetched intraday data for CGEM_2024-04-29_5d_73.51pct\n", "Successfully fetched intraday data for LU_2024-03-21_5d_73.5pct\n", "Successfully fetched intraday data for SBC_2024-09-27_5d_73.32pct\n", "Successfully fetched intraday data for MVST_2024-09-17_4d_73.01pct\n", "Successfully fetched intraday data for ATEC_2024-11-06_10d_72.9pct\n", "Successfully fetched intraday data for SFIX_2024-06-06_6d_72.73pct\n", "Successfully fetched intraday data for HEPS_2024-07-08_13d_72.63pct\n", "Successfully fetched intraday data for WOOF_2024-09-17_6d_72.63pct\n", "Successfully fetched intraday data for ASPN_2024-05-07_4d_72.6pct\n", "Successfully fetched intraday data for TMCI_2024-11-08_6d_72.53pct\n", "Successfully fetched intraday data for GEO_2024-11-07_3d_72.28pct\n", "Successfully fetched intraday data for SKYT_2024-12-18_3d_72.05pct\n", "Successfully fetched intraday data for PACK_2024-03-15_4d_71.85pct\n", "Successfully fetched intraday data for STOK_2024-03-26_3d_71.79pct\n", "Successfully fetched intraday data for SLQT_2024-05-09_7d_71.52pct\n", "Successfully fetched intraday data for SEZL_2024-08-13_6d_71.48pct\n", "Successfully fetched intraday data for ANAB_2025-02-14_3d_71.47pct\n", "Successfully fetched intraday data for ASPN_2024-08-19_8d_71.46pct\n", "Successfully fetched intraday data for SYRE_2024-03-04_7d_71.34pct\n", "Successfully fetched intraday data for PSNL_2024-12-24_4d_71.07pct\n", "Successfully fetched intraday data for OUST_2024-07-16_7d_70.91pct\n", "Successfully fetched intraday data for LU_2024-10-02_5d_70.78pct\n", "Successfully fetched intraday data for CXW_2024-11-08_4d_70.74pct\n", "Successfully fetched intraday data for QS_2024-07-15_6d_70.69pct\n", "Successfully fetched intraday data for NVCR_2024-12-03_4d_70.64pct\n", "Successfully fetched intraday data for COMM_2024-08-15_6d_70.42pct\n", "Successfully fetched intraday data for SWIM_2024-08-09_3d_70.42pct\n", "Successfully fetched intraday data for APGE_2024-03-05_3d_70.3pct\n", "Successfully fetched intraday data for ASPN_2024-05-06_3d_70.17pct\n", "Successfully fetched intraday data for CLOV_2024-08-16_7d_69.97pct\n", "Successfully fetched intraday data for UXIN_2024-09-24_3d_69.59pct\n", "Successfully fetched intraday data for SERV_2025-01-06_3d_69.56pct\n", "Successfully fetched intraday data for TMCI_2024-11-07_5d_69.53pct\n", "Successfully fetched intraday data for SENS_2024-12-12_4d_69.52pct\n", "Successfully fetched intraday data for ECX_2024-06-27_4d_69.34pct\n", "Successfully fetched intraday data for PACK_2024-03-14_3d_68.19pct\n", "Successfully fetched intraday data for HSAI_2024-11-26_7d_68.14pct\n", "Successfully fetched intraday data for TECX_2024-09-25_9d_68.0pct\n", "Successfully fetched intraday data for EH_2024-10-07_8d_67.74pct\n", "Successfully fetched intraday data for CXW_2024-11-07_3d_67.4pct\n", "Successfully fetched intraday data for SBC_2024-09-26_4d_67.37pct\n", "Successfully fetched intraday data for ATEC_2024-11-05_9d_67.25pct\n", "Successfully fetched intraday data for FUBO_2024-08-20_6d_67.24pct\n", "Successfully fetched intraday data for BFLY_2024-09-04_8d_67.22pct\n", "Successfully fetched intraday data for NNE_2024-09-12_4d_67.14pct\n", "Successfully fetched intraday data for PSNL_2024-12-23_3d_67.13pct\n", "Successfully fetched intraday data for OUST_2024-07-15_6d_67.05pct\n", "Successfully fetched intraday data for LX_2024-12-03_7d_66.97pct\n", "Successfully fetched intraday data for RCAT_2024-07-17_8d_66.96pct\n", "Successfully fetched intraday data for TMCI_2024-11-06_4d_66.95pct\n", "Successfully fetched intraday data for NVCR_2024-12-02_3d_66.95pct\n", "Successfully fetched intraday data for GPCR_2024-06-05_4d_66.77pct\n", "Successfully fetched intraday data for DAVE_2024-11-12_5d_66.76pct\n", "Successfully fetched intraday data for LEU_2024-10-18_4d_66.76pct\n", "Successfully fetched intraday data for RIOT_2024-11-11_5d_66.7pct\n", "Successfully fetched intraday data for NNE_2024-08-19_4d_66.47pct\n", "Successfully fetched intraday data for ELVN_2024-03-20_4d_66.46pct\n", "Successfully fetched intraday data for JANX_2024-02-23_4d_66.38pct\n", "Successfully fetched intraday data for ARQT_2024-02-20_7d_66.32pct\n", "Successfully fetched intraday data for RCAT_2024-07-16_7d_66.09pct\n", "Successfully fetched intraday data for MVST_2024-12-23_3d_66.09pct\n", "Successfully fetched intraday data for GRAL_2025-02-14_3d_65.97pct\n", "Successfully fetched intraday data for EBS_2024-07-15_10d_65.84pct\n", "Successfully fetched intraday data for DXYZ_2024-12-06_4d_65.79pct\n", "Successfully fetched intraday data for GDS_2025-02-19_9d_65.74pct\n", "Successfully fetched intraday data for SLQT_2024-05-08_6d_65.56pct\n", "Successfully fetched intraday data for TMC_2024-12-30_6d_65.52pct\n", "Successfully fetched intraday data for ACMR_2024-03-01_5d_65.5pct\n", "Successfully fetched intraday data for KULR_2024-11-11_7d_65.46pct\n", "Successfully fetched intraday data for CORZ_2024-06-06_4d_65.26pct\n", "Successfully fetched intraday data for COGT_2024-02-16_3d_65.2pct\n", "Successfully fetched intraday data for DJTWW_2024-06-25_3d_64.65pct\n", "Successfully fetched intraday data for NN_2024-03-26_7d_64.61pct\n", "Successfully fetched intraday data for RCAT_2024-12-26_5d_64.54pct\n", "Successfully fetched intraday data for GRAL_2025-01-24_5d_64.38pct\n", "Successfully fetched intraday data for CCSI_2024-05-14_7d_64.21pct\n", "Successfully fetched intraday data for UP_2024-07-02_3d_64.13pct\n", "Successfully fetched intraday data for NNE_2024-06-18_3d_64.07pct\n", "Successfully fetched intraday data for BTBT_2024-11-12_6d_63.8pct\n", "Successfully fetched intraday data for VRDN_2024-09-13_6d_63.77pct\n", "Successfully fetched intraday data for EXOD_2025-03-21_3d_63.65pct\n", "Successfully fetched intraday data for ATUS_2024-02-28_3d_63.39pct\n", "Successfully fetched intraday data for QS_2024-07-12_5d_63.37pct\n", "Successfully fetched intraday data for QBTS_2024-11-11_5d_63.24pct\n", "Successfully fetched intraday data for ARQT_2024-02-16_6d_63.16pct\n", "Successfully fetched intraday data for WOOF_2024-09-16_5d_63.16pct\n", "Successfully fetched intraday data for ARQT_2024-01-26_5d_63.08pct\n", "Successfully fetched intraday data for ATEC_2024-11-04_8d_62.96pct\n", "Successfully fetched intraday data for LX_2024-09-30_5d_62.87pct\n", "Successfully fetched intraday data for IREN_2024-02-09_4d_62.74pct\n", "Successfully fetched intraday data for SG_2024-03-04_5d_62.6pct\n", "Successfully fetched intraday data for HEPS_2024-10-21_3d_62.1pct\n", "Successfully fetched intraday data for ML_2024-11-07_4d_62.08pct\n", "Successfully fetched intraday data for RDW_2025-01-21_4d_62.05pct\n", "Successfully fetched intraday data for IREN_2024-05-22_8d_62.0pct\n", "Successfully fetched intraday data for CGEM_2024-04-26_4d_61.87pct\n", "Successfully fetched intraday data for ASPN_2024-08-16_7d_61.73pct\n", "Successfully fetched intraday data for BLBD_2024-05-15_10d_61.38pct\n", "Successfully fetched intraday data for RCAT_2024-04-17_3d_61.38pct\n", "Successfully fetched intraday data for AKBA_2024-03-25_5d_61.31pct\n", "Successfully fetched intraday data for ENVX_2024-05-03_3d_61.18pct\n", "Successfully fetched intraday data for EBS_2024-08-16_4d_61.02pct\n", "Successfully fetched intraday data for ASPN_2024-02-16_9d_60.95pct\n", "Successfully fetched intraday data for SLQT_2024-05-07_5d_60.93pct\n", "Successfully fetched intraday data for MSC_2025-03-07_3d_60.92pct\n", "Successfully fetched intraday data for BTBT_2024-11-11_5d_60.83pct\n", "Successfully fetched intraday data for GPCR_2024-06-04_3d_60.81pct\n", "Successfully fetched intraday data for WOOF_2024-09-13_4d_60.7pct\n", "Successfully fetched intraday data for SEZL_2024-08-12_5d_60.49pct\n", "Successfully fetched intraday data for QBTS_2024-11-08_4d_60.24pct\n", "Successfully fetched intraday data for XMTR_2024-08-13_4d_60.23pct\n", "Successfully fetched intraday data for QUBT_2024-10-21_4d_60.16pct\n", "Successfully fetched intraday data for ASPN_2024-02-15_8d_59.76pct\n", "Successfully fetched intraday data for CLOV_2024-08-15_6d_59.76pct\n", "Successfully fetched intraday data for FLX_2024-12-09_6d_59.56pct\n", "Successfully fetched intraday data for FARO_2024-11-11_7d_59.52pct\n", "Successfully fetched intraday data for TASK_2024-11-08_12d_59.43pct\n", "Successfully fetched intraday data for DJTWW_2024-10-29_3d_59.32pct\n", "Successfully fetched intraday data for WOOF_2024-09-12_3d_59.3pct\n", "Successfully fetched intraday data for OUST_2024-11-11_7d_59.13pct\n", "Successfully fetched intraday data for CCSI_2024-05-13_6d_59.05pct\n", "Successfully fetched intraday data for BTDR_2024-11-11_5d_59.05pct\n", "Successfully fetched intraday data for SEZL_2024-03-27_7d_59.04pct\n", "Successfully fetched intraday data for AHG_2024-02-01_3d_58.93pct\n", "Successfully fetched intraday data for LMND_2024-11-21_5d_58.81pct\n", "Successfully fetched intraday data for PSNY_2024-02-29_4d_58.78pct\n", "Successfully fetched intraday data for COMM_2024-08-14_5d_58.69pct\n", "Successfully fetched intraday data for GOTU_2024-02-27_5d_58.69pct\n", "Successfully fetched intraday data for DJTWW_2024-07-15_3d_58.66pct\n", "Successfully fetched intraday data for SERV_2024-12-10_4d_58.29pct\n", "Successfully fetched intraday data for DXYZ_2024-11-19_3d_58.16pct\n", "Successfully fetched intraday data for ATEC_2024-11-01_7d_58.09pct\n", "Successfully fetched intraday data for HUT_2024-11-11_5d_58.03pct\n", "Successfully fetched intraday data for PGY_2024-11-11_5d_58.0pct\n", "Successfully fetched intraday data for RGTI_2024-10-21_4d_57.93pct\n", "Successfully fetched intraday data for SEZL_2024-08-09_4d_57.84pct\n", "Successfully fetched intraday data for NNE_2025-01-21_5d_57.82pct\n", "Successfully fetched intraday data for HROW_2024-05-14_3d_57.82pct\n", "Successfully fetched intraday data for BBAI_2024-12-26_4d_57.8pct\n", "Successfully fetched intraday data for FLX_2024-12-06_5d_57.78pct\n", "Successfully fetched intraday data for RUN_2024-07-12_8d_57.58pct\n", "Successfully fetched intraday data for ASPN_2024-02-14_7d_57.38pct\n", "Successfully fetched intraday data for DAVE_2024-11-11_4d_57.36pct\n", "Successfully fetched intraday data for UXIN_2024-05-15_4d_57.33pct\n", "Successfully fetched intraday data for LX_2024-12-02_6d_57.27pct\n", "Successfully fetched intraday data for VSAT_2025-01-21_4d_57.26pct\n", "Successfully fetched intraday data for WOOF_2024-05-15_3d_57.23pct\n", "Successfully fetched intraday data for MRNO_2024-05-15_8d_57.12pct\n", "Successfully fetched intraday data for GDS_2025-02-18_8d_57.11pct\n", "Successfully fetched intraday data for SFIX_2024-06-05_5d_57.05pct\n", "Successfully fetched intraday data for DAVE_2024-01-22_6d_57.02pct\n", "Successfully fetched intraday data for COMM_2024-07-31_5d_56.97pct\n", "Successfully fetched intraday data for COMM_2024-03-12_6d_56.86pct\n", "Successfully fetched intraday data for QUBT_2025-03-14_4d_56.86pct\n", "Successfully fetched intraday data for WULF_2024-06-06_3d_56.59pct\n", "Successfully fetched intraday data for CCSI_2024-05-10_5d_56.51pct\n", "Successfully fetched intraday data for PPTA_2024-08-20_7d_56.38pct\n", "Successfully fetched intraday data for MRNO_2024-11-06_4d_56.32pct\n", "Successfully fetched intraday data for NN_2024-03-25_6d_56.29pct\n", "Successfully fetched intraday data for HNST_2024-03-11_4d_56.19pct\n", "Successfully fetched intraday data for EBS_2024-07-12_9d_56.16pct\n", "Successfully fetched intraday data for RZLV_2024-12-26_3d_56.08pct\n", "Successfully fetched intraday data for CAN_2024-02-12_5d_55.83pct\n", "Successfully fetched intraday data for BCYC_2024-02-27_13d_55.82pct\n", "Successfully fetched intraday data for REPL_2024-06-07_3d_55.82pct\n", "Successfully fetched intraday data for KULR_2025-03-17_4d_55.75pct\n", "Successfully fetched intraday data for VHI_2024-08-19_7d_55.65pct\n", "Successfully fetched intraday data for MVST_2024-09-16_3d_55.64pct\n", "Successfully fetched intraday data for CIFR_2024-07-16_3d_55.5pct\n", "Successfully fetched intraday data for DNTH_2024-01-24_4d_55.48pct\n", "Successfully fetched intraday data for CIFR_2024-02-12_3d_55.38pct\n", "Successfully fetched intraday data for PPTA_2024-08-19_6d_55.34pct\n", "Successfully fetched intraday data for HEPS_2024-07-05_12d_55.31pct\n", "Successfully fetched intraday data for SWIM_2024-05-09_6d_55.24pct\n", "Successfully fetched intraday data for RDFN_2024-09-13_5d_55.21pct\n", "Successfully fetched intraday data for FUBO_2024-08-19_5d_55.17pct\n", "Successfully fetched intraday data for ARIS_2024-11-11_6d_55.06pct\n", "Successfully fetched intraday data for NNE_2024-09-11_3d_55.0pct\n", "Successfully fetched intraday data for JANX_2024-02-22_3d_54.93pct\n", "Successfully fetched intraday data for MESO_2024-10-04_7d_54.87pct\n", "Successfully fetched intraday data for QBTS_2024-02-13_3d_54.84pct\n", "Successfully fetched intraday data for HLF_2025-02-24_3d_54.8pct\n", "Successfully fetched intraday data for EBS_2024-10-02_5d_54.71pct\n", "Successfully fetched intraday data for EH_2025-02-13_8d_54.64pct\n", "Successfully fetched intraday data for EVLV_2024-11-29_6d_54.58pct\n", "Successfully fetched intraday data for IREN_2024-05-21_7d_54.49pct\n", "Successfully fetched intraday data for TMC_2025-01-21_4d_54.29pct\n", "Successfully fetched intraday data for POWL_2024-01-31_3d_54.28pct\n", "Successfully fetched intraday data for DJTWW_2024-10-14_3d_54.14pct\n", "Successfully fetched intraday data for DNA_2025-01-23_6d_53.97pct\n", "Successfully fetched intraday data for RIOT_2024-02-12_5d_53.97pct\n", "Successfully fetched intraday data for COMM_2024-07-30_4d_53.94pct\n", "Successfully fetched intraday data for CTKB_2024-11-06_6d_53.85pct\n", "Successfully fetched intraday data for BGM_2025-01-07_3d_53.85pct\n", "Successfully fetched intraday data for PLUG_2024-01-23_3d_53.72pct\n", "Successfully fetched intraday data for OPEN_2024-07-16_4d_53.68pct\n", "Successfully fetched intraday data for VRDN_2024-09-12_5d_53.67pct\n", "Successfully fetched intraday data for HUMA_2024-07-16_4d_53.67pct\n", "Successfully fetched intraday data for LX_2024-11-29_5d_53.64pct\n", "Successfully fetched intraday data for ADMA_2024-08-15_6d_53.62pct\n", "Successfully fetched intraday data for AHG_2024-07-03_4d_53.59pct\n", "Successfully fetched intraday data for STOK_2024-02-27_3d_53.57pct\n", "Successfully fetched intraday data for RNA_2024-06-13_5d_53.46pct\n", "Successfully fetched intraday data for ADMA_2024-08-14_5d_53.45pct\n", "Successfully fetched intraday data for DDL_2024-05-09_11d_53.28pct\n", "Successfully fetched intraday data for FARO_2024-11-08_6d_53.25pct\n", "Successfully fetched intraday data for NVTS_2024-12-05_5d_53.25pct\n", "Successfully fetched intraday data for HNRG_2025-03-24_10d_53.14pct\n", "Successfully fetched intraday data for PTON_2024-08-26_4d_53.12pct\n", "Successfully fetched intraday data for BLBD_2024-05-14_9d_53.09pct\n", "Successfully fetched intraday data for HEPS_2024-07-03_11d_53.07pct\n", "Successfully fetched intraday data for ASTS_2025-02-06_6d_53.05pct\n", "Successfully fetched intraday data for RCAT_2024-07-15_6d_53.04pct\n", "Successfully fetched intraday data for ATEC_2024-10-31_6d_53.02pct\n", "Successfully fetched intraday data for ULCC_2024-02-12_5d_53.01pct\n", "Successfully fetched intraday data for PLSE_2024-05-23_4d_53.01pct\n", "Successfully fetched intraday data for IMNM_2024-02-07_3d_52.98pct\n", "Successfully fetched intraday data for WULF_2024-02-12_5d_52.88pct\n", "Successfully fetched intraday data for PLSE_2024-05-22_3d_52.88pct\n", "Successfully fetched intraday data for EOSE_2024-07-08_5d_52.76pct\n", "Successfully fetched intraday data for KC_2024-11-07_3d_52.71pct\n", "Successfully fetched intraday data for BCYC_2024-02-26_12d_52.6pct\n", "Successfully fetched intraday data for HNST_2024-11-19_5d_52.5pct\n", "Successfully fetched intraday data for REAL_2024-12-11_4d_52.45pct\n", "Successfully fetched intraday data for CIFR_2024-03-21_5d_52.37pct\n", "Successfully fetched intraday data for BFLY_2024-02-27_3d_52.24pct\n", "Successfully fetched intraday data for VERV_2025-01-23_6d_52.19pct\n", "Successfully fetched intraday data for AVAH_2024-07-17_6d_52.17pct\n", "Successfully fetched intraday data for RCAT_2024-07-12_5d_52.17pct\n", "Successfully fetched intraday data for PSNL_2024-07-18_3d_52.17pct\n", "Successfully fetched intraday data for BGM_2024-12-02_4d_52.14pct\n", "Successfully fetched intraday data for COMM_2024-08-13_4d_52.11pct\n", "Successfully fetched intraday data for ZIM_2024-05-14_8d_52.08pct\n", "Successfully fetched intraday data for ELVN_2024-03-19_3d_51.99pct\n", "Successfully fetched intraday data for RIOT_2024-04-25_6d_51.94pct\n", "Successfully fetched intraday data for RGTI_2024-12-24_3d_51.94pct\n", "Successfully fetched intraday data for NVTS_2024-11-25_3d_51.91pct\n", "Successfully fetched intraday data for BTDR_2024-12-06_4d_51.81pct\n", "Successfully fetched intraday data for TMC_2024-12-27_5d_51.72pct\n", "Successfully fetched intraday data for QBTS_2025-01-16_3d_51.7pct\n", "Successfully fetched intraday data for FWRD_2024-06-10_8d_51.5pct\n", "Successfully fetched intraday data for DDD_2024-12-12_5d_51.48pct\n", "Successfully fetched intraday data for TMC_2025-01-17_3d_51.43pct\n", "Successfully fetched intraday data for LUNR_2024-02-12_3d_51.43pct\n", "Successfully fetched intraday data for MRNO_2024-05-14_7d_51.41pct\n", "Successfully fetched intraday data for SEZL_2024-02-27_3d_51.33pct\n", "Successfully fetched intraday data for CORZ_2024-02-12_7d_51.31pct\n", "Successfully fetched intraday data for NVAX_2024-02-27_4d_51.26pct\n", "Successfully fetched intraday data for PTON_2024-08-23_3d_51.25pct\n", "Successfully fetched intraday data for EOSE_2024-07-05_4d_51.18pct\n", "Successfully fetched intraday data for GETY_2025-01-07_3d_51.18pct\n", "Successfully fetched intraday data for DNTH_2024-01-23_3d_51.13pct\n", "Successfully fetched intraday data for BGM_2024-09-03_4d_51.09pct\n", "Successfully fetched intraday data for LU_2024-10-01_4d_51.03pct\n", "Successfully fetched intraday data for SWTX_2025-02-12_4d_50.92pct\n", "Successfully fetched intraday data for IMNM_2024-12-02_7d_50.91pct\n", "Successfully fetched intraday data for ACMR_2024-02-29_4d_50.9pct\n", "Successfully fetched intraday data for GDS_2025-02-14_7d_50.83pct\n", "Successfully fetched intraday data for ADMA_2024-08-13_4d_50.83pct\n", "Successfully fetched intraday data for CLOV_2024-08-14_5d_50.75pct\n", "Successfully fetched intraday data for ZIM_2024-05-13_7d_50.72pct\n", "Successfully fetched intraday data for EMBC_2024-11-29_4d_50.72pct\n", "Successfully fetched intraday data for DNA_2025-01-22_5d_50.7pct\n", "Successfully fetched intraday data for EH_2025-02-12_7d_50.63pct\n", "Successfully fetched intraday data for CORZ_2024-06-05_3d_50.53pct\n", "Successfully fetched intraday data for RDFN_2024-07-16_9d_50.35pct\n", "Successfully fetched intraday data for SLQT_2024-05-06_4d_50.33pct\n", "Successfully fetched intraday data for LOT_2024-05-29_4d_50.16pct\n", "Successfully fetched intraday data for RXRX_2025-02-19_8d_50.14pct\n", "Successfully fetched intraday data for HNST_2024-11-18_4d_50.0pct\n", "\n", "Successfully fetched intraday data for 454 events\n", "\n", "Sample data for DNA_2024-08-21_3d_4103.16pct:\n", "Shape: (545, 6)\n", "Time range: 2024-08-21 04:00:00-04:00 to 2024-08-21 19:58:00-04:00\n", "Columns: ['open', 'high', 'low', 'close', 'volume', 'count']\n", "\n", "First few rows:\n", "                           open  high   low  close  volume  count\n", "timestamp                                                        \n", "2024-08-21 04:00:00-04:00  7.70  7.70  7.70   7.70      10      1\n", "2024-08-21 04:01:00-04:00  7.69  7.69  7.69   7.69       1      1\n", "2024-08-21 04:12:00-04:00  7.69  7.69  7.69   7.69       1      1\n", "2024-08-21 04:17:00-04:00  7.69  7.69  7.69   7.69     374      1\n", "2024-08-21 04:18:00-04:00  7.69  7.69  7.69   7.69     100      1\n"]}], "source": ["# Fetch intraday data for each event\n", "# To avoid overwhelming the API, you might want to limit to the top N events\n", "top_events = rapid_risers  # Adjust as needed\n", "print(f\"Fetching intraday data for {len(top_events)} events...\")\n", "\n", "# Get intraday data\n", "event_intraday_data = get_intraday_data_for_events(top_events)\n", "\n", "# Display summary of obtained data\n", "print(f\"\\nSuccessfully fetched intraday data for {len(event_intraday_data)} events\")\n", "\n", "# Let's check the first event's data\n", "if event_intraday_data:\n", "    first_key = list(event_intraday_data.keys())[0]\n", "    print(f\"\\nSample data for {first_key}:\")\n", "    first_data = event_intraday_data[first_key]\n", "    print(f\"Shape: {first_data.shape}\")\n", "    print(f\"Time range: {first_data.index[0]} to {first_data.index[-1]}\")\n", "    print(f\"Columns: {first_data.columns.tolist()}\")\n", "    print(\"\\nFirst few rows:\")\n", "    print(first_data.head())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Insufficient daily data for DXYZ_2024-04-08_4d_370.26pct\n", "Insufficient daily data for DXYZ_2024-04-05_3d_178.98pct\n", "Insufficient daily data for MVST_2024-12-03_4d_143.1pct\n", "Insufficient daily data for SEZL_2024-01-19_4d_123.53pct\n", "Insufficient dollar volume for AHG_2024-05-23_4d_94.92pct: $97326.59\n", "Insufficient daily data for AHG_2024-02-02_4d_85.71pct\n", "Insufficient daily data for MVST_2024-12-02_3d_82.76pct\n", "No opening range data for CLSK_2024-11-11_4d_79.69pct\n", "Insufficient daily data for SBC_2024-09-27_5d_73.32pct\n", "Insufficient daily data for SBC_2024-09-26_4d_67.37pct\n", "Insufficient daily data for AKBA_2024-03-25_5d_61.31pct\n", "Insufficient dollar volume for MSC_2025-03-07_3d_60.92pct: $63479.10\n", "Insufficient dollar volume for FLX_2024-12-09_6d_59.56pct: $233025.08\n", "Insufficient daily data for AHG_2024-02-01_3d_58.93pct\n", "Insufficient dollar volume for FLX_2024-12-06_5d_57.78pct: $198223.92\n", "Insufficient dollar volume for UXIN_2024-05-15_4d_57.33pct: $471418.58\n", "Insufficient dollar volume for MRNO_2024-05-15_8d_57.12pct: $378905.81\n", "Insufficient dollar volume for MRNO_2024-11-06_4d_56.32pct: $145391.28\n", "Insufficient dollar volume for VHI_2024-08-19_7d_55.65pct: $578090.04\n", "Insufficient daily data for BGM_2025-01-07_3d_53.85pct\n", "Insufficient dollar volume for AHG_2024-07-03_4d_53.59pct: $144283.14\n", "Insufficient dollar volume for AVAH_2024-07-17_6d_52.17pct: $844750.03\n", "Insufficient dollar volume for MRNO_2024-05-14_7d_51.41pct: $69814.55\n", "Insufficient dollar volume for BGM_2024-09-03_4d_51.09pct: $606251.99\n", "\n", "Trading Strategy Simulation Results:\n", "Total events analyzed: 430\n", "Number of trades executed: 430\n", "\n", "Performance Summary:\n", "Average P/L %: -2.50%\n", "Median P/L %: -2.23%\n", "Win Rate: 10.70%\n", "\n", "Results by Exit Type:\n", "                  count      mean  median    min    max\n", "position_outcome                                       \n", "EOD Close            63  2.705079    1.34 -11.08  18.97\n", "Profit Target         3  2.636667   -0.29  -0.35   8.55\n", "Stop Loss           364 -3.438297   -2.57 -21.09  -0.02\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA+0AAAIjCAYAAAB20vpjAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAWu9JREFUeJzt3Xd8FAXCxvFn0zYFkhACCQFSBKSJgDQjeFJyhFAU4VQ8VJriKUWKiqhIUUQBAUEU0TNwnpwnviciIIihqQQOwouKAoIHhAMSiJjE9DbvH3lZXZNQE2bC/r6fz3ycnZmdfZbJmjw7zWYYhiEAAAAAAGA5bmYHAAAAAAAA5aO0AwAAAABgUZR2AAAAAAAsitIOAAAAAIBFUdoBAAAAALAoSjsAAAAAABZFaQcAAAAAwKIo7QAAAAAAWBSlHQAAAAAAi6K0AwAuyrRp02Sz2a7Ka3Xt2lVdu3Z1PN6yZYtsNps+/PDDq/L6Q4cOVWRk5FV5rcuVlZWlBx98UKGhobLZbBo3bpzZkco4t922bNlidpSr5vc/u1Wpd+/eeuihh67Ka1WlJUuWKDw8XPn5+WZHAQBLorQDgAtatmyZbDabY/D29lZYWJhiY2O1cOFC/fLLL5XyOidPntS0adO0d+/eSllfZbJytovx4osvatmyZXrkkUf07rvv6v777y+zzLkvWi40XK2SeaWOHj3qlNvNzU1BQUGKi4tTYmKi2fHKVVU/Z1999ZU+++wzTZo0yTHt3Jck5wZPT09dd911euCBB/Sf//yn3PUMHDhQvXv3lvTrz0taWtolZTEMQ9OnT1f9+vVVt25djRs3TgUFBU7LZGVlqX79+lqxYkWZ5w8dOlQFBQV68803L+l1AcBVeJgdAABgnhkzZigqKkqFhYVKSUnRli1bNG7cOM2bN0+rV6/WjTfe6Fj22Wef1VNPPXVJ6z958qSmT5+uyMhItWnT5qKf99lnn13S61yO82V76623VFJSUuUZrsSmTZt08803a+rUqRUuM2DAADVu3NjxOCsrS4888ojuvPNODRgwwDE9JCSkSrNWtnvvvVe9e/dWcXGxfvjhB73++uvq1q2bdu3apVatWpkdz8nlfgYuZM6cOerRo4fT9j1n7Nix6tChgwoLC7Vnzx4tXbpUa9eu1bfffquwsDDHcoWFhdq4caNmzZp1RVnee+89vfjii5o0aZL8/Pw0c+ZMhYSEaPLkyY5lZs6cqcjISP35z38u83xvb28NGTJE8+bN05gxY67aET0AUF1Q2gHAhcXFxal9+/aOx5MnT9amTZvUt29f3X777dq/f798fHwkSR4eHvLwqNpfGzk5OfL19ZWXl1eVvs6FeHp6mvr6F+P06dNq0aLFeZe58cYbnb54SUtL0yOPPKIbb7xR9913X4XPy8vLk5eXl9zcrHlA3k033eSU/9Zbb1VcXJzeeOMNvf766yYmuzpOnz6ttWvXasmSJeXOv/XWW/WnP/1JkjRs2DBdf/31Gjt2rJYvX+5UpL/44gv98ssv6tOnzxXlWbNmjQYPHqwZM2ZIknJzc7V69WrHa/3444969dVXtW3btgrXcffdd2v27NnavHmzunfvfkV5AOBaY83fxgAA03Tv3l1TpkzRsWPH9Pe//90xvbxz2jdu3KguXbooMDBQNWrUUNOmTfX0009LKj1Ut0OHDpJKi8O5Q3aXLVsmqfTc3xtuuEFJSUn6wx/+IF9fX8dzKzovuLi4WE8//bRCQ0Pl5+en22+/XcePH3daJjIyUkOHDi3z3N+u80LZyjunPTs7WxMnTlTDhg1lt9vVtGlTzZ07V4ZhOC1ns9k0evRorVq1SjfccIPsdrtatmyp9evXl/8P/junT5/WiBEjFBISIm9vb7Vu3VrLly93zD93CPSRI0e0du1aR/ajR49e1Pp/79z63n//fT377LOqX7++fH19lZmZqbNnz+rxxx9Xq1atVKNGDfn7+ysuLk5ff/11mfX897//Vf/+/eXn56e6detq/PjxFZ6jvHPnTvXq1UsBAQHy9fXVbbfdpq+++uqy8kulJVUqLYe/lZ6ernHjxjm2WePGjfXyyy+XOYri/fffV7t27VSzZk35+/urVatWevXVVx3zK7qew7nTTCr6t7/Qz9mhQ4c0cOBAhYaGytvbWw0aNNCgQYOUkZFx3ve7du1aFRUVKSYm5rzLnXOuBB85cqTMelq0aHHF12/Izc1VrVq1HI+DgoKUk5PjeDxx4kQNGjTI6QvC32vXrp2CgoL08ccfX1EWALgWsacdAFDG/fffr6efflqfffZZhRe6+u6779S3b1/deOONmjFjhux2uw4fPuwoX82bN9eMGTP03HPPaeTIkY5idcsttzjW8dNPPykuLk6DBg3Sfffdd8HDtGfOnCmbzaZJkybp9OnTWrBggWJiYrR3717HEQEX42Ky/ZZhGLr99tu1efNmjRgxQm3atNGGDRv0xBNP6MSJE5o/f77T8l9++aX+9a9/6dFHH1XNmjW1cOFCDRw4UMnJyapdu3aFuXJzc9W1a1cdPnxYo0ePVlRUlFauXKmhQ4cqPT1djz32mJo3b653331X48ePV4MGDTRx4kRJUp06dS76/Zfn+eefl5eXlx5//HHl5+fLy8tL33//vVatWqW77rpLUVFRSk1N1ZtvvqnbbrtN33//veNQ69zcXPXo0UPJyckaO3aswsLC9O6772rTpk1lXmfTpk2Ki4tTu3btNHXqVLm5uSk+Pl7du3fXF198oY4dO15y9nOl+bfFMScnR7fddptOnDihhx9+WOHh4dq+fbsmT56sU6dOacGCBZJKv3i699571aNHD7388suSpP379+urr77SY489dslZfut8P2cFBQWKjY1Vfn6+xowZo9DQUJ04cUJr1qxRenq6AgICKlzv9u3bVbt2bUVERFxUjnNfZvz+Z2/dunXq27fvZb67X3Xo0EGvv/667rrrLvn5+enNN990fJY2btyoTZs26Ycffrjgem666aYr+vIGAK5ZBgDA5cTHxxuSjF27dlW4TEBAgNG2bVvH46lTpxq//bUxf/58Q5Jx5syZCtexa9cuQ5IRHx9fZt5tt91mSDKWLFlS7rzbbrvN8Xjz5s2GJKN+/fpGZmamY/oHH3xgSDJeffVVx7SIiAhjyJAhF1zn+bINGTLEiIiIcDxetWqVIcl44YUXnJb705/+ZNhsNuPw4cOOaZIMLy8vp2lff/21IclYtGhRmdf6rQULFhiSjL///e+OaQUFBUZ0dLRRo0YNp/ceERFh9OnT57zr+70zZ84YkoypU6c6pp37t73uuuuMnJwcp+Xz8vKM4uJip2lHjhwx7Ha7MWPGjDK5P/jgA8e07Oxso3HjxoYkY/PmzYZhGEZJSYnRpEkTIzY21igpKXEsm5OTY0RFRRl//OMfz5v/yJEjhiRj+vTpxpkzZ4yUlBTjiy++MDp06GBIMlauXOlY9vnnnzf8/PyMH374wWkdTz31lOHu7m4kJycbhmEYjz32mOHv728UFRVV+Lq//9k/59zn6MiRI45pF/tz9r//+79lMl+sLl26GO3atSsz/dy2fOedd4wzZ84YJ0+eNNauXWtERkYaNpvN6fP+n//8x2nb/PZ9nu8zXZ7MzEyjS5cuhiRDktGyZUvjv//9r1FYWGi0aNHCeOmlly5qPSNHjjR8fHwu6bUBwBVweDwAoFw1atQ471XkAwMDJUkff/zxZV+0zW63a9iwYRe9/AMPPKCaNWs6Hv/pT39SvXr1tG7dust6/Yu1bt06ubu7a+zYsU7TJ06cKMMw9OmnnzpNj4mJUaNGjRyPb7zxRvn7+1d4Be/fvk5oaKjuvfdexzRPT0+NHTtWWVlZ2rp1ayW8m/INGTKkzNEKdrvdcV57cXGxfvrpJ8dpEHv27HHKXa9ePcd51JLk6+urkSNHOq1v7969OnTokP785z/rp59+UlpamtLS0pSdna0ePXpo27ZtF/WzNHXqVNWpU0ehoaG69dZbtX//fr3yyitOr79y5UrdeuutqlWrluN10tLSFBMTo+LiYsf51YGBgcrOztbGjRsv/R/tCpzbk75hwwanQ8kvxk8//eR0VMHvDR8+XHXq1FFYWJj69Omj7OxsLV++3Onw9LVr1yogIEBdunS5vDfwGzVr1tTWrVv13Xffae/evdq7d6/q16+v119/Xfn5+Ro/fry+//57devWTfXr19d9992nzMzMMuupVauWcnNzL/nfAwCudZR2AEC5srKynAry791zzz3q3LmzHnzwQYWEhGjQoEH64IMPLqnA169f/5IuOtekSROnxzabTY0bN77s87kv1rFjxxQWFlbm36N58+aO+b8VHh5eZh21atXSzz//fMHXadKkSZkLwFX0OpUpKiqqzLSSkhLNnz9fTZo0kd1uV3BwsOrUqaNvvvnG6bzrY8eOqXHjxmXO+27atKnT40OHDkkq/YKgTp06TsPbb7+t/Pz8C57PLUkjR47Uxo0b9cknn2j8+PHKzc1VcXFxmddav359mdc5dx746dOnJUmPPvqorr/+esXFxalBgwYaPnz4RV9/4EpERUVpwoQJevvttxUcHKzY2FgtXrz4ot6/pDLXUvit5557znFY+jfffKOTJ0+WuSXg2rVr1bNnz0q7uKSbm5tatGih1q1by8PDQ2lpaZo2bZrmzp0rm82mvn37qlWrVvr444+VnJysMWPGVPieuHo8ADjjnHYAQBn//e9/lZGRUe7tpM7x8fHRtm3btHnzZq1du1br16/XP//5T3Xv3l2fffaZ3N3dL/g6l3Ie+sWq6A/+4uLii8pUGSp6nfMVLbOVty1efPFFTZkyRcOHD9fzzz+voKAgubm5ady4cZd1dMW558yZM6fC25/VqFHjgutp0qSJo3z37dtX7u7ueuqpp9StWzfH3uSSkhL98Y9/1JNPPlnuOq6//npJUt26dbV3715t2LBBn376qT799FPFx8frgQcecFwA8Hw/U1filVde0dChQ/Xxxx/rs88+09ixYzVr1izt2LFDDRo0qPB5tWvXPu8XQK1atTrvRepycnK0ZcsWvfHGG1eU/3ymTJmim266Sf3799cXX3yhU6dOafbs2fL29tb06dPVq1cvxcfHO31B9fPPP8vX17dK/r8AANUZpR0AUMa7774rSYqNjT3vcm5uburRo4d69OihefPm6cUXX9QzzzyjzZs3KyYmptL3mJ3bU3uOYRg6fPiw023NatWqpfT09DLPPXbsmK677jrH40vJFhERoc8//1y//PKL0972AwcOOOZXhoiICH3zzTcqKSlxKjOV/ToX68MPP1S3bt3017/+1Wl6enq6goODHY8jIiK0b98+GYbh9O968OBBp+edO2XA39//oq98fjGeeeYZvfXWW3r22Wcde8kbNWqkrKysi3odLy8v9evXT/369VNJSYkeffRRvfnmm5oyZYoaN27sOBQ9PT3dcVqIdHFHPlzo56xVq1Zq1aqVnn32WW3fvl2dO3fWkiVL9MILL1T4nGbNmul//ud/LvjaFdm0aZPy8/MVFxd32es4n6+//lrvvPOOkpKSJJXeq75WrVry9vaWJIWFhamgoEBnzpxxuvjkkSNHHEeVAAB+xeHxAAAnmzZt0vPPP6+oqCgNHjy4wuXOnj1bZtq5vafnbvXl5+cnSeWW6Mvxt7/9zek8+w8//FCnTp1yKh+NGjXSjh07VFBQ4Ji2Zs2aMreGu5RsvXv3VnFxsV577TWn6fPnz5fNZqu08tO7d2+lpKTon//8p2NaUVGRFi1apBo1aui2226rlNe5WO7u7mWODli5cqVOnDjhNK137946efKkPvzwQ8e0nJwcLV261Gm5du3aqVGjRpo7d66ysrLKvN6ZM2cuK2dgYKAefvhhbdiwQXv37pVUet/vxMREbdiwoczy6enpKioqklR6fvhvubm5Ob4EOvdzfO7Lht/eZ/zceeIXUtHPWWZmpiPDOa1atZKbm1uFt8o7Jzo6Wj///PMFr5FQkXXr1ql9+/YXvFvD5Xrsscf04IMP6oYbbpAkhYSE6MyZM47/Z+zfv18eHh5OX/xI0p49eyq8gwMAuDL2tAOAC/v000914MABFRUVKTU1VZs2bdLGjRsVERGh1atXO/aMlWfGjBnatm2b+vTpo4iICJ0+fVqvv/66GjRo4Li4VaNGjRQYGKglS5aoZs2a8vPzU6dOnco9f/piBAUFqUuXLho2bJhSU1O1YMECNW7c2Om2dA8++KA+/PBD9erVS3fffbd+/PFH/f3vf3e6MNylZuvXr5+6deumZ555RkePHlXr1q312Wef6eOPP9a4cePKrPtyjRw5Um+++aaGDh2qpKQkRUZG6sMPP9RXX32lBQsWnPcaA1Whb9++mjFjhoYNG6ZbbrlF3377rd577z2nIxYk6aGHHtJrr72mBx54QElJSapXr57effdd+fr6Oi3n5uamt99+W3FxcWrZsqWGDRum+vXr68SJE9q8ebP8/f31ySefXFbWxx57TAsWLNBLL72k999/X0888YRWr16tvn37aujQoWrXrp2ys7P17bff6sMPP9TRo0cVHBysBx98UGfPnlX37t3VoEEDHTt2TIsWLVKbNm0ce3179uyp8PBwjRgxQk888YTc3d31zjvvqE6dOkpOTj5vrop+zr7++muNHj1ad911l66//noVFRXp3Xfflbu7uwYOHHjedfbp00ceHh76/PPPy1zs72KsW7fuvBeAnDdvXrnb7umnn77guleuXKlvvvnG6UiA6OhohYSE6K677tKAAQM0d+5cDRgwwOk0kqSkJJ09e1Z33HHHJb8fALjmmXjlegCASc7dqurc4OXlZYSGhhp//OMfjVdffdXp1mLn/P62VwkJCcYdd9xhhIWFGV5eXkZYWJhx7733lrnF1scff2y0aNHC8PDwcLr11W233Wa0bNmy3HwV3fLtH//4hzF58mSjbt26ho+Pj9GnTx/j2LFjZZ7/yiuvGPXr1zfsdrvRuXNnY/fu3WXWeb5sv7/lm2EYxi+//GKMHz/eCAsLMzw9PY0mTZoYc+bMcbp1mWGU3vJt1KhRZTJVdCu630tNTTWGDRtmBAcHG15eXkarVq3KvS1dZd/yrbxbj+Xl5RkTJ0406tWrZ/j4+BidO3c2EhMTy/23PHbsmHH77bcbvr6+RnBwsPHYY48Z69evL3NbMcMovd3ZgAEDjNq1axt2u92IiIgw7r77biMhIeG8+c/d8m3OnDnlzh86dKjh7u7uuN3eL7/8YkyePNlo3Lix4eXlZQQHBxu33HKLMXfuXKOgoMAwDMP48MMPjZ49exp169Y1vLy8jPDwcOPhhx82Tp065bTupKQko1OnTo5l5s2bd1G3fDOM8n/O/vOf/xjDhw83GjVqZHh7extBQUFGt27djM8///y8/wbn3H777UaPHj2cpp1vW56zb98+Q5Lx73//u8y8c5/x8gZ3d/cLZsrJyTEiIiKMhQsXlpm3a9cu46abbjJq1qxp9OvXzzh9+rTT/EmTJhnh4eFlPk8AAMOwGYaFr4oDAACAMr744gt17dpVBw4cKHNXhfOZPXu25s2bp1OnTlnmKu35+fmKjIzUU089pccee8zsOABgOZzTDgAAUM3ceuut6tmzp2bPnn1Jz4uMjHRci8Eq4uPj5enpqb/85S9mRwEAS2JPOwAAAAAAFsWedgAAAAAALIrSDgAAAACARVHaAQAAAACwKEo7AAAAAAAW5WF2ACsoKSnRyZMnVbNmTUtdTRUAAAAAcG0yDEO//PKLwsLC5OZW8f50SrukkydPqmHDhmbHAAAAAAC4mOPHj6tBgwYVzqe0S6pZs6ak0n8sf39/k9MAAODiCgul+PjS8WHDJE9Pc/MAAFAFMjMz1bBhQ0cfrQj3aVfpP1ZAQIAyMjIo7QAAmC07W6pRo3Q8K0vy8zM3DwAAVeBieygXogMAAAAAwKIo7QAAAAAAWBSlHQAAAAAAi6K0AwAAAABgUZR2AAAAAAAsitIOAAAAAIBFcZ92AABgLXa7tGbNr+MAALgwSjsAALAWDw+pTx+zUwAAYAkcHg8AAAAAgEWxpx0AAFhLYaH03nul44MHS56e5uYBAMBElHYAAGAtBQXSsGGl43fdRWkHALg0Do8HAAAAAMCiKO0AAAAAAFgUpR0AAAAAAIuitAMAAAAAYFGUdgAAAAAALIrSDgAAAACARXHLNwAAYC12u/TBB7+OAwDgwijtAABYVHJystLS0syOcUHBwcEKDw+vvBV6eJTenx0AAFDaAQCwouTkZDVr1ly5uTlmR7kgHx9fHTiwv3KLOwAAkERpBwDAktLS0pSbm6NOw6fKv16k2XEqlHnqqHa+M11paWmVV9qLiqSPPiodv/PO0j3vAAC4KH4LAgBgYf71IhUU3tTsGFdXfr50992l41lZlHYAgEvj6vEAAAAAAFgUpR0AAAAAAIuitAMAAAAAYFGUdgAAAAAALIrSDgAAAACARVHaAQAAAACwKO6hAgAArMXLS4qP/3UcAAAXRmkHAADW4ukpDR1qdgoAACyBw+MBAAAAALAo9rQDAABrKSqSNmwoHY+NlTz4cwUA4Lr4LQgAAKwlP1/q27d0PCuL0g4AcGkcHg8AAAAAgEVR2gEAAAAAsChKOwAAAAAAFkVpBwAAAADAoijtAAAAAABYFKUdAAAAAACL4h4qAADAWry8pNde+3UcAAAXRmkHAADW4ukpjRpldgoAACyBw+MBAAAAALAo9rQDAABrKS6WvviidPzWWyV3d3PzAABgIlP3tG/btk39+vVTWFiYbDabVq1aVWaZ/fv36/bbb1dAQID8/PzUoUMHJScnO+bn5eVp1KhRql27tmrUqKGBAwcqNTX1Kr4LAABQqfLypG7dSoe8PLPTAABgKlNLe3Z2tlq3bq3FixeXO//HH39Uly5d1KxZM23ZskXffPONpkyZIm9vb8cy48eP1yeffKKVK1dq69atOnnypAYMGHC13gIAAAAAAFXG1MPj4+LiFBcXV+H8Z555Rr1799bs2bMd0xo1auQYz8jI0F//+letWLFC3bt3lyTFx8erefPm2rFjh26++eaqCw8AAAAAQBWz7IXoSkpKtHbtWl1//fWKjY1V3bp11alTJ6dD6JOSklRYWKiYmBjHtGbNmik8PFyJiYkVrjs/P1+ZmZlOAwAAAAAAVmPZ0n769GllZWXppZdeUq9evfTZZ5/pzjvv1IABA7R161ZJUkpKiry8vBQYGOj03JCQEKWkpFS47lmzZikgIMAxNGzYsCrfCgAAAAAAl8Wypb2kpESSdMcdd2j8+PFq06aNnnrqKfXt21dLliy5onVPnjxZGRkZjuH48eOVERkAAAAAgEpl2Vu+BQcHy8PDQy1atHCa3rx5c3355ZeSpNDQUBUUFCg9Pd1pb3tqaqpCQ0MrXLfdbpfdbq+S3AAAAAAAVBbL7mn38vJShw4ddPDgQafpP/zwgyIiIiRJ7dq1k6enpxISEhzzDx48qOTkZEVHR1/VvAAAoJJ4ekqzZ5cOnp5mpwEAwFSm7mnPysrS4cOHHY+PHDmivXv3KigoSOHh4XriiSd0zz336A9/+IO6deum9evX65NPPtGWLVskSQEBARoxYoQmTJigoKAg+fv7a8yYMYqOjubK8QAAVFdeXtITT5idAgAASzC1tO/evVvdunVzPJ4wYYIkaciQIVq2bJnuvPNOLVmyRLNmzdLYsWPVtGlT/c///I+6dOnieM78+fPl5uamgQMHKj8/X7GxsXr99dev+nsBAAAAAKCymVrau3btKsMwzrvM8OHDNXz48Arne3t7a/HixVq8eHFlxwMAAGYoLpb27Ckdv+kmyd3d3DwAAJjIsheiAwAALiovT+rYsXQ8K0vy8zM3DwAAJrLshegAAAAAAHB1lHYAAAAAACyK0g4AAAAAgEVR2gEAAAAAsChKOwAAAAAAFkVpBwAAAADAorjlGwAAsBZPT2nq1F/HAQBwYZR2AABgLV5e0rRpZqcAAMASODweAAAAAACLYk87AACwlpISaf/+0vHmzSU39jEAAFwXpR0AAFhLbq50ww2l41lZkp+fuXkAADARX10DAAAAAGBRlHYAAAAAACyK0g4AAAAAgEVR2gEAAAAAsChKOwAAAAAAFkVpBwAAAADAorjlGwAAsBZPT+nxx38dBwDAhVHaAQCAtXh5SXPmmJ0CAABL4PB4AAAAAAAsij3tAADAWkpKpOTk0vHwcMmNfQwAANdFaQcAANaSmytFRZWOZ2VJfn7m5gEAwER8dQ0AAAAAgEVR2gEAAAAAsChKOwAAAAAAFkVpBwAAAADAoijtAAAAAABYFKUdAAAAAACL4pZvAADAWjw8pEcf/XUcAAAXxm9CAABgLXa7tHix2SkAALAEDo8HAAAAAMCi2NMOAACsxTCktLTS8eBgyWYzNw8AACaitAMAAGvJyZHq1i0dz8qS/PzMzQMAgIk4PB4AAAAAAIuitAMAAAAAYFGUdgAAAAAALIrSDgAAAACARVHaAQAAAACwKEo7AAAAAAAWxS3fAACAtXh4SEOG/DoOAIALM3VP+7Zt29SvXz+FhYXJZrNp1apVFS77l7/8RTabTQsWLHCafvbsWQ0ePFj+/v4KDAzUiBEjlJWVVbXBAQBA1bHbpWXLSge73ew0AACYytTSnp2drdatW2vx4sXnXe6jjz7Sjh07FBYWVmbe4MGD9d1332njxo1as2aNtm3bppEjR1ZVZAAAAAAArhpTjzmLi4tTXFzceZc5ceKExowZow0bNqhPnz5O8/bv36/169dr165dat++vSRp0aJF6t27t+bOnVtuyZek/Px85efnOx5nZmZe4TsBAACVxjCknJzScV9fyWYzNw8AACay9IXoSkpKdP/99+uJJ55Qy5Yty8xPTExUYGCgo7BLUkxMjNzc3LRz584K1ztr1iwFBAQ4hoYNG1ZJfgAAcBlycqQaNUqHc+UdAAAXZenS/vLLL8vDw0Njx44td35KSorq1q3rNM3Dw0NBQUFKSUmpcL2TJ09WRkaGYzh+/Hil5gYAAAAAoDJY9pKsSUlJevXVV7Vnzx7ZKvmwOLvdLjsXtgEAAAAAWJxl97R/8cUXOn36tMLDw+Xh4SEPDw8dO3ZMEydOVGRkpCQpNDRUp0+fdnpeUVGRzp49q9DQUBNSAwAAAABQeSy7p/3+++9XTEyM07TY2Fjdf//9GjZsmCQpOjpa6enpSkpKUrt27SRJmzZtUklJiTp16nTVMwMAAAAAUJlMLe1ZWVk6fPiw4/GRI0e0d+9eBQUFKTw8XLVr13Za3tPTU6GhoWratKkkqXnz5urVq5ceeughLVmyRIWFhRo9erQGDRpU4ZXjAQAAAACoLkw9PH737t1q27at2rZtK0maMGGC2rZtq+eee+6i1/Hee++pWbNm6tGjh3r37q0uXbpo6dKlVRUZAAAAAICrxtQ97V27dpVhGBe9/NGjR8tMCwoK0ooVKyoxFQAAMJW7u/SnP/06DgCAC7PsOe0AAMBFeXtLK1eanQIAAEuw7NXjAQAAAABwdZR2AAAAAAAsitIOAACsJTtbstlKh+xss9MAAGAqSjsAAAAAABZFaQcAAAAAwKIo7QAAAAAAWBSlHQAAAAAAi6K0AwAAAABgUZR2AAAAAAAsysPsAAAAAE7c3aXevX8dBwDAhVHaAQCAtXh7S2vXmp0CAABL4PB4AAAAAAAsitIOAAAAAIBFUdoBAIC1ZGdLfn6lQ3a22WkAADAV57QDAADryckxOwEAAJbAnnYAAAAAACyK0g4AAAAAgEVR2gEAAAAAsChKOwAAAAAAFkVpBwAAAADAorh6PAAAsBY3N+m2234dBwDAhVHaAQCAtfj4SFu2mJ0CAABL4OtrAAAAAAAsitIOAAAAAIBFUdoBAIC1ZGdLdeqUDtnZZqcBAMBUnNMOAACsJy3N7AQAAFgCe9oBAAAAALAoSjsAAAAAABZFaQcAAAAAwKIo7QAAAAAAWBSlHQAAAAAAi+Lq8QAAwFrc3KT27X8dBwDAhVHaAQCAtfj4SLt2mZ0CAABL4OtrAAAAAAAsitIOAAAAAIBFUdoBAIC15ORIkZGlQ06O2WkAADAV57QDAABrMQzp2LFfxwEAcGHsaQcAAAAAwKIo7QAAAAAAWJSppX3btm3q16+fwsLCZLPZtGrVKse8wsJCTZo0Sa1atZKfn5/CwsL0wAMP6OTJk07rOHv2rAYPHix/f38FBgZqxIgRysrKusrvBAAAAACAymdqac/Ozlbr1q21ePHiMvNycnK0Z88eTZkyRXv27NG//vUvHTx4ULfffrvTcoMHD9Z3332njRs3as2aNdq2bZtGjhx5td4CAAAAAABVxtQL0cXFxSkuLq7ceQEBAdq4caPTtNdee00dO3ZUcnKywsPDtX//fq1fv167du1S+/btJUmLFi1S7969NXfuXIWFhVX5ewAAAAAAoKpUq3PaMzIyZLPZFBgYKElKTExUYGCgo7BLUkxMjNzc3LRz584K15Ofn6/MzEynAQAAWITNJrVoUTrYbGanAQDAVNWmtOfl5WnSpEm699575e/vL0lKSUlR3bp1nZbz8PBQUFCQUlJSKlzXrFmzFBAQ4BgaNmxYpdkBAMAl8PWVvvuudPD1NTsNAACmqhalvbCwUHfffbcMw9Abb7xxxeubPHmyMjIyHMPx48crISUAAAAAAJXL1HPaL8a5wn7s2DFt2rTJsZddkkJDQ3X69Gmn5YuKinT27FmFhoZWuE673S673V5lmQEAAAAAqAyW3tN+rrAfOnRIn3/+uWrXru00Pzo6Wunp6UpKSnJM27Rpk0pKStSpU6erHRcAAFSGnBypZcvSISfH7DQAAJjK1D3tWVlZOnz4sOPxkSNHtHfvXgUFBalevXr605/+pD179mjNmjUqLi52nKceFBQkLy8vNW/eXL169dJDDz2kJUuWqLCwUKNHj9agQYO4cjwAANWVYUjff//rOAAALszU0r57925169bN8XjChAmSpCFDhmjatGlavXq1JKlNmzZOz9u8ebO6du0qSXrvvfc0evRo9ejRQ25ubho4cKAWLlx4VfIDAAAAAFCVTC3tXbt2lXGeb9DPN++coKAgrVixojJjAQAAAABgCZY+px0AAAAAAFdGaQcAAAAAwKIo7QAAAAAAWJTl79MOAABcjM0mRUT8Og4AgAujtAMAAGvx9ZWOHjU7BQAAlsDh8QAAAAAAWBSlHQAAAAAAi6K0AwAAa8nNlTp0KB1yc81OAwCAqTinHQAAWEtJibR796/jAAC4MPa0AwAAAABgUZR2AAAAAAAsitIOAAAAAIBFUdoBAAAAALAoSjsAAAAAABbF1eMBAID1BAebnQAAAEugtAMAAGvx85POnDE7BQAAlsDh8QAAAAAAWBSlHQAAAAAAi6K0AwAAa8nNlbp2LR1yc81OAwCAqTinHQAAWEtJibR166/jAAC4MPa0AwAAAABgUZR2AAAAAAAsitIOAAAAAIBFUdoBAAAAALAoSjsAAAAAABbF1eMBAID1+PqanQAAAEugtAMAAGvx85Oys81OAQCAJXB4PAAAAAAAFkVpBwAAAADAoijtAADAWvLypD59Soe8PLPTAABgKs5pBwAA1lJcLK1b9+s4AAAujD3tAAAAAABYFKUdAAAAAACLorQDAAAAAGBRlHYAAAAAACyK0g4AAAAAgEVR2gEAAAAAsChu+QYAAKzFz08yDLNTAABgCexpBwAAAADAoijtAAAAAABYFKUdAABYS16edNddpUNentlpAAAwlamlfdu2berXr5/CwsJks9m0atUqp/mGYei5555TvXr15OPjo5iYGB06dMhpmbNnz2rw4MHy9/dXYGCgRowYoaysrKv4LgAAQKUqLpY+/LB0KC42Ow0AAKYytbRnZ2erdevWWrx4cbnzZ8+erYULF2rJkiXauXOn/Pz8FBsbq7zffOs+ePBgfffdd9q4caPWrFmjbdu2aeTIkVfrLQAAAAAAUGVMvXp8XFyc4uLiyp1nGIYWLFigZ599VnfccYck6W9/+5tCQkK0atUqDRo0SPv379f69eu1a9cutW/fXpK0aNEi9e7dW3PnzlVYWFi5687Pz1d+fr7jcWZmZiW/MwAAAAAArpxlz2k/cuSIUlJSFBMT45gWEBCgTp06KTExUZKUmJiowMBAR2GXpJiYGLm5uWnnzp0VrnvWrFkKCAhwDA0bNqy6NwIAAAAAwGWybGlPSUmRJIWEhDhNDwkJccxLSUlR3bp1neZ7eHgoKCjIsUx5Jk+erIyMDMdw/PjxSk4PAAAAAMCVM/XweLPY7XbZ7XazYwAAAAAAcF6W3dMeGhoqSUpNTXWanpqa6pgXGhqq06dPO80vKirS2bNnHcsAAAAAAFBdWba0R0VFKTQ0VAkJCY5pmZmZ2rlzp6KjoyVJ0dHRSk9PV1JSkmOZTZs2qaSkRJ06dbrqmQEAQCXw9ZWyskoHX1+z0wAAYCpTD4/PysrS4cOHHY+PHDmivXv3KigoSOHh4Ro3bpxeeOEFNWnSRFFRUZoyZYrCwsLUv39/SVLz5s3Vq1cvPfTQQ1qyZIkKCws1evRoDRo0qMIrxwMAAIuz2SQ/P7NTAABgCaaW9t27d6tbt26OxxMmTJAkDRkyRMuWLdOTTz6p7OxsjRw5Uunp6erSpYvWr18vb29vx3Pee+89jR49Wj169JCbm5sGDhyohQsXXvX3AgAAAABAZTO1tHft2lWGYVQ432azacaMGZoxY0aFywQFBWnFihVVEQ8AAJghP196+OHS8TfflLh4LADAhVn2nHYAAOCiioqk5ctLh6Iis9MAAGCqyyrt1113nX766acy09PT03XdddddcSgAAAAAAHCZpf3o0aMqLi4uMz0/P18nTpy44lAAAAAAAOASz2lfvXq1Y3zDhg0KCAhwPC4uLlZCQoIiIyMrLRwAAAAAAK7skkr7uVut2Ww2DRkyxGmep6enIiMj9corr1RaOAAAAAAAXNkllfaSkhJJUlRUlHbt2qXg4OAqCQUAAAAAAC7zlm9Hjhyp7BwAAAAAAOB3Lvs+7QkJCUpISNDp06cde+DPeeedd644GAAAcFG+vtLp07+OAwDgwi6rtE+fPl0zZsxQ+/btVa9ePdlstsrOBQAAXJXNJtWpY3YKAAAs4bJK+5IlS7Rs2TLdf//9lZ0HAAAAAAD8v8u6T3tBQYFuueWWys4CAAAg5edLo0aVDvn5ZqcBAMBUl1XaH3zwQa1YsaKyswAAAEhFRdLrr5cORUVmpwEAwFSXdXh8Xl6eli5dqs8//1w33nijPD09nebPmzevUsIBAAAAAODKLqu0f/PNN2rTpo0kad++fU7zuCgdAAAAAACV47JK++bNmys7BwAAAAAA+J3LOqcdAAAAAABUvcva096tW7fzHga/adOmyw4EAAAAAABKXVZpP3c++zmFhYXau3ev9u3bpyFDhlRGLgAAqkRycrLS0tLMjnFB+/fvNzsCAACwgMsq7fPnzy93+rRp05SVlXVFgQAAqCrJyclq1qy5cnNzzI5y0QrzC8yOcPX5+EhHjvw6DgCAC7us0l6R++67Tx07dtTcuXMrc7UAAFSKtLQ05ebmqNPwqfKvF2l2nPM69W2i9q1eqiJXvE+5m5sUGWl2CgAALKFSS3tiYqK8vb0rc5UAAFQ6/3qRCgpvanaM88o8ddTsCAAAwAIuq7QPGDDA6bFhGDp16pR2796tKVOmVEowAADgogoKpGeeKR2fOVPy8jI3DwAAJrqs0h4QEOD02M3NTU2bNtWMGTPUs2fPSgkGAABcVGGhdO5Uu2nTKO0AAJd2WaU9Pj6+snMAAAAAAIDfuaJz2pOSkhy3pGnZsqXatm1bKaEAAAAAAMBllvbTp09r0KBB2rJliwIDAyVJ6enp6tatm95//33VqVOnMjMCAAAAAOCS3C7nSWPGjNEvv/yi7777TmfPntXZs2e1b98+ZWZmauzYsZWdEQAAAAAAl3RZe9rXr1+vzz//XM2bN3dMa9GihRYvXsyF6AAAAAAAqCSXtae9pKREnp6eZaZ7enqqpKTkikMBAAAAAIDLLO3du3fXY489ppMnTzqmnThxQuPHj1ePHj0qLRwAAHBBPj7Svn2lg4+P2WkAADDVZZX21157TZmZmYqMjFSjRo3UqFEjRUVFKTMzU4sWLarsjAAAwJW4uUktW5YObpf1pwoAANeMyzqnvWHDhtqzZ48+//xzHThwQJLUvHlzxcTEVGo4AAAAAABc2SV9fb1p0ya1aNFCmZmZstls+uMf/6gxY8ZozJgx6tChg1q2bKkvvviiqrICAABXUFAgTZtWOhQUmJ0GAABTXVJpX7BggR566CH5+/uXmRcQEKCHH35Y8+bNq7RwAADABRUWStOnlw6FhWanAQDAVJdU2r/++mv16tWrwvk9e/ZUUlLSFYcCAAAAAACXWNpTU1PLvdXbOR4eHjpz5swVhwIAAAAAAJdY2uvXr699+/ZVOP+bb75RvXr1rjgUAAAAAAC4xNLeu3dvTZkyRXl5eWXm5ebmaurUqerbt2+lhQMAAAAAwJVd0i3fnn32Wf3rX//S9ddfr9GjR6tp06aSpAMHDmjx4sUqLi7WM888UyVBAQAAAABwNZdU2kNCQrR9+3Y98sgjmjx5sgzDkCTZbDbFxsZq8eLFCgkJqZKgAAAAAAC4mks6PF6SIiIitG7dOqWlpWnnzp3asWOH0tLStG7dOkVFRVVquOLiYk2ZMkVRUVHy8fFRo0aN9Pzzzzu+LJAkwzD03HPPqV69evLx8VFMTIwOHTpUqTkAAMBV5O0t/fvfpYO3t9lpAAAw1SXtaf+tWrVqqUOHDpWZpYyXX35Zb7zxhpYvX66WLVtq9+7dGjZsmAICAjR27FhJ0uzZs7Vw4UItX75cUVFRmjJlimJjY/X999/Lm1/0AABUP+7uUhX/jQEAQHVx2aX9ati+fbvuuOMO9enTR5IUGRmpf/zjH/r3v/8tqXQv+4IFC/Tss8/qjjvukCT97W9/U0hIiFatWqVBgwaZlh0AAAAAgCt1yYfHX0233HKLEhIS9MMPP0iSvv76a3355ZeKi4uTJB05ckQpKSmKiYlxPCcgIECdOnVSYmJihevNz89XZmam0wAAACyioECaM6d0KCgwOw0AAKay9J72p556SpmZmWrWrJnc3d1VXFysmTNnavDgwZKklJQUSSpz8buQkBDHvPLMmjVL06dPr7rgAADg8hUWSk8+WTr+6KOSl5e5eQAAMJGl97R/8MEHeu+997RixQrt2bNHy5cv19y5c7V8+fIrWu/kyZOVkZHhGI4fP15JiQEAAAAAqDyW3tP+xBNP6KmnnnKcm96qVSsdO3ZMs2bN0pAhQxQaGipJSk1NVb169RzPS01NVZs2bSpcr91ul91ur9LsAAAAAABcKUvvac/JyZGbm3NEd3d3lZSUSJKioqIUGhqqhIQEx/zMzEzt3LlT0dHRVzUrAAAAAACVzdJ72vv166eZM2cqPDxcLVu21P/+7/9q3rx5Gj58uCTJZrNp3LhxeuGFF9SkSRPHLd/CwsLUv39/c8MDAAAAAHCFLF3aFy1apClTpujRRx/V6dOnFRYWpocffljPPfecY5knn3xS2dnZGjlypNLT09WlSxetX7+ee7QDAAAAAKo9S5f2mjVrasGCBVqwYEGFy9hsNs2YMUMzZsy4esEAAAAAALgKLF3aAQCAC/L2ljZv/nUcAAAXRmkHAADW4u4ude1qdgoAACzB0lePBwAAAADAlbGnHQAAWEthobR0aen4yJGSp6e5eQAAMBGlHQAAWEtBgTR6dOn40KGUdgCAS+PweAAAAAAALIrSDgAAAACARVHaAQAAAACwKEo7AAAAAAAWRWkHAAAAAMCiKO0AAAAAAFgUt3wDAADWYrdLa9b8Og4AgAujtAMAAGvx8JD69DE7BQAAlsDh8QAAAAAAWBR72gEAgLUUFkrvvVc6Pniw5Olpbh4AAExEaQcAANZSUCANG1Y6ftddlHYAgEvj8HgAAAAAACyK0g4AAAAAgEVR2gEAAAAAsChKOwAAAAAAFkVpBwAAAADAoijtAAAAAABYFLd8AwAA1mK3Sx988Os4AAAujNIOAACsxcOj9P7sAACAw+MBAAAAALAq9rQDAABrKSqSPvqodPzOO0v3vAMA4KL4LQgAAKwlP1+6++7S8awsSjsAwKVxeDwAAAAAABZFaQcAAAAAwKIo7QAAAAAAWBSlHQAAAAAAi6K0AwAAAABgUZR2AAAAAAAsinuoAACAK7Z///7KW1lhoYKmTpUknd23T/L0rJTVBgcHKzw8vFLWBQDA1UJpBwAAly034ydJNt13331V8wLTp1faqnx8fHXgwH6KOwCgWqG0AwCAy1aY84skQ23+PEl1opqZHadCmaeOauc705WWlkZpBwBUK5R2AABwxWrUDVdQeNNKWZdbcZFaf79TkvR1i04qcefPFQCA6+K3IAAAsBTPokJNeu0JSdKQhQnKp7QDAFwYV48HAAAAAMCiKO0AAAAAAFiU5Uv7iRMndN9996l27dry8fFRq1attHv3bsd8wzD03HPPqV69evLx8VFMTIwOHTpkYmIAAAAAACqHpUv7zz//rM6dO8vT01Offvqpvv/+e73yyiuqVauWY5nZs2dr4cKFWrJkiXbu3Ck/Pz/FxsYqLy/PxOQAAAAAAFw5S1/Z5eWXX1bDhg0VHx/vmBYVFeUYNwxDCxYs0LPPPqs77rhDkvS3v/1NISEhWrVqlQYNGnTVMwMAAAAAUFksvad99erVat++ve666y7VrVtXbdu21VtvveWYf+TIEaWkpCgmJsYxLSAgQJ06dVJiYmKF683Pz1dmZqbTAAAAAACA1Vi6tP/nP//RG2+8oSZNmmjDhg165JFHNHbsWC1fvlySlJKSIkkKCQlxel5ISIhjXnlmzZqlgIAAx9CwYcOqexMAAOCSFHl46p1BE/XOoIkq8vA0Ow4AAKay9OHxJSUlat++vV588UVJUtu2bbVv3z4tWbJEQ4YMuez1Tp48WRMmTHA8zszMpLgDAGARxe4e+qzbQLNjAABgCZbe016vXj21aNHCaVrz5s2VnJwsSQoNDZUkpaamOi2TmprqmFceu90uf39/pwEAAAAAAKuxdGnv3LmzDh486DTthx9+UEREhKTSi9KFhoYqISHBMT8zM1M7d+5UdHT0Vc0KAAAqh62kWC0O7lGLg3tkKyk2Ow4AAKay9OHx48eP1y233KIXX3xRd999t/79739r6dKlWrp0qSTJZrNp3LhxeuGFF9SkSRNFRUVpypQpCgsLU//+/c0NDwAALotXYYGemzdakjRkYYLy7T4mJwIAwDyWLu0dOnTQRx99pMmTJ2vGjBmKiorSggULNHjwYMcyTz75pLKzszVy5Eilp6erS5cuWr9+vby9vU1MDgAAAADAlbN0aZekvn37qm/fvhXOt9lsmjFjhmbMmHEVUwEAAAAAUPUsfU47AAAAAACujNIOAAAAAIBFUdoBAAAAALAoSjsAAAAAABZl+QvRAQAA11Lk7qG/DxjlGAcAwJXxmxAAAFhKsYen1sQOvvCCAAC4AA6PBwAAAADAotjTDgAALMVWUqyo5IOSpCPhTWW4uZucCAAA81DaAQCApXgVFujFWQ9KkoYsTFC+3cfkRAAAmIfD4wEAAAAAsChKOwAAAAAAFkVpBwAAAADAoijtAAAAAABYFKUdAAAAAACLorQDAAAAAGBR3PINAABYSpG7hz7sO9wxDgCAK+M3IQAAsJRiD0992O9Bs2MAAGAJHB4PAAAAAIBFsacdAABYiq2kRPVTjkqSToRGynBjHwMAwHVR2gEAgKV4FeZr7vT7JElDFiYo3+5jciIAAMzDV9cAAAAAAFgUpR0AAAAAAIuitAMAAAAAYFGUdgAAAAAALIrSDgAAAACARVHaAQAAAACwKG75BgAALKXI3UOf/PHPjnEAAFwZvwkBAIClFHt46r0/jTY7BgAAlsDh8QAAAAAAWBR72gEAgKXYSkoUfDZVkpQWFCLDjX0MAADXRWkHAACW4lWYr0XPDJQkDVmYoHy7j8mJAAAwD19dAwAAAABgUZR2AAAAAAAsitIOAAAAAIBFUdoBAAAAALAoSjsAAAAAABZFaQcAAAAAwKK45RsAALCUYjd3bbhtgGMcAABXRmkHAACWUuTppfg/P252DAAALIHD4wEAAAAAsCj2tAMAAGsxDNXMSpck/VIjULLZTI0DAICZqtWe9pdeekk2m03jxo1zTMvLy9OoUaNUu3Zt1ahRQwMHDlRqaqp5IQEAwBWxF+Tprcf76K3H+8hekGd2HAAATFVtSvuuXbv05ptv6sYbb3SaPn78eH3yySdauXKltm7dqpMnT2rAgAEmpQQAAAAAoPJUi9KelZWlwYMH66233lKtWrUc0zMyMvTXv/5V8+bNU/fu3dWuXTvFx8dr+/bt2rFjh4mJAQAAAAC4ctWitI8aNUp9+vRRTEyM0/SkpCQVFhY6TW/WrJnCw8OVmJhY4fry8/OVmZnpNAAAAAAAYDWWvxDd+++/rz179mjXrl1l5qWkpMjLy0uBgYFO00NCQpSSklLhOmfNmqXp06dXdlQAAAAAACqVpfe0Hz9+XI899pjee+89eXt7V9p6J0+erIyMDMdw/PjxSls3AAAAAACVxdKlPSkpSadPn9ZNN90kDw8PeXh4aOvWrVq4cKE8PDwUEhKigoICpaenOz0vNTVVoaGhFa7XbrfL39/faQAAAAAAwGosfXh8jx499O233zpNGzZsmJo1a6ZJkyapYcOG8vT0VEJCggYOHChJOnjwoJKTkxUdHW1GZAAAcIWK3dy1Nbq3YxwAAFdm6dJes2ZN3XDDDU7T/Pz8VLt2bcf0ESNGaMKECQoKCpK/v7/GjBmj6Oho3XzzzWZEBgAAV6jI00tvDH3W7BgAAFiCpUv7xZg/f77c3Nw0cOBA5efnKzY2Vq+//rrZsQAAAAAAuGLVrrRv2bLF6bG3t7cWL16sxYsXmxMIAABULsOQvSBPkpTv5S3ZbCYHAgDAPJa+EB0AAHA99oI8LR/bQ8vH9nCUdwAAXBWlHQAAAAAAi6K0AwAAAABgUZR2AAAAAAAsitIOAAAAAIBFUdoBAAAAALAoSjsAAAAAABZV7e7TDgAArm0lbm7acVM3xzgAAK6M0g4AACyl0NOuBQ/PNDsGAACWwNfXAAAAAABYFKUdAAAAAACLorQDAABLsefn6v2Hb9H7D98ie36u2XEAADAVpR0AAAAAAIuitAMAAAAAYFGUdgAAAAAALIrSDgAAAACARVHaAQAAAACwKEo7AAAAAAAW5WF2AAAAgN8qcXPTnhuiHeMAALgySjsAALCUQk+7Zo95xewYAABYAl9fAwAAAABgUZR2AAAAAAAsitIOAAAsxZ6fq2VjumvZmO6y5+eaHQcAAFNxTjsAALAc74I8syMAAGAJ7GkHAAAAAMCi2NMOAKgUycnJSktLMzvGee3fv9/sCAAAAJeE0g4AuGLJyclq1qy5cnNzzI5yUQrzC8yOAAAAcFEo7QCAK5aWlqbc3Bx1Gj5V/vUizY5ToVPfJmrf6qUqKioyOwoAAMBFobQDACqNf71IBYU3NTtGhTJPHTU7AgAAwCWhtAMAAEspsbnp++vbOsYBAHBllHYAAGAphV52zZi42OwYAABYAl9fAwAAAABgUZR2AAAAAAAsitIOAAAsxZ6fq6UTe2vpxN6y5+eaHQcAAFNxTjsAALAc/6x0syMAAGAJ7GkHAAAAAMCiKO0AAAAAAFgUpR0AAAAAAIuitAMAAAAAYFGUdgAAAAAALMrypX3WrFnq0KGDatasqbp166p///46ePCg0zJ5eXkaNWqUateurRo1amjgwIFKTU01KTEAALgSJTY3/RjRTD9GNFOJzfJ/qgAAUKUsf8u3rVu3atSoUerQoYOKior09NNPq2fPnvr+++/l5+cnSRo/frzWrl2rlStXKiAgQKNHj9aAAQP01VdfmZweAK5ccnKy0tLSzI5xXvv37zc7Aq4hhV52PfP0O2bHAADAEixf2tevX+/0eNmyZapbt66SkpL0hz/8QRkZGfrrX/+qFStWqHv37pKk+Ph4NW/eXDt27NDNN99sRmwAqBTJyclq1qy5cnNzzI5yUQrzC8yOAAAAcE2xfGn/vYyMDElSUFCQJCkpKUmFhYWKiYlxLNOsWTOFh4crMTGx3NKen5+v/Px8x+PMzMwqTg0AlyctLU25uTnqNHyq/OtFmh2nQqe+TdS+1UtVVFRkdhQAAIBrSrUq7SUlJRo3bpw6d+6sG264QZKUkpIiLy8vBQYGOi0bEhKilJSUctcza9YsTZ8+varjAkCl8a8XqaDwpmbHqFDmqaNmR8A1xKsgT69M+7MkaeK0FSrw8jY5EQAA5qlWV3cZNWqU9u3bp/fff/+K1jN58mRlZGQ4huPHj1dSQgAAcKVshqE6P6Wozk8pshmG2XEAADBVtdnTPnr0aK1Zs0bbtm1TgwYNHNNDQ0NVUFCg9PR0p73tqampCg0NLXdddrtddru9qiMDAAAAAHBFLL+n3TAMjR49Wh999JE2bdqkqKgop/nt2rWTp6enEhISHNMOHjyo5ORkRUdHX+24AAAAAABUGsvvaR81apRWrFihjz/+WDVr1nScpx4QECAfHx8FBARoxIgRmjBhgoKCguTv768xY8YoOjqaK8cDAAAAAKo1y5f2N954Q5LUtWtXp+nx8fEaOnSoJGn+/Plyc3PTwIEDlZ+fr9jYWL3++utXOSkAAAAAAJXL8qXduIgL0Hh7e2vx4sVavHjxVUgEAAAAAMDVYfnSDgAAXIths+l4vSjHOAAArozSDgAALKXAy1tPTHvP7BgAAFiC5a8eDwAAAACAq6K0AwAAAABgUZR2AABgKV4FeZozbbDmTBssr4I8s+MAAGAqzmkHAACWYjMMNTx1xDEOAIArY087AAAAAAAWRWkHAAAAAMCiKO0AAAAAAFgUpR0AAAAAAIviQnQAAMBl7N+/3+wIFxQcHKzw8HCzYwAALILSDgAALMWw2XSmdqhjvDLkZvwkyab77ruvUtZXlXx8fHXgwH6KOwBAEqUdAABYTIGXt8a8+K9KXWdhzi+SDLX58yTViWpWqeuuTJmnjmrnO9OVlpZGaQcASKK0AwAAF1KjbriCwpuaHQMAgIvGhegAAAAAALAoSjsAALAUz4J8zXxxuGa+OFyeBflmxwEAwFQcHg8AACzFzShRo2MHHOMAALgy9rQDAAAAAGBRlHYAAAAAACyK0g4AAAAAgEVR2gEAAAAAsChKOwAAAAAAFsXV4wEAgOVk1gg0OwIAAJZAaQdQqZKTk5WWlmZ2jAsKDg5WeHi42TEAlCPf7qORr6wzO4ap9u/fb3aEi8L/SwGg6lHaAVSa5ORkNWvWXLm5OWZHuSAfH18dOLCfPzYBWEpuxk+SbLrvvvvMjnJR+H8pAFQ9SjuASpOWlqbc3Bx1Gj5V/vUizY5TocxTR7XznelKS0vjD00AllKY84skQ23+PEl1opqZHee8+H8pAFwdlHYAlc6/XqSCwpuaHQNANeVZkK/JiyZIkmaNmadCL7vJia6+GnXD+f8oAEASpR0AAFiMm1GiFj/8r2McAABXRmkH4LKqw4WeqkNGAK6tOvx/igvmAajOKO0AXE51u9CTJBXmF5gdAQCcVKf/l3LBPADVGaUdgMupThd6OvVtovatXqqioiKzowCAk+ry/1IumAeguqO0A3BZ1eFCT5mnjpodAQDOqzr8vxQAqjM3swMAAAAAAIDysacdAABYTp6Xt9kRAACwBEo7AACwlHy7j4Yu2mR2DAAALIHD4wEAAAAAsCj2tAMAAOCaVx3uJ5+fny+73W52jAvivveuKzk5WWlpaWbHuCjX0s8ppR0AAFiKZ2G+xi95WpI0/y8vqtDT+iUG1lWd7icvm00yDLNTXBD3vXdNycnJatasuXJzc8yOclGupZ9TSjsAALAUt5IS3bQv0TEOXInqcj/5U98mat/qpZbPyX3vXVdaWppyc3PUafhU+deLNDvOeV1rP6eUdlQJDp0BAABWYvX7yWeeOirJ+jnPqQ6nG0j8nVcV/OtFVouf0WsJpR2VjkNnAAAArk3V6nQD8Xcerg3XTGlfvHix5syZo5SUFLVu3VqLFi1Sx44dzY5V6arDHuz9+/dXu0NnvvjiCzVv3tzsOOdVHS5OU12+dQcAAJenupxuIF17h0jDdV0Tpf2f//ynJkyYoCVLlqhTp05asGCBYmNjdfDgQdWtW9fseJWm2u3BDgqz/KEz1erb4mpycRpJKswvMDsCAACoQtXlMH7gWnBNlPZ58+bpoYce0rBhwyRJS5Ys0dq1a/XOO+/oqaeeMjld5akuF384dyGVoqIis6NcUHX5tri6XJymOm17AAAAoDqo9qW9oKBASUlJmjx5smOam5ubYmJilJiYWO5z8vPzlZ+f73ickZEhScrMzKzasFcoKytLklRUkK+i/FyT01SsuLB0L2vGiUPy9LCZnOb8Mk8dkyQVF1aPf9PqktPq2/7cdrd6Tqn6ZCVn5asuWasip70gX+d+I58+/I3yva781CBX/vesKtUlKzkrV3XJKUmZKcmSpKSkJMff0Vbl5uamEovfLePgwYOSpLPHDlr671Hp122flZVl6Y53LptxgaNpbcaFlrC4kydPqn79+tq+fbuio6Md05988klt3bpVO3fuLPOcadOmafr06VczJgAAAAAAZRw/flwNGjSocH6139N+OSZPnqwJEyY4HpeUlOjs2bOqXbu2bDZrf2NYVTIzM9WwYUMdP35c/v7+ZsdBBdhO1sc2qh7YTtbHNqoe2E7WxzaqHthO1lcV28gwDP3yyy8KCws773LVvrQHBwfL3d1dqampTtNTU1MVGhpa7nPsdnuZq3AHBgZWVcRqxd/fn/9RVANsJ+tjG1UPbCfrYxtVD2wn62MbVQ9sJ+ur7G0UEBBwwWXcKu3VTOLl5aV27dopISHBMa2kpEQJCQlOh8sDAAAAAFDdVPs97ZI0YcIEDRkyRO3bt1fHjh21YMECZWdnO64mDwAAAABAdXRNlPZ77rlHZ86c0XPPPaeUlBS1adNG69evV0hIiNnRqg273a6pU6eWOW0A1sJ2sj62UfXAdrI+tlH1wHayPrZR9cB2sj4zt1G1v3o8AAAAAADXqmp/TjsAAAAAANcqSjsAAAAAABZFaQcAAAAAwKIo7QAAAAAAWBSl3cUdPXpUI0aMUFRUlHx8fNSoUSNNnTpVBQUFTst98803uvXWW+Xt7a2GDRtq9uzZJiV2TTNnztQtt9wiX19fBQYGlruMzWYrM7z//vtXN6iLu5jtlJycrD59+sjX11d169bVE088oaKioqsbFE4iIyPLfHZeeukls2O5vMWLFysyMlLe3t7q1KmT/v3vf5sdCf9v2rRpZT4zzZo1MzuWy9u2bZv69eunsLAw2Ww2rVq1ymm+YRh67rnnVK9ePfn4+CgmJkaHDh0yJ6yLutA2Gjp0aJnPVq9evcwJ66JmzZqlDh06qGbNmqpbt6769++vgwcPOi2Tl5enUaNGqXbt2qpRo4YGDhyo1NTUKs1FaXdxBw4cUElJid5880199913mj9/vpYsWaKnn37asUxmZqZ69uypiIgIJSUlac6cOZo2bZqWLl1qYnLXUlBQoLvuukuPPPLIeZeLj4/XqVOnHEP//v2vTkBIuvB2Ki4uVp8+fVRQUKDt27dr+fLlWrZsmZ577rmrnBS/N2PGDKfPzpgxY8yO5NL++c9/asKECZo6dar27Nmj1q1bKzY2VqdPnzY7Gv5fy5YtnT4zX375pdmRXF52drZat26txYsXlzt/9uzZWrhwoZYsWaKdO3fKz89PsbGxysvLu8pJXdeFtpEk9erVy+mz9Y9//OMqJsTWrVs1atQo7dixQxs3blRhYaF69uyp7OxsxzLjx4/XJ598opUrV2rr1q06efKkBgwYULXBDOB3Zs+ebURFRTkev/7660atWrWM/Px8x7RJkyYZTZs2NSOeS4uPjzcCAgLKnSfJ+Oijj65qHpSvou20bt06w83NzUhJSXFMe+ONNwx/f3+nzxeuroiICGP+/Plmx8BvdOzY0Rg1apTjcXFxsREWFmbMmjXLxFQ4Z+rUqUbr1q3NjoHz+P3fBCUlJUZoaKgxZ84cx7T09HTDbrcb//jHP0xIiPL+bhsyZIhxxx13mJIH5Tt9+rQhydi6dathGKWfG09PT2PlypWOZfbv329IMhITE6ssB3vaUUZGRoaCgoIcjxMTE/WHP/xBXl5ejmmxsbE6ePCgfv75ZzMiogKjRo1ScHCwOnbsqHfeeUeGYZgdCb+RmJioVq1aKSQkxDEtNjZWmZmZ+u6770xMhpdeekm1a9dW27ZtNWfOHE5ZMFFBQYGSkpIUExPjmObm5qaYmBglJiaamAy/dejQIYWFhem6667T4MGDlZycbHYknMeRI0eUkpLi9LkKCAhQp06d+FxZzJYtW1S3bl01bdpUjzzyiH766SezI7m0jIwMSXJ0o6SkJBUWFjp9lpo1a6bw8PAq/Sx5VNmaUS0dPnxYixYt0ty5cx3TUlJSFBUV5bTcudKRkpKiWrVqXdWMKN+MGTPUvXt3+fr66rPPPtOjjz6qrKwsjR071uxo+H8pKSlOhV1y/izBHGPHjtVNN92koKAgbd++XZMnT9apU6c0b948s6O5pLS0NBUXF5f7WTlw4IBJqfBbnTp10rJly9S0aVOdOnVK06dP16233qp9+/apZs2aZsdDOc79jinvc8XvH+vo1auXBgwYoKioKP344496+umnFRcXp8TERLm7u5sdz+WUlJRo3Lhx6ty5s2644QZJpZ8lLy+vMtcuqurPEnvar1FPPfVUuRcm++3w+z9+Tpw4oV69eumuu+7SQw89ZFJy13E52+h8pkyZos6dO6tt27aaNGmSnnzySc2ZM6cK34FrqOzthKvjUrbbhAkT1LVrV9144436y1/+oldeeUWLFi1Sfn6+ye8CsKa4uDjddddduvHGGxUbG6t169YpPT1dH3zwgdnRgGpt0KBBuv3229WqVSv1799fa9as0a5du7Rlyxazo7mkUaNGad++fZa4sDN72q9REydO1NChQ8+7zHXXXecYP3nypLp166ZbbrmlzAXmQkNDy1wR8dzj0NDQygnsgi51G12qTp066fnnn1d+fr7sdvtlr8fVVeZ2Cg0NLXMFbD5LVeNKtlunTp1UVFSko0ePqmnTplWQDucTHBwsd3f3cn/v8DmxpsDAQF1//fU6fPiw2VFQgXOfndTUVNWrV88xPTU1VW3atDEpFS7kuuuuU3BwsA4fPqwePXqYHceljB49WmvWrNG2bdvUoEEDx/TQ0FAVFBQoPT3daW97Vf+OorRfo+rUqaM6depc1LInTpxQt27d1K5dO8XHx8vNzfkAjOjoaD3zzDMqLCyUp6enJGnjxo1q2rQph8ZfgUvZRpdj7969qlWrFoX9ClXmdoqOjtbMmTN1+vRp1a1bV1LpZ8nf318tWrSolNdAqSvZbnv37pWbm5tjG+Hq8vLyUrt27ZSQkOC4A0ZJSYkSEhI0evRoc8OhXFlZWfrxxx91//33mx0FFYiKilJoaKgSEhIcJT0zM1M7d+684J1pYJ7//ve/+umnn5y+aEHVMgxDY8aM0UcffaQtW7aUOUW4Xbt28vT0VEJCggYOHChJOnjwoJKTkxUdHV1luSjtLu7EiRPq2rWrIiIiNHfuXJ05c8Yx79y3RX/+8581ffp0jRgxQpMmTdK+ffv06quvav78+WbFdjnJyck6e/askpOTVVxcrL1790qSGjdurBo1auiTTz5Ramqqbr75Znl7e2vjxo168cUX9fjjj5sb3MVcaDv17NlTLVq00P3336/Zs2crJSVFzz77rEaNGsWXKyZJTEzUzp071a1bN9WsWVOJiYkaP3687rvvPr6UNNGECRM0ZMgQtW/fXh07dtSCBQuUnZ2tYcOGmR0Nkh5//HH169dPEREROnnypKZOnSp3d3fde++9ZkdzaVlZWU5HOxw5ckR79+5VUFCQwsPDNW7cOL3wwgtq0qSJoqKiNGXKFIWFhXF72KvofNsoKChI06dP18CBAxUaGqoff/xRTz75pBo3bqzY2FgTU7uWUaNGacWKFfr4449Vs2ZNx3nqAQEB8vHxUUBAgEaMGKEJEyYoKChI/v7+GjNmjKKjo3XzzTdXXbAquy49qoX4+HhDUrnDb3399ddGly5dDLvdbtSvX9946aWXTErsmoYMGVLuNtq8ebNhGIbx6aefGm3atDFq1Khh+Pn5Ga1btzaWLFliFBcXmxvcxVxoOxmGYRw9etSIi4szfHx8jODgYGPixIlGYWGheaFdXFJSktGpUycjICDA8Pb2Npo3b268+OKLRl5entnRXN6iRYuM8PBww8vLy+jYsaOxY8cOsyPh/91zzz1GvXr1DC8vL6N+/frGPffcYxw+fNjsWC5v8+bN5f4OGjJkiGEYpbd9mzJlihESEmLY7XajR48exsGDB80N7WLOt41ycnKMnj17GnXq1DE8PT2NiIgI46GHHnK6TSyqXkW9KD4+3rFMbm6u8eijjxq1atUyfH19jTvvvNM4depUleay/X84AAAAAABgMVw9HgAAAAAAi6K0AwAAAABgUZR2AAAAAAAsitIOAAAAAIBFUdoBAAAAALAoSjsAAAAAABZFaQcAAAAAwKIo7QAAAAAAWBSlHQAAFzJt2jSFhITIZrNp1apVGjp0qPr37292LAAAUAFKOwAAFjN06FDZbDbZbDZ5eXmpcePGmjFjhoqKiq5ovfv379f06dP15ptv6tSpU4qLi9Orr76qZcuWOZbp2rWrxo0bV+7zjx07Jh8fH2VlZWnatGlq06bNFeWpLBMmTFBQUJAaNmyo9957z2neypUr1a9fP5OSAQBw5TzMDgAAAMrq1auX4uPjlZ+fr3Xr1mnUqFHy9PTU5MmTyyxbUFAgLy+vC67zxx9/lCTdcccdstlskiS73X7RmT7++GN169ZNNWrUuOjnVLVPPvlEK1as0GeffaZDhw5p+PDhio2NVXBwsDIyMvTMM8/o888/NzsmAACXjT3tAABYkN1uV2hoqCIiIvTII48oJiZGq1evliTHIe0zZ85UWFiYmjZtKkn69ttv1b17d/n4+Kh27doaOXKksrKyJJUeFn9uj7Obm5ujtP/28PihQ4dq69atevXVVx17+o8ePerI9PHHH+v222+/qPznyyJJW7ZsUceOHeXn56fAwEB17txZx44dkyR9/fXX6tatm2rWrCl/f3+1a9dOu3fvLvd19u/fr65du6p9+/a699575e/vryNHjkiSnnzyST3yyCMKDw+/qMwAAFgRpR0AgGrAx8dHBQUFjscJCQk6ePCgNm7cqDVr1ig7O1uxsbGqVauWdu3apZUrV+rzzz/X6NGjJUmPP/644uPjJUmnTp3SqVOnyrzGq6++qujoaD300EOOZRo2bChJSk9P15dffnlRpf1CWYqKitS/f3/ddttt+uabb5SYmKiRI0c6vkgYPHiwGjRooF27dikpKUlPPfWUPD09y32t1q1ba/fu3fr555+VlJSk3NxcNW7cWF9++aX27NmjsWPHXsK/MgAA1sPh8QAAWJhhGEpISNCGDRs0ZswYx3Q/Pz+9/fbbjsPi33rrLeXl5elvf/ub/Pz8JEmvvfaa+vXrp5dfflkhISEKDAyUJIWGhpb7WgEBAfLy8pKvr2+ZZdatW6cbb7xRYWFhF8y8YsWK82bx9PRURkaG+vbtq0aNGkmSmjdv7nh+cnKynnjiCTVr1kyS1KRJkwpfKzY2Vvfdd586dOggHx8fLV++XH5+fnrkkUe0bNkyvfHGG1q0aJGCg4O1dOlStWzZ8oL5AQCwEva0AwBgQWvWrFGNGjXk7e2tuLg43XPPPZo2bZpjfqtWrZzOY9+/f79at27tKMmS1LlzZ5WUlOjgwYNXnOdSDo2/UJagoCANHTpUsbGx6tevn1599VWnPf8TJkzQgw8+qJiYGL300kuOc/ErMm3aNB0+fFjffvut7rzzTs2aNUsxMTHy9PTUCy+8oC+//FIPPvigHnjggct78wAAmIjSDgCABXXr1k179+7VoUOHlJub69iDfM5vx6taQUGB1q9ff9Gl/WLEx8crMTFRt9xyi/75z3/q+uuv144dOySVlvDvvvtOffr00aZNm9SiRQt99NFHF7XeAwcO6O9//7uef/55bdmyRX/4wx9Up04d3X333dqzZ49++eWXSnsPAABcDZR2AAAsyM/PT40bN1Z4eLg8PC58Nlvz5s319ddfKzs72zHtq6++kpubm+NCdRfDy8tLxcXFTtO2bNmiWrVqqXXr1he1jovN0rZtW02ePFnbt2/XDTfcoBUrVjjmXX/99Ro/frw+++wzDRgwwHE+/vkYhqGHH35Y8+bNU40aNVRcXKzCwkJJcvz39+8NAACro7QDAHANGDx4sLy9vTVkyBDt27dPmzdv1pgxY3T//fcrJCTkotcTGRmpnTt36ujRo0pLS1NJSYlWr15d7l723Nxc7d2712n48ccfL5jlyJEjmjx5shITE3Xs2DHH7dqaN2+u3NxcjR49Wlu2bNGxY8f01VdfadeuXU7nvFfk7bffVp06dRxXye/cubM2bdqkHTt2aP78+WrRooXjvH4AAKoLLkQHAMA1wNfXVxs2bNBjjz2mDh06yNfXVwMHDtS8efMuaT2PP/64hgwZohYtWig3N1dHjhzR6tWr9c4775RZ9ocfflDbtm2dpvXo0UOff/75ebP4+vrqwIEDWr58uX766SfVq1dPo0aN0sMPP6yioiL99NNPeuCBB5Samqrg4GANGDBA06dPP2/u1NRUzZw5U9u3b3dM69ixoyZOnKg+ffqobt26Wr58+SX9WwAAYAU2wzAMs0MAAABr2rNnj7p3764zZ85UeNs1AABQdTg8HgAAVKioqEiLFi2isAMAYBL2tAMAAAAAYFHsaQcAAAAAwKIo7QAAAAAAWBSlHQAAAAAAi6K0AwAAAABgUZR2AAAAAAAsitIOAAAAAIBFUdoBAAAAALAoSjsAAAAAABZFaQcAAAAAwKL+D6Wo+LeWPhLRAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def simulate_trading_strategy(events_df, event_intraday_data, daily_market_data):\n", "    \"\"\"\n", "    Simulate a trading strategy for each stock event with improved position sizing.\n", "    \n", "    Position sizing:\n", "    - 2.5% risk per trade based on stop loss\n", "    - Maximum position size of $10,000\n", "    \n", "    Strategy:\n", "    Entry conditions (either one):\n", "    1. Enter short position at the open of the 6th minute (9:35 AM) if it's below the 5-min opening range low\n", "    2. Enter on the first 5-min red candle after the opening range, using the open of the next candle\n", "    \n", "    Exit conditions:\n", "    1. Stop loss at the high of the opening range (for condition 1) or high of the day (for condition 2)\n", "    2. Profit target at the 10-day moving average\n", "    3. Close position at end of day if targets aren't hit\n", "    \"\"\"\n", "    results = []\n", "    \n", "    # Initial account settings\n", "    initial_capital = 100000  # $100,000 starting capital\n", "    risk_percentage = 0.025   # 2.5% risk per trade\n", "    max_position_value = 10000  # Maximum $10,000 per position\n", "    \n", "    for event_key, intraday_data in event_intraday_data.items():\n", "        # Parse event key\n", "        parts = event_key.split('_')\n", "        ticker = parts[0]\n", "        event_date_str = parts[1]\n", "        \n", "        # Get event details from events_df\n", "        event = events_df[(events_df['ticker'] == ticker) & \n", "                          (events_df['event_date'] == event_date_str)].iloc[0]\n", "        \n", "        # Parse event date\n", "        event_date = datetime.strptime(event_date_str, '%Y-%m-%d')\n", "        event_date = eastern_tz.localize(event_date)\n", "        \n", "        # Ensure data is sorted\n", "        intraday_data = intraday_data.sort_index()\n", "        \n", "        # Define market hours\n", "        market_open = event_date.replace(hour=9, minute=30, second=0)\n", "        market_close = event_date.replace(hour=16, minute=0, second=0)\n", "        \n", "        # Check if we have data for the trading day\n", "        trading_data = intraday_data[(intraday_data.index >= market_open) & \n", "                                     (intraday_data.index <= market_close)]\n", "        \n", "        if trading_data.empty:\n", "            print(f\"No trading hours data for {event_key}\")\n", "            continue\n", "        \n", "        # Calculate cumulative dollar volume for the day\n", "        trading_data['dollar_volume'] = trading_data['close'] * trading_data['volume']\n", "        cum_dollar_volume = trading_data['dollar_volume'].sum()\n", "        \n", "        # Skip if dollar volume is less than $1 million (matching ParabolicShortStrategy)\n", "        if cum_dollar_volume < 1e6:\n", "            print(f\"Insufficient dollar volume for {event_key}: ${cum_dollar_volume:.2f}\")\n", "            continue\n", "        \n", "        # 1. Cal<PERSON> 5-min opening range (9:30-9:35 AM EST)\n", "        opening_range_end = market_open + pd.Timedelta(minutes=5)\n", "        opening_range = intraday_data[(intraday_data.index >= market_open) & \n", "                                     (intraday_data.index < opening_range_end)]\n", "        \n", "        if opening_range.empty:\n", "            print(f\"No opening range data for {event_key}\")\n", "            continue\n", "        \n", "        opening_range_low = opening_range['low'].min()\n", "        opening_range_high = opening_range['high'].max()\n", "        \n", "        # 2. Check for the 6th minute candle (9:35 AM)\n", "        sixth_minute = market_open + pd.Time<PERSON><PERSON>(minutes=5)  # This is 9:35 AM\n", "        sixth_minute_data = intraday_data[intraday_data.index == sixth_minute]\n", "        \n", "        # 3. Calculate 10-day moving average for profit target\n", "        look_back_start = event_date - pd.Timedelta(days=20)  # Extra days for weekends/holidays\n", "        daily_data = get_daily_data(ticker, look_back_start, event_date)\n", "        \n", "        if daily_data is None or len(daily_data) < 11:\n", "            print(f\"Insufficient daily data for {event_key}\")\n", "            continue\n", "        \n", "        # Calculate 10-day MA (use data up to but not including the event day)\n", "        daily_data = daily_data.sort_index()\n", "        \n", "        # Fix: Convert event_date to naive datetime for comparison with daily_data index\n", "        if daily_data.index.tz is None:\n", "            event_date_naive = event_date.replace(tzinfo=None)\n", "            event_day_filter = daily_data.index < event_date_naive\n", "        else:\n", "            if daily_data.index.tz != event_date.tzinfo:\n", "                daily_data.index = daily_data.index.tz_convert(event_date.tzinfo)\n", "            event_day_filter = daily_data.index < event_date\n", "            \n", "        ma_data = daily_data[event_day_filter]\n", "        \n", "        if len(ma_data) >= 10:\n", "            ma10 = ma_data['close'].rolling(window=10).mean().iloc[-1]\n", "        else:\n", "            print(f\"Not enough data to calculate 10-day MA for {event_key}\")\n", "            continue\n", "        \n", "        # Initialize trade variables\n", "        entry_price = None\n", "        entry_time = None\n", "        position_outcome = None\n", "        exit_price = None\n", "        exit_time = None\n", "        profit_loss_pct = None\n", "        entry_condition = None\n", "        stop_loss_level = None\n", "        shares = None\n", "        \n", "        # Check Entry Condition 1: 6th minute open below opening range low\n", "        entry_condition1_met = False\n", "        if not sixth_minute_data.empty:\n", "            sixth_minute_open = sixth_minute_data['open'].iloc[0]\n", "            entry_condition1_met = sixth_minute_open < opening_range_low\n", "            \n", "            if entry_condition1_met:\n", "                entry_price = sixth_minute_open\n", "                entry_time = sixth_minute\n", "                entry_condition = \"Opening Range Break\"\n", "                \n", "                # Use max(day_high, opening_range_high) for stop loss to match ParabolicShortStrategy\n", "                day_high_so_far = trading_data[trading_data.index <= sixth_minute]['high'].max()\n", "                stop_loss_level = max(day_high_so_far, opening_range_high)\n", "                \n", "                # Calculate position size based on risk\n", "                risk_per_share = stop_loss_level - entry_price\n", "                if risk_per_share <= 0:  # Sanity check\n", "                    print(f\"Invalid risk calculation for {event_key}: Stop {stop_loss_level}, Entry {entry_price}\")\n", "                    continue\n", "                    \n", "                # Calculate shares based on risk percentage\n", "                risk_amount = initial_capital * risk_percentage\n", "                risk_based_shares = int(risk_amount / risk_per_share)\n", "                \n", "                # Calculate shares based on max position size\n", "                max_position_shares = int(max_position_value / entry_price)\n", "                \n", "                # Take the minimum of the two calculations\n", "                shares = min(risk_based_shares, max_position_shares)\n", "        \n", "        # If Entry Condition 1 not met, check for Entry Condition 2: first red 5-min candle\n", "        if not entry_condition1_met:\n", "            # Extract 5-minute candles after opening range\n", "            five_min_candles = []\n", "            current_time = opening_range_end\n", "            \n", "            while current_time <= market_close:\n", "                candle_end = current_time + pd.Timedel<PERSON>(minutes=5)\n", "                candle_data = intraday_data[(intraday_data.index >= current_time) & \n", "                                           (intraday_data.index < candle_end)]\n", "                \n", "                if not candle_data.empty:\n", "                    candle = {\n", "                        'start_time': current_time,\n", "                        'end_time': candle_end,\n", "                        'open': candle_data['open'].iloc[0],\n", "                        'high': candle_data['high'].max(),\n", "                        'low': candle_data['low'].min(),\n", "                        'close': candle_data['close'].iloc[-1]\n", "                    }\n", "                    five_min_candles.append(candle)\n", "                \n", "                current_time = candle_end\n", "            \n", "            # Find first red candle (close < open)\n", "            first_red_candle = None\n", "            for candle in five_min_candles:\n", "                if candle['close'] < candle['open']:\n", "                    first_red_candle = candle\n", "                    break\n", "            \n", "            if first_red_candle is not None:\n", "                # Enter on the open of the next candle\n", "                next_candle_idx = five_min_candles.index(first_red_candle) + 1\n", "                \n", "                if next_candle_idx < len(five_min_candles):\n", "                    next_candle = five_min_candles[next_candle_idx]\n", "                    entry_price = next_candle['open']\n", "                    entry_time = next_candle['start_time']\n", "                    entry_condition = \"First Red Candle\"\n", "                    \n", "                    # Calculate high of the day up to entry time\n", "                    day_data_before_entry = intraday_data[(intraday_data.index >= market_open) & \n", "                                                         (intraday_data.index < entry_time)]\n", "                    day_high = day_data_before_entry['high'].max()\n", "                    stop_loss_level = max(day_high, opening_range_high)  # Match ParabolicShortStrategy\n", "                    \n", "                    # Calculate position size based on risk\n", "                    risk_per_share = stop_loss_level - entry_price\n", "                    if risk_per_share <= 0:  # Sanity check\n", "                        print(f\"Invalid risk calculation for {event_key}: Stop {stop_loss_level}, Entry {entry_price}\")\n", "                        continue\n", "                        \n", "                    # Calculate shares based on risk percentage\n", "                    risk_amount = initial_capital * risk_percentage\n", "                    risk_based_shares = int(risk_amount / risk_per_share)\n", "                    \n", "                    # Calculate shares based on max position size\n", "                    max_position_shares = int(max_position_value / entry_price)\n", "                    \n", "                    # Take the minimum of the two calculations\n", "                    shares = min(risk_based_shares, max_position_shares)\n", "        \n", "        # If we entered a trade, simulate the rest of the day\n", "        if entry_price is not None and shares is not None and shares > 0:\n", "            # Only look at data after entry\n", "            remaining_day_data = trading_data[trading_data.index > entry_time]\n", "            \n", "            for idx, row in remaining_day_data.iterrows():\n", "                current_time = idx\n", "                current_price = row['close']\n", "                current_high = row['high']\n", "                \n", "                # Check exit conditions\n", "                if position_outcome is None:\n", "                    # Check stop loss\n", "                    if row['high'] >= stop_loss_level:\n", "                        exit_price = stop_loss_level  # Assume we exit at the stop loss price\n", "                        exit_time = current_time\n", "                        position_outcome = \"Stop Loss\"\n", "                        profit_loss_pct = ((entry_price - exit_price) / entry_price) * 100\n", "                        profit_loss_dollars = (entry_price - exit_price) * shares\n", "                    \n", "                    # Check profit target (10-day MA)\n", "                    elif current_price <= ma10:\n", "                        exit_price = current_price\n", "                        exit_time = current_time\n", "                        position_outcome = \"Profit Target\"\n", "                        profit_loss_pct = ((entry_price - exit_price) / entry_price) * 100\n", "                        profit_loss_dollars = (entry_price - exit_price) * shares\n", "            \n", "            # If we entered but didn't exit, close at the end of the day\n", "            if position_outcome is None:\n", "                last_price = trading_data.iloc[-1]['close']\n", "                exit_price = last_price\n", "                exit_time = trading_data.index[-1]\n", "                position_outcome = \"EOD Close\"\n", "                profit_loss_pct = ((entry_price - exit_price) / entry_price) * 100\n", "                profit_loss_dollars = (entry_price - exit_price) * shares\n", "        \n", "        # Record results\n", "        trade_result = {\n", "            'ticker': ticker,\n", "            'event_date': event_date_str,\n", "            'consecutive_days': event['consecutive_days'],\n", "            'overall_pct_change': event['pct_change'],\n", "            'opening_range_low': opening_range_low,\n", "            'opening_range_high': opening_range_high,\n", "            'ma10': ma10,\n", "            'entry_triggered': entry_price is not None and shares is not None and shares > 0,\n", "            'entry_condition': entry_condition if (entry_price is not None and shares is not None and shares > 0) else None,\n", "            'dollar_volume': cum_dollar_volume\n", "        }\n", "        \n", "        if entry_price is not None and shares is not None and shares > 0:\n", "            trade_result.update({\n", "                'entry_price': entry_price,\n", "                'entry_time': entry_time,\n", "                'stop_loss_level': stop_loss_level,\n", "                'exit_price': exit_price,\n", "                'exit_time': exit_time,\n", "                'position_outcome': position_outcome,\n", "                'shares': shares,\n", "                'position_value': entry_price * shares,\n", "                'risk_per_share': stop_loss_level - entry_price,\n", "                'profit_loss_pct': round(profit_loss_pct, 2),\n", "                'profit_loss_dollars': round(profit_loss_dollars, 2),\n", "                'risk_reward_ratio': round(abs(entry_price - ma10) / abs(stop_loss_level - entry_price), 2)\n", "            })\n", "        \n", "        results.append(trade_result)\n", "    \n", "    # Create DataFrame from results\n", "    results_df = pd.DataFrame(results)\n", "    return results_df\n", "\n", "# Run the simulation\n", "simulation_results = simulate_trading_strategy(rapid_risers, event_intraday_data, daily_market_data)\n", "\n", "# Display results\n", "print(\"\\nTrading Strategy Simulation Results:\")\n", "print(f\"Total events analyzed: {len(simulation_results)}\")\n", "print(f\"Number of trades executed: {simulation_results['entry_triggered'].sum()}\")\n", "\n", "# Calculate summary statistics\n", "if not simulation_results.empty and 'profit_loss_pct' in simulation_results.columns:\n", "    trades_with_entry = simulation_results[simulation_results['entry_triggered'] == True]\n", "    \n", "    if not trades_with_entry.empty:\n", "        print(\"\\nPerformance Summary:\")\n", "        print(f\"Average P/L %: {trades_with_entry['profit_loss_pct'].mean():.2f}%\")\n", "        print(f\"Median P/L %: {trades_with_entry['profit_loss_pct'].median():.2f}%\")\n", "        print(f\"Win Rate: {(trades_with_entry['profit_loss_pct'] > 0).mean() * 100:.2f}%\")\n", "        \n", "        # Results by exit type\n", "        print(\"\\nResults by Exit Type:\")\n", "        exit_type_stats = trades_with_entry.groupby('position_outcome')['profit_loss_pct'].agg(\n", "            ['count', 'mean', 'median', 'min', 'max']\n", "        )\n", "        print(exit_type_stats)\n", "        \n", "        # Plot distribution of results\n", "        plt.figure(figsize=(12, 6))\n", "        sns.histplot(data=trades_with_entry, x='profit_loss_pct', bins=20)\n", "        plt.axvline(x=0, color='red', linestyle='--')\n", "        plt.title('Distribution of Trade Results (P/L %)')\n", "        plt.xlabel('Profit/Loss %')\n", "        plt.ylabel('Count')\n", "        plt.show()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== PORTFOLIO PERFORMANCE SUMMARY =====\n", "Total Events Analyzed: 430\n", "Total Trades Executed: 430 (100.00% execution rate)\n", "Win Rate: 10.70%\n", "Initial Capital: $100,000.00\n", "Final Capital: $34,021.52\n", "Total Profit: $-65,978.48\n", "Total Return: -65.98%\n", "Average Trade Size: $6,265.24\n", "Average Trade Profit: $-153.44\n"]}], "source": ["def calculate_portfolio_performance(simulation_results):\n", "    \"\"\"\n", "    Calculate portfolio performance assuming 10% per position.\n", "    \n", "    Parameters:\n", "    -----------\n", "    simulation_results : pandas.DataFrame\n", "        DataFrame containing simulation results\n", "        \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary containing portfolio performance metrics\n", "    \"\"\"\n", "    performance = {}\n", "    \n", "    # Filter for trades that were actually executed\n", "    trades = simulation_results[simulation_results['entry_triggered'] == True].copy()\n", "    \n", "    if trades.empty:\n", "        return {\"error\": \"No trades were executed\"}\n", "    \n", "    # Basic metrics\n", "    performance['total_events'] = len(simulation_results)\n", "    performance['total_trades'] = len(trades)\n", "    performance['execution_rate'] = len(trades) / len(simulation_results) * 100\n", "    \n", "    # Calculate win/loss metrics\n", "    performance['winning_trades'] = (trades['profit_loss_pct'] > 0).sum()\n", "    performance['losing_trades'] = (trades['profit_loss_pct'] <= 0).sum()\n", "    performance['win_rate'] = performance['winning_trades'] / len(trades) * 100\n", "    \n", "    # Portfolio calculation\n", "    # Starting with $100,000, each trade uses 10% of the portfolio\n", "    initial_capital = 100000\n", "    current_capital = initial_capital\n", "    trade_sizes = []\n", "    trade_profits = []\n", "    cumulative_returns = []\n", "    \n", "    # Sort trades by date\n", "    trades = trades.sort_values('event_date')\n", "    \n", "    for _, trade in trades.iterrows():\n", "        # Calculate trade size (10% of current capital)\n", "        trade_size = current_capital * 0.10\n", "        trade_sizes.append(trade_size)\n", "        \n", "        # Calculate profit/loss in dollars\n", "        profit_loss_pct = trade['profit_loss_pct'] \n", "        profit_loss = trade_size * (profit_loss_pct / 100)\n", "        trade_profits.append(profit_loss)\n", "        \n", "        # Update capital\n", "        current_capital += profit_loss\n", "        cumulative_returns.append(current_capital)\n", "    \n", "    # Calculate portfolio metrics\n", "    performance['initial_capital'] = initial_capital\n", "    performance['final_capital'] = current_capital\n", "    performance['total_profit'] = current_capital - initial_capital\n", "    performance['total_return_pct'] = (current_capital / initial_capital - 1) * 100\n", "    performance['avg_trade_size'] = np.mean(trade_sizes) if trade_sizes else 0\n", "    performance['avg_trade_profit'] = np.mean(trade_profits) if trade_profits else 0\n", "    \n", "    return performance\n", "\n", "# Calculate portfolio performance\n", "portfolio_performance = calculate_portfolio_performance(simulation_results)\n", "\n", "# Print summary (without graphs)\n", "print(\"\\n===== PORTFOLIO PERFORMANCE SUMMARY =====\")\n", "print(f\"Total Events Analyzed: {portfolio_performance.get('total_events', 0)}\")\n", "print(f\"Total Trades Executed: {portfolio_performance.get('total_trades', 0)} ({portfolio_performance.get('execution_rate', 0):.2f}% execution rate)\")\n", "print(f\"Win Rate: {portfolio_performance.get('win_rate', 0):.2f}%\")\n", "print(f\"Initial Capital: ${portfolio_performance.get('initial_capital', 0):,.2f}\")\n", "print(f\"Final Capital: ${portfolio_performance.get('final_capital', 0):,.2f}\")\n", "print(f\"Total Profit: ${portfolio_performance.get('total_profit', 0):,.2f}\")\n", "print(f\"Total Return: {portfolio_performance.get('total_return_pct', 0):.2f}%\")\n", "print(f\"Average Trade Size: ${portfolio_performance.get('avg_trade_size', 0):,.2f}\")\n", "print(f\"Average Trade Profit: ${portfolio_performance.get('avg_trade_profit', 0):,.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}