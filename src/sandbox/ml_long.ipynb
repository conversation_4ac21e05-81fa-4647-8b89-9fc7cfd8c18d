{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import numpy as np\n", "import pytz\n", "import xgboost as xgb\n", "from collections import defaultdict\n", "import pandas_market_calendars as mcal\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=FutureWarning)\n", "\n", "# ╭────────────────────────── CONFIG ──────────────────────────╮\n", "TZ                = \"US/Eastern\"\n", "TRAIN_START       = \"2020-01-01\"\n", "TRAIN_END         = \"2024-12-31\"\n", "BACKTEST_START    = \"2025-01-01\"\n", "BACKTEST_END      = \"2025-06-06\"\n", "\n", "SLOTS_LONG        = 20      # The post specifies 10 slots/positions maximum.\n", "# Note: Short<PERSON> is not used in the backtest\n", "SLOTS_SHORT       = 20      # The post describes a LONG-ONLY strategy.\n", "LONG_LEVER        = 1       # A 10-slot portfolio implies 100% investment (1.0x leverage).\n", "SHORT_LEVER       = 0       # The strategy is long-only.\n", "\n", "STOP_PCT          = 0.05\n", "MAX_HOLD_DAYS     = 6\n", "COST_BPS          = 5       # 0.05 %\n", "PROB_THRESH       = 0.75\n", "SEED              = 7\n", "\n", "MIN_PRICE         = 5.0\n", "DEBUG = True               # flip to False for silence\n", "# ╰────────────────────────────────────────────────────────────╯\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "eastern = pytz.timezone(TZ)\n", "\n", "from marketdata import MarketDataBuilder\n", "def _daily_builder():\n", "    return (MarketDataBuilder()\n", "            .with_period(\"daily\")\n", "            .with_disk_data(start_date=pd.Timestamp(TRAIN_START))\n", "            .build_market_data())\n", "_daily_md = _daily_builder()\n", "\n", "def get_daily_df(tkr: str, start: datetime, end: datetime) -> pd.DataFrame:\n", "    \"\"\"Cached daily bars wrapper (tz-aware).\"\"\"\n", "    df = _daily_md.gather_historical_data(tkr, start, end, interval=86400)\n", "    return df\n", "\n", "def dbg(msg: str):\n", "    if DEBUG:\n", "        print(msg)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[universe] 3414 symbols selected (training-period only)\n"]}], "source": ["# ╭────────────────────────── UNIVERSE ─────────────────────────╮\n", "def build_training_universe() -> list[str]:\n", "    \"\"\"\n", "    Select symbols *only* with info available in the **training window**,\n", "    avoiding any peek into 2025.\n", "    \"\"\"\n", "    # simple liquidity screen: avg $‐vol ≥ $10 M during training window\n", "    window_start = pd.Timestamp(TRAIN_START, tz=eastern)\n", "    window_end   = pd.Timestamp(TRAIN_END, tz=eastern)\n", "\n", "    # pull tickers listed in e.g. Russell 3000 (replace with your helper)\n", "    from universe.stock_universe_selector import StockUniverseSelector\n", "    from tickers.ticker_helpers import TickerInfoStore\n", "    from universe.sp500_constituents import SP500Constituents\n", "    \n", "    # sp500_constituents = SP500Constituents()\n", "    selector = StockUniverseSelector(_daily_md, TickerInfoStore())\n", "    symbols = []\n", "    \n", "    # for date in pd.date_range(window_start, window_end):\n", "    #     constituents = sp500_constituents.constituents_for(date)\n", "    #     symbols.extend(constituents)\n", "\n", "    universe_df = selector.select_by_price_and_volume(\n", "        window_start, window_end,\n", "        min_price=MIN_PRICE, min_dollar_volume=50_000_000\n", "    )\n", "\n", "    symbols = (universe_df.index\n", "               .get_level_values(\"ticker\")\n", "               .unique()\n", "               .tolist())\n", "    print(f\"[universe] {len(symbols)} symbols selected (training-period only)\")\n", "    return list(set(symbols))\n", "\n", "UNIVERSE = build_training_universe()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[model] trained on 2885941 rows\n"]}], "source": ["\n", "# ╭───────────────────────── FEATURES ──────────────────────────╮\n", "\n", "from indicators.qpi import qpi_indicator \n", "\n", "def _calculate_hurst_exponent(ts, lags=50):\n", "    \"\"\"A simplified Hurst Exponent calculation.\"\"\"\n", "    if len(ts) < lags:\n", "        return np.nan\n", "    \n", "    tau = [np.std(np.subtract(ts[lag:], ts[:-lag])) for lag in range(1, lags)]\n", "    tau = np.where(np.isclose(tau, 0), np.nan, tau) # handle cases with zero std dev\n", "    \n", "    with np.errstate(invalid='ignore', divide='ignore'):\n", "        poly = np.polyfit(np.log(np.arange(1, lags)), np.log(tau), 1)\n", "    \n", "    if np.any(np.isnan(poly)):\n", "        return np.nan\n", "        \n", "    return poly[0] * 2.0\n", "\n", "\n", "# ╭─────────────────── MODIFIED FEATURES FUNCTION ───────────────────╮\n", "\n", "def build_feature_df(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Vectorised features + binary target for one symbol.\n", "    All features are shifted one bar to prevent lookahead bias.\n", "    \"\"\"\n", "    close = df[\"close\"]\n", "    high = df[\"high\"]\n", "    low = df[\"low\"]\n", "    \n", "    feat = pd.DataFrame(index=df.index)\n", "\n", "    # 1. Rates of Change (RoC) for different windows\n", "    feat[\"roc_5\"]   = close.pct_change(5)\n", "    feat[\"roc_20\"]  = close.pct_change(20)\n", "    feat[\"roc_60\"]  = close.pct_change(60)\n", "    feat[\"roc_252\"] = close.pct_change(252)\n", "\n", "    # 2. RSIs for different windows\n", "    for n in [5, 14, 20]:\n", "        delta = close.diff()\n", "        up, down = delta.clip(lower=0), -delta.clip(upper=0)\n", "        roll_up = up.ewm(alpha=1/n, min_periods=n).mean()\n", "        roll_down = down.ewm(alpha=1/n, min_periods=n).mean()\n", "        rs = roll_up / roll_down\n", "        feat[f\"rsi_{n}\"] = 100 - (100 / (1 + rs))\n", "\n", "    # 3. QPIs for different windows\n", "    # Disable QPI for feature\n", "    # feat[\"qpi_3\"] = qpi_indicator(close, window=3, lookback_years=1)\n", "    # feat[\"qpi_5\"] = qpi_indicator(close, window=5, lookback_years=1)\n", "\n", "    # 4. IBS and Normalized ATR\n", "    feat[\"ibs\"] = (close - low) / (high - low)\n", "    \n", "    true_range = pd.concat([\n", "        high - low,\n", "        (high - close.shift()).abs(),\n", "        (low - close.shift()).abs()\n", "    ], axis=1).max(axis=1)\n", "    atr_14 = true_range.ewm(alpha=1/14, min_periods=14).mean()\n", "    feat[\"atr_norm\"] = atr_14 / close\n", "\n", "    # 5. Closing price distance to 200-day SMA\n", "    sma_200 = close.rolling(200).mean()\n", "    feat[\"sma200_dist_pct\"] = (close - sma_200) / sma_200\n", "\n", "    # 6. <PERSON><PERSON> (rolling)\n", "    # Note: This is computationally very expensive\n", "    feat[\"hurst_100\"] = close.rolling(100).apply(\n", "        _calculate_hurst_exponent, raw=True\n", "    )\n", "\n", "    # --- IMPORTANT: Shift all features to prevent lookahead bias ---\n", "    feat = feat.shift(1)\n", "\n", "    # Define the target variable (no shift needed for the target)\n", "    fwd_ret_5 = close.shift(-5) / close - 1\n", "    feat[\"target\"] = (fwd_ret_5 > 0).astype(int)\n", "\n", "    # Drop rows where any feature or target is nan\n", "    return feat.dropna()\n", "\n", "# ╭──────────────────── MODEL TRAINING (XGB) ───────────────────╮\n", "def train_model_fast() -> xgb.XGBClassifier:\n", "    dfs = []\n", "    for tkr in UNIVERSE:\n", "        df = get_daily_df(\n", "            tkr,\n", "            pd.Timestamp(TRAIN_START, tz=eastern),\n", "            pd.Timestamp(TRAIN_END,   tz=eastern)\n", "        )\n", "        if df.empty or len(df) < 205:\n", "            continue\n", "        dfs.append(build_feature_df(df))\n", "\n", "    all_feat = pd.concat(dfs, ignore_index=True)\n", "    y = all_feat.pop(\"target\")\n", "    model = xgb.XGBClassifier(\n", "        objective=\"binary:logistic\",\n", "        eval_metric=\"logloss\",\n", "        max_depth=3,\n", "        eta=0.1,\n", "        n_estimators=300,\n", "        subsample=0.8,\n", "        colsample_bytree=0.8,\n", "        random_state=SEED,\n", "    )\n", "    model.fit(all_feat, y)\n", "    print(\"[model] trained on\", len(all_feat), \"rows\")\n", "    return model\n", "\n", "MODEL = train_model_fast()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[store] feature rows: 419177\n"]}], "source": ["\n", "# ╭─────────────────────── TRADING CALENDAR ────────────────────╮\n", "nyse = mcal.get_calendar(\"NYSE\")\n", "schedule = nyse.schedule(BACKTEST_START, BACKTEST_END)\n", "\n", "TRADING_DAYS = (\n", "    schedule.index\n", "            .tz_localize(eastern)   # tag as EST/EDT, *no* conversion\n", "            .normalize()\n", ")\n", "# map day → previous trading day\n", "PREV_DAY = {d: TRADING_DAYS[i-1] if i else None\n", "            for i, d in enumerate(TRADING_DAYS)}\n", "\n", "FEATURE_STORE = None \n", "def prep_feature_store() -> pd.DataFrame:\n", "    \"\"\"\n", "    Pre-computes vectorised features for *every* symbol for the entire\n", "    back-test horizon (plus 250-day warm-up).  Results cached in\n", "    FEATURE_STORE  ⇒  zero indicator calc inside the trading loop.\n", "    \"\"\"\n", "    global FEATURE_STORE\n", "    rows = []\n", "    warmup_start = (pd.Timestamp(BACKTEST_START, tz=eastern)\n", "                    - pd.<PERSON><PERSON><PERSON>(days=400))      # 400 + safety\n", "    for tkr in UNIVERSE:\n", "        df = get_daily_df(tkr, warmup_start, pd.Timestamp(BACKTEST_END, tz=eastern))\n", "        if df.empty or len(df) < 255:\n", "            continue\n", "        fdf = build_feature_df(df)                # vectorised!\n", "        fdf = fdf.loc[fdf.index >= warmup_start]  # keep back-test range\n", "        fdf[\"ticker\"] = tkr\n", "        rows.append(fdf)\n", "    FEATURE_STORE = (pd.concat(rows)\n", "                       .reset_index()             # index→column\n", "                       .rename(columns={\"index\": \"date\"})\n", "                       .set_index([\"date\", \"ticker\"])\n", "                       .sort_index())\n", "    \n", "    FEATURE_STORE.index = (\n", "        FEATURE_STORE.index\n", "        .set_levels(\n", "            FEATURE_STORE.index.levels[0]\n", "                            .tz_convert(eastern)   # ensure US/Eastern\n", "                            .normalize()          # 00:00:00\n", "            ,\n", "            level=\"date\"\n", "        )\n", "    )\n", "    print(\"[store] feature rows:\", len(FEATURE_STORE))\n", "    \n", "prep_feature_store()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[qpi_store] Preparing QPI store...\n", "[qpi_store] QPI store prepared with 816040 rows.\n"]}], "source": ["# %%\n", "# ╭─────────────────── PREPARE QPI DATA STORE ──────────────────╮\n", "\n", "from indicators.qpi import qpi_indicator\n", "\n", "QPI_STORE = None\n", "def prep_qpi_store():\n", "    global QPI_STORE\n", "    rows = []\n", "    # QPI needs a long lookback, so start much earlier\n", "    warmup_start = (pd.Timestamp(BACKTEST_START, tz=eastern)\n", "                    - pd.<PERSON><PERSON><PERSON>(days=365 * 6)) # 5 years + buffer\n", "    \n", "    print(\"[qpi_store] Preparing QPI store...\")\n", "    for tkr in UNIVERSE:\n", "        df = get_daily_df(tkr, warmup_start, pd.Timestamp(BACKTEST_END, tz=eastern))\n", "        # Ensure enough data for a full QPI lookback period\n", "        if df.empty or len(df) < (5 * 252 + 5):\n", "             continue\n", "        \n", "        # Use the imported qpi_indicator function\n", "        qpi_values = qpi_indicator(df['close'])\n", "        \n", "        qpi_df = qpi_values.to_frame(name='qpi')\n", "        # Trim the data to only what's needed for the backtest period\n", "        qpi_df = qpi_df.loc[qpi_df.index >= pd.Timestamp(BACKTEST_START, tz=eastern) - pd.Timedelta(days=350)]\n", "        qpi_df[\"ticker\"] = tkr\n", "        rows.append(qpi_df)\n", "\n", "    if not rows:\n", "        print(\"[qpi_store] No data to create QPI store.\")\n", "        QPI_STORE = pd.DataFrame()\n", "        return\n", "\n", "    QPI_STORE = (pd.concat(rows)\n", "                   .reset_index()\n", "                   .rename(columns={\"index\": \"date\"})\n", "                   .set_index([\"date\", \"ticker\"])\n", "                   .sort_index())\n", "    \n", "    QPI_STORE.index = (\n", "        QPI_STORE.index\n", "        .set_levels(\n", "            QPI_STORE.index.levels[0]\n", "                            .tz_convert(eastern)\n", "                            .normalize()\n", "            ,\n", "            level=\"date\"\n", "        )\n", "    )\n", "    print(f\"[qpi_store] QPI store prepared with {len(QPI_STORE)} rows.\")\n", "\n", "prep_qpi_store()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# ╭──────────────────────── BACKTEST LOOP ──────────────────────╮\n", "class Position:\n", "    def __init__(self, side: str, qty: int, entry_price: float,\n", "                 entry_day: pd.Timestamp):\n", "        self.side = side\n", "        self.qty  = qty\n", "        self.entry_price = entry_price\n", "        self.entry_day   = entry_day\n", "\n", "    def mtm_equity(self, price: float) -> float:\n", "        market_value = price * self.qty\n", "        if self.side == \"long\":\n", "            return market_value\n", "        else:\n", "            return (self.entry_price * self.qty) - market_value\n", "\n", "def backtest():\n", "    cash        = 100_000.0\n", "    positions   : dict[str, Position] = {}\n", "    equity_curve = []\n", "    trades = [] # List to store trade details\n", "\n", "    for day_idx, day in enumerate(TRADING_DAYS):\n", "        prev = PREV_DAY[day]\n", "        if prev is None:\n", "            continue\n", "\n", "        today_data = {}\n", "        prev_day_data = {}\n", "        needed = set(UNIVERSE) | set(positions.keys())\n", "        for tkr in needed:\n", "            df = get_daily_df(tkr, prev, day)\n", "            if not df.empty:\n", "                if prev in df.index:\n", "                    prev_day_data[tkr] = df.loc[prev]\n", "                if day in df.index:\n", "                    today_data[tkr] = df.loc[day]\n", "\n", "        exits = []\n", "        # --- DBG ADDED BACK ---\n", "        dbg(f\"\\n--- Analyzing exits for {day.date()} ---\")\n", "        for tkr, pos in list(positions.items()):\n", "            row = today_data.get(tkr)\n", "            if row is None: continue\n", "\n", "            exit_price = 0\n", "            exit_reason = None\n", "            hit_stop = False\n", "            if pos.side == \"long\":\n", "                stop_price = pos.entry_price * (1 - STOP_PCT)\n", "                if row[\"low\"] <= stop_price:\n", "                    hit_stop = True\n", "                    exit_price = stop_price\n", "                    exit_reason = \"STOP-LOSS\"\n", "            \n", "            holding_days = len(TRADING_DAYS[(TRADING_DAYS >= pos.entry_day) & (TRADING_DAYS <= day)])\n", "            aged_out = holding_days > MAX_HOLD_DAYS\n", "\n", "            if aged_out and not hit_stop:\n", "                exit_price = row[\"open\"]\n", "                exit_reason = \"AGED-OUT\"\n", "            \n", "            if exit_reason:\n", "                fee = exit_price * abs(pos.qty) * COST_BPS / 1e4\n", "                pnl = (exit_price - pos.entry_price) * pos.qty - fee\n", "                cash += exit_price * pos.qty - fee\n", "                \n", "                # --- DBG ADDED BACK ---\n", "                dbg(f\"  EXIT: {tkr:<5s} | Reason: {exit_reason:<9s} | Entry: {pos.entry_price:7.2f} | \"\n", "                    f\"Exit: {exit_price:7.2f} | Qty: {pos.qty:4d} | PnL: ${pnl:8,.2f}\")\n", "\n", "                trades.append({\n", "                    \"ticker\": tkr, \"entry_day\": pos.entry_day, \"exit_day\": day,\n", "                    \"entry_price\": pos.entry_price, \"exit_price\": exit_price,\n", "                    \"qty\": pos.qty, \"pnl\": pnl, \"side\": pos.side,\n", "                    \"exit_reason\": exit_reason\n", "                })\n", "                exits.append(tkr)\n", "\n", "        for tkr in exits: positions.pop(tkr, None)\n", "        # --- DBG ADDED BACK ---\n", "        dbg(\"--- <PERSON><PERSON> DEBUGGING EXITS ---\")\n", "\n", "\n", "        equity_prev_close = cash\n", "        for tkr, pos in positions.items():\n", "            prev_row = prev_day_data.get(tkr)\n", "            if prev_row is not None:\n", "                equity_prev_close += pos.mtm_equity(prev_row[\"close\"])\n", "\n", "        slice_long = equity_prev_close * LONG_LEVER / SLOTS_LONG if SLOTS_LONG > 0 else 0\n", "        slice_short = 0 # Long-only strategy\n", "\n", "        feat_rows = []\n", "        prev_feat_slice = FEATURE_STORE.xs(prev, level=\"date\", drop_level=False)\n", "        for tkr in UNIVERSE:\n", "            try:\n", "                feat = prev_feat_slice.loc[(prev, tkr)].drop(\"target\")\n", "                prob = MODEL.predict_proba(feat.values.reshape(1, -1))[0, 1]\n", "                prev_close = prev_day_data.get(tkr, {}).get(\"close\", np.nan)\n", "                feat_rows.append((tkr, prob, prev_close))\n", "            except KeyError:\n", "                continue\n", "        \n", "        # ── ML loop ──────────────────────────────────────────────────\n", "        longs = sorted(\n", "            [x for x in feat_rows\n", "                if x[1] >= PROB_THRESH and x[2] >= MIN_PRICE],\n", "            key=lambda x: x[1],\n", "            reverse=True\n", "        )[:SLOTS_LONG]\n", "\n", "        shorts = []\n", "\n", "        # --- DBG ADDED BACK ---\n", "        dbg(f\"{day.date()}  longs={len(longs)} shorts={len(shorts)} \"\n", "            f\"pos={len(positions)} cash={cash:,.0f} equity={equity_prev_close:,.0f}\")\n", "\n", "        current_long_positions = sum(1 for p in positions.values() if p.side == \"long\")\n", "        slots_to_fill = SLOTS_LONG - current_long_positions\n", "        \n", "        if slots_to_fill > 0:\n", "            for tkr, _, _ in longs[:slots_to_fill]:\n", "                if tkr in positions: continue\n", "                row = today_data.get(tkr)\n", "                if row is None: continue\n", "                open_px = row[\"open\"]\n", "                qty = int(slice_long // open_px)\n", "                if qty <= 0: continue\n", "                fee = open_px * qty * COST_BPS/1e4\n", "                cash -= (open_px * qty + fee)\n", "                positions[tkr] = Position(\"long\", qty, open_px, day)\n", "\n", "        equity_close = cash\n", "        for tkr, pos in positions.items():\n", "            row = today_data.get(tkr)\n", "            if row is not None:\n", "                equity_close += pos.mtm_equity(row[\"close\"])\n", "            else:\n", "                prev_row = prev_day_data.get(tkr)\n", "                if prev_row is not None:\n", "                    equity_close += pos.mtm_equity(prev_row[\"close\"])\n", "        \n", "        equity_curve.append({\"day\": day, \"equity\": equity_close})\n", "\n", "    return pd.DataFrame(equity_curve).set_index(\"day\"), pd.DataFrame(trades)\n", "\n", "def backtest_qpi(qpi_threshold=10):\n", "    cash = 100_000.0\n", "    positions = {}\n", "    equity_curve = []\n", "    trades = [] # New list for QPI trades\n", "\n", "    for day_idx, day in enumerate(TRADING_DAYS):\n", "        prev = PREV_DAY[day]\n", "        if prev is None: continue\n", "\n", "        today_data = {}\n", "        prev_day_data = {}\n", "        needed = set(UNIVERSE) | set(positions.keys())\n", "        for tkr in needed:\n", "            df = get_daily_df(tkr, prev, day)\n", "            if not df.empty:\n", "                if prev in df.index:\n", "                    prev_day_data[tkr] = df.loc[prev]\n", "                if day in df.index:\n", "                    today_data[tkr] = df.loc[day]\n", "\n", "        exits = []\n", "        # --- DBG ADDED ---\n", "        dbg(f\"\\n--- Analyzing exits for {day.date()} (QPI) ---\")\n", "        for tkr, pos in list(positions.items()):\n", "            row = today_data.get(tkr)\n", "            if row is None: continue\n", "\n", "            exit_price = 0; exit_reason = None; hit_stop = False\n", "            \n", "            stop_price = pos.entry_price * (1 - STOP_PCT)\n", "            if row[\"low\"] <= stop_price:\n", "                hit_stop = True\n", "                exit_price = stop_price\n", "                exit_reason = \"STOP-LOSS\"\n", "\n", "            holding_days = len(TRADING_DAYS[(TRADING_DAYS >= pos.entry_day) & (TRADING_DAYS <= day)])\n", "            if holding_days > MAX_HOLD_DAYS and not hit_stop:\n", "                exit_price = row[\"open\"]\n", "                exit_reason = \"AGED-OUT\"\n", "\n", "            if exit_reason:\n", "                fee = exit_price * abs(pos.qty) * COST_BPS / 1e4\n", "                pnl = (exit_price - pos.entry_price) * pos.qty - fee\n", "                cash += exit_price * pos.qty - fee\n", "                \n", "                # --- DBG ADDED ---\n", "                dbg(f\"  EXIT: {tkr:<5s} | Reason: {exit_reason:<9s} | Entry: {pos.entry_price:7.2f} | \"\n", "                    f\"Exit: {exit_price:7.2f} | Qty: {pos.qty:4d} | PnL: ${pnl:8,.2f}\")\n", "\n", "                trades.append({\n", "                    \"ticker\": tkr, \"entry_day\": pos.entry_day, \"exit_day\": day,\n", "                    \"entry_price\": pos.entry_price, \"exit_price\": exit_price,\n", "                    \"qty\": pos.qty, \"pnl\": pnl, \"side\": \"long\",\n", "                    \"exit_reason\": exit_reason\n", "                })\n", "                exits.append(tkr)\n", "        \n", "        for tkr in exits: positions.pop(tkr, None)\n", "        # --- DBG ADDED ---\n", "        dbg(\"--- <PERSON><PERSON> DEBUGGING EXITS (QPI) ---\")\n", "\n", "        equity_prev_close = cash\n", "        for tkr, pos in positions.items():\n", "            prev_row = prev_day_data.get(tkr)\n", "            if prev_row is not None:\n", "                equity_prev_close += pos.mtm_equity(prev_row[\"close\"])\n", "        \n", "        slice_long = equity_prev_close * LONG_LEVER / SLOTS_LONG if SLOTS_LONG > 0 else 0\n", "\n", "        # --- QPI Signal Generation ---\n", "        qpi_candidates = []\n", "        if QPI_STORE is not None and not QPI_STORE.empty:\n", "            try:\n", "                prev_qpi_slice = QPI_STORE.xs(prev, level=\"date\", drop_level=False)\n", "                for tkr in UNIVERSE:\n", "                    if (prev, tkr) in prev_qpi_slice.index:\n", "                        qpi_value = prev_qpi_slice.loc[(prev, tkr)]['qpi']\n", "                        prev_close = prev_day_data.get(tkr, {}).get(\"close\", np.nan)\n", "                        if (qpi_value < qpi_threshold and\n", "                                prev_close >= MIN_PRICE):\n", "                             qpi_candidates.append((tkr, qpi_value, prev_close))\n", "            except KeyError:\n", "                pass # No QPI data for this day\n", "        \n", "        longs = sorted(qpi_candidates, key=lambda x: x[1])[:SLOTS_LONG] # Lower QPI is better\n", "        shorts = [] # QPI strategy is long-only\n", "\n", "        # --- DBG ADDED ---\n", "        dbg(f\"{day.date()}  longs={len(longs)} shorts={len(shorts)} \"\n", "            f\"pos={len(positions)} cash={cash:,.0f} equity={equity_prev_close:,.0f} (QPI)\")\n", "\n", "        current_long_positions = sum(1 for p in positions.values() if p.side == \"long\")\n", "        slots_to_fill = SLOTS_LONG - current_long_positions\n", "        if slots_to_fill > 0:\n", "            for tkr, _, _ in longs[:slots_to_fill]:\n", "                if tkr in positions: continue\n", "                row = today_data.get(tkr)\n", "                if row is None: continue\n", "                open_px = row[\"open\"]\n", "                qty = int(slice_long // open_px)\n", "                if qty <= 0: continue\n", "                fee = open_px * qty * COST_BPS/1e4\n", "                cash -= (open_px * qty + fee)\n", "                positions[tkr] = Position(\"long\", qty, open_px, day)\n", "\n", "        equity_close = cash\n", "        for tkr, pos in positions.items():\n", "            row = today_data.get(tkr)\n", "            if row is not None:\n", "                equity_close += pos.mtm_equity(row[\"close\"])\n", "            else:\n", "                prev_row = prev_day_data.get(tkr)\n", "                if prev_row is not None:\n", "                    equity_close += pos.mtm_equity(prev_row[\"close\"])\n", "        \n", "        equity_curve.append({\"day\": day, \"equity\": equity_close})\n", "\n", "    return pd.DataFrame(equity_curve).set_index(\"day\"), pd.DataFrame(trades)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Running Original XGBoost Strategy ---\n", "\n", "--- Analyzing exits for 2025-01-03 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-03  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-06 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-06  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-07 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-07  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-08 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-08  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-10 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-10  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-13 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-13  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-14 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-14  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-15 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-15  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-16 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-16  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-17 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-17  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-21 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-21  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-22 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-22  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-23 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-23  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-24 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-24  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-27 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-27  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-28 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-28  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-29 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-29  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-30 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-30  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-31 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-31  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-03 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-03  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-04 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-04  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-05 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-05  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-06 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-06  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-07 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-07  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-10 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-10  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-11 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-11  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-12 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-12  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-13 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-13  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-14 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-14  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-18 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-18  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-19 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-19  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-20 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-20  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-21 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-21  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-24 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-24  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-25 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-25  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-26 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-26  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-27 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-27  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-02-28 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-28  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-03 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-03  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-04 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-04  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-05 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-05  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-06 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-06  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-07 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-07  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-10 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-10  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-11 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-11  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-12 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-12  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-13 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-13  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-14 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-14  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-17 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-17  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-18 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-18  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-19 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-19  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-20 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-20  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-21 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-21  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-24 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-24  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-25 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-25  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-26 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-26  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-27 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-27  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-28 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-28  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-03-31 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-31  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-01 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-01  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-02 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-02  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-03 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-03  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-04 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-04  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-07 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-07  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-08 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-08  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-09 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-09  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-10 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-10  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-11 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-11  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-14 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-14  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-15 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-15  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-16 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-16  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-17 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-17  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-21 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-21  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-22 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-22  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-23 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-23  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-24 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-24  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-25 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-25  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-28 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-28  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-29 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-29  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-04-30 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-30  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-01 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-01  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-02 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-02  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-05 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-05  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-06 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-06  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-07 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-07  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-08 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-08  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-09 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-09  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-12 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-12  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-13 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-13  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-14 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-14  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-15 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-15  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-16 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-16  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-19 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-19  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-20 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-20  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-21 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-21  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-22 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-22  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-23 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-05-23  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-05-27 ---\n", "--- END DEBUGGING EXITS ---\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# ╭───────────────────────── RUN & STATS ───────────────────────╮\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m--- Running Original XGBoost Strategy ---\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m eq_orig, trades_orig = \u001b[43mbacktest\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      5\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m--- Running QPI-Only Strategy ---\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      6\u001b[39m eq_qpi, trades_qpi = backtest_qpi(qpi_threshold=\u001b[32m10\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 99\u001b[39m, in \u001b[36mbacktest\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     97\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m     98\u001b[39m     feat = prev_feat_slice.loc[(prev, tkr)].drop(\u001b[33m\"\u001b[39m\u001b[33mtarget\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m99\u001b[39m     prob = \u001b[43mMODEL\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpredict_proba\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfeat\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreshape\u001b[49m\u001b[43m(\u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m-\u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m[\u001b[32m0\u001b[39m, \u001b[32m1\u001b[39m]\n\u001b[32m    100\u001b[39m     prev_close = prev_day_data.get(tkr, {}).get(\u001b[33m\"\u001b[39m\u001b[33mclose\u001b[39m\u001b[33m\"\u001b[39m, np.nan)\n\u001b[32m    101\u001b[39m     feat_rows.append((tkr, prob, prev_close))\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/xgboost/sklearn.py:1797\u001b[39m, in \u001b[36mXGBClassifier.predict_proba\u001b[39m\u001b[34m(self, X, validate_features, base_margin, iteration_range)\u001b[39m\n\u001b[32m   1795\u001b[39m     class_prob = softmax(raw_predt, axis=\u001b[32m1\u001b[39m)\n\u001b[32m   1796\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m class_prob\n\u001b[32m-> \u001b[39m\u001b[32m1797\u001b[39m class_probs = \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpredict\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1798\u001b[39m \u001b[43m    \u001b[49m\u001b[43mX\u001b[49m\u001b[43m=\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1799\u001b[39m \u001b[43m    \u001b[49m\u001b[43mvalidate_features\u001b[49m\u001b[43m=\u001b[49m\u001b[43mvalidate_features\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1800\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbase_margin\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbase_margin\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1801\u001b[39m \u001b[43m    \u001b[49m\u001b[43miteration_range\u001b[49m\u001b[43m=\u001b[49m\u001b[43miteration_range\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1802\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1803\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m _cls_predict_proba(\u001b[38;5;28mself\u001b[39m.n_classes_, class_probs, np.vstack)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/xgboost/core.py:729\u001b[39m, in \u001b[36mrequire_keyword_args.<locals>.throw_if.<locals>.inner_f\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    727\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m k, arg \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(sig.parameters, args):\n\u001b[32m    728\u001b[39m     kwargs[k] = arg\n\u001b[32m--> \u001b[39m\u001b[32m729\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/xgboost/sklearn.py:1327\u001b[39m, in \u001b[36mXGBModel.predict\u001b[39m\u001b[34m(self, X, output_margin, validate_features, base_margin, iteration_range)\u001b[39m\n\u001b[32m   1325\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._can_use_inplace_predict():\n\u001b[32m   1326\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1327\u001b[39m         predts = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mget_booster\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43minplace_predict\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1328\u001b[39m \u001b[43m            \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1329\u001b[39m \u001b[43m            \u001b[49m\u001b[43miteration_range\u001b[49m\u001b[43m=\u001b[49m\u001b[43miteration_range\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1330\u001b[39m \u001b[43m            \u001b[49m\u001b[43mpredict_type\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmargin\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43moutput_margin\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mvalue\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   1331\u001b[39m \u001b[43m            \u001b[49m\u001b[43mmissing\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmissing\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1332\u001b[39m \u001b[43m            \u001b[49m\u001b[43mbase_margin\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbase_margin\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1333\u001b[39m \u001b[43m            \u001b[49m\u001b[43mvalidate_features\u001b[49m\u001b[43m=\u001b[49m\u001b[43mvalidate_features\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1334\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1335\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m _is_cupy_alike(predts):\n\u001b[32m   1336\u001b[39m             cp = import_cupy()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/xgboost/core.py:729\u001b[39m, in \u001b[36mrequire_keyword_args.<locals>.throw_if.<locals>.inner_f\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    727\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m k, arg \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(sig.parameters, args):\n\u001b[32m    728\u001b[39m     kwargs[k] = arg\n\u001b[32m--> \u001b[39m\u001b[32m729\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/w/backtest/.venv/lib/python3.13/site-packages/xgboost/core.py:2687\u001b[39m, in \u001b[36mBooster.inplace_predict\u001b[39m\u001b[34m(self, data, iteration_range, predict_type, missing, validate_features, base_margin, strict_shape)\u001b[39m\n\u001b[32m   2683\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m _ensure_np_dtype\n\u001b[32m   2685\u001b[39m     data, _ = _ensure_np_dtype(data, data.dtype)\n\u001b[32m   2686\u001b[39m     _check_call(\n\u001b[32m-> \u001b[39m\u001b[32m2687\u001b[39m         \u001b[43m_LIB\u001b[49m\u001b[43m.\u001b[49m\u001b[43mXGBoosterPredictFromDense\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2688\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2689\u001b[39m \u001b[43m            \u001b[49m\u001b[43marray_interface\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2690\u001b[39m \u001b[43m            \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2691\u001b[39m \u001b[43m            \u001b[49m\u001b[43mp_handle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2692\u001b[39m \u001b[43m            \u001b[49m\u001b[43mctypes\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbyref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mshape\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2693\u001b[39m \u001b[43m            \u001b[49m\u001b[43mctypes\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbyref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdims\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2694\u001b[39m \u001b[43m            \u001b[49m\u001b[43mctypes\u001b[49m\u001b[43m.\u001b[49m\u001b[43mbyref\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpreds\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2695\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2696\u001b[39m     )\n\u001b[32m   2697\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m _prediction_output(shape, dims, preds, \u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[32m   2698\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data, (ArrowTransformed, PandasTransformed)):\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["# ╭───────────────────────── RUN & STATS ───────────────────────╮\n", "print(\"\\n--- Running Original XGBoost Strategy ---\")\n", "eq_orig, trades_orig = backtest()\n", "\n", "print(\"\\n--- Running QPI-Only Strategy ---\")\n", "eq_qpi, trades_qpi = backtest_qpi(qpi_threshold=10)\n", "\n", "# Calculate stats for both\n", "def calculate_stats(eq_df, name):\n", "    if eq_df.empty:\n", "        return pd.Series([0, 0, 0], index=[\"CAGR\", \"Sharpe Ratio\", \"Max Drawdown\"], name=name)\n", "    eq_df[\"ret\"] = eq_df[\"equity\"].pct_change().fillna(0)\n", "    cagr = (eq_df[\"equity\"].iloc[-1] / eq_df[\"equity\"].iloc[0]) ** (252 / len(eq_df)) - 1\n", "    # Handle cases with zero standard deviation in returns\n", "    ret_std = eq_df[\"ret\"].std()\n", "    if ret_std == 0:\n", "        sharpe = np.inf if eq_df[\"ret\"].mean() > 0 else 0\n", "    else:\n", "        sharpe = np.sqrt(252) * eq_df[\"ret\"].mean() / ret_std\n", "    maxdd = (eq_df[\"equity\"] / eq_df[\"equity\"].cummax() - 1).min()\n", "    return pd.Series([cagr, sharpe, maxdd], index=[\"CAGR\", \"Sharpe Ratio\", \"Max Drawdown\"], name=name)\n", "\n", "stats_orig = calculate_stats(eq_orig, \"Original Strategy (XGBoost)\")\n", "stats_qpi = calculate_stats(eq_qpi, \"QPI-Only Strategy\")\n", "\n", "comparison_df = pd.concat([stats_orig, stats_qpi], axis=1)\n", "print(\"\\n--- Performance Comparison ---\")\n", "print(comparison_df.to_string(formatters={'CAGR': '{:,.2%}'.format, 'Max Drawdown': '{:,.2%}'.format, 'Sharpe Ratio': '{:,.2f}'.format}))\n", "\n", "# Plotting\n", "import matplotlib.pyplot as plt\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "fig, ax = plt.subplots(figsize=(12, 7))\n", "\n", "if not eq_orig.empty:\n", "    eq_orig[\"equity\"].plot(ax=ax, label=\"Original Strategy (XGBoost)\", legend=True)\n", "if not eq_qpi.empty:\n", "    eq_qpi[\"equity\"].plot(ax=ax, label=\"QPI-Only Strategy\", legend=True)\n", "\n", "ax.set_title(f\"Strategy Comparison: {BACKTEST_START} to {BACKTEST_END}\")\n", "ax.set_ylabel(\"Equity\")\n", "ax.set_xlabel(\"Date\")\n", "ax.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\nGenerated Trade Lists:\")\n", "if not trades_orig.empty:\n", "    print(\"\\nOriginal Strategy (First 5 Trades):\")\n", "    print(trades_orig.head())\n", "else:\n", "    print(\"\\nNo trades were made by the Original Strategy.\")\n", "\n", "if not trades_qpi.empty:\n", "    print(\"\\nQPI-Only Strategy (First 5 Trades):\")\n", "    print(trades_qpi.head())\n", "else:\n", "    print(\"\\nNo trades were made by the QPI-Only Strategy.\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}