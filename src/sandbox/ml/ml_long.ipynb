{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Add L/S version\n", "# Add regime check\n", "\n", "from __future__ import annotations\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import numpy as np\n", "import pytz\n", "import xgboost as xgb\n", "from collections import defaultdict\n", "import pandas_market_calendars as mcal\n", "import warnings\n", "import random\n", "\n", "warnings.filterwarnings(\"ignore\", category=FutureWarning)\n", "\n", "# ╭────────────────────────── CONFIG ──────────────────────────╮\n", "TZ                = \"US/Eastern\"\n", "TRAIN_START       = \"2020-01-01\"\n", "TRAIN_END         = \"2024-12-31\"\n", "BACKTEST_START    = \"2025-01-01\"\n", "BACKTEST_END      = \"2025-06-06\"\n", "\n", "SLOTS_LONG        = 20      # The post specifies 10 slots/positions maximum.\n", "# Note: Short<PERSON> is not used in the backtest\n", "SLOTS_SHORT       = 20      # The post describes a LONG-ONLY strategy.\n", "LONG_LEVER        = 1       # A 10-slot portfolio implies 100% investment (1.0x leverage).\n", "SHORT_LEVER       = 0       # The strategy is long-only.\n", "\n", "STOP_PCT          = 0.05\n", "MAX_HOLD_DAYS     = 6\n", "COST_BPS          = 5       # 0.05 %\n", "PROB_THRESH       = 0.60\n", "SEED              = 7\n", "\n", "MIN_PRICE         = 5.0\n", "DEBUG = True               # flip to False for silence\n", "\n", "SAMPLE_UNIVERSE_SIZE = None    # Set to 0 or None to use the full universe\n", "# ╰────────────────────────────────────────────────────────────╯\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "eastern = pytz.timezone(TZ)\n", "\n", "from marketdata import MarketDataBuilder\n", "def _daily_builder():\n", "    return (MarketDataBuilder()\n", "            .with_period(\"daily\")\n", "            .with_disk_data(start_date=pd.Timestamp(TRAIN_START))\n", "            .build_market_data())\n", "_daily_md = _daily_builder()\n", "\n", "def get_daily_df(tkr: str, start: datetime, end: datetime) -> pd.DataFrame:\n", "    \"\"\"Cached daily bars wrapper (tz-aware).\"\"\"\n", "    df = _daily_md.gather_historical_data(tkr, start, end, interval=86400)\n", "    return df\n", "\n", "def dbg(msg: str):\n", "    if DEBUG:\n", "        print(msg)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[universe] 3414 symbols selected (training-period only)\n"]}], "source": ["# ╭────────────────────────── UNIVERSE ─────────────────────────╮\n", "def build_training_universe() -> list[str]:\n", "    \"\"\"\n", "    Select symbols *only* with info available in the **training window**,\n", "    avoiding any peek into 2025.\n", "    \"\"\"\n", "    # simple liquidity screen: avg $‐vol ≥ $10 M during training window\n", "    window_start = pd.Timestamp(TRAIN_START, tz=eastern)\n", "    window_end   = pd.Timestamp(TRAIN_END, tz=eastern)\n", "\n", "    # pull tickers listed in e.g. Russell 3000 (replace with your helper)\n", "    from universe.stock_universe_selector import StockUniverseSelector\n", "    from tickers.ticker_helpers import TickerInfoStore\n", "    from universe.sp500_constituents import SP500Constituents\n", "    \n", "    # sp500_constituents = SP500Constituents()\n", "    selector = StockUniverseSelector(_daily_md, TickerInfoStore())\n", "    symbols = []\n", "    \n", "    # for date in pd.date_range(window_start, window_end):\n", "    #     constituents = sp500_constituents.constituents_for(date)\n", "    #     symbols.extend(constituents)\n", "\n", "    universe_df = selector.select_by_price_and_volume(\n", "        window_start, window_end,\n", "        min_price=MIN_PRICE, min_dollar_volume=50_000_000\n", "    )\n", "\n", "    symbols = (universe_df.index\n", "               .get_level_values(\"ticker\")\n", "               .unique()\n", "               .tolist())\n", "\n", "    if SAMPLE_UNIVERSE_SIZE and SAMPLE_UNIVERSE_SIZE > 0:\n", "        print(f\"[universe] Full universe has {len(symbols)} symbols.\")\n", "        # Ensure we don't try to sample more than available\n", "        sample_size = min(len(symbols), SAMPLE_UNIVERSE_SIZE)\n", "        symbols = random.sample(symbols, k=sample_size)\n", "        print(f\"[universe] Sampling {len(symbols)} symbols for validation.\")\n", "        \n", "    print(f\"[universe] {len(symbols)} symbols selected (training-period only)\")\n", "    \n", "    return list(set(symbols))\n", "\n", "UNIVERSE = build_training_universe() "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[model] Adding advanced features to training data...\n", "[store] Advanced features added.\n", "[model] Advanced features added to training data.\n", "[model] trained on 2806955 rows with 18 features.\n", "\n", "--- [Model Analysis] Feature Importances (XGBoost) ---\n", "             feature  importance\n", "0           atr_norm    0.137571\n", "1   turnover_rank_cs    0.109620\n", "2           turnover    0.074179\n", "3             rsi_14    0.072669\n", "4             rsi_20    0.062244\n", "5             roc_60    0.061266\n", "6     rsi_14_rank_cs    0.056895\n", "7     roc_20_rank_cs    0.052969\n", "8              qpi_5    0.052868\n", "9             roc_20    0.047535\n", "10   turnover_rel_ts    0.040453\n", "11   sma200_dist_pct    0.038326\n", "12             roc_5    0.037829\n", "13             qpi_3    0.036004\n", "14               ibs    0.031290\n", "15           roc_252    0.030421\n", "16             rsi_5    0.030368\n", "17         hurst_100    0.027494\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# ╭───────────────────────── FEATURES ──────────────────────────╮\n", "\n", "from indicators.qpi import qpi_indicator \n", "\n", "def _calculate_hurst_exponent(ts, lags=50):\n", "    \"\"\"A simplified Hurst Exponent calculation.\"\"\"\n", "    if len(ts) < lags:\n", "        return np.nan\n", "    \n", "    tau = [np.std(np.subtract(ts[lag:], ts[:-lag])) for lag in range(1, lags)]\n", "    tau = np.where(np.isclose(tau, 0), np.nan, tau) # handle cases with zero std dev\n", "    \n", "    with np.errstate(invalid='ignore', divide='ignore'):\n", "        poly = np.polyfit(np.log(np.arange(1, lags)), np.log(tau), 1)\n", "    \n", "    if np.any(np.isnan(poly)):\n", "        return np.nan\n", "        \n", "    return poly[0] * 2.0\n", "\n", "\n", "# ╭─────────────────── MODIFIED FEATURES FUNCTION ───────────────────╮\n", "\n", "def build_feature_df(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Vectorised features + binary target for one symbol.\n", "    All features are shifted one bar to prevent lookahead bias.\n", "    \"\"\"\n", "    close = df[\"close\"]\n", "    high = df[\"high\"]\n", "    low = df[\"low\"]\n", "    volume = df[\"volume\"]\n", "    \n", "    feat = pd.DataFrame(index=df.index)\n", "    \n", "    # Group 1: <PERSON>um\n", "    \n", "    # 1. Rates of Change (RoC) for different windows\n", "    feat[\"roc_5\"]   = close.pct_change(5)\n", "    feat[\"roc_20\"]  = close.pct_change(20)\n", "    feat[\"roc_60\"]  = close.pct_change(60)\n", "    feat[\"roc_252\"] = close.pct_change(252)\n", "\n", "    # 2. RSIs for different windows\n", "    for n in [5, 14, 20]:\n", "        delta = close.diff()\n", "        up, down = delta.clip(lower=0), -delta.clip(upper=0)\n", "        roll_up = up.ewm(alpha=1/n, min_periods=n).mean()\n", "        roll_down = down.ewm(alpha=1/n, min_periods=n).mean()\n", "        rs = roll_up / roll_down\n", "        feat[f\"rsi_{n}\"] = 100 - (100 / (1 + rs))\n", "\n", "    # 3. QPIs for different windows\n", "    feat[\"qpi_3\"] = qpi_indicator(close, window=3, lookback_years=1)\n", "    feat[\"qpi_5\"] = qpi_indicator(close, window=5, lookback_years=1)\n", "    \n", "    # Group 2: Intraday & Volatility\n", "\n", "    # 4. IBS and Normalized ATR\n", "    feat[\"ibs\"] = (close - low) / (high - low)\n", "    \n", "    true_range = pd.concat([\n", "        high - low,\n", "        (high - close.shift()).abs(),\n", "        (low - close.shift()).abs()\n", "    ], axis=1).max(axis=1)\n", "    atr_14 = true_range.ewm(alpha=1/14, min_periods=14).mean()\n", "    feat[\"atr_norm\"] = atr_14 / close\n", "    \n", "    # Group 3: Long-Term Trend\n", "\n", "    # 5. Closing price distance to 200-day SMA\n", "    sma_200 = close.rolling(200).mean()\n", "    feat[\"sma200_dist_pct\"] = (close - sma_200) / sma_200\n", "\n", "    # 6. <PERSON><PERSON> (rolling)\n", "    # Note: This is computationally very expensive\n", "    feat[\"hurst_100\"] = close.rolling(100).apply(\n", "        _calculate_hurst_exponent, raw=True\n", "    )\n", "    \n", "    # Group 4: Volume\n", "    \n", "    # 7. Turnover\n", "    feat[\"turnover\"] = close * volume\n", "    \n", "    # --- IMPORTANT: Shift all features to prevent lookahead bias ---\n", "    feat = feat.shift(1)\n", "\n", "    # Define the target variable (no shift needed for the target)\n", "    fwd_ret_5 = close.shift(-5) / close - 1\n", "    feat[\"target\"] = (fwd_ret_5 > 0).astype(int)\n", "    \n", "    feat[\"ticker\"] = df.attrs.get(\"ticker\", \"UNKNOWN\")\n", "\n", "    # Drop rows where any feature or target is nan\n", "    return feat.dropna()\n", "\n", "def add_advanced_features(feature_df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Adds time-series and cross-sectional features to the feature store.\n", "    This function operates on the entire feature store after it's built.\n", "    \"\"\"\n", "    \n", "    # 9. Time-Series Relative Turnover\n", "    # Compares today's turnover to its own 50-day rolling average.\n", "    # A value > 1 means today's turnover is higher than the average.\n", "    # We group by 'ticker' to ensure the rolling window is calculated per-stock.\n", "    feature_df['turnover_rel_ts'] = feature_df.groupby('ticker')['turnover'].transform(\n", "        lambda x: x / x.rolling(50, min_periods=20).mean()\n", "    )\n", "\n", "    # 10. Cross-Sectional Rank Features\n", "    # For each day, rank every stock's feature value against all other stocks.\n", "    # .rank(pct=True) returns the rank as a percentage (0.0 to 1.0),\n", "    # which is a great way to standardize the feature.\n", "    # We group by 'date' to perform the ranking across all tickers for that day.\n", "    \n", "    # Turnover Rank\n", "    feature_df['turnover_rank_cs'] = feature_df.groupby('date')['turnover'].rank(pct=True)\n", "    \n", "    # RSI Rank\n", "    feature_df['rsi_14_rank_cs'] = feature_df.groupby('date')['rsi_14'].rank(pct=True)\n", "    \n", "    # Rate of Change Rank\n", "    feature_df['roc_20_rank_cs'] = feature_df.groupby('date')['roc_20'].rank(pct=True)\n", "    \n", "    print(\"[store] Advanced features added.\")\n", "    return feature_df\n", "\n", "\n", "\n", "# ╭──────────────────── MODEL TRAINING (XGB) ───────────────────╮\n", "\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "def analyze_and_plot_feature_importance(model: xgb.XGBClassifier, feature_names: list[str]):\n", "    \"\"\"\n", "    Analyzes and plots the feature importances from a trained XGBoost model.\n", "\n", "    Args:\n", "        model: The trained XGBoost classifier.\n", "        feature_names: A list of the feature names, in the same order as the\n", "                       training data.\n", "    \"\"\"\n", "    # Create a DataFrame for easy viewing\n", "    importances = model.feature_importances_\n", "    feature_importance_df = pd.DataFrame({\n", "        'feature': feature_names,\n", "        'importance': importances\n", "    }).sort_values('importance', ascending=False).reset_index(drop=True)\n", "\n", "    print(\"\\n--- [Model Analysis] Feature Importances (XGBoost) ---\")\n", "    print(feature_importance_df)\n", "\n", "    # Plotting the importances\n", "    plt.style.use('seaborn-v0_8-darkgrid')\n", "    plt.figure(figsize=(12, 8))\n", "    sns.barplot(x='importance', y='feature', data=feature_importance_df, palette='viridis')\n", "    plt.title('XGBoost Feature Importance', fontsize=16)\n", "    plt.xlabel('Importance Score (Gain)', fontsize=12)\n", "    plt.ylabel('Features', fontsize=12)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "def train_model_fast() -> xgb.XGBClassifier:\n", "    dfs = []\n", "    for tkr in UNIVERSE:\n", "        df = get_daily_df(\n", "            tkr,\n", "            pd.Timestamp(TRAIN_START, tz=eastern),\n", "            pd.Timestamp(TRAIN_END,   tz=eastern)\n", "        )\n", "        if df.empty or len(df) < 205:\n", "            continue\n", "        fdf = build_feature_df(df)\n", "        fdf[\"ticker\"] = tkr\n", "        dfs.append(fdf)\n", "\n", "    all_feat = pd.concat(dfs)\n", "\n", "    print(\"[model] Adding advanced features to training data...\")\n", "    all_feat = (\n", "        all_feat.reset_index()\n", "                .rename(columns={\"index\": \"date\"})\n", "                .pipe(add_advanced_features)\n", "                .set_index([\"date\", \"ticker\"])\n", "                .sort_index()\n", "    )\n", "    all_feat.dropna(inplace=True)\n", "    print(\"[model] Advanced features added to training data.\")\n", "    \n", "    y = all_feat.pop(\"target\")\n", "    model = xgb.XGBClassifier(\n", "        objective=\"binary:logistic\",\n", "        eval_metric=\"logloss\",\n", "        max_depth=3,\n", "        eta=0.1,\n", "        n_estimators=300,\n", "        subsample=0.8,\n", "        colsample_bytree=0.8,\n", "        random_state=SEED,\n", "    )\n", "    model.fit(all_feat, y)\n", "    \n", "    print(\"[model] trained on\", len(all_feat), \"rows with\", len(all_feat.columns), \"features.\")\n", "    analyze_and_plot_feature_importance(model, all_feat.columns.tolist())\n", "\n", "    return model\n", "\n", "\n", "MODEL = train_model_fast()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[store] Advanced features added.\n", "[store] feature rows: 338918\n"]}], "source": ["\n", "# ╭─────────────────────── TRADING CALENDAR ────────────────────╮\n", "nyse = mcal.get_calendar(\"NYSE\")\n", "schedule = nyse.schedule(BACKTEST_START, BACKTEST_END)\n", "\n", "TRADING_DAYS = (\n", "    schedule.index\n", "            .tz_localize(eastern)   # tag as EST/EDT, *no* conversion\n", "            .normalize()\n", ")\n", "# map day → previous trading day\n", "PREV_DAY = {d: TRADING_DAYS[i-1] if i else None\n", "            for i, d in enumerate(TRADING_DAYS)}\n", "\n", "FEATURE_STORE = None \n", "def prep_feature_store() -> pd.DataFrame:\n", "    \"\"\"\n", "    Pre-computes vectorised features for *every* symbol for the entire\n", "    back-test horizon (plus 250-day warm-up).  Results cached in\n", "    FEATURE_STORE  ⇒  zero indicator calc inside the trading loop.\n", "    \"\"\"\n", "    global FEATURE_STORE\n", "    rows = []\n", "    warmup_start = (pd.Timestamp(BACKTEST_START, tz=eastern)\n", "                    - pd.<PERSON><PERSON><PERSON>(days=400))      # 400 + safety\n", "    for tkr in UNIVERSE:\n", "        df = get_daily_df(tkr, warmup_start, pd.Timestamp(BACKTEST_END, tz=eastern))\n", "        if df.empty or len(df) < 255:\n", "            continue\n", "        fdf = build_feature_df(df.assign(ticker=tkr))\n", "        fdf = fdf.loc[fdf.index >= warmup_start]  # keep back-test range\n", "        fdf[\"ticker\"] = tkr\n", "        rows.append(fdf)\n", "        \n", "    FEATURE_STORE = pd.concat(rows)\n", "    FEATURE_STORE = (\n", "        FEATURE_STORE.reset_index()\n", "                     .rename(columns={\"index\": \"date\"})\n", "                     .pipe(add_advanced_features) # a clean way to apply the function\n", "    )          \n", "    \n", "    FEATURE_STORE = (\n", "        FEATURE_STORE.set_index([\"date\", \"ticker\"])\n", "                     .sort_index()\n", "    )\n", "    \n", "    FEATURE_STORE.dropna(inplace=True)      \n", "    \n", "    FEATURE_STORE.index = (\n", "        FEATURE_STORE.index\n", "        .set_levels(\n", "            FEATURE_STORE.index.levels[0]\n", "                            .tz_convert(eastern)   # ensure US/Eastern\n", "                            .normalize()          # 00:00:00\n", "            ,\n", "            level=\"date\"\n", "        )\n", "    )\n", "    print(\"[store] feature rows:\", len(FEATURE_STORE))\n", "    \n", "prep_feature_store()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[qpi_store] Preparing QPI store...\n", "[qpi_store] QPI store prepared with 816040 rows.\n"]}], "source": ["# %%\n", "# ╭─────────────────── PREPARE QPI DATA STORE ──────────────────╮\n", "\n", "from indicators.qpi import qpi_indicator\n", "\n", "QPI_STORE = None\n", "def prep_qpi_store():\n", "    global QPI_STORE\n", "    rows = []\n", "    # QPI needs a long lookback, so start much earlier\n", "    warmup_start = (pd.Timestamp(BACKTEST_START, tz=eastern)\n", "                    - pd.<PERSON><PERSON><PERSON>(days=365 * 6)) # 5 years + buffer\n", "    \n", "    print(\"[qpi_store] Preparing QPI store...\")\n", "    for tkr in UNIVERSE:\n", "        df = get_daily_df(tkr, warmup_start, pd.Timestamp(BACKTEST_END, tz=eastern))\n", "        # Ensure enough data for a full QPI lookback period\n", "        if df.empty or len(df) < (5 * 252 + 5):\n", "             continue\n", "        \n", "        # Use the imported qpi_indicator function\n", "        qpi_values = qpi_indicator(df['close'])\n", "        \n", "        qpi_df = qpi_values.to_frame(name='qpi')\n", "        # Trim the data to only what's needed for the backtest period\n", "        qpi_df = qpi_df.loc[qpi_df.index >= pd.Timestamp(BACKTEST_START, tz=eastern) - pd.Timedelta(days=350)]\n", "        qpi_df[\"ticker\"] = tkr\n", "        rows.append(qpi_df)\n", "\n", "    if not rows:\n", "        print(\"[qpi_store] No data to create QPI store.\")\n", "        QPI_STORE = pd.DataFrame()\n", "        return\n", "\n", "    QPI_STORE = (pd.concat(rows)\n", "                   .reset_index()\n", "                   .rename(columns={\"index\": \"date\"})\n", "                   .set_index([\"date\", \"ticker\"])\n", "                   .sort_index())\n", "    \n", "    QPI_STORE.index = (\n", "        QPI_STORE.index\n", "        .set_levels(\n", "            QPI_STORE.index.levels[0]\n", "                            .tz_convert(eastern)\n", "                            .normalize()\n", "            ,\n", "            level=\"date\"\n", "        )\n", "    )\n", "    print(f\"[qpi_store] QPI store prepared with {len(QPI_STORE)} rows.\")\n", "\n", "prep_qpi_store()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# ╭──────────────────────── BACKTEST LOOP ──────────────────────╮\n", "class Position:\n", "    def __init__(self, side: str, qty: int, entry_price: float,\n", "                 entry_day: pd.Timestamp):\n", "        self.side = side\n", "        self.qty  = qty\n", "        self.entry_price = entry_price\n", "        self.entry_day   = entry_day\n", "\n", "    def mtm_equity(self, price: float) -> float:\n", "        market_value = price * self.qty\n", "        if self.side == \"long\":\n", "            return market_value\n", "        else:\n", "            return (self.entry_price * self.qty) - market_value\n", "\n", "def backtest():\n", "    cash        = 100_000.0\n", "    positions   : dict[str, Position] = {}\n", "    equity_curve = []\n", "    trades = [] # List to store trade details\n", "\n", "    for day_idx, day in enumerate(TRADING_DAYS):\n", "        prev = PREV_DAY[day]\n", "        if prev is None:\n", "            continue\n", "\n", "        if prev not in FEATURE_STORE.index.get_level_values('date'):\n", "            dbg(f\"WARN: Date {prev.date()} not found in FEATURE_STORE. Skipping trade logic for {day.date()}.\")\n", "            # If there's a history, carry over the last known equity. Otherwise, use initial cash.\n", "            equity_close = equity_curve[-1]['equity'] if equity_curve else cash\n", "            equity_curve.append({\"day\": day, \"equity\": equity_close})\n", "            continue\n", "\n", "        today_data = {}\n", "        prev_day_data = {}\n", "        needed = set(UNIVERSE) | set(positions.keys())\n", "        for tkr in needed:\n", "            df = get_daily_df(tkr, prev, day)\n", "            if not df.empty:\n", "                if prev in df.index:\n", "                    prev_day_data[tkr] = df.loc[prev]\n", "                if day in df.index:\n", "                    today_data[tkr] = df.loc[day]\n", "\n", "        exits = []\n", "        # --- DBG ADDED BACK ---\n", "        dbg(f\"\\n--- Analyzing exits for {day.date()} ---\")\n", "        for tkr, pos in list(positions.items()):\n", "            row = today_data.get(tkr)\n", "            if row is None: continue\n", "\n", "            exit_price = 0\n", "            exit_reason = None\n", "            hit_stop = False\n", "            if pos.side == \"long\":\n", "                stop_price = pos.entry_price * (1 - STOP_PCT)\n", "                if row[\"low\"] <= stop_price:\n", "                    hit_stop = True\n", "                    exit_price = stop_price\n", "                    exit_reason = \"STOP-LOSS\"\n", "            \n", "            holding_days = len(TRADING_DAYS[(TRADING_DAYS >= pos.entry_day) & (TRADING_DAYS <= day)])\n", "            aged_out = holding_days > MAX_HOLD_DAYS\n", "\n", "            if aged_out and not hit_stop:\n", "                exit_price = row[\"open\"]\n", "                exit_reason = \"AGED-OUT\"\n", "            \n", "            if exit_reason:\n", "                fee = exit_price * abs(pos.qty) * COST_BPS / 1e4\n", "                pnl = (exit_price - pos.entry_price) * pos.qty - fee\n", "                cash += exit_price * pos.qty - fee\n", "                \n", "                # --- DBG ADDED BACK ---\n", "                dbg(f\"  EXIT: {tkr:<5s} | Reason: {exit_reason:<9s} | Entry: {pos.entry_price:7.2f} | \"\n", "                    f\"Exit: {exit_price:7.2f} | Qty: {pos.qty:4d} | PnL: ${pnl:8,.2f}\")\n", "\n", "                trades.append({\n", "                    \"ticker\": tkr, \"entry_day\": pos.entry_day, \"exit_day\": day,\n", "                    \"entry_price\": pos.entry_price, \"exit_price\": exit_price,\n", "                    \"qty\": pos.qty, \"pnl\": pnl, \"side\": pos.side,\n", "                    \"exit_reason\": exit_reason\n", "                })\n", "                exits.append(tkr)\n", "\n", "        for tkr in exits: positions.pop(tkr, None)\n", "        # --- DBG ADDED BACK ---\n", "        dbg(\"--- <PERSON><PERSON> DEBUGGING EXITS ---\")\n", "\n", "\n", "        equity_prev_close = cash\n", "        for tkr, pos in positions.items():\n", "            prev_row = prev_day_data.get(tkr)\n", "            if prev_row is not None:\n", "                equity_prev_close += pos.mtm_equity(prev_row[\"close\"])\n", "\n", "        slice_long = equity_prev_close * LONG_LEVER / SLOTS_LONG if SLOTS_LONG > 0 else 0\n", "        slice_short = 0 # Long-only strategy\n", "\n", "        feat_rows = []\n", "        prev_feat_slice = FEATURE_STORE.xs(prev, level=\"date\", drop_level=False)\n", "        for tkr in UNIVERSE:\n", "            try:\n", "                feat = prev_feat_slice.loc[(prev, tkr)].drop(\"target\")\n", "                prob = MODEL.predict_proba(feat.values.reshape(1, -1))[0, 1]\n", "                prev_close = prev_day_data.get(tkr, {}).get(\"close\", np.nan)\n", "                feat_rows.append((tkr, prob, prev_close))\n", "            except KeyError:\n", "                continue\n", "        \n", "        # ── ML loop ──────────────────────────────────────────────────\n", "        longs = sorted(\n", "            [x for x in feat_rows\n", "                if x[1] >= PROB_THRESH and x[2] >= MIN_PRICE],\n", "            key=lambda x: x[1],\n", "            reverse=True\n", "        )[:SLOTS_LONG]\n", "\n", "        shorts = []\n", "\n", "        # --- DBG ADDED BACK ---\n", "        dbg(f\"{day.date()}  longs={len(longs)} shorts={len(shorts)} \"\n", "            f\"pos={len(positions)} cash={cash:,.0f} equity={equity_prev_close:,.0f}\")\n", "\n", "        current_long_positions = sum(1 for p in positions.values() if p.side == \"long\")\n", "        slots_to_fill = SLOTS_LONG - current_long_positions\n", "        \n", "        if slots_to_fill > 0:\n", "            for tkr, _, _ in longs[:slots_to_fill]:\n", "                if tkr in positions: continue\n", "                row = today_data.get(tkr)\n", "                if row is None: continue\n", "                open_px = row[\"open\"]\n", "                qty = int(slice_long // open_px)\n", "                if qty <= 0: continue\n", "                fee = open_px * qty * COST_BPS/1e4\n", "                cash -= (open_px * qty + fee)\n", "                positions[tkr] = Position(\"long\", qty, open_px, day)\n", "\n", "        equity_close = cash\n", "        for tkr, pos in positions.items():\n", "            row = today_data.get(tkr)\n", "            if row is not None:\n", "                equity_close += pos.mtm_equity(row[\"close\"])\n", "            else:\n", "                prev_row = prev_day_data.get(tkr)\n", "                if prev_row is not None:\n", "                    equity_close += pos.mtm_equity(prev_row[\"close\"])\n", "        \n", "        equity_curve.append({\"day\": day, \"equity\": equity_close})\n", "\n", "    return pd.DataFrame(equity_curve).set_index(\"day\"), pd.DataFrame(trades)\n", "\n", "def backtest_qpi(qpi_threshold=10):\n", "    cash = 100_000.0\n", "    positions = {}\n", "    equity_curve = []\n", "    trades = [] # New list for QPI trades\n", "\n", "    for day_idx, day in enumerate(TRADING_DAYS):\n", "        prev = PREV_DAY[day]\n", "        if prev is None: continue\n", "\n", "        if prev not in QPI_STORE.index.get_level_values('date'):\n", "            dbg(f\"WARN: Date {prev.date()} not found in QPI_STORE. Skipping trade logic for {day.date()}.\")\n", "            equity_close = equity_curve[-1]['equity'] if equity_curve else cash\n", "            equity_curve.append({\"day\": day, \"equity\": equity_close})\n", "            continue\n", "\n", "            \n", "        today_data = {}\n", "        prev_day_data = {}\n", "        needed = set(UNIVERSE) | set(positions.keys())\n", "        for tkr in needed:\n", "            df = get_daily_df(tkr, prev, day)\n", "            if not df.empty:\n", "                if prev in df.index:\n", "                    prev_day_data[tkr] = df.loc[prev]\n", "                if day in df.index:\n", "                    today_data[tkr] = df.loc[day]\n", "\n", "        exits = []\n", "        # --- DBG ADDED ---\n", "        dbg(f\"\\n--- Analyzing exits for {day.date()} (QPI) ---\")\n", "        for tkr, pos in list(positions.items()):\n", "            row = today_data.get(tkr)\n", "            if row is None: continue\n", "\n", "            exit_price = 0; exit_reason = None; hit_stop = False\n", "            \n", "            stop_price = pos.entry_price * (1 - STOP_PCT)\n", "            if row[\"low\"] <= stop_price:\n", "                hit_stop = True\n", "                exit_price = stop_price\n", "                exit_reason = \"STOP-LOSS\"\n", "\n", "            holding_days = len(TRADING_DAYS[(TRADING_DAYS >= pos.entry_day) & (TRADING_DAYS <= day)])\n", "            if holding_days > MAX_HOLD_DAYS and not hit_stop:\n", "                exit_price = row[\"open\"]\n", "                exit_reason = \"AGED-OUT\"\n", "\n", "            if exit_reason:\n", "                fee = exit_price * abs(pos.qty) * COST_BPS / 1e4\n", "                pnl = (exit_price - pos.entry_price) * pos.qty - fee\n", "                cash += exit_price * pos.qty - fee\n", "                \n", "                # --- DBG ADDED ---\n", "                dbg(f\"  EXIT: {tkr:<5s} | Reason: {exit_reason:<9s} | Entry: {pos.entry_price:7.2f} | \"\n", "                    f\"Exit: {exit_price:7.2f} | Qty: {pos.qty:4d} | PnL: ${pnl:8,.2f}\")\n", "\n", "                trades.append({\n", "                    \"ticker\": tkr, \"entry_day\": pos.entry_day, \"exit_day\": day,\n", "                    \"entry_price\": pos.entry_price, \"exit_price\": exit_price,\n", "                    \"qty\": pos.qty, \"pnl\": pnl, \"side\": \"long\",\n", "                    \"exit_reason\": exit_reason\n", "                })\n", "                exits.append(tkr)\n", "        \n", "        for tkr in exits: positions.pop(tkr, None)\n", "        # --- DBG ADDED ---\n", "        dbg(\"--- <PERSON><PERSON> DEBUGGING EXITS (QPI) ---\")\n", "\n", "        equity_prev_close = cash\n", "        for tkr, pos in positions.items():\n", "            prev_row = prev_day_data.get(tkr)\n", "            if prev_row is not None:\n", "                equity_prev_close += pos.mtm_equity(prev_row[\"close\"])\n", "        \n", "        slice_long = equity_prev_close * LONG_LEVER / SLOTS_LONG if SLOTS_LONG > 0 else 0\n", "\n", "        # --- QPI Signal Generation ---\n", "        qpi_candidates = []\n", "        if QPI_STORE is not None and not QPI_STORE.empty:\n", "            try:\n", "                prev_qpi_slice = QPI_STORE.xs(prev, level=\"date\", drop_level=False)\n", "                for tkr in UNIVERSE:\n", "                    if (prev, tkr) in prev_qpi_slice.index:\n", "                        qpi_value = prev_qpi_slice.loc[(prev, tkr)]['qpi']\n", "                        prev_close = prev_day_data.get(tkr, {}).get(\"close\", np.nan)\n", "                        if (qpi_value < qpi_threshold and\n", "                                prev_close >= MIN_PRICE):\n", "                             qpi_candidates.append((tkr, qpi_value, prev_close))\n", "            except KeyError:\n", "                pass # No QPI data for this day\n", "        \n", "        longs = sorted(qpi_candidates, key=lambda x: x[1])[:SLOTS_LONG] # Lower QPI is better\n", "        shorts = [] # QPI strategy is long-only\n", "\n", "        # --- DBG ADDED ---\n", "        dbg(f\"{day.date()}  longs={len(longs)} shorts={len(shorts)} \"\n", "            f\"pos={len(positions)} cash={cash:,.0f} equity={equity_prev_close:,.0f} (QPI)\")\n", "\n", "        current_long_positions = sum(1 for p in positions.values() if p.side == \"long\")\n", "        slots_to_fill = SLOTS_LONG - current_long_positions\n", "        if slots_to_fill > 0:\n", "            for tkr, _, _ in longs[:slots_to_fill]:\n", "                if tkr in positions: continue\n", "                row = today_data.get(tkr)\n", "                if row is None: continue\n", "                open_px = row[\"open\"]\n", "                qty = int(slice_long // open_px)\n", "                if qty <= 0: continue\n", "                fee = open_px * qty * COST_BPS/1e4\n", "                cash -= (open_px * qty + fee)\n", "                positions[tkr] = Position(\"long\", qty, open_px, day)\n", "\n", "        equity_close = cash\n", "        for tkr, pos in positions.items():\n", "            row = today_data.get(tkr)\n", "            if row is not None:\n", "                equity_close += pos.mtm_equity(row[\"close\"])\n", "            else:\n", "                prev_row = prev_day_data.get(tkr)\n", "                if prev_row is not None:\n", "                    equity_close += pos.mtm_equity(prev_row[\"close\"])\n", "        \n", "        equity_curve.append({\"day\": day, \"equity\": equity_close})\n", "\n", "    return pd.DataFrame(equity_curve).set_index(\"day\"), pd.DataFrame(trades)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Running Original XGBoost Strategy ---\n", "WARN: Date 2025-01-02 not found in FEATURE_STORE. Skipping trade logic for 2025-01-03.\n", "WARN: Date 2025-01-03 not found in FEATURE_STORE. Skipping trade logic for 2025-01-06.\n", "\n", "--- Analyzing exits for 2025-01-07 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-07  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-08 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-08  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-10 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-10  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-13 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-13  longs=0 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-14 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-14  longs=1 shorts=0 pos=0 cash=100,000 equity=100,000\n", "\n", "--- Analyzing exits for 2025-01-15 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-15  longs=0 shorts=0 pos=1 cash=95,782 equity=99,924\n", "\n", "--- Analyzing exits for 2025-01-16 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-16  longs=0 shorts=0 pos=1 cash=95,782 equity=100,023\n", "\n", "--- Analyzing exits for 2025-01-17 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-17  longs=0 shorts=0 pos=1 cash=95,782 equity=99,994\n", "\n", "--- Analyzing exits for 2025-01-21 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-21  longs=0 shorts=0 pos=1 cash=95,782 equity=100,072\n", "\n", "--- Analyzing exits for 2025-01-22 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-22  longs=0 shorts=0 pos=1 cash=95,782 equity=100,130\n", "\n", "--- Analyzing exits for 2025-01-23 ---\n", "  EXIT: NFLX  | Reason: AGED-OUT  | Entry:  843.20 | Exit:  957.81 | Qty:    5 | PnL: $  570.66\n", "--- END DEBUGGING EXITS ---\n", "2025-01-23  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-01-24 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-24  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-01-27 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-27  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-01-28 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-28  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-01-29 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-29  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-01-30 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-30  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-01-31 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-01-31  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-02-03 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-03  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-02-04 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-04  longs=0 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-02-05 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-05  longs=1 shorts=0 pos=0 cash=100,569 equity=100,569\n", "\n", "--- Analyzing exits for 2025-02-06 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-06  longs=0 shorts=0 pos=1 cash=95,546 equity=100,580\n", "\n", "--- Analyzing exits for 2025-02-07 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-07  longs=1 shorts=0 pos=1 cash=95,546 equity=100,457\n", "\n", "--- Analyzing exits for 2025-02-10 ---\n", "  EXIT: EIX   | Reason: STOP-LOSS | Entry:   52.29 | Exit:   49.68 | Qty:   96 | PnL: $ -253.38\n", "--- END DEBUGGING EXITS ---\n", "2025-02-10  longs=0 shorts=0 pos=1 cash=95,331 equity=100,298\n", "\n", "--- Analyzing exits for 2025-02-11 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-11  longs=0 shorts=0 pos=1 cash=95,331 equity=100,207\n", "\n", "--- Analyzing exits for 2025-02-12 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-12  longs=0 shorts=0 pos=1 cash=95,331 equity=100,279\n", "\n", "--- Analyzing exits for 2025-02-13 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-13  longs=0 shorts=0 pos=1 cash=95,331 equity=100,224\n", "\n", "--- Analyzing exits for 2025-02-14 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-14  longs=0 shorts=0 pos=1 cash=95,331 equity=100,213\n", "\n", "--- Analyzing exits for 2025-02-18 ---\n", "  EXIT: PVH   | Reason: AGED-OUT  | Entry:   79.04 | Exit:   77.95 | Qty:   63 | PnL: $  -71.13\n", "--- END DEBUGGING EXITS ---\n", "2025-02-18  longs=0 shorts=0 pos=0 cash=100,239 equity=100,239\n", "\n", "--- Analyzing exits for 2025-02-19 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-19  longs=0 shorts=0 pos=0 cash=100,239 equity=100,239\n", "\n", "--- Analyzing exits for 2025-02-20 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-20  longs=0 shorts=0 pos=0 cash=100,239 equity=100,239\n", "\n", "--- Analyzing exits for 2025-02-21 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-21  longs=0 shorts=0 pos=0 cash=100,239 equity=100,239\n", "\n", "--- Analyzing exits for 2025-02-24 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-24  longs=1 shorts=0 pos=0 cash=100,239 equity=100,239\n", "\n", "--- Analyzing exits for 2025-02-25 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-02-25  longs=6 shorts=0 pos=1 cash=95,234 equity=100,203\n", "\n", "--- Analyzing exits for 2025-02-26 ---\n", "  EXIT: CNK   | Reason: STOP-LOSS | Entry:   27.19 | Exit:   25.83 | Qty:  184 | PnL: $ -252.52\n", "--- END DEBUGGING EXITS ---\n", "2025-02-26  longs=16 shorts=0 pos=5 cash=75,133 equity=100,140\n", "\n", "--- Analyzing exits for 2025-02-27 ---\n", "  EXIT: FIX   | Reason: STOP-LOSS | Entry:  384.28 | Exit:  365.07 | Qty:   13 | PnL: $ -252.15\n", "--- END DEBUGGING EXITS ---\n", "2025-02-27  longs=13 shorts=0 pos=17 cash=15,814 equity=100,214\n", "\n", "--- Analyzing exits for 2025-02-28 ---\n", "  EXIT: EME   | Reason: STOP-LOSS | Entry:  419.98 | Exit:  398.98 | Qty:   11 | PnL: $ -233.18\n", "  EXIT: PWR   | Reason: STOP-LOSS | Entry:  270.40 | Exit:  256.88 | Qty:   18 | PnL: $ -245.67\n", "  EXIT: HRI   | Reason: STOP-LOSS | Entry:  150.38 | Exit:  142.86 | Qty:   33 | PnL: $ -250.48\n", "--- END DEBUGGING EXITS ---\n", "2025-02-28  longs=5 shorts=0 pos=14 cash=29,534 equity=98,387\n", "\n", "--- Analyzing exits for 2025-03-03 ---\n", "  EXIT: TFII  | Reason: STOP-LOSS | Entry:   92.00 | Exit:   87.40 | Qty:   54 | PnL: $ -250.76\n", "  EXIT: WWW   | Reason: STOP-LOSS | Entry:   15.17 | Exit:   14.41 | Qty:  330 | PnL: $ -252.68\n", "  EXIT: PSN   | Reason: STOP-LOSS | Entry:   58.83 | Exit:   55.89 | Qty:   85 | PnL: $ -252.40\n", "  EXIT: CNK   | Reason: STOP-LOSS | Entry:   26.25 | Exit:   24.94 | Qty:  190 | PnL: $ -251.74\n", "  EXIT: WDC   | Reason: STOP-LOSS | Entry:   49.77 | Exit:   47.28 | Qty:  100 | PnL: $ -251.21\n", "  EXIT: PRIM  | Reason: STOP-LOSS | Entry:   71.74 | Exit:   68.15 | Qty:   69 | PnL: $ -249.85\n", "  EXIT: FRPT  | Reason: STOP-LOSS | Entry:  106.86 | Exit:  101.52 | Qty:   46 | PnL: $ -248.11\n", "  EXIT: GKOS  | Reason: STOP-LOSS | Entry:  117.97 | Exit:  112.07 | Qty:   41 | PnL: $ -244.14\n", "  EXIT: KTB   | Reason: STOP-LOSS | Entry:   67.32 | Exit:   63.95 | Qty:   73 | PnL: $ -248.05\n", "--- END DEBUGGING EXITS ---\n", "2025-03-03  longs=17 shorts=0 pos=9 cash=52,490 equity=97,872\n", "\n", "--- Analyzing exits for 2025-03-04 ---\n", "  EXIT: PEGA  | Reason: STOP-LOSS | Entry:   78.95 | Exit:   75.00 | Qty:   63 | PnL: $ -251.06\n", "  EXIT: TRUP  | Reason: STOP-LOSS | Entry:   33.85 | Exit:   32.16 | Qty:  147 | PnL: $ -251.16\n", "  EXIT: PRM   | Reason: STOP-LOSS | Entry:   10.19 | Exit:    9.68 | Qty:  491 | PnL: $ -252.54\n", "  EXIT: EVR   | Reason: STOP-LOSS | Entry:  242.16 | Exit:  230.05 | Qty:   20 | PnL: $ -244.46\n", "  EXIT: TSLA  | Reason: STOP-LOSS | Entry:  279.50 | Exit:  265.52 | Qty:   17 | PnL: $ -239.83\n", "  EXIT: CAVA  | Reason: STOP-LOSS | Entry:   95.10 | Exit:   90.34 | Qty:   51 | PnL: $ -244.81\n", "  EXIT: KTB   | Reason: STOP-LOSS | Entry:   65.44 | Exit:   62.17 | Qty:   74 | PnL: $ -244.43\n", "  EXIT: FTDR  | Reason: STOP-LOSS | Entry:   45.50 | Exit:   43.23 | Qty:  107 | PnL: $ -245.74\n", "  EXIT: CIVI  | Reason: STOP-LOSS | Entry:   38.75 | Exit:   36.81 | Qty:  126 | PnL: $ -246.44\n", "  EXIT: ICUI  | Reason: STOP-LOSS | Entry:  147.00 | Exit:  139.65 | Qty:   33 | PnL: $ -244.85\n", "  EXIT: SDRL  | Reason: STOP-LOSS | Entry:   25.58 | Exit:   24.30 | Qty:  191 | PnL: $ -246.61\n", "  EXIT: AAON  | Reason: STOP-LOSS | Entry:   77.26 | Exit:   73.40 | Qty:   63 | PnL: $ -245.68\n", "--- END DEBUGGING EXITS ---\n", "2025-03-04  longs=16 shorts=0 pos=7 cash=59,612 equity=93,979\n", "\n", "--- Analyzing exits for 2025-03-05 ---\n", "  EXIT: HYAC  | Reason: AGED-OUT  | Entry:   10.87 | Exit:   10.89 | Qty:  460 | PnL: $    6.70\n", "  EXIT: GDDY  | Reason: AGED-OUT  | Entry:  173.69 | Exit:  174.28 | Qty:   28 | PnL: $   14.08\n", "--- END DEBUGGING EXITS ---\n", "2025-03-05  longs=20 shorts=0 pos=17 cash=13,385 equity=95,211\n", "\n", "--- Analyzing exits for 2025-03-06 ---\n", "  EXIT: JX<PERSON>   | Reason: AGED-OUT  | Entry:   85.51 | Exit:   85.53 | Qty:   58 | PnL: $   -1.32\n", "  EXIT: PAYO  | Reason: STOP-LOSS | Entry:    8.26 | Exit:    7.85 | Qty:  568 | PnL: $ -236.81\n", "  EXIT: GKOS  | Reason: STOP-LOSS | Entry:  112.21 | Exit:  106.60 | Qty:   42 | PnL: $ -237.88\n", "--- END DEBUGGING EXITS ---\n", "2025-03-06  longs=20 shorts=0 pos=15 cash=22,558 equity=96,393\n", "\n", "--- Analyzing exits for 2025-03-07 ---\n", "  EXIT: DTM   | Reason: STOP-LOSS | Entry:   93.04 | Exit:   88.39 | Qty:   52 | PnL: $ -244.20\n", "  EXIT: ONON  | Reason: STOP-LOSS | Entry:   48.10 | Exit:   45.70 | Qty:  101 | PnL: $ -245.21\n", "  EXIT: CAVA  | Reason: STOP-LOSS | Entry:   85.22 | Exit:   80.96 | Qty:   55 | PnL: $ -236.60\n", "  EXIT: HUT   | Reason: STOP-LOSS | Entry:   13.42 | Exit:   12.75 | Qty:  350 | PnL: $ -237.08\n", "  EXIT: UAL   | Reason: STOP-LOSS | Entry:   89.28 | Exit:   84.82 | Qty:   53 | PnL: $ -238.84\n", "  EXIT: DSP   | Reason: STOP-LOSS | Entry:   14.83 | Exit:   14.09 | Qty:  324 | PnL: $ -242.53\n", "--- END DEBUGGING EXITS ---\n", "2025-03-07  longs=20 shorts=0 pos=13 cash=30,708 equity=93,769\n", "\n", "--- Analyzing exits for 2025-03-10 ---\n", "  EXIT: FTDR  | Reason: STOP-LOSS | Entry:   40.87 | Exit:   38.83 | Qty:  114 | PnL: $ -235.17\n", "  EXIT: KTB   | Reason: STOP-LOSS | Entry:   62.17 | Exit:   59.06 | Qty:   75 | PnL: $ -235.35\n", "  EXIT: CAVA  | Reason: STOP-LOSS | Entry:   82.34 | Exit:   78.22 | Qty:   56 | PnL: $ -232.74\n", "  EXIT: CRGY  | Reason: STOP-LOSS | Entry:   10.97 | Exit:   10.42 | Qty:  427 | PnL: $ -236.43\n", "  EXIT: SN    | Reason: STOP-LOSS | Entry:   90.29 | Exit:   85.78 | Qty:   51 | PnL: $ -232.43\n", "  EXIT: GKOS  | Reason: STOP-LOSS | Entry:  102.88 | Exit:   97.74 | Qty:   45 | PnL: $ -233.68\n", "  EXIT: VSCO  | Reason: STOP-LOSS | Entry:   20.01 | Exit:   19.01 | Qty:  234 | PnL: $ -236.34\n", "--- END DEBUGGING EXITS ---\n", "2025-03-10  longs=20 shorts=0 pos=13 cash=29,018 equity=94,322\n", "\n", "--- Analyzing exits for 2025-03-11 ---\n", "  EXIT: PLNT  | Reason: AGED-OUT  | Entry:   93.07 | Exit:   93.71 | Qty:   52 | PnL: $   30.84\n", "  EXIT: UTHR  | Reason: AGED-OUT  | Entry:  316.46 | Exit:  314.74 | Qty:   15 | PnL: $  -28.16\n", "  EXIT: EVR   | Reason: STOP-LOSS | Entry:  201.08 | Exit:  191.03 | Qty:   23 | PnL: $ -233.44\n", "  EXIT: DUOL  | Reason: STOP-LOSS | Entry:  286.55 | Exit:  272.22 | Qty:   16 | PnL: $ -231.42\n", "  EXIT: RCL   | Reason: STOP-LOSS | Entry:  207.60 | Exit:  197.22 | Qty:   22 | PnL: $ -230.53\n", "--- END DEBUGGING EXITS ---\n", "2025-03-11  longs=20 shorts=0 pos=15 cash=19,223 equity=92,158\n", "\n", "--- Analyzing exits for 2025-03-12 ---\n", "  EXIT: AAON  | Reason: AGED-OUT  | Entry:   71.00 | Exit:   84.44 | Qty:   66 | PnL: $  884.25\n", "  EXIT: ICFI  | Reason: AGED-OUT  | Entry:   76.82 | Exit:   87.85 | Qty:   61 | PnL: $  670.15\n", "  EXIT: TFX   | Reason: AGED-OUT  | Entry:  134.57 | Exit:  138.55 | Qty:   34 | PnL: $  132.96\n", "  EXIT: SDRL  | Reason: AGED-OUT  | Entry:   22.98 | Exit:   24.19 | Qty:  204 | PnL: $  244.37\n", "  EXIT: AI    | Reason: AGED-OUT  | Entry:   21.40 | Exit:   21.70 | Qty:  219 | PnL: $   63.32\n", "  EXIT: VAL   | Reason: AGED-OUT  | Entry:   32.50 | Exit:   37.35 | Qty:  144 | PnL: $  695.71\n", "  EXIT: ILMN  | Reason: AGED-OUT  | Entry:   80.74 | Exit:   84.63 | Qty:   58 | PnL: $  223.17\n", "--- END DEBUGGING EXITS ---\n", "2025-03-12  longs=20 shorts=0 pos=12 cash=36,511 equity=93,885\n", "\n", "--- Analyzing exits for 2025-03-13 ---\n", "  EXIT: CVNA  | Reason: STOP-LOSS | Entry:  183.25 | Exit:  174.09 | Qty:   25 | PnL: $ -231.24\n", "  EXIT: UAL   | Reason: STOP-LOSS | Entry:   76.80 | Exit:   72.96 | Qty:   61 | PnL: $ -236.47\n", "  EXIT: UPST  | Reason: STOP-LOSS | Entry:   50.50 | Exit:   47.97 | Qty:   92 | PnL: $ -234.51\n", "--- END DEBUGGING EXITS ---\n", "2025-03-13  longs=20 shorts=0 pos=16 cash=17,181 equity=94,259\n", "\n", "--- Analyzing exits for 2025-03-14 ---\n", "  EXIT: FMC   | Reason: AGED-OUT  | Entry:   39.40 | Exit:   40.79 | Qty:  122 | PnL: $  167.09\n", "  EXIT: STZ   | Reason: AGED-OUT  | Entry:  172.97 | Exit:  182.49 | Qty:   27 | PnL: $  254.58\n", "--- END DEBUGGING EXITS ---\n", "2025-03-14  longs=20 shorts=0 pos=17 cash=12,985 equity=91,957\n", "\n", "--- Analyzing exits for 2025-03-17 ---\n", "  EXIT: SM    | Reason: AGED-OUT  | Entry:   27.54 | Exit:   29.96 | Qty:  170 | PnL: $  408.85\n", "  EXIT: NE    | Reason: AGED-OUT  | Entry:   23.24 | Exit:   23.98 | Qty:  201 | PnL: $  146.33\n", "--- END DEBUGGING EXITS ---\n", "2025-03-17  longs=20 shorts=0 pos=16 cash=18,304 equity=95,630\n", "\n", "--- Analyzing exits for 2025-03-18 ---\n", "  EXIT: GTLS  | Reason: AGED-OUT  | Entry:  144.25 | Exit:  149.64 | Qty:   32 | PnL: $  170.09\n", "  EXIT: GKOS  | Reason: AGED-OUT  | Entry:  100.87 | Exit:  102.56 | Qty:   46 | PnL: $   75.38\n", "  EXIT: KGS   | Reason: AGED-OUT  | Entry:   33.40 | Exit:   37.97 | Qty:  141 | PnL: $  641.69\n", "  EXIT: TGLS  | Reason: AGED-OUT  | Entry:   64.48 | Exit:   69.80 | Qty:   73 | PnL: $  385.81\n", "  EXIT: UPST  | Reason: STOP-LOSS | Entry:   48.98 | Exit:   46.53 | Qty:   96 | PnL: $ -237.34\n", "--- END DEBUGGING EXITS ---\n", "2025-03-18  longs=13 shorts=0 pos=14 cash=28,460 equity=96,820\n", "\n", "--- Analyzing exits for 2025-03-19 ---\n", "  EXIT: TWLO  | Reason: AGED-OUT  | Entry:   98.95 | Exit:  100.38 | Qty:   46 | PnL: $   63.47\n", "  EXIT: JEF   | Reason: AGED-OUT  | Entry:   53.70 | Exit:   57.66 | Qty:   85 | PnL: $  334.15\n", "  EXIT: EVR   | Reason: AGED-OUT  | Entry:  191.20 | Exit:  200.66 | Qty:   24 | PnL: $  224.63\n", "  EXIT: IOT   | Reason: AGED-OUT  | Entry:   34.05 | Exit:   38.33 | Qty:  135 | PnL: $  575.21\n", "--- END DEBUGGING EXITS ---\n", "2025-03-19  longs=3 shorts=0 pos=16 cash=19,193 equity=95,150\n", "\n", "--- Analyzing exits for 2025-03-20 ---\n", "  EXIT: CLBT  | Reason: AGED-OUT  | Entry:   17.45 | Exit:   18.74 | Qty:  268 | PnL: $  341.87\n", "  EXIT: SSB   | Reason: AGED-OUT  | Entry:   91.65 | Exit:   92.75 | Qty:   51 | PnL: $   53.73\n", "  EXIT: PTON  | Reason: AGED-OUT  | Entry:    5.95 | Exit:    6.33 | Qty:  789 | PnL: $  301.27\n", "  EXIT: HLNE  | Reason: AGED-OUT  | Entry:  142.77 | Exit:  148.60 | Qty:   32 | PnL: $  184.18\n", "  EXIT: EPAM  | Reason: STOP-LOSS | Entry:  185.61 | Exit:  176.33 | Qty:   26 | PnL: $ -243.59\n", "--- END DEBUGGING EXITS ---\n", "2025-03-20  longs=6 shorts=0 pos=11 cash=43,268 equity=96,868\n", "\n", "--- Analyzing exits for 2025-03-21 ---\n", "  EXIT: ALK   | Reason: AGED-OUT  | Entry:   53.46 | Exit:   52.18 | Qty:   88 | PnL: $ -114.94\n", "  EXIT: UAL   | Reason: AGED-OUT  | Entry:   73.15 | Exit:   72.79 | Qty:   64 | PnL: $  -25.37\n", "--- END DEBUGGING EXITS ---\n", "2025-03-21  longs=1 shorts=0 pos=14 cash=28,718 equity=96,589\n", "\n", "--- Analyzing exits for 2025-03-24 ---\n", "  EXIT: AAL   | Reason: AGED-OUT  | Entry:   10.87 | Exit:   11.56 | Qty:  422 | PnL: $  288.74\n", "--- END DEBUGGING EXITS ---\n", "2025-03-24  longs=2 shorts=0 pos=14 cash=28,814 equity=96,776\n", "\n", "--- Analyzing exits for 2025-03-25 ---\n", "  EXIT: DAL   | Reason: AGED-OUT  | Entry:   45.80 | Exit:   49.60 | Qty:  104 | PnL: $  392.62\n", "  EXIT: HOOD  | Reason: AGED-OUT  | Entry:   39.35 | Exit:   48.75 | Qty:  121 | PnL: $1,134.45\n", "  EXIT: PEGA  | Reason: AGED-OUT  | Entry:   71.55 | Exit:   76.13 | Qty:   66 | PnL: $  299.77\n", "--- END DEBUGGING EXITS ---\n", "2025-03-25  longs=0 shorts=0 pos=13 cash=35,271 equity=100,924\n", "\n", "--- Analyzing exits for 2025-03-26 ---\n", "  EXIT: SHOO  | Reason: AGED-OUT  | Entry:   25.64 | Exit:   27.30 | Qty:  188 | PnL: $  309.51\n", "  EXIT: TTD   | Reason: AGED-OUT  | Entry:   55.97 | Exit:   62.49 | Qty:   86 | PnL: $  558.03\n", "  EXIT: GPI   | Reason: AGED-OUT  | Entry:  387.99 | Exit:  423.88 | Qty:   12 | PnL: $  428.14\n", "  EXIT: IBKR  | Reason: AGED-OUT  | Entry:  171.51 | Exit:  179.59 | Qty:   28 | PnL: $  223.73\n", "  EXIT: KSS   | Reason: AGED-OUT  | Entry:    8.53 | Exit:    9.13 | Qty:  567 | PnL: $  340.45\n", "  EXIT: SRPT  | Reason: STOP-LOSS | Entry:   74.67 | Exit:   70.94 | Qty:   64 | PnL: $ -241.21\n", "--- END DEBUGGING EXITS ---\n", "2025-03-26  longs=0 shorts=0 pos=7 cash=65,594 equity=100,887\n", "\n", "--- Analyzing exits for 2025-03-27 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-03-27  longs=0 shorts=0 pos=7 cash=65,594 equity=100,118\n", "\n", "--- Analyzing exits for 2025-03-28 ---\n", "  EXIT: WRBY  | Reason: AGED-OUT  | Entry:   18.71 | Exit:   18.94 | Qty:  258 | PnL: $   56.90\n", "  EXIT: HPE   | Reason: AGED-OUT  | Entry:   16.10 | Exit:   15.85 | Qty:  300 | PnL: $  -77.38\n", "  EXIT: TSLA  | Reason: AGED-OUT  | Entry:  233.34 | Exit:  275.57 | Qty:   20 | PnL: $  841.84\n", "  EXIT: WING  | Reason: AGED-OUT  | Entry:  214.10 | Exit:  226.47 | Qty:   22 | PnL: $  269.65\n", "  EXIT: GLOB  | Reason: STOP-LOSS | Entry:  131.94 | Exit:  125.34 | Qty:   36 | PnL: $ -239.75\n", "--- END DEBUGGING EXITS ---\n", "2025-03-28  longs=0 shorts=0 pos=2 cash=90,229 equity=95,108\n", "\n", "--- Analyzing exits for 2025-03-31 ---\n", "  EXIT: EPAM  | Reason: AGED-OUT  | Entry:  170.61 | Exit:  167.09 | Qty:   28 | PnL: $ -100.90\n", "--- END DEBUGGING EXITS ---\n", "2025-03-31  longs=0 shorts=0 pos=1 cash=94,906 equity=99,774\n", "\n", "--- Analyzing exits for 2025-04-01 ---\n", "  EXIT: HYAC  | Reason: AGED-OUT  | Entry:   10.91 | Exit:   10.88 | Qty:  443 | PnL: $  -15.70\n", "--- END DEBUGGING EXITS ---\n", "2025-04-01  longs=1 shorts=0 pos=0 cash=99,723 equity=99,723\n", "\n", "--- Analyzing exits for 2025-04-02 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-02  longs=1 shorts=0 pos=1 cash=94,736 equity=99,740\n", "\n", "--- Analyzing exits for 2025-04-03 ---\n", "--- END DEBUGGING EXITS ---\n", "2025-04-03  longs=0 shorts=0 pos=2 cash=89,768 equity=99,837\n", "\n", "--- Analyzing exits for 2025-04-04 ---\n", "  EXIT: RLI   | Reason: STOP-LOSS | Entry:   80.40 | Exit:   76.38 | Qty:   62 | PnL: $ -251.61\n", "--- END DEBUGGING EXITS ---\n", "2025-04-04  longs=0 shorts=0 pos=1 cash=94,501 equity=99,577\n", "\n", "--- Analyzing exits for 2025-04-07 ---\n", "  EXIT: TSCO  | Reason: STOP-LOSS | Entry:   54.56 | Exit:   51.83 | Qty:   91 | PnL: $ -250.61\n", "--- END DEBUGGING EXITS ---\n"]}], "source": ["# ╭───────────────────────── RUN & STATS ───────────────────────╮\n", "print(\"\\n--- Running Original XGBoost Strategy ---\")\n", "eq_orig, trades_orig = backtest()\n", "\n", "print(\"\\n--- Running QPI-Only Strategy ---\")\n", "eq_qpi, trades_qpi = backtest_qpi(qpi_threshold=10)\n", "\n", "# Calculate stats for both\n", "def calculate_stats(eq_df, name):\n", "    if eq_df.empty:\n", "        return pd.Series([0, 0, 0], index=[\"CAGR\", \"Sharpe Ratio\", \"Max Drawdown\"], name=name)\n", "    eq_df[\"ret\"] = eq_df[\"equity\"].pct_change().fillna(0)\n", "    cagr = (eq_df[\"equity\"].iloc[-1] / eq_df[\"equity\"].iloc[0]) ** (252 / len(eq_df)) - 1\n", "    # Handle cases with zero standard deviation in returns\n", "    ret_std = eq_df[\"ret\"].std()\n", "    if ret_std == 0:\n", "        sharpe = np.inf if eq_df[\"ret\"].mean() > 0 else 0\n", "    else:\n", "        sharpe = np.sqrt(252) * eq_df[\"ret\"].mean() / ret_std\n", "    maxdd = (eq_df[\"equity\"] / eq_df[\"equity\"].cummax() - 1).min()\n", "    return pd.Series([cagr, sharpe, maxdd], index=[\"CAGR\", \"Sharpe Ratio\", \"Max Drawdown\"], name=name)\n", "\n", "stats_orig = calculate_stats(eq_orig, \"Original Strategy (XGBoost)\")\n", "stats_qpi = calculate_stats(eq_qpi, \"QPI-Only Strategy\")\n", "\n", "comparison_df = pd.concat([stats_orig, stats_qpi], axis=1)\n", "print(\"\\n--- Performance Comparison ---\")\n", "print(comparison_df.to_string(formatters={'CAGR': '{:,.2%}'.format, 'Max Drawdown': '{:,.2%}'.format, 'Sharpe Ratio': '{:,.2f}'.format}))\n", "\n", "# Plotting\n", "import matplotlib.pyplot as plt\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "fig, ax = plt.subplots(figsize=(12, 7))\n", "\n", "if not eq_orig.empty:\n", "    eq_orig[\"equity\"].plot(ax=ax, label=\"Original Strategy (XGBoost)\", legend=True)\n", "if not eq_qpi.empty:\n", "    eq_qpi[\"equity\"].plot(ax=ax, label=\"QPI-Only Strategy\", legend=True)\n", "\n", "ax.set_title(f\"Strategy Comparison: {BACKTEST_START} to {BACKTEST_END}\")\n", "ax.set_ylabel(\"Equity\")\n", "ax.set_xlabel(\"Date\")\n", "ax.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\nGenerated Trade Lists:\")\n", "if not trades_orig.empty:\n", "    print(\"\\nOriginal Strategy (First 5 Trades):\")\n", "    print(trades_orig.head())\n", "else:\n", "    print(\"\\nNo trades were made by the Original Strategy.\")\n", "\n", "if not trades_qpi.empty:\n", "    print(\"\\nQPI-Only Strategy (First 5 Trades):\")\n", "    print(trades_qpi.head())\n", "else:\n", "    print(\"\\nNo trades were made by the QPI-Only Strategy.\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}