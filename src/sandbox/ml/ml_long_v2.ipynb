{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Add ATR rank\n", "\n", "from __future__ import annotations\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import numpy as np\n", "import pytz\n", "import xgboost as xgb\n", "from collections import defaultdict\n", "import pandas_market_calendars as mcal\n", "import warnings\n", "import random\n", "\n", "warnings.filterwarnings(\"ignore\", category=FutureWarning)\n", "\n", "# ╭────────────────────────── CONFIG ──────────────────────────╮\n", "TZ                = \"US/Eastern\"\n", "TRAIN_START       = \"2019-01-01\"\n", "TRAIN_END         = \"2023-12-31\"\n", "BACKTEST_START    = \"2024-01-01\"\n", "BACKTEST_END      = \"2025-01-01\"\n", "\n", "SLOTS_LONG        = 20      # The post specifies 10 slots/positions maximum.\n", "# Note: Short<PERSON> is not used in the backtest\n", "SLOTS_SHORT       = 20      # The post describes a LONG-ONLY strategy.\n", "\n", "# These will be adjusted by the VIX filter\n", "INITIAL_LONG_LEVER  = 1.1\n", "INITIAL_SHORT_LEVER = 0.2\n", "\n", "STOP_PCT          = 0.05\n", "MAX_HOLD_DAYS     = 6\n", "COST_BPS          = 5       # 0.05 %\n", "PROB_THRESH_LONG  = 0.60\n", "PROB_THRESH_SHORT = 0.60\n", "SEED              = 7\n", "\n", "MIN_PRICE         = 5.0\n", "DEBUG = True               # flip to False for silence\n", "\n", "SAMPLE_UNIVERSE_SIZE = None    # Set to 0 or None to use the full universe\n", "# ╰────────────────────────────────────────────────────────────╯"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["YFinanceMarketData initialized. Cache directory: /home/<USER>/w/backtest/yfinance_cache, caching enabled: False\n"]}], "source": ["eastern = pytz.timezone(TZ)\n", "\n", "from marketdata import MarketDataBuilder\n", "from marketdata import YFinanceMarketData\n", "\n", "yfinance_market_data = YFinanceMarketData(enable_cache=False)\n", "\n", "def _daily_builder():\n", "    return (MarketDataBuilder()\n", "            .with_period(\"daily\")\n", "            .with_disk_data(start_date=pd.Timestamp(TRAIN_START))\n", "            .build_market_data())\n", "_daily_md = _daily_builder()\n", "\n", "def get_daily_df(tkr: str, start: datetime, end: datetime) -> pd.DataFrame:\n", "    \"\"\"Cached daily bars wrapper (tz-aware).\"\"\"\n", "    df = _daily_md.gather_historical_data(tkr, start, end, interval=86400)\n", "    return df\n", "\n", "def get_vix_data(start: datetime, end: datetime) -> pd.DataFrame:\n", "    \"\"\"Placeholder to get VIX data.\"\"\"\n", "    # In a real scenario, this would fetch from a data provider\n", "    # For this example, we'll simulate it.\n", "    vix_df = yfinance_market_data.gather_historical_data(\n", "        ticker='^VIX',\n", "        start_dt=start,\n", "        end_dt=end,\n", "        interval=86400\n", "    )\n", "    vix_df.index = vix_df.index.tz_convert('US/Eastern')\n", "    return vix_df\n", "\n", "def dbg(msg: str):\n", "    if DEBUG:\n", "        print(msg)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fetching ticker details:   0%|                                                           | 2/7340 [00:00<06:29, 18.82it/s]Skipping ticker AAV: Not found\n", "Fetching ticker details:   0%|▏                                                         | 18/7340 [00:00<03:04, 39.61it/s]Skipping ticker AAC: Not found\n", "Fetching ticker details:   0%|▏                                                         | 30/7340 [00:00<02:02, 59.92it/s]Skipping ticker AAXN: Not found\n", "Skipping ticker ABC: Not found\n", "Fetching ticker details:   1%|▎                                                         | 47/7340 [00:00<01:22, 88.20it/s]Skipping ticker ABIL: Not found\n", "Skipping ticker ABB: Not found\n", "Skipping ticker ABE: Not found\n", "Fetching ticker details:   1%|▋                                                        | 83/7340 [00:00<00:56, 127.71it/s]Skipping ticker ABX: Not found\n", "Skipping ticker ACH: Not found\n", "Skipping ticker ACC: Not found\n", "Fetching ticker details:   2%|▉                                                       | 116/7340 [00:01<00:51, 141.07it/s]Skipping ticker ACY: Not found\n", "Fetching ticker details:   2%|█                                                       | 133/7340 [00:01<00:49, 146.31it/s]Skipping ticker ADGE: Not found\n", "Skipping ticker ADK: Not found\n", "Fetching ticker details:   2%|█▏                                                      | 151/7340 [00:01<00:46, 154.14it/s]Skipping ticker ADS: Not found\n", "Fetching ticker details:   3%|█▍                                                      | 185/7340 [00:01<00:46, 155.20it/s]Skipping ticker AFCO: Not found\n", "Skipping ticker AET: Not found\n", "Skipping ticker AF: Not found\n", "Skipping ticker AFI: Not found\n", "Fetching ticker details:   3%|█▌                                                      | 203/7340 [00:01<00:44, 161.36it/s]Skipping ticker AGC: Not found\n", "Skipping ticker AGN: Not found\n", "Fetching ticker details:   3%|█▋                                                      | 220/7340 [00:01<00:48, 147.77it/s]Skipping ticker AGU: Not found\n", "Skipping ticker AHS: Not found\n", "Skipping ticker AHP: Not found\n", "Fetching ticker details:   3%|█▊                                                      | 238/7340 [00:01<00:45, 156.34it/s]Skipping ticker AHC: Not found\n", "Skipping ticker AIII: Not found\n", "Fetching ticker details:   3%|█▉                                                      | 254/7340 [00:02<00:46, 151.05it/s]Skipping ticker ACW: Not found\n", "Skipping ticker AKG: Not found\n", "Fetching ticker details:   4%|██                                                      | 273/7340 [00:02<00:45, 155.23it/s]Skipping ticker AKP: Not found\n", "Skipping ticker AKS: Not found\n", "Fetching ticker details:   4%|██▏                                                     | 289/7340 [00:02<00:49, 142.65it/s]Skipping ticker ALDW: Not found\n", "Skipping ticker AJRD: Not found\n", "Skipping ticker AHL: Not found\n", "Fetching ticker details:   4%|██▎                                                     | 306/7340 [00:02<00:48, 145.52it/s]Skipping ticker ALN: Not found\n", "Skipping ticker ALJ: Not found\n", "Fetching ticker details:   4%|██▍                                                     | 327/7340 [00:02<00:43, 160.76it/s]Skipping ticker ALR: Not found\n", "Fetching ticker details:   5%|██▊                                                     | 364/7340 [00:02<00:41, 167.74it/s]Skipping ticker AMBR: Not found\n", "Skipping ticker AMCO: Not found\n", "Skipping ticker AMFW: Not found\n", "Fetching ticker details:   5%|███                                                     | 402/7340 [00:02<00:41, 166.03it/s]Skipping ticker AMTG: Not found\n", "Fetching ticker details:   6%|███▏                                                    | 419/7340 [00:03<00:44, 156.06it/s]Skipping ticker ANFI: Not found\n", "Skipping ticker ANH: Not found\n", "Skipping ticker ANTM: Not found\n", "Skipping ticker ANW: Not found\n", "Skipping ticker AOI: Not found\n", "Fetching ticker details:   6%|███▎                                                    | 439/7340 [00:03<00:42, 161.97it/s]Skipping ticker APB: Not found\n", "Fetching ticker details:   6%|███▍                                                    | 456/7340 [00:03<00:42, 163.74it/s]Skipping ticker APC: Not found\n", "Skipping ticker APF: Not found\n", "Fetching ticker details:   6%|███▌                                                    | 473/7340 [00:03<00:44, 156.05it/s]Skipping ticker APU: Not found\n", "Skipping ticker APTS: Not found\n", "Fetching ticker details:   7%|███▋                                                    | 489/7340 [00:03<00:45, 150.24it/s]Skipping ticker APHB: Not found\n", "Skipping ticker ARA: Not found\n", "Skipping ticker ARCX: Not found\n", "Fetching ticker details:   7%|███▉                                                    | 508/7340 [00:03<00:42, 159.60it/s]Skipping ticker ARG: Not found\n", "Fetching ticker details:   8%|████▎                                                   | 566/7340 [00:03<00:38, 173.95it/s]Skipping ticker AST: Not found\n", "Fetching ticker details:   8%|████▍                                                   | 585/7340 [00:04<00:38, 175.50it/s]Skipping ticker AT: Not found\n", "Fetching ticker details:   8%|████▌                                                   | 605/7340 [00:04<00:39, 172.56it/s]Skipping ticker ATV: Not found\n", "Fetching ticker details:   8%|████▊                                                   | 623/7340 [00:04<00:41, 162.18it/s]Skipping ticker ATTO: Not found\n", "Skipping ticker ATU: Not found\n", "Skipping ticker ATW: Not found\n", "Skipping ticker AV: Not found\n", "Fetching ticker details:   9%|████▉                                                   | 640/7340 [00:04<00:42, 157.08it/s]Skipping ticker AUY: Not found\n", "Skipping ticker AVH: Not found\n", "Skipping ticker AVG: Not found\n", "Fetching ticker details:   9%|█████                                                   | 658/7340 [00:04<00:41, 161.58it/s]Skipping ticker AVP: Not found\n", "Skipping ticker AVX: Not found\n", "Fetching ticker details:   9%|█████▏                                                  | 675/7340 [00:04<00:42, 158.01it/s]Skipping ticker AUO: Not found\n", "Skipping ticker AXE: Not found\n", "Skipping ticker AXN: Not found\n", "Fetching ticker details:   9%|█████▎                                                  | 691/7340 [00:04<00:42, 157.97it/s]Skipping ticker AXLL: Not found\n", "Skipping ticker AXU: Not found\n", "Fetching ticker details:  10%|█████▍                                                  | 711/7340 [00:04<00:39, 169.47it/s]Skipping ticker AYR: Not found\n", "Skipping ticker AZUR: Not found\n", "Skipping ticker BAA: Not found\n", "Fetching ticker details:  10%|█████▌                                                  | 729/7340 [00:05<00:40, 162.81it/s]Skipping ticker BAF: Not found\n", "Skipping ticker BAS: Not found\n", "Fetching ticker details:  10%|█████▋                                                  | 746/7340 [00:05<00:42, 154.56it/s]Skipping ticker BBK: Not found\n", "Skipping ticker BBG: Not found\n", "Skipping ticker BBT: Not found\n", "Skipping ticker BBF: Not found\n", "Fetching ticker details:  10%|█████▊                                                  | 762/7340 [00:05<00:45, 145.96it/s]Skipping ticker BBX: Not found\n", "Skipping ticker BBL: Not found\n", "Skipping ticker BCEI: Not found\n", "Fetching ticker details:  11%|█████▉                                                  | 777/7340 [00:05<00:45, 143.95it/s]Skipping ticker BCR: Not found\n", "Skipping ticker BCRH: Not found\n", "Fetching ticker details:  11%|██████                                                  | 793/7340 [00:05<00:44, 145.92it/s]Skipping ticker BEL: Not found\n", "Fetching ticker details:  11%|██████▏                                                 | 808/7340 [00:05<00:44, 146.79it/s]Skipping ticker BDR: Not found\n", "Fetching ticker details:  11%|██████▍                                                 | 841/7340 [00:05<00:43, 148.77it/s]Skipping ticker BFR: Not found\n", "Skipping ticker BFO: Not found\n", "Skipping ticker BGG: Not found\n", "Fetching ticker details:  12%|██████▌                                                 | 862/7340 [00:05<00:39, 162.20it/s]Skipping ticker BFY: Not found\n", "Fetching ticker details:  12%|██████▋                                                 | 879/7340 [00:06<00:40, 158.39it/s]Skipping ticker BHI: Not found\n", "Skipping ticker BIF: Not found\n", "Skipping ticker BHL: Not found\n", "Skipping ticker BID: Not found\n", "Fetching ticker details:  12%|██████▊                                                 | 895/7340 [00:06<00:41, 155.44it/s]Skipping ticker BIN: Not found\n", "Skipping ticker BITA: Not found\n", "Fetching ticker details:  12%|██████▉                                                 | 911/7340 [00:06<00:44, 145.95it/s]Skipping ticker BJZ: Not found\n", "Skipping ticker BKK: Not found\n", "Fetching ticker details:  13%|███████                                                 | 926/7340 [00:06<00:44, 143.12it/s]Skipping ticker BKJ: Not found\n", "Skipping ticker BKFS: Not found\n", "Skipping ticker BIOA: Not found\n", "Skipping ticker BKS: Not found\n", "Fetching ticker details:  13%|███████▏                                                | 944/7340 [00:06<00:42, 151.19it/s]Skipping ticker BLH: Not found\n", "Skipping ticker BLJ: Not found\n", "Fetching ticker details:  13%|███████▎                                                | 960/7340 [00:06<00:42, 150.68it/s]Skipping ticker BLOX: Not found\n", "Fetching ticker details:  13%|███████▍                                                | 976/7340 [00:06<00:41, 152.88it/s]Skipping ticker BMS: Not found\n", "Skipping ticker BNK: Not found\n", "Fetching ticker details:  14%|███████▌                                                | 992/7340 [00:06<00:42, 150.70it/s]Skipping ticker BLL: Not found\n", "Skipping ticker BOI: Not found\n", "Skipping ticker BORN: Not found\n", "Skipping ticker BOXC: Not found\n", "Fetching ticker details:  14%|███████▌                                               | 1008/7340 [00:06<00:42, 148.44it/s]Skipping ticker BPI: Not found\n", "Skipping ticker BPL: Not found\n", "Skipping ticker BPK: Not found\n", "Skipping ticker BPMX: Not found\n", "Skipping ticker BQH: Not found\n", "Fetching ticker details:  14%|███████▋                                               | 1026/7340 [00:06<00:40, 154.64it/s]Skipping ticker BPY: Not found\n", "Fetching ticker details:  14%|███████▊                                               | 1043/7340 [00:07<00:41, 151.78it/s]Skipping ticker BRG: Not found\n", "Skipping ticker BRS: Not found\n", "Fetching ticker details:  14%|███████▉                                               | 1059/7340 [00:07<00:41, 152.65it/s]Skipping ticker BSD: Not found\n", "Skipping ticker BRSS: Not found\n", "Skipping ticker BSMX: Not found\n", "Skipping ticker BSE: Not found\n", "Fetching ticker details:  15%|████████                                               | 1076/7340 [00:07<00:40, 153.38it/s]Skipping ticker BSI: Not found\n", "Skipping ticker BT: Not found\n", "Fetching ticker details:  15%|████████▏                                              | 1096/7340 [00:07<00:38, 162.19it/s]Skipping ticker BTN: Not found\n", "Skipping ticker BTX: Not found\n", "Fetching ticker details:  15%|████████▎                                              | 1113/7340 [00:07<00:39, 158.17it/s]Skipping ticker BWL.A: Not found\n", "Skipping ticker BVX: Not found\n", "Skipping ticker BXE: Not found\n", "Skipping ticker BWP: Not found\n", "Fetching ticker details:  15%|████████▍                                              | 1129/7340 [00:07<00:40, 151.85it/s]Skipping ticker BXLT: Not found\n", "Skipping ticker BXS: Not found\n", "Skipping ticker CAA: Not found\n", "Fetching ticker details:  16%|████████▌                                              | 1145/7340 [00:07<00:40, 151.11it/s]Skipping ticker CAB: Not found\n", "Skipping ticker BZM: Not found\n", "Fetching ticker details:  16%|████████▋                                              | 1165/7340 [00:07<00:37, 163.45it/s]Skipping ticker CAI: Not found\n", "Skipping ticker CAJ: Not found\n", "Fetching ticker details:  16%|████████▊                                              | 1184/7340 [00:07<00:36, 170.44it/s]Skipping ticker CAS: Not found\n", "Fetching ticker details:  16%|█████████                                              | 1202/7340 [00:08<00:44, 137.86it/s]Skipping ticker CBA: Not found\n", "Skipping ticker CAW: Not found\n", "Skipping ticker CBB: Not found\n", "Skipping ticker CBI: Not found\n", "Skipping ticker CBG: Not found\n", "Skipping ticker CBK: Not found\n", "Skipping ticker CBR: Not found\n", "Fetching ticker details:  17%|█████████                                              | 1217/7340 [00:08<00:45, 134.04it/s]Skipping ticker CBPX: Not found\n", "Skipping ticker CBS: Not found\n", "Skipping ticker CBS.A: Not found\n", "Fetching ticker details:  17%|█████████▏                                             | 1233/7340 [00:08<00:44, 138.73it/s]Skipping ticker CCA: Not found\n", "Skipping ticker CCC: Not found\n", "Fetching ticker details:  17%|█████████▎                                             | 1248/7340 [00:08<00:43, 139.35it/s]Skipping ticker CCE: Not found\n", "Skipping ticker CCF: Not found\n", "Skipping ticker CBM: Not found\n", "Fetching ticker details:  17%|█████████▍                                             | 1263/7340 [00:08<00:43, 138.32it/s]Skipping ticker CCP: Not found\n", "Skipping ticker CDI: Not found\n", "Fetching ticker details:  18%|█████████▋                                             | 1293/7340 [00:08<00:45, 134.02it/s]Skipping ticker CDR: Not found\n", "Skipping ticker CELP: Not found\n", "Skipping ticker CEB: Not found\n", "Skipping ticker CEA: Not found\n", "Skipping ticker CEL: Not found\n", "Fetching ticker details:  18%|█████████▊                                             | 1310/7340 [00:08<00:42, 143.36it/s]Skipping ticker CEQP: Not found\n", "Skipping ticker CEN: Not found\n", "Skipping ticker CEO: Not found\n", "Fetching ticker details:  18%|█████████▉                                             | 1325/7340 [00:09<00:41, 144.30it/s]Skipping ticker CFD: Not found\n", "Skipping ticker CFI: Not found\n", "Fetching ticker details:  18%|██████████                                             | 1341/7340 [00:09<00:41, 146.31it/s]Skipping ticker CFX: Not found\n", "Skipping ticker CGG: Not found\n", "Skipping ticker CGI: Not found\n", "Fetching ticker details:  18%|██████████▏                                            | 1356/7340 [00:09<00:40, 146.78it/s]Skipping ticker CH: Not found\n", "Skipping ticker CHA: Not found\n", "Fetching ticker details:  19%|██████████▎                                            | 1371/7340 [00:09<00:41, 145.56it/s]Skipping ticker CHL: Not found\n", "Skipping ticker CHFS: Not found\n", "Skipping ticker CHMT: Not found\n", "Fetching ticker details:  19%|██████████▍                                            | 1389/7340 [00:09<00:41, 143.84it/s]Skipping ticker CHU: Not found\n", "Skipping ticker CHSP: Not found\n", "Skipping ticker CIE: Not found\n", "Fetching ticker details:  19%|██████████▌                                            | 1404/7340 [00:09<00:40, 145.15it/s]Skipping ticker CINR: Not found\n", "Fetching ticker details:  19%|██████████▋                                            | 1420/7340 [00:09<00:39, 149.10it/s]Skipping ticker CIT: Not found\n", "Skipping ticker CIR: Not found\n", "Skipping ticker CKH: Not found\n", "Skipping ticker CJES: Not found\n", "Skipping ticker CLC: Not found\n", "Fetching ticker details:  20%|██████████▊                                            | 1437/7340 [00:09<00:38, 154.47it/s]Skipping ticker CLD: Not found\n", "Skipping ticker CLGX: Not found\n", "Fetching ticker details:  20%|██████████▉                                            | 1453/7340 [00:09<00:38, 152.54it/s]Skipping ticker CLI: Not found\n", "Fetching ticker details:  20%|███████████                                            | 1470/7340 [00:09<00:37, 155.60it/s]Skipping ticker CLNY: Not found\n", "Skipping ticker CLR: Not found\n", "Fetching ticker details:  20%|███████████▎                                           | 1503/7340 [00:10<00:37, 157.42it/s]Skipping ticker CMN: Not found\n", "Skipping ticker CMO: Not found\n", "Fetching ticker details:  21%|███████████▍                                           | 1523/7340 [00:10<00:34, 167.29it/s]Skipping ticker CNCO: Not found\n", "Fetching ticker details:  21%|███████████▌                                           | 1541/7340 [00:10<00:33, 170.58it/s]Skipping ticker CNNX: Not found\n", "Fetching ticker details:  21%|███████████▋                                           | 1559/7340 [00:10<00:33, 172.71it/s]Skipping ticker CO: Not found\n", "Skipping ticker CNR: Not found\n", "Skipping ticker COH: Not found\n", "Skipping ticker COG: Not found\n", "Fetching ticker details:  22%|███████████▉                                           | 1594/7340 [00:10<00:35, 159.79it/s]Skipping ticker COT: Not found\n", "Skipping ticker CPGX: Not found\n", "Fetching ticker details:  22%|████████████                                           | 1611/7340 [00:10<00:38, 148.29it/s]Skipping ticker COL: Not found\n", "Skipping ticker CPL: Not found\n", "Skipping ticker CPN: Not found\n", "Fetching ticker details:  22%|████████████▏                                          | 1630/7340 [00:10<00:36, 157.79it/s]Skipping ticker CPPL: Not found\n", "Skipping ticker CQH: Not found\n", "Fetching ticker details:  22%|████████████▎                                          | 1650/7340 [00:11<00:33, 169.17it/s]Skipping ticker CRCM: Not found\n", "Skipping ticker CRHM: Not found\n", "Fetching ticker details:  23%|████████████▍                                          | 1668/7340 [00:11<00:32, 172.01it/s]Skipping ticker CRR: Not found\n", "Skipping ticker CRY: Not found\n", "Fetching ticker details:  23%|████████████▋                                          | 1686/7340 [00:11<00:36, 153.82it/s]Skipping ticker CSI: Not found\n", "Skipping ticker CSC: Not found\n", "Skipping ticker CRVP: Not found\n", "Skipping ticker CSLT: Not found\n", "Fetching ticker details:  23%|████████████▊                                          | 1702/7340 [00:11<00:37, 151.68it/s]Skipping ticker CSRA: Not found\n", "Skipping ticker CSS: Not found\n", "Skipping ticker CS: Not found\n", "Skipping ticker CSH: Not found\n", "Fetching ticker details:  23%|████████████▊                                          | 1718/7340 [00:11<00:36, 152.33it/s]Skipping ticker CST: Not found\n", "Skipping ticker CSU: Not found\n", "Fetching ticker details:  24%|████████████▉                                          | 1734/7340 [00:11<00:37, 150.72it/s]Skipping ticker CTB: Not found\n", "Skipping ticker CTF: Not found\n", "Skipping ticker CTL: Not found\n", "Skipping ticker CUB: Not found\n", "Fetching ticker details:  24%|█████████████▎                                          | 1750/7340 [00:12<01:06, 83.46it/s]Skipping ticker CTT: Not found\n", "Fetching ticker details:  24%|█████████████▍                                          | 1763/7340 [00:12<02:28, 37.54it/s]Skipping ticker CUO: Not found\n", "Skipping ticker CUDA: Not found\n", "Fetching ticker details:  24%|█████████████▌                                          | 1773/7340 [00:13<02:08, 43.26it/s]Skipping ticker CVC: Not found\n", "Skipping ticker CVG: Not found\n", "Fetching ticker details:  24%|█████████████▋                                          | 1789/7340 [00:13<01:37, 57.03it/s]Skipping ticker CVA: Not found\n", "Fetching ticker details:  25%|█████████████▊                                          | 1806/7340 [00:13<01:15, 73.03it/s]Skipping ticker CVRS: Not found\n", "Skipping ticker CWEI: Not found\n", "Fetching ticker details:  25%|█████████████▉                                          | 1820/7340 [00:13<01:06, 83.01it/s]Skipping ticker CVO: Not found\n", "Skipping ticker CXO: Not found\n", "Skipping ticker CVT: Not found\n", "Skipping ticker CXP: Not found\n", "Fetching ticker details:  25%|█████████████▊                                         | 1839/7340 [00:13<00:54, 101.52it/s]Skipping ticker CVRR: Not found\n", "Skipping ticker CYS: Not found\n", "Skipping ticker CZZ: Not found\n", "Skipping ticker DAKP: Not found\n", "Fetching ticker details:  26%|██████████████                                         | 1876/7340 [00:13<00:42, 129.07it/s]Skipping ticker DATA: Not found\n", "Skipping ticker DCA: Not found\n", "Skipping ticker DCM: Not found\n", "Fetching ticker details:  26%|██████████████▏                                        | 1892/7340 [00:13<00:45, 120.31it/s]Skipping ticker DANG: Not found\n", "Skipping ticker DDF: Not found\n", "Skipping ticker DDR: Not found\n", "Skipping ticker DCT: Not found\n", "Skipping ticker DDE: Not found\n", "Skipping ticker DELT: Not found\n", "Skipping ticker DEL: Not found\n", "Fetching ticker details:  26%|██████████████▎                                        | 1907/7340 [00:13<00:42, 126.99it/s]Skipping ticker DF: Not found\n", "Skipping ticker DEX: Not found\n", "Skipping ticker DEG: Not found\n", "Fetching ticker details:  26%|██████████████▍                                        | 1922/7340 [00:14<00:41, 130.77it/s]Skipping ticker DFT: Not found\n", "Skipping ticker DHG: Not found\n", "Fetching ticker details:  26%|██████████████▌                                        | 1941/7340 [00:14<00:37, 144.48it/s]Skipping ticker DGSE: Not found\n", "Fetching ticker details:  27%|██████████████▋                                        | 1958/7340 [00:14<00:35, 149.55it/s]Skipping ticker DGI: Not found\n", "Skipping ticker DL: Not found\n", "Fetching ticker details:  27%|██████████████▊                                        | 1976/7340 [00:14<00:34, 157.49it/s]Skipping ticker DLPH: Not found\n", "Skipping ticker DMD: Not found\n", "Fetching ticker details:  27%|██████████████▉                                        | 1999/7340 [00:14<00:30, 177.34it/s]Skipping ticker DNI: Not found\n", "Skipping ticker DNR: Not found\n", "Fetching ticker details:  27%|███████████████                                        | 2018/7340 [00:14<00:31, 167.56it/s]Skipping ticker DPM: Not found\n", "Skipping ticker DPLO: Not found\n", "Skipping ticker DPS: Not found\n", "Skipping ticker DRE: Not found\n", "Fetching ticker details:  28%|███████████████▎                                       | 2036/7340 [00:14<00:32, 160.81it/s]Skipping ticker DPW: Not found\n", "Skipping ticker DRII: Not found\n", "Skipping ticker DSE: Not found\n", "Fetching ticker details:  28%|███████████████▍                                       | 2054/7340 [00:14<00:32, 163.94it/s]Skipping ticker DRA: Not found\n", "Skipping ticker DST: Not found\n", "Skipping ticker DSW: Not found\n", "Fetching ticker details:  28%|███████████████▌                                       | 2073/7340 [00:14<00:30, 170.72it/s]Skipping ticker DUC: Not found\n", "Fetching ticker details:  28%|███████████████▋                                       | 2091/7340 [00:15<00:31, 168.30it/s]Skipping ticker DVD: Not found\n", "Skipping ticker DW: Not found\n", "Skipping ticker DWRE: Not found\n", "Skipping ticker DXI: Not found\n", "Fetching ticker details:  29%|███████████████▉                                       | 2127/7340 [00:15<00:30, 170.31it/s]Skipping ticker ECA: Not found\n", "Skipping ticker ECR: Not found\n", "Skipping ticker ECT: Not found\n", "Skipping ticker ECOM: Not found\n", "Fetching ticker details:  29%|████████████████                                       | 2145/7340 [00:15<00:32, 161.06it/s]Skipping ticker EDE: Not found\n", "Skipping ticker EDI: Not found\n", "Skipping ticker EEQ: Not found\n", "Skipping ticker EEP: Not found\n", "Fetching ticker details:  29%|████████████████▏                                      | 2162/7340 [00:15<00:32, 159.61it/s]Skipping ticker EFF: Not found\n", "Skipping ticker EGAS: Not found\n", "Skipping ticker EGIF: Not found\n", "Skipping ticker EGI: Not found\n", "Fetching ticker details:  30%|████████████████▎                                      | 2179/7340 [00:15<00:35, 146.85it/s]Skipping ticker EGL: Not found\n", "Skipping ticker EGN: Not found\n", "Skipping ticker EHIC: Not found\n", "Skipping ticker EIO: Not found\n", "Fetching ticker details:  30%|████████████████▍                                      | 2194/7340 [00:15<00:34, 147.20it/s]Skipping ticker EIA: Not found\n", "Skipping ticker EJ: Not found\n", "Skipping ticker EIV: Not found\n", "Skipping ticker EIP: Not found\n", "Fetching ticker details:  30%|████████████████▌                                      | 2211/7340 [00:15<00:35, 144.25it/s]Skipping ticker ELLI: Not found\n", "Skipping ticker ELY: Not found\n", "Fetching ticker details:  30%|████████████████▋                                      | 2227/7340 [00:15<00:35, 143.81it/s]Skipping ticker EMG: Not found\n", "Skipping ticker EMAN: Not found\n", "Skipping ticker EMES: Not found\n", "Skipping ticker EMJ: Not found\n", "Fetching ticker details:  31%|████████████████▊                                      | 2243/7340 [00:16<00:35, 144.44it/s]Skipping ticker EMXX: Not found\n", "Skipping ticker ENBL: Not found\n", "Skipping ticker EMI: Not found\n", "Fetching ticker details:  31%|████████████████▉                                      | 2259/7340 [00:16<00:34, 146.34it/s]Skipping ticker ENH: Not found\n", "Skipping ticker ENLK: Not found\n", "Skipping ticker ENRJ: Not found\n", "Fetching ticker details:  31%|█████████████████                                      | 2276/7340 [00:16<00:33, 149.11it/s]Skipping ticker EOCA: Not found\n", "Fetching ticker details:  31%|█████████████████▏                                     | 2291/7340 [00:16<00:35, 143.42it/s]Skipping ticker EOCC: Not found\n", "Skipping ticker ENIA: Not found\n", "Skipping ticker EPE: Not found\n", "Fetching ticker details:  31%|█████████████████▎                                     | 2308/7340 [00:16<00:33, 150.13it/s]Skipping ticker EQGP: Not found\n", "Skipping ticker EQM: Not found\n", "Skipping ticker EQY: Not found\n", "Skipping ticker ERB: Not found\n", "Fetching ticker details:  32%|█████████████████▍                                     | 2324/7340 [00:16<00:32, 152.03it/s]Skipping ticker ERA: Not found\n", "Skipping ticker EROS: Not found\n", "Skipping ticker ERN: Not found\n", "Skipping ticker ESD: Not found\n", "Fetching ticker details:  32%|█████████████████▌                                     | 2340/7340 [00:16<00:33, 150.37it/s]Skipping ticker ESL: Not found\n", "Fetching ticker details:  32%|█████████████████▋                                     | 2356/7340 [00:16<00:34, 142.71it/s]Skipping ticker ESTE: Not found\n", "Skipping ticker ESV: Not found\n", "Skipping ticker ETE: Not found\n", "Skipping ticker ETH: Not found\n", "Fetching ticker details:  32%|█████████████████▊                                     | 2372/7340 [00:16<00:34, 145.90it/s]Skipping ticker ETM: Not found\n", "Skipping ticker ETP: Not found\n", "Fetching ticker details:  33%|█████████████████▉                                     | 2389/7340 [00:17<00:32, 150.83it/s]Skipping ticker ETAK: Not found\n", "Skipping ticker EVDY: Not found\n", "Fetching ticker details:  33%|██████████████████                                     | 2407/7340 [00:17<00:31, 156.65it/s]Skipping ticker EVHC: Not found\n", "Skipping ticker EVJ: Not found\n", "Fetching ticker details:  33%|██████████████████▏                                    | 2423/7340 [00:17<00:31, 154.28it/s]Skipping ticker EVY: Not found\n", "Skipping ticker EXAR: Not found\n", "Skipping ticker EXAM: Not found\n", "Skipping ticker EXD: Not found\n", "Fetching ticker details:  33%|██████████████████▎                                    | 2440/7340 [00:17<00:30, 158.44it/s]Skipping ticker EXTN: Not found\n", "Fetching ticker details:  33%|██████████████████▍                                    | 2456/7340 [00:17<00:30, 158.72it/s]Skipping ticker FAC: Not found\n", "Fetching ticker details:  34%|██████████████████▌                                    | 2472/7340 [00:17<00:32, 147.58it/s]Skipping ticker EVP: Not found\n", "Skipping ticker FAV: Not found\n", "Skipping ticker FBC: Not found\n", "Skipping ticker FBHS: Not found\n", "Skipping ticker FBR: Not found\n", "Fetching ticker details:  34%|██████████████████▋                                    | 2487/7340 [00:17<00:33, 146.48it/s]Skipping ticker FCAU: Not found\n", "Skipping ticker FCB: Not found\n", "Skipping ticker FCE.A: Not found\n", "Skipping ticker FCE.B: Not found\n", "Fetching ticker details:  34%|██████████████████▊                                    | 2504/7340 [00:17<00:31, 151.26it/s]Skipping ticker FCH: Not found\n", "Skipping ticker FDEU: Not found\n", "Fetching ticker details:  34%|██████████████████▉                                    | 2521/7340 [00:17<00:31, 150.97it/s]Skipping ticker FDC: Not found\n", "Skipping ticker FEO: Not found\n", "Skipping ticker FELP: Not found\n", "Fetching ticker details:  35%|███████████████████                                    | 2552/7340 [00:18<00:32, 146.01it/s]Skipping ticker FGL: Not found\n", "Skipping ticker FFG: Not found\n", "Skipping ticker FGP: Not found\n", "Skipping ticker FHY: Not found\n", "Fetching ticker details:  35%|███████████████████▎                                   | 2571/7340 [00:18<00:30, 158.40it/s]Skipping ticker FII: Not found\n", "Skipping ticker FIT: Not found\n", "Fetching ticker details:  35%|███████████████████▌                                   | 2605/7340 [00:18<00:29, 162.78it/s]Skipping ticker FLTX: Not found\n", "Skipping ticker FMSA: Not found\n", "Fetching ticker details:  36%|███████████████████▋                                   | 2624/7340 [00:18<00:27, 169.48it/s]Skipping ticker FMD: Not found\n", "Skipping ticker FLY: Not found\n", "Skipping ticker FMO: Not found\n", "Fetching ticker details:  36%|███████████████████▊                                   | 2641/7340 [00:18<00:28, 165.51it/s]Skipping ticker FOE: Not found\n", "Fetching ticker details:  36%|███████████████████▉                                   | 2658/7340 [00:18<00:30, 155.39it/s]Skipping ticker FPP: Not found\n", "Skipping ticker FPT: Not found\n", "Skipping ticker FPO: Not found\n", "Skipping ticker FRC: Not found\n", "Skipping ticker FNFV: Not found\n", "Fetching ticker details:  36%|████████████████████                                   | 2678/7340 [00:18<00:27, 167.40it/s]Skipping ticker FSB: Not found\n", "Fetching ticker details:  37%|████████████████████▏                                  | 2695/7340 [00:18<00:28, 163.85it/s]Skipping ticker FSIC: Not found\n", "Fetching ticker details:  37%|████████████████████▍                                  | 2730/7340 [00:19<00:28, 161.49it/s]Skipping ticker FUR: Not found\n", "Skipping ticker FVE: Not found\n", "Fetching ticker details:  37%|████████████████████▌                                  | 2752/7340 [00:19<00:26, 176.20it/s]Skipping ticker FXCM: Not found\n", "Fetching ticker details:  38%|████████████████████▊                                  | 2770/7340 [00:19<00:26, 172.14it/s]Skipping ticker GAS: Not found\n", "Skipping ticker GBL: Not found\n", "Fetching ticker details:  38%|████████████████████▉                                  | 2788/7340 [00:19<00:26, 172.49it/s]Skipping ticker GCAP: Not found\n", "Skipping ticker GCH: Not found\n", "Skipping ticker GCP: Not found\n", "Fetching ticker details:  38%|█████████████████████▏                                 | 2825/7340 [00:19<00:27, 166.21it/s]Skipping ticker GDF: Not found\n", "Skipping ticker GEQ: Not found\n", "Skipping ticker GER: Not found\n", "Skipping ticker GFA: Not found\n", "Skipping ticker GG: Not found\n", "Fetching ticker details:  39%|█████████████████████▎                                 | 2842/7340 [00:19<00:28, 156.22it/s]Skipping ticker GGP: Not found\n", "Skipping ticker GHL: Not found\n", "Skipping ticker GFY: Not found\n", "Fetching ticker details:  39%|█████████████████████▍                                 | 2861/7340 [00:19<00:27, 160.92it/s]Skipping ticker GI: Not found\n", "Skipping ticker GIG: Not found\n", "Fetching ticker details:  39%|█████████████████████▌                                 | 2878/7340 [00:20<00:27, 162.73it/s]Skipping ticker GIMO: Not found\n", "Fetching ticker details:  39%|█████████████████████▋                                 | 2895/7340 [00:20<00:30, 145.53it/s]Skipping ticker GLOW: Not found\n", "Skipping ticker GLF: Not found\n", "Skipping ticker GLOG: Not found\n", "Skipping ticker GLOP: Not found\n", "Fetching ticker details:  40%|█████████████████████▉                                 | 2928/7340 [00:20<00:28, 153.24it/s]Skipping ticker GMO: Not found\n", "Skipping ticker GNC: Not found\n", "Skipping ticker GNRT: Not found\n", "Skipping ticker GMT: Not found\n", "Skipping ticker GMZ: Not found\n", "Fetching ticker details:  40%|██████████████████████                                 | 2947/7340 [00:20<00:28, 156.16it/s]Skipping ticker GOV: Not found\n", "Fetching ticker details:  40%|██████████████████████▏                                | 2963/7340 [00:20<00:30, 142.60it/s]Skipping ticker GPM: Not found\n", "Skipping ticker GPL: Not found\n", "Skipping ticker GRA: Not found\n", "Skipping ticker GPT: Not found\n", "Skipping ticker GPX: Not found\n", "Fetching ticker details:  41%|██████████████████████▎                                | 2979/7340 [00:20<00:30, 143.72it/s]Skipping ticker GRO: Not found\n", "Fetching ticker details:  41%|██████████████████████▍                                | 2994/7340 [00:20<00:32, 132.76it/s]Skipping ticker GRR: Not found\n", "Skipping ticker GSB: Not found\n", "Fetching ticker details:  41%|██████████████████████▌                                | 3008/7340 [00:21<00:32, 131.64it/s]Skipping ticker GSH: Not found\n", "Skipping ticker GSS: Not found\n", "Skipping ticker GSV: Not found\n", "Skipping ticker GST: Not found\n", "Fetching ticker details:  41%|██████████████████████▋                                | 3024/7340 [00:21<00:31, 138.92it/s]Skipping ticker GRUB: Not found\n", "Skipping ticker GTS: Not found\n", "Skipping ticker GTT: Not found\n", "Fetching ticker details:  41%|██████████████████████▊                                | 3039/7340 [00:21<00:32, 132.62it/s]Skipping ticker GSI: Not found\n", "Skipping ticker GWR: Not found\n", "Skipping ticker GWB: Not found\n", "Fetching ticker details:  42%|██████████████████████▉                                | 3057/7340 [00:21<00:29, 143.47it/s]Skipping ticker GXP: Not found\n", "Skipping ticker GZT: Not found\n", "Fetching ticker details:  42%|███████████████████████                                | 3074/7340 [00:21<00:28, 150.02it/s]Skipping ticker HAR: Not found\n", "Fetching ticker details:  42%|███████████████████████▏                               | 3090/7340 [00:21<00:30, 141.05it/s]Skipping ticker HCLP: Not found\n", "Skipping ticker HCN: Not found\n", "Skipping ticker HEB: Not found\n", "Fetching ticker details:  42%|███████████████████████▎                               | 3106/7340 [00:21<00:31, 136.40it/s]Skipping ticker HCHC: Not found\n", "Skipping ticker HEP: Not found\n", "Fetching ticker details:  43%|███████████████████████▍                               | 3121/7340 [00:21<00:30, 139.38it/s]Skipping ticker HFC: Not found\n", "Skipping ticker HH: Not found\n", "Skipping ticker HGG: Not found\n", "Skipping ticker HGT: Not found\n", "Fetching ticker details:  43%|███████████████████████▍                               | 3136/7340 [00:21<00:29, 141.52it/s]Skipping ticker HHY: Not found\n", "Skipping ticker HHC: Not found\n", "Skipping ticker HIL: Not found\n", "Fetching ticker details:  43%|███████████████████████▋                               | 3155/7340 [00:22<00:27, 151.65it/s]Skipping ticker HIFR: Not found\n", "Fetching ticker details:  43%|███████████████████████▊                               | 3174/7340 [00:22<00:25, 161.10it/s]Skipping ticker HLS: Not found\n", "Skipping ticker HK: Not found\n", "Skipping ticker HMG: Not found\n", "Fetching ticker details:  43%|███████████████████████▉                               | 3191/7340 [00:22<00:26, 156.80it/s]Skipping ticker HMLP: Not found\n", "Skipping ticker HNP: Not found\n", "Skipping ticker HNR: Not found\n", "Fetching ticker details:  44%|████████████████████████                               | 3208/7340 [00:22<00:26, 158.49it/s]Skipping ticker HOS: Not found\n", "Skipping ticker HOT: Not found\n", "Fetching ticker details:  44%|████████████████████████▏                              | 3227/7340 [00:22<00:24, 165.61it/s]Skipping ticker HRS: Not found\n", "Skipping ticker HPT: Not found\n", "Skipping ticker HRC: Not found\n", "Skipping ticker HRG: Not found\n", "Fetching ticker details:  44%|████████████████████████▎                              | 3244/7340 [00:22<00:25, 157.97it/s]Skipping ticker HSC: Not found\n", "Skipping ticker HT: Not found\n", "Skipping ticker HTA: Not found\n", "Fetching ticker details:  44%|████████████████████████▍                              | 3262/7340 [00:22<00:24, 163.21it/s]Skipping ticker HTM: Not found\n", "Skipping ticker HTS: Not found\n", "Skipping ticker HTR: Not found\n", "Fetching ticker details:  45%|████████████████████████▌                              | 3283/7340 [00:22<00:23, 172.07it/s]Skipping ticker HW: Not found\n", "Fetching ticker details:  45%|████████████████████████▋                              | 3301/7340 [00:22<00:25, 156.46it/s]Skipping ticker HYH: Not found\n", "Skipping ticker HYF: Not found\n", "Skipping ticker I: Not found\n", "Skipping ticker HZN: Not found\n", "Fetching ticker details:  45%|████████████████████████▊                              | 3317/7340 [00:23<00:25, 155.89it/s]Skipping ticker IBA: Not found\n", "Skipping ticker ICB: Not found\n", "Fetching ticker details:  45%|████████████████████████▉                              | 3336/7340 [00:23<00:24, 164.21it/s]Skipping ticker ICA: Not found\n", "Fetching ticker details:  46%|█████████████████████████                              | 3353/7340 [00:23<00:25, 156.72it/s]Skipping ticker IDI: Not found\n", "Skipping ticker IF: Not found\n", "Fetching ticker details:  46%|█████████████████████████▏                             | 3369/7340 [00:23<00:26, 150.77it/s]Skipping ticker IFMI: Not found\n", "Skipping ticker IHC: Not found\n", "Fetching ticker details:  46%|█████████████████████████▎                             | 3385/7340 [00:23<00:26, 148.69it/s]Skipping ticker IEC: Not found\n", "Skipping ticker IID: Not found\n", "Skipping ticker IM: Not found\n", "Skipping ticker IL: Not found\n", "Fetching ticker details:  46%|█████████████████████████▍                             | 3400/7340 [00:23<00:28, 138.16it/s]Skipping ticker IMH: Not found\n", "Skipping ticker IMN: Not found\n", "Skipping ticker IMPR: Not found\n", "Skipping ticker IMPV: Not found\n", "Skipping ticker IMS: Not found\n", "Fetching ticker details:  47%|█████████████████████████▋                             | 3422/7340 [00:23<00:25, 156.08it/s]Skipping ticker INB: Not found\n", "Fetching ticker details:  47%|█████████████████████████▉                             | 3460/7340 [00:23<00:24, 159.41it/s]Skipping ticker INS: Not found\n", "Fetching ticker details:  47%|██████████████████████████                             | 3477/7340 [00:24<00:25, 149.65it/s]Skipping ticker IOC: Not found\n", "Skipping ticker IO: Not found\n", "Skipping ticker INF: Not found\n", "Skipping ticker INT: Not found\n", "Skipping ticker INXN: Not found\n", "Skipping ticker INVN: Not found\n", "Fetching ticker details:  48%|██████████████████████████▏                            | 3496/7340 [00:24<00:24, 159.70it/s]Skipping ticker IPHI: Not found\n", "Fetching ticker details:  48%|██████████████████████████▎                            | 3513/7340 [00:24<00:23, 159.73it/s]Skipping ticker IRR: Not found\n", "Skipping ticker ISL: Not found\n", "Fetching ticker details:  48%|██████████████████████████▍                            | 3530/7340 [00:24<00:26, 142.68it/s]Skipping ticker IRET: Not found\n", "Skipping ticker IRL: Not found\n", "Skipping ticker ISR: Not found\n", "Skipping ticker ITCB: Not found\n", "Skipping ticker ITG: Not found\n", "Fetching ticker details:  48%|██████████████████████████▌                            | 3545/7340 [00:24<00:26, 144.20it/s]Skipping ticker ITC: Not found\n", "Skipping ticker IVC: Not found\n", "Fetching ticker details:  49%|██████████████████████████▋                            | 3564/7340 [00:24<00:24, 154.01it/s]Skipping ticker IVH: Not found\n", "Fetching ticker details:  49%|██████████████████████████▊                            | 3580/7340 [00:24<00:29, 126.25it/s]Skipping ticker JAX: Not found\n", "Skipping ticker JCAP: Not found\n", "Skipping ticker JDD: Not found\n", "Skipping ticker JCP: Not found\n", "Skipping ticker JFC: Not found\n", "Skipping ticker JEC: Not found\n", "Skipping ticker JHA: Not found\n", "Fetching ticker details:  49%|██████████████████████████▉                            | 3594/7340 [00:24<00:31, 119.05it/s]Skipping ticker JGV: Not found\n", "Skipping ticker JHD: Not found\n", "Skipping ticker JGW: Not found\n", "Skipping ticker JE: Not found\n", "Skipping ticker JHY: Not found\n", "Fetching ticker details:  49%|███████████████████████████                            | 3607/7340 [00:25<00:30, 120.48it/s]Skipping ticker JMF: Not found\n", "Skipping ticker JMEI: Not found\n", "Skipping ticker JMT: Not found\n", "Skipping ticker JMLP: Not found\n", "Skipping ticker JMP: Not found\n", "Skipping ticker JNS: Not found\n", "Fetching ticker details:  49%|███████████████████████████▏                           | 3620/7340 [00:25<00:31, 119.86it/s]Skipping ticker JOY: Not found\n", "Skipping ticker JP: Not found\n", "Skipping ticker JONE: Not found\n", "Skipping ticker JRJR: Not found\n", "Fetching ticker details:  49%|███████████████████████████▏                           | 3633/7340 [00:25<00:34, 108.08it/s]Skipping ticker JRO: Not found\n", "Skipping ticker JPW: Not found\n", "Skipping ticker JSD: Not found\n", "Fetching ticker details:  50%|███████████████████████████▎                           | 3647/7340 [00:25<00:32, 115.25it/s]Skipping ticker JW.A: Not found\n", "Skipping ticker JTD: Not found\n", "Skipping ticker JTA: Not found\n", "Skipping ticker JPEP: Not found\n", "Fetching ticker details:  50%|███████████████████████████▍                           | 3664/7340 [00:25<00:28, 128.48it/s]Skipping ticker JPS: Not found\n", "Fetching ticker details:  50%|███████████████████████████▌                           | 3679/7340 [00:25<00:27, 134.22it/s]Skipping ticker JW.B: Not found\n", "Skipping ticker KED: Not found\n", "Skipping ticker KATE: Not found\n", "Fetching ticker details:  50%|███████████████████████████▋                           | 3696/7340 [00:25<00:25, 142.66it/s]Skipping ticker KCG: Not found\n", "Skipping ticker KEG: Not found\n", "Skipping ticker KBSF: Not found\n", "Skipping ticker KEF: Not found\n", "Fetching ticker details:  51%|███████████████████████████▊                           | 3717/7340 [00:25<00:23, 157.34it/s]Skipping ticker KKD: Not found\n", "Fetching ticker details:  51%|███████████████████████████▉                           | 3733/7340 [00:25<00:23, 156.54it/s]Skipping ticker KMM: Not found\n", "Skipping ticker KHI: Not found\n", "Skipping ticker KMF: Not found\n", "Skipping ticker KND: Not found\n", "Fetching ticker details:  51%|████████████████████████████                           | 3751/7340 [00:26<00:22, 159.71it/s]Skipping ticker KNL: Not found\n", "Skipping ticker KLDX: Not found\n", "Skipping ticker KMG: Not found\n", "Fetching ticker details:  51%|████████████████████████████▏                          | 3769/7340 [00:26<00:21, 162.45it/s]Skipping ticker KORS: Not found\n", "Skipping ticker KRA: Not found\n", "Fetching ticker details:  52%|████████████████████████████▍                          | 3802/7340 [00:26<00:23, 153.68it/s]Skipping ticker KSU: Not found\n", "Skipping ticker KST: Not found\n", "Skipping ticker KS: Not found\n", "Fetching ticker details:  52%|████████████████████████████▋                          | 3822/7340 [00:26<00:21, 164.22it/s]Skipping ticker KYE: Not found\n", "Skipping ticker KYO: Not found\n", "Fetching ticker details:  52%|████████████████████████████▊                          | 3839/7340 [00:26<00:22, 157.60it/s]Skipping ticker LAQ: Not found\n", "Skipping ticker LB: Not found\n", "Fetching ticker details:  53%|████████████████████████████▉                          | 3855/7340 [00:26<00:22, 154.16it/s]Skipping ticker LBF: Not found\n", "Skipping ticker LBY: Not found\n", "Fetching ticker details:  53%|█████████████████████████████                          | 3871/7340 [00:26<00:22, 154.00it/s]Skipping ticker LCM: Not found\n", "Skipping ticker LCI: Not found\n", "Skipping ticker LDR: Not found\n", "Skipping ticker LDL: Not found\n", "Skipping ticker LDF: Not found\n", "Fetching ticker details:  53%|█████████████████████████████▏                         | 3887/7340 [00:26<00:22, 155.56it/s]Skipping ticker LEI: Not found\n", "Skipping ticker LFC: Not found\n", "Fetching ticker details:  53%|█████████████████████████████▏                         | 3903/7340 [00:27<00:22, 154.43it/s]Skipping ticker LFL: Not found\n", "Skipping ticker LGF: Not found\n", "Fetching ticker details:  53%|█████████████████████████████▎                         | 3920/7340 [00:27<00:21, 155.46it/s]Skipping ticker LHO: Not found\n", "Fetching ticker details:  54%|█████████████████████████████▋                         | 3957/7340 [00:27<00:20, 164.11it/s]Skipping ticker LLL: Not found\n", "Skipping ticker LM: Not found\n", "Fetching ticker details:  54%|█████████████████████████████▊                         | 3974/7340 [00:27<00:20, 160.48it/s]Skipping ticker LNKD: Not found\n", "Fetching ticker details:  54%|█████████████████████████████▉                         | 3991/7340 [00:27<00:22, 151.52it/s]Skipping ticker LOV: Not found\n", "Skipping ticker LOCK: Not found\n", "Skipping ticker LQ: Not found\n", "Fetching ticker details:  55%|██████████████████████████████                         | 4007/7340 [00:27<00:21, 153.81it/s]Skipping ticker LPT: Not found\n", "Skipping ticker LOR: Not found\n", "Skipping ticker LPI: Not found\n", "Fetching ticker details:  55%|██████████████████████████████▏                        | 4023/7340 [00:27<00:22, 150.49it/s]Skipping ticker LTS: Not found\n", "Fetching ticker details:  55%|██████████████████████████████▎                        | 4039/7340 [00:27<00:21, 150.94it/s]Skipping ticker LUB: Not found\n", "Skipping ticker LUK: Not found\n", "Skipping ticker LVLT: Not found\n", "Fetching ticker details:  55%|██████████████████████████████▍                        | 4057/7340 [00:28<00:20, 158.47it/s]Skipping ticker LXK: Not found\n", "Skipping ticker LXFT: Not found\n", "Fetching ticker details:  56%|██████████████████████████████▋                        | 4095/7340 [00:28<00:19, 169.50it/s]Skipping ticker MAB: Not found\n", "Fetching ticker details:  56%|██████████████████████████████▊                        | 4113/7340 [00:28<00:19, 162.67it/s]Skipping ticker MAXR: Not found\n", "Skipping ticker MBT: Not found\n", "Fetching ticker details:  56%|██████████████████████████████▉                        | 4131/7340 [00:28<00:19, 165.12it/s]Skipping ticker MCA: Not found\n", "Fetching ticker details:  57%|███████████████████████████████                        | 4148/7340 [00:28<00:20, 154.63it/s]Skipping ticker MCRN: Not found\n", "Skipping ticker MCC: Not found\n", "Skipping ticker MCF: Not found\n", "Skipping ticker MCZ: Not found\n", "Skipping ticker MDGN: Not found\n", "Fetching ticker details:  57%|███████████████████████████████▏                       | 4164/7340 [00:28<00:20, 151.35it/s]Skipping ticker MDP: Not found\n", "Skipping ticker MDR: Not found\n", "Fetching ticker details:  57%|███████████████████████████████▎                       | 4180/7340 [00:28<00:21, 148.63it/s]Skipping ticker MEN: Not found\n", "Skipping ticker MDLY: Not found\n", "Fetching ticker details:  57%|███████████████████████████████▍                       | 4195/7340 [00:28<00:22, 138.41it/s]Skipping ticker MEP: Not found\n", "Skipping ticker MFS: Not found\n", "Skipping ticker MFL: Not found\n", "Skipping ticker MFT: Not found\n", "Fetching ticker details:  57%|███████████████████████████████▌                       | 4209/7340 [00:29<00:22, 136.70it/s]Skipping ticker MGH: Not found\n", "Skipping ticker MGN: Not found\n", "Skipping ticker MGP: Not found\n", "Skipping ticker MGT: Not found\n", "Skipping ticker MGU: Not found\n", "Fetching ticker details:  58%|███████████████████████████████▋                       | 4223/7340 [00:29<00:25, 122.77it/s]Skipping ticker MFCB: Not found\n", "Skipping ticker MHE: Not found\n", "Skipping ticker MHG: Not found\n", "Fetching ticker details:  58%|███████████████████████████████▊                       | 4239/7340 [00:29<00:24, 127.29it/s]Skipping ticker MIC: Not found\n", "Skipping ticker MHY: Not found\n", "Fetching ticker details:  58%|████████████████████████████████                       | 4272/7340 [00:29<00:21, 140.83it/s]Skipping ticker MJCO: Not found\n", "Skipping ticker MJN: Not found\n", "Fetching ticker details:  58%|████████████████████████████████                       | 4287/7340 [00:29<00:21, 141.05it/s]Skipping ticker MIE: Not found\n", "Skipping ticker MMP: Not found\n", "Skipping ticker MIW: Not found\n", "Fetching ticker details:  59%|████████████████████████████████▎                      | 4305/7340 [00:29<00:20, 149.60it/s]Skipping ticker MNK: Not found\n", "Skipping ticker MNI: Not found\n", "Skipping ticker MNP: Not found\n", "Skipping ticker MNE: Not found\n", "Skipping ticker MN: Not found\n", "Fetching ticker details:  59%|████████████████████████████████▍                      | 4321/7340 [00:29<00:19, 151.48it/s]Skipping ticker MOC: Not found\n", "Fetching ticker details:  59%|████████████████████████████████▍                      | 4337/7340 [00:29<00:21, 140.34it/s]Skipping ticker MON: Not found\n", "Skipping ticker MORE: Not found\n", "Fetching ticker details:  59%|████████████████████████████████▋                      | 4354/7340 [00:30<00:20, 142.60it/s]Skipping ticker MPG: Not found\n", "Skipping ticker MPSX: Not found\n", "Fetching ticker details:  60%|████████████████████████████████▉                      | 4390/7340 [00:30<00:21, 138.48it/s]Skipping ticker MSGN: Not found\n", "Fetching ticker details:  60%|█████████████████████████████████                      | 4405/7340 [00:30<00:21, 136.60it/s]Skipping ticker MSF: Not found\n", "Skipping ticker MSL: Not found\n", "Skipping ticker MSP: Not found\n", "Skipping ticker MSTX: Not found\n", "Fetching ticker details:  60%|█████████████████████████████████▎                     | 4438/7340 [00:30<00:20, 141.63it/s]Skipping ticker MTL: Not found\n", "Skipping ticker MSG: Not found\n", "Skipping ticker MTOR: Not found\n", "Skipping ticker MTT: Not found\n", "Fetching ticker details:  61%|█████████████████████████████████▍                     | 4456/7340 [00:30<00:20, 143.28it/s]Skipping ticker MUS: Not found\n", "Skipping ticker MUH: Not found\n", "Skipping ticker MVC: Not found\n", "Skipping ticker MVG: Not found\n", "Fetching ticker details:  61%|█████████████████████████████████▌                     | 4471/7340 [00:30<00:21, 130.85it/s]Skipping ticker MWW: Not found\n", "Skipping ticker MTU: Not found\n", "Skipping ticker MYCC: Not found\n", "Skipping ticker MY: Not found\n", "Skipping ticker MYC: Not found\n", "Fetching ticker details:  61%|█████████████████████████████████▌                     | 4485/7340 [00:31<00:22, 128.14it/s]Skipping ticker MYF: Not found\n", "Skipping ticker MYJ: Not found\n", "Skipping ticker MZF: Not found\n", "Skipping ticker N: Not found\n", "Skipping ticker MZA: Not found\n", "Skipping ticker NADL: Not found\n", "Fetching ticker details:  61%|█████████████████████████████████▋                     | 4500/7340 [00:31<00:21, 133.65it/s]Skipping ticker NAO: Not found\n", "Fetching ticker details:  62%|█████████████████████████████████▊                     | 4515/7340 [00:31<00:20, 135.26it/s]Skipping ticker NAV: Not found\n", "Skipping ticker NAP: Not found\n", "Skipping ticker NBD: Not found\n", "Fetching ticker details:  62%|█████████████████████████████████▉                     | 4529/7340 [00:31<00:22, 124.42it/s]Skipping ticker NBL: Not found\n", "Skipping ticker NBO: Not found\n", "Skipping ticker NBW: Not found\n", "Skipping ticker NCB: Not found\n", "Fetching ticker details:  62%|██████████████████████████████████                     | 4543/7340 [00:31<00:21, 128.25it/s]Skipping ticker NCR: Not found\n", "Skipping ticker NCI: Not found\n", "Skipping ticker NCT: Not found\n", "Skipping ticker NCS: Not found\n", "Skipping ticker NCQ: Not found\n", "Fetching ticker details:  62%|██████████████████████████████████▎                    | 4578/7340 [00:31<00:19, 140.02it/s]Skipping ticker NEFF: Not found\n", "Skipping ticker NEV: Not found\n", "Fetching ticker details:  63%|██████████████████████████████████▍                    | 4596/7340 [00:31<00:18, 150.26it/s]Skipping ticker NDRO: Not found\n", "Skipping ticker NEWR: Not found\n", "Skipping ticker NFX: Not found\n", "Skipping ticker NEWM: Not found\n", "Fetching ticker details:  63%|██████████████████████████████████▌                    | 4612/7340 [00:31<00:19, 142.32it/s]Skipping ticker NHF: Not found\n", "Skipping ticker NHA: Not found\n", "Fetching ticker details:  63%|██████████████████████████████████▋                    | 4627/7340 [00:32<00:20, 129.74it/s]Skipping ticker NID: Not found\n", "Skipping ticker NKA: Not found\n", "Skipping ticker NJV: Not found\n", "Skipping ticker NIQ: Not found\n", "Skipping ticker NKG: Not found\n", "Fetching ticker details:  63%|██████████████████████████████████▊                    | 4641/7340 [00:32<00:20, 129.37it/s]Skipping ticker NLS: Not found\n", "Skipping ticker NMBL: Not found\n", "Skipping ticker NLSN: Not found\n", "Skipping ticker NM: Not found\n", "Fetching ticker details:  63%|██████████████████████████████████▉                    | 4655/7340 [00:32<00:20, 132.02it/s]Skipping ticker NM: Not found\n", "Skipping ticker NMO: Not found\n", "Fetching ticker details:  64%|███████████████████████████████████                    | 4672/7340 [00:32<00:18, 141.13it/s]Skipping ticker NNA: Not found\n", "Skipping ticker NMY: Not found\n", "Fetching ticker details:  64%|███████████████████████████████████                    | 4687/7340 [00:32<00:22, 116.74it/s]Skipping ticker NPF: Not found\n", "Skipping ticker NPD: Not found\n", "Skipping ticker NPM: Not found\n", "Skipping ticker NP: Not found\n", "Skipping ticker NPI: Not found\n", "Skipping ticker NPP: Not found\n", "Skipping ticker NPN: Not found\n", "Fetching ticker details:  64%|███████████████████████████████████▏                   | 4702/7340 [00:32<00:21, 123.47it/s]Skipping ticker NNC: Not found\n", "Skipping ticker NPTN: Not found\n", "Skipping ticker NQM: Not found\n", "Skipping ticker NQS: Not found\n", "Skipping ticker NQ: Not found\n", "Fetching ticker details:  64%|███████████████████████████████████▍                   | 4722/7340 [00:32<00:18, 140.03it/s]Skipping ticker NRF: Not found\n", "Skipping ticker NRE: Not found\n", "Fetching ticker details:  65%|███████████████████████████████████▍                   | 4737/7340 [00:32<00:20, 130.10it/s]Skipping ticker NRZ: Not found\n", "Skipping ticker NSAM: Not found\n", "Skipping ticker NSL: Not found\n", "Skipping ticker NSH: Not found\n", "Skipping ticker NSR: Not found\n", "Fetching ticker details:  65%|███████████████████████████████████▌                   | 4751/7340 [00:33<00:20, 127.27it/s]Skipping ticker NSM: Not found\n", "Skipping ticker NSU: Not found\n", "Skipping ticker NTI: Not found\n", "Skipping ticker NTC: Not found\n", "Fetching ticker details:  65%|███████████████████████████████████▋                   | 4766/7340 [00:33<00:19, 131.91it/s]Skipping ticker NSAT: Not found\n", "Skipping ticker NTP: Not found\n", "Skipping ticker NTN: Not found\n", "Fetching ticker details:  65%|███████████████████████████████████▊                   | 4780/7340 [00:33<00:19, 130.26it/s]Skipping ticker NTL: Not found\n", "Skipping ticker NTT: Not found\n", "Skipping ticker NTX: Not found\n", "Fetching ticker details:  66%|████████████████████████████████████                   | 4820/7340 [00:33<00:16, 157.23it/s]Skipping ticker NUO: Not found\n", "Fetching ticker details:  66%|████████████████████████████████████▏                  | 4837/7340 [00:33<00:16, 150.12it/s]Skipping ticker NUM: Not found\n", "Skipping ticker NWHM: Not found\n", "Skipping ticker NWY: Not found\n", "Skipping ticker NXR: Not found\n", "Fetching ticker details:  66%|████████████████████████████████████▎                  | 4853/7340 [00:33<00:17, 145.46it/s]Skipping ticker NXQ: Not found\n", "Skipping ticker NYLD: Not found\n", "Skipping ticker NYRT: Not found\n", "Skipping ticker NYLD.A: Not found\n", "Skipping ticker NYH: Not found\n", "Fetching ticker details:  66%|████████████████████████████████████▍                  | 4868/7340 [00:33<00:16, 146.65it/s]Skipping ticker NYV: Not found\n", "Skipping ticker OAK: Not found\n", "Skipping ticker NZH: Not found\n", "Skipping ticker OAS: Not found\n", "Skipping ticker OAKS: Not found\n", "Skipping ticker OA: Not found\n", "Fetching ticker details:  67%|████████████████████████████████████▋                  | 4903/7340 [00:34<00:15, 154.15it/s]Skipping ticker OCIP: Not found\n", "Fetching ticker details:  67%|████████████████████████████████████▊                  | 4919/7340 [00:34<00:15, 154.59it/s]Skipping ticker OIBR.C: Not found\n", "Fetching ticker details:  67%|████████████████████████████████████▉                  | 4937/7340 [00:34<00:14, 161.21it/s]Skipping ticker OKS: Not found\n", "Fetching ticker details:  67%|█████████████████████████████████████                  | 4954/7340 [00:34<00:14, 159.56it/s]Skipping ticker OMAM: Not found\n", "Skipping ticker OME: Not found\n", "Fetching ticker details:  68%|█████████████████████████████████████▏                 | 4970/7340 [00:34<00:16, 144.69it/s]Skipping ticker OMN: Not found\n", "Skipping ticker OFC: Not found\n", "Skipping ticker ONDK: Not found\n", "Skipping ticker ONE: Not found\n", "Skipping ticker ONP: Not found\n", "Fetching ticker details:  69%|█████████████████████████████████████▋                 | 5029/7340 [00:34<00:13, 167.18it/s]Skipping ticker OPWR: Not found\n", "Skipping ticker ORM: Not found\n", "Skipping ticker OSB: Not found\n", "Fetching ticker details:  69%|█████████████████████████████████████▊                 | 5050/7340 [00:34<00:12, 178.58it/s]Skipping ticker OSGB: Not found\n", "Fetching ticker details:  69%|█████████████████████████████████████▉                 | 5069/7340 [00:35<00:12, 175.91it/s]Skipping ticker OZM: Not found\n", "Skipping ticker P: Not found\n", "Skipping ticker PACD: Not found\n", "Fetching ticker details:  69%|██████████████████████████████████████▏                | 5089/7340 [00:35<00:12, 180.16it/s]Skipping ticker PAH: Not found\n", "Fetching ticker details:  70%|██████████████████████████████████████▍                | 5127/7340 [00:35<00:12, 177.78it/s]Skipping ticker PBFX: Not found\n", "Fetching ticker details:  70%|██████████████████████████████████████▌                | 5146/7340 [00:35<00:12, 176.39it/s]Skipping ticker PCI: Not found\n", "Skipping ticker PE: Not found\n", "Fetching ticker details:  70%|██████████████████████████████████████▋                | 5167/7340 [00:35<00:11, 185.79it/s]Skipping ticker PES: Not found\n", "Fetching ticker details:  71%|██████████████████████████████████████▊                | 5186/7340 [00:35<00:11, 182.64it/s]Skipping ticker PER: Not found\n", "Skipping ticker PEI: Not found\n", "Skipping ticker PF: Not found\n", "Skipping ticker PFNX: Not found\n", "Fetching ticker details:  71%|███████████████████████████████████████                | 5205/7340 [00:35<00:12, 169.82it/s]Skipping ticker PGEM: Not found\n", "Skipping ticker PGND: Not found\n", "Fetching ticker details:  71%|███████████████████████████████████████▏               | 5224/7340 [00:35<00:12, 168.31it/s]Skipping ticker PHF: Not found\n", "Skipping ticker PHCF: Not found\n", "Skipping ticker PGH: Not found\n", "Skipping ticker PHH: Not found\n", "Fetching ticker details:  71%|███████████████████████████████████████▎               | 5241/7340 [00:36<00:13, 153.30it/s]Skipping ticker PIP: Not found\n", "Skipping ticker PIR: Not found\n", "Skipping ticker PJC: Not found\n", "Skipping ticker PKD: Not found\n", "Fetching ticker details:  72%|███████████████████████████████████████▍               | 5257/7340 [00:36<00:13, 152.51it/s]Skipping ticker PKI: Not found\n", "Skipping ticker PKO: Not found\n", "Skipping ticker PKY: Not found\n", "Fetching ticker details:  72%|███████████████████████████████████████▌               | 5275/7340 [00:36<00:13, 151.34it/s]Skipping ticker PLM: Not found\n", "Fetching ticker details:  72%|███████████████████████████████████████▋               | 5291/7340 [00:36<00:13, 153.05it/s]Skipping ticker PMC: Not found\n", "Skipping ticker PLT: Not found\n", "Fetching ticker details:  72%|███████████████████████████████████████▊               | 5312/7340 [00:36<00:12, 164.22it/s]Skipping ticker PN: Not found\n", "Skipping ticker PNX: Not found\n", "Fetching ticker details:  73%|███████████████████████████████████████▉               | 5329/7340 [00:36<00:13, 149.40it/s]Skipping ticker PNY: Not found\n", "Skipping ticker POT: Not found\n", "Fetching ticker details:  73%|████████████████████████████████████████               | 5346/7340 [00:36<00:12, 153.61it/s]Skipping ticker PPP: Not found\n", "Skipping ticker PPR: Not found\n", "Skipping ticker PPS: Not found\n", "Skipping ticker PQ: Not found\n", "Fetching ticker details:  74%|████████████████████████████████████████▌              | 5405/7340 [00:37<00:11, 171.88it/s]Skipping ticker PSB: Not found\n", "Fetching ticker details:  74%|████████████████████████████████████████▋              | 5423/7340 [00:37<00:11, 165.00it/s]Skipping ticker PSG: Not found\n", "Skipping ticker PTR: Not found\n", "Fetching ticker details:  74%|████████████████████████████████████████▊              | 5440/7340 [00:37<00:11, 164.94it/s]Skipping ticker PSXP: Not found\n", "Skipping ticker PWE: Not found\n", "Skipping ticker PRTY: Not found\n", "Skipping ticker PVG: Not found\n", "Fetching ticker details:  74%|████████████████████████████████████████▉              | 5457/7340 [00:37<00:11, 161.34it/s]Skipping ticker PZN: Not found\n", "Fetching ticker details:  75%|█████████████████████████████████████████              | 5474/7340 [00:37<00:12, 153.80it/s]Skipping ticker Q: Not found\n", "Skipping ticker QEP: Not found\n", "Skipping ticker PZE: Not found\n", "Skipping ticker QHC: Not found\n", "Skipping ticker QIHU: Not found\n", "Fetching ticker details:  75%|█████████████████████████████████████████▏             | 5490/7340 [00:37<00:11, 154.40it/s]Skipping ticker QTS: Not found\n", "Skipping ticker QTM: Not found\n", "Skipping ticker QUOT: Not found\n", "Fetching ticker details:  75%|█████████████████████████████████████████▎             | 5506/7340 [00:37<00:12, 151.22it/s]Skipping ticker RAD: Not found\n", "Skipping ticker RAI: Not found\n", "Fetching ticker details:  75%|█████████████████████████████████████████▍             | 5522/7340 [00:37<00:12, 142.31it/s]Skipping ticker RAX: Not found\n", "Skipping ticker RAS: Not found\n", "Fetching ticker details:  75%|█████████████████████████████████████████▌             | 5540/7340 [00:37<00:11, 150.96it/s]Skipping ticker RBS: Not found\n", "Fetching ticker details:  76%|█████████████████████████████████████████▋             | 5556/7340 [00:38<00:11, 151.00it/s]Skipping ticker RDC: Not found\n", "Skipping ticker RDS.B: Not found\n", "Skipping ticker RDS.A: Not found\n", "Fetching ticker details:  76%|█████████████████████████████████████████▊             | 5572/7340 [00:38<00:13, 133.62it/s]Skipping ticker RE: Not found\n", "Skipping ticker RENX: Not found\n", "Skipping ticker REN: Not found\n", "Skipping ticker REV: Not found\n", "Fetching ticker details:  76%|█████████████████████████████████████████▉             | 5592/7340 [00:38<00:11, 150.15it/s]Skipping ticker RESI: Not found\n", "Skipping ticker RENN: Not found\n", "Fetching ticker details:  77%|██████████████████████████████████████████▏            | 5630/7340 [00:38<00:10, 160.16it/s]Skipping ticker RIC: Not found\n", "Skipping ticker RHT: Not found\n", "Skipping ticker RICE: Not found\n", "Skipping ticker RIF: Not found\n", "Skipping ticker RIGP: Not found\n", "Skipping ticker RIT: Not found\n", "Skipping ticker RFP: Not found\n", "Fetching ticker details:  77%|██████████████████████████████████████████▎            | 5647/7340 [00:38<00:10, 155.36it/s]Skipping ticker RKUS: Not found\n", "Fetching ticker details:  77%|██████████████████████████████████████████▍            | 5666/7340 [00:38<00:10, 162.61it/s]Skipping ticker RLGY: Not found\n", "Skipping ticker RMP: Not found\n", "Fetching ticker details:  77%|██████████████████████████████████████████▌            | 5685/7340 [00:38<00:10, 163.93it/s]Skipping ticker RLH: Not found\n", "Skipping ticker RNN: Not found\n", "Fetching ticker details:  78%|██████████████████████████████████████████▋            | 5705/7340 [00:39<00:10, 160.00it/s]Skipping ticker ROX: Not found\n", "Skipping ticker RPAI: Not found\n", "Skipping ticker ROYT: Not found\n", "Fetching ticker details:  78%|██████████████████████████████████████████▉            | 5722/7340 [00:39<00:10, 152.03it/s]Skipping ticker RRMS: Not found\n", "Skipping ticker RSO: Not found\n", "Skipping ticker RSE: Not found\n", "Skipping ticker RT: Not found\n", "Skipping ticker RSPP: Not found\n", "Fetching ticker details:  78%|██████████████████████████████████████████▉            | 5738/7340 [00:39<00:11, 141.75it/s]Skipping ticker RTEC: Not found\n", "Skipping ticker RUBI: Not found\n", "Fetching ticker details:  78%|███████████████████████████████████████████            | 5753/7340 [00:39<00:11, 143.54it/s]Skipping ticker RTN: Not found\n", "Fetching ticker details:  79%|███████████████████████████████████████████▎           | 5773/7340 [00:39<00:10, 152.59it/s]Skipping ticker RXN: Not found\n", "Skipping ticker RWC: Not found\n", "Fetching ticker details:  79%|███████████████████████████████████████████▍           | 5792/7340 [00:39<00:09, 160.88it/s]Skipping ticker RST: Not found\n", "Fetching ticker details:  79%|███████████████████████████████████████████▌           | 5811/7340 [00:39<00:09, 168.37it/s]Skipping ticker SALT: Not found\n", "Fetching ticker details:  79%|███████████████████████████████████████████▋           | 5829/7340 [00:39<00:09, 159.78it/s]Skipping ticker SBW: Not found\n", "Skipping ticker SBY: Not found\n", "Fetching ticker details:  80%|███████████████████████████████████████████▊           | 5846/7340 [00:39<00:09, 150.88it/s]Skipping ticker SBGL: Not found\n", "Skipping ticker SCG: Not found\n", "Skipping ticker SC: Not found\n", "Skipping ticker SCNB: Not found\n", "Fetching ticker details:  80%|███████████████████████████████████████████▉           | 5864/7340 [00:40<00:09, 156.25it/s]Skipping ticker SDLP: Not found\n", "Fetching ticker details:  80%|████████████████████████████████████████████           | 5883/7340 [00:40<00:08, 164.88it/s]Skipping ticker SDT: Not found\n", "Skipping ticker SDR: Not found\n", "Skipping ticker SEMG: Not found\n", "Fetching ticker details:  80%|████████████████████████████████████████████▏          | 5900/7340 [00:40<00:09, 159.16it/s]Skipping ticker SERV: Not found\n", "Skipping ticker SFUN: Not found\n", "Skipping ticker SFR: Not found\n", "Fetching ticker details:  81%|████████████████████████████████████████████▎          | 5917/7340 [00:40<00:09, 143.17it/s]Skipping ticker SGF: Not found\n", "Skipping ticker SGB: Not found\n", "Skipping ticker SFS: Not found\n", "Skipping ticker SGM: Not found\n", "Skipping ticker SGL: Not found\n", "Fetching ticker details:  81%|████████████████████████████████████████████▍          | 5936/7340 [00:40<00:09, 154.85it/s]Skipping ticker SGY: Not found\n", "Skipping ticker SEP: Not found\n", "Fetching ticker details:  81%|████████████████████████████████████████████▋          | 5960/7340 [00:40<00:07, 177.56it/s]Skipping ticker SHI: Not found\n", "Skipping ticker SHLX: Not found\n", "Fetching ticker details:  82%|████████████████████████████████████████████▉          | 6000/7340 [00:40<00:07, 167.82it/s]Skipping ticker SJR: Not found\n", "Skipping ticker SJI: Not found\n", "Fetching ticker details:  82%|█████████████████████████████████████████████▏         | 6025/7340 [00:40<00:07, 186.05it/s]Skipping ticker SIR: Not found\n", "Fetching ticker details:  83%|█████████████████████████████████████████████▍         | 6067/7340 [00:41<00:07, 178.91it/s]Skipping ticker SLW: Not found\n", "Skipping ticker SMM: Not found\n", "Fetching ticker details:  83%|█████████████████████████████████████████████▌         | 6086/7340 [00:41<00:07, 167.56it/s]Skipping ticker SNE: Not found\n", "Skipping ticker SNH: Not found\n", "Fetching ticker details:  83%|█████████████████████████████████████████████▋         | 6105/7340 [00:41<00:07, 172.25it/s]Skipping ticker SNP: Not found\n", "Skipping ticker SNR: Not found\n", "Fetching ticker details:  83%|█████████████████████████████████████████████▉         | 6125/7340 [00:41<00:06, 177.86it/s]Skipping ticker SPA: Not found\n", "Fetching ticker details:  84%|██████████████████████████████████████████████         | 6144/7340 [00:41<00:07, 169.86it/s]Skipping ticker SPP: Not found\n", "Fetching ticker details:  84%|██████████████████████████████████████████████▏        | 6162/7340 [00:41<00:07, 167.13it/s]Skipping ticker SPN: Not found\n", "Fetching ticker details:  84%|██████████████████████████████████████████████▎        | 6179/7340 [00:41<00:06, 167.41it/s]Skipping ticker SRF: Not found\n", "Skipping ticker SRLP: Not found\n", "Fetching ticker details:  84%|██████████████████████████████████████████████▍        | 6196/7340 [00:42<00:08, 139.43it/s]Skipping ticker SSN: Not found\n", "Skipping ticker SSNI: Not found\n", "Skipping ticker SSS: Not found\n", "Skipping ticker SSW: Not found\n", "Fetching ticker details:  85%|██████████████████████████████████████████████▌        | 6211/7340 [00:42<00:08, 129.55it/s]Skipping ticker STAR: Not found\n", "Fetching ticker details:  85%|██████████████████████████████████████████████▋        | 6232/7340 [00:42<00:07, 143.84it/s]Skipping ticker STAY: Not found\n", "Skipping ticker SSI: Not found\n", "Fetching ticker details:  85%|██████████████████████████████████████████████▊        | 6247/7340 [00:42<00:07, 138.20it/s]Skipping ticker STL: Not found\n", "Skipping ticker STO: Not found\n", "Fetching ticker details:  85%|██████████████████████████████████████████████▉        | 6262/7340 [00:42<00:07, 139.58it/s]Skipping ticker STON: Not found\n", "Skipping ticker STOR: Not found\n", "Skipping ticker STS: Not found\n", "Fetching ticker details:  86%|███████████████████████████████████████████████        | 6280/7340 [00:42<00:07, 143.83it/s]Skipping ticker STV: Not found\n", "Skipping ticker STRP: Not found\n", "Skipping ticker STJ: Not found\n", "Skipping ticker STI: Not found\n", "Skipping ticker STZ.B: Not found\n", "Fetching ticker details:  86%|███████████████████████████████████████████████▏       | 6297/7340 [00:42<00:06, 150.70it/s]Skipping ticker SVU: Not found\n", "Fetching ticker details:  86%|███████████████████████████████████████████████▎       | 6313/7340 [00:42<00:07, 144.63it/s]Skipping ticker SWFT: Not found\n", "Skipping ticker SWM: Not found\n", "Skipping ticker SWC: Not found\n", "Fetching ticker details:  86%|███████████████████████████████████████████████▍       | 6330/7340 [00:43<00:06, 148.75it/s]Skipping ticker SXL: Not found\n", "Skipping ticker SXE: Not found\n", "Skipping ticker SXCP: Not found\n", "Fetching ticker details:  86%|███████████████████████████████████████████████▌       | 6346/7340 [00:43<00:07, 141.41it/s]Skipping ticker SYRG: Not found\n", "Skipping ticker SYN: Not found\n", "Skipping ticker SYX: Not found\n", "Skipping ticker TA: Not found\n", "Fetching ticker details:  87%|███████████████████████████████████████████████▋       | 6361/7340 [00:43<00:07, 139.49it/s]Skipping ticker TAOM: Not found\n", "Skipping ticker SZC: Not found\n", "Skipping ticker TAT: Not found\n", "Fetching ticker details:  87%|███████████████████████████████████████████████▊       | 6376/7340 [00:43<00:06, 142.29it/s]Skipping ticker TCAP: Not found\n", "Fetching ticker details:  87%|███████████████████████████████████████████████▉       | 6391/7340 [00:43<00:06, 140.65it/s]Skipping ticker TAHO: Not found\n", "Skipping ticker TCB: Not found\n", "Skipping ticker TCO: Not found\n", "Fetching ticker details:  87%|████████████████████████████████████████████████       | 6407/7340 [00:43<00:06, 144.65it/s]Skipping ticker TCPI: Not found\n", "Skipping ticker TCK: Not found\n", "Fetching ticker details:  88%|████████████████████████████████████████████████▏      | 6424/7340 [00:43<00:06, 151.55it/s]Skipping ticker TE: Not found\n", "Skipping ticker TEN: Not found\n", "Skipping ticker TEP: Not found\n", "Fetching ticker details:  88%|████████████████████████████████████████████████▎      | 6442/7340 [00:43<00:05, 158.64it/s]Skipping ticker TEGP: Not found\n", "Skipping ticker TCP: Not found\n", "Fetching ticker details:  88%|████████████████████████████████████████████████▍      | 6461/7340 [00:43<00:05, 165.44it/s]Skipping ticker TGC: Not found\n", "Skipping ticker TGD: Not found\n", "Fetching ticker details:  88%|████████████████████████████████████████████████▌      | 6479/7340 [00:43<00:05, 169.42it/s]Skipping ticker TGP: Not found\n", "Fetching ticker details:  89%|████████████████████████████████████████████████▋      | 6496/7340 [00:44<00:05, 166.98it/s]Skipping ticker TIER: Not found\n", "Skipping ticker TIF: Not found\n", "Skipping ticker TI: Not found\n", "Skipping ticker TIK: Not found\n", "Skipping ticker TI.A: Not found\n", "Fetching ticker details:  89%|████████████████████████████████████████████████▊      | 6513/7340 [00:44<00:05, 140.30it/s]Skipping ticker TKF: Not found\n", "Skipping ticker TLN: Not found\n", "Skipping ticker TLLP: Not found\n", "Skipping ticker TLI: Not found\n", "Skipping ticker TLRD: Not found\n", "Fetching ticker details:  89%|████████████████████████████████████████████████▉      | 6529/7340 [00:44<00:05, 143.76it/s]Skipping ticker TLP: Not found\n", "Skipping ticker TIS: Not found\n", "Skipping ticker TMH: Not found\n", "Fetching ticker details:  89%|█████████████████████████████████████████████████      | 6547/7340 [00:44<00:05, 153.01it/s]Skipping ticker TNH: Not found\n", "Fetching ticker details:  89%|█████████████████████████████████████████████████▏     | 6563/7340 [00:44<00:05, 142.38it/s]Skipping ticker TOF: Not found\n", "Skipping ticker TOT: Not found\n", "Skipping ticker TOO: Not found\n", "Skipping ticker TOWR: Not found\n", "Fetching ticker details:  90%|█████████████████████████████████████████████████▎     | 6578/7340 [00:44<00:05, 138.80it/s]Skipping ticker TMK: Not found\n", "Skipping ticker TPRE: Not found\n", "Fetching ticker details:  90%|█████████████████████████████████████████████████▍     | 6598/7340 [00:44<00:04, 155.11it/s]Skipping ticker TPUB: Not found\n", "Skipping ticker TPLM: Not found\n", "Skipping ticker TREC: Not found\n", "Fetching ticker details:  90%|█████████████████████████████████████████████████▌     | 6614/7340 [00:44<00:04, 154.28it/s]Skipping ticker TRK: Not found\n", "Fetching ticker details:  90%|█████████████████████████████████████████████████▋     | 6631/7340 [00:44<00:04, 158.44it/s]Skipping ticker TRCO: Not found\n", "Skipping ticker TRR: Not found\n", "Fetching ticker details:  91%|█████████████████████████████████████████████████▊     | 6648/7340 [00:45<00:04, 154.47it/s]Skipping ticker TRQ: Not found\n", "Skipping ticker TRXC: Not found\n", "Fetching ticker details:  91%|█████████████████████████████████████████████████▉     | 6664/7340 [00:45<00:04, 146.77it/s]Skipping ticker TSLF: Not found\n", "Skipping ticker TSS: Not found\n", "Skipping ticker TSU: Not found\n", "Fetching ticker details:  91%|██████████████████████████████████████████████████     | 6682/7340 [00:45<00:04, 155.12it/s]Skipping ticker TTM: Not found\n", "Skipping ticker TUMI: Not found\n", "Fetching ticker details:  91%|██████████████████████████████████████████████████▏    | 6698/7340 [00:45<00:04, 146.43it/s]Skipping ticker TSO: Not found\n", "Skipping ticker TVPT: Not found\n", "Fetching ticker details:  91%|██████████████████████████████████████████████████▎    | 6713/7340 [00:45<00:04, 144.56it/s]Skipping ticker TTF: Not found\n", "Skipping ticker TWC: Not found\n", "Skipping ticker TWX: Not found\n", "Skipping ticker TWTR: Not found\n", "Skipping ticker TYC: Not found\n", "Skipping ticker TYME: Not found\n", "Fetching ticker details:  92%|██████████████████████████████████████████████████▍    | 6731/7340 [00:45<00:04, 150.89it/s]Skipping ticker TXTR: Not found\n", "Skipping ticker UAM: Not found\n", "Skipping ticker UA.C: Not found\n", "Skipping ticker UBA: Not found\n", "Fetching ticker details:  92%|██████████████████████████████████████████████████▌    | 6747/7340 [00:45<00:04, 145.34it/s]Skipping ticker UBP: Not found\n", "Fetching ticker details:  92%|██████████████████████████████████████████████████▋    | 6765/7340 [00:45<00:03, 154.72it/s]Skipping ticker UCP: Not found\n", "Skipping ticker UFAB: Not found\n", "Skipping ticker UFS: Not found\n", "Fetching ticker details:  92%|██████████████████████████████████████████████████▊    | 6781/7340 [00:46<00:03, 150.60it/s]Skipping ticker UN: Not found\n", "Fetching ticker details:  93%|██████████████████████████████████████████████████▉    | 6800/7340 [00:46<00:03, 159.06it/s]Skipping ticker UNT: Not found\n", "Skipping ticker UNVR: Not found\n", "Fetching ticker details:  93%|███████████████████████████████████████████████████    | 6819/7340 [00:46<00:03, 165.60it/s]Skipping ticker UQM: Not found\n", "Fetching ticker details:  93%|███████████████████████████████████████████████████▏   | 6839/7340 [00:46<00:02, 174.40it/s]Skipping ticker USG: Not found\n", "Skipping ticker UTX: Not found\n", "Fetching ticker details:  93%|███████████████████████████████████████████████████▍   | 6858/7340 [00:46<00:02, 175.74it/s]Skipping ticker UWN: Not found\n", "Fetching ticker details:  94%|███████████████████████████████████████████████████▌   | 6876/7340 [00:46<00:02, 165.63it/s]Skipping ticker VAR: Not found\n", "Skipping ticker VCF: Not found\n", "Skipping ticker VEDL: Not found\n", "Skipping ticker VCO: Not found\n", "Fetching ticker details:  94%|███████████████████████████████████████████████████▋   | 6893/7340 [00:46<00:02, 164.36it/s]Skipping ticker VCRA: Not found\n", "Skipping ticker VEC: Not found\n", "Skipping ticker VER: Not found\n", "Fetching ticker details:  94%|███████████████████████████████████████████████████▊   | 6910/7340 [00:46<00:02, 164.40it/s]Skipping ticker VG: Not found\n", "Skipping ticker VII: Not found\n", "Fetching ticker details:  94%|███████████████████████████████████████████████████▉   | 6927/7340 [00:46<00:02, 145.60it/s]Skipping ticker VISI: Not found\n", "Fetching ticker details:  95%|████████████████████████████████████████████████████   | 6942/7340 [00:47<00:02, 133.02it/s]Skipping ticker VLP: Not found\n", "Skipping ticker VMEM: Not found\n", "Fetching ticker details:  95%|████████████████████████████████████████████████████▏  | 6958/7340 [00:47<00:02, 139.47it/s]Skipping ticker VMM: Not found\n", "Skipping ticker VMW: Not found\n", "Fetching ticker details:  95%|████████████████████████████████████████████████████▎  | 6980/7340 [00:47<00:02, 158.39it/s]Skipping ticker VNTV: Not found\n", "Fetching ticker details:  95%|████████████████████████████████████████████████████▍  | 6997/7340 [00:47<00:02, 146.02it/s]Skipping ticker VRX: Not found\n", "Skipping ticker VRTV: Not found\n", "Skipping ticker VSLR: Not found\n", "Fetching ticker details:  96%|████████████████████████████████████████████████████▌  | 7013/7340 [00:47<00:02, 146.86it/s]Skipping ticker VSI: Not found\n", "Skipping ticker VSR: Not found\n", "Skipping ticker VTA: Not found\n", "Fetching ticker details:  96%|████████████████████████████████████████████████████▋  | 7031/7340 [00:47<00:02, 150.83it/s]Skipping ticker VTTI: Not found\n", "Skipping ticker VVC: Not found\n", "Fetching ticker details:  96%|████████████████████████████████████████████████████▊  | 7047/7340 [00:47<00:02, 145.59it/s]Skipping ticker WAIR: Not found\n", "Skipping ticker WAGE: Not found\n", "Fetching ticker details:  96%|████████████████████████████████████████████████████▉  | 7062/7340 [00:47<00:01, 142.09it/s]Skipping ticker WBC: Not found\n", "Skipping ticker WBAI: Not found\n", "Skipping ticker WBK: Not found\n", "Skipping ticker WCIC: Not found\n", "Fetching ticker details:  96%|█████████████████████████████████████████████████████  | 7081/7340 [00:47<00:01, 154.58it/s]Skipping ticker WCG: Not found\n", "Skipping ticker WDR: Not found\n", "Skipping ticker WAC: Not found\n", "Fetching ticker details:  97%|█████████████████████████████████████████████████████▏ | 7097/7340 [00:48<00:01, 153.79it/s]Skipping ticker WFT: Not found\n", "Fetching ticker details:  97%|█████████████████████████████████████████████████████▎ | 7113/7340 [00:48<00:01, 144.95it/s]Skipping ticker WGP: Not found\n", "Skipping ticker WGL: Not found\n", "Skipping ticker WGA: Not found\n", "Skipping ticker WG: Not found\n", "Fetching ticker details:  97%|█████████████████████████████████████████████████████▍ | 7130/7340 [00:48<00:01, 147.40it/s]Skipping ticker WLH: Not found\n", "Skipping ticker WLL: Not found\n", "Fetching ticker details:  97%|█████████████████████████████████████████████████████▌ | 7145/7340 [00:48<00:01, 133.23it/s]Skipping ticker WMLP: Not found\n", "Skipping ticker WNR: Not found\n", "Fetching ticker details:  98%|█████████████████████████████████████████████████████▋ | 7159/7340 [00:48<00:01, 130.56it/s]Skipping ticker WMC: Not found\n", "Skipping ticker WR: Not found\n", "Skipping ticker WPZ: Not found\n", "Skipping ticker WNRL: Not found\n", "Fetching ticker details:  98%|█████████████████████████████████████████████████████▊ | 7174/7340 [00:48<00:01, 135.53it/s]Skipping ticker WPG: Not found\n", "Skipping ticker WPT: Not found\n", "Skipping ticker WPX: Not found\n", "Skipping ticker WRE: Not found\n", "Skipping ticker WRI: Not found\n", "Fetching ticker details:  98%|█████████████████████████████████████████████████████▉ | 7195/7340 [00:48<00:00, 153.37it/s]Skipping ticker WTR: Not found\n", "Fetching ticker details:  98%|██████████████████████████████████████████████████████ | 7211/7340 [00:48<00:00, 132.64it/s]Skipping ticker WWAV: Not found\n", "Skipping ticker WTT: Not found\n", "Skipping ticker WWE: Not found\n", "Fetching ticker details:  98%|██████████████████████████████████████████████████████▏| 7227/7340 [00:49<00:00, 136.79it/s]Skipping ticker XCO: Not found\n", "Skipping ticker XEC: Not found\n", "Fetching ticker details:  99%|██████████████████████████████████████████████████████▎| 7243/7340 [00:49<00:00, 140.46it/s]Skipping ticker WUBA: Not found\n", "Skipping ticker XL: Not found\n", "Skipping ticker XNY: Not found\n", "Skipping ticker WYN: Not found\n", "Fetching ticker details:  99%|██████████████████████████████████████████████████████▍| 7258/7340 [00:49<00:00, 128.91it/s]Skipping ticker XON: Not found\n", "Skipping ticker XRA: Not found\n", "Fetching ticker details:  99%|██████████████████████████████████████████████████████▍| 7272/7340 [00:49<00:00, 129.16it/s]Skipping ticker XRM: Not found\n", "Skipping ticker XRS: Not found\n", "Skipping ticker Y: Not found\n", "Fetching ticker details:  99%|██████████████████████████████████████████████████████▌| 7286/7340 [00:49<00:00, 129.66it/s]Skipping ticker YDKN: Not found\n", "Skipping ticker XOXO: Not found\n", "Skipping ticker YGE: Not found\n", "Fetching ticker details:  99%|██████████████████████████████████████████████████████▋| 7301/7340 [00:49<00:00, 131.84it/s]Skipping ticker XUE: Not found\n", "Skipping ticker XTLY: Not found\n", "Skipping ticker YUME: Not found\n", "Skipping ticker YUMA: Not found\n", "Skipping ticker YZC: Not found\n", "Fetching ticker details: 100%|██████████████████████████████████████████████████████▊| 7318/7340 [00:49<00:00, 138.42it/s]Skipping ticker ZAYO: Not found\n", "Skipping ticker ZEN: Not found\n", "Skipping ticker ZF: Not found\n", "Skipping ticker ZFC: Not found\n", "Fetching ticker details: 100%|██████████████████████████████████████████████████████▉| 7332/7340 [00:49<00:00, 133.23it/s]Skipping ticker ZNH: Not found\n", "Skipping ticker ZOES: Not found\n", "Skipping ticker ZPIN: Not found\n", "Skipping ticker ZX: Not found\n", "Fetching ticker details: 100%|███████████████████████████████████████████████████████| 7340/7340 [00:50<00:00, 146.66it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[universe] 3738 symbols selected (training-period only)\n"]}], "source": ["# ╭────────────────────────── UNIVERSE ─────────────────────────╮\n", "def build_training_universe() -> list[str]:\n", "    \"\"\"\n", "    Select symbols *only* with info available in the **training window**,\n", "    avoiding any peek into 2025.\n", "    \"\"\"\n", "    # simple liquidity screen: avg $‐vol ≥ $10 M during training window\n", "    window_start = pd.Timestamp(TRAIN_START, tz=eastern)\n", "    window_end   = pd.Timestamp(TRAIN_END, tz=eastern)\n", "\n", "    # pull tickers listed in e.g. Russell 3000 (replace with your helper)\n", "    from universe.stock_universe_selector import StockUniverseSelector\n", "    from tickers.ticker_helpers import TickerInfoStore\n", "    from universe.sp500_constituents import SP500Constituents\n", "    \n", "    # sp500_constituents = SP500Constituents()\n", "    selector = StockUniverseSelector(_daily_md, TickerInfoStore())\n", "    symbols = []\n", "    \n", "    # for date in pd.date_range(window_start, window_end):\n", "    #     constituents = sp500_constituents.constituents_for(date)\n", "    #     symbols.extend(constituents)\n", "\n", "    universe_df = selector.select_by_price_and_volume(\n", "        window_start, window_end,\n", "        min_price=MIN_PRICE, min_dollar_volume=50_000_000\n", "    )\n", "\n", "    symbols = (universe_df.index\n", "               .get_level_values(\"ticker\")\n", "               .unique()\n", "               .tolist())\n", "\n", "    if SAMPLE_UNIVERSE_SIZE and SAMPLE_UNIVERSE_SIZE > 0:\n", "        print(f\"[universe] Full universe has {len(symbols)} symbols.\")\n", "        # Ensure we don't try to sample more than available\n", "        sample_size = min(len(symbols), SAMPLE_UNIVERSE_SIZE)\n", "        symbols = random.sample(symbols, k=sample_size)\n", "        print(f\"[universe] Sampling {len(symbols)} symbols for validation.\")\n", "        \n", "    print(f\"[universe] {len(symbols)} symbols selected (training-period only)\")\n", "    \n", "    return list(set(symbols))\n", "\n", "UNIVERSE = build_training_universe() "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[store] Advanced features added.\n", "[model] Long model trained.\n", "[model] Short model trained.\n", "             feature  importance\n", "0           atr_norm    0.201687\n", "1           turnover    0.075902\n", "2             roc_60    0.065768\n", "3             roc_20    0.055006\n", "4             rsi_14    0.054914\n", "5    sma200_dist_pct    0.054478\n", "6     roc_20_rank_cs    0.053670\n", "7              qpi_3    0.048261\n", "8              roc_5    0.046574\n", "9             rsi_20    0.045812\n", "10             qpi_5    0.041917\n", "11         hurst_100    0.040267\n", "12               ibs    0.039063\n", "13  turnover_rank_cs    0.038280\n", "14    rsi_14_rank_cs    0.036943\n", "15           roc_252    0.035152\n", "16   turnover_rel_ts    0.034332\n", "17             rsi_5    0.031973\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["             feature  importance\n", "0           atr_norm    0.218797\n", "1            roc_252    0.104514\n", "2   turnover_rank_cs    0.099661\n", "3           turnover    0.062230\n", "4    sma200_dist_pct    0.047556\n", "5              qpi_3    0.047073\n", "6             roc_60    0.045585\n", "7             roc_20    0.043643\n", "8     roc_20_rank_cs    0.043202\n", "9              roc_5    0.040948\n", "10            rsi_20    0.039958\n", "11            rsi_14    0.037681\n", "12               ibs    0.034967\n", "13   turnover_rel_ts    0.034793\n", "14             rsi_5    0.026469\n", "15             qpi_5    0.025059\n", "16         hurst_100    0.024517\n", "17    rsi_14_rank_cs    0.023346\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# ╭───────────────────────── FEATURES ──────────────────────────╮\n", "\n", "from indicators.qpi import qpi_indicator \n", "\n", "def _calculate_hurst_exponent(ts, lags=50):\n", "    \"\"\"A simplified Hurst Exponent calculation.\"\"\"\n", "    if len(ts) < lags:\n", "        return np.nan\n", "    \n", "    tau = [np.std(np.subtract(ts[lag:], ts[:-lag])) for lag in range(1, lags)]\n", "    tau = np.where(np.isclose(tau, 0), np.nan, tau) # handle cases with zero std dev\n", "    \n", "    with np.errstate(invalid='ignore', divide='ignore'):\n", "        poly = np.polyfit(np.log(np.arange(1, lags)), np.log(tau), 1)\n", "    \n", "    if np.any(np.isnan(poly)):\n", "        return np.nan\n", "        \n", "    return poly[0] * 2.0\n", "\n", "\n", "# ╭─────────────────── MODIFIED FEATURES FUNCTION ───────────────────╮\n", "\n", "def build_feature_df(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Vectorised features + binary target for one symbol.\n", "    All features are shifted one bar to prevent lookahead bias.\n", "    \"\"\"\n", "    close = df[\"close\"]\n", "    high = df[\"high\"]\n", "    low = df[\"low\"]\n", "    volume = df[\"volume\"]\n", "    \n", "    feat = pd.DataFrame(index=df.index)\n", "    \n", "    # Group 1: <PERSON>um\n", "    \n", "    # 1. Rates of Change (RoC) for different windows\n", "    feat[\"roc_5\"]   = close.pct_change(5)\n", "    feat[\"roc_20\"]  = close.pct_change(20)\n", "    feat[\"roc_60\"]  = close.pct_change(60)\n", "    feat[\"roc_252\"] = close.pct_change(252)\n", "\n", "    # 2. RSIs for different windows\n", "    for n in [5, 14, 20]:\n", "        delta = close.diff()\n", "        up, down = delta.clip(lower=0), -delta.clip(upper=0)\n", "        roll_up = up.ewm(alpha=1/n, min_periods=n).mean()\n", "        roll_down = down.ewm(alpha=1/n, min_periods=n).mean()\n", "        rs = roll_up / roll_down\n", "        feat[f\"rsi_{n}\"] = 100 - (100 / (1 + rs))\n", "\n", "    # 3. QPIs for different windows\n", "    feat[\"qpi_3\"] = qpi_indicator(close, window=3, lookback_years=1)\n", "    feat[\"qpi_5\"] = qpi_indicator(close, window=5, lookback_years=1)\n", "    \n", "    # Group 2: Intraday & Volatility\n", "\n", "    # 4. IBS and Normalized ATR\n", "    feat[\"ibs\"] = (close - low) / (high - low)\n", "    \n", "    true_range = pd.concat([\n", "        high - low,\n", "        (high - close.shift()).abs(),\n", "        (low - close.shift()).abs()\n", "    ], axis=1).max(axis=1)\n", "    atr_14 = true_range.ewm(alpha=1/14, min_periods=14).mean()\n", "    feat[\"atr_norm\"] = atr_14 / close\n", "    \n", "    # Group 3: Long-Term Trend\n", "\n", "    # 5. Closing price distance to 200-day SMA\n", "    sma_200 = close.rolling(200).mean()\n", "    feat[\"sma200_dist_pct\"] = (close - sma_200) / sma_200\n", "\n", "    # 6. <PERSON><PERSON> (rolling)\n", "    # Note: This is computationally very expensive\n", "    feat[\"hurst_100\"] = close.rolling(100).apply(\n", "        _calculate_hurst_exponent, raw=True\n", "    )\n", "    \n", "    # Group 4: Volume\n", "    \n", "    # 7. Turnover\n", "    feat[\"turnover\"] = close * volume\n", "    \n", "    # --- IMPORTANT: Shift all features to prevent lookahead bias ---\n", "    feat = feat.shift(1)\n", "\n", "    # Define the target variable (no shift needed for the target)\n", "    fwd_ret_5 = close.shift(-5) / close - 1\n", "    feat[\"target_long\"] = (fwd_ret_5 > 0.01).astype(int) # Target for long model\n", "    feat[\"target_short\"] = (fwd_ret_5 < -0.01).astype(int) # Target for short model\n", "\n", "    feat[\"ticker\"] = df.attrs.get(\"ticker\", \"UNKNOWN\")\n", "\n", "    # Drop rows where any feature or target is nan\n", "    return feat.dropna(subset=[col for col in feat.columns if 'target' not in col])\n", "\n", "def add_advanced_features(feature_df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Adds time-series and cross-sectional features to the feature store.\n", "    This function operates on the entire feature store after it's built.\n", "    \"\"\"\n", "    \n", "    # 9. Time-Series Relative Turnover\n", "    # Compares today's turnover to its own 50-day rolling average.\n", "    # A value > 1 means today's turnover is higher than the average.\n", "    # We group by 'ticker' to ensure the rolling window is calculated per-stock.\n", "    feature_df['turnover_rel_ts'] = feature_df.groupby('ticker')['turnover'].transform(\n", "        lambda x: x / x.rolling(50, min_periods=20).mean()\n", "    )\n", "\n", "    # 10. Cross-Sectional Rank Features\n", "    # For each day, rank every stock's feature value against all other stocks.\n", "    # .rank(pct=True) returns the rank as a percentage (0.0 to 1.0),\n", "    # which is a great way to standardize the feature.\n", "    # We group by 'date' to perform the ranking across all tickers for that day.\n", "    \n", "    # Turnover Rank\n", "    feature_df['turnover_rank_cs'] = feature_df.groupby('date')['turnover'].rank(pct=True)\n", "    \n", "    # RSI Rank\n", "    feature_df['rsi_14_rank_cs'] = feature_df.groupby('date')['rsi_14'].rank(pct=True)\n", "    \n", "    # Rate of Change Rank\n", "    feature_df['roc_20_rank_cs'] = feature_df.groupby('date')['roc_20'].rank(pct=True)\n", "    \n", "    print(\"[store] Advanced features added.\")\n", "    return feature_df\n", "\n", "\n", "\n", "# ╭──────────────────── MODEL TRAINING (XGB) ───────────────────╮\n", "\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "def analyze_and_plot_feature_importance(model: xgb.XGBClassifier, feature_names: list[str], title: str):\n", "    \"\"\"\n", "    Analyzes and plots the feature importances from a trained XGBoost model.\n", "\n", "    Args:\n", "        model: The trained XGBoost classifier.\n", "        feature_names: A list of the feature names, in the same order as the\n", "                       training data.\n", "    \"\"\"\n", "    # Create a DataFrame for easy viewing\n", "    importances = model.feature_importances_\n", "    feature_importance_df = pd.DataFrame({\n", "        'feature': feature_names,\n", "        'importance': importances\n", "    }).sort_values('importance', ascending=False).reset_index(drop=True)\n", "\n", "    title = f\"XGBoost Feature Importance ({title})\"\n", "    print(feature_importance_df)\n", "\n", "    # Plotting the importances\n", "    plt.style.use('seaborn-v0_8-darkgrid')\n", "    plt.figure(figsize=(12, 8))\n", "    sns.barplot(x='importance', y='feature', data=feature_importance_df, palette='viridis')\n", "    plt.title(f'XGBoost Feature Importance ({title})', fontsize=16)\n", "    plt.xlabel('Importance Score (Gain)', fontsize=12)\n", "    plt.ylabel('Features', fontsize=12)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "\n", "def train_models() -> tuple[xgb.XGBClassifier, xgb.XGBClassifier]:\n", "    dfs = []\n", "    for tkr in UNIVERSE:\n", "        df = get_daily_df(\n", "            tkr,\n", "            pd.Timestamp(TRAIN_START, tz=eastern),\n", "            pd.Timestamp(TRAIN_END,   tz=eastern)\n", "        )\n", "        if df.empty or len(df) < 205: continue\n", "        fdf = build_feature_df(df)\n", "        fdf[\"ticker\"] = tkr\n", "        dfs.append(fdf)\n", "\n", "    all_feat = pd.concat(dfs)\n", "    all_feat = (\n", "        all_feat.reset_index()\n", "                .rename(columns={\"index\": \"date\"})\n", "                .pipe(add_advanced_features)\n", "                .set_index([\"date\", \"ticker\"])\n", "                .sort_index()\n", "    )\n", "    all_feat.dropna(inplace=True)\n", "    \n", "    features = all_feat.drop(columns=['target_long', 'target_short'])\n", "    \n", "    # Train Long Model\n", "    y_long = all_feat[\"target_long\"]\n", "    model_long = xgb.XGBClassifier(\n", "        objective=\"binary:logistic\", eval_metric=\"logloss\", max_depth=3,\n", "        eta=0.1, n_estimators=300, subsample=0.8, colsample_bytree=0.8,\n", "        random_state=SEED\n", "    )\n", "    model_long.fit(features, y_long)\n", "    print(\"[model] Long model trained.\")\n", "\n", "    # Train Short Model\n", "    y_short = all_feat[\"target_short\"]\n", "    model_short = xgb.XGBClassifier(\n", "        objective=\"binary:logistic\", eval_metric=\"logloss\", max_depth=3,\n", "        eta=0.1, n_estimators=300, subsample=0.8, colsample_bytree=0.8,\n", "        random_state=SEED\n", "    )\n", "    model_short.fit(features, y_short)\n", "    print(\"[model] Short model trained.\")\n", "    \n", "    analyze_and_plot_feature_importance(model_long, features.columns.tolist(), \"Long\")\n", "    analyze_and_plot_feature_importance(model_short, features.columns.tolist(), \"Short\")\n", "\n", "    return model_long, model_short\n", "\n", "MODEL_LONG, MODEL_SHORT = train_models()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[store] Advanced features added.\n", "[store] feature rows: 797576\n"]}], "source": ["# ╭─────────────────────── TRADING CALENDAR ────────────────────╮\n", "nyse = mcal.get_calendar(\"NYSE\")\n", "schedule = nyse.schedule(BACKTEST_START, BACKTEST_END)\n", "\n", "TRADING_DAYS = (\n", "    schedule.index\n", "            .tz_localize(eastern)   # tag as EST/EDT, *no* conversion\n", "            .normalize()\n", ")\n", "# map day → previous trading day\n", "PREV_DAY = {d: TRADING_DAYS[i-1] if i else None\n", "            for i, d in enumerate(TRADING_DAYS)}\n", "\n", "FEATURE_STORE = None \n", "def prep_feature_store() -> pd.DataFrame:\n", "    \"\"\"\n", "    Pre-computes vectorised features for *every* symbol for the entire\n", "    back-test horizon (plus 250-day warm-up).  Results cached in\n", "    FEATURE_STORE  ⇒  zero indicator calc inside the trading loop.\n", "    \"\"\"\n", "    global FEATURE_STORE\n", "    rows = []\n", "    warmup_start = (pd.Timestamp(BACKTEST_START, tz=eastern)\n", "                    - pd.<PERSON><PERSON><PERSON>(days=400))      # 400 + safety\n", "    for tkr in UNIVERSE:\n", "        df = get_daily_df(tkr, warmup_start, pd.Timestamp(BACKTEST_END, tz=eastern))\n", "        if df.empty or len(df) < 255:\n", "            continue\n", "        fdf = build_feature_df(df.assign(ticker=tkr))\n", "        fdf = fdf.loc[fdf.index >= warmup_start]  # keep back-test range\n", "        fdf[\"ticker\"] = tkr\n", "        rows.append(fdf)\n", "        \n", "    FEATURE_STORE = pd.concat(rows)\n", "    FEATURE_STORE = (\n", "        FEATURE_STORE.reset_index()\n", "                     .rename(columns={\"index\": \"date\"})\n", "                     .pipe(add_advanced_features) # a clean way to apply the function\n", "    )          \n", "    \n", "    FEATURE_STORE = (\n", "        FEATURE_STORE.set_index([\"date\", \"ticker\"])\n", "                     .sort_index()\n", "    )\n", "    \n", "    FEATURE_STORE.dropna(inplace=True)      \n", "    \n", "    FEATURE_STORE.index = (\n", "        FEATURE_STORE.index\n", "        .set_levels(\n", "            FEATURE_STORE.index.levels[0]\n", "                            .tz_convert(eastern)   # ensure US/Eastern\n", "                            .normalize()          # 00:00:00\n", "            ,\n", "            level=\"date\"\n", "        )\n", "    )\n", "    print(\"[store] feature rows:\", len(FEATURE_STORE))\n", "    \n", "prep_feature_store()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# ╭──────────────────────── BACKTEST LOOP ──────────────────────╮\n", "class Position:\n", "    def __init__(self, side: str, qty: int, entry_price: float,\n", "                 entry_day: pd.Timestamp):\n", "        self.side = side\n", "        self.qty  = qty\n", "        self.entry_price = entry_price\n", "        self.entry_day   = entry_day\n", "\n", "    def mtm_equity(self, price: float) -> float:\n", "        market_value = price * abs(self.qty)\n", "        if self.side == \"long\":\n", "            return market_value\n", "        else: # short\n", "            # Since cash was credited on entry, we subtract the current\n", "            # market value (cost to cover) to get the correct total equity.\n", "            return -market_value\n", "\n", "\n", "def backtest_long_short_vix_filter():\n", "    cash        = 100_000.0\n", "    positions   : dict[str, Position] = {}\n", "    equity_curve = []\n", "    trades = []\n", "\n", "    vix_data = get_vix_data(pd.Timestamp(BACKTEST_START) - pd.Timedelta(days=30), pd.Timestamp(BACKTEST_END))\n", "    vix_data['sma_15'] = vix_data['close'].rolling(15).mean()\n", "    vix_data['threshold'] = vix_data['sma_15'] * 1.15\n", "    \n", "    for day_idx, day in enumerate(TRADING_DAYS):\n", "        prev = PREV_DAY[day]\n", "        if prev is None:\n", "            continue\n", "\n", "        # VIX Regime Filter Logic\n", "        long_lever = INITIAL_LONG_LEVER\n", "        short_lever = INITIAL_SHORT_LEVER\n", "        try:\n", "            vix_row = vix_data.loc[vix_data.index <= prev].iloc[-1]\n", "            if vix_row['close'] > vix_row['threshold']:\n", "                # Bear Market\n", "                long_lever = 0.1\n", "                dbg(f\"{day.date()}: BEAR market regime (VIX filter)\")\n", "            else:\n", "                # Bull Market\n", "                dbg(f\"{day.date()}: BULL market regime (VIX filter)\")\n", "        except IndexError:\n", "            # Not enough VIX data yet, default to bull\n", "            pass\n", "        \n", "        if prev not in FEATURE_STORE.index.get_level_values('date'):\n", "            dbg(f\"WARN: Date {prev.date()} not found in FEATURE_STORE. Skipping trade logic for {day.date()}.\")\n", "            # If there's a history, carry over the last known equity. Otherwise, use initial cash.\n", "            equity_close = equity_curve[-1]['equity'] if equity_curve else cash\n", "            equity_curve.append({\"day\": day, \"equity\": equity_close})\n", "            continue\n", "\n", "        today_data = {}\n", "        prev_day_data = {}\n", "        needed = set(UNIVERSE) | set(positions.keys())\n", "        for tkr in needed:\n", "            df = get_daily_df(tkr, prev, day)\n", "            if not df.empty:\n", "                if prev in df.index:\n", "                    prev_day_data[tkr] = df.loc[prev]\n", "                if day in df.index:\n", "                    today_data[tkr] = df.loc[day]\n", "\n", "        exits = []\n", "        # --- DBG ADDED BACK ---\n", "        dbg(f\"\\n--- Analyzing exits for {day.date()} ---\")\n", "        for tkr, pos in list(positions.items()):\n", "            row = today_data.get(tkr)\n", "            if row is None: continue\n", "\n", "            exit_price = 0\n", "            exit_reason = None\n", "            hit_stop = False\n", "\n", "            # Stop-loss logic for both long and short positions\n", "            if pos.side == \"long\":\n", "                stop_price = pos.entry_price * (1 - STOP_PCT)\n", "                if row[\"low\"] <= stop_price:\n", "                    hit_stop = True\n", "                    exit_price = stop_price\n", "                    exit_reason = \"STOP-LOSS\"\n", "            elif pos.side == \"short\":\n", "                stop_price = pos.entry_price * (1 + STOP_PCT)\n", "                if row[\"high\"] >= stop_price:\n", "                    hit_stop = True\n", "                    exit_price = stop_price\n", "                    exit_reason = \"STOP-LOSS\"\n", "                    \n", "            holding_days = len(TRADING_DAYS[(TRADING_DAYS >= pos.entry_day) & (TRADING_DAYS <= day)])\n", "            aged_out = holding_days > MAX_HOLD_DAYS\n", "\n", "            if aged_out and not hit_stop:\n", "                exit_price = row[\"open\"]\n", "                exit_reason = \"AGED-OUT\"\n", "            \n", "            if exit_reason:\n", "                fee = exit_price * abs(pos.qty) * COST_BPS / 1e4\n", "                if pos.side == \"long\":\n", "                    pnl = (exit_price - pos.entry_price) * pos.qty - fee\n", "                    cash += exit_price * pos.qty - fee\n", "                else: # Short\n", "                    pnl = (pos.entry_price - exit_price) * abs(pos.qty) - fee\n", "\n", "                    # On entry, cash was credited with the sale proceeds.\n", "                    cost_to_cover = exit_price * abs(pos.qty)\n", "                    cash -= (cost_to_cover + fee)\n", "                \n", "                dbg(f\"  EXIT: {pos.side.upper():<5s} {tkr:<5s} | Reason: {exit_reason:<9s} | Entry: {pos.entry_price:7.2f} | \"\n", "                    f\"Exit: {exit_price:7.2f} | Qty: {pos.qty:4d} | PnL: ${pnl:8,.2f}\")\n", "\n", "                trades.append({\n", "                    \"ticker\": tkr, \"entry_day\": pos.entry_day, \"exit_day\": day,\n", "                    \"entry_price\": pos.entry_price, \"exit_price\": exit_price,\n", "                    \"qty\": pos.qty, \"pnl\": pnl, \"side\": pos.side,\n", "                    \"exit_reason\": exit_reason\n", "                })\n", "                exits.append(tkr)\n", "\n", "        for tkr in exits: positions.pop(tkr, None)\n", "        # --- DBG ADDED BACK ---\n", "        dbg(\"--- <PERSON><PERSON> DEBUGGING EXITS ---\")\n", "\n", "        # --- Mark-to-Market Equity Calculation ---\n", "        equity_prev_close = cash\n", "        for tkr, pos in positions.items():\n", "            prev_row = prev_day_data.get(tkr)\n", "            if prev_row is not None:\n", "                equity_prev_close += pos.mtm_equity(prev_row[\"close\"])\n", "\n", "        slice_long = equity_prev_close * long_lever / SLOTS_LONG if SLOTS_LONG > 0 else 0\n", "        slice_short = equity_prev_close * short_lever / SLOTS_SHORT if SLOTS_SHORT > 0 else 0\n", "\n", "        # --- Generate Long and Short Signals ---\n", "        long_candidates = []\n", "        short_candidates = []\n", "        prev_feat_slice = FEATURE_STORE.xs(prev, level=\"date\", drop_level=False)\n", "         \n", "        feature_columns = [col for col in prev_feat_slice.columns if 'target' not in col]\n", "        for tkr in UNIVERSE:\n", "            try:\n", "                # Ensure the feature columns are in the correct order for the model\n", "                feat = prev_feat_slice.loc[(prev, tkr)][feature_columns]\n", "                \n", "                # Get probabilities for both models\n", "                prob_long = MODEL_LONG.predict_proba(feat.values.reshape(1, -1))[0, 1]\n", "                prob_short = MODEL_SHORT.predict_proba(feat.values.reshape(1, -1))[0, 1]\n", "                \n", "                prev_close = prev_day_data.get(tkr, {}).get(\"close\", np.nan)\n", "\n", "                if prob_long >= PROB_THRESH_LONG and prev_close >= MIN_PRICE:\n", "                    long_candidates.append((tkr, prob_long, prev_close))\n", "                \n", "                if prob_short >= PROB_THRESH_SHORT and prev_close >= MIN_PRICE:\n", "                    short_candidates.append((tkr, prob_short, prev_close))\n", "\n", "            except KeyError:\n", "                continue\n", "        \n", "        # ── ML loop ──────────────────────────────────────────────────\n", "        longs = sorted(long_candidates, key=lambda x: x[1], reverse=True)[:SLOTS_LONG]\n", "        shorts = sorted(short_candidates, key=lambda x: x[1], reverse=True)[:SLOTS_SHORT]\n", "        \n", "        dbg(f\"{day.date()}  Longs={len(longs)} Shorts={len(shorts)} \"\n", "            f\"Positions={len(positions)} Cash=${cash:,.0f} Equity=${equity_prev_close:,.0f}\")\n", "\n", "\n", "      # --- Entry Logic for Longs ---\n", "        current_long_positions = sum(1 for p in positions.values() if p.side == \"long\")\n", "        slots_to_fill_long = SLOTS_LONG - current_long_positions\n", "        if slots_to_fill_long > 0:\n", "            for tkr, _, _ in longs[:slots_to_fill_long]:\n", "                if tkr in positions: continue\n", "                row = today_data.get(tkr)\n", "                if row is None: continue\n", "                open_px = row[\"open\"]\n", "                qty = int(slice_long // open_px)\n", "                if qty <= 0: continue\n", "                fee = open_px * qty * COST_BPS/1e4\n", "                cash -= (open_px * qty + fee)\n", "                positions[tkr] = Position(\"long\", qty, open_px, day)\n", "                dbg(f\"  ENTRY: LONG  {tkr:<5s} | Price: {open_px:7.2f} | Qty: {qty:4d}\")\n", "\n", "        # --- Entry Logic for Shorts ---\n", "        current_short_positions = sum(1 for p in positions.values() if p.side == \"short\")\n", "        slots_to_fill_short = SLOTS_SHORT - current_short_positions\n", "        if slots_to_fill_short > 0:\n", "            for tkr, _, _ in shorts[:slots_to_fill_short]:\n", "                if tkr in positions: continue\n", "                row = today_data.get(tkr)\n", "                if row is None: continue\n", "                open_px = row[\"open\"]\n", "                qty = -int(slice_short // open_px) # Quantity is negative for shorts\n", "                if abs(qty) <= 0: continue\n", "                fee = open_px * abs(qty) * COST_BPS/1e4\n", "                # For short selling, cash increases by the sale proceeds minus fee\n", "                cash += (open_px * abs(qty) - fee)\n", "                positions[tkr] = Position(\"short\", qty, open_px, day)\n", "                dbg(f\"  ENTRY: SHORT {tkr:<5s} | Price: {open_px:7.2f} | Qty: {qty:4d}\")\n", "\n", "        # --- Daily Equity Update ---\n", "        equity_close = cash\n", "        for tkr, pos in positions.items():\n", "            row = today_data.get(tkr)\n", "            price = row[\"close\"] if row is not None else prev_day_data.get(tkr, {}).get(\"close\")\n", "            if price is not None:\n", "                equity_close += pos.mtm_equity(price)\n", "        \n", "        equity_curve.append({\"day\": day, \"equity\": equity_close})\n", "\n", "    return pd.DataFrame(equity_curve).set_index(\"day\"), pd.DataFrame(trades)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Running Original XGBoost Strategy ---\n", "Cache miss or invalid cache for ^VIX (2023-12-02 00:00:00 - 2025-01-01 00:00:00, 1d). Fetching from yfinance...\n", "Successfully fetched and processed data for ^VIX. Shape: (271, 5). Index type: <class 'pandas.core.indexes.datetimes.DatetimeIndex'>\n", "2024-01-03: BULL market regime (VIX filter)\n", "WARN: Date 2024-01-02 not found in FEATURE_STORE. Skipping trade logic for 2024-01-03.\n", "2024-01-04: BULL market regime (VIX filter)\n", "WARN: Date 2024-01-03 not found in FEATURE_STORE. Skipping trade logic for 2024-01-04.\n", "2024-01-05: BULL market regime (VIX filter)\n", "WARN: Date 2024-01-04 not found in FEATURE_STORE. Skipping trade logic for 2024-01-05.\n", "2024-01-08: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-08 ---\n", "--- END DEBUGGING EXITS ---\n", "2024-01-08  Longs=0 Shorts=5 Positions=0 Cash=$100,000 Equity=$100,000\n", "  ENTRY: SHORT POL   | Price:    7.00 | Qty: -142\n", "  ENTRY: SHORT SIDU  | Price:   12.00 | Qty:  -83\n", "  ENTRY: SHORT ENTA  | Price:   11.74 | Qty:  -85\n", "  ENTRY: SHORT CING  | Price:    6.53 | Qty: -153\n", "  ENTRY: SHORT MULN  | Price:   13.17 | Qty:  -75\n", "2024-01-09: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-09 ---\n", "  EXIT: SHORT POL   | Reason: STOP-LOSS | Entry:    7.00 | Exit:    7.35 | Qty: -142 | PnL: $  -50.22\n", "  EXIT: SHORT SIDU  | Reason: STOP-LOSS | Entry:   12.00 | Exit:   12.60 | Qty:  -83 | PnL: $  -50.32\n", "  EXIT: SHORT ENTA  | Reason: STOP-LOSS | Entry:   11.74 | Exit:   12.33 | Qty:  -85 | PnL: $  -50.42\n", "  EXIT: SHORT CING  | Reason: STOP-LOSS | Entry:    6.53 | Exit:    6.86 | Qty: -153 | PnL: $  -50.48\n", "  EXIT: SHORT MULN  | Reason: STOP-LOSS | Entry:   13.17 | Exit:   13.83 | Qty:  -75 | PnL: $  -49.91\n", "--- END DEBUGGING EXITS ---\n", "2024-01-09  Longs=0 Shorts=4 Positions=0 Cash=$99,746 Equity=$99,746\n", "  ENTRY: SHORT POL   | Price:    7.91 | Qty: -126\n", "  ENTRY: SHORT FWBI  | Price:    5.07 | Qty: -196\n", "  ENTRY: SHORT NEXI  | Price:    8.12 | Qty: -122\n", "  ENTRY: SHORT SIDU  | Price:   15.70 | Qty:  -63\n", "2024-01-10: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-10 ---\n", "  EXIT: SHORT POL   | Reason: STOP-LOSS | Entry:    7.91 | Exit:    8.31 | Qty: -126 | PnL: $  -50.36\n", "  EXIT: SHORT NEXI  | Reason: STOP-LOSS | Entry:    8.12 | Exit:    8.53 | Qty: -122 | PnL: $  -50.05\n", "  EXIT: SHORT SIDU  | Reason: STOP-LOSS | Entry:   15.70 | Exit:   16.48 | Qty:  -63 | PnL: $  -49.97\n", "--- END DEBUGGING EXITS ---\n", "2024-01-10  Longs=0 Shorts=5 Positions=1 Cash=$100,588 Equity=$99,629\n", "  ENTRY: SHORT POL   | Price:    9.36 | Qty: -106\n", "  ENTRY: SHORT SIDU  | Price:   16.67 | Qty:  -59\n", "  ENTRY: SHORT NEXI  | Price:   11.20 | Qty:  -88\n", "  ENTRY: SHORT INGN  | Price:    6.01 | Qty: -165\n", "  ENTRY: SHORT ENTA  | Price:   11.79 | Qty:  -84\n", "2024-01-11: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-11 ---\n", "  EXIT: SHORT POL   | Reason: STOP-LOSS | Entry:    9.36 | Exit:    9.83 | Qty: -106 | PnL: $  -50.13\n", "  EXIT: SHORT NEXI  | Reason: STOP-LOSS | Entry:   11.20 | Exit:   11.76 | Qty:  -88 | PnL: $  -49.80\n", "  EXIT: SHORT INGN  | Reason: STOP-LOSS | Entry:    6.01 | Exit:    6.31 | Qty: -165 | PnL: $  -50.10\n", "--- END DEBUGGING EXITS ---\n", "2024-01-11  Longs=0 Shorts=5 Positions=3 Cash=$102,409 Equity=$99,766\n", "  ENTRY: SHORT NEXI  | Price:   12.30 | Qty:  -81\n", "  ENTRY: SHORT POL   | Price:    9.85 | Qty: -101\n", "  ENTRY: SHORT MULN  | Price:   13.71 | Qty:  -72\n", "  ENTRY: SHORT PIXY  | Price:    6.41 | Qty: -155\n", "2024-01-12: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-12 ---\n", "  EXIT: SHORT NEXI  | Reason: STOP-LOSS | Entry:   12.30 | Exit:   12.92 | Qty:  -81 | PnL: $  -50.34\n", "--- END DEBUGGING EXITS ---\n", "2024-01-12  Longs=0 Shorts=4 Positions=6 Cash=$105,332 Equity=$100,115\n", "  ENTRY: SHORT NEXI  | Price:   11.89 | Qty:  -84\n", "2024-01-16: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-16 ---\n", "--- END DEBUGGING EXITS ---\n", "2024-01-16  Longs=0 Shorts=4 Positions=7 Cash=$106,331 Equity=$100,543\n", "  ENTRY: SHORT CING  | Price:    6.74 | Qty: -149\n", "2024-01-17: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-17 ---\n", "--- END DEBUGGING EXITS ---\n", "2024-01-17  Longs=0 Shorts=5 Positions=8 Cash=$107,334 Equity=$100,654\n", "  ENTRY: SHORT VVOS  | Price:    7.08 | Qty: -142\n", "2024-01-18: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-18 ---\n", "  EXIT: SHORT FWBI  | Reason: AGED-OUT  | Entry:    5.07 | Exit:    3.77 | Qty: -196 | PnL: $  254.43\n", "--- END DEBUGGING EXITS ---\n", "2024-01-18  Longs=0 Shorts=4 Positions=8 Cash=$107,600 Equity=$101,104\n", "2024-01-19: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-19 ---\n", "  EXIT: SHORT SIDU  | Reason: AGED-OUT  | Entry:   16.67 | Exit:    8.50 | Qty:  -59 | PnL: $  481.78\n", "  EXIT: SHORT ENTA  | Reason: AGED-OUT  | Entry:   11.79 | Exit:   11.65 | Qty:  -84 | PnL: $   11.27\n", "--- END DEBUGGING EXITS ---\n", "2024-01-19  Longs=0 Shorts=3 Positions=6 Cash=$106,119 Equity=$101,377\n", "  ENTRY: SHORT SIDU  | Price:    8.50 | Qty: -119\n", "2024-01-22: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-22 ---\n", "  EXIT: SHORT POL   | Reason: AGED-OUT  | Entry:    9.85 | Exit:    5.35 | Qty: -101 | PnL: $  454.23\n", "  EXIT: SHORT MULN  | Reason: AGED-OUT  | Entry:   13.71 | Exit:    7.72 | Qty:  -72 | PnL: $  431.00\n", "  EXIT: SHORT PIXY  | Reason: AGED-OUT  | Entry:    6.41 | Exit:    5.87 | Qty: -155 | PnL: $   83.46\n", "--- END DEBUGGING EXITS ---\n", "2024-01-22  Longs=0 Shorts=3 Positions=4 Cash=$105,123 Equity=$101,985\n", "  ENTRY: SHORT POL   | Price:    5.35 | Qty: -190\n", "  ENTRY: SHORT MULN  | Price:    7.72 | Qty: -132\n", "2024-01-23: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-23 ---\n", "  EXIT: SHORT NEXI  | Reason: AGED-OUT  | Entry:   11.89 | Exit:    5.51 | Qty:  -84 | PnL: $  535.69\n", "--- END DEBUGGING EXITS ---\n", "2024-01-23  Longs=0 Shorts=3 Positions=5 Cash=$106,694 Equity=$102,338\n", "  ENTRY: SHORT NEXI  | Price:    5.51 | Qty: -185\n", "2024-01-24: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-24 ---\n", "  EXIT: SHORT CING  | Reason: AGED-OUT  | Entry:    6.74 | Exit:    3.57 | Qty: -149 | PnL: $  472.06\n", "  EXIT: SHORT SIDU  | Reason: STOP-LOSS | Entry:    8.50 | Exit:    8.93 | Qty: -119 | PnL: $  -51.11\n", "--- END DEBUGGING EXITS ---\n", "2024-01-24  Longs=0 Shorts=3 Positions=4 Cash=$106,119 Equity=$102,414\n", "  ENTRY: SHORT RUM   | Price:    6.37 | Qty: -160\n", "  ENTRY: SHORT MBI   | Price:    6.51 | Qty: -157\n", "  ENTRY: SHORT SIDU  | Price:    8.10 | Qty: -126\n", "2024-01-25: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-25 ---\n", "  EXIT: SHORT VVOS  | Reason: AGED-OUT  | Entry:    7.08 | Exit:    5.61 | Qty: -142 | PnL: $  207.86\n", "  EXIT: SHORT SIDU  | Reason: STOP-LOSS | Entry:    8.10 | Exit:    8.51 | Qty: -126 | PnL: $  -51.57\n", "--- END DEBUGGING EXITS ---\n", "2024-01-25  Longs=0 Shorts=4 Positions=5 Cash=$107,309 Equity=$102,631\n", "  ENTRY: SHORT NVVE  | Price:    4.76 | Qty: -215\n", "  ENTRY: SHORT SIDU  | Price:    8.50 | Qty: -120\n", "2024-01-26: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-26 ---\n", "  EXIT: SHORT SIDU  | Reason: STOP-LOSS | Entry:    8.50 | Exit:    8.93 | Qty: -120 | PnL: $  -51.54\n", "--- END DEBUGGING EXITS ---\n", "2024-01-26  Longs=0 Shorts=5 Positions=6 Cash=$108,280 Equity=$102,724\n", "  ENTRY: SHORT ULCC  | Price:    5.42 | Qty: -189\n", "  ENTRY: SHORT SIDU  | Price:    8.83 | Qty: -116\n", "  ENTRY: SHORT SPRC  | Price:    6.03 | Qty: -170\n", "2024-01-29: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-29 ---\n", "  EXIT: SHORT NEXI  | Reason: STOP-LOSS | Entry:    5.51 | Exit:    5.79 | Qty: -185 | PnL: $  -51.50\n", "  EXIT: SHORT RUM   | Reason: STOP-LOSS | Entry:    6.37 | Exit:    6.69 | Qty: -160 | PnL: $  -51.50\n", "  EXIT: SHORT SIDU  | Reason: STOP-LOSS | Entry:    8.83 | Exit:    9.27 | Qty: -116 | PnL: $  -51.75\n", "--- END DEBUGGING EXITS ---\n", "2024-01-29  Longs=0 Shorts=2 Positions=6 Cash=$108,135 Equity=$102,934\n", "  ENTRY: SHORT ARQT  | Price:    5.48 | Qty: -187\n", "  ENTRY: SHORT SIDU  | Price:    8.85 | Qty: -116\n", "2024-01-30: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-30 ---\n", "  EXIT: SHORT POL   | Reason: STOP-LOSS | Entry:    5.35 | Exit:    5.62 | Qty: -190 | PnL: $  -51.36\n", "  EXIT: SHORT MULN  | Reason: AGED-OUT  | Entry:    7.72 | Exit:    7.50 | Qty: -132 | PnL: $   28.54\n", "  EXIT: SHORT NVVE  | Reason: STOP-LOSS | Entry:    4.76 | Exit:    5.00 | Qty: -215 | PnL: $  -51.71\n", "  EXIT: SHORT ARQT  | Reason: STOP-LOSS | Entry:    5.48 | Exit:    5.75 | Qty: -187 | PnL: $  -51.78\n", "--- END DEBUGGING EXITS ---\n", "2024-01-30  Longs=0 Shorts=5 Positions=4 Cash=$105,975 Equity=$102,267\n", "  ENTRY: SHORT ARQT  | Price:    5.94 | Qty: -172\n", "  ENTRY: SHORT HUT   | Price:    8.26 | Qty: -123\n", "  ENTRY: SHORT CRBP  | Price:   21.01 | Qty:  -48\n", "  ENTRY: SHORT DWAC  | Price:   34.67 | Qty:  -29\n", "2024-01-31: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-01-31 ---\n", "  EXIT: SHORT ARQT  | Reason: STOP-LOSS | Entry:    5.94 | Exit:    6.24 | Qty: -172 | PnL: $  -51.62\n", "  EXIT: SHORT CRBP  | Reason: STOP-LOSS | Entry:   21.01 | Exit:   22.06 | Qty:  -48 | PnL: $  -50.95\n", "  EXIT: SHORT DWAC  | Reason: STOP-LOSS | Entry:   34.67 | Exit:   36.40 | Qty:  -29 | PnL: $  -50.80\n", "--- END DEBUGGING EXITS ---\n", "2024-01-31  Longs=0 Shorts=5 Positions=5 Cash=$106,836 Equity=$102,713\n", "  ENTRY: SHORT ARQT  | Price:    5.84 | Qty: -175\n", "  ENTRY: SHORT NEXI  | Price:   19.19 | Qty:  -53\n", "  ENTRY: SHORT CRBP  | Price:   28.45 | Qty:  -36\n", "  ENTRY: SHORT POL   | Price:    5.41 | Qty: -189\n", "2024-02-01: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-01 ---\n", "  EXIT: SHORT MBI   | Reason: AGED-OUT  | Entry:    6.51 | Exit:    6.09 | Qty: -157 | PnL: $   65.46\n", "  EXIT: SHORT ARQT  | Reason: STOP-LOSS | Entry:    5.84 | Exit:    6.13 | Qty: -175 | PnL: $  -51.64\n", "--- END DEBUGGING EXITS ---\n", "2024-02-01  Longs=0 Shorts=4 Positions=7 Cash=$108,889 Equity=$103,147\n", "  ENTRY: SHORT TSVT  | Price:    5.30 | Qty: -194\n", "  ENTRY: SHORT ARQT  | Price:    5.65 | Qty: -182\n", "2024-02-02: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-02 ---\n", "  EXIT: SHORT TSVT  | Reason: STOP-LOSS | Entry:    5.30 | Exit:    5.57 | Qty: -194 | PnL: $  -51.95\n", "  EXIT: SHORT ARQT  | Reason: STOP-LOSS | Entry:    5.65 | Exit:    5.93 | Qty: -182 | PnL: $  -51.95\n", "--- END DEBUGGING EXITS ---\n", "2024-02-02  Longs=0 Shorts=6 Positions=7 Cash=$108,784 Equity=$102,989\n", "  ENTRY: SHORT TSVT  | Price:    5.13 | Qty: -200\n", "  ENTRY: SHORT MINM  | Price:    7.12 | Qty: -144\n", "  ENTRY: SHORT MULN  | Price:    7.20 | Qty: -143\n", "  ENTRY: SHORT INGN  | Price:    7.19 | Qty: -143\n", "  ENTRY: SHORT VVOS  | Price:    6.30 | Qty: -163\n", "2024-02-05: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-05 ---\n", "  EXIT: SHORT ULCC  | Reason: AGED-OUT  | Entry:    5.42 | Exit:    5.35 | Qty: -189 | PnL: $   12.72\n", "  EXIT: SHORT SPRC  | Reason: AGED-OUT  | Entry:    6.03 | Exit:    3.67 | Qty: -170 | PnL: $  400.89\n", "  EXIT: SHORT TSVT  | Reason: STOP-LOSS | Entry:    5.13 | Exit:    5.39 | Qty: -200 | PnL: $  -51.84\n", "--- END DEBUGGING EXITS ---\n", "2024-02-05  Longs=0 Shorts=7 Positions=9 Cash=$111,204 Equity=$103,301\n", "  ENTRY: SHORT TSVT  | Price:    5.30 | Qty: -194\n", "  ENTRY: SHORT ARQT  | Price:    6.02 | Qty: -171\n", "2024-02-06: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-06 ---\n", "  EXIT: SHORT SIDU  | Reason: AGED-OUT  | Entry:    8.85 | Exit:    4.35 | Qty: -116 | PnL: $  521.75\n", "  EXIT: SHORT MULN  | Reason: STOP-LOSS | Entry:    7.20 | Exit:    7.56 | Qty: -143 | PnL: $  -52.02\n", "  EXIT: SHORT TSVT  | Reason: STOP-LOSS | Entry:    5.30 | Exit:    5.57 | Qty: -194 | PnL: $  -51.95\n", "--- END DEBUGGING EXITS ---\n", "2024-02-06  Longs=0 Shorts=4 Positions=8 Cash=$110,594 Equity=$103,552\n", "  ENTRY: SHORT MULN  | Price:    7.11 | Qty: -145\n", "  ENTRY: SHORT TSVT  | Price:    5.60 | Qty: -184\n", "2024-02-07: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-07 ---\n", "  EXIT: SHORT HUT   | Reason: AGED-OUT  | Entry:    8.26 | Exit:    6.60 | Qty: -123 | PnL: $  203.77\n", "  EXIT: SHORT INGN  | Reason: STOP-LOSS | Entry:    7.19 | Exit:    7.55 | Qty: -143 | PnL: $  -51.95\n", "--- END DEBUGGING EXITS ---\n", "2024-02-07  Longs=0 Shorts=4 Positions=8 Cash=$110,762 Equity=$103,802\n", "  ENTRY: SHORT GNPX  | Price:    5.78 | Qty: -179\n", "2024-02-08: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-08 ---\n", "  EXIT: SHORT NEXI  | Reason: AGED-OUT  | Entry:   19.19 | Exit:    9.10 | Qty:  -53 | PnL: $  534.53\n", "  EXIT: SHORT CRBP  | Reason: AGED-OUT  | Entry:   28.45 | Exit:   23.71 | Qty:  -36 | PnL: $  170.21\n", "  EXIT: SHORT POL   | Reason: AGED-OUT  | Entry:    5.41 | Exit:    4.37 | Qty: -189 | PnL: $  196.15\n", "  EXIT: SHORT TSVT  | Reason: STOP-LOSS | Entry:    5.60 | Exit:    5.88 | Qty: -184 | PnL: $  -52.06\n", "--- END DEBUGGING EXITS ---\n", "2024-02-08  Longs=0 Shorts=2 Positions=5 Cash=$108,551 Equity=$104,146\n", "  ENTRY: SHORT ULCC  | Price:    6.95 | Qty: -149\n", "  ENTRY: SHORT NEXI  | Price:    9.10 | Qty: -114\n", "2024-02-09: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-09 ---\n", "  EXIT: SHORT ARQT  | Reason: STOP-LOSS | Entry:    6.02 | Exit:    6.32 | Qty: -171 | PnL: $  -52.01\n", "  EXIT: SHORT ULCC  | Reason: STOP-LOSS | Entry:    6.95 | Exit:    7.30 | Qty: -149 | PnL: $  -52.32\n", "  EXIT: SHORT NEXI  | Reason: STOP-LOSS | Entry:    9.10 | Exit:    9.55 | Qty: -114 | PnL: $  -52.41\n", "--- END DEBUGGING EXITS ---\n", "2024-02-09  Longs=0 Shorts=1 Positions=4 Cash=$107,363 Equity=$104,100\n", "2024-02-12: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-12 ---\n", "  EXIT: SHORT MINM  | Reason: AGED-OUT  | Entry:    7.12 | Exit:    4.60 | Qty: -144 | PnL: $  362.55\n", "  EXIT: SHORT VVOS  | Reason: AGED-OUT  | Entry:    6.30 | Exit:    5.30 | Qty: -163 | PnL: $  162.57\n", "  EXIT: SHORT MULN  | Reason: STOP-LOSS | Entry:    7.11 | Exit:    7.47 | Qty: -145 | PnL: $  -52.09\n", "--- END DEBUGGING EXITS ---\n", "2024-02-12  Longs=0 Shorts=5 Positions=1 Cash=$104,753 Equity=$103,933\n", "  ENTRY: SHORT FHTX  | Price:    5.92 | Qty: -175\n", "  ENTRY: SHORT IREN  | Price:    6.07 | Qty: -171\n", "  ENTRY: SHORT AGL   | Price:    6.89 | Qty: -150\n", "  ENTRY: SHORT RGNX  | Price:   16.29 | Qty:  -63\n", "  ENTRY: SHORT MULN  | Price:    7.01 | Qty: -148\n", "2024-02-13: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-13 ---\n", "  EXIT: SHORT FHTX  | Reason: STOP-LOSS | Entry:    5.92 | Exit:    6.22 | Qty: -175 | PnL: $  -52.34\n", "  EXIT: SHORT IREN  | Reason: STOP-LOSS | Entry:    6.07 | Exit:    6.37 | Qty: -171 | PnL: $  -52.44\n", "  EXIT: SHORT AGL   | Reason: STOP-LOSS | Entry:    6.89 | Exit:    7.23 | Qty: -150 | PnL: $  -52.22\n", "  EXIT: SHORT MULN  | Reason: STOP-LOSS | Entry:    7.01 | Exit:    7.36 | Qty: -148 | PnL: $  -52.42\n", "--- END DEBUGGING EXITS ---\n", "2024-02-13  Longs=0 Shorts=1 Positions=2 Cash=$105,567 Equity=$103,597\n", "  ENTRY: SHORT GREE  | Price:    4.42 | Qty: -234\n", "2024-02-14: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-14 ---\n", "  EXIT: SHORT RGNX  | Reason: STOP-LOSS | Entry:   16.29 | Exit:   17.10 | Qty:  -63 | PnL: $  -51.85\n", "  EXIT: SHORT GREE  | Reason: STOP-LOSS | Entry:    4.42 | Exit:    4.64 | Qty: -234 | PnL: $  -52.26\n", "--- END DEBUGGING EXITS ---\n", "2024-02-14  Longs=0 Shorts=3 Positions=1 Cash=$104,437 Equity=$103,565\n", "  ENTRY: SHORT CDMO  | Price:    6.88 | Qty: -150\n", "  ENTRY: SHORT MULN  | Price:    7.10 | Qty: -145\n", "  ENTRY: SHORT HUT   | Price:    9.66 | Qty: -107\n", "2024-02-15: BEAR market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-15 ---\n", "  EXIT: SHORT GNPX  | Reason: AGED-OUT  | Entry:    5.78 | Exit:    4.46 | Qty: -179 | PnL: $  235.88\n", "  EXIT: SHORT CDMO  | Reason: STOP-LOSS | Entry:    6.88 | Exit:    7.22 | Qty: -150 | PnL: $  -52.14\n", "  EXIT: SHORT MULN  | Reason: STOP-LOSS | Entry:    7.10 | Exit:    7.46 | Qty: -145 | PnL: $  -52.02\n", "  EXIT: SHORT HUT   | Reason: STOP-LOSS | Entry:    9.66 | Exit:   10.14 | Qty: -107 | PnL: $  -52.22\n", "--- END DEBUGGING EXITS ---\n", "2024-02-15  Longs=0 Shorts=0 Positions=0 Cash=$103,480 Equity=$103,480\n", "2024-02-16: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-16 ---\n", "--- END DEBUGGING EXITS ---\n", "2024-02-16  Longs=0 Shorts=2 Positions=0 Cash=$103,480 Equity=$103,480\n", "  ENTRY: SHORT MULN  | Price:    7.95 | Qty: -130\n", "  ENTRY: SHORT CDMO  | Price:    7.55 | Qty: -137\n", "2024-02-20: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-20 ---\n", "  EXIT: SHORT MULN  | Reason: STOP-LOSS | Entry:    7.95 | Exit:    8.35 | Qty: -130 | PnL: $  -52.22\n", "--- END DEBUGGING EXITS ---\n", "2024-02-20  Longs=0 Shorts=3 Positions=1 Cash=$104,461 Equity=$103,434\n", "  ENTRY: SHORT PLCE  | Price:   29.12 | Qty:  -35\n", "  ENTRY: SHORT CRBP  | Price:   23.80 | Qty:  -43\n", "  ENTRY: SHORT MULN  | Price:    9.14 | Qty: -113\n", "2024-02-21: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-21 ---\n", "  EXIT: SHORT CRBP  | Reason: STOP-LOSS | Entry:   23.80 | Exit:   24.99 | Qty:  -43 | PnL: $  -51.71\n", "--- END DEBUGGING EXITS ---\n", "2024-02-21  Longs=0 Shorts=2 Positions=3 Cash=$106,460 Equity=$103,682\n", "  ENTRY: SHORT CPOP  | Price:    4.85 | Qty: -213\n", "  ENTRY: SHORT PHAT  | Price:    9.94 | Qty: -104\n", "2024-02-22: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-22 ---\n", "  EXIT: SHORT CDMO  | Reason: STOP-LOSS | Entry:    7.55 | Exit:    7.93 | Qty: -137 | PnL: $  -52.26\n", "  EXIT: SHORT CPOP  | Reason: STOP-LOSS | Entry:    4.85 | Exit:    5.09 | Qty: -213 | PnL: $  -52.19\n", "  EXIT: SHORT PHAT  | Reason: STOP-LOSS | Entry:    9.94 | Exit:   10.44 | Qty: -104 | PnL: $  -52.23\n", "--- END DEBUGGING EXITS ---\n", "2024-02-22  Longs=0 Shorts=3 Positions=2 Cash=$105,268 Equity=$103,643\n", "  ENTRY: SHORT CPOP  | Price:    6.36 | Qty: -162\n", "  ENTRY: SHORT VTYX  | Price:    5.08 | Qty: -203\n", "2024-02-23: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-23 ---\n", "  EXIT: SHORT CPOP  | Reason: STOP-LOSS | Entry:    6.36 | Exit:    6.68 | Qty: -162 | PnL: $  -52.06\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:    5.08 | Exit:    5.34 | Qty: -203 | PnL: $  -52.15\n", "--- END DEBUGGING EXITS ---\n", "2024-02-23  Longs=0 Shorts=4 Positions=2 Cash=$105,162 Equity=$103,643\n", "  ENTRY: SHORT VTYX  | Price:    5.62 | Qty: -184\n", "  ENTRY: SHORT CPOP  | Price:    6.59 | Qty: -157\n", "  ENTRY: SHORT NEXI  | Price:    7.19 | Qty: -144\n", "2024-02-26: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-26 ---\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:    5.62 | Exit:    5.90 | Qty: -184 | PnL: $  -52.23\n", "--- END DEBUGGING EXITS ---\n", "2024-02-26  Longs=0 Shorts=4 Positions=4 Cash=$107,179 Equity=$104,091\n", "  ENTRY: SHORT VTYX  | Price:    6.65 | Qty: -156\n", "  ENTRY: SHORT NNOX  | Price:    8.72 | Qty: -119\n", "2024-02-27: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-27 ---\n", "  EXIT: SHORT NEXI  | Reason: STOP-LOSS | Entry:    7.19 | Exit:    7.55 | Qty: -144 | PnL: $  -52.31\n", "  EXIT: SHORT NNOX  | Reason: STOP-LOSS | Entry:    8.72 | Exit:    9.16 | Qty: -119 | PnL: $  -52.43\n", "--- END DEBUGGING EXITS ---\n", "2024-02-27  Longs=0 Shorts=3 Positions=4 Cash=$107,075 Equity=$104,064\n", "  ENTRY: SHORT MRVI  | Price:    8.08 | Qty: -128\n", "  ENTRY: SHORT SNBR  | Price:   17.21 | Qty:  -60\n", "2024-02-28: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-28 ---\n", "  EXIT: SHORT PLCE  | Reason: AGED-OUT  | Entry:   29.12 | Exit:   20.00 | Qty:  -35 | PnL: $  318.85\n", "  EXIT: SHORT MULN  | Reason: AGED-OUT  | Entry:    9.14 | Exit:    7.33 | Qty: -113 | PnL: $  204.12\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:    6.65 | Exit:    6.98 | Qty: -156 | PnL: $  -52.41\n", "--- END DEBUGGING EXITS ---\n", "2024-02-28  Longs=0 Shorts=4 Positions=3 Cash=$106,522 Equity=$103,955\n", "  ENTRY: SHORT VTYX  | Price:    6.60 | Qty: -157\n", "  ENTRY: SHORT VRM   | Price:   12.51 | Qty:  -83\n", "2024-02-29: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-02-29 ---\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:    6.60 | Exit:    6.93 | Qty: -157 | PnL: $  -52.35\n", "--- END DEBUGGING EXITS ---\n", "2024-02-29  Longs=0 Shorts=9 Positions=4 Cash=$107,507 Equity=$103,976\n", "  ENTRY: SHORT BIG   | Price:    5.52 | Qty: -188\n", "  ENTRY: SHORT RAPT  | Price:    8.87 | Qty: -117\n", "  ENTRY: SHORT VTYX  | Price:    8.01 | Qty: -129\n", "  ENTRY: SHORT NEXI  | Price:    6.80 | Qty: -152\n", "  ENTRY: SHORT SIDU  | Price:    6.84 | Qty: -152\n", "  ENTRY: SHORT AGTI  | Price:    9.87 | Qty: -105\n", "2024-03-01: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-01 ---\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:    8.01 | Exit:    8.41 | Qty: -129 | PnL: $  -52.21\n", "--- END DEBUGGING EXITS ---\n", "2024-03-01  Longs=0 Shorts=5 Positions=9 Cash=$112,637 Equity=$103,696\n", "  ENTRY: SHORT RILY  | Price:   16.11 | Qty:  -64\n", "  ENTRY: SHORT VTYX  | Price:    7.38 | Qty: -140\n", "  ENTRY: SHORT BYND  | Price:   10.85 | Qty:  -95\n", "2024-03-04: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-04 ---\n", "  EXIT: SHORT CPOP  | Reason: AGED-OUT  | Entry:    6.59 | Exit:    3.44 | Qty: -157 | PnL: $  494.28\n", "  EXIT: SHORT RILY  | Reason: STOP-LOSS | Entry:   16.11 | Exit:   16.92 | Qty:  -64 | PnL: $  -52.09\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:    7.38 | Exit:    7.75 | Qty: -140 | PnL: $  -52.20\n", "--- END DEBUGGING EXITS ---\n", "2024-03-04  Longs=0 Shorts=6 Positions=9 Cash=$113,021 Equity=$104,193\n", "  ENTRY: SHORT VTYX  | Price:    8.57 | Qty: -121\n", "  ENTRY: SHORT VKTX  | Price:   90.00 | Qty:  -11\n", "  ENTRY: SHORT RVNC  | Price:    6.17 | Qty: -168\n", "2024-03-05: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-05 ---\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:    8.57 | Exit:    9.00 | Qty: -121 | PnL: $  -52.39\n", "  EXIT: SHORT RVNC  | Reason: STOP-LOSS | Entry:    6.17 | Exit:    6.48 | Qty: -168 | PnL: $  -52.37\n", "--- END DEBUGGING EXITS ---\n", "2024-03-05  Longs=0 Shorts=5 Positions=10 Cash=$113,905 Equity=$104,497\n", "  ENTRY: SHORT SGML  | Price:   15.04 | Qty:  -69\n", "  ENTRY: SHORT VTYX  | Price:    8.99 | Qty: -116\n", "  ENTRY: SHORT CDNA  | Price:   11.97 | Qty:  -87\n", "  ENTRY: SHORT PHUN  | Price:   15.99 | Qty:  -65\n", "  ENTRY: SHORT DTIL  | Price:   12.40 | Qty:  -84\n", "2024-03-06: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-06 ---\n", "  EXIT: SHORT MRVI  | Reason: AGED-OUT  | Entry:    8.08 | Exit:    7.11 | Qty: -128 | PnL: $  123.70\n", "  EXIT: SHORT SNBR  | Reason: AGED-OUT  | Entry:   17.21 | Exit:   16.00 | Qty:  -60 | PnL: $   72.12\n", "  EXIT: SHORT VKTX  | Reason: STOP-LOSS | Entry:   90.00 | Exit:   94.50 | Qty:  -11 | PnL: $  -50.02\n", "--- END DEBUGGING EXITS ---\n", "2024-03-06  Longs=0 Shorts=5 Positions=12 Cash=$116,194 Equity=$104,779\n", "  ENTRY: SHORT IFBD  | Price:    6.56 | Qty: -159\n", "  ENTRY: SHORT MINM  | Price:    5.35 | Qty: -195\n", "  ENTRY: SHORT AKRO  | Price:   31.20 | Qty:  -33\n", "2024-03-07: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-07 ---\n", "  EXIT: SHORT VRM   | Reason: STOP-LOSS | Entry:   12.51 | Exit:   13.14 | Qty:  -83 | PnL: $  -52.46\n", "  EXIT: SHORT RAPT  | Reason: STOP-LOSS | Entry:    8.87 | Exit:    9.31 | Qty: -117 | PnL: $  -52.43\n", "  EXIT: SHORT NEXI  | Reason: STOP-LOSS | Entry:    6.80 | Exit:    7.14 | Qty: -152 | PnL: $  -52.22\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:    8.99 | Exit:    9.44 | Qty: -116 | PnL: $  -52.69\n", "  EXIT: SHORT MINM  | Reason: STOP-LOSS | Entry:    5.35 | Exit:    5.62 | Qty: -195 | PnL: $  -52.71\n", "--- END DEBUGGING EXITS ---\n", "2024-03-07  Longs=0 Shorts=4 Positions=10 Cash=$113,850 Equity=$104,406\n", "  ENTRY: SHORT VTYX  | Price:   10.00 | Qty: -104\n", "  ENTRY: SHORT MINM  | Price:    5.93 | Qty: -176\n", "2024-03-08: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-08 ---\n", "  EXIT: SHORT BIG   | Reason: AGED-OUT  | Entry:    5.52 | Exit:    5.26 | Qty: -188 | PnL: $   48.39\n", "  EXIT: SHORT SIDU  | Reason: AGED-OUT  | Entry:    6.84 | Exit:    4.39 | Qty: -152 | PnL: $  372.07\n", "  EXIT: SHORT AGTI  | Reason: AGED-OUT  | Entry:    9.87 | Exit:    9.91 | Qty: -105 | PnL: $   -4.72\n", "  EXIT: SHORT IFBD  | Reason: STOP-LOSS | Entry:    6.56 | Exit:    6.89 | Qty: -159 | PnL: $  -52.70\n", "  EXIT: SHORT VTYX  | Reason: STOP-LOSS | Entry:   10.00 | Exit:   10.50 | Qty: -104 | PnL: $  -52.55\n", "--- END DEBUGGING EXITS ---\n", "2024-03-08  Longs=0 Shorts=4 Positions=7 Cash=$111,047 Equity=$104,534\n", "  ENTRY: SHORT IFBD  | Price:    5.98 | Qty: -174\n", "  ENTRY: SHORT APM   | Price:    6.65 | Qty: -157\n", "  ENTRY: SHORT VTYX  | Price:   10.30 | Qty: -101\n", "2024-03-11: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-11 ---\n", "  EXIT: SHORT BYND  | Reason: AGED-OUT  | Entry:   10.85 | Exit:    8.15 | Qty:  -95 | PnL: $  256.11\n", "  EXIT: SHORT IFBD  | Reason: STOP-LOSS | Entry:    5.98 | Exit:    6.28 | Qty: -174 | PnL: $  -52.57\n", "--- END DEBUGGING EXITS ---\n", "2024-03-11  Longs=0 Shorts=6 Positions=8 Cash=$112,302 Equity=$104,616\n", "  ENTRY: SHORT LYT   | Price:    7.14 | Qty: -146\n", "  ENTRY: SHORT IFBD  | Price:    6.17 | Qty: -169\n", "  ENTRY: SHORT APVO  | Price:    5.17 | Qty: -202\n", "2024-03-12: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-12 ---\n", "--- END DEBUGGING EXITS ---\n", "2024-03-12  Longs=0 Shorts=4 Positions=11 Cash=$115,430 Equity=$105,328\n", "2024-03-13: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-13 ---\n", "  EXIT: SHORT SGML  | Reason: AGED-OUT  | Entry:   15.04 | Exit:   14.39 | Qty:  -69 | PnL: $   44.35\n", "  EXIT: SHORT CDNA  | Reason: AGED-OUT  | Entry:   11.97 | Exit:   11.11 | Qty:  -87 | PnL: $   74.34\n", "  EXIT: SHORT PHUN  | Reason: AGED-OUT  | Entry:   15.99 | Exit:    9.90 | Qty:  -65 | PnL: $  395.53\n", "  EXIT: SHORT DTIL  | Reason: AGED-OUT  | Entry:   12.40 | Exit:   11.92 | Qty:  -84 | PnL: $   39.82\n", "  EXIT: SHORT MINM  | Reason: STOP-LOSS | Entry:    5.93 | Exit:    6.23 | Qty: -176 | PnL: $  -52.73\n", "  EXIT: SHORT APVO  | Reason: STOP-LOSS | Entry:    5.17 | Exit:    5.43 | Qty: -202 | PnL: $  -52.77\n", "--- END DEBUGGING EXITS ---\n", "2024-03-13  Longs=0 Shorts=4 Positions=5 Cash=$109,631 Equity=$105,339\n", "  ENTRY: SHORT PHUN  | Price:    9.90 | Qty: -106\n", "2024-03-14: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-14 ---\n", "  EXIT: SHORT AKRO  | Reason: AGED-OUT  | Entry:   31.20 | Exit:   27.78 | Qty:  -33 | PnL: $  112.40\n", "  EXIT: SHORT APM   | Reason: STOP-LOSS | Entry:    6.65 | Exit:    6.98 | Qty: -157 | PnL: $  -52.75\n", "--- END DEBUGGING EXITS ---\n", "2024-03-14  Longs=0 Shorts=3 Positions=4 Cash=$108,665 Equity=$105,121\n", "  ENTRY: SHORT MULN  | Price:    6.40 | Qty: -164\n", "2024-03-15: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-15 ---\n", "  EXIT: SHORT PHUN  | Reason: STOP-LOSS | Entry:    9.90 | Exit:   10.40 | Qty: -106 | PnL: $  -53.02\n", "--- END DEBUGGING EXITS ---\n", "2024-03-15  Longs=0 Shorts=4 Positions=4 Cash=$108,612 Equity=$105,128\n", "  ENTRY: SHORT VRM   | Price:   15.86 | Qty:  -66\n", "  ENTRY: SHORT PHUN  | Price:    8.73 | Qty: -120\n", "  ENTRY: SHORT PGY   | Price:   11.68 | Qty:  -90\n", "2024-03-18: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-18 ---\n", "  EXIT: SHORT VTYX  | Reason: AGED-OUT  | Entry:   10.30 | Exit:    7.65 | Qty: -101 | PnL: $  267.26\n", "  EXIT: SHORT PHUN  | Reason: STOP-LOSS | Entry:    8.73 | Exit:    9.17 | Qty: -120 | PnL: $  -52.93\n", "--- END DEBUGGING EXITS ---\n", "2024-03-18  Longs=0 Shorts=4 Positions=5 Cash=$109,882 Equity=$105,503\n", "  ENTRY: SHORT PHUN  | Price:   10.43 | Qty: -101\n", "  ENTRY: SHORT NEXI  | Price:    5.90 | Qty: -178\n", "  ENTRY: SHORT APM   | Price:    7.10 | Qty: -148\n", "2024-03-19: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-19 ---\n", "  EXIT: SHORT LYT   | Reason: AGED-OUT  | Entry:    7.14 | Exit:    5.38 | Qty: -146 | PnL: $  256.57\n", "  EXIT: SHORT IFBD  | Reason: AGED-OUT  | Entry:    6.17 | Exit:    4.59 | Qty: -169 | PnL: $  266.63\n", "  EXIT: SHORT APM   | Reason: STOP-LOSS | Entry:    7.10 | Exit:    7.46 | Qty: -148 | PnL: $  -53.09\n", "--- END DEBUGGING EXITS ---\n", "2024-03-19  Longs=0 Shorts=3 Positions=5 Cash=$110,369 Equity=$105,836\n", "  ENTRY: SHORT LYT   | Price:    5.38 | Qty: -196\n", "  ENTRY: SHORT MINM  | Price:    5.89 | Qty: -179\n", "2024-03-20: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-20 ---\n", "  EXIT: SHORT PHUN  | Reason: STOP-LOSS | Entry:   10.43 | Exit:   10.95 | Qty: -101 | PnL: $  -53.22\n", "  EXIT: SHORT MINM  | Reason: STOP-LOSS | Entry:    5.89 | Exit:    6.18 | Qty: -179 | PnL: $  -53.27\n", "--- END DEBUGGING EXITS ---\n", "2024-03-20  Longs=0 Shorts=2 Positions=5 Cash=$110,263 Equity=$105,983\n", "  ENTRY: SHORT MINM  | Price:    6.25 | Qty: -169\n", "2024-03-21: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-21 ---\n", "  EXIT: SHORT MINM  | Reason: STOP-LOSS | Entry:    6.25 | Exit:    6.56 | Qty: -169 | PnL: $  -53.37\n", "--- END DEBUGGING EXITS ---\n", "2024-03-21  Longs=0 Shorts=3 Positions=5 Cash=$110,209 Equity=$105,767\n", "  ENTRY: SHORT APM   | Price:    8.29 | Qty: -127\n", "  ENTRY: SHORT MINM  | Price:    6.65 | Qty: -159\n", "2024-03-22: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-22 ---\n", "  EXIT: SHORT MULN  | Reason: AGED-OUT  | Entry:    6.40 | Exit:    4.58 | Qty: -164 | PnL: $  298.10\n", "--- END DEBUGGING EXITS ---\n", "2024-03-22  Longs=0 Shorts=3 Positions=6 Cash=$111,567 Equity=$105,705\n", "  ENTRY: SHORT PHUN  | Price:    9.86 | Qty: -107\n", "  ENTRY: SHORT MEDS  | Price:   21.14 | Qty:  -50\n", "2024-03-25: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-25 ---\n", "  EXIT: SHORT VRM   | Reason: AGED-OUT  | Entry:   15.86 | Exit:   13.54 | Qty:  -66 | PnL: $  152.67\n", "  EXIT: SHORT PGY   | Reason: AGED-OUT  | Entry:   11.68 | Exit:   10.17 | Qty:  -90 | PnL: $  135.44\n", "  EXIT: SHORT PHUN  | Reason: STOP-LOSS | Entry:    9.86 | Exit:   10.35 | Qty: -107 | PnL: $  -53.30\n", "--- END DEBUGGING EXITS ---\n", "2024-03-25  Longs=0 Shorts=4 Positions=5 Cash=$110,759 Equity=$105,748\n", "  ENTRY: SHORT NOVA  | Price:    5.80 | Qty: -182\n", "  ENTRY: SHORT PHUN  | Price:    9.60 | Qty: -110\n", "  ENTRY: SHORT JRVR  | Price:    8.70 | Qty: -121\n", "2024-03-26: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-26 ---\n", "  EXIT: SHORT NEXI  | Reason: AGED-OUT  | Entry:    5.90 | Exit:    5.70 | Qty: -178 | PnL: $   35.98\n", "  EXIT: SHORT APM   | Reason: STOP-LOSS | Entry:    8.29 | Exit:    8.70 | Qty: -127 | PnL: $  -53.19\n", "  EXIT: SHORT NOVA  | Reason: STOP-LOSS | Entry:    5.80 | Exit:    6.09 | Qty: -182 | PnL: $  -53.33\n", "  EXIT: SHORT PHUN  | Reason: STOP-LOSS | Entry:    9.60 | Exit:   10.08 | Qty: -110 | PnL: $  -53.35\n", "--- END DEBUGGING EXITS ---\n", "2024-03-26  Longs=0 Shorts=3 Positions=4 Cash=$109,584 Equity=$106,335\n", "  ENTRY: SHORT NOVA  | Price:    5.71 | Qty: -186\n", "  ENTRY: SHORT APM   | Price:    7.52 | Qty: -141\n", "  ENTRY: SHORT SWVL  | Price:   11.13 | Qty:  -95\n", "2024-03-27: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-27 ---\n", "  EXIT: SHORT LYT   | Reason: AGED-OUT  | Entry:    5.38 | Exit:    4.10 | Qty: -196 | PnL: $  250.48\n", "  EXIT: SHORT JRVR  | Reason: STOP-LOSS | Entry:    8.70 | Exit:    9.13 | Qty: -121 | PnL: $  -53.19\n", "  EXIT: SHORT NOVA  | Reason: STOP-LOSS | Entry:    5.71 | Exit:    6.00 | Qty: -186 | PnL: $  -53.66\n", "  EXIT: SHORT APM   | Reason: STOP-LOSS | Entry:    7.52 | Exit:    7.90 | Qty: -141 | PnL: $  -53.57\n", "  EXIT: SHORT SWVL  | Reason: STOP-LOSS | Entry:   11.13 | Exit:   11.69 | Qty:  -95 | PnL: $  -53.42\n", "--- END DEBUGGING EXITS ---\n", "2024-03-27  Longs=0 Shorts=1 Positions=2 Cash=$107,512 Equity=$106,081\n", "  ENTRY: SHORT APM   | Price:    8.58 | Qty: -123\n", "2024-03-28: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-03-28 ---\n", "  EXIT: SHORT APM   | Reason: STOP-LOSS | Entry:    8.58 | Exit:    9.01 | Qty: -123 | PnL: $  -53.32\n", "--- END DEBUGGING EXITS ---\n", "2024-03-28  Longs=0 Shorts=0 Positions=2 Cash=$107,458 Equity=$106,021\n", "2024-04-01: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-01 ---\n", "  EXIT: SHORT MINM  | Reason: AGED-OUT  | Entry:    6.65 | Exit:    6.25 | Qty: -159 | PnL: $   63.10\n", "--- END DEBUGGING EXITS ---\n", "2024-04-01  Longs=0 Shorts=1 Positions=1 Cash=$106,463 Equity=$105,951\n", "  ENTRY: SHORT APM   | Price:    9.48 | Qty: -111\n", "2024-04-02: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-02 ---\n", "  EXIT: SHORT MEDS  | Reason: AGED-OUT  | Entry:   21.14 | Exit:    9.99 | Qty:  -50 | PnL: $  557.25\n", "  EXIT: SHORT APM   | Reason: STOP-LOSS | Entry:    9.48 | Exit:    9.95 | Qty: -111 | PnL: $  -53.17\n", "--- END DEBUGGING EXITS ---\n", "2024-04-02  Longs=0 Shorts=1 Positions=0 Cash=$105,910 Equity=$105,910\n", "  ENTRY: SHORT APM   | Price:    9.68 | Qty: -109\n", "2024-04-03: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-03 ---\n", "  EXIT: SHORT APM   | Reason: STOP-LOSS | Entry:    9.68 | Exit:   10.16 | Qty: -109 | PnL: $  -53.31\n", "--- END DEBUGGING EXITS ---\n", "2024-04-03  Longs=0 Shorts=1 Positions=0 Cash=$105,856 Equity=$105,856\n", "  ENTRY: SHORT APM   | Price:    9.79 | Qty: -108\n", "2024-04-04: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-04 ---\n", "  EXIT: SHORT APM   | Reason: STOP-LOSS | Entry:    9.79 | Exit:   10.28 | Qty: -108 | PnL: $  -53.42\n", "--- END DEBUGGING EXITS ---\n", "2024-04-04  Longs=0 Shorts=4 Positions=0 Cash=$105,802 Equity=$105,802\n", "  ENTRY: SHORT APM   | Price:   10.76 | Qty:  -98\n", "  ENTRY: SHORT GCT   | Price:   36.48 | Qty:  -29\n", "  ENTRY: SHORT PYXS  | Price:    5.64 | Qty: -187\n", "  ENTRY: SHORT VRM   | Price:   11.62 | Qty:  -91\n", "2024-04-05: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-05 ---\n", "  EXIT: SHORT VRM   | Reason: STOP-LOSS | Entry:   11.62 | Exit:   12.20 | Qty:  -91 | PnL: $  -53.43\n", "--- END DEBUGGING EXITS ---\n", "2024-04-05  Longs=0 Shorts=4 Positions=3 Cash=$108,914 Equity=$105,964\n", "  ENTRY: SHORT CGC   | Price:    9.70 | Qty: -109\n", "  ENTRY: SHORT SWVL  | Price:   15.04 | Qty:  -70\n", "2024-04-08: BEAR market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-08 ---\n", "  EXIT: SHORT CGC   | Reason: STOP-LOSS | Entry:    9.70 | Exit:   10.19 | Qty: -109 | PnL: $  -53.42\n", "  EXIT: SHORT SWVL  | Reason: STOP-LOSS | Entry:   15.04 | Exit:   15.79 | Qty:  -70 | PnL: $  -53.19\n", "--- END DEBUGGING EXITS ---\n", "2024-04-08  Longs=0 Shorts=1 Positions=3 Cash=$108,806 Equity=$105,993\n", "  ENTRY: SHORT MEDS  | Price:    9.17 | Qty: -115\n", "2024-04-09: BEAR market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-09 ---\n", "--- END DEBUGGING EXITS ---\n", "2024-04-09  Longs=0 Shorts=1 Positions=4 Cash=$109,860 Equity=$105,968\n", "  ENTRY: SHORT ACB   | Price:    6.61 | Qty: -160\n", "2024-04-10: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-10 ---\n", "  EXIT: SHORT ACB   | Reason: STOP-LOSS | Entry:    6.61 | Exit:    6.94 | Qty: -160 | PnL: $  -53.44\n", "--- END DEBUGGING EXITS ---\n", "2024-04-10  Longs=0 Shorts=1 Positions=4 Cash=$109,806 Equity=$106,017\n", "  ENTRY: SHORT PHUN  | Price:    7.59 | Qty: -139\n", "2024-04-11: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-11 ---\n", "--- END DEBUGGING EXITS ---\n", "2024-04-11  Longs=0 Shorts=2 Positions=5 Cash=$110,861 Equity=$106,123\n", "  ENTRY: SHORT IFBD  | Price:    5.45 | Qty: -194\n", "2024-04-12: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-12 ---\n", "  EXIT: SHORT APM   | Reason: AGED-OUT  | Entry:   10.76 | Exit:    8.49 | Qty:  -98 | PnL: $  222.04\n", "  EXIT: SHORT GCT   | Reason: AGED-OUT  | Entry:   36.48 | Exit:   33.16 | Qty:  -29 | PnL: $   95.80\n", "  EXIT: SHORT PYXS  | Reason: AGED-OUT  | Entry:    5.64 | Exit:    5.29 | Qty: -187 | PnL: $   64.96\n", "--- END DEBUGGING EXITS ---\n", "2024-04-12  Longs=0 Shorts=4 Positions=3 Cash=$109,133 Equity=$106,079\n", "  ENTRY: SHORT VRM   | Price:   12.02 | Qty:  -88\n", "  ENTRY: SHORT RENT  | Price:   18.73 | Qty:  -56\n", "2024-04-15: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-15 ---\n", "  EXIT: SHORT RENT  | Reason: STOP-LOSS | Entry:   18.73 | Exit:   19.67 | Qty:  -56 | PnL: $  -52.99\n", "--- END DEBUGGING EXITS ---\n", "2024-04-15  Longs=0 Shorts=4 Positions=4 Cash=$110,137 Equity=$106,324\n", "  ENTRY: SHORT RENT  | Price:   21.99 | Qty:  -48\n", "  ENTRY: SHORT MINM  | Price:    5.21 | Qty: -204\n", "2024-04-16: BEAR market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-16 ---\n", "  EXIT: SHORT MEDS  | Reason: AGED-OUT  | Entry:    9.17 | Exit:    7.46 | Qty: -115 | PnL: $  196.22\n", "--- END DEBUGGING EXITS ---\n", "2024-04-16  Longs=0 Shorts=2 Positions=5 Cash=$111,396 Equity=$107,047\n", "2024-04-17: BEAR market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-17 ---\n", "--- END DEBUGGING EXITS ---\n", "2024-04-17  Longs=0 Shorts=1 Positions=5 Cash=$111,396 Equity=$107,236\n", "2024-04-18: BEAR market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-18 ---\n", "  EXIT: SHORT PHUN  | Reason: AGED-OUT  | Entry:    7.59 | Exit:    6.05 | Qty: -139 | PnL: $  213.64\n", "--- END DEBUGGING EXITS ---\n", "2024-04-18  Longs=0 Shorts=3 Positions=4 Cash=$110,554 Equity=$107,258\n", "  ENTRY: SHORT WISA  | Price:    6.00 | Qty: -178\n", "  ENTRY: SHORT PALI  | Price:    6.05 | Qty: -177\n", "2024-04-19: BEAR market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-19 ---\n", "  EXIT: SHORT IFBD  | Reason: AGED-OUT  | Entry:    5.45 | Exit:    3.94 | Qty: -194 | PnL: $  292.56\n", "  EXIT: SHORT WISA  | Reason: STOP-LOSS | Entry:    6.00 | Exit:    6.30 | Qty: -178 | PnL: $  -53.96\n", "--- END DEBUGGING EXITS ---\n", "2024-04-19  Longs=0 Shorts=3 Positions=4 Cash=$110,805 Equity=$107,455\n", "  ENTRY: SHORT WISA  | Price:    5.99 | Qty: -179\n", "2024-04-22: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-22 ---\n", "  EXIT: SHORT VRM   | Reason: AGED-OUT  | Entry:   12.02 | Exit:   11.33 | Qty:  -88 | PnL: $   60.22\n", "--- END DEBUGGING EXITS ---\n", "2024-04-22  Longs=0 Shorts=3 Positions=4 Cash=$110,880 Equity=$107,571\n", "  ENTRY: SHORT GL    | Price:   68.03 | Qty:  -15\n", "  ENTRY: SHORT AGEN  | Price:    5.40 | Qty: -199\n", "2024-04-23: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-23 ---\n", "  EXIT: SHORT RENT  | Reason: AGED-OUT  | Entry:   21.99 | Exit:   11.32 | Qty:  -48 | PnL: $  511.89\n", "  EXIT: SHORT MINM  | Reason: AGED-OUT  | Entry:    5.21 | Exit:    3.51 | Qty: -204 | PnL: $  346.44\n", "  EXIT: SHORT PALI  | Reason: STOP-LOSS | Entry:    6.05 | Exit:    6.35 | Qty: -177 | PnL: $  -54.10\n", "  EXIT: SHORT GL    | Reason: STOP-LOSS | Entry:   68.03 | Exit:   71.43 | Qty:  -15 | PnL: $  -51.56\n", "  EXIT: SHORT AGEN  | Reason: STOP-LOSS | Entry:    5.40 | Exit:    5.67 | Qty: -199 | PnL: $  -54.29\n", "--- END DEBUGGING EXITS ---\n", "2024-04-23  Longs=0 Shorts=0 Positions=1 Cash=$108,388 Equity=$107,614\n", "2024-04-24: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-24 ---\n", "  EXIT: SHORT WISA  | Reason: STOP-LOSS | Entry:    5.99 | Exit:    6.29 | Qty: -179 | PnL: $  -54.17\n", "--- END DEBUGGING EXITS ---\n", "2024-04-24  Longs=0 Shorts=4 Positions=0 Cash=$107,261 Equity=$107,261\n", "  ENTRY: SHORT WISA  | Price:    5.26 | Qty: -203\n", "  ENTRY: SHORT SWVL  | Price:   11.90 | Qty:  -90\n", "  ENTRY: SHORT RIOT  | Price:   11.80 | Qty:  -90\n", "  ENTRY: SHORT PHUN  | Price:    6.23 | Qty: -172\n", "2024-04-25: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-25 ---\n", "  EXIT: SHORT WISA  | Reason: STOP-LOSS | Entry:    5.26 | Exit:    5.52 | Qty: -203 | PnL: $  -53.95\n", "--- END DEBUGGING EXITS ---\n", "2024-04-25  Longs=0 Shorts=6 Positions=3 Cash=$110,410 Equity=$107,228\n", "  ENTRY: SHORT GL    | Price:   77.30 | Qty:  -13\n", "  ENTRY: SHORT PALI  | Price:    6.10 | Qty: -175\n", "  ENTRY: SHORT VRM   | Price:   11.44 | Qty:  -93\n", "  ENTRY: SHORT RENT  | Price:   11.63 | Qty:  -92\n", "  ENTRY: SHORT WISA  | Price:    5.93 | Qty: -180\n", "2024-04-26: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-26 ---\n", "  EXIT: SHORT RIOT  | Reason: STOP-LOSS | Entry:   11.80 | Exit:   12.39 | Qty:  -90 | PnL: $  -53.66\n", "  EXIT: SHORT PHUN  | Reason: STOP-LOSS | Entry:    6.23 | Exit:    6.54 | Qty: -172 | PnL: $  -54.14\n", "  EXIT: SHORT PALI  | Reason: STOP-LOSS | Entry:    6.10 | Exit:    6.41 | Qty: -175 | PnL: $  -53.94\n", "  EXIT: SHORT VRM   | Reason: STOP-LOSS | Entry:   11.44 | Exit:   12.01 | Qty:  -93 | PnL: $  -53.75\n", "  EXIT: SHORT WISA  | Reason: STOP-LOSS | Entry:    5.93 | Exit:    6.23 | Qty: -180 | PnL: $  -53.93\n", "--- END DEBUGGING EXITS ---\n", "2024-04-26  Longs=0 Shorts=6 Positions=3 Cash=$110,079 Equity=$106,941\n", "  ENTRY: SHORT AGEN  | Price:    8.60 | Qty: -124\n", "  ENTRY: SHORT VRM   | Price:   11.58 | Qty:  -92\n", "  ENTRY: SHORT WISA  | Price:    6.25 | Qty: -171\n", "2024-04-29: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-29 ---\n", "  EXIT: SHORT AGEN  | Reason: STOP-LOSS | Entry:    8.60 | Exit:    9.03 | Qty: -124 | PnL: $  -53.88\n", "--- END DEBUGGING EXITS ---\n", "2024-04-29  Longs=0 Shorts=4 Positions=5 Cash=$112,158 Equity=$107,227\n", "  ENTRY: SHORT PALI  | Price:    5.94 | Qty: -180\n", "2024-04-30: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-04-30 ---\n", "  EXIT: SHORT SWVL  | Reason: STOP-LOSS | Entry:   11.90 | Exit:   12.50 | Qty:  -90 | PnL: $  -54.11\n", "  EXIT: SHORT PALI  | Reason: STOP-LOSS | Entry:    5.94 | Exit:    6.24 | Qty: -180 | PnL: $  -54.02\n", "--- END DEBUGGING EXITS ---\n", "2024-04-30  Longs=0 Shorts=5 Positions=4 Cash=$110,978 Equity=$107,118\n", "  ENTRY: SHORT AGEN  | Price:   12.76 | Qty:  -83\n", "  ENTRY: SHORT PALI  | Price:    5.94 | Qty: -180\n", "2024-05-01: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-05-01 ---\n", "  EXIT: SHORT RENT  | Reason: STOP-LOSS | Entry:   11.63 | Exit:   12.21 | Qty:  -92 | PnL: $  -54.06\n", "  EXIT: SHORT AGEN  | Reason: STOP-LOSS | Entry:   12.76 | Exit:   13.40 | Qty:  -83 | PnL: $  -53.51\n", "  EXIT: SHORT PALI  | Reason: STOP-LOSS | Entry:    5.94 | Exit:    6.24 | Qty: -180 | PnL: $  -54.02\n", "--- END DEBUGGING EXITS ---\n", "2024-05-01  Longs=0 Shorts=7 Positions=3 Cash=$109,745 Equity=$106,780\n", "  ENTRY: SHORT RENT  | Price:    9.94 | Qty: -107\n", "  ENTRY: SHORT PALI  | Price:    7.95 | Qty: -134\n", "  ENTRY: SHORT IRBT  | Price:    8.50 | Qty: -125\n", "  ENTRY: SHORT VCNX  | Price:    6.45 | Qty: -165\n", "  ENTRY: SHORT PHUN  | Price:    6.66 | Qty: -160\n", "2024-05-02: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-05-02 ---\n", "  EXIT: SHORT VRM   | Reason: STOP-LOSS | Entry:   11.58 | Exit:   12.16 | Qty:  -92 | PnL: $  -53.83\n", "  EXIT: SHORT RENT  | Reason: STOP-LOSS | Entry:    9.94 | Exit:   10.44 | Qty: -107 | PnL: $  -53.74\n", "  EXIT: SHORT IRBT  | Reason: STOP-LOSS | Entry:    8.50 | Exit:    8.93 | Qty: -125 | PnL: $  -53.68\n", "--- END DEBUGGING EXITS ---\n", "2024-05-02  Longs=0 Shorts=5 Positions=5 Cash=$111,711 Equity=$106,841\n", "  ENTRY: SHORT BKKT  | Price:    6.36 | Qty: -167\n", "  ENTRY: SHORT RENT  | Price:   12.63 | Qty:  -84\n", "  ENTRY: SHORT VRM   | Price:   12.35 | Qty:  -86\n", "  ENTRY: SHORT IRBT  | Price:    8.73 | Qty: -122\n", "2024-05-03: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-05-03 ---\n", "  EXIT: SHORT GL    | Reason: AGED-OUT  | Entry:   77.30 | Exit:   79.30 | Qty:  -13 | PnL: $  -26.52\n", "  EXIT: SHORT BKKT  | Reason: STOP-LOSS | Entry:    6.36 | Exit:    6.68 | Qty: -167 | PnL: $  -53.66\n", "  EXIT: SHORT RENT  | Reason: STOP-LOSS | Entry:   12.63 | Exit:   13.26 | Qty:  -84 | PnL: $  -53.60\n", "  EXIT: SHORT VRM   | Reason: STOP-LOSS | Entry:   12.35 | Exit:   12.97 | Qty:  -86 | PnL: $  -53.66\n", "  EXIT: SHORT IRBT  | Reason: STOP-LOSS | Entry:    8.73 | Exit:    9.17 | Qty: -122 | PnL: $  -53.81\n", "--- END DEBUGGING EXITS ---\n", "2024-05-03  Longs=0 Shorts=4 Positions=4 Cash=$110,463 Equity=$106,551\n", "  ENTRY: SHORT DTIL  | Price:   10.99 | Qty:  -96\n", "  ENTRY: SHORT BKKT  | Price:    6.43 | Qty: -165\n", "2024-05-06: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-05-06 ---\n", "  EXIT: SHORT WISA  | Reason: AGED-OUT  | Entry:    6.25 | Exit:    4.56 | Qty: -171 | PnL: $  288.60\n", "  EXIT: SHORT PALI  | Reason: STOP-LOSS | Entry:    7.95 | Exit:    8.35 | Qty: -134 | PnL: $  -53.82\n", "  EXIT: SHORT PHUN  | Reason: STOP-LOSS | Entry:    6.66 | Exit:    6.99 | Qty: -160 | PnL: $  -53.84\n", "  EXIT: SHORT BKKT  | Reason: STOP-LOSS | Entry:    6.43 | Exit:    6.75 | Qty: -165 | PnL: $  -53.60\n", "--- END DEBUGGING EXITS ---\n", "2024-05-06  Longs=0 Shorts=5 Positions=2 Cash=$108,445 Equity=$106,437\n", "  ENTRY: SHORT SGBX  | Price:    4.27 | Qty: -249\n", "  ENTRY: SHORT OCUL  | Price:    5.97 | Qty: -178\n", "  ENTRY: SHORT PHUN  | Price:    6.63 | Qty: -160\n", "  ENTRY: SHORT PALI  | Price:    7.41 | Qty: -143\n", "  ENTRY: SHORT RENT  | Price:   14.89 | Qty:  -71\n", "2024-05-07: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-05-07 ---\n", "  EXIT: SHORT SGBX  | Reason: STOP-LOSS | Entry:    4.27 | Exit:    4.48 | Qty: -249 | PnL: $  -53.72\n", "  EXIT: SHORT PHUN  | Reason: STOP-LOSS | Entry:    6.63 | Exit:    6.96 | Qty: -160 | PnL: $  -53.60\n", "  EXIT: SHORT PALI  | Reason: STOP-LOSS | Entry:    7.41 | Exit:    7.78 | Qty: -143 | PnL: $  -53.54\n", "  EXIT: SHORT RENT  | Reason: STOP-LOSS | Entry:   14.89 | Exit:   15.63 | Qty:  -71 | PnL: $  -53.41\n", "--- END DEBUGGING EXITS ---\n", "2024-05-07  Longs=0 Shorts=5 Positions=3 Cash=$109,291 Equity=$106,270\n", "  ENTRY: SHORT RENT  | Price:   14.49 | Qty:  -73\n", "  ENTRY: SHORT PHUN  | Price:    7.00 | Qty: -151\n", "  ENTRY: SHORT PALI  | Price:    8.35 | Qty: -127\n", "  ENTRY: SHORT SWVL  | Price:   11.30 | Qty:  -94\n", "2024-05-08: BULL market regime (VIX filter)\n", "\n", "--- Analyzing exits for 2024-05-08 ---\n", "--- END DEBUGGING EXITS ---\n"]}], "source": ["# ╭───────────────────────── RUN & STATS ───────────────────────╮\n", "print(\"\\n--- Running Original XGBoost Strategy ---\")\n", "eq_orig, trades_orig = backtest_long_short_vix_filter()\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Performance Comparison ---\n", "              Original Strategy (XGBoost)\n", "CAGR                             0.229366\n", "Sharpe Ratio                     3.973653\n", "Max Drawdown                    -0.016019\n"]}, {"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 1200x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Generated Trade Lists:\n", "\n", "Original Strategy (First 5 Trades):\n", "  ticker                 entry_day                  exit_day  entry_price  \\\n", "0    POL 2024-01-08 00:00:00-05:00 2024-01-09 00:00:00-05:00         7.00   \n", "1   SIDU 2024-01-08 00:00:00-05:00 2024-01-09 00:00:00-05:00        12.00   \n", "2   ENTA 2024-01-08 00:00:00-05:00 2024-01-09 00:00:00-05:00        11.74   \n", "3   CING 2024-01-08 00:00:00-05:00 2024-01-09 00:00:00-05:00         6.53   \n", "4   MULN 2024-01-08 00:00:00-05:00 2024-01-09 00:00:00-05:00        13.17   \n", "\n", "   exit_price  qty        pnl   side exit_reason  \n", "0      7.3500 -142 -50.221850  short   STOP-LOSS  \n", "1     12.6000  -83 -50.322900  short   STOP-LOSS  \n", "2     12.3270  -85 -50.418897  short   STOP-LOSS  \n", "3      6.8565 -153 -50.479022  short   STOP-LOSS  \n", "4     13.8285  -75 -49.906069  short   STOP-LOSS  \n"]}], "source": ["# Calculate stats for both\n", "def calculate_stats(eq_df, name):\n", "    if eq_df.empty:\n", "        return pd.Series([0, 0, 0], index=[\"CAGR\", \"Sharpe Ratio\", \"Max Drawdown\"], name=name)\n", "    eq_df[\"ret\"] = eq_df[\"equity\"].pct_change().fillna(0)\n", "    cagr = (eq_df[\"equity\"].iloc[-1] / eq_df[\"equity\"].iloc[0]) ** (252 / len(eq_df)) - 1\n", "    # Handle cases with zero standard deviation in returns\n", "    ret_std = eq_df[\"ret\"].std()\n", "    if ret_std == 0:\n", "        sharpe = np.inf if eq_df[\"ret\"].mean() > 0 else 0\n", "    else:\n", "        sharpe = np.sqrt(252) * eq_df[\"ret\"].mean() / ret_std\n", "    maxdd = (eq_df[\"equity\"] / eq_df[\"equity\"].cummax() - 1).min()\n", "    return pd.Series([cagr, sharpe, maxdd], index=[\"CAGR\", \"Sharpe Ratio\", \"Max Drawdown\"], name=name)\n", "\n", "stats_orig = calculate_stats(eq_orig, \"Original Strategy (XGBoost)\")\n", "comparison_df = pd.concat([stats_orig], axis=1)\n", "print(\"\\n--- Performance Comparison ---\")\n", "print(comparison_df.to_string(formatters={'CAGR': '{:,.2%}'.format, 'Max Drawdown': '{:,.2%}'.format, 'Sharpe Ratio': '{:,.2f}'.format}))\n", "\n", "# Plotting\n", "import matplotlib.pyplot as plt\n", "plt.style.use('seaborn-v0_8-darkgrid')\n", "fig, ax = plt.subplots(figsize=(12, 7))\n", "\n", "if not eq_orig.empty:\n", "    eq_orig[\"equity\"].plot(ax=ax, label=\"Original Strategy (XGBoost)\", legend=True)\n", "\n", "ax.set_title(f\"Strategy Comparison: {BACKTEST_START} to {BACKTEST_END}\")\n", "ax.set_ylabel(\"Equity\")\n", "ax.set_xlabel(\"Date\")\n", "ax.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\nGenerated Trade Lists:\")\n", "if not trades_orig.empty:\n", "    print(\"\\nOriginal Strategy (First 5 Trades):\")\n", "    print(trades_orig.head())\n", "else:\n", "    print(\"\\nNo trades were made by the Original Strategy.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}