"""
Utility functions for processing trade conditions from market data feeds.
This module helps determine which trades should be included in OHLCV calculations
based on their condition codes.
"""

from typing import Dict, Set, Tuple, Optional

# Define sets of condition codes for different eligibility categories
OHLC_ELIGIBLE_CONDITIONS = {0, 7, 18}  # Regular, Opening, Auto Execution
VOLUME_ELIGIBLE_CONDITIONS = {0, 7, 18, 124}  # Including Qualified Contingent Trade for volume only
INELIGIBLE_CONDITIONS = {45, 46, 59, 65, 115, 148}  # Various special conditions

# Condition codes that require special handling
OPENING_TRADE_CONDITION = 7  # OPEN_REPORT_IN_SEQ
CLOSING_TRADE_CONDITION = 98  # CLOSING
CANCELLED_TRADE_CONDITIONS = {40, 41, 42, 43, 44}  # Various cancellation codes
ODD_LOT_CONDITION = 115  # ODD_LOT
EXTENDED_HOURS_CONDITION = 148  # EXTENDED_HOURS_TRADE

def is_trade_eligible_for_ohlc(condition_code: int) -> bool:
    """
    Determine if a trade should be included in OHLC calculations based on its condition code.
    
    Args:
        condition_code: The condition code from the trade data
        
    Returns:
        True if the trade should be included in OHLC calculations, False otherwise
    """
    # Explicitly cancelled trades are never eligible
    if condition_code in CANCELLED_TRADE_CONDITIONS:
        return False
    
    # Trades with explicitly eligible conditions
    if condition_code in OHLC_ELIGIBLE_CONDITIONS:
        return True
    
    # Trades with explicitly ineligible conditions
    if condition_code in INELIGIBLE_CONDITIONS:
        return False
    
    # Default to eligible for any other condition codes not explicitly categorized
    # This is a conservative approach - you may want to adjust based on your specific needs
    return True

def is_trade_eligible_for_volume(condition_code: int) -> bool:
    """
    Determine if a trade should be included in volume calculations based on its condition code.
    
    Args:
        condition_code: The condition code from the trade data
        
    Returns:
        True if the trade should be included in volume calculations, False otherwise
    """
    # Explicitly cancelled trades are never eligible for volume
    if condition_code in CANCELLED_TRADE_CONDITIONS:
        return False
    
    # Trades with explicitly volume-eligible conditions
    if condition_code in VOLUME_ELIGIBLE_CONDITIONS:
        return True
    
    # Some trades contribute to volume but not OHLC
    if condition_code == 124:  # QUALIFIED_CONTINGENT_TRADE
        return True
    
    # Trades with explicitly ineligible conditions
    if condition_code in {46, 59}:  # FAST_MARKET, VWAP
        return False
    
    # Default to eligible for any other condition codes not explicitly categorized
    return True

def is_opening_trade(condition_code: int) -> bool:
    """
    Determine if a trade is an opening trade.
    
    Args:
        condition_code: The condition code from the trade data
        
    Returns:
        True if the trade is an opening trade, False otherwise
    """
    return condition_code == OPENING_TRADE_CONDITION

def is_closing_trade(condition_code: int) -> bool:
    """
    Determine if a trade is a closing trade.
    
    Args:
        condition_code: The condition code from the trade data
        
    Returns:
        True if the trade is a closing trade, False otherwise
    """
    return condition_code == CLOSING_TRADE_CONDITION

def is_odd_lot(condition_code: int) -> bool:
    """
    Determine if a trade is an odd lot trade.
    
    Args:
        condition_code: The condition code from the trade data
        
    Returns:
        True if the trade is an odd lot trade, False otherwise
    """
    return condition_code == ODD_LOT_CONDITION

def is_extended_hours_trade(condition_code: int) -> bool:
    """
    Determine if a trade occurred during extended hours.
    
    Args:
        condition_code: The condition code from the trade data
        
    Returns:
        True if the trade occurred during extended hours, False otherwise
    """
    return condition_code == EXTENDED_HOURS_CONDITION

def get_condition_name(condition_code: int) -> str:
    """
    Get the human-readable name for a condition code.
    
    Args:
        condition_code: The condition code from the trade data
        
    Returns:
        A string representing the name of the condition
    """
    condition_names = {
        0: "REGULAR",
        2: "OUT_OF_SEQ",
        3: "AVG_PRC",
        4: "AVG_PRC_NASDAQ",
        7: "OPEN_REPORT_IN_SEQ",
        18: "AUTO_EXECUTION",
        40: "CANCELLED",
        41: "CANCELLED_LAST",
        42: "CANCELLED_OPEN",
        43: "CANCELLED_ONLY",
        44: "CANCELLED_STOPPED",
        45: "MATCH_CROSS",
        46: "FAST_MARKET",
        59: "VWAP",
        65: "OUT_OF_SEQ_PRE_MKT",
        98: "CLOSING",
        115: "ODD_LOT",
        124: "QUALIFIED_CONTINGENT_TRADE",
        148: "EXTENDED_HOURS_TRADE"
    }
    
    return condition_names.get(condition_code, f"UNKNOWN_CONDITION_{condition_code}")

def process_trade_for_ohlcv(
    trade: Dict, 
    current_ohlcv: Optional[Dict] = None
) -> Dict:
    """
    Process a trade and update OHLCV data based on trade condition.
    
    Args:
        trade: A dictionary containing trade data with at least 'price', 'size', and 'condition' fields
        current_ohlcv: Optional current OHLCV data to update. If None, a new dict will be created.
        
    Returns:
        Updated OHLCV dictionary
    """
    if current_ohlcv is None:
        current_ohlcv = {
            'open': None,
            'high': float('-inf'),
            'low': float('inf'),
            'close': None,
            'volume': 0
        }
    
    price = trade['price']
    size = trade['size']
    condition = trade['condition']
    
    # Check if trade is eligible for OHLC
    if is_trade_eligible_for_ohlc(condition):
        # Update open price if not set
        if current_ohlcv['open'] is None:
            current_ohlcv['open'] = price
        
        # Update high and low
        current_ohlcv['high'] = max(current_ohlcv['high'], price)
        current_ohlcv['low'] = min(current_ohlcv['low'], price)
        
        # Update close price
        current_ohlcv['close'] = price
    
    # Check if trade is eligible for volume
    if is_trade_eligible_for_volume(condition):
        current_ohlcv['volume'] += size
    
    return current_ohlcv 