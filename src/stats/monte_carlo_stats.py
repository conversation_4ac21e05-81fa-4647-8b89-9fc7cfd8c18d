import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import base64
from io import BytesIO
from datetime import datetime, timedelta
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MonteCarloStats:
    """
    A class to perform Monte Carlo stats on trade data and
    generate probabilistic equity curves.
    """
    
    def __init__(self, trades_df=None):
        """
        Initialize the Monte Carlo stats object.
        
        Args:
            trades_df (pd.DataFrame, optional): DataFrame with trade information.
                Should contain at minimum columns for trade P&L and dates.
        """
        self.trades_df = trades_df
        self.simulation_results = None
        self.stats = {}
        
    def set_trades(self, trades_df):
        """
        Set the trades dataframe.
        
        Args:
            trades_df (pd.DataFrame): DataFrame with trade information.
        """
        self.trades_df = trades_df
        
    def run_simulation(self, num_simulations=1000, days=365, starting_equity=10000, 
                       pnl_column='pnl', date_column='close_date', 
                       random_seed=None):
        """
        Run Monte Carlo simulations to generate probabilistic equity curves.
        
        Args:
            num_simulations (int): Number of simulations to run.
            days (int): Number of days to simulate.
            starting_equity (float): Starting equity amount.
            pnl_column (str): Name of the column containing P&L values.
            date_column (str): Name of the column containing trade dates.
            random_seed (int, optional): Seed for the random number generator.
            
        Returns:
            pd.DataFrame: DataFrame with simulation results.
        """
        if self.trades_df is None or len(self.trades_df) == 0:
            raise ValueError("Trades data not set or empty. Use set_trades() first.")
        
        # Set random seed for reproducibility if provided
        if random_seed is not None:
            np.random.seed(random_seed)
        
        # Make a copy to avoid modifying the original
        df = self.trades_df.copy()
        if isinstance(df[date_column].iloc[0], datetime):
            # The column already contains datetime objects, just remove timezone info
            df[date_column] = df[date_column].apply(lambda dt: dt.replace(tzinfo=None))
        else:
            # Not datetime objects, raise an error
            raise ValueError(f"Column '{date_column}' does not contain datetime objects")
        
        # Sort by date
        df = df.sort_values(by=date_column)
        
        # Compute average trades per day
        start_date = df[date_column].min()
        end_date = df[date_column].max()
        date_range = (end_date - start_date).days
        if date_range <= 0:
            date_range = 1  # Avoid division by zero
        
        trades_per_day = len(df) / date_range
        
        # Create a distribution of returns from historical trades
        trade_returns = df[pnl_column].values
        
        # Generate simulation results
        logger.info(f"Running {num_simulations} simulations over {days} days...")
        
        # Create a date range for the simulation
        sim_dates = [datetime.now() + timedelta(days=i) for i in range(days)]
        
        # Initialize results dataframe
        results = pd.DataFrame(index=sim_dates)
        
        # Collect all simulation results in a dictionary
        sim_results = {}
        
        # Run simulations
        for sim in range(num_simulations):
            equity_curve = [starting_equity]
            
            for day in range(1, days):
                # Estimate number of trades for this day based on historical average
                num_trades_today = np.random.poisson(trades_per_day)
                
                # If we have trades today, sample from historical returns
                if num_trades_today > 0:
                    day_returns = np.random.choice(trade_returns, size=num_trades_today)
                    day_pnl = sum(day_returns)
                else:
                    day_pnl = 0
                    
                # Update equity
                new_equity = equity_curve[-1] + day_pnl
                equity_curve.append(new_equity)
            
            # Store this simulation's results in the dictionary
            sim_results[f'sim_{sim}'] = equity_curve
        
        # Create DataFrame from the collected results all at once
        results = pd.DataFrame(sim_results, index=sim_dates)
        
        # Calculate statistics across all simulations
        results['mean'] = results.mean(axis=1)
        results['std'] = results.std(axis=1)
        results['upper_2std'] = results['mean'] + 2 * results['std']
        results['lower_2std'] = results['mean'] - 2 * results['std']
        # Add 1 standard deviation bands
        results['upper_1std'] = results['mean'] + results['std']
        results['lower_1std'] = results['mean'] - results['std']
        
        self.simulation_results = results
        logger.info("Monte Carlo simulation completed successfully.")
        
        return results
    
    def generate_report(self, output_path=None, title="Monte Carlo Simulation Report"):
        """
        Generate an HTML report with equity curve charts.
        
        Args:
            output_path (str, optional): Path to save the report. 
                If None, saves to 'monte_carlo_report.html' in current directory.
            title (str, optional): Title for the report.
                
        Returns:
            str: Path to the generated report
        """
        if self.simulation_results is None:
            raise ValueError("Simulation not run. Call run_simulation() first.")
        
        if output_path is None:
            output_path = "monte_carlo_report.html"
        
        # Ensure directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        try:
            # Generate charts
            thirty_day_chart = self._generate_chart(30, "30-Day Equity Curve")
            one_year_chart = self._generate_chart(365, "1-Year Equity Curve")
            
            # Create HTML report
            html_content = f"""
            <html>
            <head>
                <title>{title}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #333366; }}
                    h2 {{ color: #333366; margin-top: 30px; }}
                    .chart-container {{ margin: 20px 0; }}
                </style>
            </head>
            <body>
                <h1>{title}</h1>
                
                <h2>30-Day Equity Curve Projection</h2>
                <div class="chart-container">
                    <img src="data:image/png;base64,{thirty_day_chart}" width="100%" />
                </div>
                
                <h2>1-Year Equity Curve Projection</h2>
                <div class="chart-container">
                    <img src="data:image/png;base64,{one_year_chart}" width="100%" />
                </div>
                
                <h2>Simulation Summary</h2>
                <p>
                    <strong>Starting Equity:</strong> {self.simulation_results.iloc[0]['mean']:.2f}<br/>
                    <strong>Final Expected Equity (Mean):</strong> {self.simulation_results.iloc[-1]['mean']:.2f}<br/>
                    <strong>Upper 2σ Equity:</strong> {self.simulation_results.iloc[-1]['upper_2std']:.2f}<br/>
                    <strong>Lower 2σ Equity:</strong> {self.simulation_results.iloc[-1]['lower_2std']:.2f}<br/>
                    <strong>Standard Deviation at End:</strong> {self.simulation_results.iloc[-1]['std']:.2f}<br/>
                </p>
            </body>
            </html>
            """
            
            # Write to file
            with open(output_path, 'w') as f:
                f.write(html_content)
                
            logger.info(f"Monte Carlo simulation report generated successfully at {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Failed to generate Monte Carlo simulation report: {str(e)}", exc_info=True)
            return None
    
    def _generate_chart(self, days, title):
        """
        Generate a chart of the equity curves.
        
        Args:
            days (int): Number of days to include in the chart.
            title (str): Chart title.
            
        Returns:
            str: Base64-encoded PNG image of the chart.
        """
        # Get subset of data for the specified days
        data = self.simulation_results.iloc[:days]
        
        plt.figure(figsize=(12, 8))
        
        # Plot 2 sigma confidence bands
        plt.fill_between(
            range(len(data)), 
            data['lower_2std'], 
            data['upper_2std'], 
            alpha=0.2, 
            color='blue', 
            label='±2σ Range'
        )
        
        # Plot 1 sigma confidence bands
        plt.fill_between(
            range(len(data)), 
            data['lower_1std'], 
            data['upper_1std'], 
            alpha=0.3, 
            color='lightblue', 
            label='±1σ Range'
        )
        
        # Plot expected curve
        plt.plot(
            range(len(data)), 
            data['mean'], 
            'b-', 
            linewidth=2, 
            label='Expected Equity'
        )
        
        # Plot upper and lower bounds
        plt.plot(
            range(len(data)), 
            data['upper_2std'], 
            'g--', 
            linewidth=1.5, 
            label='Upper 2σ Bound'
        )
        plt.plot(
            range(len(data)), 
            data['lower_2std'], 
            'r--', 
            linewidth=1.5, 
            label='Lower 2σ Bound'
        )
        
        # Plot 1 sigma bounds
        plt.plot(
            range(len(data)), 
            data['upper_1std'], 
            'g:', 
            linewidth=1.5, 
            label='Upper 1σ Bound'
        )
        plt.plot(
            range(len(data)), 
            data['lower_1std'], 
            'r:', 
            linewidth=1.5, 
            label='Lower 1σ Bound'
        )
        
        # Add labels for final values
        last_idx = len(data) - 1
        
        # Label the final values
        plt.annotate(
            f'Mean: ${data["mean"].iloc[-1]:,.2f}',
            xy=(last_idx, data["mean"].iloc[-1]),
            xytext=(last_idx - 10, data["mean"].iloc[-1]),
            fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="blue", alpha=0.8)
        )
        
        plt.annotate(
            f'+2σ: ${data["upper_2std"].iloc[-1]:,.2f}',
            xy=(last_idx, data["upper_2std"].iloc[-1]),
            xytext=(last_idx - 10, data["upper_2std"].iloc[-1]),
            fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="green", alpha=0.8)
        )
        
        plt.annotate(
            f'-2σ: ${data["lower_2std"].iloc[-1]:,.2f}',
            xy=(last_idx, data["lower_2std"].iloc[-1]),
            xytext=(last_idx - 10, data["lower_2std"].iloc[-1]),
            fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="red", alpha=0.8)
        )
        
        plt.annotate(
            f'+1σ: ${data["upper_1std"].iloc[-1]:,.2f}',
            xy=(last_idx, data["upper_1std"].iloc[-1]),
            xytext=(last_idx - 10, data["upper_1std"].iloc[-1]),
            fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="green", alpha=0.6)
        )
        
        plt.annotate(
            f'-1σ: ${data["lower_1std"].iloc[-1]:,.2f}',
            xy=(last_idx, data["lower_1std"].iloc[-1]),
            xytext=(last_idx - 10, data["lower_1std"].iloc[-1]),
            fontsize=10,
            bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="red", alpha=0.6)
        )
        
        # Add labels and title
        plt.xlabel('Days')
        plt.ylabel('Equity')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Save chart to BytesIO object
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=100)
        buffer.seek(0)
        
        # Encode to base64
        chart_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        plt.close()
        
        return chart_base64