import pandas as pd
import logging
import os
import json
from datetime import datetime
from brokers.ibroker_stats import IBrokerStats
from stats.portfolio_stats import PortfolioStats
from stats.trade_stats import TradeStats
from stats.monte_carlo_stats import MonteCarloStats

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class StatsBuilder:
    """
    A class to generate different types of statistics from a broker instance.
    Supports portfolio stats, trade stats, and Monte Carlo simulations.
    """
    
    def __init__(self, broker: IBrokerStats, strategy_name=None, strategy_version=None):
        """
        Initialize the StatsBuilder with a broker instance.
        
        Args:
            broker: An IBrokerStats instance containing trade and position data
            strategy_name: Name of the strategy being tested
            strategy_version: Version or parameter set identifier for the strategy
        """
        self.broker = broker
        self.strategy_name = strategy_name
        self.strategy_version = strategy_version
        # Cache for historical data to avoid redundant API calls
        self.historical_data_cache = {}
        
    async def get_daily_portfolio_values(self, start_date, end_date=None):
        """
        Generate daily portfolio values as a pandas Series for use with PortfolioStats.
        Includes the value of open positions based on market data for each day.
        
        Args:
            start_date (datetime, optional): Start date for the portfolio values.
                If None, uses the earliest trade date.
            end_date (datetime, optional): End date for the portfolio values.
                If None, uses the current date from the clock.
                
        Returns:
            pd.Series: Daily portfolio values indexed by date
        """
        # Add debug logging
        logger.info(f"Generating daily portfolio values from {start_date} to {end_date or 'current date'}")
        logger.info(f"Initial capital: ${self.broker.initial_capital:,.2f}")
        
        # Determine date range
        if not self.broker.closed_trades and not any(pos_list for pos_list in self.broker.positions.values()):
            logger.warning("No closed trades or open positions found, returning initial capital only")
            if end_date is None:
                end_date = self.broker.clock.now().date()
            date_range = pd.date_range(start=start_date, end=end_date)
            return pd.Series(self.broker.initial_capital, index=date_range)
        
        # Get earliest and latest dates from trades if not specified
        trade_dates = []
        for trade in self.broker.closed_trades:
            if hasattr(trade, 'entry_time') and trade['entry_time']:
                trade_dates.append(trade['entry_time'].date())
            trade_dates.append(trade['exit_time'].date())
        
        # Also consider entry times from current open positions
        for symbol, positions in self.broker.positions.items():
            for pos in positions:
                if pos['quantity'] != 0 and 'entry_time' in pos:
                    trade_dates.append(pos['entry_time'].date())
        
        if end_date is None:
            end_date = max(self.broker.clock.now().date(), max(trade_dates) if trade_dates else start_date)
        
        # Create date range for the portfolio values
        date_range = pd.date_range(start=start_date, end=end_date)
        logger.info(f"Calculating portfolio values for {len(date_range)} days")
        
        # Initialize portfolio values with initial capital
        portfolio_values = {}
        
        # Fetch historical data for all symbols upfront
        symbols = set([trade['symbol'] for trade in self.broker.closed_trades])
        symbols.update(self.broker.positions.keys())
        logger.info(f"Fetching historical data for {len(symbols)} symbols: {', '.join(symbols)}")
        
        # Create a dictionary to store historical data for each symbol
        historical_data = {}
        
        # Fetch historical data for all symbols
        for symbol in symbols:
            try:
                # Check if the data is already in the cache
                if symbol in self.historical_data_cache and self.historical_data_cache[symbol] is not None:
                    logger.info(f"Using cached historical data for {symbol}")
                    historical_data[symbol] = self.historical_data_cache[symbol]
                else:
                    # Use the async method from IMarketData to get historical data
                    df = await self.broker.market_data.gather_historical_data_async(
                        symbol, 
                        start_dt=datetime.combine(start_date, datetime.min.time()),
                        end_dt=datetime.combine(end_date, datetime.max.time())
                    )
                    historical_data[symbol] = df
                    # Store in the instance cache
                    self.historical_data_cache[symbol] = df
                    logger.info(f"Got {len(df) if df is not None else 0} data points for {symbol}")
            except Exception as e:
                logger.error(f"Failed to fetch historical data for {symbol}: {e}")
                historical_data[symbol] = None
                self.historical_data_cache[symbol] = None
        
        # Create a timeline of all position changes
        position_timeline = []
        
        # Add position openings from current open positions
        for symbol, positions in self.broker.positions.items():
            for pos in positions:
                if pos['quantity'] != 0:
                    position_timeline.append({
                        'date': pos['entry_time'].date(),
                        'type': 'open',
                        'symbol': symbol,
                        'quantity': pos['quantity'],
                        'price': pos['avg_price'],
                        'trade_id': pos['trade_id']
                    })
        
        # Add position openings and closings from closed trades
        for trade in self.broker.closed_trades:
            # For closed trades, we need to reconstruct the opening event
            if 'entry_time' in trade and trade['entry_time']:
                position_timeline.append({
                    'date': trade['entry_time'].date(),
                    'type': 'open',
                    'symbol': trade['symbol'],
                    'quantity': trade['quantity'] if trade['position_type'] == 'long' else -trade['quantity'],
                    'price': trade['entry_price'],
                    'trade_id': trade['trade_id'] if 'trade_id' in trade else f"trade_{len(position_timeline)}"
                })
            
            # Add the closing event
            position_timeline.append({
                'date': trade['exit_time'].date(),
                'type': 'close',
                'symbol': trade['symbol'],
                'quantity': trade['quantity'] if trade['position_type'] == 'long' else -trade['quantity'],
                'price': trade['exit_price'],
                'trade_id': trade['trade_id'] if 'trade_id' in trade else f"trade_{len(position_timeline)}"
            })
        
        # Sort the timeline by date
        position_timeline.sort(key=lambda x: x['date'])
        logger.info(f"Created timeline with {len(position_timeline)} position events")
        
        # Track active positions for each day
        active_positions = {}  # trade_id -> position details
        
        # Calculate daily portfolio values
        cash = self.broker.initial_capital
        logger.info(f"Starting cash: ${cash:,.2f}")
        
        for date in date_range:
            date_only = date.date()
            
            # Process position changes for this date
            for event in position_timeline:
                if event['date'] == date_only:
                    if event['type'] == 'open':
                        # Opening a position - deduct cash
                        trade_id = event['trade_id']
                        quantity = event['quantity']
                        price = event['price']
                        
                        # Deduct cash for buys (positive quantity), add for shorts (negative quantity)
                        cash_change = -quantity * price
                        cash += cash_change
                        
                        # Add to active positions
                        active_positions[trade_id] = {
                            'symbol': event['symbol'],
                            'quantity': quantity,
                            'price': price
                        }
                        
                        logger.debug(f"{date_only}: OPEN {event['symbol']} {quantity} @ ${price:,.2f}, cash change: ${cash_change:,.2f}, new cash: ${cash:,.2f}")
                    
                    elif event['type'] == 'close':
                        # Closing a position - add cash
                        trade_id = event['trade_id']
                        quantity = event['quantity']
                        price = event['price']
                        
                        # Add cash for sells (positive quantity), deduct for covering shorts (negative quantity)
                        cash_change = quantity * price  # This might be the issue - should be abs(quantity) * price
                        cash += cash_change
                        
                        logger.debug(f"{date_only}: CLOSE {event['symbol']} {quantity} @ ${price:,.2f}, cash change: ${cash_change:,.2f}, new cash: ${cash:,.2f}")
                        
                        # Remove from active positions
                        if trade_id in active_positions:
                            del active_positions[trade_id]
            
            # Calculate market value of all active positions
            market_value = 0.0
            for trade_id, position in active_positions.items():
                symbol = position['symbol']
                quantity = position['quantity']
                
                # Skip if quantity is zero
                if quantity == 0:
                    continue
                
                # Get market price for this symbol on this date
                try:
                    if historical_data.get(symbol) is not None:
                        # Find the closest data point to this date
                        date_str = date.strftime('%Y-%m-%d')
                        df = historical_data[symbol]
                        
                        # Filter for the specific date
                        day_data = df[df.index.strftime('%Y-%m-%d') == date_str]
                        
                        if not day_data.empty:
                            # Use closing price if available
                            if 'close' in day_data.columns:
                                market_price = day_data['close'].iloc[-1]
                            else:
                                # Use the last available price
                                market_price = day_data.iloc[-1, 0]
                            
                            # Add to market value (positive for long, negative for short)
                            position_value = quantity * market_price
                            market_value += position_value
                            logger.debug(f"{date_only}: {symbol} position value: ${position_value:,.2f} ({quantity} @ ${market_price:,.2f})")
                    else:
                        logger.warning(f"No historical data available for {symbol}")
                except Exception as e:
                    logger.warning(f"Could not get price for {symbol} on {date}: {e}")
            
            # Total portfolio value is cash + market value
            portfolio_value = cash + market_value
            portfolio_values[date] = portfolio_value
                         
        # Convert to pandas Series
        portfolio_series = pd.Series(portfolio_values)
        
        # Ensure the series has a DatetimeIndex with daily frequency
        if not portfolio_series.empty:
            portfolio_series.index = pd.DatetimeIndex(portfolio_series.index)
            portfolio_series = portfolio_series.asfreq('D')
            # Fill any missing days with the previous value
            portfolio_series = portfolio_series.ffill()
                
        return portfolio_series
    
    async def build_portfolio_stats(self, start_date, end_date=None, benchmark_ticker="SPY"):
        """
        Build a PortfolioStats object with the calculated daily portfolio values.
        
        Args:
            start_date: Start date for the portfolio values
            end_date: End date for the portfolio values
            benchmark_ticker: Ticker symbol for benchmark comparison
            
        Returns:
            PortfolioStats: A configured PortfolioStats object
        """
        portfolio_values = await self.get_daily_portfolio_values(start_date, end_date)
        
        portfolio_stats = PortfolioStats(portfolio_values, benchmark_ticker)
        portfolio_stats.calculate_returns()
        
        # HACK: If the portfolio only has one day of values, create an artificial three-day returns series.
        if len(portfolio_stats.portfolio_values) == 1:
            single_date = portfolio_stats.portfolio_values.index[0]
            initial_cash = self.broker.initial_capital
            final_value = portfolio_stats.portfolio_values.iloc[0]
            computed_return = final_value / initial_cash - 1

            # Use the previous business day for the artificial date.
            artificial_date = (single_date - pd.offsets.BDay(1)).normalize()
            next_date = (single_date + pd.offsets.BDay(1)).normalize()
            # Build a two-day returns series.
            adjusted_returns = pd.Series(
                [0.0, computed_return, 0.0],
                index=pd.to_datetime([artificial_date, single_date, next_date])
            ).asfreq('B')
            
            portfolio_stats.returns = adjusted_returns

        return portfolio_stats
    
    async def build_trade_stats(self):
        """
        Build a TradeStats object from the broker's closed trades.
        
        Returns:
            TradeStats: A configured TradeStats object
        """
        trades_df = await self.broker.get_trades_dataframe()
        
        trade_stats = TradeStats(trades_df)
        if not trades_df.empty:
            trade_stats.calculate_stats()
        
        return trade_stats
    
    async def build_monte_carlo_stats(self, num_simulations=1000, days=365, starting_equity=None):
        """
        Build a MonteCarloStats object based on the broker's trade history.
        
        Args:
            num_simulations: Number of simulations to run
            days: Number of days to project forward
            starting_equity: Starting equity amount, defaults to current portfolio value
            
        Returns:
            MonteCarloStats: A configured MonteCarloStats object
        """
        trades_df = await self.broker.get_trades_dataframe()
        
        # Default starting equity to current portfolio value if not specified
        if starting_equity is None:
            current_portfolio = await self.get_daily_portfolio_values(
                start_date=(self.broker.clock.now() - pd.Timedelta(days=7)).date(),
                end_date=self.broker.clock.now().date()
            )
            starting_equity = current_portfolio.iloc[-1] if not current_portfolio.empty else self.broker.cash
        
        monte_carlo = MonteCarloStats(trades_df)
        if not trades_df.empty:
            monte_carlo.run_simulation(
                num_simulations=num_simulations,
                days=days,
                starting_equity=starting_equity
            )
        
        return monte_carlo 
    
    async def store_statistics(self, start_date, end_date=None, is_backtest=True, benchmark_ticker="SPY"):
        """
        Store core statistics data for later analysis and report generation.
        First generates portfolio statistics to ensure historical data is cached.
        
        Args:
            start_date: Start date for the statistics
            end_date: End date for the statistics
            is_backtest: Whether this is from a backtest (True) or live trading (False)
            benchmark_ticker: Ticker symbol for benchmark comparison
            
        Returns:
            dict: Paths to the stored data files
        """
        if self.strategy_name is None:
            raise ValueError("strategy_name must be provided to store statistics")
        
        # Convert dates to string format for directory naming and ensure they're serializable
        if hasattr(start_date, 'strftime'):
            start_date_str = start_date.strftime('%Y%m%d')
        else:
            start_date_str = str(start_date)
        
        if end_date and hasattr(end_date, 'strftime'):
            end_date_str = end_date.strftime('%Y%m%d')
        else:
            end_date_str = datetime.now().strftime('%Y%m%d') if end_date is None else str(end_date)
        
        # Determine the environment folder (backtest or live)
        env_folder = "backtest" if is_backtest else "live"
        
        # For single-day range, use just the start_date_str
        date_folder = start_date_str if start_date_str == end_date_str else f"{start_date_str}_to_{end_date_str}"
        
        # Create the directory structure
        base_dir = os.path.join(
            "stats", 
            self.strategy_name,
            self.strategy_version or "default", 
            date_folder,
            env_folder
        )
        
        # Create single directory without subfolders
        os.makedirs(base_dir, exist_ok=True)
        
        # FIRST: Generate portfolio stats to ensure historical data is cached
        logger.info("Generating portfolio statistics to cache historical data...")
        portfolio_stats = await self.build_portfolio_stats(
            start_date=start_date,
            end_date=end_date,
            benchmark_ticker=benchmark_ticker
        )
        
        # Get the portfolio values series from the portfolio stats
        portfolio_values = portfolio_stats.portfolio_values
        
        # Now get trades data
        trades_df = await self.broker.get_trades_dataframe()
        trades_path = os.path.join(base_dir, "trades.csv")
        trades_df.to_csv(trades_path)
        logger.info(f"Trades data stored at: {trades_path}")
        
        # Store portfolio values
        portfolio_path = os.path.join(base_dir, "daily_portfolio_values.csv")
        portfolio_values.to_csv(portfolio_path)
        logger.info(f"Portfolio values stored at: {portfolio_path}")
        
        # Store historical market data for all symbols traded - using the data cached during portfolio stats calculation
        symbols = set([trade['symbol'] for trade in self.broker.closed_trades])
        symbols.update(self.broker.positions.keys())
        
        market_data_info = {}

        # Extract historical data from the calculation that already occurred in portfolio_stats
        try:
            # Try to access historical data through the calculation that should have already happened
            for symbol in symbols:
                if symbol in self.historical_data_cache and self.historical_data_cache[symbol] is not None:
                    df = self.historical_data_cache[symbol]
                    if not df.empty:
                        # Store the historical data
                        symbol_path = os.path.join(base_dir, f"{symbol}_historical_data.csv")
                        df.to_csv(symbol_path)
                        market_data_info[symbol] = {
                            "path": symbol_path,
                            "rows": len(df)
                        }
                        logger.info(f"Historical data for {symbol} stored at: {symbol_path}")
                else:
                    logger.warning(f"No cached historical data found for {symbol} even after portfolio calculation")
        except Exception as e:
            logger.error(f"Error storing historical data: {e}")
        
        # Store metadata about the backtest/live run
        final_value = portfolio_values.iloc[-1] if not portfolio_values.empty else self.broker.cash
        
        # Create metadata with JSON-serializable values
        metadata = {
            "strategy_name": self.strategy_name,
            "strategy_version": self.strategy_version,
            "start_date": start_date_str,
            "end_date": end_date_str,
            "initial_capital": float(self.broker.initial_capital),
            "final_capital": float(final_value),
            "benchmark_ticker": benchmark_ticker,
            "total_trades": len(trades_df),
            "symbols_traded": list(symbols),
            "created_at": datetime.now().isoformat()
        }
        
        # Custom JSON encoder to handle non-serializable objects
        class CustomJSONEncoder(json.JSONEncoder):
            def default(self, obj):
                if hasattr(obj, 'isoformat'):
                    return obj.isoformat()
                elif hasattr(obj, '__str__'):
                    return str(obj)
                return json.JSONEncoder.default(self, obj)
        
        metadata_path = os.path.join(base_dir, "metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=4, cls=CustomJSONEncoder)
        
        logger.info(f"Metadata stored at: {metadata_path}")
        
        # Generate reports if we have enough data
        report_paths = {}
        
        if not trades_df.empty:
            # Generate trade stats report
            trade_stats = await self.build_trade_stats()
            trade_stats_path = os.path.join(base_dir, "trade_stats.html")
            trade_stats.generate_report(
                output_path=trade_stats_path,
                title=f"{self.strategy_name} Trade Statistics ({start_date_str} to {end_date_str})"
            )
            report_paths["trade_stats"] = trade_stats_path
            logger.info(f"Trade statistics report generated at: {trade_stats_path}")
        
        # Generate portfolio stats report - we already have portfolio_stats
        portfolio_stats_path = os.path.join(base_dir, "portfolio_stats.html")
        portfolio_stats.generate_html_report(
            output_path=portfolio_stats_path,
            title=f"{self.strategy_name} {'Backtest' if is_backtest else 'Live Trading'} ({start_date_str} to {end_date_str})"
        )
        report_paths["portfolio_stats"] = portfolio_stats_path
        logger.info(f"Portfolio statistics report generated at: {portfolio_stats_path}")
        
        return {
            "base_dir": base_dir,
            "trades_path": trades_path,
            "portfolio_path": portfolio_path,
            "market_data": market_data_info,
            "metadata_path": metadata_path,
            "reports": report_paths,
            "portfolio_stats": portfolio_stats,
            "trade_stats": trade_stats if not trades_df.empty else None
        } 