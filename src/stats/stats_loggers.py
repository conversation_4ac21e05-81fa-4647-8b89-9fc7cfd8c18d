import os
import logging
import async<PERSON>
from datetime import datetime, timed<PERSON>ta
import json
from contextlib import contextmanager
import sys
import abc

from tools.clock import Clock, is_regular_hours, is_trading_time
from stats.stats_builder import StatsBuilder
from brokers.local_broker import LocalBroker
from brokers.ibroker_stats import IBrokerStats  # Import the new interface

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class BacktestStatsLogger:
    """
    Handles logging statistics for backtest runs.
    Uses StatsBuilder to generate and store statistics.
    """
    
    def __init__(self, strategy_name, strategy_params, broker, start_date, end_date):
        """
        Initialize the backtest stats logger.
        
        Args:
            strategy_name: Name of the strategy
            strategy_params: Dictionary of strategy parameters
            broker: The broker instance that implements IBrokerStats
            start_date: Start date of the backtest
            end_date: End date of the backtest
        """
        # Validate that broker implements IBrokerStats
        if not isinstance(broker, IBrokerStats):
            raise TypeError("Broker must implement IBrokerStats interface")
            
        self.strategy_name = strategy_name
        self.strategy_params = strategy_params or {}
        self.broker = broker
        self.start_date = start_date
        self.end_date = end_date
        
        # Derive strategy version from params if available
        self.strategy_version = None
        if self.strategy_params:
            # Create a version identifier from the parameters
            self.strategy_version = "_".join([f"{k}_{v}" for k, v in self.strategy_params.items()])
        
        # Create the StatsBuilder for generating and storing statistics
        self.stats_builder = StatsBuilder(
            broker=self.broker,
            strategy_name=self.strategy_name,
            strategy_version=self.strategy_version
        )
    
    @contextmanager
    def redirect_output_to_file(self):
        """
        Context manager to redirect stdout, stderr, and logging to a log file.
        
        Yields:
            str: Path to the log file
        """
        # Save original stdout and stderr
        original_stdout = sys.stdout
        original_stderr = sys.stderr
        
        # Save original logging configuration
        root_logger = logging.getLogger()
        original_handlers = root_logger.handlers.copy()
        
        # Create output directory if it doesn't exist
        output_dir = "output/logs"
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate log file name
        log_filename = self.generate_file_name("backtest_log", "log")
        log_path = os.path.join(output_dir, log_filename)
        
        try:
            # Redirect stdout and stderr to the log file
            log_file = open(log_path, 'w')
            sys.stdout = log_file
            sys.stderr = log_file
            
            # Also redirect logging to the same file
            # First, remove all existing handlers
            for handler in original_handlers:
                root_logger.removeHandler(handler)
            
            # Add a file handler for the log file
            file_handler = logging.FileHandler(log_path)
            file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            root_logger.addHandler(file_handler)
            
            print(f"Redirecting output to log file: {log_path}")
            logging.info(f"Redirecting output to log file: {log_path}")
            
            yield log_path
        finally:
            # Restore original stdout and stderr
            sys.stdout = original_stdout
            sys.stderr = original_stderr
            
            # Restore original logging configuration
            for handler in root_logger.handlers:
                root_logger.removeHandler(handler)
                
            for handler in original_handlers:
                root_logger.addHandler(handler)
            
            if 'log_file' in locals():
                log_file.close()
                logging.info(f"Log file created at: {log_path}")
    
    def generate_file_name(self, file_type, extension):
        """
        Generate a standardized file name for reports and logs.
        
        Args:
            file_type: Type of file (e.g., 'backtest_report', 'trade_stats', 'backtest_log')
            extension: File extension (e.g., 'html', 'log')
            
        Returns:
            str: Formatted file name
        """
        # Create a string representation of strategy parameters for the filename
        params_str = "_".join([f"{k}_{v}" for k, v in self.strategy_params.items()])
        if params_str:
            params_str = "_" + params_str
        
        # Generate file name
        return f"{self.strategy_name}{params_str}_{file_type}_{self.start_date.strftime('%Y%m%d')}_to_{self.end_date.strftime('%Y%m%d')}.{extension}"
    
    async def log_backtest_stats(self, benchmark_ticker="SPY", run_monte_carlo=True, 
                         monte_carlo_simulations=1000, monte_carlo_days=365):
        """
        Log and store backtest statistics, including portfolio stats and trade stats.
        
        Args:
            benchmark_ticker: Ticker symbol for benchmark comparison
            run_monte_carlo: Whether to run Monte Carlo simulation
            monte_carlo_simulations: Number of Monte Carlo simulations
            monte_carlo_days: Number of days for Monte Carlo projection
            
        Returns:
            dict: Information about stored statistics
        """
        logger.info(f"Storing backtest statistics from {self.start_date} to {self.end_date}")
        
        # Store the statistics in the organized directory structure
        storage_info = await self.stats_builder.store_statistics(
            start_date=self.start_date.date(),
            end_date=self.end_date.date(),
            is_backtest=True,
            benchmark_ticker=benchmark_ticker
        )
        
        # Run Monte Carlo simulation if enabled
        if run_monte_carlo and len(self.broker.closed_trades) > 0:
            logger.info("Running Monte Carlo simulation...")
            
            # Build Monte Carlo stats
            monte_carlo = await self.stats_builder.build_monte_carlo_stats(
                num_simulations=monte_carlo_simulations,
                days=monte_carlo_days,
                starting_equity=self.broker.initial_capital
            )
            
            # Generate Monte Carlo report
            monte_carlo_report_path = os.path.join(
                storage_info["base_dir"],
                "monte_carlo.html"
            )
            
            monte_carlo.generate_report(
                output_path=monte_carlo_report_path,
                title=f"{self.strategy_name} Monte Carlo Simulation ({self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')})"
            )
            
            if "reports" in storage_info:
                storage_info["reports"]["monte_carlo"] = monte_carlo_report_path
            
            logger.info(f"Monte Carlo simulation report generated at: {monte_carlo_report_path}")
        
        logger.info(f"Backtest statistics stored at: {storage_info['base_dir']}")
        return storage_info


class LiveStatsLogger:
    """
    Handles logging statistics for live trading sessions.
    Logs stats at the end of each trading day, based on the specified session type.
    """
    
    def __init__(self, strategy_name, strategy_params, broker, trade_session="rth"):
        """
        Initialize the live stats logger.
        
        Args:
            strategy_name: Name of the strategy
            strategy_params: Dictionary of strategy parameters
            broker: The broker instance that implements IBrokerStats
            trade_session: Trading session type, either "rth" (regular trading hours) 
                          or "full" (including extended hours)
        """
        # Validate that broker implements IBrokerStats
        if not isinstance(broker, IBrokerStats):
            raise TypeError("Broker must implement IBrokerStats interface")
            
        self.strategy_name = strategy_name
        self.strategy_params = strategy_params or {}
        self.broker = broker
        self.trade_session = trade_session
        
        # Derive strategy version from params
        self.strategy_version = None
        if self.strategy_params:
            self.strategy_version = "_".join([f"{k}_{v}" for k, v in self.strategy_params.items()])
        
        # Create the StatsBuilder
        self.stats_builder = StatsBuilder(
            broker=self.broker,
            strategy_name=self.strategy_name,
            strategy_version=self.strategy_version
        )
        
        # Keep track of the last logged date
        self.last_logged_date = None
        
        # Use the broker's clock to determine time
        self.clock = self.broker.clock
        
        # Store the task for cleanup
        self._logging_task = None
    
    async def start(self, benchmark_ticker="SPY"):
        """
        Start the stats logging process as a task.
        
        Args:
            benchmark_ticker: Ticker symbol for benchmark comparison
            
        Returns:
            The LiveStatsLogger instance for method chaining
        """
        logger.info(f"Starting live stats logging for {self.strategy_name} with {self.trade_session} session")
        self._logging_task = asyncio.create_task(
            self.start_logging(benchmark_ticker=benchmark_ticker)
        )
        return self
    
    async def stop(self):
        """
        Stop the stats logging process cleanly.
        """
        if self._logging_task and not self._logging_task.done():
            logger.info("Stopping live stats logging")
            self._logging_task.cancel()
            try:
                await self._logging_task
            except asyncio.CancelledError:
                pass
            self._logging_task = None
    
    async def start_logging(self, benchmark_ticker="SPY", check_interval=60):
        """
        Start continuous logging of live trading statistics.
        Will check periodically if it's time to log stats (at market close).
        
        Args:
            benchmark_ticker: Ticker symbol for benchmark comparison
            check_interval: Interval in seconds to check if market is closed
        """
        logger.info(f"Live stats logging process started for {self.strategy_name}")
        
        while True:
            try:
                now = self.clock.now()
                today = now.date()
                
                # Check if we need to log stats for today
                if self._is_market_close_time(now) and (self.last_logged_date is None or today > self.last_logged_date):
                    logger.info(f"Market close detected at {now}, logging stats for {today}")
                    
                    await self.log_daily_stats(today, benchmark_ticker)
                    self.last_logged_date = today
                    
                    # Calculate time until next market close (approximately next day)
                    wait_time = min(86400, check_interval)  # Max 1 day
                    logger.info(f"Next stats check in {wait_time} seconds")
                else:
                    wait_time = check_interval
                
                # Wait for the next check
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                logger.error(f"Error in live stats logging: {e}")
                await asyncio.sleep(check_interval)
    
    def _is_market_close_time(self, dt):
        """
        Check if the given datetime is at or just after market close time
        for the specified trading session.
        
        Args:
            dt: Datetime to check
            
        Returns:
            bool: True if it's market close time, False otherwise
        """
        # For regular trading hours (RTH), market close is at 16:00 EST
        hour, minute = dt.hour, dt.minute
        
        if self.trade_session == "rth":
            # Check for a window right after 16:00 (4:00 PM EST)
            return hour == 16 and 0 <= minute < 10
        else:  # "full" session
            # For full session, market close is at 20:00 EST (8:00 PM)
            return hour == 20 and 0 <= minute < 10
    
    async def log_daily_stats(self, date, benchmark_ticker="SPY"):
        """
        Log statistics for a specific date.
        
        Args:
            date: The date to log statistics for
            benchmark_ticker: Ticker symbol for benchmark comparison
            
        Returns:
            dict: Information about stored statistics
        """
        logger.info(f"Logging live trading stats for {date}")
        
        # We use the same date for both start and end to indicate a single-day log
        storage_info = await self.stats_builder.store_statistics(
            start_date=date,
            end_date=date,
            is_backtest=False,  # This is live trading
            benchmark_ticker=benchmark_ticker
        )
        
        logger.info(f"Live trading statistics for {date} stored at: {storage_info['base_dir']}")
        return storage_info 