import os
import pytz
from datetime import datetime
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from polygon import RESTClient
from marketdata.polygon_market_data import PolygonMarketData

import dotenv

dotenv.load_dotenv()

# -----------------------------
# Setup Polygon Market Data
# -----------------------------
api_key = os.getenv("POLYGON_API_KEY")
if not api_key:
    raise ValueError("POLYGON_API_KEY environment variable not set.")
client = RESTClient(api_key=api_key)
polygon_data = PolygonMarketData(client)

# -----------------------------
# Parameters and Market Data Setup
# -----------------------------
# Define the tickers for the backtest
tickers = ['SPY', 'EEM']

# Define the backtest period: 2 years (e.g., Jan 1, 2021 to Jan 1, 2023)
eastern = pytz.timezone("US/Eastern")
start_dt = eastern.localize(datetime(2023, 3, 1))
end_dt = eastern.localize(datetime(2025, 2, 26))

# Retrieve historical data for each ticker using PolygonMarketData.
spy_df = polygon_data.gather_historical_data("SPY", start_dt, end_dt)
eem_df = polygon_data.gather_historical_data("EEM", start_dt, end_dt)

# Aggregate minute-level data to daily data by taking the last available close price each day.
spy_daily = spy_df['close'].resample('D').last().dropna()
eem_daily = eem_df['close'].resample('D').last().dropna()

# Create a unified DataFrame ensuring the dates align.
data = pd.DataFrame({'SPY': spy_daily, 'EEM': eem_daily}).dropna()

# -----------------------------
# Step 2: Compute Rolling Returns and Statistics
# -----------------------------
# Calculate 100-day rolling returns for both assets
data['SPY_100d_return'] = data['SPY'].pct_change(periods=100)
data['EEM_100d_return'] = data['EEM'].pct_change(periods=100)

# Calculate the spread (SPY outperformance over EEM)
data['spread'] = data['SPY_100d_return'] - data['EEM_100d_return']

# Convert to percentage for easier comparison with article thresholds
data['spread_pct'] = data['spread'] * 100

# Historical mean and standard deviation from the article
HISTORICAL_MEAN = 3.89  # 3.89% from article
UPPER_THRESHOLD = 11.0  # 11% (mean + 1 std dev from article)
LOWER_THRESHOLD = -3.32  # -3.32% (mean - 1 std dev from article)

# -----------------------------
# Step 3: Generate Trading Signals
# -----------------------------
position = 0
positions = []

for idx, row in data.iterrows():
    # Skip periods where rolling returns are not available
    if pd.isna(row['spread_pct']):
        positions.append(0)
        continue
    
    if position == 0:
        # No position currently
        if row['spread_pct'] >= UPPER_THRESHOLD:
            position = -1  # Short SPY / Long EEM
        if row['spread_pct'] <= LOWER_THRESHOLD:
            position = 1   # Long SPY / Short EEM
    else:
        # Check if spread has returned to mean
        if abs(row['spread_pct'] - HISTORICAL_MEAN) <= 0.25:  # Within 0.25% of mean
            position = 0   # Close position
        # If not at mean, check for reversal signals
        elif position == -1 and row['spread_pct'] <= LOWER_THRESHOLD:
            position = 1   # Reverse from Short to Long
        elif position == 1 and row['spread_pct'] >= UPPER_THRESHOLD:
            position = -1  # Reverse from Long to Short
            
    positions.append(position)

data['position'] = positions

# -----------------------------
# Step 4: Calculate Daily Returns for SPY and EEM
# -----------------------------
data['r_SPY'] = data['SPY'].pct_change()
data['r_EEM'] = data['EEM'].pct_change()

# -----------------------------
# Step 5: Calculate Strategy Returns and Portfolio Equity
# -----------------------------
# We assume an initial capital of $100K with equal-dollar allocation ($50K each leg).
data['strategy_return'] = 0  # Initialize

data.loc[data['position'] == 1, 'strategy_return'] = 0.5 * (data['r_SPY'] - data['r_EEM'])
data.loc[data['position'] == -1, 'strategy_return'] = 0.5 * (data['r_EEM'] - data['r_SPY'])
data['strategy_return'] = data['strategy_return'].fillna(0)

# Compute the portfolio value over time using the cumulative product of daily returns.
initial_capital = 100000
data['portfolio_value'] = initial_capital * (1 + data['strategy_return']).cumprod()

# -----------------------------
# Step 6: Output Results and Plot the Performance
# -----------------------------
print("Final portfolio value: ${:,.2f}".format(data['portfolio_value'].iloc[-1]))

plt.figure(figsize=(12, 6))
plt.plot(data['portfolio_value'], label="Portfolio Value", color='blue')
plt.xlabel("Date")
plt.ylabel("Portfolio Value ($)")
plt.title("Mean Reversion Pairs Backtest on SPY/EEM Ratio using Polygon Market Data")
plt.legend()
plt.grid(True)
plt.savefig("portfolio_value_plot.png")  # Save the plot to a file
plt.close()  # Close the plot to free up memory

# Create a new figure to show the spread and positions
plt.figure(figsize=(14, 8))

# Plot the spread curve
plt.plot(data.index, data['spread_pct'], label='SPY-EEM Spread (%)', color='blue')

# Add horizontal lines for the thresholds
plt.axhline(y=UPPER_THRESHOLD, color='r', linestyle='--', alpha=0.7, label=f'Upper Threshold ({UPPER_THRESHOLD:.2f}%)')
plt.axhline(y=LOWER_THRESHOLD, color='g', linestyle='--', alpha=0.7, label=f'Lower Threshold ({LOWER_THRESHOLD:.2f}%)')
plt.axhline(y=HISTORICAL_MEAN, color='k', linestyle='-', alpha=0.7, label=f'Historical Mean ({HISTORICAL_MEAN:.2f}%)')

# Highlight position entries and exits
long_entries = data[data['position'].diff() == 1].index
long_exits = data[(data['position'].diff() == -1) & (data['position'].shift(1) == 1)].index
short_entries = data[data['position'].diff() == -1].index
short_exits = data[(data['position'].diff() == 1) & (data['position'].shift(1) == -1)].index

# Plot position markers
plt.scatter(long_entries, data.loc[long_entries, 'spread_pct'], marker='^', color='g', s=100, label='Long SPY/Short EEM Entry')
plt.scatter(long_exits, data.loc[long_exits, 'spread_pct'], marker='v', color='g', s=100, label='Long SPY/Short EEM Exit')
plt.scatter(short_entries, data.loc[short_entries, 'spread_pct'], marker='^', color='r', s=100, label='Short SPY/Long EEM Entry')
plt.scatter(short_exits, data.loc[short_exits, 'spread_pct'], marker='v', color='r', s=100, label='Short SPY/Long EEM Exit')

# Calculate total P&L
total_pnl = data['portfolio_value'].iloc[-1] - initial_capital
pnl_pct = (total_pnl / initial_capital) * 100

# Add title and labels
plt.title(f'SPY-EEM Spread with Trading Signals') #  (Total P&L: ${total_pnl:,.2f}, {pnl_pct:.2f}%)
plt.xlabel('Date')
plt.ylabel('Spread (%)')
plt.grid(True, alpha=0.3)
plt.legend(loc='best')


# Combine both legends
lines1, labels1 = plt.gca().get_legend_handles_labels()

plt.tight_layout()
plt.savefig("spread_and_positions_plot.png")
plt.close()

