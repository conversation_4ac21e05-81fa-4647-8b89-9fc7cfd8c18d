import os
import pandas as pd
import logging
import json
from datetime import datetime, date
from pathlib import Path
import glob
from typing import List, Dict, Union, Optional, Tuple

logger = logging.getLogger(__name__)

class StatsStoreEntry:
    """
    Represents a single statistics entry from a backtest or live trading run.
    Responsible for loading, accessing, and working with individual stat entries.
    """
    
    def __init__(self, base_path: str):
        """
        Initialize a StatsStoreEntry from a path to a stats directory.
        
        Args:
            base_path: Path to the stats directory containing data files
        """
        self.base_path = Path(base_path)
        self.metadata = self._load_metadata()
        self.trades_df = None
        self.portfolio_values = None
        self.market_data = {}
        
    def _load_metadata(self) -> dict:
        metadata_path = self.base_path / "metadata.json"
        if metadata_path.exists():
            try:
                with open(metadata_path, 'r') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                raise RuntimeError(f"Error parsing metadata file at {metadata_path}")
        else:
            raise RuntimeError(f"No metadata file found at {metadata_path}")
    
    def load_trades(self) -> pd.DataFrame:
        """
        Load trades data from the trades.csv file.
        
        Returns:
            pd.DataFrame: DataFrame containing trades data
        """
        trades_path = self.base_path / "trades.csv"
        if trades_path.exists():
            self.trades_df = pd.read_csv(trades_path, index_col=0, parse_dates=True)
            return self.trades_df
        else:
            raise RuntimeError(f"No trades file found at {trades_path}")
            
    def load_portfolio_values(self) -> pd.Series:
        portfolio_path = self.base_path / "daily_portfolio_values.csv"
        if portfolio_path.exists():
            self.portfolio_values = pd.read_csv(portfolio_path, index_col=0, parse_dates=True)
            return self.portfolio_values
        else:
            raise RuntimeError(f"No portfolio values file found at {portfolio_path}")

    def load_market_data(self, symbols: List[str] = None) -> Dict[str, pd.DataFrame]:
        if symbols is None:
            pattern = str(self.base_path / "*_historical_data.csv")
            data_files = glob.glob(pattern)
            symbols = [Path(f).stem.split('_historical_data')[0] for f in data_files]

        for symbol in symbols:
            file_path = self.base_path / f"{symbol}_historical_data.csv"
            if file_path.exists():
                self.market_data[symbol] = pd.read_csv(file_path, index_col=0, parse_dates=True)
            else:
                raise RuntimeError(f"No historical data found for {symbol} at {file_path}")

        return self.market_data

    def get_start_date(self) -> Union[datetime, date, None]:
        """
        Get the start date of this stats entry.
        
        Returns:
            datetime or date: Start date from metadata or None if not available
        """
        if self.metadata and 'start_date' in self.metadata:
            start_date_str = self.metadata['start_date']
            try:
                return datetime.strptime(start_date_str, '%Y%m%d').date()
            except ValueError:
                logger.warning(f"Could not parse start date: {start_date_str}")
                return None
        return None
    
    def get_end_date(self) -> Union[datetime, date, None]:
        """
        Get the end date of this stats entry.
        
        Returns:
            datetime or date: End date from metadata or None if not available
        """
        if self.metadata and 'end_date' in self.metadata:
            end_date_str = self.metadata['end_date']
            try:
                return datetime.strptime(end_date_str, '%Y%m%d').date()
            except ValueError:
                logger.warning(f"Could not parse end date: {end_date_str}")
                return None
        return None
    
    def is_backtest(self) -> bool:
        """
        Check if this stats entry is from a backtest (True) or live trading (False).
        
        Returns:
            bool: True if backtest, False if live trading
        """
        # Infer from the path structure
        return "backtest" in str(self.base_path)
    
    def get_strategy_info(self) -> Tuple[str, str]:
        """
        Get the strategy name and version.
        
        Returns:
            tuple: (strategy_name, strategy_version)
        """
        strategy_name = self.metadata.get('strategy_name', None)
        strategy_version = self.metadata.get('strategy_version', None)
        
        if strategy_name is None:
            # Try to infer from path
            parts = self.base_path.parts
            if 'stats' in parts:
                idx = parts.index('stats')
                if idx + 1 < len(parts):
                    strategy_name = parts[idx + 1]
                if idx + 2 < len(parts):
                    strategy_version = parts[idx + 2]
        
        return strategy_name, strategy_version
   
    def _merge_portfolio_values(self, base_values: pd.DataFrame, new_values: pd.DataFrame) -> pd.DataFrame:
        """
        Simpler implementation that merges portfolio values by continuing the last value from base_values
        and appending the daily dollar changes from new_values.
        
        This assumes that the new_values DataFrame contains the date of the last base_values entry.
        
        Args:
            base_values: Base portfolio values DataFrame
            new_values: New portfolio values DataFrame to append
            
        Returns:
            pd.DataFrame: Merged portfolio values
        """
        if base_values.empty:
            return new_values
        
        if new_values.empty:
            return base_values
            
        # Sort both DataFrames by date
        base_values = base_values.sort_index()
        new_values = new_values.sort_index()
        
        # Get the last date in base_values
        last_base_date = base_values.index[-1]
        last_base_value = base_values.iloc[-1].copy()
        
        # Get the dollar change in new_values (day-to-day differences)
        new_values_dollar_change = new_values.diff()
        
        # Filter to just get changes after the last base date
        future_dollar_changes = new_values_dollar_change[new_values_dollar_change.index > last_base_date]
        
        # If there are no new values after the last base date, return base_values
        if future_dollar_changes.empty:
            return base_values
        
        # Adjust the future values by applying changes to the last base value
        adjusted_values = pd.DataFrame(index=future_dollar_changes.index, columns=future_dollar_changes.columns)
        
        # Start with the last base value
        current_value = last_base_value.copy()
        
        # Apply daily dollar changes
        for date, row in future_dollar_changes.iterrows():
            current_value = current_value + row
            adjusted_values.loc[date] = current_value
        
        # Concatenate the base_values with the adjusted new_values
        merged_values = pd.concat([base_values, adjusted_values])
        
        return merged_values
 
    def append(self, other: 'StatsStoreEntry') -> 'StatsStoreEntry':
        """
        Append trades_df, portfolio_values, and market_data from another StatsStoreEntry
        and return a new StatsStoreEntry without modifying the existing one.
        
        Args:
            other: Another StatsStoreEntry to append data from
            
        Returns:
            StatsStoreEntry: A new in-memory StatsStoreEntry with the combined data
        """
        # Ensure data is loaded for both entries
        if self.trades_df is None:
            self.load_trades()
        if self.portfolio_values is None:
            self.load_portfolio_values()
        if not self.market_data:
            self.load_market_data()
            
        if other.trades_df is None:
            other.load_trades()
        if other.portfolio_values is None:
            other.load_portfolio_values()
        if not other.market_data:
            other.load_market_data()
        
        # Get strategy info
        strategy_name, strategy_version = self.get_strategy_info()
        
        # Determine date range
        start_date = self.get_start_date() or datetime.now().date()
        end_date = other.get_end_date() or datetime.now().date()
        
        # Merge trades data
        all_trades = pd.concat([self.trades_df, other.trades_df])
        all_trades = all_trades.sort_index()
        
        # Merge portfolio values
        all_portfolio_values = self._merge_portfolio_values(self.portfolio_values, other.portfolio_values)
        
        # Determine if backtest or live
        is_backtest = self.is_backtest() and other.is_backtest()
        
        # Gather all unique symbols across entries
        all_symbols = set()
        if 'symbols_traded' in self.metadata:
            all_symbols.update(self.metadata['symbols_traded'])
        if 'symbols_traded' in other.metadata:
            all_symbols.update(other.metadata['symbols_traded'])
        
        # Merge market data for each symbol
        merged_market_data = {}
        for symbol in all_symbols:
            symbol_data = []
            if symbol in self.market_data:
                symbol_data.append(self.market_data[symbol])
            if symbol in other.market_data:
                symbol_data.append(other.market_data[symbol])
                
            if symbol_data:
                merged_df = pd.concat(symbol_data)
                merged_df = merged_df.sort_index()
                merged_df = merged_df[~merged_df.index.duplicated(keep='last')]
                merged_market_data[symbol] = merged_df


        # Create merged metadata
        merged_metadata = {
            "strategy_name": strategy_name,
            "strategy_version": strategy_version,
            "start_date": start_date.strftime('%Y%m%d'),
            "end_date": end_date.strftime('%Y%m%d'),
            "initial_capital": float(self.metadata.get('initial_capital', 0)),
            "final_capital": float(all_portfolio_values.iloc[-1] if not all_portfolio_values.empty else 0),
            "total_trades": len(all_trades),
            "symbols_traded": list(all_symbols),
            "created_at": datetime.now().isoformat(),
            "is_appended": True,
            "appended_from": [str(self.base_path), str(other.base_path)],
            "appended_at": datetime.now().isoformat()
        }

        # Create a new in-memory entry
        new_entry = StatsStoreEntry.__new__(StatsStoreEntry)
        # Set the base_path for the new entry
        new_entry.base_path = Path(f"stats/{strategy_name}/{strategy_version or 'default'}/{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}/{('backtest' if is_backtest else 'live')}")
        new_entry.metadata = merged_metadata
        new_entry.trades_df = all_trades
        new_entry.portfolio_values = all_portfolio_values
        new_entry.market_data = merged_market_data
        
        return new_entry

    def save_to_disk(self) -> 'StatsStoreEntry':
        """
        Save the current StatsStoreEntry to disk.
        
        Args:
            base_dir: Optional base directory. If not provided, creates one based on strategy and date range
            
        Returns:
            StatsStoreEntry: A new StatsStoreEntry pointing to the saved location
        """
        output_dir = self.base_path

        # Create directory, fail if it already exists
        if output_dir.exists():
            raise FileExistsError(f"Directory already exists: {output_dir}")
        output_dir.mkdir(parents=True)
        
        # Save trades
        if self.trades_df is not None:
            trades_path = output_dir / "trades.csv"
            self.trades_df.to_csv(trades_path)
        
        # Save portfolio values
        if self.portfolio_values is not None:
            portfolio_path = output_dir / "daily_portfolio_values.csv"
            self.portfolio_values.to_csv(portfolio_path)
        
        # Save market data
        for symbol, data in self.market_data.items():
            symbol_path = output_dir / f"{symbol}_historical_data.csv"
            data.to_csv(symbol_path)
        
        # Save metadata
        metadata_path = output_dir / "metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(self.metadata, f, indent=4)
        
        return StatsStoreEntry(output_dir)

class StatsStore:
    """
    Manages multiple statistics entries and operations across them.
    Supports merging, querying, and aggregating stats across multiple runs.
    """
    
    def __init__(self, base_dir: str = "stats"):
        """
        Initialize the StatsStore with a base directory.
        
        Args:
            base_dir: Base directory where stats are stored
        """
        self.base_dir = Path(base_dir)
        
    def list_strategies(self) -> List[str]:
        """
        List all strategy names available in the stats store.
        
        Returns:
            list: List of strategy names
        """
        if not self.base_dir.exists():
            return []
        
        return [d.name for d in self.base_dir.iterdir() if d.is_dir()]
    
    def list_strategy_versions(self, strategy_name: str) -> List[str]:
        """
        List all versions of a strategy.
        
        Args:
            strategy_name: Name of the strategy
            
        Returns:
            list: List of strategy versions
        """
        strategy_dir = self.base_dir / strategy_name
        if not strategy_dir.exists():
            return []
        
        return [d.name for d in strategy_dir.iterdir() if d.is_dir()]
    
    def find_stat_entries(self, 
                         strategy_name: str, 
                         strategy_version: Optional[str] = None,
                         is_backtest: Optional[bool] = None,
                         start_date: Optional[Union[str, datetime, date]] = None,
                         end_date: Optional[Union[str, datetime, date]] = None) -> List[StatsStoreEntry]:
        """
        Find stat entries matching the specified criteria.
        
        Args:
            strategy_name: Name of the strategy
            strategy_version: Version of the strategy (optional)
            is_backtest: True for backtest entries, False for live entries (optional)
            start_date: Filter for entries starting on/after this date (optional)
            end_date: Filter for entries ending on/before this date (optional)
            
        Returns:
            list: List of StatsStoreEntry objects matching the criteria
        """
        if not self.base_dir.exists():
            return []
        
        strategy_dir = self.base_dir / strategy_name
        if not strategy_dir.exists():
            return []
        
        # Convert dates to standard format
        if start_date and isinstance(start_date, str):
            try:
                start_date = datetime.strptime(start_date, '%Y%m%d').date()
            except ValueError:
                logger.warning(f"Invalid start date format: {start_date}")
                start_date = None
        
        if end_date and isinstance(end_date, str):
            try:
                end_date = datetime.strptime(end_date, '%Y%m%d').date()
            except ValueError:
                logger.warning(f"Invalid end date format: {end_date}")
                end_date = None
            
        entries = []
        
        # Get all versions if not specified
        versions = [strategy_version] if strategy_version else self.list_strategy_versions(strategy_name)
        
        for version in versions:
            version_dir = strategy_dir / version
            if not version_dir.exists():
                continue
            
            # Get all date ranges
            for date_range_dir in version_dir.iterdir():
                if not date_range_dir.is_dir():
                    continue
                
                # For backtest or live
                env_dirs = []
                if is_backtest is None:
                    env_dirs = list(date_range_dir.iterdir())
                elif is_backtest:
                    backtest_dir = date_range_dir / "backtest"
                    if backtest_dir.exists():
                        env_dirs = [backtest_dir]
                else:
                    live_dir = date_range_dir / "live"
                    if live_dir.exists():
                        env_dirs = [live_dir]
                
                for env_dir in env_dirs:
                    if not env_dir.is_dir():
                        continue
                    
                    # Create a stats entry
                    entry = StatsStoreEntry(env_dir)
                    
                    # Apply date filters if specified
                    entry_start = entry.get_start_date()
                    entry_end = entry.get_end_date()
                    
                    if start_date and entry_end and entry_end < start_date:
                        continue
                    if end_date and entry_start and entry_start > end_date:
                        continue
                    
                    entries.append(entry)
        
        return entries
    
    def merge_entries(self, 
                     entries: List[StatsStoreEntry], 
                     base_dir: Optional[str] = None,
                     strategy_name: Optional[str] = None,
                     strategy_version: Optional[str] = None) -> StatsStoreEntry:
        """
        Merge multiple stat entries into a single entry.
        
        Args:
            entries: List of StatsStoreEntry objects to merge
            output_dir: Directory to save the merged entry (optional)
            strategy_name: Strategy name for the merged entry (optional)
            strategy_version: Strategy version for the merged entry (optional)
            
        Returns:
            StatsStoreEntry: A new StatsStoreEntry with the merged data
        """
        if not entries:
            logger.warning("No entries provided to merge")
            return None
            
        if len(entries) == 1:
            return entries[0]
            
        # Use first entry as base and append others sequentially
        merged_entry = entries[0]
        for entry in entries[1:]:
            merged_entry = merged_entry.append(entry)
            
        # Update metadata with provided strategy info
        if strategy_name:
            merged_entry.metadata['strategy_name'] = strategy_name
        if strategy_version:
            merged_entry.metadata['strategy_version'] = strategy_version
            
        return merged_entry.save_to_disk()

    def merge_daily_stats(self, 
                         strategy_name: str, 
                         strategy_version: Optional[str] = None,
                         is_backtest: bool = False, 
                         start_date: Optional[Union[str, datetime, date]] = None,
                         end_date: Optional[Union[str, datetime, date]] = None) -> StatsStoreEntry:
        """
        Merge daily stats for a strategy into a continuous aggregated stat.
        
        Args:
            strategy_name: Name of the strategy
            strategy_version: Version of the strategy (optional)
            is_backtest: Whether to merge backtest (True) or live (False) stats
            start_date: Start date to merge from (optional)
            end_date: End date to merge to (optional)
            
        Returns:
            StatsStoreEntry: A new StatsStoreEntry with the merged data
        """
        # Find all matching entries
        entries = self.find_stat_entries(
            strategy_name=strategy_name,
            strategy_version=strategy_version,
            is_backtest=is_backtest,
            start_date=start_date,
            end_date=end_date
        )
        
        if not entries:
            logger.warning(f"No entries found for {strategy_name} {strategy_version or 'all versions'} "
                         f"from {start_date or 'beginning'} to {end_date or 'now'}")
            return None
        
        # Merge the entries
        return self.merge_entries(entries) 