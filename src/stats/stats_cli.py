#!/usr/bin/env python
import argparse
import logging
from datetime import datetime, date, timedelta
import sys
import os

from stats.stats_store import StatsStore, StatsStoreEntry
from stats.stats_builder import StatsBuilder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def parse_date(date_str):
    """Parse a date string in YYYYMMDD format"""
    if not date_str:
        return None
    try:
        return datetime.strptime(date_str, '%Y%m%d').date()
    except ValueError:
        raise ValueError(f"Invalid date format: {date_str}. Use YYYYMMDD format.")

def main():
    parser = argparse.ArgumentParser(description="Stats Management Utilities")
    subparsers = parser.add_subparsers(dest="command")
    
    # List strategies command
    list_parser = subparsers.add_parser("list", help="List available strategies")
    list_parser.add_argument("--strategy", help="Filter by strategy name")
    
    # Interactive Merge command
    interactive_merge_parser = subparsers.add_parser("interactive-merge", help="Merge specific stats entries interactively")
    interactive_merge_parser.add_argument("--strategy", help="Filter by strategy name to narrow down choices")
    interactive_merge_parser.add_argument("--version", help="Filter by strategy version")
    interactive_merge_parser.add_argument("--env", choices=["backtest", "live"], help="Filter by environment")
    interactive_merge_parser.add_argument("--output-dir", help="Custom output directory for the merged entry")

    # Merge daily stats command
    merge_parser = subparsers.add_parser("merge-daily", help="Merge daily stats into a continuous stat")
    merge_parser.add_argument("strategy", help="Strategy name")
    merge_parser.add_argument("--version", help="Strategy version")
    merge_parser.add_argument("--env", choices=["backtest", "live"], default="live", 
                             help="Environment (backtest or live)")
    merge_parser.add_argument("--start-date", help="Start date (YYYYMMDD)")
    merge_parser.add_argument("--end-date", help="End date (YYYYMMDD)")
    merge_parser.add_argument("--last-days", type=int, help="Merge last N days")
    merge_parser.add_argument("--output-dir", help="Custom output directory")
    
    # Rebuild reports command
    rebuild_parser = subparsers.add_parser("rebuild-reports", help="Rebuild reports for a merged stat")
    rebuild_parser.add_argument("path", help="Path to the stats directory")
    rebuild_parser.add_argument("--benchmark", default="SPY", help="Benchmark ticker")
    
    args = parser.parse_args()
    
    if args.command is None:
        parser.print_help()
        return
    
    stats_store = StatsStore()
    
    if args.command == "list":
        if args.strategy:
            versions = stats_store.list_strategy_versions(args.strategy)
            print(f"Versions for strategy '{args.strategy}':")
            for v in versions:
                print(f"  - {v}")
        else:
            strategies = stats_store.list_strategies()
            print("Available strategies:")
            for s in strategies:
                print(f"  - {s}")
    
    elif args.command == "interactive-merge":
        # Determine environment filter
        is_backtest_filter = None
        if args.env:
            is_backtest_filter = args.env == "backtest"
            
        print("\nSearching for matching entries...")
        try:
            # Use find_stat_entries which returns StatsStoreEntry objects
            found_entries = stats_store.find_stat_entries(
                strategy_name=args.strategy, 
                strategy_version=args.version, 
                is_backtest=is_backtest_filter
            )
        except Exception as e:
            logger.error(f"Error finding stat entries: {e}", exc_info=True)
            print(f"An error occurred while searching for entries: {e}")
            return

        if not found_entries:
            print("No matching entries found for the specified filters.")
            return

        print("\nFound the following entries:")
        # Sort entries chronologically for better display
        found_entries.sort(key=lambda entry: entry.get_start_date() or date.min)
        
        for i, entry in enumerate(found_entries):
            start_date = entry.get_start_date()
            end_date = entry.get_end_date()
            env_type = "backtest" if entry.is_backtest() else "live"
            s_name, s_version = entry.get_strategy_info()
            # Display only the directory name part of the path
            print(f"  {i+1}: {entry.base_path.name} ({s_name or 'N/A'} v{s_version or 'N/A'}, {env_type}, {start_date or '?'} to {end_date or '?'})")

        while True:
            try:
                selection = input("\nEnter the numbers of the entries to merge (min 2), separated by commas (e.g., 1,3,4), or 'q' to quit: ")
                selection = selection.strip().lower()
                if selection == 'q':
                    print("Merge cancelled.")
                    return
                    
                if not selection:
                    continue
                    
                selected_indices = []
                parts = selection.split(',')
                valid_input = True
                for part in parts:
                    part = part.strip()
                    if part.isdigit():
                        selected_indices.append(int(part) - 1) # User sees 1-based index
                    else:
                        valid_input = False
                        break
                
                if not valid_input:
                    print("Invalid input. Please enter only numbers separated by commas.")
                    continue
                
                # Validate indices and build list of entries to merge
                selected_entries_to_merge = []
                invalid_indices = []
                # Remove duplicates and sort indices before validation
                unique_indices = sorted(list(set(selected_indices)))
                
                for index in unique_indices:
                    if 0 <= index < len(found_entries):
                        selected_entries_to_merge.append(found_entries[index])
                    else:
                        invalid_indices.append(index + 1) # Show user 1-based index
                        
                if invalid_indices:
                    print(f"Error: Invalid index number(s): {', '.join(map(str, invalid_indices))}. Please choose from 1 to {len(found_entries)}.")
                    continue
                    
                if len(selected_entries_to_merge) < 2:
                    print("Please select at least two different entries to merge.")
                    continue

                # Confirm selection
                print("\nYou selected the following entries to merge (sorted chronologically):")
                # Sort selected entries chronologically before showing confirmation and merging
                selected_entries_to_merge.sort(key=lambda entry: entry.get_start_date() or date.min)
                for entry in selected_entries_to_merge:
                     s_name, s_version = entry.get_strategy_info()
                     # Display only the directory name part of the path for confirmation
                     print(f"  - {entry.base_path.name} ({s_name or 'N/A'} v{s_version or 'N/A'}, {entry.get_start_date() or '?'} to {entry.get_end_date() or '?'})")
                
                confirm = input("Proceed with merge? (y/n): ").strip().lower()
                if confirm != 'y':
                    print("Merge selection cancelled. Please enter new selection or 'q'.")
                    # Allow user to re-select without re-listing
                    continue 

                # Perform the merge
                print("\nMerging selected entries...")
                try:
                    # Determine merged strategy name/version
                    # Use first selected entry's info if not provided via args
                    merged_strategy_name = args.strategy
                    merged_strategy_version = args.version
                    # Get info from the chronologically first selected entry
                    s_name_inferred, s_version_inferred = selected_entries_to_merge[0].get_strategy_info()
                    merged_strategy_name = merged_strategy_name or s_name_inferred
                    merged_strategy_version = merged_strategy_version or s_version_inferred
                         
                    if not merged_strategy_name or not merged_strategy_version:
                        print("Error: Cannot determine strategy name/version for the merged entry.")
                        print("Please specify --strategy and --version, or ensure the first selected entry has this info in its metadata.")
                        # Allow re-selection or quitting
                        continue 

                    merged_entry = stats_store.merge_entries(
                        entries=selected_entries_to_merge, # Pass the sorted list
                        base_dir=args.output_dir, # Pass the custom output dir if provided
                        strategy_name=merged_strategy_name,
                        strategy_version=merged_strategy_version
                    )
                    
                    if merged_entry:
                        print(f"\nSuccessfully merged entries. Result saved to: {merged_entry.base_path}")
                    else:
                        # merge_entries should raise an exception on failure, but handle case it returns None
                        print("\nFailed to merge entries. No merged entry was returned.") 
                        
                except Exception as e:
                    logger.error(f"Error during merge process: {e}", exc_info=True)
                    print(f"An error occurred during the merge: {e}")
                
                # Exit the while loop after successful merge or explicit cancellation during prompt
                # or if an error occurs during the actual merge process.
                break 

            except ValueError as e:
                # This might catch errors from int() conversion if isdigit failed somehow, or other unexpected ValueErrors
                print(f"An unexpected input error occurred: {e}")
            except KeyboardInterrupt:
                 print("\nMerge cancelled by user.")
                 return

    elif args.command == "merge-daily":
        start_date = None
        end_date = None
        
        if args.last_days:
            end_date = date.today()
            start_date = end_date - timedelta(days=args.last_days)
        else:
            start_date = parse_date(args.start_date)
            end_date = parse_date(args.end_date)
        
        is_backtest = args.env == "backtest"
        
        print(f"Merging {args.env} stats for {args.strategy} {args.version or 'all versions'} "
              f"from {start_date or 'beginning'} to {end_date or 'now'}...")
        
        merged_entry = stats_store.merge_daily_stats(
            strategy_name=args.strategy,
            strategy_version=args.version,
            is_backtest=is_backtest,
            start_date=start_date,
            end_date=end_date
        )
        
        if merged_entry:
            print(f"Merged stats saved to: {merged_entry.base_path}")
        else:
            print("No stats were merged - no matching entries found.")
    
    elif args.command == "rebuild-reports":
        from brokers.local_broker import LocalBroker
        
        path = args.path
        if not os.path.exists(path):
            print(f"Error: Path {path} does not exist")
            return
        
        entry = StatsStoreEntry(path)
        entry.load_trades()
        entry.load_portfolio_values()
        
        print(f"Rebuilding reports for {entry.base_path}...")
        
        # Create a temporary broker with the loaded trades
        broker = LocalBroker()
        
        # Load the trades into the broker
        if entry.trades_df is not None and not entry.trades_df.empty:
            for _, row in entry.trades_df.iterrows():
                # Convert trade dataframe row to trade dict format expected by broker
                trade = {
                    'symbol': row.get('symbol', ''),
                    'quantity': row.get('quantity', 0),
                    'entry_price': row.get('entry_price', 0),
                    'exit_price': row.get('exit_price', 0),
                    'entry_time': row.get('entry_time', None),
                    'exit_time': row.get('exit_time', None),
                    'position_type': row.get('position_type', 'long'),
                    'pnl': row.get('pnl', 0),
                    'trade_id': row.get('trade_id', '')
                }
                broker.closed_trades.append(trade)
        
        # Set initial capital based on metadata or first portfolio value
        if 'initial_capital' in entry.metadata:
            broker.initial_capital = float(entry.metadata['initial_capital'])
        elif entry.portfolio_values is not None and not entry.portfolio_values.empty:
            broker.initial_capital = float(entry.portfolio_values.iloc[0])
        
        # Create stats builder and rebuild reports
        stats_builder = StatsBuilder(
            broker=broker, 
            strategy_name=entry.metadata.get('strategy_name', 'unknown'),
            strategy_version=entry.metadata.get('strategy_version', 'unknown')
        )
        
        start_date = entry.get_start_date()
        end_date = entry.get_end_date()
        
        if start_date and end_date:
            # Rebuild reports asynchronously
            import asyncio
            
            async def rebuild():
                return await stats_builder.store_statistics(
                    start_date=start_date,
                    end_date=end_date,
                    is_backtest=entry.is_backtest(),
                    benchmark_ticker=args.benchmark
                )
            
            result = asyncio.run(rebuild())
            print(f"Reports rebuilt successfully: {result['reports']}")
        else:
            print("Error: Could not determine date range from metadata")

if __name__ == "__main__":
    main() 