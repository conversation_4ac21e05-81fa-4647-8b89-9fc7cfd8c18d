import pandas as pd
import numpy as np
import logging
from pathlib import Path
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class TradeStats:
    """
    A class to generate trade statistics from a list of trades.
    Takes a dataframe with trade information and calculates various
    performance metrics and visualizations.
    """
    
    def __init__(self, trades_df=None):
        """
        Initialize the TradeStats object.
        
        Args:
            trades_df (pd.DataFrame, optional): DataFrame with trade information.
                Should contain at minimum columns for trade P&L and dates.
        """
        self.trades_df = trades_df
        self.stats = {}
        
    def set_trades(self, trades_df):
        """
        Set the trades dataframe.
        
        Args:
            trades_df (pd.DataFrame): DataFrame with trade information.
        """
        self.trades_df = trades_df
        
    def calculate_stats(self, pnl_column='pnl', date_column='close_date'):
        """
        Calculate trade statistics from the trades dataframe.
        
        Args:
            pnl_column (str): Name of the column containing P&L values.
            date_column (str): Name of the column containing trade dates.
            
        Returns:
            dict: Dictionary containing calculated statistics
        """
        if self.trades_df is None or len(self.trades_df) == 0:
            raise ValueError("Trades data not set or empty. Use set_trades() first.")
        
        # Ensure the dataframe has the required columns
        required_columns = [pnl_column, date_column]
        for col in required_columns:
            if col not in self.trades_df.columns:
                raise ValueError(f"Required column '{col}' not found in trades dataframe")
        
        # Make a copy to avoid modifying the original
        df = self.trades_df.copy()
        
        # First check if the column contains datetime objects
        if isinstance(df[date_column].iloc[0], datetime):
            # The column already contains datetime objects, just remove timezone info
            df[date_column] = df[date_column].apply(lambda dt: dt.replace(tzinfo=None))
        else:
            # Not datetime objects, raise an error
            raise ValueError(f"Column '{date_column}' does not contain datetime objects")
        # Sort by date
        df = df.sort_values(by=date_column)
        
        # Calculate basic statistics
        total_trades = len(df)
        winning_trades = df[df[pnl_column] > 0]
        losing_trades = df[df[pnl_column] < 0]
        
        # Number of transactions and trades
        self.stats['total_trades'] = total_trades
        
        # Gross and net P&L
        self.stats['gross_profit'] = winning_trades[pnl_column].sum() if len(winning_trades) > 0 else 0
        self.stats['gross_loss'] = losing_trades[pnl_column].sum() if len(losing_trades) > 0 else 0
        self.stats['net_pnl'] = df[pnl_column].sum()
        
        # Mean/median P&L per trade
        self.stats['mean_pnl_per_trade'] = df[pnl_column].mean()
        self.stats['median_pnl_per_trade'] = df[pnl_column].median()
        
        # Standard deviation of trade P&L
        self.stats['pnl_std_dev'] = df[pnl_column].std()
        
        # Largest winning/losing trade
        self.stats['largest_winning_trade'] = df[pnl_column].max()
        self.stats['largest_losing_trade'] = df[pnl_column].min()
        
        # Percent of positive/negative trades
        self.stats['winning_trades_count'] = len(winning_trades)
        self.stats['losing_trades_count'] = len(losing_trades)
        self.stats['winning_trades_percent'] = (len(winning_trades) / total_trades * 100) if total_trades > 0 else 0
        self.stats['losing_trades_percent'] = (len(losing_trades) / total_trades * 100) if total_trades > 0 else 0
        
        # Profit Factor
        self.stats['profit_factor'] = abs(self.stats['gross_profit'] / self.stats['gross_loss']) if self.stats['gross_loss'] != 0 else float('inf')
        
        # Mean/median P&L of profitable/losing trades
        self.stats['mean_winning_trade'] = winning_trades[pnl_column].mean() if len(winning_trades) > 0 else 0
        self.stats['median_winning_trade'] = winning_trades[pnl_column].median() if len(winning_trades) > 0 else 0
        self.stats['mean_losing_trade'] = losing_trades[pnl_column].mean() if len(losing_trades) > 0 else 0
        self.stats['median_losing_trade'] = losing_trades[pnl_column].median() if len(losing_trades) > 0 else 0
        
        # Win/loss ratios
        if len(losing_trades) > 0 and abs(self.stats['mean_losing_trade']) > 0:
            self.stats['win_loss_ratio_mean'] = abs(self.stats['mean_winning_trade'] / self.stats['mean_losing_trade'])
        else:
            self.stats['win_loss_ratio_mean'] = float('inf')
            
        if len(losing_trades) > 0 and abs(self.stats['median_losing_trade']) > 0:
            self.stats['win_loss_ratio_median'] = abs(self.stats['median_winning_trade'] / self.stats['median_losing_trade'])
        else:
            self.stats['win_loss_ratio_median'] = float('inf')
            
        if abs(self.stats['gross_loss']) > 0:
            self.stats['win_loss_ratio_total'] = abs(self.stats['gross_profit'] / self.stats['gross_loss'])
        else:
            self.stats['win_loss_ratio_total'] = float('inf')
        
        # Calculate cumulative P&L for drawdown analysis
        df['cumulative_pnl'] = df[pnl_column].cumsum()
        
        # Max drawdown calculation
        df['running_max'] = df['cumulative_pnl'].cummax()
        df['drawdown'] = df['running_max'] - df['cumulative_pnl']
        self.stats['max_drawdown'] = df['drawdown'].max()
        
        # Start-trade drawdown (from Fitschen 2013)
        # This is the maximum drawdown from the beginning of trading
        self.stats['start_trade_drawdown'] = (df['cumulative_pnl'].cummax() - df['cumulative_pnl']).max()
        
        # Calculate annualized Sharpe-like ratio
        # First, get the trading period in years
        if len(df) > 1:
            start_date = df[date_column].min()
            end_date = df[date_column].max()
            trading_days = (end_date - start_date).days
            if trading_days > 0:
                years = trading_days / 365
                
                # Calculate annualized return and volatility
                total_return = df[pnl_column].sum()
                annualized_return = total_return / years if years > 0 else 0
                
                # Daily returns for volatility calculation
                daily_returns = df.set_index(date_column)[pnl_column].resample('D').sum().fillna(0)
                annualized_volatility = daily_returns.std() * np.sqrt(252) if len(daily_returns) > 1 else 0
                
                # Sharpe ratio (assuming risk-free rate of 0 for simplicity)
                self.stats['annualized_sharpe_ratio'] = annualized_return / annualized_volatility if annualized_volatility > 0 else 0
            else:
                self.stats['annualized_sharpe_ratio'] = 0
        else:
            self.stats['annualized_sharpe_ratio'] = 0
            
        return self.stats
    
    def generate_report(self, output_path=None, title="Trade Statistics Report"):
        """
        Generate a report of trade statistics.
        
        Args:
            output_path (str, optional): Path to save the report. 
                If None, saves to 'trade_stats_report.html' in current directory.
            title (str, optional): Title for the report.
                
        Returns:
            str: Path to the generated report
        """
        if not self.stats:
            raise ValueError("Statistics not calculated. Call calculate_stats() first.")
        
        if output_path is None:
            output_path = "trade_stats_report.html"
        
        # Ensure directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        try:
            # Create a simple HTML report
            html_content = f"""
            <html>
            <head>
                <title>{title}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #333366; }}
                    table {{ border-collapse: collapse; width: 80%; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                </style>
            </head>
            <body>
                <h1>{title}</h1>
                <h2>Trade Statistics Summary</h2>
                <table>
                    <tr><th>Metric</th><th>Value</th></tr>
            """
            
            # Add all statistics to the table
            for key, value in self.stats.items():
                if isinstance(value, float):
                    formatted_value = f"{value:.4f}"
                else:
                    formatted_value = str(value)
                html_content += f"<tr><td>{key.replace('_', ' ').title()}</td><td>{formatted_value}</td></tr>\n"
            
            html_content += """
                </table>
            </body>
            </html>
            """
            
            # Write to file
            with open(output_path, 'w') as f:
                f.write(html_content)
                
            logger.info(f"Trade statistics report generated successfully at {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Failed to generate trade statistics report: {str(e)}", exc_info=True)
            return None 