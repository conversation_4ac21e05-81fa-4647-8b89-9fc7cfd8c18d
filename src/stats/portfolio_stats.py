import pandas as pd
import quantstats as qs
import logging
from pathlib import Path
import os

logger = logging.getLogger(__name__)

class PortfolioStats:
    """
    A class to generate portfolio statistics and reports using quantstats.
    Takes a dataframe with portfolio value timeseries and generates various
    performance metrics and visualizations.
    """
    
    def __init__(self, portfolio_values=None, benchmark_ticker="SPY"):
        """
        Initialize the PortfolioStats object.
        
        Args:
            portfolio_values (pd.DataFrame, optional): DataFrame with portfolio values over time.
                Should have a datetime index and a column with portfolio values.
            benchmark_ticker (str, optional): Ticker symbol for benchmark. Defaults to "SPY".
        """
        self.portfolio_values = portfolio_values
        self.benchmark_ticker = benchmark_ticker
        self.returns = None
        
    def set_portfolio_values(self, portfolio_values):
        """
        Set the portfolio values dataframe.
        
        Args:
            portfolio_values (pd.DataFrame): DataFrame with portfolio values over time.
                Should have a datetime index and a column with portfolio values.
        """
        self.portfolio_values = portfolio_values
        
    def calculate_returns(self, column_name=None):
        """
        Calculate returns from portfolio values.
        
        Args:
            column_name (str, optional): Name of the column containing portfolio values.
                If None, uses the first column.
        
        Returns:
            pd.Series: Daily returns series
        """
        if self.portfolio_values is None:
            raise ValueError("Portfolio values not set. Use set_portfolio_values() first.")
        
        # If column name not specified, use the first column
        if column_name is None:
            if isinstance(self.portfolio_values, pd.Series):
                values = self.portfolio_values
            else:
                values = self.portfolio_values.iloc[:, 0]
        else:
            values = self.portfolio_values[column_name]
        
        daily_values = values.resample('B').last().ffill()
        # Calculate returns and ensure proper datetime frequency
        self.returns = daily_values.pct_change().dropna()
        self.returns = self.returns.asfreq('B')  # Explicitly set business day frequency
        return self.returns
    
    def generate_html_report(self, output_path=None, title="Portfolio Performance Report"):
        """
        Generate an HTML report using quantstats.
        
        Args:
            output_path (str, optional): Path to save the HTML report. 
                If None, saves to 'portfolio_report.html' in current directory.
            title (str, optional): Title for the report.
                
        Returns:
            str: Path to the generated HTML report
        """
        if self.returns is None:
            raise ValueError("Returns not calculated. Call calculate_returns() first.")
        
        if output_path is None:
            output_path = "portfolio_report.html"
        
        # Ensure directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
                    
        # Generate report
        try:
            # Ensure returns is a proper pandas Series, not a resampler object
            daily_returns = pd.Series(self.returns.values, index=self.returns.index)
            
            qs.reports.html(
                daily_returns, 
                benchmark=self.benchmark_ticker,
                title=title,
                output=output_path
            )
            logger.info(f"Portfolio report generated successfully at {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Failed to generate portfolio report: {str(e)}", exc_info=True)
            return None
    