"""
Backtest script for bullish tickers extracted from Substack. 

For each bullish ticker event:
   - The backend extracts the 5-minute aggregated market prices within a specified window.
   - Then, calculates the percent change from the first valid trading price (adjusting for trading sessions)
     at multiple target intervals.
   - Two scenarios are supported:
         * "full": Look for price changes over the next 24 hours – using all allowed trading sessions 
                   (pre‑market: 4–9:30, regular: 9:30–16, after‑hours: 16–20 EST).
         * "rth":  Only process ticker events that occur during regular trading hours (9:30–16) and limit 
                   analysis to that session.
   - Finally, the script averages the percent changes across many events, computes the median,
     and plots a grouped bar chart as well as smooth ticker curves.
     
Timezone awareness is handled in EST.
"""

from datetime import datetime, timedelta, time
from zoneinfo import ZoneInfo
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import dotenv
from polygon import RESTClient
import os

# Import our interfaces and ticker source.
from marketdata import IMarketData, PolygonMarketData
from tickers.substack_ticker_source import SubstackTickerSource

# -----------------------------------------------------------------------------
# Helper functions for trading times and filtering data
# -----------------------------------------------------------------------------
def is_trading_time(dt: datetime) -> bool:
    """
    Check if a datetime (dt) falls within allowed trading hours in EST.
    
    Tradable sessions in EST:
      - Pre-market: 4:00 AM to 9:30 AM
      - Regular hours: 9:30 AM to 4:00 PM
      - After-hours: 4:00 PM to 8:00 PM
    """
    dt_est = dt.astimezone(ZoneInfo("America/New_York"))
    t = dt_est.time()
    pre_market = time(4, 0) <= t < time(9, 30)
    regular    = time(9, 30) <= t < time(16, 0)
    after_hours= time(16, 0) <= t < time(20, 0)
    return pre_market or regular or after_hours

def is_regular_hours(dt: datetime) -> bool:
    """
    Check if a datetime (dt) falls within regular trading hours (9:30–16:00 EST).
    """
    dt_est = dt.astimezone(ZoneInfo("America/New_York"))
    return time(9,30) <= dt_est.time() < time(16,0)

def get_valid_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Filters the market data DataFrame to include only rows that occur 
    during allowed trading hours.
    """
    filtered_df = df[df.index.to_series().apply(is_trading_time)]
    return filtered_df

def get_valid_rth_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Filters the market data DataFrame to include only rows that occur 
    during regular trading hours (RTH).
    """
    filtered_df = df[df.index.to_series().apply(is_regular_hours)]
    return filtered_df

def get_price_at_or_after(df: pd.DataFrame, target_time: datetime) -> float:
    """
    Returns the 'close' price at the first timestamp in df (assumed sorted) 
    that is at or after the target_time. Returns None if not found.
    """
    df_after = df[df.index >= target_time]
    if df_after.empty:
        return None
    return df_after.iloc[0]['close']

def get_trading_window(created_at: datetime, scenario: str = "full"):
    """
    Returns a (start, end) tuple for the market data window in EST timezone based on the scenario.
    
    For the "rth" scenario:
       - Only events during regular trading hours (9:30 to 16:00 EST) are processed.
       - The window start is the time of the event (created_at) converted to EST.
       - The window end is fixed at 16:00 EST on the same day.
       
    For the "full" scenario:
       - The window starts at created_at (converted to EST) and spans the following 24 hours.
       
    Note:
       The input created_at is expected to be in UTC, but it might be tz-naive. We localize it to UTC.
    """
    # Skip if created_at is None or NaT.
    if created_at is None or pd.isna(created_at):
        return None

    # Ensure created_at is tz-aware.
    if created_at.tzinfo is None:
        created_at = created_at.replace(tzinfo=ZoneInfo("UTC"))
        
    dt_est = created_at.astimezone(ZoneInfo("America/New_York"))
    
    if scenario.lower() == "rth":
        if not is_regular_hours(dt_est):
            return None  # Skip events outside of regular trading hours.
        window_end_est = datetime(dt_est.year, dt_est.month, dt_est.day, 16, 0, tzinfo=ZoneInfo("America/New_York"))
        return (dt_est, window_end_est)
    else:
        return (dt_est, dt_est + timedelta(hours=24))

# -----------------------------------------------------------------------------
# Helper function to process backtest logic and generate plots
# -----------------------------------------------------------------------------
def generate_backtest_plots(df_tickers: pd.DataFrame, market_data: IMarketData, buckets: dict, scenario: str = "full") -> None:
    """
    Process ticker events for a given scenario and generate backtest plots.

    Parameters:
      df_tickers: DataFrame containing ticker events (with at least 'ticker', 'created_at' and 'sentiment').
      market_data: Market data provider (e.g. instance of PolygonMarketData).
      buckets: Dictionary of {label: timedelta} for percent change intervals.
      scenario: 'full' for a full 24‑hr analysis (all allowed trading sessions) or 'rth' 
                for regular trading hours only.
    """
    # Dictionary to store the percentage changes for each interval bucket.
    pct_changes = { label: [] for label in buckets }
    
    # List to store the smooth curves for each ticker event.
    # Each element is a tuple: (ticker, new_x, new_y)
    ticker_curves = []
    processed_event_count = 0

    for _, row in df_tickers.iterrows():
        ticker = row['ticker']
        created_at_utc = row['created_at']
        
        # Determine the market window based on the scenario.
        window = get_trading_window(created_at_utc, scenario)
        if window is None:
            continue  # skip events that do not meet the scenario criteria (e.g. non-RTH events in RTH mode)
        window_start, window_end = window

        # Retrieve 1-minute market data for this ticker within the relevant window.
        df_market = market_data.gather_historical_data(ticker, window_start, window_end)
        # Filter the data based on scenario.
        if scenario.lower() == "rth":
            df_market = get_valid_rth_data(df_market)
        else:
            df_market = get_valid_data(df_market)
            
        if df_market.empty:
            continue
        
        processed_event_count += 1

        # Use the first valid bar as the effective start.
        start_time_market = df_market.index[0]
        start_price = df_market.iloc[0]['close']
        
        # For each bucket, calculate the percentage change if the price is available.
        for label, delta in buckets.items():
            target_time = start_time_market + delta
            price = get_price_at_or_after(df_market, target_time)
            if price is not None:
                pct = (price - start_price) / start_price * 100
                pct_changes[label].append(pct)
                
        # Build the smooth curve for this ticker event.
        x_vals = np.array([(dt - start_time_market).total_seconds()/3600 for dt in df_market.index])
        y_vals = np.array([(price - start_price)/start_price*100 for price in df_market['close']])
        
        # Create a uniform grid from 0 until the window_end (in hours).
        hours_total = (window_end - start_time_market).total_seconds() / 3600
        new_x = np.linspace(0, hours_total, 200)
        if len(x_vals) > 1:
            new_y = np.interp(new_x, x_vals, y_vals)
        else:
            new_y = np.full_like(new_x, y_vals[0])
        
        ticker_curves.append((ticker, new_x, new_y))
    
    # Compute average and median changes for each bucket.
    avg_changes = { label: (np.mean(changes) if changes else None) for label, changes in pct_changes.items() }
    median_changes = { label: (np.median(changes) if changes else None) for label, changes in pct_changes.items() }
    
    # Filter out buckets without data (preserve the defined bucket order).
    bucket_labels, bucket_avg_values, bucket_median_values = [], [], []
    for label in buckets:
        if avg_changes[label] is not None:
            bucket_labels.append(label)
            bucket_avg_values.append(avg_changes[label])
            bucket_median_values.append(median_changes[label])
    
    # Plot the grouped bar chart for average and median percentage changes.
    x = np.arange(len(bucket_labels))
    width = 0.35  # width of each bar

    plt.figure(figsize=(10,6))
    plt.bar(x - width/2, bucket_avg_values, width, label='Average', color='skyblue')
    plt.bar(x + width/2, bucket_median_values, width, label='Median', color='salmon')
    plt.xlabel("Bucket")
    plt.ylabel("Percentage Change (%)")
    plt.title(f"Backtest Results ({scenario.upper()}): Price Change at Buckets (Analyzed {processed_event_count} tickers)")
    plt.xticks(x, bucket_labels)
    plt.legend()
    plt.savefig(f"backtest_results_{scenario.lower()}.png")
    plt.close()
    
    # Plot the smooth curves for each ticker event.
    if ticker_curves:
        plt.figure(figsize=(10,6))
        for ticker, new_x, new_y in ticker_curves:
            plt.plot(new_x, new_y, alpha=0.5)
        plt.xlabel(f"Time (hours) from effective start ({scenario.upper()})")
        plt.ylabel("Percentage Change from Start (%)")
        plt.title(f"Smooth Percentage Change Curves for Each Ticker Event ({scenario.upper()})")
        # Set the x-limit to the maximum time span encountered.
        plt.xlim(0, max(curve[1][-1] for curve in ticker_curves))
        plt.grid(True)
        plt.savefig(f"backtest_smooth_curves_{scenario.lower()}.png")
        plt.close()
        
        # -- Compute a composite (average, median, max, min) curve over all events --
        # Determine the shortest duration in hours among all events, to define a common x-axis.
        min_hours = min(curve[1][-1] for curve in ticker_curves)
        composite_x = np.linspace(0, min_hours, 200)
        composite_y_list = []
        for ticker, new_x, new_y in ticker_curves:
            # Interpolate each curve on the common grid.
            interp_y = np.interp(composite_x, new_x, new_y)
            composite_y_list.append(interp_y)
        composite_y = np.mean(composite_y_list, axis=0)       # Composite Average Curve
        composite_median = np.median(composite_y_list, axis=0)   # Composite Median Curve
        composite_max = np.max(composite_y_list, axis=0)         # Composite Maximum Curve
        composite_min = np.min(composite_y_list, axis=0)         # Composite Minimum Curve
        
        # Plot all composite curves on the same figure.
        plt.figure(figsize=(10,6))
        plt.plot(composite_x, composite_y, color="green", lw=2, label="Composite Average")
        plt.plot(composite_x, composite_median, color="blue", lw=2, label="Composite Median")
        plt.plot(composite_x, composite_max, color="red", lw=2, linestyle="--", label="Composite Max")
        plt.plot(composite_x, composite_min, color="orange", lw=2, linestyle="--", label="Composite Min")
        plt.xlabel(f"Time (hours) from effective start ({scenario.upper()})")
        plt.ylabel("Percentage Change from Start (%)")
        plt.title(f"Composite Profile of Percentage Change ({scenario.upper()})")
        plt.legend()
        plt.grid(True)
        plt.savefig(f"composite_profile_{scenario.lower()}.png")
        plt.close()

# -----------------------------------------------------------------------------
# Main backtest implementation.
# -----------------------------------------------------------------------------
def run_backtest():
    dotenv.load_dotenv()

    # Define the date range for fetching tickers (use similar settings as in SubstackTickerSource usage)
    end = datetime.fromisoformat("2025-02-01").replace(tzinfo=ZoneInfo("America/New_York"))
    start = datetime.fromisoformat("2025-01-01").replace(tzinfo=ZoneInfo("America/New_York"))
    
    # Instantiate the Substack ticker source.
    ticker_source = SubstackTickerSource(
        chat_id="836125", 
        rate_limit=0
    )
    
    # Get ticker events and filter only those with Bullish sentiment.
    df_tickers = ticker_source.get_tickers(start, end)
    df_bull = df_tickers[df_tickers['sentiment'] == 'Bullish']
    
    client = RESTClient(os.getenv("POLYGON_API_KEY"))
    # Instantiate the market data provider.
    market_data = PolygonMarketData(client)
    
    # Define bucket intervals as a dictionary.
    buckets = {
        "5min": timedelta(minutes=5),
        "30min": timedelta(minutes=30),
        "1hr": timedelta(hours=1),
        "2hr": timedelta(hours=2),
        "4hr": timedelta(hours=4),
        "6hr": timedelta(hours=6),
        "7hr": timedelta(hours=7),
        "8hr": timedelta(hours=8)
    }
    
    # Generate plots for the full (all trading sessions) scenario.
    generate_backtest_plots(df_bull, market_data, buckets, scenario="full")
    
    # Generate plots for the RTH only scenario.
    generate_backtest_plots(df_bull, market_data, buckets, scenario="rth")

if __name__ == "__main__":
    run_backtest() 