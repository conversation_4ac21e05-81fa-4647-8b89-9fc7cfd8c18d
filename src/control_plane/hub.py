import logging
from log_utils import configure_basic_logging
import json
import asyncio
import time
from collections import defaultdict, deque
from typing import Dict, Optional, List, Any, Deque, Tuple

from fastapi import FastAP<PERSON>, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi_websocket_rpc import <PERSON><PERSON><PERSON><PERSON>CEndpoint
from fastapi_websocket_rpc.rpc_methods import RpcMethodsBase
from fastapi_websocket_rpc.rpc_channel import RpcChannel


logger = logging.getLogger(__name__)

configure_basic_logging(level=logging.INFO)

app = FastAPI()

# Templates for in-hub lightweight UI (HTMX + Tailwind)
templates = Jinja2Templates(directory="templates")

# registry of connected agents by node_id -> HubMethods instance (per connection)
AGENTS: Dict[str, "HubMethods"] = {}

# --- Basic health tracking (broker-only for now) ---
# Store (timestamp, overall_status) samples per agent, trimmed to last 60 minutes
HEALTH_HISTORY: Dict[str, Deque[Tuple[float, str]]] = defaultdict(lambda: deque(maxlen=1000))
HEALTH_POLL_TASK: Optional[asyncio.Task] = None
HEALTH_WINDOW_SECONDS: int = 60 * 60  # keep 60 minutes
HEALTH_POLL_INTERVAL_SECONDS: float = 60.0  # cadence for polling agents


class HubMethods(RpcMethodsBase):
    """RPC surface the agents will call when they connect."""
    def __init__(self):
        super().__init__()

    async def register(self, node_id: str):
        # Record the connection handler (this specific methods instance) for the node_id
        AGENTS[node_id] = self
        logger.info("Agent registered: node_id=%s", node_id)
        return "ok"

    async def ping(self):
        return "pong"

    # ----- controller-facing helpers to call a specific agent -----
    async def invoke_block_opens(self, node_id: str):
        agent_channel = get_agent(node_id)
        assert agent_channel is not None, f"Unknown agent {node_id}"
        result = await agent_channel.other.block_opens()
        _log_agent_response(method="block_opens", node_id=node_id, result=result)
        return result

    async def invoke_unblock_opens(self, node_id: str):
        agent_channel = get_agent(node_id)
        assert agent_channel is not None, f"Unknown agent {node_id}"
        result = await agent_channel.other.unblock_opens()
        _log_agent_response(method="unblock_opens", node_id=node_id, result=result)
        return result

    async def invoke_block_opens_for(self, node_id: str, symbol: str):
        agent_channel = get_agent(node_id)
        assert agent_channel is not None, f"Unknown agent {node_id}"
        result = await agent_channel.other.block_opens_for(symbol=symbol)
        _log_agent_response(method="block_opens_for", node_id=node_id, result=result)
        return result

    async def invoke_close_all_positions(self, node_id: str):
        agent_channel = get_agent(node_id)
        assert agent_channel is not None, f"Unknown agent {node_id}"
        result = await agent_channel.other.close_all_positions()
        _log_agent_response(method="close_all_positions", node_id=node_id, result=result)
        return result


# Create endpoint bound to our HubMethods; register route on FastAPI app
endpoint = WebsocketRPCEndpoint(HubMethods())
endpoint.register_route(app, "/ws")

def get_agent(node_id: str) -> Optional[RpcChannel]:
    methods = AGENTS.get(node_id)
    return methods.channel if methods is not None else None


def _safe_serialize(value: Any) -> str:
    try:
        return json.dumps(value, default=str)  # fall back to str for non-serializable types
    except Exception:
        try:
            return repr(value)
        except Exception:
            return "<unserializable>"


def _log_agent_response(method: str, node_id: str, result: Any) -> None:
    # Log the raw wrapper and, if present, the inner .result
    try:
        logger.info(
            "Hub received RPC raw: method=%s node_id=%s type=%s repr=%s",
            method,
            node_id,
            type(result).__name__,
            repr(result),
        )
    except Exception:
        pass
    inner = getattr(result, "result", result)
    payload = _safe_serialize(inner)
    # Truncate overly large logs to protect the hub
    if len(payload) > 5000:
        payload = payload[:5000] + "...<truncated>"
    logger.info("Agent RPC response: method=%s node_id=%s result=%s", method, node_id, payload)


def _pos_neg_text(value: Any) -> str:
    """Return text color classes (green/red) based on sign for robust CDN styling."""
    try:
        v = float(value)
    except Exception:
        return ""
    if v > 0:
        return "text-emerald-400"
    if v < 0:
        return "text-rose-400"
    return "text-base-content/70"


# ---------- Health poller utilities ----------
def _trim_health_history(node_id: str) -> None:
    cutoff = time.time() - HEALTH_WINDOW_SECONDS
    dq = HEALTH_HISTORY[node_id]
    while dq and dq[0][0] < cutoff:
        dq.popleft()


def _record_health_sample(node_id: str, status: str) -> None:
    dq = HEALTH_HISTORY[node_id]
    dq.append((time.time(), status))
    _trim_health_history(node_id)


def _status_severity(status: str) -> int:
    # lower is better for min() across a bucket
    mapping = {"ok": 0, "warn": 1, "unknown": 1, "down": 2}
    return mapping.get(status, 1)


async def _poll_once() -> None:
    # snapshot keys to avoid loop-time mutation issues
    node_ids = list(AGENTS.keys())
    if not node_ids:
        return
    async def poll_one(node_id: str):
        channel = get_agent(node_id)
        if channel is None:
            _record_health_sample(node_id, "down")
            return
        try:
            # Minimal broker-only snapshot; agent provides overall already
            result = await asyncio.wait_for(channel.other.get_health_snapshot(), timeout=1.5)
            payload = getattr(result, "result", result)
            status = str(payload.get("overall", "unknown")) if isinstance(payload, dict) else "unknown"
            if status not in ("ok", "warn", "down", "unknown"):
                status = "unknown"
            _record_health_sample(node_id, status)
        except Exception:
            _record_health_sample(node_id, "down")

    await asyncio.gather(*(poll_one(n) for n in node_ids), return_exceptions=True)


async def _health_poll_loop(stop_event: asyncio.Event) -> None:
    while not stop_event.is_set():
        try:
            await _poll_once()
        except Exception as exc:
            logger.warning("Health poll iteration error: %s", exc)
        try:
            await asyncio.wait_for(stop_event.wait(), timeout=HEALTH_POLL_INTERVAL_SECONDS)
        except asyncio.TimeoutError:
            continue
        else:
            break


_health_stop_event: asyncio.Event = asyncio.Event()


@app.on_event("startup")
async def _start_health_poller():
    global HEALTH_POLL_TASK
    _health_stop_event.clear()
    HEALTH_POLL_TASK = asyncio.create_task(_health_poll_loop(_health_stop_event))


@app.on_event("shutdown")
async def _stop_health_poller():
    if HEALTH_POLL_TASK is None:
        return
    try:
        _health_stop_event.set()
        await HEALTH_POLL_TASK
    except Exception:
        pass

# ---------- Discovery (read-only) endpoints ----------
@app.get("/agents")
async def list_agents() -> List[str]:
    return list(AGENTS.keys())


@app.get("/agents/{node_id}/positions")
async def list_positions(node_id: str):
    agent_channel = get_agent(node_id)
    assert agent_channel is not None, f"Unknown agent {node_id}"
    result = await agent_channel.other.list_positions()
    _log_agent_response(method="list_positions", node_id=node_id, result=result)
    # Return the underlying payload (typed via agent method annotation)
    return getattr(result, "result", result)


# (strategy concept removed)


# ---------- Minimal REST façade to invoke RPC on an agent ----------
@app.post("/control/{node_id}/block_opens")
async def rest_block_opens(node_id: str):
    agent_channel = get_agent(node_id)
    assert agent_channel is not None, f"Unknown agent {node_id}"
    result = await agent_channel.other.block_opens()
    _log_agent_response(method="block_opens", node_id=node_id, result=result)
    return result


@app.post("/control/{node_id}/close_all")
async def rest_close_all(node_id: str):
    agent_channel = get_agent(node_id)
    assert agent_channel is not None, f"Unknown agent {node_id}"
    result = await agent_channel.other.close_all_positions()
    _log_agent_response(method="close_all_positions", node_id=node_id, result=result)
    return result


@app.post("/control/{node_id}/unblock_opens")
async def rest_unblock_opens(node_id: str):
    agent_channel = get_agent(node_id)
    assert agent_channel is not None, f"Unknown agent {node_id}"
    result = await agent_channel.other.unblock_opens()
    _log_agent_response(method="unblock_opens", node_id=node_id, result=result)
    return result


@app.post("/control/{node_id}/block_opens_for")
async def rest_block_opens_for(node_id: str, symbol: str):
    agent_channel = get_agent(node_id)
    assert agent_channel is not None, f"Unknown agent {node_id}"
    result = await agent_channel.other.block_opens_for(symbol=symbol)
    _log_agent_response(method="block_opens_for", node_id=node_id, result=result)
    return result


@app.post("/control/{node_id}/unblock_opens_for")
async def rest_unblock_opens_for(node_id: str, symbol: str):
    agent_channel = get_agent(node_id)
    assert agent_channel is not None, f"Unknown agent {node_id}"
    result = await agent_channel.other.unblock_opens_for(symbol=symbol)
    _log_agent_response(method="unblock_opens_for", node_id=node_id, result=result)
    return result


@app.post("/control/{node_id}/close_positions_for")
async def rest_close_positions_for(node_id: str, symbol: str):
    agent_channel = get_agent(node_id)
    assert agent_channel is not None, f"Unknown agent {node_id}"
    result = await agent_channel.other.close_positions_for(symbol=symbol)
    _log_agent_response(method="close_positions_for", node_id=node_id, result=result)
    return result


# ---------- Lightweight HTMX + Tailwind UI ----------
@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    return templates.TemplateResponse("dashboard.html", {"request": request})


# HTML fragments to populate <select> options via HTMX
@app.get("/ui/agents/options", response_class=HTMLResponse)
async def ui_agents_options():
    options = [f'<option value="{node}">{node}</option>' for node in AGENTS.keys()]
    # include a placeholder option if none
    if not options:
        options = ['<option value="" disabled selected>No agents connected</option>']
    return HTMLResponse("\n".join(options))

# ---------- Health UI fragments ----------
def _uptime_percent(node_id: str, window_seconds: int) -> float:
    now = time.time()
    cutoff = now - window_seconds
    samples = HEALTH_HISTORY.get(node_id, ())
    if not samples:
        return 0.0
    ok = 0
    total = 0
    for ts, st in samples:
        if ts >= cutoff:
            total += 1
            if st == "ok":
                ok += 1
    return (ok / total * 100.0) if total else 0.0


def _status_badge(status: str) -> str:
    color = {
        "ok": "badge-success",
        "warn": "badge-warning",
        "unknown": "badge-neutral",
        "down": "badge-error",
    }.get(status, "badge-neutral")
    return f'<span class="badge {color} badge-sm">{status}</span>'


def _timeline_html(node_id: str, minutes: int = 15) -> str:
    # Build minute buckets; worst status per minute
    now = time.time()
    buckets: List[str] = []
    samples = list(HEALTH_HISTORY.get(node_id, ()))
    for i in range(minutes - 1, -1, -1):
        start = now - (i + 1) * 60
        end = now - i * 60
        worst = None
        worst_sev = -1
        for ts, st in samples:
            if start <= ts < end:
                sev = _status_severity(st)
                if sev > worst_sev:
                    worst_sev = sev
                    worst = st
        st = worst or "unknown"
        bg = {
            "ok": "bg-emerald-500",
            "warn": "bg-amber-400",
            "unknown": "bg-slate-400",
            "down": "bg-rose-500",
        }.get(st, "bg-slate-400")
        buckets.append(f'<div class="w-2 h-3 {bg}" title="{st}"></div>')
    return '<div class="flex gap-0.5 items-center">' + "".join(buckets) + "</div>"


@app.get("/ui/health/table", response_class=HTMLResponse)
async def ui_health_table():
    if not AGENTS:
        return HTMLResponse('<tr><td colspan="5" class="text-center text-sm py-6">No agents connected</td></tr>')
    rows: List[str] = []
    for node_id in AGENTS.keys():
        latest_status = HEALTH_HISTORY.get(node_id)
        latest = latest_status[-1][1] if latest_status else "unknown"
        p15 = _uptime_percent(node_id, 15 * 60)
        p60 = _uptime_percent(node_id, 60 * 60)
        rows.append(
            "".join([
                "<tr>",
                f"<td class=\"font-mono\">{node_id}</td>",
                f"<td>{_status_badge(latest)}</td>",
                f"<td class=\"text-right tabular-nums\">{p15:.1f}%</td>",
                f"<td class=\"text-right tabular-nums\">{p60:.1f}%</td>",
                f"<td>{_timeline_html(node_id, minutes=60)}</td>",
                "</tr>",
            ])
        )
    return HTMLResponse("\n".join(rows))

# UI actions that accept form posts and return an out-of-band toast
def _toast_html(message: str) -> str:
    return f'<div id="toast" class="fixed top-4 right-4 bg-emerald-600 text-white px-3 py-2 rounded shadow" hx-swap-oob="true">{message}</div>'


@app.post("/ui/control/block_opens", response_class=HTMLResponse)
async def ui_block_opens(node_id: str = Form(...)):
    await rest_block_opens(node_id=node_id)
    return HTMLResponse(_toast_html("Blocked opens"))


@app.post("/ui/control/unblock_opens", response_class=HTMLResponse)
async def ui_unblock_opens(node_id: str = Form(...)):
    await rest_unblock_opens(node_id=node_id)
    return HTMLResponse(_toast_html("Unblocked opens"))


@app.post("/ui/control/close_all", response_class=HTMLResponse)
async def ui_close_all(node_id: str = Form(...)):
    await rest_close_all(node_id=node_id)
    return HTMLResponse(_toast_html("Closed all positions"))


@app.post("/ui/control/block_opens_for", response_class=HTMLResponse)
async def ui_block_opens_for(node_id: str = Form(...), symbol: str = Form(...)):
    await rest_block_opens_for(node_id=node_id, symbol=symbol)
    return HTMLResponse(_toast_html(f"Blocked opens for {symbol}"))


@app.post("/ui/control/unblock_opens_for", response_class=HTMLResponse)
async def ui_unblock_opens_for(node_id: str = Form(...), symbol: str = Form(...)):
    await rest_unblock_opens_for(node_id=node_id, symbol=symbol)
    return HTMLResponse(_toast_html(f"Unblocked opens for {symbol}"))


@app.post("/ui/control/close_positions_for", response_class=HTMLResponse)
async def ui_close_positions_for(node_id: str = Form(...), symbol: str = Form(...)):
    await rest_close_positions_for(node_id=node_id, symbol=symbol)
    return HTMLResponse(_toast_html(f"Closed positions for {symbol}"))


@app.get("/ui/positions/table", response_class=HTMLResponse)
async def ui_positions_table():
    rows: List[str] = []
    if not AGENTS:
        return HTMLResponse('<tr><td colspan="9" class="text-center text-sm py-6">No agents connected</td></tr>')
    for node_id in AGENTS.keys():
        try:
            positions = await list_positions(node_id)
        except Exception:
            positions = []
        # Add a separator row for the node/strategy
        rows.append(
            f'<tr><td colspan="6" class="bg-base-200 text-base-content/80 text-xs md:text-sm font-semibold tracking-wide uppercase">{node_id}</td></tr>'
        )
        if not positions:
            rows.append('<tr><td colspan="6" class="text-sm text-gray-500">No open positions</td></tr>')
            continue
        for p in positions:
            # Robust extraction supporting dicts, objects, and tuples
            symbol = None
            quantity = None
            avg_cost = None
            current_price = None
            market_value = None
            realized = None
            unreal = None
            traded_val = None
            if isinstance(p, dict):
                symbol = p.get("symbol")
                quantity = p.get("quantity", p.get("qty"))
                avg_cost = p.get("average_cost", p.get("avg_cost"))
                current_price = p.get("current_price")
                market_value = p.get("market_value")
                realized = p.get("realized")
                unreal = p.get("unrealized")
                traded_val = p.get("total_traded_value")
            else:
                symbol = getattr(p, "symbol", None)
                # quantities / avg cost under common aliases
                quantity = getattr(p, "quantity", getattr(p, "qty", None))
                avg_cost = getattr(p, "average_cost", getattr(p, "avg_cost", None))
                current_price = getattr(p, "current_price", None)
                market_value = getattr(p, "market_value", None)
                realized = getattr(p, "realized", None)
                unreal = getattr(p, "unrealized", None)
                traded_val = getattr(p, "total_traded_value", None)
                # last resort: tuple/list shape [symbol, qty, ...]
                if symbol is None and isinstance(p, (list, tuple)) and len(p) > 0:
                    symbol = p[0]
                if quantity is None and isinstance(p, (list, tuple)) and len(p) > 1:
                    quantity = p[1]
            symbol = symbol or ""
            qty_txt = "-" if quantity is None else f"{quantity:,}"
            avg_cost_txt = "-" if avg_cost is None else f"{avg_cost:,.2f}"
            price_txt = "-" if current_price is None else f"{current_price:,.2f}"
            mkt_val_txt = "-" if market_value is None else f"{market_value:,.2f}"
            realized_txt = "-" if realized is None else f"{realized:,.2f}"
            unreal_txt = "-" if unreal is None else f"{unreal:,.2f}"
            traded_val_txt = "-" if traded_val is None else f"{traded_val:,.2f}"
            # Compact action buttons for mobile: icon-only, joined group, extra-small
            block_btn = (
                f"<form hx-post=\"/ui/control/block_opens_for\" hx-target=\"#toast\" hx-swap=\"outerHTML\" class=\"inline\" title=\"Block opens\">"
                f"<input type=\"hidden\" name=\"node_id\" value=\"{node_id}\"/>"
                f"<input type=\"hidden\" name=\"symbol\" value=\"{symbol}\"/>"
                f"<button type=\"submit\" class=\"btn btn-xs btn-ghost\">🚫</button>"
                f"</form>"
            )
            unblock_btn = (
                f"<form hx-post=\"/ui/control/unblock_opens_for\" hx-target=\"#toast\" hx-swap=\"outerHTML\" class=\"inline\" title=\"Unblock opens\">"
                f"<input type=\"hidden\" name=\"node_id\" value=\"{node_id}\"/>"
                f"<input type=\"hidden\" name=\"symbol\" value=\"{symbol}\"/>"
                f"<button type=\"submit\" class=\"btn btn-xs btn-ghost\">✅</button>"
                f"</form>"
            )
            close_btn = (
                f"<form hx-post=\"/ui/control/close_positions_for\" hx-target=\"#toast\" hx-swap=\"outerHTML\" class=\"inline\" title=\"Close positions\">"
                f"<input type=\"hidden\" name=\"node_id\" value=\"{node_id}\"/>"
                f"<input type=\"hidden\" name=\"symbol\" value=\"{symbol}\"/>"
                f"<button type=\"submit\" class=\"btn btn-xs btn-ghost\">❌</button>"
                f"</form>"
            )
            actions_html = f"<div class=\"join justify-end\"><div class=\"join-item\">{block_btn}</div><div class=\"join-item\">{unblock_btn}</div><div class=\"join-item\">{close_btn}</div></div>"
            # Build cells with better readability and tooltips for long text
            # Only render the requested columns: Symbol, Mkt, R, UnR, Price, Actions
            symbol_cell = (
                f'<td class="font-mono whitespace-nowrap truncate" title="{symbol}">{symbol}</td>'
            )
            mkt_cell = (
                f'<td class="text-right px-1"><span class="tabular-nums font-mono md:inline-block md:w-36">{mkt_val_txt}</span></td>'
            )
            realized_color = _pos_neg_text(realized)
            unreal_color = _pos_neg_text(unreal)
            realized_cell = (
                f'<td class="text-right px-1 {realized_color}"><span class="tabular-nums font-mono md:inline-block md:w-24">{realized_txt}</span></td>'
            )
            unreal_cell = (
                f'<td class="text-right px-1 {unreal_color}"><span class="tabular-nums font-mono md:inline-block md:w-24">{unreal_txt}</span></td>'
            )
            price_cell = (
                f'<td class="text-right px-1"><span class="tabular-nums font-mono md:inline-block md:w-28">{price_txt}</span></td>'
            )
            action_cell = f'<td class="text-right">{actions_html}</td>'

            rows.append(
                "<tr>" + symbol_cell + mkt_cell + realized_cell + unreal_cell + price_cell + action_cell + "</tr>"
            )
    return HTMLResponse("\n".join(rows))


