from __future__ import annotations

import os
import socket
import logging
from typing import Awai<PERSON>, Callable, Tu<PERSON>, Dict

from brokers.control_plane_broker import ControlPlaneBroker
from control_plane.agent import ControlAgent
from control_plane.health import StreamingMarketDataHealthTracker
from marketdata.istreamingmarketdata import IStreamingMarketData
from stats.stats_loggers import LiveStatsLogger

logger = logging.getLogger(__name__)


class HealthCheckedStreamingMarketData(IStreamingMarketData):
    """Wrapper that reports heartbeats to StreamingMarketDataHealthTracker on each bar yield."""

    def __init__(self, delegate: IStreamingMarketData, tracker: StreamingMarketDataHealthTracker):
        self._delegate = delegate
        self._tracker = tracker

    async def subscribe_to_bars(self, ticker, interval, start_time, lookback_window):
        # Make this method itself an async generator to match the expected interface.
        interval_seconds = float(interval.total_seconds())
        self._tracker.heartbeat(ticker=ticker, interval_seconds=interval_seconds)
        try:
            async for hist, latest in self._delegate.subscribe_to_bars(
                ticker, interval, start_time, lookback_window
            ):
                self._tracker.heartbeat(ticker=ticker, interval_seconds=interval_seconds)
                yield hist, latest
        finally:
            # Unregister on close/end
            self._tracker.unregister(ticker=ticker, interval_seconds=interval_seconds)


async def attach_control_plane(
    *,
    local_broker,
    strategy_name: str,
    strategy_params: Dict | None = None,
    trade_session: str = "rth",
    node_id: str | None = None,
    hub_url: str | None = None,
    streaming_market_data: IStreamingMarketData | None = None,
) -> Tuple[ControlPlaneBroker, Callable[[], Awaitable[None]], IStreamingMarketData | None]:
    """Attach Control-Plane services (broker wrapper, agent, stats logger) to a live runner.

    This helper performs the following steps:
    1. Wrap *local_broker* with :class:`~brokers.control_plane_broker.ControlPlaneBroker` to
       enforce Control-Plane trading policies.
    2. Instantiate and *auto-connect* a :class:`~control_plane.agent.ControlAgent` so the runner
       can be managed remotely by the Control-Plane hub.
    3. Start a :class:`~stats.stats_loggers.LiveStatsLogger` for end-of-day statistics.

    It returns a tuple consisting of the new *cp_broker* and an asynchronous *cleanup* coroutine
    that should be awaited during the runner shutdown phase to gracefully stop the agent and the
    stats logger.

    Parameters
    ----------
    local_broker
        The underlying broker already configured by the runner (e.g. ``LocalBroker``).
    strategy_name
        A short identifier of the strategy (used for *node_id* generation and stats tagging).
    strategy_params
        Optional dictionary of strategy parameters recorded by the stats logger.
    trade_session
        Market session passed to the stats logger (defaults to ``"rth"``).
    node_id
        Optional explicit Control-Plane node identifier. If *None* a default value of
        ``"{hostname}-{strategy_name}"`` is used.
    hub_url
        Optional Control-Plane hub websocket URL. If *None* the ``CONTROL_HUB_URL`` environment
        variable is honoured or the fallback ``"ws://127.0.0.1:8000/ws"`` is used.

    Returns
    -------
    cp_broker : ControlPlaneBroker
        Broker wrapper to be used by downstream components (e.g. ExecutionOrchestrator).
    cleanup : Callable[[], Awaitable[None]]
        Awaitable callable that stops the stats logger and disconnects the ControlAgent.
    """

    if strategy_params is None:
        strategy_params = {}

    # 1. Wrap the original broker to enforce Control-Plane trading policies.
    cp_broker = ControlPlaneBroker(local_broker)

    # 2. Configure ControlAgent (remote RPC interface).
    if hub_url is None:
        hub_url = os.environ.get("CONTROL_HUB_URL", "ws://127.0.0.1:8000/ws")
    if node_id is None:
        node_id = f"{strategy_name}"

    control_agent = ControlAgent(
        hub_url=hub_url,
        broker=cp_broker,
        node_id=node_id,
    )
    await control_agent.start_auto_connect()

    logger.info("Control-Plane agent started: hub=%s node_id=%s", hub_url, node_id)

    # 2b. If market_data provided, wrap with health tracker and attach to agent health
    wrapped_market_data: IStreamingMarketData | None = None
    if streaming_market_data is not None:
        tracker = StreamingMarketDataHealthTracker()
        # attach tracker into agent health monitor
        try:
            control_agent.health.attach_streaming_marketdata_tracker(tracker)
        except Exception:
            pass
        wrapped_market_data = HealthCheckedStreamingMarketData(delegate=streaming_market_data, tracker=tracker)

    # 3. Start statistics logger (uses *local_broker* to avoid double-wrap accounting).
    stats_logger = LiveStatsLogger(
        strategy_name=strategy_name,
        strategy_params=strategy_params,
        broker=local_broker,
        trade_session=trade_session,
    )
    await stats_logger.start()

    logger.info("Live stats logger started for strategy=%s", strategy_name)

    # Provide a cleanup coroutine for graceful shutdown.
    async def _cleanup() -> None:
        """Stop stats logger and disconnect ControlAgent."""
        try:
            await stats_logger.stop()
        except Exception as exc:
            logger.warning("Error while stopping stats logger: %s", exc)
        try:
            await control_agent.close()
        except Exception as exc:
            logger.warning("Error while closing ControlAgent: %s", exc)

    # Backward-compatible return: if no market_data provided, return (broker, cleanup)
    if wrapped_market_data is None:
        return cp_broker, _cleanup
    return cp_broker, _cleanup, wrapped_market_data
