import asyncio
import json
import logging
import time
from typing import Dict, Optional

from fastapi_websocket_rpc import RpcMethodsBase, WebSocketRpcClient

from brokers.control_plane_broker import ControlPlaneBroker
from control_plane.health import AgentHealthMonitor, ComponentHealth

logger = logging.getLogger(__name__)

class AgentMethods(RpcMethodsBase):
    """RPC surface exposed by the agent to the ControlPlaneHub."""

    def __init__(self, broker: ControlPlaneBroker, health_monitor: AgentHealthMonitor):
        super().__init__()
        self._broker = broker
        self._health = health_monitor

    async def ping(self):
        return "pong"

    async def halt_strategy(self):
        await self._broker.halt_strategy()
        return "ok"

    async def block_opens(self):
        await self._broker.block_opens()
        return "ok"

    async def unblock_opens(self):
        await self._broker.unblock_opens()
        return "ok"

    async def block_opens_for(self, symbol: str):
        await self._broker.block_opens_for(symbol)
        return "ok"

    async def unblock_opens_for(self, symbol: str):
        await self._broker.unblock_opens_for(symbol)
        return "ok"

    async def close_all_positions(self):
        await self._broker.close_all_positions()
        return "ok"

    async def close_positions_for(self, symbol: str):
        await self._broker.close_positions_for(symbol)
        return "ok"

    async def get_stats(self):
        broker = self._broker
        df = await broker.get_trades_dataframe()
        # minimize payload
        return {
            "cash": broker.cash,
            "initial_capital": broker.initial_capital,
            "num_closed_trades": 0 if df is None or getattr(df, "empty", False) else len(df),
        }

    async def get_health_snapshot(self) -> dict:
        """Return a minimal health snapshot (broker + marketdata + logs)."""
        broker_t0 = time.perf_counter()
        broker_ok = False
        broker_detail = None
        try:
            broker_ok = await asyncio.wait_for(self._broker.check_connection(), timeout=1.0)
        except Exception as e:
            broker_detail = f"{type(e).__name__}: {e}"
        broker_latency_ms = (time.perf_counter() - broker_t0) * 1000.0

        logs = self._health.summarize_logs()
        md = self._health.summarize_streaming_marketdata()
        overall = self._health.derive_overall(
            broker=ComponentHealth(ok=broker_ok, status="ok" if broker_ok else "down", latency_ms=round(broker_latency_ms, 1), detail=broker_detail),
            logs_recent_errors=logs.get("recent_errors", 0),
            marketdata=md,
        )
        return {
            "ts": time.time(),
            "overall": overall,
            "broker": {
                "ok": broker_ok,
                "status": "ok" if broker_ok else "down",
                "latency_ms": round(broker_latency_ms, 1),
                "detail": broker_detail,
            },
            "streaming_marketdata": None if md is None else {
                "ok": md.ok,
                "status": md.status,
                "detail": md.detail,
            },
            "logs": logs,
            "agent_ping_ms": None,
            "uptime_seconds": round(self._health.uptime_seconds, 1),
        }

    async def list_positions(self) -> list[dict]:
        """Forward positions from the broker without computing values.

        Only minimal normalization is applied to ensure JSON-serializable primitives.
        """
        def _plain(v):
            try:
                # numpy scalar support
                if hasattr(v, "item"):
                    return v.item()
            except Exception:
                pass
            # Keep ints/floats/None as-is
            if isinstance(v, (int, float)) or v is None:
                return v
            # Best-effort numeric cast; otherwise leave unchanged (let JSON handle or fail loudly)
            try:
                return float(v)
            except Exception:
                try:
                    return int(v)
                except Exception:
                    return v

        positions = await self._broker.list_positions()
        out = []
        for p in positions:
            out.append({
                "symbol": getattr(p, "symbol", ""),
                "quantity": _plain(getattr(p, "quantity", None)),
                "average_cost": _plain(getattr(p, "average_cost", getattr(p, "avg_cost", None))),
                "current_price": _plain(getattr(p, "current_price", None)),
                "market_value": _plain(getattr(p, "market_value", None)),
                "realized": _plain(getattr(p, "realized", None)),
                "unrealized": _plain(getattr(p, "unrealized", None)),
                "total_traded_value": _plain(getattr(p, "total_traded_value", None)),
            })
        try:
            logger.info("Agent list_positions returning payload: type=list len=%s body=%s", len(out), json.dumps(out))
        except Exception:
            logger.info("Agent list_positions returning payload (repr): %s", repr(out))
        return out


class ControlAgent:
    """Client that connects to the ControlPlaneHub and exposes AgentMethods."""

    def __init__(self, hub_url: str, broker: ControlPlaneBroker, node_id: str):
        self.hub_url = hub_url
        self.health = AgentHealthMonitor()
        self.methods = AgentMethods(broker, self.health)
        self.node_id = node_id
        # Pass only supported kwargs; include node_id in register RPC, not as connect kw
        self.client = WebSocketRpcClient(self.hub_url, self.methods)
        self._auto_task: Optional[asyncio.Task] = None
        self._stop_event: asyncio.Event = asyncio.Event()
        self._ping_interval_seconds: float = 5.0

    async def run_forever(self):
        await self.client.run()

    async def connect_and_register(self):
        """Connect to hub and register this node_id for discovery by the hub."""
        # Use context-enter to establish the websocket
        await self.client.__aenter__()
        # Inform hub of our node_id so it can route commands
        await self.client.other.register(node_id=self.node_id)

    async def _keepalive(self) -> None:
        """Periodic ping to detect disconnects; raises on failure."""
        while not self._stop_event.is_set():
            try:
                await self.client.other.ping()
            except Exception as exc:  # connection lost
                raise exc
            try:
                await asyncio.wait_for(self._stop_event.wait(), timeout=self._ping_interval_seconds)
            except asyncio.TimeoutError:
                continue
            else:
                break

    async def auto_connect_forever(self, min_backoff_seconds: float = 0.5, max_backoff_seconds: float = 10.0, ping_interval_seconds: float = 5.0) -> None:
        """Continuously ensure connection; re-register on reconnect with exponential backoff."""
        self._ping_interval_seconds = ping_interval_seconds
        backoff = min_backoff_seconds
        while not self._stop_event.is_set():
            try:
                await self.connect_and_register()
                backoff = min_backoff_seconds  # reset on success
                await self._keepalive()
            except Exception as exc:
                logger.warning("ControlAgent connection lost or failed: %s", exc)
            finally:
                # ensure socket is closed before attempting reconnect
                try:
                    await self.client.__aexit__(None, None, None)
                except Exception:
                    pass

            if self._stop_event.is_set():
                break

            await asyncio.sleep(backoff)
            backoff = min(backoff * 2.0, max_backoff_seconds)

    async def start_auto_connect(self, min_backoff_seconds: float = 0.5, max_backoff_seconds: float = 10.0, ping_interval_seconds: float = 5.0) -> None:
        """Start auto-connect loop in background."""
        if self._auto_task and not self._auto_task.done():
            return
        self._stop_event.clear()
        self._auto_task = asyncio.create_task(
            self.auto_connect_forever(
                min_backoff_seconds=min_backoff_seconds,
                max_backoff_seconds=max_backoff_seconds,
                ping_interval_seconds=ping_interval_seconds,
            )
        )

    async def stop_auto_connect(self) -> None:
        self._stop_event.set()
        if self._auto_task is not None:
            try:
                await self._auto_task
            finally:
                self._auto_task = None

    async def close(self):
        # Gracefully close the websocket connection
        await self.stop_auto_connect()
        await self.client.__aexit__(None, None, None)
    

