import logging
import threading
import time
from collections import deque
from dataclasses import dataclass
from typing import Deque, List, Optional, Tuple, Dict


@dataclass
class ComponentHealth:
    ok: bool
    status: str
    latency_ms: float | None = None
    detail: str | None = None


class LogMonitor:
    """Keep a bounded, time-windowed list of error/critical log events."""

    def __init__(self, window_seconds: int = 900, max_items: int = 500):
        self.window_seconds: int = window_seconds
        self.max_items: int = max_items
        self._events: Deque[Tuple[float, str, str, str]] = deque()
        self._lock = threading.Lock()

    def _trim(self) -> None:
        cutoff = time.time() - self.window_seconds
        while self._events and self._events[0][0] < cutoff:
            self._events.popleft()
        # hard cap as safety if window is silent but many errors happened quickly
        while len(self._events) > self.max_items:
            self._events.popleft()

    def record(self, logger_name: str, level_name: str, message: str) -> None:
        ts = time.time()
        msg = message if len(message) <= 300 else (message[:300] + "...<truncated>")
        with self._lock:
            self._events.append((ts, logger_name, level_name, msg))
            self._trim()

    def summarize(self, last_n_messages: int = 5) -> dict:
        with self._lock:
            self._trim()
            recent_errors = len(self._events)
            last_ts = self._events[-1][0] if self._events else None
            last_msgs = [f"{name}: {msg}" for _, name, _, msg in list(self._events)[-last_n_messages:]]
        return {
            "recent_errors": recent_errors,
            "last_error_ts": last_ts,
            "last_messages": last_msgs,
        }


class _LogHealthHandler(logging.Handler):
    def __init__(self, monitor: LogMonitor, ignored_loggers: tuple[str, ...] = ()):  # type: ignore
        super().__init__(level=logging.ERROR)
        self._monitor = monitor
        self._ignored = ignored_loggers

    def emit(self, record: logging.LogRecord) -> None:  # noqa: D401
        try:
            logger_name = record.name or "root"
            for ig in self._ignored:
                if logger_name.startswith(ig):
                    return
            msg = record.getMessage()
            self._monitor.record(logger_name=logger_name, level_name=record.levelname, message=msg)
        except Exception:
            # Never let logging health monitoring crash user code
            pass


class AgentHealthMonitor:
    """Aggregates process-wide health, currently focusing on log errors."""

    def __init__(self, window_seconds: int = 900, max_items: int = 500,
                 ignored_loggers: tuple[str, ...] = ("uvicorn", "httpx", "websockets", "asyncio")):
        self.start_ts: float = time.time()
        self.log_monitor = LogMonitor(window_seconds=window_seconds, max_items=max_items)
        self._handler = _LogHealthHandler(self.log_monitor, ignored_loggers=ignored_loggers)
        self._install_handler_once()
        self._streaming_md_tracker: Optional["StreamingMarketDataHealthTracker"] = None

    def _install_handler_once(self) -> None:
        root = logging.getLogger()
        # Prevent duplicate installation
        for h in root.handlers:
            if isinstance(h, _LogHealthHandler):
                return
        root.addHandler(self._handler)

    @property
    def uptime_seconds(self) -> float:
        return time.time() - self.start_ts

    async def summarize_strategy(self):  # placeholder for future strategy monitoring
        return ComponentHealth(ok=True, status="unknown")

    def summarize_logs(self) -> dict:
        return self.log_monitor.summarize()

    # ----- Market data health integration -----
    def attach_streaming_marketdata_tracker(self, tracker: "StreamingMarketDataHealthTracker") -> None:
        self._streaming_md_tracker = tracker

    def summarize_streaming_marketdata(self) -> Optional[ComponentHealth]:
        if self._streaming_md_tracker is None:
            return None
        return self._streaming_md_tracker.summarize()

    @staticmethod
    def derive_overall(broker: ComponentHealth, logs_recent_errors: int, marketdata: Optional[ComponentHealth] = None) -> str:
        if not broker.ok:
            return "down"
        # If marketdata is provided and down, treat as warn for now (until defined strictly)
        if marketdata is not None and not marketdata.ok:
            return "warn"
        if logs_recent_errors > 0:
            return "warn"
        return "ok"



# ------------------ Market Data Health ------------------
class StreamingMarketDataHealthTracker:
    """Tracks last-seen bar timestamps for streaming subscriptions and evaluates health.

    A subscription is identified by a composite key of (ticker, interval_seconds).
    Health policy: a subscription is healthy if we have seen data within
    factor * interval_seconds. Overall market data is healthy if all active
    subscriptions are healthy. If there are no active subscriptions, status is "unknown".
    """

    def __init__(self, *, factor: float = 2.0):
        self._factor: float = max(1.0, float(factor))
        self._lock = threading.Lock()
        # key -> {"last_ts": float, "interval_s": float, "ticker": str}
        self._subs: Dict[str, Dict[str, float | str]] = {}

    @staticmethod
    def _make_key(ticker: str, interval_seconds: float) -> str:
        return f"{ticker}:{int(interval_seconds)}"

    def heartbeat(self, *, ticker: str, interval_seconds: float) -> None:
        now = time.time()
        key = self._make_key(ticker, interval_seconds)
        with self._lock:
            self._subs[key] = {"last_ts": now, "interval_s": float(interval_seconds), "ticker": ticker}

    def unregister(self, *, ticker: str, interval_seconds: float) -> None:
        key = self._make_key(ticker, interval_seconds)
        with self._lock:
            self._subs.pop(key, None)

    def summarize(self) -> ComponentHealth:
        now = time.time()
        with self._lock:
            if not self._subs:
                return ComponentHealth(ok=True, status="unknown", latency_ms=None, detail="no active subscriptions")

            worst_over: float = 0.0
            worst_key: Optional[str] = None
            unhealthy = False
            for key, meta in self._subs.items():
                last_ts = float(meta["last_ts"])  # type: ignore[index]
                interval_s = float(meta["interval_s"])  # type: ignore[index]
                elapsed = max(0.0, now - last_ts)
                threshold = self._factor * interval_s
                over = (elapsed - threshold)
                if over > 0 and over > worst_over:
                    worst_over = over
                    worst_key = key
                    unhealthy = True

            if unhealthy:
                detail = f"stale subscription: {worst_key} over_by={worst_over:.1f}s"
                return ComponentHealth(ok=False, status="down", latency_ms=None, detail=detail)

            # All subs within threshold
            return ComponentHealth(ok=True, status="ok", latency_ms=None, detail=None)

