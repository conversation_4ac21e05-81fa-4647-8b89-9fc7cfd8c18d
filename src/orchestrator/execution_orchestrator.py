import asyncio
import logging
from typing import Dict, List, Optional, Any
from collections.abc import Callable

from datetime import datetime, timedelta
import pandas as pd
import pytz

from marketdata.istreamingmarketdata import IStreamingMarketData
from tickers.iticker_discover import ITickerDiscover
from trading_framework.core import Strategy, BarSlice
from trading_framework.execution_feedback import (
    ExecutionReport,
    ExecOutcome,
)
from brokers.ibroker import IBroker, OrderParams, OrderParamsBuilder
from brokers.ibroker_extended import BracketOrderParams, TrailingStopOrderParams
from position_manager import PositionManager
from strategies.trade_signals import TradeSignal, SignalType, TradeType
from collections import deque

logger = logging.getLogger(__name__)

class ExecutionOrchestrator:
    """
    Orchestrates the execution flow between market data, ticker discover, and strategy components.
    """
    
    def __init__(
        self,
        market_data: IStreamingMarketData,
        ticker_discover: ITickerDiscover,
        strategy_factory: Callable[[], Strategy],
        broker: IBroker,
        position_manager: PositionManager
    ):
        """
        Initialize the execution orchestrator.
        
        Args:
            market_data: Market data provider
            ticker_discover: Ticker discovery service
            strategy: Trading strategy implementation
            broker: Broker implementation for executing trades
            position_manager: Position manager for tracking and sizing positions
        """
        self.market_data = market_data
        self.ticker_discover = ticker_discover
        self.strategy_factory  = strategy_factory
        self.broker = broker
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self._tasks = set()  # Track all running tasks
        self.position_manager = position_manager
        
    async def execute(self):
        """
        Execute the trading strategy by discovering tickers and processing them.
        """
        # Hint interval for strategies
        self.interval_hint = timedelta(minutes=1)  # Default interval hint
        
        # 2. Use the ITickerDiscover as a generator that yields tickers
        try:
            # Use the discover method which is now an async generator
            async for ticker_df in self.ticker_discover.discover():
                # Process each ticker in the current batch
                for _, row in ticker_df.iterrows():
                    ticker = row['ticker']
                    ticker_timestamp = row['timestamp']  # Get timestamp for this specific ticker
                    # Extract metadata from the row if it exists
                    metadata = row.get('metadata', {})
                    
                    if ticker not in self.active_tasks or self.active_tasks[ticker].done():
                        task = asyncio.create_task(
                            # Pass interval_hint instead of pre-calculated interval/lookback
                            self._process_ticker(ticker, self.interval_hint, ticker_timestamp, metadata)
                        )
                        self.active_tasks[ticker] = task
                        self._tasks.add(task)
                        task.add_done_callback(self._tasks.discard)  # Remove task when done
                        logger.info("Started processing ticker: %s", ticker)
            
            # The generator has exited naturally
            logger.info("Ticker discovery complete - no more tickers to process")
        
        except Exception as e:
            logger.error("Error in execution process: %s", e, exc_info=True)
            await self.stop()

                    
    async def _process_ticker(self, ticker: str, interval_hint: timedelta, timestamp: datetime, metadata: Dict[str, Any]):
        """
        Process a single ticker by subscribing to market data and generating signals.
        Exits as soon as an EXIT_STRATEGY signal is emitted.
        
        Args:
            ticker: The ticker symbol to process
            interval_hint: The time interval for bars
            timestamp: timestamp indicating when the ticker was discovered
            metadata: Optional dictionary containing additional context information
        """
        logger.info("Processing ticker %s with interval %s", ticker, interval_hint)
        
        market_data_subscription = None
        try:
            strategy = self.strategy_factory()
            strategy.position_manager = self.position_manager
            bar_interval, lookback_window = strategy.on_start(
                ticker,
                interval_hint, # TODO: Remove internal hint, its not really used anywhere
                metadata,
            )

            logger.info("Strategy requested interval %s and lookback %s for ticker %s", bar_interval, lookback_window, ticker)
            
            # Subscribe to market data and feed it to the signal generator
            # TODO: Remove timestamp from the subscribe_to_bars, its not necessary at all
            market_data_subscription = self.market_data.subscribe_to_bars(
                ticker, bar_interval, timestamp, lookback_window
            )
            
            async for historical_bars, current_open_bar in market_data_subscription:
                # Send the new data to the generator and get a signal if one is generated
                # Use BarSlice
                bar_slice = (historical_bars, current_open_bar)
                initial_signals = strategy.on_bar(bar_slice)
                if not initial_signals:
                    continue
                
                # Kick off a queue of signals to process (including any follow-ups)
                queue = deque(initial_signals)
                exit_requested = False

                # Process everything in one flat loop
                while queue:
                    sig = queue.popleft()

                    if sig.signal == SignalType.EXIT_STRATEGY:
                        logger.info("EXIT_STRATEGY requested for %s", ticker)
                        exit_requested = True
                        continue

                    # execute normal signal
                    rpt = await self._execute_signal(ticker, sig, bar_slice)
                    if not rpt:
                        continue

                    # let strategy generate follow‑ups
                    followups = strategy.on_exec(rpt) or []
                    if followups:
                        queue.extend(followups)

                if exit_requested:
                    # once we see EXIT_STRATEGY, drop out of the async-for and finish up
                    break
        except Exception as e:
            logger.error("Error processing ticker %s: %s", ticker, e, exc_info=True)   
        finally:
            # Close the market data subscription if it exists
            if market_data_subscription is not None and hasattr(market_data_subscription, 'aclose'):
                try:
                    await market_data_subscription.aclose()
                except Exception as e:
                    logger.error("Error closing market data subscription for %s: %s", ticker, e)
            
            logger.info("Stopped processing ticker %s", ticker)
            
    async def _execute_signal(self, ticker: str, signal: TradeSignal, bars: BarSlice) -> Optional[ExecutionReport]:
        """
        Execute a trade signal for a specific ticker.
        
        This method intelligently routes signals based on type and broker capabilities.
        
        Args:
            ticker: The ticker symbol
            signal: The trade signal to execute
            bars: Historical price data
        """
        (_, latest) = bars
        current_time = latest.index[-1]
        try:
            # Utility to convert broker result -> ExecutionReport, handling None / rejection
            def _build_report(result: Optional[dict], *, qty: float) -> ExecutionReport:
                if not result:
                    return ExecutionReport(
                        order_id="",
                        signal=signal,
                        outcome=ExecOutcome.REJECTED,
                        filled_qty=0,
                        avg_price=0.0,
                        broker_msg="Broker returned no result",
                        timestamp=current_time,
                    )
                return ExecutionReport(
                    order_id=result.get("order_id", ""),
                    signal=signal,
                    outcome=ExecOutcome.FILLED,
                    filled_qty=qty,
                    avg_price=signal.price,
                    broker_msg=str(result),
                    timestamp=current_time,
                )

            # Skip EXIT_STRATEGY signals as they're handled at a higher level
            if signal.signal == SignalType.EXIT_STRATEGY:
                logger.info(f"Skipping execution of EXIT_STRATEGY signal for {ticker}")
                return None
            
            # Handle SHORT_LOCATE signal type
            if signal.signal == SignalType.SHORT_LOCATE:
                logger.info(f"Processing SHORT_LOCATE request for {ticker}, quantity: {signal.quantity}")
                
                # Create OrderParams using the builder
                params = (OrderParamsBuilder()
                    .with_symbol(signal.symbol)
                    .with_quantity(signal.quantity)
                    .with_expect_price(signal.price)
                    .with_position_intent("open")  # Short locates are for opening positions
                    .with_datetime(current_time)
                    .build())
                
                # Request the locate from the broker
                locate_response = await self.broker.pre_allocate_short_locate(params)
                
                # Create execution report based on locate response
                if locate_response.success:
                    logger.info(f"Successfully located {locate_response.located_quantity} shares of {ticker}: {locate_response.message}")
                    return ExecutionReport(
                        order_id = locate_response.locate_id or "",
                        signal = signal,
                        outcome = ExecOutcome.FILLED,
                        filled_qty = locate_response.located_quantity,
                        avg_price = signal.price,  # Expected price
                        broker_msg = locate_response.message,
                        timestamp = current_time,
                        # Add locate-specific info to the execution report
                        metadata = {
                            "locate_success": True,
                            "requested_quantity": locate_response.requested_quantity,
                            "located_quantity": locate_response.located_quantity,
                            "locate_id": locate_response.locate_id,
                        }
                    )
                else:
                    logger.warning(f"Failed to locate shares for {ticker}: {locate_response.message}")
                    return ExecutionReport(
                        order_id = locate_response.locate_id or "",
                        signal = signal,
                        outcome = ExecOutcome.REJECTED,
                        filled_qty = locate_response.located_quantity,  # May be partial or 0
                        avg_price = 0.0,
                        broker_msg = locate_response.message,
                        timestamp = current_time,
                        # Add locate-specific info to the execution report
                        metadata = {
                            "locate_success": False,
                            "requested_quantity": locate_response.requested_quantity,
                            "located_quantity": locate_response.located_quantity,
                            "locate_id": locate_response.locate_id,
                        }
                    )
            
            supports_complex = False
            
            # Check if broker supports complex orders
            if hasattr(self.broker, 'supports_complex_orders'):
                supports_complex = await self.broker.supports_complex_orders()
            
            # Handle different signal types
            if signal.signal == SignalType.BRACKET and supports_complex:
                params  = BracketOrderParams.from_trade_signal(signal, current_time)
                result  = await self.broker.bracket_order(params)
                logger.info(f"Executed bracket order for {ticker}: {result}")
                return ExecutionReport(
                    order_id   = result.get("order_id", ""),
                    signal     = signal,
                    outcome    = ExecOutcome.FILLED,
                    filled_qty = signal.quantity or 0,
                    avg_price  = signal.price,
                    broker_msg = str(result),
                    timestamp  = current_time,
                )
            
            elif signal.signal == SignalType.TRAILING_STOP and supports_complex:
                params  = TrailingStopOrderParams.from_trade_signal(signal, current_time)
                result  = await self.broker.trailing_stop_order(params)
                logger.info(f"Executed trailing stop order for {ticker}: {result}")
                return ExecutionReport(
                    order_id   = result.get("order_id", ""),
                    signal     = signal,
                    outcome    = ExecOutcome.FILLED,
                    filled_qty = signal.quantity or 0,
                    avg_price  = signal.price,
                    broker_msg = str(result),
                    timestamp  = current_time,
                )
            
            # For simple orders or when broker doesn't support complex orders
            elif signal.signal == SignalType.OPEN:
                quantity = signal.quantity
                
                # Create OrderParams using the builder
                params = (OrderParamsBuilder()
                    .with_symbol(signal.symbol)
                    .with_quantity(quantity)
                    .with_expect_price(signal.price)
                    .with_position_intent("open")
                    .with_datetime(current_time)
                    .build())
                
                # Execute the appropriate order
                if signal.trade_type == TradeType.BUY:
                    result = await self.broker.market_buy(params)
                else:  # SELL
                    result = await self.broker.market_sell(params)

                logger.info(f"Executed {signal.trade_type.name} order for {ticker}: {result}")
                return _build_report(result, qty=signal.quantity or 0)
            
            elif signal.signal == SignalType.CLOSE:
                quantity = signal.quantity
                
                # Create OrderParams using the builder
                params = (OrderParamsBuilder()
                    .with_symbol(signal.symbol)
                    .with_quantity(quantity)
                    .with_expect_price(signal.price)
                    .with_position_intent("close")
                    .with_datetime(current_time)
                    .build())
                
                # Execute the closing order
                if signal.trade_type == TradeType.BUY:
                    result = await self.broker.market_buy(params)
                else:  # SELL
                    result = await self.broker.market_sell(params)

                logger.info(f"Executed CLOSE {signal.trade_type.name} for {ticker}: {result}")
                return _build_report(result, qty=signal.quantity or 0)
            
            else:
                logger.warning(f"Unsupported signal type: {signal.signal} for {ticker}")
                return None
                
        except Exception as e:
            logger.error(f"Error executing signal for {ticker}: {e}", exc_info=True)
            # Return a failed execution report
            return ExecutionReport(
                order_id = "",
                signal = signal,
                outcome = ExecOutcome.ERROR,
                broker_msg = str(e),
                timestamp = datetime.now(pytz.utc) # Or get time from bars if possible
            )

    
    async def stop(self):
        """Stop the execution orchestrator and wait for all pending tasks to complete."""
        self._running = False
        
        if self._tasks:
            logger.info("Waiting for %d pending tasks to complete...", len(self._tasks))
            await asyncio.gather(*self._tasks)
            logger.info("All tasks completed.")