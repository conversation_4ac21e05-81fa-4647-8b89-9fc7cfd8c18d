from typing import Dict, List, Optional
import pandas as pd
from datetime import datetime

class PositionManager:
    def __init__(self, max_positions: int = 10, risk_per_position: float = 0.05, initial_capital: float = 100000, max_position_size: float = 4):
        self.max_positions = max_positions
        self.active_positions: Dict[str, Dict] = {}
        self.risk_per_position = risk_per_position
        self.initial_capital = initial_capital
        self.max_position_size = max_position_size 
        
    def can_take_position(self, timestamp: datetime) -> bool:
        """Check if we can take a new position given current exposure"""
        return len(self.active_positions) < self.max_positions

    def add_position(self, ticker: str, signal_data: Dict):
        """Add a new position if within limits"""
        if self.can_take_position(signal_data['entry_time']):
            self.active_positions[ticker] = signal_data
            return True
        return False

    def close_position(self, ticker: str):
        """Close an existing position"""
        if ticker in self.active_positions:
            del self.active_positions[ticker]

    def calculate_position_size(
        self,
        ticker: str,
        hist_data: pd.DataFrame,
        price: float,
        stop_loss: Optional[float] = None
    ) -> int:
        """
        Return the number of shares to buy based on capital-at-risk
        and the max-position-size constraint.
        """
        risk_amount   = self.initial_capital * self.risk_per_position
        max_shares    = int(self.max_position_size * self.initial_capital / price)

        # If we don't have a usable stop-loss, cap out at max_shares.
        if not stop_loss or stop_loss == price:
            return max_shares

        shares_risk_based = int(risk_amount / abs(price - stop_loss))
        return min(shares_risk_based, max_shares)

    def calculate_position_size_by_leverage(
        self,
        price: float,
        notional_multiplier: float = 1.0
    ) -> int:
        """
        Calculate position size based on leverage multiplier and price.
        
        Args:
            price: Current price of the asset
            notional_multiplier: Leverage factor to apply
        
        Returns:
            int: Number of shares to trade
        """
        position_value = self.initial_capital * notional_multiplier
        shares = int(position_value / price)
        return shares