from typing import Optional, AsyncGenerator, Set
import pandas as pd
from datetime import datetime, timedelta
import asyncio
from zoneinfo import ZoneInfo

from tickers.iticker_discover import ITickerDiscover
from tickers.tickersource import ITickerSource


class TickerSourceDiscoverAdapter(ITickerDiscover):
    """
    Adapter class that wraps an ITickerSource and makes it compatible with the ITickerDiscover interface.
    This allows synchronous ticker sources to be used in contexts requiring the async discovery interface.
    
    The adapter polls the ticker source at regular intervals and yields new tickers.
    All timestamps are in Eastern Standard Time (EST).
    """
    
    def __init__(self, ticker_source: ITickerSource, 
                 poll_interval: float = 60.0,
                 overlap_window: timedelta = timedelta(minutes=1)):
        """
        Initialize the adapter with a ticker source.
        
        Args:
            ticker_source: The ITickerSource implementation to adapt
            poll_interval: Time in seconds to wait between polls
            overlap_window: How far back to look for tickers in each poll
        """
        self.ticker_source = ticker_source
        self.poll_interval = poll_interval
        self.overlap_window = overlap_window
        self.seen_tickers: Set[str] = set()
        self.timezone = ZoneInfo("America/New_York")  # EST timezone
    
    async def discover(self, timestamp: Optional[datetime] = None) -> AsyncGenerator[pd.DataFrame, None]:
        """
        Discover tickers by periodically polling the underlying ITickerSource.
        
        Args:
            timestamp: Optional timestamp to start from. If None, uses current time in EST.
            
        Yields:
            pd.DataFrame containing the columns:
                - timestamp: The timestamp for the ticker in EST
                - ticker: The ticker symbol identified
        """
        last_poll_time = datetime.now(tz=self.timezone)
            
        while True:
            # Calculate the start time for this poll
            start_time = last_poll_time - self.overlap_window
            
            # Get tickers from the source
            tickers_df = self.ticker_source.get_tickers(start=start_time)
            tickers_df.rename(columns={"created_at": "timestamp"}, inplace=True)
            
            # Filter out tickers we've already seen
            if not tickers_df.empty:
                # Create a unique identifier for each ticker-timestamp combination
                tickers_df['unique_id'] = tickers_df['ticker'] + tickers_df['timestamp'].astype(str)
                
                # Filter to only new tickers
                new_tickers_mask = ~tickers_df['unique_id'].isin(self.seen_tickers)
                new_tickers_df = tickers_df[new_tickers_mask]
                
                # Update our set of seen tickers
                self.seen_tickers.update(tickers_df.loc[new_tickers_mask, 'unique_id'].tolist())
                
                # If we have new tickers, add a metadata column and yield them (without the unique_id column)
                if not new_tickers_df.empty:
                    new_tickers_df['metadata'] = new_tickers_df['timestamp'].apply(lambda ts: {"timestamp": ts})
                    yield new_tickers_df[['timestamp', 'ticker', 'metadata']]
            
            # Update the last poll time to current time in EST
            last_poll_time = datetime.now(tz=self.timezone)
            
            # Wait for the next poll
            await asyncio.sleep(self.poll_interval) 