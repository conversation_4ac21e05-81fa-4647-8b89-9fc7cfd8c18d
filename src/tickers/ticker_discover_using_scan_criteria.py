import pandas as pd
import logging
from typing import List, Set, Type, Dict, Any, Optional, AsyncGenerator
import asyncio
from datetime import datetime, timedelta, time
from abc import ABC, abstractmethod

# Import necessary classes
from marketdata import IMarketData, ThetaBulkOHLCData
from marketdata.polygon_disk_market_data import PolygonDiskMarketData
from tickers.iticker_discover import ITickerDiscover
from tickers.iscancriteria import IScanCriteria
from tools.clock import Clock  # Import the Clock class

logger = logging.getLogger(__name__)

class TickerDiscoverUsingScanCriteria(ITickerDiscover):
    """
    Discovery system for finding tickers that match specific criteria
    using historical and current day market data.
    """
    
    def __init__(self, disk_market_data: PolygonDiskMarketData, theta_bulk_ohlc: ThetaBulkOHLCData, criteria: IScanCriteria,
                 max_run_hours: Optional[float] = 1.0, refresh_interval: float = 60.0, clock: Optional[Clock] = None):
        """
        Initialize the ticker discovery system with a specific scan criteria.
        
        Args:
            disk_market_data: PolygonDiskMarketData instance for historical data
            theta_bulk_ohlc: ThetaBulkOHLCData instance for current day data
            criteria: The IScanCriteria to use for discovery
            max_run_hours: Optional number of hours to run after market open (RTH hours).
                           If None, discovery will run indefinitely until explicitly stopped.
            refresh_interval: Time in seconds between consecutive scans.
            clock: Optional Clock instance for time management, useful for replay mode
        """
        self.disk_market_data = disk_market_data
        self.theta_bulk_ohlc = theta_bulk_ohlc
        self.criteria = criteria
        self.discovered_tickers: Set[str] = set()
        self.max_run_hours = max_run_hours
        self.refresh_interval = refresh_interval
        self.clock = clock or Clock()
        
    def reset_discovered_tickers(self):
        """Reset the set of discovered tickers."""
        self.discovered_tickers = set()
        logger.info("Reset discovered tickers list")
    
    def _is_within_run_window(self, start_time: datetime, max_run_hours: Optional[float]) -> bool:
        """
        Check if we're still within the maximum running time window.
        
        Args:
            start_time: The market open time or start time for scanning
            max_run_hours: Maximum number of hours to run
            
        Returns:
            True if we should continue running, False if the max_run_hours has been exceeded
        """
        # If no max_run_hours is set, always keep running
        if max_run_hours is None:
            return True
        current_time = self.clock.now()
        end_time = start_time + timedelta(hours=max_run_hours)
        return current_time <= end_time
        
    async def discover(self, timestamp: Optional[datetime] = None) -> AsyncGenerator[pd.DataFrame, None]:
        """
        Discover tickers of interest using the configured scan criteria.
        Runs in a loop, checking for new tickers at the configured refresh interval.
        
        Args:
            timestamp: Optional timestamp to scan at. If None, uses current time.
            
        Yields:
            pd.DataFrame containing the newly discovered tickers with their timestamps
        """
        # Use provided timestamp or clock's current time as the start time
        start_timestamp = self.clock.now()
        
        # Set market open time to 9:30 AM EST
        market_open_date = start_timestamp.date()
        market_open_time = datetime.combine(market_open_date, time(9, 30), tzinfo=start_timestamp.tzinfo)
        
        logger.info(f"Starting continuous ticker discovery at {start_timestamp}")
        
        # Load historical data once at the beginning
        logger.info("Loading historical data from DiskMarketData...")
        historical_df = self.disk_market_data.get_preloaded_data()
        
        if historical_df.empty:
            logger.warning("No historical data available. Make sure to initialize DiskMarketData with preload_months > 0")
            return
            
        # Ensure consistent timezone handling for historical data
        if 'date' in historical_df.columns and pd.api.types.is_datetime64_any_dtype(historical_df['date']):
            historical_df['date'] = historical_df['date'].dt.tz_localize(None)
            
        # Run discovery loop until max_run_hours is reached; runs indefinitely if max_run_hours is None
        while self._is_within_run_window(market_open_time, self.max_run_hours):
            current_timestamp = self.clock.now()  # Use clock instead of datetime.now()
            
            # Use configured refresh interval for scans
            refresh_interval = self.refresh_interval
            
            logger.info(f"Scanning for new tickers at {current_timestamp} (refresh interval: {refresh_interval}s)")
            
            try:
                # Get updated current day data each iteration
                logger.info("Fetching current day data from BulkOHLCData...")
                current_day_df = await self.theta_bulk_ohlc.get_current_day_ohlc()
                
                # Process current day data timezone if it exists
                if current_day_df is not None and not current_day_df.empty and 'date' in current_day_df.columns:
                    if pd.api.types.is_datetime64_any_dtype(current_day_df['date']):
                        current_day_df['date'] = current_day_df['date'].dt.tz_localize(None)
                
                # Skip scanning if current day data is missing
                if current_day_df is None or current_day_df.empty:
                    logger.warning("Current day data not available, waiting before retry...")
                    await asyncio.sleep(refresh_interval)
                    continue
                
                # Apply scan criteria in a thread pool to not block the event loop
                logger.info(f"Applying scan criteria: {self.criteria.__class__.__name__}")
                matching_tickers = await asyncio.to_thread(self.criteria.scan, historical_df, current_day_df)
                
                # Filter out already discovered tickers
                new_tickers = [ticker for ticker in matching_tickers if ticker not in self.discovered_tickers]
                
                # Update discovered tickers set
                self.discovered_tickers.update(new_tickers)
                
                logger.info(f"Discovered {len(new_tickers)} new tickers that match criteria")
                
                # If new tickers were found, yield them
                if new_tickers:
                    # Create dataframe for the results
                    result_df = ITickerDiscover.init_df()
                    
                    # Add the newly discovered tickers
                    for ticker in new_tickers:
                        new_row = pd.DataFrame({
                            'timestamp': [current_timestamp],
                            'ticker': [ticker]
                        })
                        result_df = pd.concat([result_df, new_row], ignore_index=True)
                    
                    # Yield the batch of discovered tickers
                    yield result_df
                    
                # Wait for the appropriate refresh interval before next scan
                await asyncio.sleep(refresh_interval)
                    
            except Exception as e:
                import traceback
                traceback.print_exc()
                logger.error(f"Error during ticker discovery: {str(e)}")
                # Wait a bit before retrying
                await asyncio.sleep(refresh_interval)
                # Continue the loop rather than re-raising to maintain continuous operation
        
        logger.info(f"Ticker discovery stopped after reaching max run time of {self.max_run_hours} hours")