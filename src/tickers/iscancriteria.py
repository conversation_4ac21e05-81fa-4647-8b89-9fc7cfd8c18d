from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from datetime import datetime, timedelta

class IScanCriteria(ABC):
    """Interface for scan criteria to be applied to market data."""
    
    @abstractmethod
    def scan(self, historical_df: pd.DataFrame, current_day_df: Optional[pd.DataFrame] = None) -> List[str]:
        """
        Scan the provided data and return tickers that match the criteria.
        
        Args:
            historical_df: DataFrame with historical OHLCV data
            current_day_df: Optional DataFrame with current day's OHLCV data
            
        Returns:
            List of ticker symbols that match the criteria
        """
        pass