import logging
import pandas as pd
from datetime import datetime
from typing import Optional, AsyncGenerator
from get_tickers import get_smallcap_tickers
from tickers.iticker_discover import ITickerDiscover

logger = logging.getLogger(__name__)

class DynamicTickerDiscover(ITickerDiscover):
    """
    Dynamically discovers tickers based on market cap and price criteria.
    """
    
    def __init__(self, min_market_cap=None, max_market_cap=None, min_price=None, start_time: Optional[datetime] = None):
        """
        Initialize the dynamic ticker discoverer.
        
        Args:
            min_market_cap (float): Minimum market cap filter
            max_market_cap (float): Maximum market cap filter
            min_price (float): Minimum price filter
            start_time (Optional[datetime]): Optional start time to include in the response
        """
        self.min_market_cap = min_market_cap if min_market_cap is not None else 0
        self.max_market_cap = max_market_cap if max_market_cap is not None else float('inf')
        self.min_price = min_price
        self.start_time = start_time
        
    async def discover(self, timestamp: Optional[datetime] = None) -> AsyncGenerator[pd.<PERSON>Frame, None]:
        """
        Discover tickers based on the specified criteria.
        
        Args:
            timestamp: Optional datetime for discovery
            
        Yields:
            DataFrame with ticker information, then exits
        """
        logger.info(f"Dynamically discovering tickers with market cap between {self.min_market_cap} and {self.max_market_cap}")
        
        # Use the provided timestamp, start_time, or current time
        actual_timestamp = timestamp or self.start_time or datetime.now()
        
        # Get tickers based on market cap
        tickers = get_smallcap_tickers(
            min_market_cap=self.min_market_cap,
            max_market_cap=self.max_market_cap,
            date=datetime(2024, 12, 31).date()
        )
        
        # Filter by minimum price if specified
        if self.min_price is not None:
            # This would require additional price data fetching
            # For now, we'll log that this feature is not fully implemented
            logger.warning(f"Minimum price filtering is not fully implemented yet. Requested min_price: {self.min_price}")
        
        logger.info(f"Discovered {len(tickers)} tickers matching criteria")
        
        # Create DataFrame to return
        df = self.init_df()
        
        df['ticker'] = tickers
        df['timestamp'] = [actual_timestamp] * len(tickers)
        
        yield df 