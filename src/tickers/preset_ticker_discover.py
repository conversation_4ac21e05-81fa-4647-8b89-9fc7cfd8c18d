import pandas as pd
from datetime import datetime
from typing import Optional, List, AsyncGenerator

from tickers.iticker_discover import ITickerDiscover

class PresetTickerDiscover(ITickerDiscover):
    """
    A ticker discoverer that returns a predefined list of tickers.
    This is useful for backtesting with a specific set of tickers.
    """
    
    def __init__(self, tickers: List[str], start_time: Optional[datetime] = None):
        """
        Initialize with a list of ticker symbols.
        
        Args:
            tickers: List of ticker symbols to return
            start_time: Optional start time to include in the response
        """
        self.tickers = tickers
        self.start_time = start_time
    
    async def discover(self, timestamp: Optional[datetime] = None) -> AsyncGenerator[pd.DataFrame, None]:
        """
        Yields a DataFrame containing the preset tickers, but only once.
        The generator exits naturally after yielding the tickers.
        
        Args:
            timestamp: Optional datetime for discovery
            
        Yields:
            DataFrame with ticker information, then exits
        """

        # Create a DataFrame with ticker symbols and start_time
        df = self.init_df()
        
        df['ticker'] = self.tickers
        # Use the provided timestamp or start_time for each ticker
        actual_timestamp = timestamp or self.start_time or datetime.now()
        df['timestamp'] = [actual_timestamp] * len(self.tickers)
        
        yield df 