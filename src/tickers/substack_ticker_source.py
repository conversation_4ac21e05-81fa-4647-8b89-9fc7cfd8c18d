from datetime import datetime, timedelta, timezone
import pandas as pd
import asyncio
import time
import re
import requests
import os
import json
from dataclasses import dataclass, field
from typing import List, Literal, Optional
from zoneinfo import ZoneInfo

# New imports for LLM-based extraction using Instructor + Pydantic.
from pydantic import BaseModel, Field
# Literal was already imported via typing above if needed, but we import it here for clarity.
from typing import Literal

from .tickersource import ITickerSource
from .ticker_helpers import get_ticker_set, filter_valid_ticker_events

import logging
logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# TODO
# -----------------------------------------------------------------------------

@dataclass
class SubstackChatComment:
    comment_id: str
    created_at: datetime
    text: str
    author: str


@dataclass
class SubstackChatPost:
    post_id: str
    created_at: datetime
    text: str
    author: str
    comments: List[SubstackChatComment] = field(default_factory=list)


# New Pydantic model to represent an extracted ticker.
class ExtractedTicker(BaseModel):
    ticker: str = Field(..., description="Ticker symbol extracted")
    sentiment: Literal["Bullish", "Bearish"] = Field(..., description="The sentiment associated with the ticker")
    confidence: float = Field(..., description="Confidence score between 0 and 1")
    post_id: Optional[str] = Field(None, description="ID of the post from where the ticker was extracted")
    comment_id: Optional[str] = Field(None, description="ID of the comment from where the ticker was extracted, if applicable")

    def __str__(self):
        # Compact representation: example "ESOA(Bullish, 0.8)"
        return f"{self.ticker}({self.sentiment}, {self.confidence:.1f})"

    def __repr__(self):
        # When printing a list, repr() is used. So have repr() delegate to str()
        return self.__str__()


class SubstackTickerSource(ITickerSource):
    """
    SubstackTickerSource implements the ITickerSource interface for retrieving ticker data
    within a specified date range and for a given chat ID.

    Parameters:
        chat_id (str): The chat identifier.
        allowed_users (set): A set of usernames allowed to access the tickers.
        rate_limit (int): The rate limit for fetching posts.
        cookie (str): The cookie for authentication.
    """

    def __init__(self, chat_id: str, allowed_users=set(), rate_limit=1, cookie=None):
        self.chat_id = chat_id
        self.allowed_users = allowed_users
        self.rate_limit = rate_limit
        self.base_url = f"https://substack.com/api/v1/community/publications/{self.chat_id}/posts"
        self.ticker_pattern = r'\b[A-Z]{2,5}\b(?!\s*(?:UPDATE|[A-Z]{2,}))'
        self.headers = {'Cookie': os.getenv('SUBSTACK_COOKIE')}
        if cookie:
            self.headers['Cookie'] = cookie
            

    def fetch_comments_for_post(self, post: SubstackChatPost, start: datetime, end: datetime) -> List[SubstackChatComment]:
        """
        Fetches comments for a given Substack post using the Substack API endpoint.
        
        The paginated API endpoint used is:
            https://substack.com/api/v1/community/posts/{post.post_id}/comments?order=desc&before=<timestamp>
        
        Comments are filtered by the provided start and end dates.
        
        Returns:
            List[SubstackChatComment]: A list of SubstackChatComment objects parsed from the API response.
        """
        comments = []
        url = f"https://substack.com/api/v1/community/posts/{post.post_id}/comments"
        params = {"order": "desc"}
        before = None
        
        while True:
            if before:
                params["before"] = before
            try:
                response = requests.get(url, params=params, headers=self.headers)
                if response.status_code != 200:
                    # Log the error if needed and return what was collected.
                    break

                data = response.json()
                replies = data.get("replies", [])
                if not replies:
                    break

                for reply in replies:
                    comment_data = reply.get("comment", {})
                    user_data = reply.get("user", {})

                    created_at_str = comment_data.get("created_at", "")
                    if not created_at_str:
                        # Skip comments without a timestamp.
                        continue
                    try:
                        created_at = datetime.fromisoformat(created_at_str.replace("Z", "+00:00"))
                        # Convert the UTC datetime to Eastern Time ("America/New_York").
                        created_at = created_at.astimezone(ZoneInfo("America/New_York"))
                    except Exception:
                        continue

                    # Use the passed parameters instead of instance defaults.
                    if created_at < start:
                        return comments

                    # If the comment is newer than end, skip it.
                    if created_at > end:
                        continue

                    text = comment_data.get("body", "") or comment_data.get("raw_body", "")
                    author = user_data.get("name", "") or user_data.get("handle", "unknown")
                    comment = SubstackChatComment(
                        comment_id=comment_data.get("id", ""),
                        created_at=created_at,
                        text=text,
                        author=author
                    )
                    comments.append(comment)

                # Update pagination: use the 'created_at' of the last comment in this batch.
                last_reply = replies[-1]
                last_comment_data = last_reply.get("comment", {})
                before = last_comment_data.get("created_at")
                if not before:
                    break

                # Optionally, if the API returns flags indicating no more pages, we can break out.
                if not data.get("more", False) and not data.get("moreBefore", False):
                    break

                time.sleep(self.rate_limit)
            except Exception as e:
                # Optionally log exception details.
                break

        return comments

    def _cache_file_path(self, start: datetime, end: datetime) -> str:
        """
        Constructs a cache file path based on self.chat_id and the provided start and end datetimes.
        Uses the "cache_substack" directory.
        """
        cache_dir = os.path.join(os.getcwd(), "cache","cache_substack")
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        # Use ISO format for start and end, replacing ':' with '-' for safe filenames.
        start_str = start.isoformat().replace(":", "-")
        end_str = end.isoformat().replace(":", "-")
        filename = f"substack_posts_{self.chat_id}_{start_str}_{end_str}.json"
        return os.path.join(cache_dir, filename)

    def _serialize_post(self, post: SubstackChatPost) -> dict:
        """
        Converts a SubstackChatPost object (and its comments) into a dictionary
        that is JSON serializable.
        """
        return {
            "post_id": post.post_id,
            "created_at": post.created_at.isoformat(),
            "text": post.text,
            "author": post.author,
            "comments": [
                {
                    "comment_id": comment.comment_id,
                    "created_at": comment.created_at.isoformat(),
                    "text": comment.text,
                    "author": comment.author
                }
                for comment in post.comments
            ]
        }

    def _deserialize_post(self, data: dict) -> SubstackChatPost:
        """
        Reconstructs a SubstackChatPost (and its comments) from a dictionary.
        """
        comments = [
            SubstackChatComment(
                comment_id=c["comment_id"],
                created_at=datetime.fromisoformat(c["created_at"]).replace(tzinfo=ZoneInfo("America/New_York")),
                text=c["text"],
                author=c["author"]
            )
            for c in data.get("comments", [])
        ]
        return SubstackChatPost(
            post_id=data["post_id"],
            created_at=datetime.fromisoformat(data["created_at"]).replace(tzinfo=ZoneInfo("America/New_York")),
            text=data["text"],
            author=data["author"],
            comments=comments
        )

    def fetch_posts(self, start: datetime, end: datetime) -> List[SubstackChatPost]:
        """
        Fetches posts from Substack using the posts API endpoint.
        Uses caching based on self.chat_id and the provided start and end datetimes.
        If a cache is available, load from cache instead of making HTTP requests.
        """
        # Attempt to load cached posts if available.
        cache_file = self._cache_file_path(start, end)
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r") as f:
                    cached_data = json.load(f)
                posts = [self._deserialize_post(post_data) for post_data in cached_data]
                logger.info("Loaded posts from cache.")
                return posts
            except Exception as e:
                logger.warning("Failed to load cache: %s. Proceeding with fresh fetch.", e)

        posts = []
        url = self.base_url
        params = {}
        pagination_before = None

        while True:
            if pagination_before:
                params["before"] = pagination_before

            response = requests.get(url, params=params, headers=self.headers)
            if response.status_code != 200:
                logger.error("Error fetching posts: HTTP %s", response.status_code)
                break

            data = response.json()
            threads = data.get("threads", [])
            if not threads:
                break

            threads = sorted(threads, 
                             key=lambda x: x.get("communityPost", {}).get("created_at", ""),
                             reverse=True)
            last_thread = threads[-1]
            last_cp = last_thread.get("communityPost", {})

            for thread in threads:
                cp = thread.get("communityPost", {})
                created_at_str = cp.get("created_at")
                if not created_at_str:
                    continue

                try:
                    post_time = datetime.strptime(created_at_str, "%Y-%m-%dT%H:%M:%S.%fZ")
                except ValueError:
                    post_time = datetime.strptime(created_at_str, "%Y-%m-%dT%H:%M:%SZ")
                # Attach UTC timezone and then convert to Eastern Time ("America/New_York")
                post_time = post_time.replace(tzinfo=ZoneInfo("UTC")).astimezone(ZoneInfo("America/New_York"))

                # Since posts are returned in descending order, if we hit a post older than start_date we can quit.
                if post_time < start:
                    logger.info("Post too old: %s < %s", post_time, start)
                    # Cache the posts collected so far.
                    try:
                        with open(cache_file, "w") as f:
                            json.dump([self._serialize_post(post) for post in posts], f)
                    except Exception as e:
                        logger.error("Failed to write cache: %s", e)
                    return posts

                if post_time > end:
                    logger.info("Post too new: %s > %s", post_time, end)
                    continue

                text = cp.get("body", "")
                if not text:
                    continue

                author = cp.get("username") or cp.get("author") or "unknown"
                post_id = cp.get("id")
                comments = []
                # Fetch comments for the post.
                # TODO: Uncomment this after fixing
                # comments = self.fetch_comments_for_post(SubstackChatPost(
                #     post_id=post_id,
                #     created_at=post_time,
                #     text=text,
                #     author=author
                # ), start, end)

                substack_post = SubstackChatPost(
                    post_id=post_id,
                    created_at=post_time,
                    text=text,
                    author=author,
                    comments=comments
                )

                posts.append(substack_post)

            pagination_before = last_cp.get("created_at")
            time.sleep(self.rate_limit)

        # After fetching new posts, write them to cache.
        try:
            with open(cache_file, "w") as f:
                json.dump([self._serialize_post(post) for post in posts], f)
        except Exception as e:
            logger.error("Failed to write cache: %s", e)

        return posts

    def _llm_query_with_cache(self, client, prompt: str) -> tuple[List[ExtractedTicker], bool]:
        """
        Queries the LLM with a given prompt and caches the response to a file.
        The cache file is stored in "cache/gemini_llm/<sha256(prompt)>.json".
        If the file exists, the cached response is returned along with a flag indicating
        that the result came from the cache.
        
        Returns:
            tuple: A tuple (extracted, from_cache) where extracted is a list of ExtractedTicker
                   objects and from_cache is a boolean indicating whether the response was cached.
        """
        import hashlib
        import json

        # Define the cache directory (relative to the current working directory)
        cache_dir = os.path.join(os.getcwd(), "cache", "gemini_llm")
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)

        # Create a hash of the prompt to use as the cache file name.
        hash_digest = hashlib.sha256(prompt.encode("utf-8")).hexdigest()
        cache_file = os.path.join(cache_dir, f"{hash_digest}.json")

        # Return the cached response if it exists.
        if os.path.exists(cache_file):
            try:
                with open(cache_file, "r") as f:
                    cached_data = json.load(f)
                return [ExtractedTicker(**entry) for entry in cached_data], True
            except Exception as e:
                logger.error("Error loading LLM cache from %s: %s. Re-querying the LLM.", cache_file, e)

        # Otherwise, perform the LLM query and cache the result.
        try:
            response = client.chat.completions.create(
                model="google/gemini-2.5-flash",
                messages=[{"role": "user", "content": prompt}],
                response_model=List[ExtractedTicker],
            )
            extracted = response.model_dump() if hasattr(response, "model_dump") else response

            # Write the LLM result to the cache file.
            with open(cache_file, "w") as f:
                json.dump([item.dict() for item in extracted], f)
            logger.info("Saved LLM result to cache: %s", cache_file)
            return extracted, False
        except Exception as ex:
            logger.error("LLM extraction error: %s", ex)
            return [], False

    def llm_extract_tickers(self, posts: List[SubstackChatPost]) -> pd.DataFrame:
        """
        Uses an LLM (via the Instructor library) to extract tickers from each post individually.
        For each post, the LLM is provided with the post text and its comments, and is asked to extract
        all stock tickers mentioned along with the associated sentiment ("Bullish" or "Bearish"),
        a confidence score (a float between 0 and 1), and an identifier (post_id or comment_id) indicating where the ticker was mentioned.

        Returns:
            DataFrame: A pandas DataFrame with columns: ticker, sentiment, confidence, post_id, comment_id.
            Only tickers with Bullish sentiment are returned.
        """
        import instructor
        from openai import OpenAI
        
        # Initialize OpenAI-compatible client via OpenRouter and wrap with Instructor.
        client = instructor.from_openai(
            OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=os.getenv("OPENROUTER_API_KEY"),
            ),
            mode=instructor.Mode.JSON,  # Gemini on OpenRouter supports JSON mode for structured outputs
        )
        
        all_extracted_tickers = []  # List to accumulate extracted tickers across posts.

        for post in posts:
            # Build the content for this post along with its comments.
            content = (
                f"Post (post_id: {post.post_id}) by {post.author} on {post.created_at.isoformat()}:\n"
                f"{post.text}\n"
            )
            if post.comments:
                for comment in post.comments:
                    content += (
                        f"Comment (comment_id: {comment.comment_id}) by {comment.author} on {comment.created_at.isoformat()}:\n"
                        f"{comment.text}\n"
                    )

            # Construct a prompt specific to this post.
            prompt = (
                "You are a financial text analyst. Analyze the following data extracted from a Substack publication post and its comments. "
                "Identify all stock tickers mentioned, determine for each ticker whether the sentiment is 'Bullish' or 'Bearish', "
                "and provide a confidence score (a float between 0 and 1) for your sentiment classification. "
                "For each ticker, also return the 'post_id' if it was mentioned in the main post, or the relevant 'comment_id' if it was mentioned in a comment. "
                "Return the result as a JSON array of objects; each object should have the keys 'ticker', 'sentiment', 'confidence', "
                "and either 'post_id' or 'comment_id'.\n\n"
                "Data:\n" + content
            )

            # Use the cache-enabled LLM query.
            try:
                extracted, from_cache = self._llm_query_with_cache(client, prompt)
            except Exception as ex:
                logger.error("LLM extraction error for post %s: %s", post.post_id, ex)
                continue

            all_extracted_tickers.extend(extracted)
            logger.info("Extracted Tickers: %s", extracted)
            if not from_cache:
                time.sleep(self.rate_limit)
     
        columns = ['ticker', 'sentiment', 'confidence', 'post_id', 'comment_id']
        # Filter to only include tickers with Bullish sentiment
        bullish_tickers = [ticker for ticker in all_extracted_tickers if ticker.sentiment == "Bullish"]
        df = pd.DataFrame([ticker.dict() for ticker in bullish_tickers], columns=columns)
        return df

    def get_tickers(self, start: datetime, end: Optional[datetime] = None) -> pd.DataFrame:
        """
        Retrieves posts (and comments) from Substack and extracts tickers using an LLM-based approach.
        For each ticker, the LLM returns the ticker symbol, the sentiment (Bullish or Bearish), a confidence score,
        and an identifier (post_id or comment_id). This method then enriches the resulting DataFrame with a
        'created_at' timestamp based on the source of the ticker.
        
        Parameters:
            start: Optional datetime to filter tickers on/after this time.
            end  : Optional datetime to filter tickers on/before this time.
            
        Returns:
            DataFrame: A pandas DataFrame with columns: ticker, sentiment, confidence, post_id, comment_id, created_at.
        """
        
        if end is None:
            end = datetime.now().replace(tzinfo=start.tzinfo)

        posts = self.fetch_posts(start, end)
        # Use the LLM extractor to get ticker information.
        df = self.llm_extract_tickers(posts)
        # Filter the results for valid tickers.
        valid_tickers = get_ticker_set()
        logger.info(f"Extracted tickers: {df}")
        
        df = df[df['ticker'].isin(valid_tickers)]
        
        # Build lookup dictionaries for created_at timestamps.
        post_created_map = {post.post_id: post.created_at for post in posts}
        comment_created_map = {}
        for post in posts:
            for comment in post.comments:
                comment_created_map[comment.comment_id] = comment.created_at

        # Define a helper function to look up the created_at time based on the post_id or comment_id.
        def lookup_created_at(row):
            post_id = row.get("post_id")
            comment_id = row.get("comment_id")
            if pd.notna(post_id) and post_id in post_created_map:
                return post_created_map[post_id]
            elif pd.notna(comment_id) and comment_id in comment_created_map:
                return comment_created_map[comment_id]
            else:
                return None

        # Add the created_at column to the DataFrame.
        df['created_at'] = df.apply(lookup_created_at, axis=1)

        logger.info(f"Final valid tickers: {df}")
        return df


# Example usage:
if __name__ == "__main__":
    import dotenv
    dotenv.load_dotenv()
    
    end = datetime.fromisoformat("2025-02-10").replace(tzinfo=ZoneInfo("America/New_York"))
    start = end - timedelta(days=10)
    
    allowed_users = {"citriniadministrative", "citrini"}
        
    source = SubstackTickerSource(
        chat_id="836125", 
        allowed_users=allowed_users, 
        rate_limit=1
    )
    
    # Retrieve the tickers using the LLM-based extractor and show the results.
    df_tickers = source.get_tickers(start, end)
    logger.info("Extracted tickers:\n%s", df_tickers) 