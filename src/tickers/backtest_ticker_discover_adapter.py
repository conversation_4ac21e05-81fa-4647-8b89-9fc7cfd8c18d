from typing import Optional, AsyncGenerator
import pandas as pd
from datetime import datetime
import asyncio
from zoneinfo import ZoneInfo

from tickers.iticker_discover import ITickerDiscover
from tickers.tickersource import ITickerSource


class BacktestTickerDiscoverAdapter(ITickerDiscover):
    """
    Adapter for backtesting that fetches all tickers between start_date and end_date
    and yields them at a controlled rate.
    """
    
    def __init__(self, ticker_source: ITickerSource, 
                 start_date: datetime,
                 end_date: datetime,
                 tickers_per_second: float = 10.0):
        """
        Initialize the backtesting adapter with a ticker source and time range.
        
        Args:
            ticker_source: The ITickerSource implementation to adapt
            start_date: Start datetime for backtesting
            end_date: End datetime for backtesting
            tickers_per_second: How many tickers to yield per second during backtesting
        """
        self.ticker_source = ticker_source
        self.tickers_per_second = tickers_per_second
        self.timezone = ZoneInfo("America/New_York")  # EST timezone
        
        # Ensure timestamps have timezone info
        if start_date.tzinfo is None:
            self.start_date = start_date.replace(tzinfo=self.timezone)
        else:
            self.start_date = start_date
            
        if end_date.tzinfo is None:
            self.end_date = end_date.replace(tzinfo=self.timezone)
        else:
            self.end_date = end_date
    
    async def discover(self, timestamp: Optional[datetime] = None) -> AsyncGenerator[pd.DataFrame, None]:
        """
        Discover tickers by fetching all tickers between start_date and end_date
        and yielding them at a controlled rate.
        
        Args:
            timestamp: Unused.
            
        Yields:
            pd.DataFrame containing the columns:
                - timestamp: The timestamp for the ticker in EST
                - ticker: The ticker symbol identified
        """
        # Use the provided timestamp or fall back to the class's start_date
        start_time =self.start_date
        
        
        # Fetch all tickers between start and end timestamps
        all_tickers_df = self.ticker_source.get_tickers(start=start_time, end=self.end_date)
        
        # If the dataframe has a 'created_at' column, rename it to 'timestamp'
        if 'created_at' in all_tickers_df.columns and 'timestamp' not in all_tickers_df.columns:
            all_tickers_df.rename(columns={"created_at": "timestamp"}, inplace=True)
        
        # Sort by timestamp to ensure chronological order
        all_tickers_df = all_tickers_df.sort_values('timestamp')
        
        # Calculate the delay between yielding tickers
        delay = 1.0 / self.tickers_per_second if self.tickers_per_second > 0 else 0
        
        # Group tickers by timestamp for more realistic simulation
        grouped = all_tickers_df.groupby(pd.Grouper(key='timestamp', freq='1S'))
        
        # Yield tickers at the controlled rate
        for _, group in grouped:
            if not group.empty:
                # Create a copy of the group before modifying it
                group_copy = group.copy()
                
                # Add metadata column using .loc to avoid the warning
                group_copy.loc[:, 'metadata'] = group_copy['timestamp'].apply(lambda ts: {"timestamp": ts})
                
                # Yield the group
                yield group_copy[['timestamp', 'ticker', 'metadata']]
                
                # Wait before yielding the next group
                await asyncio.sleep(delay) 