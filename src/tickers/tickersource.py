from abc import ABC, abstractmethod
import pandas as pd
from datetime import datetime
from typing import Optional

class ITickerSource(ABC):
    """
    ITickerSource defines the common synchronous method for ticker sources.
    It is expected to return a list of tickers along with a timestamp indicating when each ticker was present,
    as well as any optional metadata. The returned data should be a pandas DataFrame.

    The DataFrame is expected to include, at a minimum, the following columns:
        - 'ticker': The ticker symbol.
        - 'timestamp': The time when the ticker was encountered.

    Additional metadata can be provided in extra columns.
    """

    @abstractmethod
    def get_tickers(self, start: datetime, end: Optional[datetime] = None) -> pd.DataFrame:
        """
        Fetches tickers with associated timestamp and optional metadata within the specified time window.
        
        Parameters:
            start: Datetime to filter tickers on/after this time.
            end  : Optional datetime to filter tickers on/before this time.
        
        Returns:
            pd.DataFrame: A DataFrame containing ticker information.
        """
        pass 