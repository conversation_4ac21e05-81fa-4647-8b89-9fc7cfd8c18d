import os
import glob
import pandas as pd
import logging
import datetime
import json
from datetime import datetime as dt
from pathlib import Path
from typing import Optional, Set, Tuple, List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from polygon import RESTClient
from polygon.exceptions import BadResponse

logger = logging.getLogger(__name__)


class TickerInfoStore:
    """
    A class to manage ticker data, including loading, caching, filtering operations,
    and interactions with the Polygon API for refreshing ticker data.
    """

    def __init__(self, cache_dir: str = None, polygon_api_key: str = None):
        """
        Initialize the TickerInfoStore with a specified cache directory.

        Args:
            cache_dir: The directory to use for caching ticker data.
                       If None, uses the CACHE_DIR environment variable or defaults to "cache".
            polygon_api_key: The Polygon API key to use for fetching ticker data.
                            If None, uses the POLYGON_API_KEY environment variable.
        """
        self.cache_dir = cache_dir or os.getenv("CACHE_DIR", "cache")
        self.polygon_api_key = polygon_api_key or os.getenv("POLYGON_API_KEY")
        self.client = RESTClient(self.polygon_api_key)
        os.makedirs(self.cache_dir, exist_ok=True)
        # Directory for caching individual ticker details
        self.details_cache_dir = os.path.join(self.cache_dir, "ticker_details")
        os.makedirs(self.details_cache_dir, exist_ok=True)

    def get_cache_file_path(self, date: dt) -> str:
        """
        Generates the path to a cache file for a specific date.

        Args:
            date: The date for which to generate the cache file path.

        Returns:
            The path to the cache file.
        """
        date_str = date.strftime("%Y-%m-%d")
        return os.path.join(self.cache_dir, f"ticker_data_{date_str}.csv")

    def find_latest_cache_file(self) -> Optional[str]:
        """
        Finds the most recent ticker data cache file.

        Returns:
            The path to the most recent cache file, or None if no cache files exist.
        """
        files = glob.glob(os.path.join(self.cache_dir, "ticker_data_*.csv"))
        if not files:
            return None

        # Define a helper to extract the date from the filename
        def extract_date(file_path: str) -> dt:
            # Assumes filename structure: ticker_data_YYYY-MM-DD.csv
            basename = os.path.basename(file_path)
            date_str = basename.split("_")[2].split(".")[0]
            return dt.strptime(date_str, "%Y-%m-%d")

        # Pick the file with the most recent date
        files_sorted = sorted(files, key=extract_date, reverse=True)
        return files_sorted[0]

    def load_ticker_data(self, date: Optional[dt] = None, refresh_cache: bool = False) -> pd.DataFrame:
        """
        Loads the ticker data for the specified date or the latest available date.

        Args:
            date: The date for which to load ticker data. If None, uses the latest available data.
            refresh_cache: If True, refreshes the cache by fetching new data from the Polygon API.

        Returns:
            A DataFrame containing the ticker data, or an empty DataFrame if no data is available.
        """
        if date is None:
            cache_file = self.find_latest_cache_file()
            if cache_file is None:
                return pd.DataFrame()
        else:
            cache_file = self.get_cache_file_path(date)
            if not os.path.exists(cache_file):
                return pd.DataFrame() if not refresh_cache else self.fetch_ticker_data(date)

        logger.info(f"Loading cached ticker data from {cache_file}")
        return pd.read_csv(cache_file)

    def cache_ticker_data(self, df: pd.DataFrame, date: dt) -> None:
        """
        Caches the ticker DataFrame to a CSV file based on the provided date.

        Args:
            df: The DataFrame to cache.
            date: The date to associate with the cached data.
        """
        cache_file = self.get_cache_file_path(date)
        df.to_csv(cache_file, index=False)
        logger.info(f"Cached ticker data to {cache_file}")

    def get_ticker_set(self, date: Optional[dt] = None) -> Set[str]:
        """
        Returns a set of valid tickers from the cached data file for the specified date.

        Args:
            date: The date for which to get tickers. If None, uses the latest available data.

        Returns:
            A set of valid ticker symbols, or an empty set if no data is available.
        """
        df = self.load_ticker_data(date)
        if df.empty:
            return set()
        if 'active' in df.columns:
            df = df[df['active']]
        return set(df['ticker'].tolist())

    def filter_valid_ticker_events(self, events_df: pd.DataFrame, valid_tickers: Optional[Set[str]] = None, date: Optional[dt] = None) -> pd.DataFrame:
        """
        Filters a DataFrame of ticker events, keeping only those rows where the ticker is valid.

        Args:
            events_df: The DataFrame of events to filter.
            valid_tickers: A set of valid tickers to use for filtering. If None, retrieves the set using get_ticker_set.
            date: The date for which to get valid tickers if valid_tickers is None.

        Returns:
            A filtered DataFrame containing only rows with valid tickers.
        """
        if events_df.empty:
            return events_df

        if valid_tickers is None:
            valid_tickers = self.get_ticker_set(date)

        if not valid_tickers:
            return events_df

        return events_df[events_df['ticker'].isin(valid_tickers)]
    
    def get_ticker_details(self, ticker: str, date: datetime.date) -> Dict[str, Any]:
        """
        Get ticker details from Polygon API, with simple disk‐cache by ticker+date.
        """
        # build cache path
        date_str = date.strftime("%Y-%m-%d")
        cache_file = os.path.join(self.details_cache_dir, f"{ticker}_{date_str}.json")
        # return cached details if present
        if os.path.exists(cache_file):
            with open(cache_file, "r") as f:
                return json.load(f)

        try:
            params = {"ticker": ticker}
            params["date"] = date_str
            details = self.client.get_ticker_details(**params)
            result = {k: v for k, v in details.__dict__.items() if not k.startswith('_')}
            # write out cache
            with open(cache_file, "w") as f:
                json.dump(result, f, default=str)
            return result
        except BadResponse:
            # cache negative response (ticker not found)
            with open(cache_file, "w") as f:
                json.dump(None, f)
            return None
        except Exception as e:
            # cache negative response (error)
            with open(cache_file, "w") as f:
                json.dump(None, f)
            logger.error(f"Skipping ticker {ticker}: Unexpected error - {e}")
            return None

    def fetch_ticker_data(self, date: Optional[datetime.date] = None, active: Optional[bool] = None) -> pd.DataFrame:
        """
        Fetch ticker data from Polygon API.

        Args:
            date: Date to fetch data for. Defaults to today.
            active: Filter for active (True) or inactive (False) tickers. Defaults to None (all tickers).

        Returns:
            DataFrame containing ticker data

        Raises:
            ValueError: If polygon_api_key is not set
        """
        if not self.polygon_api_key:
            raise ValueError(
                "Polygon API key not set. Please set POLYGON_API_KEY environment variable or pass it to the constructor.")

        if date is None:
            date = datetime.date.today()

        logger.info(f"Fetching ticker data for {date}")

        # Create the REST client
        client = RESTClient(self.polygon_api_key)

        # Step 1: Get all stock tickers with pagination
        all_tickers = []
        date_str = None
        if date:
            date_str = date.strftime("%Y-%m-%d")

        logger.info("Fetching ticker list...")
        try:
            for ticker in client.list_tickers(
                type="CS", market="stocks", active=active, limit=1000, sort="ticker", order="asc", date=date_str):
                all_tickers.append(ticker)
        except Exception as e:
            logger.error(f"Error fetching ticker list: {str(e)}")
            raise

        logger.info(f"Found {len(all_tickers)} tickers")

        # Step 2: Fetch additional ticker details in parallel
        ticker_data = []

        def fetch_details(t):
            try:
                params = {"ticker": t.ticker}
                if not t.active and t.delisted_utc:
                    # parse the ISO‐8601 timestamp into a datetime, then format as YYYY-MM-DD
                    # if your API can accept full UTC timestamps you could also skip the .date()
                    parsed = dt.fromisoformat(t.delisted_utc.replace("Z", "+00:00"))
                    params["date"] = parsed.strftime("%Y-%m-%d")
                elif date:
                    params["date"] = date.strftime("%Y-%m-%d")
                details = client.get_ticker_details(**params)
                # Store all details as a dictionary
                details_dict = {
                    k: v for k, v in details.__dict__.items() if not k.startswith('_')}
                details_dict['ticker'] = t.ticker
                return details_dict
            except BadResponse as e:
                logger.warning(f"Skipping ticker {t.ticker}: Not found")
                return {
                    k: getattr(t, k) for k in dir(t) 
                    if not k.startswith('_') and not callable(getattr(t, k))
                }
            except Exception as e:
                logger.error(
                    f"Skipping ticker {t.ticker}: Unexpected error - {str(e)}")
            return None

        max_workers = 20
        logger.info("Fetching ticker details...")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_ticker = {executor.submit(
                fetch_details, t): t for t in all_tickers}
            for future in tqdm(as_completed(future_to_ticker), total=len(future_to_ticker), desc="Fetching ticker details"):
                result = future.result()
                if result is not None:
                    ticker_data.append(result)

        # Create DataFrame from ticker data
        df = pd.DataFrame(ticker_data)

        # Cache results
        self.cache_ticker_data(df, dt.combine(date, datetime.time()))

        return df

    def prune_old_cache_files(self, max_age_days: int = 7) -> Tuple[int, int]:
        """
        Removes cache files that are older than the specified number of days.

        Args:
            max_age_days: Maximum age of cache files in days

        Returns:
            tuple: (num_pruned, num_kept) - Count of files pruned and kept
        """
        # Ensure cache directory exists
        cache_path = Path(self.cache_dir)
        if not cache_path.exists():
            logger.warning(
                f"Cache directory {self.cache_dir} does not exist. Creating it.")
            cache_path.mkdir(parents=True, exist_ok=True)
            return 0, 0

        # Calculate cutoff date
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=max_age_days)

        # Track statistics
        num_pruned = 0
        num_kept = 0

        # Iterate through files in the cache directory
        for file_path in cache_path.glob("ticker_data_*.csv"):
            # Get file modification time
            file_mtime = datetime.datetime.fromtimestamp(
                file_path.stat().st_mtime)

            # Check if file is older than cutoff
            if file_mtime < cutoff_date:
                logger.info(
                    f"Pruning old cache file: {file_path} (modified: {file_mtime})")
                try:
                    file_path.unlink()
                    num_pruned += 1
                except Exception as e:
                    logger.error(f"Failed to delete {file_path}: {e}")
            else:
                logger.debug(
                    f"Keeping cache file: {file_path} (modified: {file_mtime})")
                num_kept += 1

        return num_pruned, num_kept

    def refresh_ticker_data(self, force_refresh: bool = False, max_age_days: int = 7) -> None:
        """
        Main function to refresh ticker data:
        1. Download today's ticker data if needed
        2. Prune old cache files

        Args:
            force_refresh: Force refresh even if today's data exists
            max_age_days: Maximum age of cache files in days
        """
        # Fetch today's ticker data
        today = datetime.date.today()
        logger.info(f"Refreshing ticker data for {today}")

        # Check if we already have data for today
        existing_data = self.load_ticker_data(
            dt.combine(today, datetime.time()))
        if not existing_data.empty and not force_refresh:
            logger.info(
                f"Already have ticker data for {today}, skipping download")
        else:
            # Fetch new data
            df = self.fetch_ticker_data(today)
            logger.info(f"Successfully fetched {len(df)} ticker records")

        # Prune old cache files
        pruned, kept = self.prune_old_cache_files(max_age_days)
        logger.info(
            f"Cache pruning complete: {pruned} files pruned, {kept} files kept")


def load_cached_ticker_data(date: Optional[dt] = None) -> pd.DataFrame:
    """
    Loads the cached ticker data CSV file for the specified date.
    If no date is provided, uses the latest available cached file in the cache directory.

    Returns a pandas DataFrame of the cached tickers.
    If the cache file does not exist, returns an empty DataFrame.
    """
    store = TickerInfoStore()
    return store.load_ticker_data(date)


def get_ticker_set(date: Optional[dt] = None) -> set:
    """
    Returns a set of valid tickers from the cached data file for the specified date.
    If no date is provided, uses the latest available cached file.
    This function does not perform any market cap filtering.
    """
    store = TickerInfoStore()
    return store.get_ticker_set(date)


def cache_ticker_data(df: pd.DataFrame, date: dt) -> None:
    """
    Caches the ticker DataFrame to a CSV file based on the provided date.
    """
    store = TickerInfoStore()
    store.cache_ticker_data(df, date)


def filter_valid_ticker_events(events_df: pd.DataFrame, valid_tickers: set) -> pd.DataFrame:
    """
    Filters a DataFrame of ticker events (e.g. from Substack), keeping only those rows
    where the ticker is in the valid_tickers set.

    If the valid_tickers set is empty, returns the original DataFrame.
    """
    store = TickerInfoStore()
    return store.filter_valid_ticker_events(events_df, valid_tickers)
