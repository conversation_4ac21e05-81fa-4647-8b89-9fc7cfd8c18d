#!/usr/bin/env python3
import argparse
import importlib.util
import sys
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import List, Optional, Type

from tickers.iscancriteria import IScanCriteria
from marketdata.disk_market_data import DiskMarketData
from abc import ABC, abstractmethod

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger("ScannerCLI")


def load_criteria_from_module(module_path: str) -> Type[IScanCriteria]:
    """
    Dynamically load a criteria class from a Python module.
    
    Args:
        module_path: Path to the Python module containing the criteria class
                    Can be a file path (e.g., 'strategies/para_short_strategy.py')
                    or a module path (e.g., 'strategies.para_short_strategy')
    
    Returns:
        The criteria class (not instance)
    """
    try:
        # Check if the path is a file path or a module path
        if module_path.endswith('.py') or '/' in module_path:
            # It's a file path
            module_name = module_path.split('/')[-1].replace('.py', '')
            
            # Load the module from file
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            if spec is None or spec.loader is None:
                raise ImportError(f"Could not load module from file {module_path}")
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
        else:
            # It's a module path (e.g., 'strategies.para_short_strategy')
            logger.info(f"Importing module {module_path} as a Python package")
            module = importlib.import_module(module_path)
        
        # Find the first class that implements IScanCriteria
        criteria_class = None
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            # Check if it's a class and a subclass of IScanCriteria (but not IScanCriteria itself)
            if (isinstance(attr, type) and 
                issubclass(attr, IScanCriteria) and 
                attr is not IScanCriteria):
                criteria_class = attr
                break
        
        if criteria_class is None:
            raise ValueError(f"No implementation of IScanCriteria found in {module_path}")
        
        return criteria_class
    
    except Exception as e:
        logger.error(f"Error loading criteria module: {str(e)}")
        sys.exit(1)

def split_historical_and_current_day(df: pd.DataFrame) -> tuple:
    """
    Split the dataframe into historical data and current day data.
    
    Args:
        df: DataFrame with all data
    
    Returns:
        Tuple of (historical_df, current_day_df)
    """
    if df.empty:
        return df, pd.DataFrame()
    
    # Ensure date is in datetime format
    if not pd.api.types.is_datetime64_any_dtype(df['date']):
        df['date'] = pd.to_datetime(df['date'])
    
    # Get the most recent date
    max_date = df['date'].max()
    
    # Split the data
    current_day_df = df[df['date'] == max_date].copy()
    historical_df = df[df['date'] < max_date].copy()
    
    return historical_df, current_day_df

def main():
    """Main CLI function"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Market Data Scanner CLI")
    
    parser.add_argument('--criteria', '-c', type=str, required=True,
                        help='Path to Python module with criteria implementation')
    parser.add_argument('--param', '-p', action='append', default=[],
                        help='Criteria parameters in format name=value (can be used multiple times)')
    parser.add_argument('--output', '-o', type=str, default=None,
                        help='Output file path (optional, defaults to stdout)')
    parser.add_argument('--keep-current', '-k', action='store_true',
                        help='Keep current day data in historical data (don\'t split)')
    parser.add_argument('--base-dir', '-b', type=str, default="market_data",
                        help='Base directory for market data (default: market_data)')
    parser.add_argument('--preload-months', '-m', type=int, default=3,
                        help='Number of months to preload (default: 3)')
    
    args = parser.parse_args()
    
    try:
        # Initialize market data reader with preloading
        logger.info(f"Initializing DiskMarketData from {args.base_dir} with {args.preload_months} months preloaded")
        reader = DiskMarketData(base_dir=args.base_dir, preload_months=args.preload_months)
        
        # Load criteria class
        logger.info(f"Loading criteria from {args.criteria}")
        criteria_class = load_criteria_from_module(args.criteria)
        
        # Parse criteria parameters
        criteria_params = {}
        for param in args.param:
            if '=' not in param:
                logger.error(f"Invalid parameter format: {param}, expected name=value")
                sys.exit(1)
            
            name, value = param.split('=', 1)
            
            # Try to convert value to appropriate type
            try:
                # Try as int
                criteria_params[name] = int(value)
            except ValueError:
                try:
                    # Try as float
                    criteria_params[name] = float(value)
                except ValueError:
                    # Keep as string
                    criteria_params[name] = value
        
        logger.info(f"Criteria parameters: {criteria_params}")
        
        # Create criteria instance with parameters
        criteria = criteria_class(**criteria_params)
        
        # Get preloaded data
        logger.info("Getting preloaded market data")
        preloaded_df = reader.get_preloaded_data()
        
        if preloaded_df.empty:
            logger.error("No preloaded data available. Check your market data directory.")
            sys.exit(1)
        
        # Ensure date is in datetime format
        if not pd.api.types.is_datetime64_any_dtype(preloaded_df['date']):
            preloaded_df['date'] = pd.to_datetime(preloaded_df['date'])
        
        logger.info(f"Using {len(preloaded_df)} data points from preloaded data")
        
        # Split into historical and current day data if requested
        if args.keep_current:
            historical_df = preloaded_df
            current_day_df = None
            logger.info("Using all data as historical data (including most recent day)")
        else:
            historical_df, current_day_df = split_historical_and_current_day(preloaded_df)
            logger.info(f"Split data: {len(historical_df)} historical points, {len(current_day_df)} current day points")
        
        # Run the scan
        logger.info("Running scan criteria...")
        matching_tickers = criteria.scan(historical_df, current_day_df)
        
        # Format the results
        result_text = "\nMatching Tickers:\n" + "\n".join(matching_tickers) if matching_tickers else "\nNo matching tickers found."
        result_text = f"Found {len(matching_tickers)} tickers matching criteria:\n" + result_text
        
        # Output results
        if args.output:
            with open(args.output, 'w') as f:
                f.write(result_text)
            logger.info(f"Results written to {args.output}")
        else:
            print(result_text)
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())