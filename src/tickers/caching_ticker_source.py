from datetime import datetime
from typing import Optional
import pandas as pd

from .tickersource import ITickerSource  # Assuming ITickerSource is defined in src/tickers/tickersource.py

class CachingTickerSource(ITickerSource):
    """
    CachingTickerSource wraps a delegate ITickerSource and caches ticker data over a specified time window.

    On first use, it retrieves all tickers from the delegate within the caching window.
    Subsequent calls to get_tickers() filter the cached data to return only the data for 
    the requested time range. It throws an error if the requested range isn't fully within 
    the caching window.
    """
    def __init__(self, delegate: ITickerSource, start: datetime, end: datetime):
        if start > end:
            raise ValueError("Caching window start cannot be after caching window end.")
        self.delegate = delegate
        self.cache_start = start
        self.cache_end = end
        self._cache: Optional[pd.DataFrame] = None

    def get_tickers(self, start: datetime, end: Optional[datetime] = None) -> pd.DataFrame:
        """
        Fetch tickers in the desired time range by filtering the cached data.

        Parameters:
            start (datetime): The start datetime for ticker filtering.
            end (datetime, optional): The end datetime for ticker filtering.
                If not provided, defaults to the caching window's end.

        Returns:
            pd.DataFrame: A DataFrame containing ticker information filtered by the provided time range.

        Raises:
            ValueError: If the requested range is not fully within the caching window.
        """
        if end is None:
            end = self.cache_end

        # Ensure the requested range is a subset of the caching window
        if start < self.cache_start or end > self.cache_end:
            raise ValueError("Requested ticker range is out of the caching window.")
        
        # Cache the delegate's data if not already cached.
        if self._cache is None:
            self._cache = self.delegate.get_tickers(self.cache_start, self.cache_end)
                    
        mask = (self._cache['created_at'] >= start) & (self._cache['created_at'] <= end)
        return self._cache.loc[mask] 