from datetime import datetime, time
import pandas as pd
from typing import Optional, Set
from zoneinfo import ZoneInfo
from enum import Enum

from tickers.ticker_helpers import load_cached_ticker_data
from tickers.tickersource import ITickerSource
from tools.clock import Clock

class TickerType(Enum):
    """Enumeration of possible ticker types."""
    COMMON_STOCK = "CS"
    ETF = "ETF"
    PREFERRED_STOCK = "PS"
    WARRANT = "WARRANT"
    RIGHT = "RIGHT"
    UNIT = "UNIT"
    ADR = "ADR"
    REIT = "REIT"
    LP = "LP"
    OTHER = "OTHER"

def is_trading_time(dt: datetime) -> bool:
    """
    Check if a datetime (dt) falls within allowed trading hours in EST.
    Tradable sessions in EST:
      - Pre-market: 4:00 AM to 9:30 AM
      - Regular hours: 9:30 AM to 4:00 PM
      - After-hours: 4:00 PM to 8:00 PM
    """
    # Ensure dt is tz-aware; assume dt is in EST if not set.
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=ZoneInfo("America/New_York"))
    t = dt.time()
    pre_market = time(4, 0) <= t < time(9, 30)
    regular    = time(9, 30) <= t < time(16, 0)
    after_hours= time(16, 0) <= t < time(20, 0)
    return pre_market or regular or after_hours

def is_regular_hours(dt: datetime) -> bool:
    """
    Check if a datetime (dt) falls within regular trading hours (9:30–16:00 EST).
    """
    # Ensure dt is tz-aware; assume dt is in EST if not set.
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=ZoneInfo("America/New_York"))
    return time(9, 30) <= dt.time() < time(16, 0)

class FilteringTickSource(ITickerSource):
    """
    FilteringTickSource is a decorator for an underlying ITickerSource.
    It wraps the provided ticker source and then applies filtering based on
    supplemental details loaded from the cached ticker data, as well as a trading
    hours filter on the 'created_at' field.

    The filtering criteria are determined from the cached data:
      - If the cached data contains a 'market_cap' column, only tickers with a
        market cap between min_market_cap and max_market_cap are considered.
      - If the cached data contains an 'active' column, only active tickers are kept.
      - If allowed_ticker_types is specified, only tickers of those types are kept.
    Additionally, if the underlying ticker events contain a 'created_at' field,
    they are filtered based on trading session:
      - "full": Only values that fall in any allowed trading session (pre-market, regular, after-hours)
      - "rth":  Only values that fall during regular trading hours (9:30–16:00).

    Attributes:
        ticker_source (ITickerSource): The underlying ticker source to retrieve tickers.
        min_market_cap (float): The minimum market cap filter.
        max_market_cap (float): The maximum market cap filter.
        date (Optional[datetime]): The date used to load the cached ticker data.
                                   If none, the latest cache is used.
        trade_session (str): Either "full" (all trading sessions) or "rth" (regular trading hours only).
        clock (Clock): An optional clock to generate timestamps.
        allowed_ticker_types (Optional[Set[TickerType]]): Set of allowed ticker types. If None, all types are allowed.
    """
    def __init__(
        self, 
        ticker_source: Optional[ITickerSource] = None,
        min_market_cap: float = 0.0, 
        max_market_cap: float = float('inf'),
        date: Optional[datetime] = None,
        trade_session: str = "full",
        clock: Optional[Clock] = None,
        allowed_ticker_types: Optional[Set[TickerType]] = None
    ) -> None:
        self.ticker_source = ticker_source
        self.min_market_cap = min_market_cap
        self.max_market_cap = max_market_cap
        self.date = date
        self.trade_session = trade_session
        self.clock = clock or Clock()
        self.allowed_ticker_types = allowed_ticker_types

    def get_tickers(self, start: datetime, end: Optional[datetime] = None) -> pd.DataFrame:
        """
        Retrieves tickers from the underlying source and applies filtering based on
        cached ticker details and trading hours:
          - Loads the underlying tickers.
          - Loads cached ticker data using load_cached_ticker_data (checking its columns).
          - If the cached data includes 'market_cap', tickers are filtered to be
            within [min_market_cap, max_market_cap].
          - If allowed_ticker_types is specified, only tickers of those types are kept.
          - Only tickers present in both the underlying data and the filtered cached data are returned.
          - If a 'created_at' column exists, further filter the tickers based on trading hours:
                * "full": use is_trading_time
                * "rth":  use is_regular_hours

        Returns:
            pd.DataFrame: A DataFrame of the filtered ticker information.
        """
        if self.ticker_source is not None:
            # Retrieve tickers from the underlying source.
            underlying_df = self.ticker_source.get_tickers(start, end)
            if underlying_df.empty:
                return underlying_df
        else:
            # New behavior when ticker_source is None - create base DataFrame from cached data
            underlying_df = pd.DataFrame()
        
        # Load cached ticker data (using provided date or latest available).
        cached_df = load_cached_ticker_data(self.date)
        if not cached_df.empty and "market_cap" in cached_df.columns:
            valid_condition = (
                cached_df["market_cap"].isna() |
                (
                    cached_df["market_cap"].notna() &
                    (cached_df["market_cap"] >= self.min_market_cap) &
                    (cached_df["market_cap"] <= self.max_market_cap)
                )
            )
            valid_tickers = set(cached_df.loc[valid_condition, "ticker"])
        else:
            valid_tickers = set(cached_df["ticker"]) if not cached_df.empty else set()

        if self.ticker_source is None:
            # Use the clock to generate the timestamp
            now = pd.Timestamp(self.clock.now())
            filtered_df = pd.DataFrame({
                'ticker': list(valid_tickers),
                'timestamp': now,
                'created_at': now
            })
        else:
            # Filter underlying tickers by the valid tickers from cached data.
            filtered_df = underlying_df[underlying_df["ticker"].isin(valid_tickers)].copy()

        # Apply ticker type filtering if specified
        if self.allowed_ticker_types is not None:
            # Extract allowed ticker type values from the allowed ticker types set
            allowed_type_values = {t.value for t in self.allowed_ticker_types}
            # Identify tickers from the cached data that match the allowed types
            allowed_tickers = cached_df.loc[cached_df["type"].isin(allowed_type_values), "ticker"]
            # Filter the DataFrame to include only tickers that are in the allowed list
            filtered_df = filtered_df[filtered_df["ticker"].isin(allowed_tickers)]

        # Apply the trading hours filter if the 'created_at' column is available.
        if "created_at" in filtered_df.columns:
            if self.trade_session.lower() == "rth":
                filtered_df = filtered_df[filtered_df["created_at"].apply(is_regular_hours)]
            else:  # "full" session (all valid trading hours)
                filtered_df = filtered_df[filtered_df["created_at"].apply(is_trading_time)]
        
        return filtered_df.reset_index(drop=True)