from abc import ABC, abstractmethod
from typing import Optional, AsyncGenerator
import pandas as pd
from datetime import datetime

class ITickerDiscover(ABC):
    """
    Interface for ticker discover services.
    Responsible for discover and returning tickers of interest.
    """
    
    @staticmethod
    def init_df() -> pd.DataFrame:
        """
        Initialize an empty DataFrame with the required columns.
        
        Returns:
            pd.DataFrame with the required columns for ticker discovery results.
        """
        return pd.DataFrame({
            'timestamp': pd.Series(dtype='datetime64[ns]'),
            'ticker': pd.Series(dtype='str')
        })
    
    @abstractmethod
    async def discover(self, timestamp: Optional[datetime] = None) -> AsyncGenerator[pd.DataFrame, None]:
        """
        Discover tickers of interest at a given timestamp as an async generator.
        The generator can exit naturally when there are no more tickers to discover.
        
        Args:
            timestamp: Optional timestamp to scan at. If None, uses current time.
            
        Yields:
            pd.DataFrame containing at minimum the columns:
                - timestamp: The timestamp for the ticker
                - ticker: The ticker symbol identified
        """
        pass 