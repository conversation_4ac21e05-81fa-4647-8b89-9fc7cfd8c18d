"""
A combined ticker source that applies filtering to a SubstackTickerSource.
"""

from datetime import datetime
from typing import Optional
import pandas as pd

from tickers.tickersource import ITickerSource
from tickers.substack_ticker_source import SubstackTickerSource
from tickers.filtering_ticksource import FilteringTickSource

class FilteredSubstackTickerSource(ITickerSource):
    """
    A custom ticker source that combines SubstackTickerSource with FilteringTickSource.
    This allows us to use both sources without modifying the run.py framework.
    """
    
    def __init__(self, chat_id: str, rate_limit: int = 1, 
                 min_market_cap: float = 0.0, max_market_cap: float = 5_000_000_000, # 5 billion
                 trade_session: str = "full"):
        """
        Initialize the combined ticker source.
        
        Args:
            chat_id: The Substack chat ID to use
            rate_limit: Rate limit for API calls
            min_market_cap: Minimum market cap for filtering
            max_market_cap: Maximum market cap for filtering
            trade_session: Trading session type ("rth" or "full")
        """
        # Create the base SubstackTickerSource
        self.base_ticker_source = SubstackTickerSource(
            chat_id=chat_id,
            rate_limit=rate_limit
        )
        
        # Create the filtering layer
        self.filtering_source = FilteringTickSource(
            ticker_source=self.base_ticker_source,
            min_market_cap=min_market_cap,
            max_market_cap=max_market_cap,
            trade_session=trade_session
        )
    
    def get_tickers(self, start: datetime, end: Optional[datetime] = None) -> pd.DataFrame:
        """
        Get tickers by delegating to the filtering source.
        
        Args:
            start: Start datetime for ticker retrieval
            end: Optional end datetime for ticker retrieval
            
        Returns:
            DataFrame of filtered tickers
        """
        return self.filtering_source.get_tickers(start, end) 