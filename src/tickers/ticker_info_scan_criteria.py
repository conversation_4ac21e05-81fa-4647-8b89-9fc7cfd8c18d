from abc import ABC
import pandas as pd
from typing import List, Optional, Set
from datetime import datetime
from enum import Enum
import logging

from tickers.iscancriteria import IScanCriteria
from tickers.ticker_helpers import load_cached_ticker_data

logger = logging.getLogger(__name__)

class TickerType(Enum):
    """Enumeration of possible ticker types."""
    COMMON_STOCK = "CS"
    ETF = "ETF"
    PREFERRED_STOCK = "PS"
    WARRANT = "WARRANT"
    RIGHT = "RIGHT"
    UNIT = "UNIT"
    ADR = "ADR"
    REIT = "REIT"
    LP = "LP"
    OTHER = "OTHER"

class TickerInfoScanCriteria(IScanCriteria):
    """
    A scan criteria that filters tickers based on their market capitalization range, type, and an optional blocklist.

    This class filters tickers from the cached ticker data based on specified criteria:
    - Market cap range (min/max)
    - Allowed ticker types (e.g., only common stocks and ETFs)
    - Blocklist of tickers to exclude no matter what

    This criteria will first apply the delegate's scan method and then filter
    those results based on the specified criteria.

    Attributes:
        min_market_cap (float): The minimum market cap threshold (in USD).
        max_market_cap (float): The maximum market cap threshold (in USD).
        allowed_ticker_types (Set[TickerType]): Set of ticker types to include.
        blocklist (Set[str]): Set of ticker symbols that should always be excluded.
        delegate_criteria (IScanCriteria): Delegate scan criteria to filter first.
        cached_ticker_info_df (pd.DataFrame): Cached ticker information loaded once during initialization.
    """

    def __init__(
        self,
        delegate_criteria: IScanCriteria,
        min_market_cap: float = 0.0,
        max_market_cap: float = float("inf"),
        allowed_ticker_types: Optional[Set[TickerType]] = None,
        blocklist: Optional[Set[str]] = None,
    ) -> None:
        """Initialize the TickerInfoScanCriteria.

        Args:
            delegate_criteria: Scan criteria to apply before filtering.
            min_market_cap: The minimum market cap threshold (in USD).
            max_market_cap: The maximum market cap threshold (in USD).
            allowed_ticker_types: Set of ticker types to include. If None, all types are allowed.
            blocklist: Set of ticker symbols that should always be excluded, regardless of other criteria.
        """
        self.delegate_criteria = delegate_criteria
        self.min_market_cap = min_market_cap
        self.max_market_cap = max_market_cap
        self.allowed_ticker_types = allowed_ticker_types
        # Normalize blocklist to uppercase for consistent comparison
        self.blocklist: Set[str] = {ticker.upper() for ticker in blocklist} if blocklist else set()

        # Load cached ticker data once during initialization
        self.cached_ticker_info_df = load_cached_ticker_data()

    def scan(self, historical_df: pd.DataFrame, current_day_df: Optional[pd.DataFrame] = None) -> List[str]:
        """Scan tickers based on market cap range, type criteria, and blocklist.

        This method will first apply the delegate's scan method and then filter
        those results based on market cap, allowed types, and blocklisted tickers.

        Args:
            historical_df: DataFrame with historical OHLCV data
            current_day_df: Optional DataFrame with current day's OHLCV data

        Returns:
            List of ticker symbols that match both the delegate criteria
            and the specified market cap, type criteria, excluding any blocklisted symbols.
        """
        # Determine tickers to filter
        if self.delegate_criteria is None:
            # No delegate: use all symbols from current_day_df
            if current_day_df is None or current_day_df.empty:
                logger.info("No delegate criteria and no current day data, skipping scan")
                return []
            tickers_to_filter = current_day_df["symbol"].unique().tolist()
            logger.info(
                "No delegate criteria provided, using all symbols from current_day_df: %d",
                len(tickers_to_filter),
            )
        else:
            # Delegate present: defer to it
            tickers_to_filter = self.delegate_criteria.scan(historical_df, current_day_df)
            logger.info("Got tickers from delegate criteria %s", tickers_to_filter)

        if not tickers_to_filter:
            return []  # If no tickers to filter, bail out

        # Remove blocklisted tickers immediately
        tickers_to_filter = [t for t in tickers_to_filter if t.upper() not in self.blocklist]
        if not tickers_to_filter:
            logger.info("All tickers were excluded by blocklist.")
            return []
        
        # Evaluate each ticker individually: fail open if missing from cache or missing fields, else apply filters
        matching_tickers = []
        for ticker in tickers_to_filter:
            # Attempt to fetch cached info for ticker
            rows = self.cached_ticker_info_df[self.cached_ticker_info_df["ticker"] == ticker]
            if rows.empty:
                # Ticker not in cached data: exclude by default
                continue
            info = rows.iloc[0]

            # Type check: enforce if allowed types specified and type present, else include
            if self.allowed_ticker_types is not None:
                ttype = info.get("type", None)
                if pd.notna(ttype):
                    allowed_type_values = {t.value for t in self.allowed_ticker_types}
                    if ttype not in allowed_type_values:
                        continue

            # Market cap check: enforce if present, else include
            mc = info.get("market_cap", None)
            if pd.isna(mc):
                logger.info("Ticker %s: market cap missing, including by default", ticker)
            elif mc < self.min_market_cap or mc > self.max_market_cap:
                continue

            matching_tickers.append(ticker)

        logger.info("Tickers after filtering criteria and blocklist %s..", matching_tickers[:10])
        return matching_tickers
