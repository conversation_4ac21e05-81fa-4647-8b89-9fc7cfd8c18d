from datetime import datetime
from typing import Optional, List
import pandas as pd

from tickers.tickersource import ITickerSource

class StaticTickerSource(ITickerSource):
    """
    A static ticker source that returns a list of ticker symbols provided to it.

    The get_tickers method returns a DataFrame containing:
      - 'ticker': The ticker symbol.
      - 'timestamp': A timestamp corresponding to the provided start datetime.
      - 'created_at': The same value as 'timestamp'.

    This is useful for backtesting scenarios where you need a fixed set of tickers.
    """

    def __init__(self, tickers: List[str]) -> None:
        """
        Initialize the StaticTickerSource with a list of ticker symbols.

        Parameters:
            tickers (List[str]): A list of ticker symbol strings.
        """
        self.tickers = tickers

    def get_tickers(self, start: datetime, end: Optional[datetime] = None) -> pd.DataFrame:
        """
        Returns a DataFrame with the static tickers and the start datetime as the timestamp.

        Parameters:
            start (datetime): The start datetime used for the ticker timestamp.
            end (Optional[datetime]): Ignored in this static implementation.

        Returns:
            pd.DataFrame: A DataFrame with columns 'ticker', 'timestamp', and 'created_at'.
        """
        data = {
            "ticker": self.tickers,
            "timestamp": [start] * len(self.tickers),
            "created_at": [start] * len(self.tickers)
        }
        return pd.DataFrame(data) 