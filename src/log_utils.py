import logging
import os
import pytz
from datetime import datetime

class TimezoneFormatter(logging.Formatter):
    """Custom formatter that supports timezone-aware timestamps."""
    
    def __init__(self, fmt=None, datefmt=None, timezone='UTC'):
        """
        Initialize formatter with specified format and timezone.
        
        Args:
            fmt (str): Log format string
            datefmt (str): Date format string
            timezone (str): Timezone name (e.g., 'US/Eastern', 'UTC', 'Europe/London')
        """
        super().__init__(fmt, datefmt)
        self.timezone = pytz.timezone(timezone)
    
    def converter(self, timestamp):
        """Convert timestamp to timezone-aware datetime."""
        dt = datetime.fromtimestamp(timestamp)
        return dt.astimezone(self.timezone)
    
    def formatTime(self, record, datefmt=None):
        """Format the time with timezone information."""
        dt = self.converter(record.created)
        if datefmt:
            return dt.strftime(datefmt)
        return dt.strftime("%Y-%m-%d %H:%M:%S %Z")

def configure_basic_logging(
    level=logging.INFO,
    format_str='[%(asctime)s] %(levelname)s - %(name)s - %(message)s',
    date_format=None,
    timezone='US/Eastern',
    clear_existing=True,
    log_file=None
):
    """
    Configure the basic logging for the entire application.
    
    Args:
        level (int): Logging level
        format_str (str): Log format string
        date_format (str): Date format string
        timezone (str): Timezone name (e.g., 'US/Eastern', 'UTC')
        clear_existing (bool): Whether to clear existing handlers
        log_file (str): Optional path to a log file. If provided, logs will be written to this file
                        in addition to the console.
        
    Returns:
        None
    """
    # Clear existing handlers if requested
    if clear_existing:
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
    
    # Create a list to store handlers
    handlers = []
    
    # Create console handler
    console_handler = logging.StreamHandler()
    formatter = TimezoneFormatter(format_str, date_format, timezone)
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)
    
    # Create file handler if log_file is specified
    if log_file:
        # Create the directory if it doesn't exist
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        # Create file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)  # Use the same formatter
        handlers.append(file_handler)
    
    # Configure basic logging with all handlers
    logging.basicConfig(level=level, handlers=handlers)

def create_order_logger(
    prefix: str,
    log_dir: str = "output/logs",
    timezone: str = "US/Eastern"
) -> logging.Logger:
    """
    Create and configure a file-logger for order events using EST timestamps.
    """
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f"{prefix}_orders.log")
    logger = logging.getLogger(f"{__name__}.order_{prefix}")
    logger.setLevel(logging.INFO)
    # Use timezone-aware formatter in EST
    formatter = TimezoneFormatter("%(asctime)s %(levelname)s %(message)s", None, timezone)
    fh = logging.FileHandler(log_file)
    fh.setFormatter(formatter)
    logger.addHandler(fh)
    logger.propagate = False
    return logger