import asyncio
import argparse
import logging
from datetime import datetime, timedelta
import pandas as pd
import pytz
import websockets
import json
import tabulate

from marketdata.theta_market_data import ThetaMarketData, ThetaVenue
from marketdata.thetadata_streaming import ThetaDataStreamingMarketData
from tools.clock import Clock

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize global variables for data storage
streaming_data = pd.DataFrame()
polling_data = pd.DataFrame()

async def compare_streaming_vs_polling(ticker, interval_minutes, lookback_hours):
    """
    Run both streaming and polling market data providers simultaneously and compare their outputs.
    """
    # Initialize clock
    clock = Clock()
    
    # Set up market data providers
    market_data_provider = ThetaMarketData(rth=True, venue=ThetaVenue.NQB)
    
    # Create streaming instance
    global streaming_market_data
    streaming_market_data = ThetaDataStreamingMarketData(
        market_data_provider=market_data_provider,
        clock=clock,
        use_polling=False  # Use WebSocket streaming
    )
    # Add attribute to store latest bar
    streaming_market_data.latest_bar = pd.DataFrame()
    
    # Create polling instance
    global polling_market_data
    polling_market_data = ThetaDataStreamingMarketData(
        market_data_provider=market_data_provider,
        clock=clock,
        use_polling=True,  # Use polling
        polling_interval=timedelta(seconds=30)  # Poll every 30 seconds
    )
    # Add attribute to store latest bar
    polling_market_data.latest_bar = pd.DataFrame()
    
    # Convert interval and lookback to timedelta
    interval = timedelta(minutes=interval_minutes)
    lookback_window = timedelta(hours=lookback_hours)
    
    # Current time as start time
    start_time = clock.now()
    
    logger.info(f"Starting comparison between streaming and polling for {ticker}")
    logger.info(f"Interval: {interval_minutes} minutes")
    logger.info(f"Lookback window: {lookback_hours} hours")
    logger.info(f"Start time: {start_time}")
    
    # Start both streams concurrently
    streaming_task = asyncio.create_task(
        run_streaming(streaming_market_data, ticker, interval, start_time, lookback_window)
    )
    
    polling_task = asyncio.create_task(
        run_polling(polling_market_data, ticker, interval, start_time, lookback_window)
    )
    
    # Wait for both tasks to complete (they'll run until interrupted)
    try:
        await asyncio.gather(streaming_task, polling_task)
    except asyncio.CancelledError:
        streaming_task.cancel()
        polling_task.cancel()
        logger.info("Tasks cancelled")
    finally:
        logger.info("Comparison completed")

async def run_streaming(market_data, ticker, interval, start_time, lookback_window):
    """Run the streaming market data provider and store results."""
    global streaming_data
    
    try:
        async for historical_bars, latest_bar in market_data.subscribe_to_bars(
            ticker=ticker,
            interval=interval,
            start_time=start_time,
            lookback_window=lookback_window
        ):
            # Store the latest data
            streaming_data = historical_bars
            # Store the latest bar for comparison
            streaming_market_data.latest_bar = latest_bar
    except Exception as e:
        logger.error(f"Error in streaming: {e}")

async def run_polling(market_data, ticker, interval, start_time, lookback_window):
    """Run the polling market data provider and store results."""
    global polling_data
    
    try:
        async for historical_bars, latest_bar in market_data.subscribe_to_bars(
            ticker=ticker,
            interval=interval,
            start_time=start_time,
            lookback_window=lookback_window
        ):
            # Store the latest data
            polling_data = historical_bars
            # Store the latest bar for comparison
            polling_market_data.latest_bar = latest_bar
            
            # Compare with streaming data if available
            await compare_and_print_results()
    except Exception as e:
        logger.error(f"Error in polling: {e}")

async def compare_and_print_results():
    """Compare streaming vs polling data for the most recent bars."""
    global streaming_data, polling_data
    
    # Check if both datasets are available
    if streaming_data.empty and polling_data.empty:
        return
    
    # Get latest bars
    streaming_latest = getattr(streaming_market_data, 'latest_bar', pd.DataFrame())
    polling_latest = getattr(polling_market_data, 'latest_bar', pd.DataFrame())
    
    # Combine historical and latest bars for streaming
    combined_streaming = streaming_data.copy()
    if not streaming_latest.empty:
        # Add latest bar to historical data (will override if timestamp already exists)
        for idx in streaming_latest.index:
            combined_streaming.loc[idx] = streaming_latest.loc[idx]
    
    # Combine historical and latest bars for polling
    combined_polling = polling_data.copy()
    if not polling_latest.empty:
        # Add latest bar to historical data (will override if timestamp already exists)
        for idx in polling_latest.index:
            combined_polling.loc[idx] = polling_latest.loc[idx]
    
    # Get all unique timestamps from both datasets
    all_timestamps = sorted(set(combined_streaming.index) | set(combined_polling.index))
    
    # Limit to the 5 most recent timestamps
    recent_timestamps = all_timestamps[-5:] if len(all_timestamps) > 5 else all_timestamps
    
    # Print the comparison table
    now = datetime.now(tz=pytz.timezone('America/New_York'))
    print("\n" + "="*100)
    print(f"Streaming vs Polling Comparison at {now.strftime('%Y-%m-%d %H:%M:%S')} (Most Recent 5 Bars)")
    print("="*100)
    
    # Format the table for better readability
    table_data = []
    headers = ["Timestamp", "Stream Open", "Stream High", "Stream Low", "Stream Close", "Stream Vol", 
               "Poll Open", "Poll High", "Poll Low", "Poll Close", "Poll Vol", "Diff Close"]
    
    # Compare bars with matching timestamps
    for ts in recent_timestamps:
        try:
            # Get data for this timestamp from both sources (if available)
            stream_data = combined_streaming.loc[ts] if ts in combined_streaming.index else None
            poll_data = combined_polling.loc[ts] if ts in combined_polling.index else None
            
            # Format timestamp - handle NaT values
            if pd.isna(ts):
                ts_str = "NaT"
            else:
                try:
                    ts_str = ts.strftime('%H:%M:%S') if hasattr(ts, 'strftime') else str(ts)
                except (ValueError, AttributeError):
                    ts_str = str(ts)
            
            # Create row with data from both sources
            row = [ts_str]
            
            # Add streaming data
            if stream_data is not None:
                # Check if stream_data is a Series (single row) or DataFrame (multiple rows)
                if isinstance(stream_data, pd.Series):
                    row.extend([
                        f"{stream_data['open']:.2f}" if 'open' in stream_data.index and not pd.isna(stream_data['open']) else "N/A",
                        f"{stream_data['high']:.2f}" if 'high' in stream_data.index and not pd.isna(stream_data['high']) else "N/A",
                        f"{stream_data['low']:.2f}" if 'low' in stream_data.index and not pd.isna(stream_data['low']) else "N/A",
                        f"{stream_data['close']:.2f}" if 'close' in stream_data.index and not pd.isna(stream_data['close']) else "N/A",
                        f"{stream_data['volume']}" if 'volume' in stream_data.index and not pd.isna(stream_data['volume']) else "N/A"
                    ])
                else:
                    # Handle DataFrame case
                    if not stream_data.empty:
                        row.extend([
                            f"{stream_data.iloc[0]['open']:.2f}" if 'open' in stream_data.columns and not pd.isna(stream_data.iloc[0]['open']) else "N/A",
                            f"{stream_data.iloc[0]['high']:.2f}" if 'high' in stream_data.columns and not pd.isna(stream_data.iloc[0]['high']) else "N/A",
                            f"{stream_data.iloc[0]['low']:.2f}" if 'low' in stream_data.columns and not pd.isna(stream_data.iloc[0]['low']) else "N/A",
                            f"{stream_data.iloc[0]['close']:.2f}" if 'close' in stream_data.columns and not pd.isna(stream_data.iloc[0]['close']) else "N/A",
                            f"{stream_data.iloc[0]['volume']}" if 'volume' in stream_data.columns and not pd.isna(stream_data.iloc[0]['volume']) else "N/A"
                        ])
                    else:
                        row.extend(["N/A", "N/A", "N/A", "N/A", "N/A"])
            else:
                row.extend(["N/A", "N/A", "N/A", "N/A", "N/A"])
            
            # Add polling data
            if poll_data is not None:
                # Check if poll_data is a Series (single row) or DataFrame (multiple rows)
                if isinstance(poll_data, pd.Series):
                    row.extend([
                        f"{poll_data['open']:.2f}" if 'open' in poll_data.index and not pd.isna(poll_data['open']) else "N/A",
                        f"{poll_data['high']:.2f}" if 'high' in poll_data.index and not pd.isna(poll_data['high']) else "N/A",
                        f"{poll_data['low']:.2f}" if 'low' in poll_data.index and not pd.isna(poll_data['low']) else "N/A",
                        f"{poll_data['close']:.2f}" if 'close' in poll_data.index and not pd.isna(poll_data['close']) else "N/A",
                        f"{poll_data['volume']}" if 'volume' in poll_data.index and not pd.isna(poll_data['volume']) else "N/A"
                    ])
                else:
                    # Handle DataFrame case
                    if not poll_data.empty:
                        row.extend([
                            f"{poll_data.iloc[0]['open']:.2f}" if 'open' in poll_data.columns and not pd.isna(poll_data.iloc[0]['open']) else "N/A",
                            f"{poll_data.iloc[0]['high']:.2f}" if 'high' in poll_data.columns and not pd.isna(poll_data.iloc[0]['high']) else "N/A",
                            f"{poll_data.iloc[0]['low']:.2f}" if 'low' in poll_data.columns and not pd.isna(poll_data.iloc[0]['low']) else "N/A",
                            f"{poll_data.iloc[0]['close']:.2f}" if 'close' in poll_data.columns and not pd.isna(poll_data.iloc[0]['close']) else "N/A",
                            f"{poll_data['volume']}" if 'volume' in poll_data.index and not pd.isna(poll_data['volume']) else "N/A"
                        ])
                    else:
                        row.extend(["N/A", "N/A", "N/A", "N/A", "N/A"])
            else:
                row.extend(["N/A", "N/A", "N/A", "N/A", "N/A"])
            
            # Add difference in close price if both are available
            stream_close = None
            poll_close = None
            
            if stream_data is not None:
                if isinstance(stream_data, pd.Series):
                    if 'close' in stream_data.index and not pd.isna(stream_data['close']):
                        stream_close = stream_data['close']
                elif not stream_data.empty and 'close' in stream_data.columns and not pd.isna(stream_data.iloc[0]['close']):
                    stream_close = stream_data.iloc[0]['close']
            
            if poll_data is not None:
                if isinstance(poll_data, pd.Series):
                    if 'close' in poll_data.index and not pd.isna(poll_data['close']):
                        poll_close = poll_data['close']
                elif not poll_data.empty and 'close' in poll_data.columns and not pd.isna(poll_data.iloc[0]['close']):
                    poll_close = poll_data.iloc[0]['close']
            
            if stream_close is not None and poll_close is not None:
                row.append(f"{stream_close - poll_close:.4f}")
            else:
                row.append("N/A")
            
            table_data.append(row)
        except Exception as e:
            logger.warning(f"Error processing bar at {ts}: {e}")
            continue
    
    # Print the table
    print(tabulate.tabulate(table_data, headers=headers, tablefmt="grid"))
    print("="*100 + "\n")

async def test_streaming_market_data(ticker, interval_minutes, lookback_hours):
    # Initialize clock
    clock = Clock()
    
    # Set up live market data
    market_data_provider = ThetaMarketData(rth=True, venue=ThetaVenue.NQB)
    live_market_data = ThetaDataStreamingMarketData(
        market_data_provider=market_data_provider,
        clock=clock,
    )
    
    # Convert interval and lookback to timedelta
    interval = timedelta(minutes=interval_minutes)
    lookback_window = timedelta(hours=lookback_hours)
    
    # Current time as start time
    start_time = clock.now()
    
    logger.info(f"Starting streaming market data test for {ticker}")
    logger.info(f"Interval: {interval_minutes} minutes")
    logger.info(f"Lookback window: {lookback_hours} hours")
    logger.info(f"Start time: {start_time}")
    
    try:
        # Subscribe to bars
        async for historical_bars, latest_bar in live_market_data.subscribe_to_bars(
            ticker=ticker,
            interval=interval,
            start_time=start_time,
            lookback_window=lookback_window
        ):
            # Print information about the data received
            logger.info(f"Received data for {ticker}")
            logger.info(f"Historical bars shape: {historical_bars.shape}")
            logger.info(f"Latest bar timestamp: {latest_bar.index[0] if not latest_bar.empty else 'N/A'}")
            
            # Print the latest bar data
            if not latest_bar.empty:
                logger.info(f"Latest bar data: {latest_bar.iloc[0].to_dict()}")
            
            # Wait a bit before processing the next update
            await asyncio.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Error during streaming: {e}")
    finally:
        # Clean up resources if needed
        logger.info("Test completed")

async def stop_all_streams():
    """
    Connect to the Theta Data WebSocket server and send a STOP message to terminate all streams.
    
    First waits for the initial CONNECTED status message, then sends the STOP request,
    and finally waits for confirmation that the STOP request was processed successfully.
    """
    websocket_url = 'ws://127.0.0.1:25520/v1/events'
    
    try:
        logger.info("Connecting to Theta Data WebSocket server to stop all streams...")
        async with websockets.connect(websocket_url) as websocket:
            # First, wait for the initial connection status message
            try:
                initial_response = await asyncio.wait_for(websocket.recv(), timeout=5)
                logger.info(f"Received initial connection message: {initial_response}")
                
                # Verify it's the expected connection message
                initial_data = json.loads(initial_response)
                if not (initial_data.get('header', {}).get('type') == 'STATUS' and 
                        initial_data.get('header', {}).get('status') == 'CONNECTED'):
                    logger.warning(f"Unexpected initial message: {initial_data}")
                    return False
                
                logger.info("Successfully connected to WebSocket server")
                
                # Now that we're connected, send the STOP request
                stop_request = {
                    "msg_type": "STOP"
                }
                
                await websocket.send(json.dumps(stop_request))
                logger.info("STOP message sent successfully")
                
                # Wait for confirmation response to the STOP request
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                logger.info(f"Received response to STOP request: {response}")
                
                # Parse the response to check for success
                response_data = json.loads(response)
                if (response_data.get('header', {}).get('type') == 'REQ_RESPONSE' and 
                    response_data.get('header', {}).get('response') == 'SUCCESS'):
                    logger.info("Server confirmed successful termination of all streams")
                    return True
                else:
                    logger.warning(f"Unexpected response format: {response_data}")
                    # Still return True if we got any response, as the server likely processed the request
                    return True
                    
            except asyncio.TimeoutError as e:
                logger.warning(f"Timeout waiting for server response: {e}")
                return False
            except json.JSONDecodeError as e:
                logger.warning(f"Received invalid JSON response: {e}")
                return False
                
    except Exception as e:
        logger.error(f"Error stopping streams: {e}")
        return False

def parse_arguments():
    parser = argparse.ArgumentParser(description='Test Streaming Market Data')
    parser.add_argument('--ticker', type=str, default='QQQ', help='Ticker symbol to test (default: QQQ)')
    parser.add_argument('--interval', type=int, default=1, help='Bar interval in minutes (default: 1)')
    parser.add_argument('--lookback', type=int, default=1, help='Lookback window in hours (default: 1)')
    parser.add_argument('--stop-streams', action='store_true', help='Stop all active streams and exit')
    parser.add_argument('--compare', action='store_true', help='Compare streaming vs polling OHLCV data')
    return parser.parse_args()

async def main():
    args = parse_arguments()
    
    if args.stop_streams:
        success = await stop_all_streams()
        if success:
            logger.info("Successfully stopped all streams")
        else:
            logger.error("Failed to stop all streams")
        return
    
    if args.compare:
        await compare_streaming_vs_polling(
            ticker=args.ticker,
            interval_minutes=args.interval,
            lookback_hours=args.lookback
        )
    else:
        await test_streaming_market_data(
            ticker=args.ticker,
            interval_minutes=args.interval,
            lookback_hours=args.lookback
        )

if __name__ == "__main__":
    import tabulate
    asyncio.run(main())