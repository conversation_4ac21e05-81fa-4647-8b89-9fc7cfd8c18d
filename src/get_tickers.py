from polygon import RESTClient
import pandas as pd
from dotenv import load_dotenv
import os
from polygon.exceptions import BadResponse
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed

from tickers.ticker_helpers import get_ticker_set, load_cached_ticker_data, cache_ticker_data
import logging

logger = logging.getLogger(__name__)
load_dotenv()

POLYGON_API_KEY = os.getenv("POLYGON_API_KEY")

# Create the REST client
client = RESTClient(POLYGON_API_KEY)

def get_smallcap_tickers(min_market_cap=0, max_market_cap=1e7, date=None):
    """
    Retrieves a list of US tickers and filters them by market cap.
    
    Args:
        min_market_cap (float): Minimum market cap filter.
        max_market_cap (float): Maximum market cap filter.
        date (date): The date to fetch data for. If None, uses latest data.
    """
    # Check if we have cached data for this date and apply market cap filtering.
    cache_dir = "cache"
    os.makedirs(cache_dir, exist_ok=True)
    
    if date:
        df_cache = load_cached_ticker_data(date)
        if not df_cache.empty:
            if 'active' in df_cache.columns:
                df_cache = df_cache[df_cache['active']]
            if 'market_cap' in df_cache.columns:
                df_filtered = df_cache[
                    df_cache['market_cap'].notna() &
                    (df_cache['market_cap'] >= min_market_cap) &
                    (df_cache['market_cap'] <= max_market_cap)
                ]
            else:
                df_filtered = df_cache
            if not df_filtered.empty:
                return df_filtered["ticker"].tolist()

    # Step 1: Get all stock tickers with pagination
    all_tickers = []
    params = {
        "market": "stocks", 
        "limit": 1000, 
        "active": None, 
        "sort": "ticker", 
        "order": "asc"
    }
    
    if date:
        params["date"] = date.strftime("%Y-%m-%d")
    
    while batch := list(client.list_tickers(**params)):
        all_tickers.extend(batch)
        params["ticker_gt"] = batch[-1].ticker

    # Step 2: Fetch additional ticker details in parallel
    ticker_data = []

    def fetch_details(t):
        try:
            params = {"ticker": t.ticker}
            if date:
                params["date"] = date.strftime("%Y-%m-%d")
            details = client.get_ticker_details(**params)
            # Store all details as a dictionary.
            details_dict = {k: v for k, v in details.__dict__.items() if not k.startswith('_')}
            details_dict['ticker'] = t.ticker
            return details_dict
        except BadResponse as e:
            logger.debug(f"Skipping ticker {t.ticker}: Not found")
        except Exception as e:
            logger.error(f"Skipping ticker {t.ticker}: Unexpected error - {str(e)}")
        return None

    max_workers = 20
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_ticker = {executor.submit(fetch_details, t): t for t in all_tickers}
        for future in tqdm(as_completed(future_to_ticker), total=len(future_to_ticker), desc="Fetching ticker details"):
            result = future.result()
            if result is not None:
                ticker_data.append(result)

    # Cache results if a date was specified.
    if date and ticker_data:
        df = pd.DataFrame(ticker_data)
        cache_ticker_data(df, date)

    # Final filtering based on market cap (and active status)
    filtered_tickers = [
        t["ticker"] for t in ticker_data 
        if (market_cap := t.get("market_cap")) is not None 
           and min_market_cap <= market_cap <= max_market_cap 
           and t.get("active", False)
    ]

    return filtered_tickers


if __name__ == "__main__":
    from datetime import date

    tickers = get_smallcap_tickers(
        min_market_cap=2_000_000,  # 2M
        max_market_cap=10_000_000_000, # 10B
        date=date(2024, 12, 31)
    )

    logger.info(f"Found {len(tickers)} small cap tickers from 50M to 500M market cap:")
    logger.info(tickers)
    import IPython; IPython.embed()