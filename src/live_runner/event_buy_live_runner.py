import logging
from log_utils import configure_basic_logging

configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

import asyncio
from datetime import datetime, timedelta
import os
from zoneinfo import ZoneInfo

from brokers.local_broker import LocalBroker
from brokers.schwab_broker import SchwabBroker
from strategies.event_buy_strategy import EventBuyStrategy
from tickers.filtering_ticksource import FilteringTickSource, TickerType
from tickers.ticker_source_discover_adapter import TickerSourceDiscoverAdapter
from tickers.substack_ticker_source import SubstackTickerSource
from marketdata.schwab_market_data import SchwabStreamingMarketData
from tools.clock import Clock
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from position_manager import PositionManager
from control_plane.runner_helper import attach_control_plane
import pytz

import dotenv

INITIAL_CAPITAL = 10000
MAX_POSITION_SIZE = 0.1 # 1K

# Import MarketDataBuilder
from marketdata.market_data_builder import MarketDataBuilder

dotenv.load_dotenv()
dotenv.load_dotenv(dotenv_path=".env.citrini", override=True)

async def run_live(substack_chat_id: str, 
                  poll_interval: float = 60.0):
    """
    Run the TickerEventBuyStrategy in live mode using tickers discovered from Substack.
    
    Args:
        substack_chat_id: The Substack chat ID to monitor for ticker events
        poll_interval: How often to poll Substack for new tickers (in seconds)
    """
    # Initialize clock
    clock = Clock()
    
    # Initialize Schwab broker and HTTP client for streaming market data
    schwab_broker = SchwabBroker(token_path="./schwab_token_2.json")
    await schwab_broker.connect()
    http_client = await schwab_broker.get_http_client()

    # Set up streaming market data using Schwab
    live_data_builder = (MarketDataBuilder(clock=clock)
                          .with_trade_session('full'))
    live_market_data = live_data_builder.build_streaming_market_data()
    market_data = live_data_builder.build_market_data()

    substack_source = SubstackTickerSource(
        chat_id=substack_chat_id,
        rate_limit=1,
        cookie=os.getenv('SUBSTACK_COOKIE')
    )
    
    filter_source = FilteringTickSource(
        ticker_source=substack_source,
        allowed_ticker_types={TickerType.COMMON_STOCK}
    )
    
    # Wrap the ticker source with the adapter
    ticker_discover = TickerSourceDiscoverAdapter(
        ticker_source=filter_source,
        poll_interval=poll_interval,
        overlap_window=timedelta(minutes=15)
    )
    
    # Initialize strategy with the specified parameters
    strategy_factory = lambda: EventBuyStrategy(
        # default parameters
    )
    
    # Initialize position manager and broker
    position_manager = PositionManager(
        max_positions=10,
        risk_per_position=MAX_POSITION_SIZE,
        initial_capital=INITIAL_CAPITAL,
        max_position_size=MAX_POSITION_SIZE
    )
    

    # Broker setup now uses the market_data_provider from the builder
    local_broker = LocalBroker(
        market_data=market_data,
        initial_capital=INITIAL_CAPITAL,
        clock=clock,
        delegate_broker=schwab_broker
    )

    # Attach Control-Plane services
    cp_broker, cp_cleanup = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="ticker_event_buy_strategy",
        strategy_params={},
        trade_session="rth",
    )
    
    orchestrator = ExecutionOrchestrator(
        market_data=live_market_data,
        ticker_discover=ticker_discover,
        strategy_factory=strategy_factory,
        broker=cp_broker,
        position_manager=position_manager
    )
    
    logger.info(f"Starting live trading with Substack chat ID: {substack_chat_id}")
    logger.info(f"Polling Substack every {poll_interval} seconds")
    
    # Run the orchestrator in live mode
    await orchestrator.execute()
    
    # Cleanup tasks
    await orchestrator.stop()
    await cp_cleanup()
    
    logger.info("Live trading completed.")

async def main():    
    substack_chat_id = "836125"
    await run_live(
        substack_chat_id=substack_chat_id,
        poll_interval=60.0
    )

if __name__ == "__main__":
    asyncio.run(main()) 