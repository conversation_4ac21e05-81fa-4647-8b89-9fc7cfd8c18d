import logging
from log_utils import configure_basic_logging

configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

import asyncio
from datetime import datetime, timedelta
import pytz
import typing

from marketdata.theta_splicing_market_data import ThetaSplicingMarketData
from marketdata.theta_market_data import ThetaMarketData, ThetaVenue
from marketdata.thetadata_streaming import ThetaDataStreamingMarketData
from strategies.orb_strategy import ORBStrategy
from tickers.preset_ticker_discover import PresetTickerDiscover
from tools.clock import Clock
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from brokers.alpaca_broker import AlpacaBroker
from brokers.local_broker import LocalBroker
from brokers.schwab_broker import <PERSON>hwab<PERSON>roker
from position_manager import PositionManager
from control_plane.runner_helper import attach_control_plane
from marketdata.imarketdata import IMarketData
from marketdata.market_data_builder import MarketDataBuilder

import dotenv
dotenv.load_dotenv()

async def run_live():
    # Initialize clock for backtesting
    clock = Clock()
    
    # Set up market data using the builder (defaults to Polygon)
    live_data = (MarketDataBuilder(clock=clock)
                 .with_trade_session('rth') # Keep rth session
                 .build_streaming_market_data())
    
    # Test with a single ticker
    # Initialize ticker source with the provided tickers
    ticker_source = PresetTickerDiscover(tickers=["TQQQ"], start_time=clock.now())
    
    position_manager = PositionManager(max_positions=10, risk_per_position=0.01, initial_capital=10000, max_position_size=1)
    
    # Create real broker (Alpaca)
    schwab_broker = SchwabBroker()
    await schwab_broker.connect()
    
    # Create local broker with delegation to Alpaca
    local_broker = LocalBroker(
        market_data=live_data,
        initial_capital=10000,
        clock=clock,
        delegate_broker=schwab_broker
    )

    # Attach Control-Plane services
    cp_broker, cp_cleanup = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="orb_strategy",
        strategy_params={},
        trade_session="rth",
    )
    
    # Create the execution orchestrator with the Control-Plane broker
    orchestrator = ExecutionOrchestrator(
        market_data=live_data,
        ticker_discover=ticker_source,
        strategy_factory=lambda: ORBStrategy(),
        broker=cp_broker,
        position_manager=position_manager
    )
    
    logger.info("Starting live production mode for the ORB Strategy...")
    
    # Run the main execution
    await orchestrator.execute()
    
    # Cleanup task
    await orchestrator.stop()
    await cp_cleanup()

async def main():
    # Get tickers
    await run_live()

if __name__ == "__main__":
    asyncio.run(main())