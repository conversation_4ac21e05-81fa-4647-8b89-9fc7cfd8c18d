import os
import socket
import asyncio
import logging
from datetime import timedelta

from log_utils import configure_basic_logging

from strategies.example_strategy import ExampleStrategy
from tickers.preset_ticker_discover import PresetTickerDiscover
from tools.clock import Clock
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from brokers.local_broker import LocalBroker
from position_manager import PositionManager
from control_plane.runner_helper import attach_control_plane
from marketdata.synthetic_market_data import SyntheticMarketData, SyntheticPriceModelConfig

import dotenv


configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

dotenv.load_dotenv()

INITIAL_CAPITAL = 10_000


async def run_live():
    # Use a real-time clock; synthetic generator will pace itself
    clock = Clock()

    # Synthetic market data that streams forever
    model = SyntheticPriceModelConfig(
        base_price=100.0,
        drift_per_bar=0.0,
        volatility_pct=0.0025,  # ~0.25% per bar
        mean_reversion_strength=0.001,
        seed=42,
    )
    # 60 "bar seconds" per wall second => 1-minute bars emitted every 1s
    live_market_data = SyntheticMarketData(clock=clock, model=model, realtime_speed=1.0) # 1.0 = realtime

    # Strategy and discovery
    strategy_factory = lambda: ExampleStrategy()
    ticker_source = PresetTickerDiscover(tickers=["SPY", "QQQ"], start_time=clock.now())

    # Position manager and brokers
    position_manager = PositionManager(initial_capital=INITIAL_CAPITAL, max_position_size=1)

    local_broker = LocalBroker(
        market_data=live_market_data,
        initial_capital=INITIAL_CAPITAL,
        clock=clock,
    )

    # Attach Control-Plane services (including streaming MD health)
    cp_broker, cp_cleanup, cp_market_data = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="synthetic_example",
        strategy_params={},
        trade_session="full",
        streaming_market_data=live_market_data,
    )

    orchestrator = ExecutionOrchestrator(
        market_data=cp_market_data,
        ticker_discover=ticker_source,
        strategy_factory=strategy_factory,
        broker=cp_broker,
        position_manager=position_manager,
    )

    logger.info("Starting synthetic live runner for ExampleStrategy...")

    try:
        await orchestrator.execute()
    finally:
        await orchestrator.stop()
        await cp_cleanup()


async def main():
    await run_live()


if __name__ == "__main__":
    asyncio.run(main())


