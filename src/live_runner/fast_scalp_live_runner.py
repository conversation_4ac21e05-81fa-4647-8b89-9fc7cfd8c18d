import dotenv
from position_manager import PositionManager
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from tools.clock import Clock
from tickers.ticker_info_scan_criteria import TickerInfoScanCriteria, TickerType
from tickers.ticker_discover_using_scan_criteria import TickerDiscoverUsingScanCriteria
from strategies.premarket_gap_scanner import PreMarketGapScanner
from marketdata.market_data_builder import MarketDataBuilder
from datetime import datetime
from dateutil.relativedelta import relativedelta
import asyncio
import logging
from log_utils import configure_basic_logging
from strategies.fast_scalp_strategy import FastScalpStrategy

from ib_async import IB
from brokers.ibkr_broker import IBKRBrokerExtended
from brokers.updating_broker import UpdatingIBroker
from control_plane.runner_helper import attach_control_plane

configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

dotenv.load_dotenv()

INITIAL_CAPITAL = 10000
POSITION_SIZE = 0.05

async def run_live():
    clock = Clock()

    # Set up market data using the builder (Polygon streaming, full session)
    live_data_builder = (MarketDataBuilder(clock=clock)
                          .with_trade_session('full'))
    
    market_data = live_data_builder.build_market_data()
    live_market_data = live_data_builder.build_streaming_market_data()
    
    # Initialize strategy - will be used both as strategy and scan criteria
    def strategy_factory():
        return FastScalpStrategy()

    # Set up components for ticker discovery
    # Use MarketDataBuilder for disk data with a start_date ~2 months ago
    two_months_ago = datetime.now() - relativedelta(months=2)
    disk_market_data = (MarketDataBuilder() # Create new disk data with builder
                          .with_disk_data(start_date=two_months_ago)
                          .build_market_data())
    bulk_ohlc = live_data_builder.build_bulk_ohlc()

    market_cap_criteria = TickerInfoScanCriteria(min_market_cap=2_000_000,  # 2M
                                                 max_market_cap=1_000_000_000,  # 1B
                                                 allowed_ticker_types={TickerType.COMMON_STOCK},
                                                 delegate_criteria=PreMarketGapScanner(),
                                                 blocklist=set([]))

    # Initialize ticker discovery with our ParabolicShortStrategy as criteria
    ticker_source = TickerDiscoverUsingScanCriteria(
        disk_market_data=disk_market_data,
        theta_bulk_ohlc=bulk_ohlc,
        criteria=market_cap_criteria,
        max_run_hours=12
    )

    # Initialize position manager and broker
    position_manager = PositionManager(
        max_positions=10, risk_per_position=1, initial_capital=INITIAL_CAPITAL, max_position_size=POSITION_SIZE)

    # Create IBKR broker
    ib = IB()
    await ib.connectAsync('127.0.0.1', 8496, clientId=1001) # Live 8496, clientId=1245
    print("Connected to IB")

    ibkr_broker = IBKRBrokerExtended(ib)
    await ibkr_broker.check_connection()

    underlying_broker = UpdatingIBroker(ibkr_broker, market_data)
    cp_broker, cp_cleanup = await attach_control_plane(
        local_broker=underlying_broker,
        strategy_name="fast_scalp_live",
        strategy_params={},
        trade_session="rth",
    )

    # Create the execution orchestrator
    orchestrator = ExecutionOrchestrator(
        market_data=live_market_data,  # Use streaming data from builder
        ticker_discover=ticker_source,
        strategy_factory=strategy_factory,
        broker=cp_broker,
        position_manager=position_manager,
    )

    logger.info("Starting live production mode for the PreMarket Gap Short Strategy...")

    # Run the main execution
    await orchestrator.execute()

    # Cleanup tasks
    await orchestrator.stop()
    await cp_cleanup()


async def main():
    # Get tickers
    await run_live()

if __name__ == "__main__":
    asyncio.run(main())
