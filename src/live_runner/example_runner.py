import asyncio
import logging
from log_utils import configure_basic_logging

from strategies.example_strategy import ExampleStrategy
from tickers.preset_ticker_discover import PresetTickerDiscover
from tools.clock import Clock
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from brokers.local_broker import LocalBroker
from position_manager import PositionManager
from control_plane.runner_helper import attach_control_plane
from marketdata.market_data_builder import MarketDataBuilder

import dotenv

configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

dotenv.load_dotenv()

INITIAL_CAPITAL = 10000

async def run_live():
    # Initialize clock
    clock = Clock()

    # Set up market data using the builder (Polygon streaming, extended hours)
    live_data_builder = (
        MarketDataBuilder(clock=clock)
        .with_trade_session('full')
    )
    market_data = live_data_builder.build_market_data()
    live_market_data = live_data_builder.build_streaming_market_data()

    # Strategy and ticker discovery
    strategy_factory = lambda: ExampleStrategy()
    ticker_source = PresetTickerDiscover(tickers=["SPY"], start_time=clock.now())

    # Position manager and brokers
    position_manager = PositionManager(initial_capital=INITIAL_CAPITAL, max_position_size=1)

    local_broker = LocalBroker(
        market_data=market_data,
        initial_capital=INITIAL_CAPITAL,
        clock=clock,
    )

    # Attach Control-Plane services (broker wrapper, agent, stats logger)
    cp_broker, cp_cleanup = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="example_strategy",
        strategy_params={},
        trade_session="rth",
    )

    # Create the execution orchestrator with the control-plane broker
    orchestrator = ExecutionOrchestrator(
        market_data=live_market_data,
        ticker_discover=ticker_source,
        strategy_factory=strategy_factory,
        broker=cp_broker,
        position_manager=position_manager,
    )

    logger.info("Starting live production mode for the Example Strategy (agent mode)...")

    try:
        await orchestrator.execute()
    finally:
        await orchestrator.stop()
        await cp_cleanup()

async def main():
    # Get tickers
    await run_live()

if __name__ == "__main__":
    asyncio.run(main())