import os
import dotenv
from control_plane.runner_helper import attach_control_plane
from position_manager import PositionManager
from brokers.clear_street_broker_v2 import ClearStreetBrokerV2
from brokers.clear_street_locate_manager import ClearStreetLocateManager
from brokers.local_broker import LocalBroker
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from tools.clock import Clock
from tickers.ticker_info_scan_criteria import TickerInfoScanCriteria, TickerType
from tickers.ticker_discover_using_scan_criteria import TickerDiscoverUsingScanCriteria
from strategies.rth_gap_short_strategy import RTHGapEMAShortStrategy
from strategies.premarket_gap_scanner import PreMarketGapScanner
from marketdata.market_data_builder import MarketDataBuilder
from datetime import datetime, time
from dateutil.relativedelta import relativedelta
import asyncio
import logging
from log_utils import configure_basic_logging

configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

dotenv.load_dotenv()

INITIAL_CAPITAL = 30000
POSITION_SIZE = 0.01  # 300


async def run_live():
    clock = Clock()

    # Set up market data using the builder (Polygon streaming, full session)
    live_data_builder = MarketDataBuilder(clock=clock).with_trade_session("full")
    live_market_data = live_data_builder.build_streaming_market_data()

    market_data = live_data_builder.build_market_data()

    # Initialize strategy factory
    strategy_factory = lambda: RTHGapEMAShortStrategy()

    # Disk market data for discovery
    two_months_ago = datetime.now() - relativedelta(months=2)
    disk_market_data = (
        MarketDataBuilder()  # Create new disk data with builder
        .with_disk_data(start_date=two_months_ago)
        .build_market_data()
    )
    bulk_ohlc = live_data_builder.build_bulk_ohlc()

    # Scanner configured for RTH window 09:29 → 15:30 ET
    rth_scanner = PreMarketGapScanner(
        scan_start_time=time(9, 29),
        scap_stop_time=time(15, 30),
        # You can tune these thresholds as needed
        min_gap_up_pct=70.0,
        min_price_threshold=1.0,
    )

    market_cap_criteria = TickerInfoScanCriteria(
        min_market_cap=1_000_000,  # 1M
        max_market_cap=5_000_000_000,  # 5B
        allowed_ticker_types={TickerType.COMMON_STOCK},
        delegate_criteria=rth_scanner,
        blocklist=set([]),
    )

    ticker_source = TickerDiscoverUsingScanCriteria(
        disk_market_data=disk_market_data,
        theta_bulk_ohlc=bulk_ohlc,
        criteria=market_cap_criteria,
        refresh_interval=5,  # 5 seconds refresh interval
    )

    # Position manager and broker
    position_manager = PositionManager(
        max_positions=10,
        risk_per_position=1,
        initial_capital=INITIAL_CAPITAL,
        max_position_size=POSITION_SIZE,
    )

    ACCOUNT_ID = os.environ.get("CLEAR_STREET_ACCOUNT_ID")
    MPID = os.environ.get("CLEAR_STREET_MPID")
    ENVIRONMENT = os.environ.get("CLEAR_STREET_ENVIRONMENT")

    locate_manager = ClearStreetLocateManager(
        account_id=ACCOUNT_ID,
        mpid=MPID,
        market_data=market_data,
        max_fee_pct=0.0125,  # 1.25%
        locate_budget_limit=INITIAL_CAPITAL * 0.01,  # No more than 1% of capital
    )

    clear_street_broker = ClearStreetBrokerV2(
        ACCOUNT_ID, environment=ENVIRONMENT, locate_manager=locate_manager
    )

    await clear_street_broker.connect()
    local_broker = LocalBroker(
        market_data=market_data,
        initial_capital=INITIAL_CAPITAL,
        clock=clock,
        delegate_broker=clear_street_broker,
    )

    # Attach Control-Plane services
    cp_broker, cp_cleanup = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="rth_gap_short",
        strategy_params={},
        trade_session="rth",
    )

    # Orchestrator
    orchestrator = ExecutionOrchestrator(
        market_data=live_market_data,
        ticker_discover=ticker_source,
        strategy_factory=strategy_factory,
        broker=cp_broker,
        position_manager=position_manager,
    )

    logger.info("Starting live production mode for the RTH Gap EMA Short Strategy...")

    await orchestrator.execute()

    await orchestrator.stop()
    await cp_cleanup()


async def main():
    await run_live()


if __name__ == "__main__":
    asyncio.run(main())



