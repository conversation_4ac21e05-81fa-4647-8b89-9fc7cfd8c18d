import os
import dotenv
import asyncio
import logging
from datetime import datetime, time
from dateutil.relativedelta import relativedelta

from control_plane.runner_helper import attach_control_plane
from position_manager import PositionManager
from brokers.clear_street_broker import ClearStreetBroker
from brokers.local_broker import LocalBroker
from brokers.alpaca_broker import AlpacaBroker
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from tools.clock import Clock
from tickers.ticker_helpers import TickerInfoStore
from tickers.ticker_info_scan_criteria import TickerInfoScanCriteria, TickerType
from tickers.ticker_discover_using_scan_criteria import TickerDiscoverUsingScanCriteria
from strategies.ltr_gap_strategy import LTRGapLiveStrategy
from marketdata.market_data_builder import MarketDataBuilder
from log_utils import configure_basic_logging
import pandas as pd

# ─── Configuration ──────────────────────────────────────────────────────────
configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

env_path = dotenv.find_dotenv()
dotenv.load_dotenv(env_path)

INITIAL_CAPITAL = float(os.getenv("INITIAL_CAPITAL", 100000))
POSITION_SIZE = float(os.getenv("POSITION_SIZE", 0.05))
PREVENT_LOOKAHEAD = os.getenv("PREVENT_LOOKAHEAD", "false").lower() in ("true", "1", "yes")

# ─── Live Runner ─────────────────────────────────────────────────────────────
async def run_live():
    # Initialize clock for time ticks
    clock = Clock()

    # Build streaming market data (full session)
    live_data_builder = MarketDataBuilder().with_trade_session('full')
    live_market_data = live_data_builder.build_streaming_market_data()
    intraday_market_data = (MarketDataBuilder()
                            .with_trade_session("full")
                            .with_adjusted(False)
                            .with_period("intraday")
                            .build_market_data())

    ticker_info_store = TickerInfoStore()
     
    def create_intraday_fetcher():
        def get_30min_data(tkr: str, day_str: str):
            day = pd.Timestamp(day_str)
            today = pd.Timestamp.now().normalize()
            
            # Only prevent lookahead if PREVENT_LOOKAHEAD is enabled AND the requested date is today
            if PREVENT_LOOKAHEAD and day.date() == today.date():
                end_time = day.replace(hour=4, minute=30)
                logger.info(f"Preventing lookahead for {tkr} on today's date ({day.date()})")
            else:
                end_time = day.replace(hour=23, minute=59)
                
            return intraday_market_data.gather_historical_data(
                ticker=tkr,
                start_dt=day.replace(hour=0, minute=0),
                end_dt=end_time,
                interval=1800
            )
        return get_30min_data

    # Disk (historical) market data for ticker discovery (last 2 months)
    two_months_ago = datetime.now() - relativedelta(months=2)
    disk_market_data = (MarketDataBuilder()
                        .with_disk_data(start_date=two_months_ago)
                        .build_market_data())
    
    feature_cols = [
        'gap_atr', 'prev_gap_atr', 'prev_ah_gap_atr', 'prev_ah_vol_rel',
        'max_gap30_atr', 'log_mcap', 'log_listing_age', 'weekday',
        'prev_day_return_atr', 'prev_vol_ratio',
        'dist_ma5_atr', 'dist_ma10_atr', 'dist_ma20_atr', 'dist_ma50_atr', 
        'pre_30m_return_atr', 'pre_30m_vol'
    ]

    
    # Strategy factory: create a new PreMarketGapMAShortStrategy each run
    
    strategy_factory = lambda: LTRGapLiveStrategy(ticker_info_store=ticker_info_store,
                                                  intraday_fetcher=create_intraday_fetcher(),
                                                  market_data_provider=intraday_market_data,
                                                  feature_cols=feature_cols)
    
    # Bulk OHLCV snapshot for immediate criteria evaluation
    bulk_ohlc = live_data_builder.build_bulk_ohlc()

    # Ticker discovery: filter by market cap, then apply gap scan
    market_cap_criteria = TickerInfoScanCriteria(
        min_market_cap=1_000_000,
        max_market_cap=500_000_000,
        allowed_ticker_types={TickerType.COMMON_STOCK},
        delegate_criteria=strategy_factory()
    )
    ticker_source = TickerDiscoverUsingScanCriteria(
        disk_market_data=disk_market_data,
        theta_bulk_ohlc=bulk_ohlc,
        criteria=market_cap_criteria,
        max_run_hours=24,
    )

    # Position manager and brokers
    position_manager = PositionManager(
        max_positions=10,
        risk_per_position=1,
        initial_capital=INITIAL_CAPITAL,
        max_position_size=POSITION_SIZE
    )

    ACCOUNT_ID = os.getenv("CLEAR_STREET_ACCOUNT_ID")
    MPID = os.getenv("CLEAR_STREET_MPID")
    ENV = os.getenv("CLEAR_STREET_ENVIRONMENT")

    clear_street = ClearStreetBroker(
        account_id=ACCOUNT_ID,
        mpid=MPID,
        environment=ENV,
        max_locate_price_accept_pct=0.00, # Disable non ETB locates
        load_existing_locates=False
    )
    
    alpaca_broker = AlpacaBroker(extended_hours=True) 
    
    local_broker = LocalBroker(
        market_data=live_market_data,
        initial_capital=INITIAL_CAPITAL,
        clock=clock,
        delegate_broker=alpaca_broker
        # Disable delegate delegate_broker=clear_street
    )

    # Attach Control-Plane services
    cp_broker, cp_cleanup = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="ltr_gap_strategy",
        strategy_params={},
        trade_session="rth",
    )

    # Execution orchestrator wires discovery, strategy, broker, and sizing
    orchestrator = ExecutionOrchestrator(
        market_data=live_market_data,
        ticker_discover=ticker_source,
        strategy_factory=strategy_factory,
        broker=cp_broker,
        position_manager=position_manager
    )

    logger.info("Starting live production mode for PreMarketGapMAShortStrategy...")

    # Start orchestrator
    await orchestrator.execute()

    # Graceful shutdown
    await orchestrator.stop()
    await cp_cleanup()

# ─── Entry point ─────────────────────────────────────────────────────────────
if __name__ == "__main__":
    asyncio.run(run_live())
