import os
import dotenv
from control_plane.runner_helper import attach_control_plane
from position_manager import PositionManager
from brokers.clear_street_broker import ClearStreetBroker
from brokers.logging_broker import LoggingBroker
from brokers.local_broker import LocalBroker
from brokers.alpaca_broker import AlpacaBroker
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from tools.clock import Clock
from tickers.ticker_info_scan_criteria import TickerInfoScanCriteria, TickerType
from tickers.ticker_discover_using_scan_criteria import TickerDiscoverUsingScanCriteria
from strategies.gap_long_midday_relative_strength_strategy import GapLongMiddayRelativeStrengthStrategy
from marketdata.market_data_builder import MarketDataBuilder
import typing
import pytz
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import asyncio
import logging
from log_utils import configure_basic_logging

configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

dotenv.load_dotenv()

# Target params from backtest is 25% size
# with bottom 2 only
# tr1 = simulate_midday_gap_pairs_trade(
#     gap_up_df,
#     minute_data,
#     position_size_pct_long   = 0.25,
#     position_size_pct_short  = 0.00,
#     entry_time              = time(13, 0),
#     exit_time               = time(15, 55),
#     min_entry_dvol          = 500_000,
#     min_gap_pct             = 60.0,
#     top_n                   = 0,
#     bottom_n                = 2,
#     min_entry_price     = 0.5,
# )

INITIAL_CAPITAL = 80000
POSITION_SIZE = 0.05

async def run_live():
    clock = Clock()

    # Set up market data using the builder (Polygon streaming, full session)
    live_data_builder = (MarketDataBuilder(clock=clock)
                          .with_trade_session('full'))
    live_market_data = live_data_builder.build_streaming_market_data()

    market_data = live_data_builder.build_market_data()

    # Initialize strategy - will be used both as strategy and scan criteria
    strategy_factory = lambda: GapLongMiddayRelativeStrengthStrategy(ticker_info_scan_criteria=market_cap_criteria, market_data=market_data)

    # Set up components for ticker discovery
    # Use MarketDataBuilder for disk data with a start_date ~2 months ago
    two_months_ago = datetime.now() - relativedelta(months=2)
    disk_market_data = (MarketDataBuilder() # Create new disk data with builder
                          .with_disk_data(start_date=two_months_ago)
                          .build_market_data())
    bulk_ohlc = live_data_builder.build_bulk_ohlc()

    market_cap_criteria = TickerInfoScanCriteria(delegate_criteria=None,
                                                 min_market_cap=1_000_000,  # 1M
                                                 max_market_cap=5_000_000_000,  # 5B
                                                 allowed_ticker_types={TickerType.COMMON_STOCK},
                                                 blocklist=set([]))
    
    
    criteria = GapLongMiddayRelativeStrengthStrategy(ticker_info_scan_criteria=market_cap_criteria, market_data=market_data)

    # Initialize ticker discovery with our ParabolicShortStrategy as criteria
    ticker_source = TickerDiscoverUsingScanCriteria(
        disk_market_data=disk_market_data,
        theta_bulk_ohlc=bulk_ohlc,
        criteria=criteria,
        max_run_hours=None
    )

    # Initialize position manager and broker
    position_manager = PositionManager(
        max_positions=10, risk_per_position=1, initial_capital=INITIAL_CAPITAL, max_position_size=POSITION_SIZE)


    ACCOUNT_ID = os.environ.get("CLEAR_STREET_ACCOUNT_ID")
    MPID = os.environ.get("CLEAR_STREET_MPID")
    ENVIRONMENT = os.environ.get("CLEAR_STREET_ENVIRONMENT")

    clear_street_broker = ClearStreetBroker(
       ACCOUNT_ID, MPID, environment=ENVIRONMENT, max_locate_price_accept_pct=0, load_existing_locates=False)
    
    local_broker = LoggingBroker(LocalBroker(
        market_data=market_data,
        initial_capital=INITIAL_CAPITAL,
        clock=clock,
        delegate_broker=clear_street_broker
    ))

    # Attach Control-Plane services
    cp_broker, cp_cleanup = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="gap_long_midday_relative_strength",
        strategy_params={},
        trade_session="rth",
    )

    # Create the execution orchestrator
    orchestrator = ExecutionOrchestrator(
        market_data=live_market_data,
        ticker_discover=ticker_source,
        strategy_factory=strategy_factory,
        broker=cp_broker,
        position_manager=position_manager
    )

    logger.info("Starting live production mode for the Gap Long Midday Relative Strength Strategy...")

    # Run the main execution
    await orchestrator.execute()

    # Cleanup tasks
    await orchestrator.stop()
    await cp_cleanup()


async def main():
    # Get tickers
    await run_live()

if __name__ == "__main__":
    asyncio.run(main())
