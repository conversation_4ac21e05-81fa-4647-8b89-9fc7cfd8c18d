import os
import dotenv
from control_plane.runner_helper import attach_control_plane
from position_manager import PositionManager
from brokers.local_broker import LocalBroker
from brokers.das_broker import <PERSON><PERSON><PERSON><PERSON>
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from tools.clock import Clock
from tickers.ticker_info_scan_criteria import TickerInfoScanCriteria, TickerType
from tickers.ticker_discover_using_scan_criteria import TickerDiscoverUsingScanCriteria
from strategies.post_market_gap_short_strategy import PostMarketGapShortStrategy
from marketdata.market_data_builder import MarketDataBuilder
from datetime import datetime
from dateutil.relativedelta import relativedelta
import asyncio
import logging
from log_utils import configure_basic_logging


configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

dotenv.load_dotenv()

# DAS-specific risk sizing
INITIAL_CAPITAL = 32000
POSITION_SIZE = 0.01


async def run_live():
    clock = Clock()

    # Set up market data using the builder (Polygon streaming, full session)
    live_data_builder = MarketDataBuilder(clock=clock).with_trade_session('full')
    live_market_data = live_data_builder.build_streaming_market_data()
    market_data = live_data_builder.build_market_data()

    # Initialize strategy - will be used both as strategy and scan criteria
    strategy_factory = lambda: PostMarketGapShortStrategy()

    # Set up components for ticker discovery
    two_months_ago = datetime.now() - relativedelta(months=2)
    disk_market_data = (MarketDataBuilder()
                        .with_disk_data(start_date=two_months_ago)
                        .build_market_data())
    bulk_ohlc = live_data_builder.build_bulk_ohlc()

    market_cap_criteria = TickerInfoScanCriteria(
        min_market_cap=1_000_000,
        max_market_cap=5_000_000_000,
        allowed_ticker_types={TickerType.COMMON_STOCK},
        delegate_criteria=PostMarketGapShortStrategy(),
        blocklist=set([])
    )

    # Initialize ticker discovery
    ticker_source = TickerDiscoverUsingScanCriteria(
        disk_market_data=disk_market_data,
        theta_bulk_ohlc=bulk_ohlc,
        criteria=market_cap_criteria,
        max_run_hours=18,
        refresh_interval=5
    )

    # Initialize position manager
    position_manager = PositionManager(
        max_positions=10,
        risk_per_position=1,
        initial_capital=INITIAL_CAPITAL,
        max_position_size=POSITION_SIZE
    )

    # DAS connection parameters from environment
    das_host = os.getenv("DAS_HOST", "127.0.0.1")
    das_port = int(os.getenv("DAS_PORT", "11000"))
    das_user = os.getenv("DAS_USER") or ""
    das_password = os.getenv("DAS_PASSWORD") or ""
    das_account = os.getenv("DAS_ACCOUNT", "")

    # Create DAS broker & connect
    broker = DasBroker(
        host=das_host,
        port=das_port,
        user=das_user,
        password=das_password,
        account=das_account,
    )
    await broker.connect()

    # Wrap DAS broker in LocalBroker for position & risk accounting
    local_broker = LocalBroker(
        market_data=market_data,
        initial_capital=INITIAL_CAPITAL,
        clock=clock,
        delegate_broker=broker,
    )

    # Attach Control-Plane services
    cp_broker, cp_cleanup = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="post_market_gap_short_das",
        strategy_params={},
        trade_session="full",
    )

    # Create the execution orchestrator
    orchestrator = ExecutionOrchestrator(
        market_data=live_market_data,
        ticker_discover=ticker_source,
        strategy_factory=strategy_factory,
        broker=cp_broker,
        position_manager=position_manager
    )

    logger.info("Starting live production mode for the PostMarket Gap Short Strategy (DAS)...")

    # Run the main execution
    await orchestrator.execute()

    # Cleanup tasks
    await orchestrator.stop()
    await cp_cleanup()


async def main():
    await run_live()


if __name__ == "__main__":
    asyncio.run(main())


