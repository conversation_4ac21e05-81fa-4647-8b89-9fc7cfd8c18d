import logging
from log_utils import configure_basic_logging

configure_basic_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

import asyncio
from datetime import datetime, timedelta
import pytz
import typing

from marketdata.market_data_builder import MarketDataBuilder
from marketdata.theta_splicing_market_data import ThetaSplicingMarketData
from marketdata.theta_market_data import ThetaMarketData, ThetaVenue
from marketdata.thetadata_streaming import ThetaDataStreamingMarketData
from strategies.moc_imbalance_strategy import MOCImbalanceStrategy
from tickers.preset_ticker_discover import PresetTickerDiscover
from tools.clock import Clock
from orchestrator.execution_orchestrator import ExecutionOrchestrator
from brokers.local_broker import LocalBroker
from brokers.schwab_broker import SchwabBroker
from brokers.ibkr_broker import IBKRBroker
from ib_async import IB

from position_manager import PositionManager
from control_plane.runner_helper import attach_control_plane
from marketdata.imarketdata import IMarketData
from marketdata.schwab_market_data import SchwabStreamingMarketData

import dotenv
dotenv.load_dotenv()

    # trades = simulate_moc_pop(
    #     initial_aum=100_000,
    #     ticker=tk,
    #     start_dt=start_dt,
    #     end_dt=end_dt,
    #     threshold_sd=1.5,
    #     target_vol=0.1,
    #     **spec
    # )
    
INITIAL_CAPITAL = 100000 
TARGET_VOL = 0.1
MAX_LEVERAGE = 12

async def run_live():
    # Initialize clock for backtesting
    clock = Clock()
    
    # Create Schwab Market Data for futures live data
    schwab_broker_live_data = SchwabBroker(token_path="./schwab_token_2.json")
    await schwab_broker_live_data.connect()
    http_client = await schwab_broker_live_data.get_http_client()

    # Set up streaming market data using Schwab
    live_data = SchwabStreamingMarketData(http_client=http_client)
    await live_data.connect()
    
    market_data = (MarketDataBuilder(clock=clock)
                      .with_trade_session('rth')
                      .build_market_data())
    
    # Create IBKR broker
    ib = IB()
    await ib.connectAsync('127.0.0.1', 8496, clientId=1111)
    print("Connected to IB")

    ibkr_broker = IBKRBroker(ib, outside_rth=True)
    await ibkr_broker.check_connection()

    ticker_source = PresetTickerDiscover(tickers=["/NQ"], start_time=clock.now())
    position_manager = PositionManager(initial_capital=INITIAL_CAPITAL, max_position_size=1)
    
    # Create local broker with delegation to Alpaca
    local_broker = LocalBroker(
        market_data=market_data,
        initial_capital=INITIAL_CAPITAL,
        clock=clock,
        delegate_broker=ibkr_broker
    )

    # Attach Control-Plane services (including market data health)
    cp_broker, cp_cleanup, cp_market_data = await attach_control_plane(
        local_broker=local_broker,
        strategy_name="moc_imbalance_strategy",
        strategy_params={},
        trade_session="rth",
        streaming_market_data=live_data,
    )
    
    # Create the execution orchestrator with the control plane broker
    orchestrator = ExecutionOrchestrator(
        market_data=cp_market_data,
        ticker_discover=ticker_source,
        strategy_factory=lambda: MOCImbalanceStrategy(
            threshold_sd=1.5,
            target_vol=0.1,
            max_leverage_futures=MAX_LEVERAGE,
            contract_multiplier=20,
            max_contracts=10, # Enforce max contracts for safety
        ),
        broker=cp_broker,
        position_manager=position_manager
    )
    
    logger.info("Starting live production mode for the ORB Strategy...")
    
    # Run the main execution
    await orchestrator.execute()
    
    # Cleanup task
    await orchestrator.stop()
    await cp_cleanup()

async def main():
    # Get tickers
    await run_live()

if __name__ == "__main__":
    asyncio.run(main())