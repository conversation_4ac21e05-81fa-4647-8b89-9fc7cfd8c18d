import os
import json
import glob
import pandas as pd

def load_strategy_data():
    """Load all available strategy data from the reports directory"""
    base_dir = "stats"
    if not os.path.exists(base_dir):
        return {}, []
    
    strategies = {}
    for strategy_dir in glob.glob(os.path.join(base_dir, "*")):
        if os.path.isdir(strategy_dir):
            strategy_name = os.path.basename(strategy_dir)
            strategies[strategy_name] = {}
            
            # Get versions for this strategy
            for version_dir in glob.glob(os.path.join(strategy_dir, "*")):
                if os.path.isdir(version_dir):
                    version_name = os.path.basename(version_dir)
                    strategies[strategy_name][version_name] = {
                        "path": version_dir,
                        "dates": []
                    }
                    
                    # Get dates for this version
                    for date_dir in glob.glob(os.path.join(version_dir, "*")):
                        if os.path.isdir(date_dir):
                            date_name = os.path.basename(date_dir)
                            
                            # Check if backtest or live data exists
                            backtest_dir = os.path.join(date_dir, "backtest")
                            live_dir = os.path.join(date_dir, "live")
                            
                            if os.path.exists(backtest_dir):
                                strategies[strategy_name][version_name]["dates"].append({
                                    "date_name": date_name,
                                    "type": "backtest",
                                    "path": backtest_dir
                                })
                            
                            if os.path.exists(live_dir):
                                strategies[strategy_name][version_name]["dates"].append({
                                    "date_name": date_name,
                                    "type": "live",
                                    "path": live_dir
                                })
    
    # Create a flat list of all runs for the adhoc view
    all_runs = []
    for strategy_name, versions in strategies.items():
        for version_name, version_data in versions.items():
            for date_data in version_data["dates"]:
                # Try to read metadata if it exists
                metadata_path = os.path.join(date_data["path"], "metadata.json")
                metadata = {}
                if os.path.exists(metadata_path):
                    try:
                        with open(metadata_path, "r") as f:
                            metadata = json.load(f)
                    except:
                        pass
                
                run_info = {
                    "strategy": strategy_name,
                    "version": version_name,
                    "date_name": date_data["date_name"],
                    "type": date_data["type"],
                    "path": date_data["path"],
                    "metadata": metadata
                }
                all_runs.append(run_info)
    
    return strategies, all_runs 