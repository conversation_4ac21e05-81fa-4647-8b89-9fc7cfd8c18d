import os
import pandas as pd
import plotly.express as px
import streamlit as st

def show_trades_tab(run_path, key_prefix=""):
    """Display trade statistics"""
    trades_file = os.path.join(run_path, "trades.csv")
    if not os.path.exists(trades_file):
        st.warning("Trades data not found")
        return
    
    # Load trades data
    trades_df = pd.read_csv(trades_file)
    
    # Display summary
    st.subheader("Trade Summary")
    
    # Basic trade metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Trades", len(trades_df))
    with col2:
        if 'pnl' in trades_df.columns:
            win_rate = len(trades_df[trades_df['pnl'] > 0]) / len(trades_df) * 100
            st.metric("Win Rate", f"{win_rate:.2f}%")
    with col3:
        if 'pnl' in trades_df.columns:
            avg_pnl = trades_df['pnl'].mean()
            st.metric("Avg. P&L", f"${avg_pnl:.2f}")
    
    # Display raw trades table
    st.subheader("Trades")
    st.dataframe(trades_df)
    
    # Show P&L distribution if available with a unique key
    if 'pnl' in trades_df.columns:
        st.subheader("P&L Distribution")
        fig = px.histogram(trades_df, x='pnl', title="P&L Distribution")
        st.plotly_chart(fig, use_container_width=True, key=f"{key_prefix}_pnl_dist_{hash(run_path)}")
    
    # Try to load HTML report if it exists
    trade_report = os.path.join(run_path, "trade_stats.html")
    if os.path.exists(trade_report):
        with open(trade_report, "r") as f:
            html_content = f.read()
        st.subheader("Trade Statistics Report")
        st.components.v1.html(html_content, height=500, scrolling=True) 