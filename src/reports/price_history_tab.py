import os
import glob
import pandas as pd
import plotly.graph_objects as go
import streamlit as st

def show_price_history_tab(run_path, key_prefix="", trades_df=None):
    """Display historical price data with trade entries and exits
    
    Parameters:
    -----------
    run_path : str
        Path to the run directory
    key_prefix : str
        Prefix for the Streamlit keys
    trades_df : pandas.DataFrame, optional
        DataFrame containing trade data, already loaded from trades.csv
    """
    # Find all historical price data files
    price_files = glob.glob(os.path.join(run_path, "*_historical_data.csv"))
    
    if not price_files:
        st.warning("No historical price data found")
        return
    
    # Create dropdown for selecting symbol
    symbols = [os.path.basename(f).replace("_historical_data.csv", "") for f in price_files]
    selected_symbol = st.selectbox("Select Symbol", symbols, key=f"{key_prefix}_symbol_select_{hash(run_path)}")
    
    # Load price data for selected symbol
    price_file = os.path.join(run_path, f"{selected_symbol}_historical_data.csv")
    
    # If trades_df wasn't passed, try to load it from trades.csv
    if trades_df is None:
        trades_file = os.path.join(run_path, "trades.csv")
        if os.path.exists(trades_file):
            try:
                trades_df = pd.read_csv(trades_file)
            except Exception as e:
                st.warning(f"Error loading trades data: {e}")
    
    if os.path.exists(price_file):
        prices_df = pd.read_csv(price_file)
        prices_df.set_index(pd.to_datetime(prices_df.iloc[:, 0]), inplace=True)
        
        # Show price chart
        st.subheader(f"{selected_symbol} Price History")
        
        # Determine what columns we have
        if all(col in prices_df.columns for col in ['open', 'high', 'low', 'close']):
            # Create OHLC chart without gaps
            fig = go.Figure()
            
            # Add candlestick trace
            fig.add_trace(go.Candlestick(
                x=prices_df.index,
                open=prices_df['open'],
                high=prices_df['high'],
                low=prices_df['low'],
                close=prices_df['close'],
                name="Price"
            ))
            
            # Plot trades if available
            if trades_df is not None and not trades_df.empty:
                # Filter trades for the selected symbol
                if 'symbol' in trades_df.columns:
                    symbol_trades = trades_df[trades_df['symbol'] == selected_symbol]
                else:
                    symbol_trades = trades_df  # Assume all trades are for this symbol
                
                if not symbol_trades.empty:
                    # Make sure datetime columns are properly formatted
                    if 'entry_time' in symbol_trades.columns:
                        symbol_trades['entry_time'] = pd.to_datetime(symbol_trades['entry_time'])
                    if 'exit_time' in symbol_trades.columns:
                        symbol_trades['exit_time'] = pd.to_datetime(symbol_trades['exit_time'])
                    # If exit_time isn't available but close_date is, use that instead
                    elif 'close_date' in symbol_trades.columns:
                        symbol_trades['exit_time'] = pd.to_datetime(symbol_trades['close_date'])
                    
                    # Plot entry points with green arrows
                    if 'entry_time' in symbol_trades.columns and 'entry_price' in symbol_trades.columns:
                        fig.add_trace(go.Scatter(
                            x=symbol_trades['entry_time'],
                            y=symbol_trades['entry_price'],
                            mode='markers',
                            marker=dict(symbol='arrow-up', size=10, color='green'),
                            name='Entry',
                            text=symbol_trades.apply(lambda row: f"Entry: {row['entry_price']:.2f}", axis=1),
                            hoverinfo='text'
                        ))
                    
                    # Plot exit points with red arrows
                    if ('exit_time' in symbol_trades.columns or 'close_date' in symbol_trades.columns) and 'exit_price' in symbol_trades.columns:
                        exit_times = symbol_trades.get('exit_time', symbol_trades.get('close_date'))
                        
                        # Calculate PnL if not already present
                        if 'pnl' not in symbol_trades.columns and 'entry_price' in symbol_trades.columns:
                            symbol_trades['pnl'] = symbol_trades.apply(
                                lambda row: (row['exit_price'] - row['entry_price']) * 
                                            (1 if row.get('direction', 'long').lower() == 'long' else -1) * 
                                            row.get('quantity', 1),
                                axis=1
                            )
                        
                        fig.add_trace(go.Scatter(
                            x=exit_times,
                            y=symbol_trades['exit_price'],
                            mode='markers',
                            marker=dict(symbol='arrow-down', size=10, color='red'),
                            name='Exit',
                            text=symbol_trades.apply(
                                lambda row: f"Exit: {row['exit_price']:.2f}<br>PnL: ${row.get('pnl', 0):.2f}", 
                                axis=1
                            ),
                            hoverinfo='text'
                        ))
            
            # Update layout to remove gaps and improve appearance
            fig.update_layout(
                title=f"{selected_symbol} OHLC Chart",
                xaxis_rangeslider_visible=False,
                xaxis=dict(
                    type='date',  # Keep as date type for accurate point placement
                    title="Date",
                    # Group consecutive timestamps together to minimize gaps
                    rangebreaks=[
                        dict(bounds=["sat", "mon"]),  # Hide weekends
                        dict(bounds=[16, 9.5], pattern="hour"),  # Hide overnight hours (4pm to 9:30am ET)
                    ],
                ),
                yaxis=dict(
                    title="Price"
                ),
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                )
            )
            
            st.plotly_chart(fig, use_container_width=True, key=f"{key_prefix}_ohlc_{selected_symbol}_{hash(run_path)}")
        else:
            st.warning(f"OHLC data not available for {selected_symbol}") 