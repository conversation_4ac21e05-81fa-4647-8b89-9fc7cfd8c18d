import os
import pandas as pd
import plotly.express as px
import streamlit as st

def show_portfolio_tab(run_path, key_prefix=""):
    """Display portfolio statistics"""
    portfolio_file = os.path.join(run_path, "daily_portfolio_values.csv")
    if not os.path.exists(portfolio_file):
        st.warning("Portfolio data not found")
        return
    
    # Load portfolio values
    portfolio_df = pd.read_csv(portfolio_file)
    portfolio_df.set_index(pd.to_datetime(portfolio_df.iloc[:, 0]), inplace=True)
    portfolio_df = portfolio_df.iloc[:, 1]  # Select just the values column
    
    # Display summary stats
    st.subheader("Portfolio Performance")
    
    # Calculate returns
    portfolio_df.name = "Portfolio Value"
    returns = portfolio_df.pct_change().dropna()
    cumulative_returns = (1 + returns).cumprod() - 1
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Starting Value", f"${portfolio_df.iloc[0]:,.2f}")
    with col2:
        st.metric("Ending Value", f"${portfolio_df.iloc[-1]:,.2f}")
    with col3:
        total_return = (portfolio_df.iloc[-1] / portfolio_df.iloc[0] - 1) * 100
        st.metric("Total Return", f"{total_return:.2f}%")
    with col4:
        if len(returns) > 0:
            st.metric("Annualized Volatility", f"{returns.std() * (252 ** 0.5) * 100:.2f}%")
    
    # Create portfolio value chart with a unique key
    st.subheader("Portfolio Value Over Time")
    fig1 = px.line(portfolio_df, title="Portfolio Value")
    st.plotly_chart(fig1, use_container_width=True, key=f"{key_prefix}_portfolio_value_{hash(run_path)}")
    
    # Returns chart with a unique key
    st.subheader("Cumulative Returns")
    fig2 = px.line(cumulative_returns * 100, title="Cumulative Returns (%)")
    st.plotly_chart(fig2, use_container_width=True, key=f"{key_prefix}_cumulative_returns_{hash(run_path)}")
    
    # Try to load HTML report if it exists
    portfolio_report = os.path.join(run_path, "portfolio_stats.html")
    if os.path.exists(portfolio_report):
        with open(portfolio_report, "r") as f:
            html_content = f.read()
        st.subheader("Portfolio Statistics Report")
        st.components.v1.html(html_content, height=500, scrolling=True) 