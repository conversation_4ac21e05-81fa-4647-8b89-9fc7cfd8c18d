import pandas as pd
import streamlit as st
from reports.metadata_tab import show_metadata
from reports.portfolio_tab import show_portfolio_tab
from reports.trades_tab import show_trades_tab
from reports.price_history_tab import show_price_history_tab

def show_strategy_timeline(strategies):
    """Display the strategy timeline view tab"""
    st.header("Strategy Timeline")
    
    # Let user select a strategy
    strategy_name = st.selectbox("Select Strategy", list(strategies.keys()))
    
    # Let user select a version
    versions = list(strategies[strategy_name].keys())
    version_name = st.selectbox("Select Version", versions)
    
    # Show runs for this strategy/version in a table
    runs = strategies[strategy_name][version_name]["dates"]
    
    # Create a table for the runs
    run_data = []
    for run in runs:
        # Try to load metadata for more info
        metadata_path = run["path"] + "/metadata.json"
        start_date = run["date_name"]
        end_date = run["date_name"]
        total_trades = "N/A"
        final_capital = "N/A"
        
        try:
            import os
            import json
            if os.path.exists(metadata_path):
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)
                
                start_date = metadata.get("start_date", run["date_name"])
                end_date = metadata.get("end_date", run["date_name"])
                total_trades = metadata.get("total_trades", "N/A")
                final_capital = f"${metadata.get('final_capital', 0):,.2f}"
        except:
            pass
        
        run_data.append({
            "Date Range": f"{start_date} to {end_date}",
            "Type": run["type"].capitalize(),
            "Trades": total_trades,
            "Final Value": final_capital,
            "Path": run["path"]
        })
    
    # Display runs as a dataframe
    if run_data:
        run_df = pd.DataFrame(run_data)
        
        # Display dataframe with selection capability
        st.dataframe(run_df.drop(columns=["Path"]), use_container_width=True)
        
        # Use selectbox for run selection instead of relying on dataframe selection
        if run_data:
            run_labels = [f"{run['Date Range']} - {run['Type']}" for run in run_data]
            selected_run_label = st.selectbox("Select Run to View Details", run_labels, index=len(run_labels)-1)
            selected_idx = run_labels.index(selected_run_label)
            selected_run_path = run_data[selected_idx]["Path"]
            
            st.subheader("Run Details")
            
            # Create tabs for different types of reports
            details_tab1, details_tab2, details_tab3, details_tab4 = st.tabs([
                "Metadata", "Portfolio", "Trades", "Price History"
            ])
            
            with details_tab1:
                show_metadata(selected_run_path, key_prefix="timeline_tab1")
            
            with details_tab2:
                show_portfolio_tab(selected_run_path, key_prefix="timeline_tab2")
            
            with details_tab3:
                show_trades_tab(selected_run_path, key_prefix="timeline_tab3")
            
            with details_tab4:
                show_price_history_tab(selected_run_path, key_prefix="timeline_tab4")
    else:
        st.info(f"No runs found for {strategy_name} version {version_name}")