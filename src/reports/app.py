import streamlit as st
from reports.utils import load_strategy_data
from reports.strategy_timeline import show_strategy_timeline
from reports.all_runs import show_all_runs

def main():
    st.set_page_config(page_title="Trading Strategy Reports", layout="wide")
    
    st.title("Trading Strategy Reports")
    
    # Load available strategies
    strategies, all_runs = load_strategy_data()
    
    if not strategies:
        st.error("No strategy data found. Please run some strategies first!")
        return
    
    # Create tabs for different views
    tab1, tab2 = st.tabs(["Strategy Timeline", "All Runs"])
    
    with tab1:
        show_strategy_timeline(strategies)
    
    with tab2:
        show_all_runs(all_runs)

if __name__ == "__main__":
    main() 