import os
import json
import streamlit as st

def show_metadata(run_path, key_prefix=""):
    """Display metadata about the strategy run"""
    metadata_file = os.path.join(run_path, "metadata.json")
    if not os.path.exists(metadata_file):
        st.warning("Metadata not found")
        return
    
    try:
        with open(metadata_file, "r") as f:
            metadata = json.load(f)
        
        st.subheader("Run Metadata")
        
        # Display key metrics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Initial Capital", f"${metadata.get('initial_capital', 0):,.2f}")
        with col2:
            st.metric("Final Capital", f"${metadata.get('final_capital', 0):,.2f}")
        with col3:
            pct_gain = ((metadata.get('final_capital', 0) / metadata.get('initial_capital', 1)) - 1) * 100
            st.metric("Gain/Loss", f"{pct_gain:.2f}%")
        
        # Display other metadata
        st.json(metadata)
    except Exception as e:
        st.error(f"Error loading metadata: {e}") 