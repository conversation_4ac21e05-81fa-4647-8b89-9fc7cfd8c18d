import pandas as pd
import streamlit as st
from reports.metadata_tab import show_metadata
from reports.portfolio_tab import show_portfolio_tab
from reports.trades_tab import show_trades_tab
from reports.price_history_tab import show_price_history_tab

def show_all_runs(all_runs):
    """Display the all runs view tab"""
    st.header("All Strategy Runs")
    
    # Create a dataframe for all runs
    runs_data = []
    for run in all_runs:
        metadata = run["metadata"]
        
        # Extract key metrics from metadata if available
        start_date = metadata.get("start_date", run["date_name"])
        end_date = metadata.get("end_date", run["date_name"])
        total_trades = metadata.get("total_trades", "N/A")
        final_capital = f"${metadata.get('final_capital', 0):,.2f}" if "final_capital" in metadata else "N/A"
        initial_capital = f"${metadata.get('initial_capital', 0):,.2f}" if "initial_capital" in metadata else "N/A"
        
        runs_data.append({
            "Strategy": run["strategy"],
            "Version": run["version"],
            "Date Range": f"{start_date} to {end_date}",
            "Type": run["type"].capitalize(),
            "Initial Capital": initial_capital,
            "Final Capital": final_capital,
            "Trades": total_trades,
            "Path": run["path"]
        })

    # Display all runs
    if runs_data:
        runs_df = pd.DataFrame(runs_data)
        
        # Add filters
        col1, col2, col3 = st.columns(3)
        with col1:
            strategy_filter = st.multiselect(
                "Filter by Strategy", 
                options=sorted(runs_df["Strategy"].unique()),
                default=[]
            )
        with col2:
            version_filter = st.multiselect(
                "Filter by Version", 
                options=sorted(runs_df["Version"].unique()),
                default=[]
            )
        with col3:
            type_filter = st.multiselect(
                "Filter by Type", 
                options=sorted(runs_df["Type"].unique()),
                default=[]
            )
        
        # Apply filters
        filtered_df = runs_df.copy()
        if strategy_filter:
            filtered_df = filtered_df[filtered_df["Strategy"].isin(strategy_filter)]
        if version_filter:
            filtered_df = filtered_df[filtered_df["Version"].isin(version_filter)]
        if type_filter:
            filtered_df = filtered_df[filtered_df["Type"].isin(type_filter)]
        
        # Show the filtered dataframe
        st.dataframe(filtered_df.drop(columns=["Path"]), use_container_width=True)
        
        # Let user select a run to view details using a selectbox
        if not filtered_df.empty:
            # Create readable labels for the selectbox
            run_labels = [f"{row['Strategy']} - {row['Version']} - {row['Date Range']} ({row['Type']})" 
                         for _, row in filtered_df.iterrows()]
            
            selected_run_label = st.selectbox("Select Run to View Details", run_labels, index=len(run_labels)-1)
            selected_idx = run_labels.index(selected_run_label)
            selected_run_path = filtered_df.iloc[selected_idx]["Path"]
            
            st.subheader("Run Details")
            
            # Create tabs for different types of reports
            details_tab1, details_tab2, details_tab3, details_tab4 = st.tabs([
                "Metadata", "Portfolio", "Trades", "Price History"
            ])
            
            with details_tab1:
                show_metadata(selected_run_path)
            
            with details_tab2:
                show_portfolio_tab(selected_run_path, key_prefix="tab2")
            
            with details_tab3:
                show_trades_tab(selected_run_path, key_prefix="tab3")
            
            with details_tab4:
                show_price_history_tab(selected_run_path, key_prefix="tab4")
    else:
        st.info("No strategy runs found") 