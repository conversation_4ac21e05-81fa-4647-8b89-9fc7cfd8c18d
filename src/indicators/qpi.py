"""qpi.py
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Quantitativo Probability Indicator (QPI)
------------------------------------------------
A reusable implementation of the mean‑reversion indicator described in
Quantitativo's "First Principles" series (Aug 2024).

The QPI answers the question:
    *"How rare is the last *N‑day* price move, conditional on its sign?"*

For a positive move the indicator returns a value in **[0, 100]** where small
numbers denote extreme *up* moves (candidates for short reversion).  For a
negative move, small numbers denote extreme *down* moves (candidates for long
reversion).

The implementation follows the paper as closely as possible and is packaged as
plain Python + Pandas so it can slot straight into the existing ML library.

--------------------------------------------------------------------------
Usage
--------------------------------------------------------------------------
>>> import pandas as pd, yfinance as yf
>>> from qpi import qpi_indicator
>>> px = yf.download('AAPL', start='2015-01-01')['Close']
>>> qpi = qpi_indicator(px)
>>> qpi.tail()

See docstrings below for details.
"""
from __future__ import annotations

import numpy as np
import pandas as pd
from typing import Union, Optional

__all__ = [
    "qpi_indicator",
    "qpi_single",
]

# ---------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------

def _percent_rank(value: float, sample: np.ndarray) -> float:
    """Return the percentile rank (0–100) of *value* within *sample*.

    Ties are handled by assigning the highest possible rank to *value* (same
    behaviour as Excel/RealTest `PERCENTRANK.INC`).  When *sample* has a single
    element we return 100 to avoid division‑by‑zero.
    """
    n = sample.size
    if n <= 1:
        return 100.0
    rank = np.searchsorted(np.sort(sample), value, side="right")
    return (rank - 1) / (n - 1) * 100.0


def _qpi_from_history(recent_ret: float, hist: np.ndarray) -> float:
    """Core QPI formula for one observation given its historical window."""
    if hist.size == 0 or np.all(np.isnan(hist)):
        return np.nan

    # Split histogram at zero to get conditional areas
    neg_mask = hist <= 0
    pos_mask = ~neg_mask
    area_left = np.sum(neg_mask) / hist.size
    area_right = np.sum(pos_mask) / hist.size

    # Fallback guards so we never divide by zero
    area_left = area_left or np.nan
    area_right = area_right or np.nan

    pctrank = _percent_rank(recent_ret, hist)

    if recent_ret <= 0:
        raw_qpi = pctrank / area_left
    else:
        raw_qpi = (100.0 - pctrank) / area_right

    # Clip to the theoretical bounds [0, 100] and return
    return float(np.clip(raw_qpi, 0.0, 100.0))

# ---------------------------------------------------------------------
# Public API
# ---------------------------------------------------------------------

def qpi_single(
    close: Union[pd.Series, np.ndarray],
    window: int = 3,
    lookback_years: int = 5,
    trading_days_per_year: int = 252,
) -> float:
    """Compute a *single* QPI value for the last bar of *close*.

    Parameters
    ----------
    close : pd.Series | np.ndarray
        Daily close prices in chronological order.
    window : int, default 3
        The N‑day return window used in the paper.
    lookback_years : int, default 5
        How many *calendar* years of history to include in the histogram.
    trading_days_per_year : int, default 252
        Approximate number of trading days per year.

    Returns
    -------
    float
        QPI value in the range 0‑100, or *np.nan* if not enough data.
    """
    close_arr = np.asarray(close, dtype="float64")

    lb = lookback_years * trading_days_per_year
    if close_arr.size < lb + window + 1:
        # print(f"Not enough history for {close_arr.size} bars, required {lb + window + 1}")
        return np.nan  # not enough history

    # Most recent N‑day return (exclude current bar from history)
    recent_ret = close_arr[-1] / close_arr[-window - 1] - 1.0

    # Historical window ends *before* the current bar to avoid look‑ahead bias
    hist_rets = close_arr[-lb - 1 : -1] / close_arr[-lb - window - 1 : -window - 1] - 1.0

    return _qpi_from_history(recent_ret, hist_rets)


def qpi_indicator(
    close: pd.Series,
    window: int = 3,
    lookback_years: int = 5,
    trading_days_per_year: int = 252,
    min_periods: Optional[int] = None,
    ) -> pd.Series:
    """Vectorised QPI series.

    Parameters
    ----------
    close : pd.Series
        Daily close prices with a *DateIndex* (or anything `pd.Series` supports).
    window : int, default 3
        N‑day return window.
    lookback_years : int, default 5
        Number of years in the historical histogram.
    trading_days_per_year : int, default 252
        Trading days per year.
    min_periods : int | None
        Override the minimum number of observations required before we start
        returning non‑NaN values.  Defaults to *lookback_years × trading_days*.

    Returns
    -------
    pd.Series
        QPI values aligned with *close.index*.
    """
    if close.isna().any():
        close = close.fillna(method="ffill")

    close_arr = close.values
    lb = lookback_years * trading_days_per_year
    min_periods = lb + window if min_periods is None else min_periods

    out = np.full(close_arr.shape[0], np.nan, dtype="float64")

    # --- main loop (NumPy fast thanks to contiguous slices) -------------
    for i in range(min_periods, close_arr.shape[0]):
        recent_ret = close_arr[i] / close_arr[i - window] - 1.0
        hist_slice = close_arr[i - lb : i]
        hist_rets = hist_slice[window:] / hist_slice[:-window] - 1.0
        out[i] = _qpi_from_history(recent_ret, hist_rets)

    return pd.Series(out, index=close.index, name="qpi")

# ---------------------------------------------------------------------
# If executed as a script run a quick self‑test ------------------------
# ---------------------------------------------------------------------
if __name__ == "__main__":
    import yfinance as yf
    import matplotlib.pyplot as plt

    s = yf.download("META", start="2010-01-01")["Close"]
    # if you still get a DataFrame, squeeze it:
    s = s.squeeze()
    q = qpi_indicator(s)
    
    ax = q.loc["2024-01-01":].plot(figsize=(10, 4), title="QPI")
    ax.set_ylabel("QPI value (0–100)")
    plt.savefig("qpi.png")