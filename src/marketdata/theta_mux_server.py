"""
Theta Mux Server — polygon‑compatible aggregate feed powered by ThetaData trades
==========================================================================

Runs a local WebSocket that *looks* like Polygon.ioʼs aggregates feed (`A` =
1‑second, `AM` = 1‑minute) but builds those bars on the fly from ThetaData raw
trades. Downstream clients can therefore keep using Polygonʼs JSON protocol
without modification, while you avoid Polygonʼs subscription costs.

Quick start
-----------
$ pip install websockets
$ export THETADATA_WS_URL=ws://127.0.0.1:25520/v1/events
$ python theta_mux_server.py

Downstream protocol (exactly the same as Polygonʼs multiplexer example)
-----------------------------------------------------------------------
    {"action":"auth","params":"any-string"}
    {"action":"subscribe","params":"A.AAPL,AM.MSFT"}

Environment variables
---------------------
THETADATA_WS_URL   – upstream WebSocket endpoint (default 127.0.0.1:25520)
THETADATA_API_KEY  – optional – sent in an AUTH message right after connect
HOST               – bind address (default 0.0.0.0)
PORT               – bind port    (default 8765)
"""

from __future__ import annotations

import asyncio
import json
import logging
import os
import secrets
from collections import defaultdict
from datetime import datetime, timezone
from typing import DefaultDict, Dict, List, Set

from zoneinfo import ZoneInfo

import websockets
from websockets.client import WebSocketClientProtocol
from websockets.server import WebSocketServerProtocol
from websockets.exceptions import ConnectionClosedOK

# ─────────────────────────── configuration ────────────────────────────────
logger = logging.getLogger("theta_mux")
logging.basicConfig(
    format="%(asctime)s %(levelname)s %(name)s: %(message)s", level=logging.INFO
)

THETADATA_WS_URL = os.getenv("THETADATA_WS_URL", "ws://127.0.0.1:25520/v1/events")
THETADATA_API_KEY = os.getenv("THETADATA_API_KEY")
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", "9765"))

EST = ZoneInfo("America/New_York")

# ─────────────────────────── helpers ───────────────────────────────────────

class BarState:
    """Incrementally builds an OHLCV bar for a given period starting at *start*."""

    __slots__ = ("start", "open", "high", "low", "close", "volume")

    def __init__(self, start: datetime):
        self.start = start  # tz‑aware (EST)
        self.open: float | None = None
        self.high = float("-inf")
        self.low = float("inf")
        self.close: float | None = None
        self.volume = 0

    def update(self, price: float, size: int):
        if self.open is None:
            self.open = price
        self.high = max(self.high, price)
        self.low = min(self.low, price)
        self.close = price
        self.volume += size

    def finalize(self) -> dict:
        if self.open is None:  # empty bar (no eligible trades)
            o = h = l = c = None
            v = 0
        else:
            o, h, l, c, v = self.open, self.high, self.low, self.close, self.volume
        epoch_ms = int(self.start.astimezone(timezone.utc).timestamp() * 1000)
        return {"t": epoch_ms, "o": o, "h": h, "l": l, "c": c, "v": v}


def floor_time(ts: datetime, seconds: int) -> datetime:
    """Floor *ts* (tz‑aware) to the nearest multiple of *seconds*."""
    epoch = int(ts.timestamp())
    return datetime.fromtimestamp(epoch - epoch % seconds, ts.tzinfo)

# ─────────────────────────── main server ───────────────────────────────────

class ThetaMuxServer:
    """Maintain one ThetaData connection and fan‑out Polygon‑style aggregates."""

    def __init__(self, host: str = HOST, port: int = PORT):
        self.host = host
        self.port = port

        # downstream bookkeeping ------------------------------------------------
        self._clients: Dict[WebSocketServerProtocol, dict] = {}
        self._channel_subs: DefaultDict[str, Set[WebSocketServerProtocol]] = defaultdict(set)

        # upstream bookkeeping ---------------------------------------------------
        self._theta_ws: WebSocketClientProtocol | None = None
        self._send_lock = asyncio.Lock()
        self._symbol_refcount: DefaultDict[str, int] = defaultdict(int)
        self._req_id = secrets.randbits(32)

        # aggregation state ------------------------------------------------------
        # symbol → {"1s": BarState, "1m": BarState}
        self._agg: Dict[str, dict[str, BarState]] = defaultdict(dict)

    # ────────────────── public ───────────────────
    async def start(self):
        async with websockets.serve(self._handle_client, self.host, self.port, ping_interval=None):
            logger.info("Downstream server listening on %s:%s", self.host, self.port)
            await self._thetadata_loop()

    # ---------- graceful-shutdown helper ----------
    async def stop(self):
        """
        Tell ThetaData to terminate every active stream on this connection.
        """
        if self._theta_ws:
            try:
                await self._theta_ws.send(json.dumps({"msg_type": "STOP"}))
            except ConnectionClosedOK:
                pass

    # ────────────────── downstream logic ───────────────────
    async def _handle_client(self, ws: WebSocketServerProtocol):
        self._clients[ws] = {"subs": set(), "auth": False}
        await self._send_status(ws, "connected", "Connected Successfully")
        logger.info("Client connected (%d total)", len(self._clients))

        try:
            async for raw in ws:
                logger.debug("RAW ThetaData message: %s", raw)
                try:
                    msg = json.loads(raw)
                except json.JSONDecodeError:
                    await self._send_error(ws, "Invalid JSON")
                    continue

                action = msg.get("action")
                params = msg.get("params", "")

                if action == "auth":
                    self._clients[ws]["auth"] = True
                    await self._send_status(ws, "auth_success", "authenticated")
                elif action in {"subscribe", "unsubscribe"}:
                    await self._process_subscription(ws, action, params)
                else:
                    await self._send_error(ws, "Unsupported action.")
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            await self._disconnect_client(ws)
            logger.info("Client disconnected (%d remaining)", len(self._clients))

    async def _process_subscription(self, ws: WebSocketServerProtocol, action: str, params: str):
        tokens = [t.strip() for t in params.split(",") if t.strip()]
        channels: List[str] = []

        for tok in tokens:
            if "." in tok:
                prefix, sym = tok.split(".", 1)
            else:
                prefix, sym = "AM", tok
            prefix, sym = prefix.upper(), sym.upper()
            if prefix not in {"A", "AM"}:
                await self._send_error(ws, f"Unsupported prefix '{prefix}'.")
                continue
            channels.append(f"{prefix}.{sym}")

        affected: List[str] = []
        if action == "subscribe":
            for ch in channels:
                if ch in self._clients[ws]["subs"]:
                    continue
                self._clients[ws]["subs"].add(ch)
                self._channel_subs[ch].add(ws)
                affected.append(ch)

                sym = ch.split(".")[1]
                self._symbol_refcount[sym] += 1
                if self._symbol_refcount[sym] == 1:
                    await self._subscribe_upstream(sym)
        else:  # unsubscribe
            for ch in channels:
                if ch not in self._clients[ws]["subs"]:
                    continue
                self._clients[ws]["subs"].remove(ch)
                self._channel_subs[ch].discard(ws)
                affected.append(ch)

                sym = ch.split(".")[1]
                self._symbol_refcount[sym] -= 1
                if self._symbol_refcount[sym] == 0:
                    await self._unsubscribe_upstream(sym)

        if affected:
            await self._send_status(ws, "success", ("subscribed to: " if action == "subscribe" else "unsubscribed from: ") + ",".join(affected))

    async def _disconnect_client(self, ws: WebSocketServerProtocol):
        info = self._clients.pop(ws, None)
        if not info:
            return
        for ch in list(info["subs"]):
            self._channel_subs[ch].discard(ws)
            sym = ch.split(".")[1]
            self._symbol_refcount[sym] -= 1
            if self._symbol_refcount[sym] == 0:
                await self._unsubscribe_upstream(sym)
        try:
            await ws.close()
        except Exception:
            pass

    async def _send_status(self, ws: WebSocketServerProtocol, status: str, message: str):
        await ws.send(json.dumps([{"ev": "status", "status": status, "message": message}]))

    async def _send_error(self, ws: WebSocketServerProtocol, message: str):
        await ws.send(json.dumps([{"ev": "status", "status": "error", "message": message}]))

    # ────────────────── upstream logic ───────────────────
    async def _thetadata_loop(self):
        while True:
            try:
                async with websockets.connect(THETADATA_WS_URL, ping_interval=20) as ws:
                    self._theta_ws = ws
                    logger.info("Connected upstream to ThetaData (%s)", THETADATA_WS_URL)

                    if THETADATA_API_KEY:
                        await ws.send(json.dumps({"msg_type": "AUTH", "api_key": THETADATA_API_KEY}))

                    # resubscribe existing symbols
                    active_syms = [s for s, cnt in self._symbol_refcount.items() if cnt]
                    if active_syms:
                        await self._send_upstream_subscription(active_syms, add=True)

                    await self._consume_thetadata()
            except Exception as exc:
                logger.exception("ThetaData connection error (%s). Reconnecting in 5 s…", exc)
                await asyncio.sleep(5)

    async def _consume_thetadata(self):
        async for raw in self._theta_ws:
            try:
                msg = json.loads(raw)
            except json.JSONDecodeError:
                continue
            if not self._is_trade_message(msg):
                continue
            trade = self._parse_trade(msg)
            sym = trade["symbol"]
            price = trade["price"]
            size = trade["size"]
            ts = trade["timestamp"]

            # update 1‑second bar
            sec_start = floor_time(ts, 1)
            bar_s = self._agg[sym].get("1s")
            if bar_s is None or sec_start > bar_s.start:
                if bar_s:
                    await self._publish_bar(sym, "A", bar_s)
                bar_s = BarState(sec_start)
                self._agg[sym]["1s"] = bar_s
            bar_s.update(price, size)

            # update 1‑minute bar
            min_start = floor_time(ts, 60)
            bar_m = self._agg[sym].get("1m")
            if bar_m is None or min_start > bar_m.start:
                if bar_m:
                    await self._publish_bar(sym, "AM", bar_m)
                bar_m = BarState(min_start)
                self._agg[sym]["1m"] = bar_m
            bar_m.update(price, size)

    # ---------- subscribe helpers ----------
    async def _subscribe_upstream(self, sym: str):
        await self._send_upstream_subscription([sym], add=True)

    async def _unsubscribe_upstream(self, sym: str):
        await self._send_upstream_subscription([sym], add=False)

    async def _send_upstream_subscription(self, symbols: List[str], *, add: bool):
        if not self._theta_ws:
            logger.error("ThetaData connection closed, skipping subscription %s", symbols)
            return
        async with self._send_lock:
            for sym in symbols:
                self._req_id += 1
                msg = {
                    "msg_type": "STREAM",
                    "sec_type": "STOCK",
                    "req_type": "TRADE",
                    "add": add,
                    "id": self._req_id,
                    "contract": {"root": sym},
                }
                await self._theta_ws.send(json.dumps(msg))
                logger.debug("↑ %s %s", "SUB" if add else "UNSUB", sym)

    # ---------- trade parsing ----------
    def _is_trade_message(self, msg: dict) -> bool:
        """
        A real trade message always has
        header.type   == "TRADE"
        contract.root present
        trade         present
        ThetaData keeps header.status = "CONNECTED" even on normal traffic,
        so we must NOT filter on status.
        """
        hdr = msg.get("header", {})
        return (
            hdr.get("type") == "TRADE" and
            "contract" in msg and "root" in msg["contract"] and
            "trade" in msg
        )


    def _parse_trade(self, msg: dict) -> dict:
        t = msg["trade"]
        sym = msg["contract"]["root"].upper()           # ← fixed

        # timestamp
        date = t["date"]                 # YYYYMMDD
        ms   = t["ms_of_day"]            # milliseconds since 00:00
        y, m, d = date // 10000, (date // 100) % 100, date % 100
        sec, ms = divmod(ms, 1000)
        h, mi, s = sec // 3600, (sec % 3600) // 60, sec % 60
        ts = datetime(y, m, d, h, mi, s, ms * 1000, tzinfo=EST)

        return {
            "symbol": sym,
            "price":  t["price"],
            "size":   t["size"],
            "timestamp": ts,
        }


    # ---------- publish to downstream ----------
    async def _publish_bar(self, sym: str, ev_type: str, bar: BarState):
        payload = bar.finalize()
        payload.update({"ev": ev_type, "sym": sym})
        channel = f"{ev_type}.{sym}"
        msg = json.dumps([payload])
        for client in list(self._channel_subs.get(channel, [])):
            try:
                await client.send(msg)
            except websockets.exceptions.ConnectionClosed:
                logger.info("Client disconnected, unable to publish bar for %s", channel)
                await self._disconnect_client(client)

    # ────────────────── helper to run class ───────────────────

async def _main():
    server = ThetaMuxServer()
    try:
        await server.start()           # runs forever until Ctrl-C
    finally:
        # on KeyboardInterrupt or any unhandled exception, send STOP
        await server.stop()

if __name__ == "__main__":
    try:
        asyncio.run(_main())
    except KeyboardInterrupt:
        # _main's finally block already sent STOP
        logger.info("Shutting down.")
