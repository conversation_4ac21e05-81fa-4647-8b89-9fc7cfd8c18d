from __future__ import annotations

import asyncio
import math
import random
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from typing import As<PERSON><PERSON><PERSON>ator, Dict, Tuple

import pandas as pd
from zoneinfo import ZoneInfo

from marketdata.imarketdata import IMarketData, LastQuote
from marketdata.istreamingmarketdata import IStreamingMarketData
from tools.clock import Clock


EST = ZoneInfo("America/New_York")


@dataclass
class SyntheticPriceModelConfig:
    base_price: float = 100.0
    drift_per_bar: float = 0.0  # absolute drift per bar
    volatility_pct: float = 0.003  # % of price move (e.g., 0.003 => 0.3% per bar)
    mean_reversion_strength: float = 0.0  # 0..1, pull toward base
    volume_mean: float = 100_000
    seed: int | None = None


class SyntheticMarketData(IMarketData, IStreamingMarketData):
    """
    A self-contained synthetic market data provider that can:
      - generate historical OHLCV data deterministically per ticker
      - stream new bars indefinitely at a controllable real-time speed

    This implements both IMarketData and IStreamingMarketData so it can be
    passed anywhere either interface is expected (e.g., LocalBroker).
    """

    def __init__(
        self,
        clock: Clock | None = None,
        *,
        model: SyntheticPriceModelConfig | None = None,
        realtime_speed: float = 60.0,
        timezone: ZoneInfo = EST,
    ) -> None:
        """
        Args:
            clock: Optional shared clock. If omitted, a new Clock is created.
            model: Controls the stochastic generation behavior.
            realtime_speed: How many "bar seconds" elapse per wall second.
                             For example, with 1-minute bars (60 seconds),
                             realtime_speed=60 yields 1 bar per second.
            timezone: Timezone for timestamps.
        """
        self.clock = clock or Clock()
        self.model = model or SyntheticPriceModelConfig()
        self.realtime_speed = max(1e-6, realtime_speed)
        self.tz = timezone
        # Per-ticker RNGs for determinism across calls
        self._rng_by_ticker: Dict[str, random.Random] = {}

    # ------------- IMarketData -------------
    def gather_historical_data(
        self, ticker: str, start_dt: datetime, end_dt: datetime | None = None, interval: int = 60
    ) -> pd.DataFrame:
        end_dt = end_dt or self.clock.now()
        start_dt = self._ensure_tz(start_dt)
        end_dt = self._ensure_tz(end_dt)
        interval_td = timedelta(seconds=interval)
        index = pd.date_range(start=start_dt, end=end_dt, freq=pd.DateOffset(seconds=interval), tz=self.tz, inclusive="left")
        if len(index) == 0:
            return self._empty_bars()
        prices, volumes = self._simulate_path(ticker, len(index), start_price=self.model.base_price)
        # Build OHLCV with small candle bodies
        df = pd.DataFrame(index=index, data={
            "open": prices,
            "high": [p * (1.0 + abs(self.model.volatility_pct) * 0.5) for p in prices],
            "low":  [p * (1.0 - abs(self.model.volatility_pct) * 0.5) for p in prices],
            "close": [p * (1.0 + self._rng(ticker).uniform(-0.05, 0.05) * self.model.volatility_pct) for p in prices],
            "volume": volumes,
        })
        df.index.name = "timestamp"
        return df

    async def get_quote_async(
        self, ticker: str, start_dt: datetime | None = None, end_dt: datetime | None = None
    ) -> LastQuote:
        """
        Return a synthetic real-time quote with randomized bid/ask around a stochastic mid.

        The mid is generated using the internal stochastic model; the spread scales with volatility.
        """
        rng = self._rng(ticker)
        # Start from base and take a stochastic step to generate a plausible mid
        mid = self._step_price(ticker, self.model.base_price, rng=rng)
        # Spread proportional to volatility and price; ensure a minimum tick
        spread = max(0.01, abs(self.model.volatility_pct) * 0.2 * max(0.01, mid))
        # Randomize around spread a bit
        spread *= (0.9 + 0.2 * rng.random())
        bid_price = max(0.01, mid - spread / 2.0)
        ask_price = max(bid_price + 0.01, mid + spread / 2.0)
        ask_size = rng.randint(int(self.model.volume_mean * 0.001), int(self.model.volume_mean * 0.01))
        bid_exchange = rng.randint(1, 10)
        return LastQuote(
            ticker=ticker,
            ask_price=ask_price,
            ask_size=ask_size,
            bid_price=bid_price,
            bid_exchange=bid_exchange,
        )

    # ------------- IStreamingMarketData -------------
    async def subscribe_to_bars(
        self,
        ticker: str,
        interval: timedelta,
        start_time: datetime,
        lookback_window: timedelta,
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        start_time = self._ensure_tz(start_time)
        interval_seconds = int(interval.total_seconds())
        if interval_seconds <= 0:
            interval_seconds = 60

        # Seed initial history
        history_start = start_time - lookback_window
        hist = self.gather_historical_data(
            ticker=ticker,
            start_dt=history_start,
            end_dt=start_time,
            interval=interval_seconds,
        )

        # Stream indefinitely
        current_ts = self._align_to_interval(start_time, interval_seconds)
        price = hist["close"].iloc[-1] if not hist.empty else self.model.base_price

        while True:
            price = self._step_price(ticker, price)
            open_px = price
            # small range around open
            high_px = open_px * (1.0 + abs(self.model.volatility_pct) * 0.5)
            low_px  = open_px * (1.0 - abs(self.model.volatility_pct) * 0.5)
            close_px = open_px * (1.0 + self._rng(ticker).uniform(-0.05, 0.05) * self.model.volatility_pct)
            vol = self._rng(ticker).randint(int(self.model.volume_mean * 0.5), int(self.model.volume_mean * 1.5))

            latest = pd.DataFrame(
                {"open": [open_px], "high": [high_px], "low": [low_px], "close": [close_px], "volume": [vol]},
                index=[current_ts],
            )
            latest.index = latest.index.tz_convert(self.tz) if latest.index.tz is not None else latest.index.tz_localize(self.tz)
            latest.index.name = "timestamp"

            yield hist, latest

            # Advance state: append the closed bar to history, move time forward
            hist = pd.concat([hist, latest])
            current_ts = current_ts + timedelta(seconds=interval_seconds)

            # Real-time pacing
            await asyncio.sleep(max(0.0, interval_seconds / self.realtime_speed))

    # ------------- internals -------------
    def _rng(self, ticker: str) -> random.Random:
        if ticker not in self._rng_by_ticker:
            seed = self.model.seed if self.model.seed is not None else (hash(ticker) & 0xFFFFFFFF)
            self._rng_by_ticker[ticker] = random.Random(seed)
        return self._rng_by_ticker[ticker]

    def _simulate_path(self, ticker: str, n: int, start_price: float) -> Tuple[pd.Series, pd.Series]:
        rng = self._rng(ticker)
        prices = []
        price = start_price
        for _ in range(n):
            price = self._step_price(ticker, price, rng=rng)
            prices.append(price)
        vols = [rng.randint(int(self.model.volume_mean * 0.5), int(self.model.volume_mean * 1.5)) for _ in range(n)]
        return pd.Series(prices), pd.Series(vols)

    def _step_price(self, ticker: str, price: float, rng: random.Random | None = None) -> float:
        rng = rng or self._rng(ticker)
        # mean reversion toward base
        reversion = self.model.mean_reversion_strength * (self.model.base_price - price)
        shock = rng.gauss(self.model.drift_per_bar, self.model.volatility_pct * max(1.0, price))
        new_price = max(0.01, price + reversion + shock)
        return new_price

    def _empty_bars(self) -> pd.DataFrame:
        df = pd.DataFrame(columns=["open", "high", "low", "close", "volume"])  # correct schema
        df.index.name = "timestamp"
        return df

    def _ensure_tz(self, dt: datetime) -> datetime:
        if dt.tzinfo is None:
            return dt.replace(tzinfo=self.tz)
        return dt.astimezone(self.tz)

    def _align_to_interval(self, dt: datetime, interval_seconds: int) -> datetime:
        dt = self._ensure_tz(dt)
        epoch = datetime(1970, 1, 1, tzinfo=self.tz)
        seconds = int((dt - epoch).total_seconds())
        aligned = seconds - (seconds % interval_seconds)
        return epoch + timedelta(seconds=aligned)


