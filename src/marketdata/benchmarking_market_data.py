"""benchmarking_market_data.py
---------------------------------------------------------------------
*Zero‑blocking* benchmarking wrapper for **IStreamingMarketData**.

Key points
==========
1. **Main producer path is O(1)** – the `subscribe_to_bars()` generator
   simply enqueues the bar into an internal queue **and immediately yields**
   what it got from the primary provider.
2. **Background comparator task** – a dedicated coroutine consumes a single
   `asyncio.Queue` that receives both primary (`tag='P'`) and secondary
   (`tag='S'`) bars, performs matching / diffing / timeout handling, and
   writes structured JSON lines.
3. **Timeout‑aware** – if the matching secondary bar does not show up within
   *max_wait* (default **10 s**), we still emit a record flagged as `late`.
4. **Clean shutdown** – all background tasks are cancelled when the caller
   exits the async generator.
"""

from __future__ import annotations

import asyncio
import contextlib
import json
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import AsyncGenerator, Dict, Tuple

import pandas as pd

from marketdata.istreamingmarketdata import IStreamingMarketData

# ---------------------------------------------------------------------------
# Helpers & types
# ---------------------------------------------------------------------------

Bar = pd.Series  # one‑row Series with index [open, high, low, close, volume]
_FIELDS = ("open", "high", "low", "close", "volume")


def _pct_diff(a: float, b: float) -> float:
    if pd.isna(a) or pd.isna(b):
        return float("nan")
    if a == b == 0:
        return 0.0
    midpoint = (abs(a) + abs(b)) / 2 or 1e-9
    return abs(a - b) / midpoint * 100


@dataclass
class _BufItem:
    bar: Bar
    arrival: datetime


# ---------------------------------------------------------------------------
# Main implementation
# ---------------------------------------------------------------------------

class BenchmarkingMarketData(IStreamingMarketData):
    """Benchmark two streaming providers without blocking the primary path."""

    def __init__(
        self,
        primary: IStreamingMarketData,
        secondary: IStreamingMarketData,
        *,
        interval: timedelta,
        log_path: str | Path,
        max_wait: timedelta = timedelta(seconds=10),
        logger_name: str | None = None,
    ) -> None:
        self.primary = primary
        self.secondary = secondary
        self.interval = interval
        self.max_wait = max_wait

        # Structured JSON‑per‑line logger
        self._logger = logging.getLogger(logger_name or "BenchmarkingMarketData")
        self._logger.setLevel(logging.INFO)
        fh = logging.FileHandler(str(log_path))
        fh.setFormatter(logging.Formatter("%(message)s"))
        self._logger.addHandler(fh)
        self._logger.propagate = False

    # ------------------------------------------------------------------
    # IStreamingMarketData interface
    # ------------------------------------------------------------------
    async def subscribe_to_bars(
        self,
        ticker: str,
        interval: timedelta,
        start_time: datetime,
        lookback_window: timedelta,
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """Yield bars from *primary* while comparison happens in background."""

        # Unified queue consumed by background comparator
        comp_queue: asyncio.Queue[Tuple[str, datetime, Bar, datetime]] = asyncio.Queue()

        # --------------------------------------------------------------
        # Background: secondary collector – pumps bars into comp_queue
        # --------------------------------------------------------------
        async def _secondary_collector():
            async for hist_s, _ in self.secondary.subscribe_to_bars(
                ticker, interval, start_time, lookback_window
            ):
                if hist_s.empty:
                    continue
                ts = hist_s.index[-1]
                bar = hist_s.iloc[-1]
                await comp_queue.put(("S", ts, bar, datetime.now().astimezone()))

        # --------------------------------------------------------------
        # Background: comparator – matches P vs S, logs, handles timeouts
        # --------------------------------------------------------------
        async def _comparator():
            pending_p: Dict[datetime, _BufItem] = {}
            pending_s: Dict[datetime, _BufItem] = {}

            while True:
                try:
                    tag, ts, bar, arrival = await asyncio.wait_for(
                        comp_queue.get(), timeout=1.0
                    )
                except asyncio.TimeoutError:
                    # Periodic timeout scan
                    _flush_timeouts()
                    continue

                if tag == "P":
                    pending_p[ts] = _BufItem(bar=bar, arrival=arrival)
                    if ts in pending_s:
                        sb = pending_s.pop(ts)
                        _log_match(ts, bar, arrival, sb.bar, sb.arrival)
                        pending_p.pop(ts, None)
                else:  # tag == "S"
                    if ts in pending_p:
                        pb = pending_p.pop(ts)
                        _log_match(ts, pb.bar, pb.arrival, bar, arrival)
                    else:
                        pending_s[ts] = _BufItem(bar=bar, arrival=arrival)

            # nested helper to scan for overdue primary bars
            def _flush_timeouts():
                now = datetime.now().astimezone()
                overdue = [
                    ts_ for ts_, buf in pending_p.items()
                    if now - buf.arrival > self.max_wait
                ]
                for ts_ in overdue:
                    pb = pending_p.pop(ts_)
                    _log_late(ts_, pb.bar, pb.arrival)

            # nested helpers for logging
            def _log_match(ts, bar_p, arr_p, bar_s, arr_s):
                rec = {
                    "ts": ts.isoformat(),
                    "ticker": ticker,
                    "interval": f"{int(self.interval.total_seconds())}s",
                    "late": False,
                    "arrival_primary": arr_p.isoformat(),
                    "arrival_secondary": arr_s.isoformat(),
                    "arrival_diff_sec": (arr_s - arr_p).total_seconds(),
                    "diff_%": {
                        f: _pct_diff(bar_p[f], bar_s[f]) for f in _FIELDS
                    },
                }
                self._logger.info(json.dumps(rec, default=str))

            def _log_late(ts, bar_p, arr_p):
                rec = {
                    "ts": ts.isoformat(),
                    "ticker": ticker,
                    "interval": f"{int(self.interval.total_seconds())}s",
                    "late": True,
                    "arrival_primary": arr_p.isoformat(),
                    "arrival_secondary": None,
                    "arrival_diff_sec": None,
                }
                self._logger.info(json.dumps(rec, default=str))

        # Kick off background tasks
        sec_task = asyncio.create_task(_secondary_collector())
        comp_task = asyncio.create_task(_comparator())

        try:
            # Main (non‑blocking) loop over *primary* bars
            async for hist_p, latest_p in self.primary.subscribe_to_bars(
                ticker, interval, start_time, lookback_window
            ):
                if not hist_p.empty:
                    ts = hist_p.index[-1]
                    bar_p = hist_p.iloc[-1]
                    await comp_queue.put(("P", ts, bar_p, datetime.now().astimezone()))

                # Instantly yield – no heavy work here
                yield hist_p, latest_p
        finally:
            # Clean shutdown
            sec_task.cancel()
            comp_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await sec_task
                await comp_task
