from abc import ABC, abstractmethod
import asyncio
import pandas as pd
from datetime import datetime
from typing import Optional
from dataclasses import dataclass

@dataclass
class LastQuote:
    """LastQuote contains a subset of fields from the most recent NBBO tick."""
    ticker: Optional[str] = None
    ask_price: Optional[float] = None
    ask_size: Optional[int] = None
    bid_price: Optional[float] = None
    bid_exchange: Optional[int] = None

class IMarketData(ABC):
    """
    IMarketData defines the common synchronous interface 
    for retrieving historical market data, along with a default 
    asynchronous counterpart.
    """
    
    @abstractmethod
    def gather_historical_data(
        self, ticker: str, start_dt: datetime, end_dt: Optional[datetime] = None, interval: int = 60
    ) -> pd.DataFrame:
        """
        Retrieves historical market data for the given ticker 
        between start_dt and end_dt synchronously.
        
        :param ticker: The ticker symbol.
        :param start_dt: The start datetime for the data.
        :param end_dt: The end datetime for the data (if omitted, the current datetime is used).
        :param interval: The interval between data points in seconds (default: 60 seconds).
        :return: A pandas DataFrame containing market data.
        """
        pass 

    async def gather_historical_data_async(
        self, ticker: str, start_dt: datetime, end_dt: Optional[datetime] = None, interval: int = 60
    ) -> pd.DataFrame:
        """
        Asynchronously retrieves historical market data by calling the synchronous 
        gather_historical_data method using asyncio.to_thread. This default implementation
        allows asynchronous usage without requiring each subclass to implement async code.
        
        :param ticker: The ticker symbol.
        :param start_dt: The start datetime for the data.
        :param end_dt: The end datetime for the data (if omitted, the current datetime is used).
        :param interval: The interval between data points in seconds (default: 60 seconds).
        :return: A pandas DataFrame containing market data.
        """
        return await asyncio.to_thread(self.gather_historical_data, ticker, start_dt, end_dt, interval)

    def get_quote(
        self, ticker: str, start_dt: Optional[datetime] = None, end_dt: Optional[datetime] = None
    ) -> LastQuote:
        """
        Retrieves a real-time quote for the given ticker. Both start_dt and end_dt parameters are optional.
        If both are omitted, the provider should return data for the last 15 minutes in minute intervals.
        
        The returned DataFrame should have the following structure:
          - Index: "timestamp"
          - Columns: "bid_size", "bid", "ask_size", "ask"
        
        :param ticker: The ticker symbol.
        :param start_dt: The start datetime for the quote data (optional).
        :param end_dt: The end datetime for the quote data (optional).
        :raises NotImplementedError: Always raised to indicate that realtime quotes are not supported in the chosen market data provider.
        """
        raise NotImplementedError("Realtime quotes are not supported in the chosen market data provider.")

    async def get_quote_async(
        self, ticker: str, start_dt: Optional[datetime] = None, end_dt: Optional[datetime] = None
    ) -> LastQuote:
        """
        Asynchronously retrieves a real-time quote for the given ticker by calling the synchronous
        get_realtime_quote method using asyncio.to_thread. This default implementation allows asynchronous
        usage without requiring each subclass to implement async code.
        
        :param ticker: The ticker symbol.
        :param start_dt: The start datetime for the quote data (optional).
        :param end_dt: The end datetime for the quote data (optional).
        """
        return await asyncio.to_thread(self.get_realtime_quote, ticker, start_dt, end_dt)