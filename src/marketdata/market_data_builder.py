import os
from datetime import datetime
from typing import Optional

from dateutil.relativedelta import relativedelta
from tools.clock import Clock

# Polygon implementations
from polygon import RESTClient
from marketdata.polygon_market_data import PolygonMarketData
from marketdata.polygon_disk_market_data import PolygonDiskMarketData
from marketdata.polygon_streaming_market_data import PolygonStreamingMarketData
from marketdata.polygon_bulk_ohlc_data import PolygonBulkOHLCData

# Theta implementations
from marketdata.theta_market_data import ThetaMarketData, ThetaVenue
from marketdata.theta_splicing_market_data import ThetaSplicingMarketData
from marketdata.theta_eod_market_data import ThetaEODMarketData
from marketdata.thetadata_streaming import ThetaDataStreamingMarketData


class MarketDataBuilder:
    """
    Fluent builder for market data providers (Polygon or Theta).

    Usage examples:

        intraday = (MarketDataBuilder()
                       .with_source('polygon')
                       .with_trade_session('full')
                       .with_period('intraday')
                       .build_market_data())

        daily_disk = (MarketDataBuilder()
                          .with_period('daily')
                          .with_disk_data(start_date=datetime(2020, 1, 1))
                          .build_market_data())

        streamer = (MarketDataBuilder()
                        .with_trade_session('full')
                        .build_streaming_market_data())

        bulk_ohlc = MarketDataBuilder().build_bulk_ohlc()
    """

    def __init__(self, clock: Optional[Clock] = None):
        self._clock = clock or Clock()
        self._source = 'polygon'
        self._trade_session = 'rth'
        self._period = 'intraday'
        self._adjusted = True
        self._disk_start: Optional[datetime] = None
        
    def with_adjusted(self, adjusted: bool) -> 'MarketDataBuilder':
        self._adjusted = adjusted
        return self

    def with_source(self, source: str) -> 'MarketDataBuilder':
        if source not in ('polygon', 'theta'):
            raise ValueError(f"Unknown source: {source}. Use 'polygon' or 'theta'.")
        self._source = source
        return self

    def with_trade_session(self, session: str) -> 'MarketDataBuilder':
        if session not in ('rth', 'full'):
            raise ValueError("Trade session must be 'rth' or 'full'.")
        self._trade_session = session
        return self

    def with_period(self, period: str) -> 'MarketDataBuilder':
        if period not in ('intraday', 'daily'):
            raise ValueError("Period must be 'intraday' or 'daily'.")
        self._period = period
        return self

    def with_disk_data(self, start_date: Optional[datetime] = None) -> 'MarketDataBuilder':
        # Only supported for polygon
        if self._source != 'polygon':
            raise ValueError("Disk data only supported for 'polygon' source.")
        if start_date and start_date.tzinfo is not None:
            start_date = start_date.replace(tzinfo=None)
        if start_date and start_date > datetime.now():
            raise ValueError("start_date cannot be in the future.")
        self._disk_start = start_date
        return self

    def build_market_data(self):
        """Build spliced or disk-based market data."""
        # Polygon disk data if requested
        if self._source == 'polygon' and self._disk_start is not None:
            delta = relativedelta(datetime.now(), self._disk_start)
            months = delta.years * 12 + delta.months + (1 if delta.days > 0 else 0)
            return PolygonDiskMarketData(preload_months=months)

        if self._source == 'polygon':
            # Implicitly load and validate API key
            api_key = os.getenv("POLYGON_API_KEY")
            if not api_key:
                raise ValueError("POLYGON_API_KEY environment variable not set or empty.")
            client = RESTClient(api_key)
            realtime = PolygonMarketData(client, adjusted=self._adjusted)
            return realtime
        
        # Theta real/historical splicing
        is_rth = self._trade_session == 'rth'
        theta_realtime = ThetaMarketData(venue=ThetaVenue.NQB, rth=is_rth)
        theta_hist = ThetaMarketData(rth=is_rth)
        return ThetaSplicingMarketData(
            realtime_market_data=theta_realtime,
            historical_market_data=theta_hist
        )

    def build_streaming_market_data(self):
        """Build a streaming wrapper around the configured market data."""
        base = self.build_market_data()
        if self._source == 'polygon':
            # base already validated API key and client
            api_key = os.getenv("POLYGON_API_KEY")
            return PolygonStreamingMarketData(
                market_data_provider=base,
                clock=self._clock,
                api_key=api_key,
                trade_session=self._trade_session
            )
        else:
            return ThetaDataStreamingMarketData(
                market_data_provider=base,
                clock=self._clock,
                use_polling=True,
                clone_data_near_close=False,
                trade_session=self._trade_session
            )

    def build_bulk_ohlc(self):
        """Build bulk OHLC data (polygon only)."""
        if self._source != 'polygon':
            raise NotImplementedError("Bulk OHLC only supported for 'polygon'.")
        # Implicitly require API key for bulk
        api_key = os.getenv("POLYGON_API_KEY")
        if not api_key:
            raise ValueError("POLYGON_API_KEY environment variable not set or empty.")
        return PolygonBulkOHLCData(api_key)
