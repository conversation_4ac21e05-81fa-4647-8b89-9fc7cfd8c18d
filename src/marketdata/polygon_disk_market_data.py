# disk_market_data_polygon.py

from abc import ABC, abstractmethod
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from typing import Optional, List, Dict, Tuple, Set, Union
# Removed pyarrow imports as we'll use pandas for CSV
import pytz
import os
from pathlib import Path
import logging
import json
from dateutil.relativedelta import relativedelta
from marketdata import IMarketData 

logger = logging.getLogger(__name__)
logger.setLevel(logging.ERROR)

class PolygonDiskMarketData(IMarketData):
    """
    A market data provider that reads historical daily data from disk-based
    CSV storage, mirroring Polygon.io's daily flat file format.
    
    Assumes data is stored in a structure like:
    base_dir/
        daily_data/
            YYYY/
                MM/
                    YYYY-MM-DD.gz
        metadata/
            metadata.json
            symbols.json
            
    Metadata files (metadata.json, symbols.json) are assumed to be pre-generated
    by a separate process that scans the daily files.
    """
    
    def __init__(self, base_dir: str = "market_data/polygon", preload_months: int = 0):
        """
        Initialize the Polygon daily disk market data reader.
        
        Args:
            base_dir: Base directory where market data is stored
            preload_months: Number of most recent months to preload into memory (0 for none)
        """
        self.base_dir = Path(base_dir)
        # Changed directory name to reflect daily nature
        self.data_dir = self.base_dir / "daily_data" 
        self.metadata_dir = self.base_dir / "metadata"
        self.preload_months = preload_months
        
        # Use the global logger
        self.logger = logger
        
        # Initialize metadata cache
        self._metadata = None
        self._available_symbols = None
        self._date_ranges = None
        
        # Preloaded data cache
        self._preloaded_data = None
        
        # Ensure the directories exist (for reading)
        if not self.data_dir.exists():
            self.logger.warning(
                f"Data directory not found at {self.data_dir}. Please ensure data is available."
            )
        if not self.metadata_dir.exists():
             self.logger.warning(
                f"Metadata directory not found at {self.metadata_dir}. Metadata-dependent features may fail."
            )
        
        # Load metadata on initialization
        self._load_metadata()
        
        # Preload data if requested
        if self.preload_months > 0:
            self._preload_recent_data()

    def _load_metadata(self) -> None:
        """Load metadata from disk if available."""
        metadata_file = self.metadata_dir / "metadata.json"
        symbols_file = self.metadata_dir / "symbols.json"
        
        if metadata_file.exists() and symbols_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    self._metadata = json.load(f)
                
                with open(symbols_file, 'r') as f:
                    symbols_data = json.load(f)
                    self._available_symbols = set(symbols_data.get('symbols', []))
                    # Store date ranges as strings initially for flexibility
                    self._date_ranges = symbols_data.get('date_ranges', {}) 
                    
                self.logger.info(f"Loaded metadata for {len(self._available_symbols)} symbols.")
                
            except Exception as e:
                self.logger.error(f"Error loading metadata: {str(e)}")
                self._metadata = {}
                self._available_symbols = set()
                self._date_ranges = {}
        else:
            self.logger.warning(f"Metadata files not found in {self.metadata_dir}. "
                                "Available symbols and date ranges may be inaccurate. "
                                "A separate process is needed to generate these files by scanning daily data.")
            self._metadata = {}
            self._available_symbols = set() # Can't know symbols without scanning or metadata
            self._date_ranges = {}
            # Attempt to populate available symbols by listing directories if metadata fails?
            # This could be slow and is better handled by a dedicated metadata generation step.
            # For now, rely solely on the metadata files.

    @property
    def available_symbols(self) -> Set[str]:
        """Get set of available ticker symbols from metadata."""
        if self._available_symbols is None:
            # Attempt to load again if not loaded initially
            self._load_metadata()
        return self._available_symbols if self._available_symbols is not None else set()

    def _get_daily_file_path(self, target_date: date) -> Path:
        """Get file path for a specific date."""
        year = target_date.year
        month = target_date.month
        day = target_date.day
        
        # Try both .csv.gz and .csv extensions
        base_path = self.data_dir / str(year) / f"{month:02d}" / f"{year}-{month:02d}-{day:02d}"
        path_gz = base_path.with_suffix(".gz")
        path_csv = base_path.with_suffix(".csv")
        
        if path_gz.exists():
            return path_gz
        elif path_csv.exists():
            return path_csv
        else:
            # Return the expected path even if it doesn't exist, 
            # let the caller handle non-existence
            return path_gz 

    def _get_date_range_for_ticker(self, ticker: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """Get the available date range for a specific ticker from metadata."""
        if self._date_ranges is None:
            self._load_metadata() # Ensure metadata is loaded

        if ticker in self._date_ranges:
            start_str = self._date_ranges[ticker].get('start')
            end_str = self._date_ranges[ticker].get('end')
            
            # Parse ISO format strings from metadata. Assume they represent dates.
            # Convert to datetime at midnight EST for consistency
            est = pytz.timezone('US/Eastern')
            start_dt = datetime.fromisoformat(start_str).replace(tzinfo=est) if start_str else None
            end_dt = datetime.fromisoformat(end_str).replace(tzinfo=est) if end_str else None

            # Ensure they are datetime objects at the beginning of the day in EST
            if start_dt:
                 start_dt = est.localize(datetime.combine(start_dt.date(), datetime.min.time()))
            if end_dt:
                 end_dt = est.localize(datetime.combine(end_dt.date(), datetime.min.time()))

            return start_dt, end_dt
        
        # If not in metadata, return None
        self.logger.warning(f"Date range for ticker '{ticker}' not found in metadata.")
        return None, None

    def _get_file_paths_for_date_range(
        self, start_dt: datetime, end_dt: datetime
    ) -> List[Path]:
        """Get list of daily file paths that cover the given date range."""
        
        # Ensure datetimes are timezone-aware (use US/Eastern)
        est = pytz.timezone('US/Eastern')
        if start_dt.tzinfo is None:
            start_dt = est.localize(start_dt)
        else:
            start_dt = start_dt.astimezone(est)
            
        if end_dt.tzinfo is None:
            end_dt = est.localize(end_dt)
        else:
            end_dt = end_dt.astimezone(est)

        # Iterate day by day
        current_date = start_dt.date()
        end_date = end_dt.date()
        
        file_paths = []
        while current_date <= end_date:
            file_path = self._get_daily_file_path(current_date)
            # Check existence here to avoid adding paths for missing days
            if file_path.exists():
                file_paths.append(file_path)
            else:
                 # Log missing files for specific dates if needed (can be verbose)
                 # self.logger.debug(f"No data file found for date {current_date}")
                 pass 
            
            # Move to next day
            current_date += timedelta(days=1)
            
        return file_paths

    def gather_historical_data(
        self, ticker: str, start_dt: datetime, end_dt: Optional[datetime] = None, interval: int = 86400
    ) -> pd.DataFrame:
        """
        Retrieves historical daily market data for the given ticker 
        between start_dt and end_dt from daily CSV files or preloaded data if available.
        
        Args:
            ticker: The ticker symbol (case-sensitive, matching CSV content)
            start_dt: The start datetime for the data (inclusive)
            end_dt: The end datetime (inclusive, defaults to current EST time)
            interval: Data interval in seconds. Only 86400 (daily) is supported.
        
        Returns:
            DataFrame with historical market data (open, high, low, close, volume, transactions), 
            indexed by date (datetime objects representing midnight EST).
            Returns an empty DataFrame if the ticker is not found in metadata, 
            no files are found, or no data for the ticker exists in the date range.
        """
        # Validate ticker against metadata
        # Case-sensitive check
        if ticker not in self.available_symbols:
             self.logger.warning(f"Ticker '{ticker}' not found in available symbols metadata. Returning empty DataFrame.")
             return pd.DataFrame()

        # Set default end date if not provided (use current time in EST)
        est = pytz.timezone('US/Eastern')
        if end_dt is None:
            end_dt = datetime.now(est)
        
        # Ensure datetimes are timezone-aware and localized/converted to EST
        if start_dt.tzinfo is None:
            start_dt = est.localize(start_dt)
        else:
            start_dt = start_dt.astimezone(est)
        if end_dt.tzinfo is None:
            end_dt = est.localize(end_dt)
        else:
            end_dt = end_dt.astimezone(est)

        # Validate interval
        if interval != 86400:
            self.logger.warning("Only daily data (interval=86400) is currently supported. Returning empty DataFrame.")
            return pd.DataFrame()
        
        if self._preloaded_data is not None and not self._preloaded_data.empty:
            df = self._preloaded_by_symbol[ticker]
            df = df[(df['date'] >= start_dt) & (df['date'] <= end_dt)].copy()
            
            if not df.empty:
                df.set_index('date', inplace=True)
                self.logger.info(f"Using preloaded data for {ticker}")
                return df
            else: # No point in going through file paths if we have preloaded
                return df

        # Get file paths for the date range
        file_paths = self._get_file_paths_for_date_range(start_dt, end_dt)
        
        if not file_paths:
            self.logger.warning(f"No data files found for the date range {start_dt.date()} to {end_dt.date()}.")
            return pd.DataFrame()
        
        self.logger.info(f"Found {len(file_paths)} potential data files for range {start_dt.date()} to {end_dt.date()}.")

        # --- Define expected columns and dtypes for robustness ---
        # Based on stocks_day_candlesticks_example.csv
        expected_columns = {
            'ticker': str,
            'volume': float, # Use float to handle potential NaNs gracefully
            'open': float,
            'close': float,
            'high': float,
            'low': float,
            'window_start': np.int64, # Read as integer first
            'transactions': float # Use float for NaNs
        }
        
        # Columns to keep in the final DataFrame
        final_columns = ['open', 'high', 'low', 'close', 'volume']

        # Load, filter, and process data day by day
        daily_dfs = []
        processed_files = 0
        for file_path in file_paths:
            try:
                # Determine compression based on extension
                compression = 'gzip' if file_path.suffix == '.gz' else None
                
                # Read the CSV for the day
                # Use low_memory=False to prevent dtype warnings with mixed types if any
                day_df = pd.read_csv(
                    file_path, 
                    usecols=expected_columns.keys(), # Read only necessary columns
                    dtype=expected_columns,          # Specify dtypes
                    na_values=[''],                  # Treat empty strings as NaN
                    keep_default_na=True,
                    low_memory=False,
                    compression=compression
                )

                # Filter for the specific ticker *after* loading
                # Ensure case-sensitive comparison matches how symbols are stored
                ticker_data = day_df[day_df['ticker'] == ticker].copy() # Use .copy() to avoid SettingWithCopyWarning

                if not ticker_data.empty:
                    # Convert 'window_start' (Unix nanoseconds) to datetime index (Midnight EST)
                    # 1. Convert ns to UTC datetime
                    # 2. Convert UTC to EST
                    # 3. Normalize to midnight (removes time part but keeps date and tz)
                    ticker_data['date'] = pd.to_datetime(ticker_data['window_start'], unit='ns', utc=True)
                    ticker_data['date'] = ticker_data['date'].dt.tz_convert('US/Eastern')
                    ticker_data['date'] = ticker_data['date'].dt.normalize() # Set time to 00:00:00

                    # Set the new 'date' column as the index
                    ticker_data = ticker_data.set_index('date')

                    # Keep only the relevant financial columns
                    ticker_data = ticker_data[final_columns]

                    # Optional: Add validation for OHLC relationships (h>=l, h>=o, h>=c, l<=o, l<=c)
                    # (Can add this later if needed)

                    daily_dfs.append(ticker_data)
                    processed_files += 1

            except FileNotFoundError:
                self.logger.warning(f"File not found: {file_path}")
            except pd.errors.EmptyDataError:
                 self.logger.warning(f"File is empty: {file_path}")
            except KeyError as e:
                 self.logger.error(f"Missing expected column in {file_path}: {e}")
            except Exception as e:
                self.logger.error(f"Error processing file {file_path}: {str(e)}")
        
        if not daily_dfs:
            self.logger.warning(f"No data found for ticker '{ticker}' within the specified date range in the processed files.")
            return pd.DataFrame()
        
        # Combine data from all relevant daily files
        combined_df = pd.concat(daily_dfs)
        
        # Sort by date (index)
        sorted_df = combined_df.sort_index()

        # Final filter by exact start/end datetime (already done by file selection, but good practice)
        # The index is already timezone-aware (EST)
        sorted_df = sorted_df[(sorted_df.index >= start_dt) & (sorted_df.index <= end_dt)]
        
        self.logger.info(f"Successfully gathered {len(sorted_df)} records for '{ticker}' from {processed_files} files.")
        
        return sorted_df
    
    def list_available_tickers(self) -> List[str]:
        """Get list of all available tickers from metadata."""
        return sorted(list(self.available_symbols))
    
    def get_available_symbols(self) -> Set[str]:
        """Get set of available ticker symbols from metadata."""
        return set(list(self.available_symbols))

    def get_data_date_range(self, ticker: str = None) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        Get the date range of available data from metadata.
        
        Args:
            ticker: Optional ticker to get specific date range.
                   If None, returns the overall date range of the dataset from metadata.
        
        Returns:
            Tuple of (start_date, end_date) as datetime objects (Midnight EST).
            May be (None, None) if data or metadata is not available.
        """
        if self._metadata is None:
             self._load_metadata() # Ensure metadata is loaded

        if ticker:
            return self._get_date_range_for_ticker(ticker)
        
        # If no specific ticker, return overall date range from metadata
        start_str = self._metadata.get('start_date')
        end_str = self._metadata.get('end_date')
        
        # Parse ISO format strings from metadata. Assume they represent dates.
        # Convert to datetime at midnight EST for consistency
        est = pytz.timezone('US/Eastern')
        start_dt = datetime.fromisoformat(start_str).replace(tzinfo=est) if start_str else None
        end_dt = datetime.fromisoformat(end_str).replace(tzinfo=est) if end_str else None

        # Ensure they are datetime objects at the beginning of the day in EST
        if start_dt:
             start_dt = est.localize(datetime.combine(start_dt.date(), datetime.min.time()))
        if end_dt:
             end_dt = est.localize(datetime.combine(end_dt.date(), datetime.min.time()))
             
        return start_dt, end_dt

    def _preload_recent_data(self) -> None:
        """Preload the most recent months of market data into memory."""
        self.logger.info(f"Preloading {self.preload_months} months of market data...")
        
        # Get current date in EST timezone
        est = pytz.timezone('US/Eastern')
        current_date = datetime.now(est).date()
        
        # Calculate the start date for preloading
        start_date = date(current_date.year, current_date.month, 1) - relativedelta(months=self.preload_months-1)
        
        # Get all dates in the range
        start_dt = est.localize(datetime.combine(start_date, datetime.min.time()))
        end_dt = est.localize(datetime.combine(current_date, datetime.min.time()))
        
        # Get all file paths for the date range
        file_paths = self._get_file_paths_for_date_range(start_dt, end_dt)
        
        if not file_paths:
            self.logger.warning(f"No data files found for preloading in range {start_date} to {current_date}")
            self._preloaded_data = pd.DataFrame()
            return
            
        self.logger.info(f"Found {len(file_paths)} files to preload from {start_date} to {current_date}")
        
        # --- Define expected columns and dtypes ---
        expected_columns = {
            'ticker': str,
            'volume': float,
            'open': float,
            'close': float,
            'high': float,
            'low': float,
            'window_start': np.int64,
            'transactions': float
        }
        
        # Load data from all daily files
        all_dfs = []
        processed_files = 0
        
        for file_path in file_paths:
            try:
                # Determine compression based on extension
                compression = 'gzip' if file_path.suffix == '.gz' else None
                
                # Read the CSV file
                df = pd.read_csv(
                    file_path,
                    usecols=expected_columns.keys(),
                    dtype=expected_columns,
                    na_values=[''],
                    keep_default_na=True,
                    low_memory=False,
                    compression=compression
                )
                
                if not df.empty:
                    # Convert 'window_start' to datetime, localize to EST
                    df['date'] = pd.to_datetime(df['window_start'], unit='ns', utc=True)
                    df['date'] = df['date'].dt.tz_convert('US/Eastern')
                    df['date'] = df['date'].dt.normalize()  # Set time to midnight
                    
                    # Rename ticker column to symbol for consistency
                    df = df.rename(columns={'ticker': 'symbol'})
                    
                    # Keep only necessary columns
                    df = df[['date', 'open', 'high', 'low', 'close', 'volume', 'symbol']]
                    
                    # Convert volume to int64
                    df['volume'] = df['volume'].fillna(0).astype(np.int64)
                    
                    all_dfs.append(df)
                    processed_files += 1
                    
                    if processed_files % 10 == 0:
                        self.logger.info(f"Processed {processed_files}/{len(file_paths)} files...")
                
            except Exception as e:
                self.logger.error(f"Error reading file {file_path}: {str(e)}")
        
        if not all_dfs:
            self.logger.warning("No data was preloaded")
            self._preloaded_data = pd.DataFrame()
            return
            
        # Combine all dataframes
        self._preloaded_data = pd.concat(all_dfs, ignore_index=True)
        # Partition preloaded data by symbol into a dict for fast access
        self._preloaded_by_symbol = {
            symbol: df.drop(columns='symbol')
            for symbol, df in self._preloaded_data.groupby('symbol')
        }
        self.logger.info(f"Successfully preloaded {len(self._preloaded_data)} records from {processed_files} files")
        
    def get_preloaded_data(self) -> pd.DataFrame:
        """
        Returns the preloaded market data.
        
        Returns:
            DataFrame containing all preloaded market data, or empty DataFrame if no data was preloaded.
            The DataFrame has columns: date, open, high, low, close, volume, symbol.
        """
        if self._preloaded_data is None:
            self.logger.warning("No data has been preloaded. Use preload_months parameter during initialization.")
            return pd.DataFrame()
        
        return self._preloaded_data.copy()

# --- CLI Functionality (Adapted) ---

def parse_arguments():
    """Parse command line arguments for the CLI."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Polygon Daily Market Data Explorer CLI")
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Command to execute', required=True)
    
    # Interactive command
    interactive_parser = subparsers.add_parser('interactive', help='Load data for a ticker and start interactive session')
    interactive_parser.add_argument('--ticker', '-t', type=str, required=True,
                                   help='Ticker symbol to load data for (case-sensitive)')
    interactive_parser.add_argument('--start-date', '-s', type=str, required=True,
                                   help='Start date (YYYY-MM-DD)')
    interactive_parser.add_argument('--end-date', '-e', type=str, default=None,
                                   help='End date (YYYY-MM-DD), defaults to today')
    interactive_parser.add_argument('--base-dir', '-d', type=str, default="market_data/polygon",
                                   help='Base directory for market data')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List available tickers from metadata')
    list_parser.add_argument('--base-dir', '-d', type=str, default="market_data/polygon",
                            help='Base directory for market data')
    
    # Info command
    info_parser = subparsers.add_parser('info', help='Show date range information from metadata')
    info_parser.add_argument('--ticker', '-t', type=str, default=None,
                            help='Ticker symbol to show info for (optional, case-sensitive)')
    info_parser.add_argument('--base-dir', '-d', type=str, default="market_data/polygon",
                            help='Base directory for market data')
    
    return parser.parse_args()

def main_cli():
    """Main function for the CLI."""
    args = parse_arguments()
    
    # Initialize the reader
    reader = PolygonDiskMarketData(base_dir=args.base_dir)
    
    if args.command == 'interactive':
        # Parse dates
        try:
            # Assume dates are given as local time, treat as EST start of day
            est = pytz.timezone('US/Eastern')
            start_date = est.localize(datetime.strptime(args.start_date, "%Y-%m-%d"))
            end_date = est.localize(datetime.strptime(args.end_date, "%Y-%m-%d")) if args.end_date else None
            
            # If end_date is specified, make it inclusive by setting time to end of day,
            # but since we work with daily files, comparing dates is sufficient.
            # The gather_historical_data function handles inclusivity correctly based on dates.

        except ValueError:
            print("Error: Invalid date format. Please use YYYY-MM-DD.")
            return 1
            
        print(f"Loading data for {args.ticker} from {start_date.date()}...")
        df = reader.gather_historical_data(args.ticker, start_date, end_date) # end_date can be None
        
        if df.empty:
            print(f"No data found for {args.ticker} in the specified date range.")
            # Add a check: was the ticker even in the metadata?
            if args.ticker not in reader.available_symbols:
                 print(f"Note: Ticker '{args.ticker}' was not found in the metadata.")
            return 1
        
        print(f"\nLoaded {len(df)} records for {args.ticker}")
        print(f"Date range in DataFrame: {df.index.min().date()} to {df.index.max().date()}")
        print("\nSample data (first 5 rows):")
        print(df.head())
        print("\nLast 5 rows:")
        print(df.tail())
        
        # Drop into IPython shell
        print("\nDropping into IPython shell.")
        print("Variables available:")
        print("  - df: Pandas DataFrame with the loaded market data")
        print("  - reader: PolygonDiskMarketData instance")
        print(f"  - ticker: '{args.ticker}'")
        print(f"  - start_date: {start_date}")
        print(f"  - end_date: {end_date if end_date else 'Default (now)'}")
        
        try:
            from IPython import embed
            # Create a namespace dictionary for the embedded shell
            namespace = dict(df=df, reader=reader, ticker=args.ticker, start_date=start_date, end_date=end_date)
            embed(user_ns=namespace, colors="neutral", 
                  header="--- Exiting IPython shell ---")
        except ImportError:
            print("\nIPython is not installed. Interactive session requires IPython.")
            print("Install it with: pip install ipython")
            # Fallback: just print the data and exit
            # (or could start a basic Python console, but IPython is better)
            return 1
            
    elif args.command == 'list':
        # List available tickers from metadata
        tickers = reader.list_available_tickers()
        
        if not tickers:
            print(f"No tickers found. Ensure metadata files exist and are populated in {reader.metadata_dir}")
            return 1
        
        print(f"Available tickers listed in metadata ({len(tickers)}):")
        # Print tickers in columns for better readability if many
        col_width = max(len(t) for t in tickers) + 2 # Find max length for alignment
        num_cols = max(1, os.get_terminal_size().columns // col_width) # Adjust columns to terminal width
        
        for i, ticker in enumerate(tickers):
            print(f"{ticker:<{col_width}}", end="")
            if (i + 1) % num_cols == 0:
                print() # Newline after num_cols
        if len(tickers) % num_cols != 0: # Ensure final newline
             print() 

    elif args.command == 'info':
        # Show information about available data from metadata
        start_date, end_date = reader.get_data_date_range(args.ticker)
        
        if args.ticker:
             # Check if ticker exists in metadata first
            if args.ticker not in reader.available_symbols:
                 print(f"Ticker '{args.ticker}' not found in available symbols metadata.")
                 return 1
                 
            print(f"Metadata Date Range for {args.ticker}:")
            print(f"  Start: {start_date.date() if start_date else 'N/A'}")
            print(f"  End:   {end_date.date() if end_date else 'N/A'}")
            if start_date is None or end_date is None:
                 print("  (Date range information missing or incomplete in metadata)")
        else:
            tickers = reader.list_available_tickers()
            print("Overall Market Data Store Information (from metadata):")
            print(f"  Total tickers listed: {len(tickers)}")
            print(f"  Overall Start Date:   {start_date.date() if start_date else 'N/A'}")
            print(f"  Overall End Date:     {end_date.date() if end_date else 'N/A'}")
            if start_date is None or end_date is None:
                 print("  (Overall date range information missing or incomplete in metadata)")

            # Show some example tickers if available
            if tickers:
                print(f"  Sample tickers: {', '.join(tickers[:5])}{'...' if len(tickers) > 5 else ''}")
            else:
                 print("  No tickers listed in metadata.")
            print(f"\n  Data Directory:  {reader.data_dir}")
            print(f"  Metadata Directory: {reader.metadata_dir}")
            
    return 0

if __name__ == "__main__":
    import sys
    # Setup logging for the script when run directly
    # logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    sys.exit(main_cli())