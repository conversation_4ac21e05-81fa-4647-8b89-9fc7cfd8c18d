import asyncio, weakref
import logging
# Set httpx logger to WARNING level (or higher) to suppress INFO logs
logging.getLogger("httpx").setLevel(logging.WARNING)

from datetime import datetime, timedelta
from typing import Optional

import httpx
import pandas as pd
import pytz

from .imarketdata import IMarketData

# Create a module-level logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class ThetaVenue:
    NQB = "nqb"
    UTP_CTA = "utp_cta"

class ThetaMarketData(IMarketData):
    def __init__(
        self, 
        base_url: str = "http://127.0.0.1:25510", 
        venue: str = ThetaVenue.UTP_CTA,
        prune_zero_prices: bool = True,
        rth: bool = False,
        timeout: int = 60,  # Default timeout of 60 seconds
        max_concurrent_requests: int = 4
    ):
        """
        Initialize ThetaMarketData with a base URL for Theta Terminal.
        
        :param base_url: The base URL where the Theta terminal API is hosted.
        :param venue: The venue to use for historical data.
        :param prune_zero_prices: If True, prunes rows where the 'open' or 'close' columns equal zero.
                                  If False, the data is left intact.
        :param rth: If True, only returns data during regular trading hours.
                    If False, returns data for all hours.
        :param timeout: Timeout in seconds for HTTP requests (default: 60 seconds).
        :param max_concurrent_requests: Maximum number of parallel requests (default: 8).
        """
        self.base_url = base_url
        self.venue = venue
        self.prune_zero_prices = prune_zero_prices
        self.rth = rth
        self.timeout = timeout
        # store the concurrency limit, but create the semaphore lazily
        self._max_concurrent_requests = max_concurrent_requests
        # each running event-loop gets its own semaphore
        self._semaphores: weakref.WeakKeyDictionary[
            asyncio.AbstractEventLoop, asyncio.Semaphore
        ] = weakref.WeakKeyDictionary()

    def _convert_to_dataframe(self, data: dict) -> pd.DataFrame:
        """
        Convert Theta's JSON response into a pandas DataFrame.
        Expects the response to have a header with a "format" list
        and a "response" list containing rows of data.
        
        Both the historical and quote endpoints include a "date" and "ms_of_day"
        field which are combined to form a timezone-aware timestamp (US/Eastern).
        
        :param data: JSON data returned from the API.
        :return: A pandas DataFrame with a datetime index.
        """
        # If no response data, return an empty DataFrame.
        if "response" not in data or not data["response"]:
            return pd.DataFrame()

        # Determine the column names from the header.
        columns = data.get("header", {}).get("format", None)
        if columns is None:
            logger.warning("No header format provided in response. Using default column names.")
            columns = [f"col_{i}" for i in range(len(data["response"][0]))]

        df = pd.DataFrame(data["response"], columns=columns)

        if "date" in df.columns and "ms_of_day" in df.columns:
            # Combine the 'date' (YYYYMMDD) and 'ms_of_day' (milliseconds since midnight ET)
            # to produce a proper timestamp.
            df["timestamp"] = (
                pd.to_datetime(df["date"].astype(str), format="%Y%m%d")
                + pd.to_timedelta(df["ms_of_day"], unit="ms")
            )
            # Localize the timestamp to US/Eastern.
            df["timestamp"] = df["timestamp"].dt.tz_localize("US/Eastern")
            df.set_index("timestamp", inplace=True)
            # Drop the now redundant date and ms_of_day columns.
            df.drop(columns=["date", "ms_of_day"], inplace=True)

        # Filter out rows where the 'open' or 'close' columns are zero,
        # but only if prune_zero_prices is True.
        if self.prune_zero_prices:
            if "open" in df.columns and "close" in df.columns:
                df = df[(df["open"] != 0) & (df["close"] != 0)]
            else:
                # Fallback in case 'open' and 'close' columns are not present.
                df = df[(df != 0).any(axis=1)]
        
        return df

    # ------------------------------------------------------------
    # internal helper – always call this inside a coroutine
    # ------------------------------------------------------------
    def _get_semaphore(self) -> asyncio.Semaphore:
        """
        Return an event-loop-local semaphore, creating one on demand.
        This prevents “bound to a different event loop” errors when the
        synchronous wrappers spawn fresh loops via `asyncio.run`.
        """
        loop = asyncio.get_running_loop()
        sem = self._semaphores.get(loop)
        if sem is None:
            sem = asyncio.Semaphore(self._max_concurrent_requests)
            self._semaphores[loop] = sem
        return sem

    async def gather_historical_data_async(
        self, 
        ticker: str, 
        start_dt: datetime, 
        end_dt: Optional[datetime] = None,
        interval: int = 60
    ) -> pd.DataFrame:
        """
        Asynchronously retrieves historical OHLC market data from Theta.
        Uses the venue specified during class initialization.
        
        :param ticker: The ticker symbol.
        :param start_dt: Start datetime.
        :param end_dt: End datetime; if None, uses the current Eastern time.
        :param interval: The interval between data points in seconds (default: 60 seconds).
        :return: A pandas DataFrame containing OHLC data.
                 Returns an empty DataFrame if end_dt is not at least 1 second after start_dt.
        """
        eastern = pytz.timezone("US/Eastern")
        now_eastern = datetime.now(eastern)
        
        explicit_end_dt = end_dt
        if explicit_end_dt is None:
            end_dt = now_eastern

        # Return empty DataFrame if end_dt is not at least 1 second after start_dt
        if (end_dt - start_dt).total_seconds() < 1:
            return pd.DataFrame()

        # Compute the milliseconds since midnight for start_dt and end_dt
        start_time_ms = int(
            (start_dt - start_dt.replace(hour=0, minute=0, second=0, microsecond=0)).total_seconds() * 1000
        )
        end_time_ms = int(
            (end_dt - end_dt.replace(hour=0, minute=0, second=0, microsecond=0)).total_seconds() * 1000
        )

        # Convert interval from seconds to milliseconds
        interval_ms = interval * 1000

        # Round both times to nearest interval
        start_time_ms = (start_time_ms // interval_ms) * interval_ms
        end_time_ms = (end_time_ms // interval_ms) * interval_ms

        # Ensure end_time is greater than start_time by at least the interval
        if end_time_ms <= start_time_ms + interval_ms:
            end_time_ms = start_time_ms + interval_ms  # Add exactly one interval

        params = {
            "root": ticker,
            "start_date": start_dt.strftime("%Y%m%d"),
            "end_date": end_dt.strftime("%Y%m%d"),
            "ivl": interval_ms,  # Use the provided interval in milliseconds
            "venue": self.venue,
            "rth": str(self.rth).lower()
        }

        # Only add start_time (and end_time only if end_dt was explicitly passed) when start_dt and end_dt are on the same day
        if start_dt.date() == end_dt.date():
            if explicit_end_dt is not None:
                params.update({
                    "start_time": str(start_time_ms),
                    "end_time": str(end_time_ms)
                })
            else:
                params.update({
                    "start_time": str(start_time_ms)
                })

        url = f"{self.base_url}/v2/hist/stock/ohlc"

        accumulated_data = []
        first_page_header = None

        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(self.timeout)) as client:
                next_url = url
                current_params = params
                while True:
                    async with self._get_semaphore():
                        response = await client.get(next_url, params=current_params)
                    response.raise_for_status()
                    data = response.json()

                    # Save the header from the first page (assumed consistent across pages)
                    if first_page_header is None:
                        first_page_header = data.get("header", {})

                    # Accumulate response data from this page
                    if "response" in data and data["response"]:
                        accumulated_data.extend(data["response"])

                    # Attempt to get the next page URL.
                    next_page = response.headers.get("Next-Page") or data.get("header", {}).get("next_page")
                    if next_page and next_page != "null":
                        next_url = next_page
                        logger.info("Next URL: %s", next_page)
                        current_params = None  # next_page URL contains all required parameters
                    else:
                        break
        except Exception as e:
            # Skip logging for HTTP status 472 and 473 errors.
            if isinstance(e, httpx.HTTPStatusError) and e.response.status_code in {472, 473}:
                logger.debug(f"Error encountered while retrieving historical data: {str(e)}")
            else:
                logger.error(f"Error encountered while retrieving historical data: {str(e)}", exc_info=True)
            return pd.DataFrame()

        # Combine accumulated data into one payload to convert into a DataFrame.
        final_data = {"header": first_page_header, "response": accumulated_data}
        df = self._convert_to_dataframe(final_data)
        
        return df

    async def _get_historical_quote_async(
        self,
        ticker: str,
        start_dt: Optional[datetime] = None,
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Asynchronously retrieves historical NBBO quote data from Theta via the 
        /v2/hist/stock/quote endpoint using pagination. Aggregates using 1-minute intervals (ivl=60000)
        as per the API documentation.

        :param ticker: The ticker symbol.
        :param start_dt: The starting datetime. If not provided, current Eastern time is used.
        :param end_dt: The ending datetime. If not provided, start_dt is used.
        :return: A pandas DataFrame containing the historical quote data.
        """
        eastern = pytz.timezone("US/Eastern")
        now_eastern = datetime.now(eastern)
        if start_dt is None:
            start_dt = now_eastern
        start_date_str = start_dt.strftime("%Y%m%d")
        # Default end date to start date if not provided.
        end_date_str = end_dt.strftime("%Y%m%d") if end_dt else start_date_str

        params = {
            "root": ticker,
            "start_date": start_date_str,
            "end_date": end_date_str,
            "ivl": 60000,  # 1-minute interval
            "use_csv": "false",
            "rth": str(self.rth).lower()
        }
        url = f"{self.base_url}/v2/hist/stock/quote"

        # Pagination logic: accumulate data from multiple pages if available.
        accumulated_data = []
        first_page_header = None

        async with httpx.AsyncClient(timeout=httpx.Timeout(self.timeout)) as client:
            next_url = url
            current_params = params
            while True:
                async with self._get_semaphore():
                    response = await client.get(next_url, params=current_params)
                response.raise_for_status()
                data = response.json()
                
                if first_page_header is None:
                    first_page_header = data.get("header", {})
                
                if "response" in data and data["response"]:
                    accumulated_data.extend(data["response"])
                    
                # Check for pagination via the 'Next-Page' header or from the JSON header.
                next_page = response.headers.get("Next-Page") or data.get("header", {}).get("next_page")
                if next_page and next_page != "null":
                    next_url = next_page
                    current_params = None  # Next pages already have parameters embedded in the URL.
                else:
                    break

        final_data = {"header": first_page_header, "response": accumulated_data}
        df = self._convert_to_dataframe(final_data)
        return df

    async def get_quote_async(
        self, 
        ticker: str, 
        start_dt: Optional[datetime] = None, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Asynchronously retrieves a market quote from Theta.
        
        If the requested quote time (using end_dt if provided, or start_dt otherwise)
        is more than 15 minutes in the past relative to current Eastern time, this method 
        retrieves historical NBBO quote data via the /v2/hist/stock/quote endpoint.
        Otherwise, it uses the realtime snapshot at /v2/at_time/stock/quote.

        :param ticker: The ticker symbol.
        :param start_dt: Optional datetime specifying the quote time. If not provided, current time is used.
        :param end_dt: Optional datetime; if provided it becomes the reference time for historical data.
                       (If end_dt is None, start_dt is used.)
        :return: A pandas DataFrame containing the market quote data.
        """
        eastern = pytz.timezone("US/Eastern")
        now_eastern = datetime.now(eastern)
        # Use start_dt if provided; if not, use the current time.
        dt = start_dt if start_dt is not None else now_eastern
        # Use end_dt as the reference time if provided; otherwise, dt is used.
        ref_dt = end_dt if end_dt is not None else dt

        # If the reference time is more than 15 minutes in the past, use historical data.
        if (now_eastern - ref_dt) > timedelta(minutes=15):
            return await self._get_historical_quote_async(ticker, start_dt, end_dt)
        else:
            # Otherwise, use the realtime snapshot through the at_time endpoint.
            midnight = dt.replace(hour=0, minute=0, second=0, microsecond=0)
            # For the at_time endpoint, ivl represents the ms offset into the day.
            ivl = int((dt - midnight).total_seconds() * 1000)
            date_str = dt.strftime("%Y%m%d")
            params = {
                "root": ticker,
                "start_date": date_str,
                "end_date": date_str,
                "ivl": ivl,
                "use_csv": "false",
                "rth": str(self.rth).lower()
            }
            url = f"{self.base_url}/v2/at_time/stock/quote"
            async with httpx.AsyncClient(timeout=httpx.Timeout(self.timeout)) as client:
                async with self._get_semaphore():
                    response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
            df = self._convert_to_dataframe(data)
            return df

    def gather_historical_data(
        self, 
        ticker: str, 
        start_dt: datetime, 
        end_dt: Optional[datetime] = None,
        interval: int = 60
    ) -> pd.DataFrame:
        """
        Synchronously retrieves historical OHLC market data by wrapping the asynchronous version.
        
        The start_time and end_time are automatically derived from start_dt and end_dt.
        
        :param ticker: The ticker symbol.
        :param start_dt: Start datetime.
        :param end_dt: End datetime.
        :param interval: The interval between data points in seconds (default: 60 seconds).
        :return: A pandas DataFrame containing OHLC data.
        """
        return asyncio.run(
            self.gather_historical_data_async(ticker, start_dt, end_dt, interval)
        )

    def get_quote(
        self, 
        ticker: str, 
        start_dt: Optional[datetime] = None, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Synchronously retrieves a market quote by wrapping the asynchronous version.
        
        :param ticker: The ticker symbol.
        :param start_dt: Optional datetime specifying the quote time.
        :param end_dt: Ignored.
        :return: A pandas DataFrame containing the market quote.
        """
        return asyncio.run(self.get_quote_async(ticker, start_dt, end_dt))