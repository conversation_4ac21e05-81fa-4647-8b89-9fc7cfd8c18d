#!/usr/bin/env python
"""
Backfill script to download EOD market data from Theta and save it using DiskMarketDataWriter.
"""

import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date, time
import logging
from pathlib import Path
import sys
from tqdm import tqdm
import time
import random
from typing import List, Dict, Optional
import pandas_market_calendars as mcal

# Import the DiskMarketDataWriter and ThetaEODMarketData
from marketdata.disk_market_data_writer import DiskMarketDataWriter
from marketdata.disk_market_data import DiskMarketData
from marketdata.theta_eod_market_data import ThetaEODMarketData, ThetaSplicingMarketData
import pytz

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("backfill_theta_eod")
est_tz = pytz.timezone('US/Eastern')

def read_ticker_list(file_path: str) -> List[str]:
    """
    Read a list of tickers from a file, one ticker per line.
    
    Args:
        file_path: Path to the file containing tickers
        
    Returns:
        List of ticker symbols
    """
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"Ticker file not found: {file_path}")
    
    with open(path, 'r') as f:
        # Read lines, strip whitespace, and filter out empty lines
        tickers = [line.strip() for line in f.readlines()]
        tickers = [ticker for ticker in tickers if ticker]
    
    return tickers


def is_trading_day(date_to_check: datetime) -> bool:
    """
    Check if a given date is a trading day in the US stock market.
    
    Args:
        date_to_check: Date to check
        
    Returns:
        True if it's a trading day, False otherwise
    """
    # Get NYSE calendar
    nyse = mcal.get_calendar('NYSE')
    
    # Convert to date if it's a datetime
    if isinstance(date_to_check, datetime):
        date_to_check = date_to_check.date()
    
    # Get trading days for the month containing the date
    start_of_month = date(date_to_check.year, date_to_check.month, 1)
    if date_to_check.month == 12:
        end_of_month = date(date_to_check.year + 1, 1, 1) - timedelta(days=1)
    else:
        end_of_month = date(date_to_check.year, date_to_check.month + 1, 1) - timedelta(days=1)
    
    trading_days = nyse.valid_days(start_date=start_of_month, end_date=end_of_month)
    
    # Convert trading_days to dates for comparison
    trading_dates = [d.date() for d in trading_days]
    
    return date_to_check in trading_dates


def get_nearest_trading_day(date_to_adjust: datetime, direction: str = 'backward') -> datetime:
    """
    Get the nearest trading day to the given date.
    
    Args:
        date_to_adjust: Date to adjust
        direction: 'backward' to get the previous trading day, 'forward' to get the next
        
    Returns:
        Nearest trading day as datetime
    """
    nyse = mcal.get_calendar('NYSE')
    
    # Convert to date if it's a datetime
    if isinstance(date_to_adjust, datetime):
        dt_time = date_to_adjust.time()
        date_to_adjust = date_to_adjust.date()
    else:
        dt_time = time(0, 0)
    
    # Set search range (30 days should be more than enough)
    if direction == 'backward':
        start_date = date_to_adjust - timedelta(days=30)
        end_date = date_to_adjust
    else:  # forward
        start_date = date_to_adjust
        end_date = date_to_adjust + timedelta(days=30)
    
    # Get trading days in the range
    trading_days = nyse.valid_days(start_date=start_date, end_date=end_date)
    
    if len(trading_days) == 0:
        # No trading days found in range
        return date_to_adjust
    
    if direction == 'backward':
        # Get the most recent trading day
        nearest_date = trading_days[-1].date()
    else:
        # Get the next trading day
        nearest_date = trading_days[0].date()
    
    # Convert back to datetime with original time
    return datetime.combine(nearest_date, dt_time)


def adjust_date_range_for_trading_days(start_date: datetime, end_date: datetime) -> tuple[datetime, datetime]:
    """
    Adjust start and end dates to be valid trading days.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        Tuple of adjusted (start_date, end_date)
    """
    # Adjust start date to the nearest previous trading day
    adjusted_start = get_nearest_trading_day(start_date, 'backward')
    
    # Adjust end date to the nearest previous trading day (can't use future data)
    today = est_tz.localize(datetime.now().replace(hour=0, minute=0, second=0, microsecond=0))
    if end_date > today:
        end_date = today
    
    adjusted_end = get_nearest_trading_day(end_date, 'backward')
    
    # Ensure start date is not after end date
    if adjusted_start > adjusted_end:
        adjusted_start = adjusted_end
    
    return est_tz.localize(adjusted_start), est_tz.localize(adjusted_end)


def get_theta_eod_data(
    theta_client: ThetaEODMarketData,
    ticker: str, 
    start_date: datetime, 
    end_date: Optional[datetime] = None
) -> pd.DataFrame:
    """
    Fetch EOD data from Theta API for a given ticker.
    
    Args:
        theta_client: ThetaEODMarketData instance
        ticker: Ticker symbol
        start_date: Start date for data
        end_date: End date for data (defaults to today)
        
    Returns:
        DataFrame with EOD data
    """
    # Default end_date to today if not provided
    if end_date is None:
        end_date = datetime.now(pytz.timezone('US/Eastern'))
    
    # Ensure start_date and end_date are timezone-aware
    if start_date.tzinfo is None:
        start_date = est_tz.localize(start_date)
    if end_date.tzinfo is None:
        end_date = est_tz.localize(end_date)
    
    # Adjust dates to be valid trading days
    adjusted_start, adjusted_end = adjust_date_range_for_trading_days(start_date, end_date)
    
    # If dates are the same or adjusted_end is before adjusted_start, there's no valid trading day
    if adjusted_start > adjusted_end:
        logger.warning(f"No valid trading days in the requested range for {ticker}")
        return pd.DataFrame()
    
    try:
        # Get data from Theta
        df = theta_client.gather_historical_data(ticker, adjusted_start, adjusted_end)
        
        # Ensure we have the required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                logger.warning(f"Required column '{col}' missing from Theta data for {ticker}")
                return pd.DataFrame()
        
        # Reset index to get date as a column
        if isinstance(df.index, pd.DatetimeIndex):
            df = df.reset_index()
            # Rename 'index' to 'date' if needed
            if 'index' in df.columns:
                df = df.rename(columns={'index': 'date'})
            elif 'timestamp' in df.columns:
                df = df.rename(columns={'timestamp': 'date'})
        
        # Ensure all required columns exist
        required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns for {ticker}: {missing_columns}")
            logger.info(f"Available columns: {df.columns.tolist()}")
            return pd.DataFrame()
        
        return df
    
    except Exception as e:
        # Check if it's a 472 error (no data for date)
        if "472" in str(e):
            logger.warning(f"No data available for {ticker} in the requested date range (HTTP 472)")
        else:
            logger.error(f"Error fetching data for {ticker}: {str(e)}")
        return pd.DataFrame()


def backfill_tickers(
    tickers: List[str],
    start_date: datetime,
    end_date: Optional[datetime] = None,
    base_dir: str = "market_data",
    batch_size: int = 10,
    delay: float = 0.5,
    jitter: float = 0.2,
    theta_base_url: str = "http://127.0.0.1:25510",
    skip_existing: bool = True
) -> Dict[str, bool]:
    """
    Backfill market data for a list of tickers.
    
    Args:
        tickers: List of ticker symbols
        start_date: Start date for data
        end_date: End date for data (defaults to today)
        base_dir: Base directory for market data storage
        batch_size: Number of tickers to process before saving
        delay: Base delay between API calls in seconds
        jitter: Random jitter to add to delay (±jitter)
        theta_base_url: Base URL for Theta API
        skip_existing: If True, only fetch data for missing date ranges
        
    Returns:
        Dictionary mapping tickers to success status
    """
    # Default end_date to today if not provided
    if end_date is None:
        end_date = datetime.now(pytz.timezone('US/Eastern'))
    
    # Ensure start_date and end_date are timezone-aware
    est_tz = pytz.timezone('US/Eastern')
    if start_date.tzinfo is None:
        start_date = est_tz.localize(start_date)
    if end_date.tzinfo is None:
        end_date = est_tz.localize(end_date)
    
    # Initialize market data writer and reader
    writer = DiskMarketDataWriter(base_dir=base_dir)
    reader = DiskMarketData(base_dir=base_dir)
    
    # Initialize Theta EOD client
    theta_client = ThetaEODMarketData(base_url=theta_base_url)
    
    # Track success/failure for each ticker
    results = {}
    
    # Process tickers with progress bar
    data_batch = {}
    
    for i, ticker in enumerate(tqdm(tickers, desc="Backfilling tickers")):
        try:
            if skip_existing and ticker in reader.available_symbols:
                # Get existing data range for this ticker
                existing_start, existing_end = reader.get_data_date_range(ticker)
                
                if existing_start and existing_end:
                    # Ensure existing_start and existing_end are timezone-aware
                    if existing_start.tzinfo is None:
                        existing_start = est_tz.localize(existing_start)
                    if existing_end.tzinfo is None:
                        existing_end = est_tz.localize(existing_end)
                    
                    # Adjust dates to valid trading days
                    adjusted_start, _ = adjust_date_range_for_trading_days(start_date, start_date)
                    _, adjusted_end = adjust_date_range_for_trading_days(end_date, end_date)
                    
                    # Determine if we need to fetch any data
                    need_earlier_data = adjusted_start < existing_start
                    need_later_data = adjusted_end > existing_end
                    
                    if not need_earlier_data and not need_later_data:
                        logger.info(f"Skipping {ticker} - data already exists for requested date range")
                        results[ticker] = True
                        continue
                    
                    # Initialize combined dataframe
                    combined_df = pd.DataFrame()
                    
                    # Fetch earlier data if needed
                    if need_earlier_data:
                        earlier_end = existing_start - timedelta(days=1)
                        logger.info(f"Fetching earlier data for {ticker}: {adjusted_start.date()} to {earlier_end.date()}")
                        earlier_df = get_theta_eod_data(
                            theta_client, 
                            ticker, 
                            adjusted_start, 
                            earlier_end
                        )
                        if not earlier_df.empty:
                            combined_df = pd.concat([combined_df, earlier_df], ignore_index=True)
                    
                    # Fetch later data if needed
                    if need_later_data:
                        later_start = existing_end + timedelta(days=1)
                        logger.info(f"Fetching later data for {ticker}: {later_start.date()} to {adjusted_end.date()}")
                        later_df = get_theta_eod_data(
                            theta_client, 
                            ticker, 
                            later_start, 
                            adjusted_end
                        )
                        if not later_df.empty:
                            combined_df = pd.concat([combined_df, later_df], ignore_index=True)
                    
                    if combined_df.empty:
                        logger.warning(f"No new data returned for {ticker}")
                        results[ticker] = False
                        continue
                    
                    # Get existing data - use naive datetimes for gather_historical_data
                    existing_df = reader.gather_historical_data(
                        ticker, 
                        start_date.replace(tzinfo=None), 
                        end_date.replace(tzinfo=None)
                    )
                    
                    # Reset index to get date as a column if it's the index
                    if isinstance(existing_df.index, pd.DatetimeIndex):
                        existing_df = existing_df.reset_index()
                    
                    # Ensure date columns are compatible
                    if 'date' in existing_df.columns and 'date' in combined_df.columns:
                        # Make both date columns timezone-naive for consistent comparison
                        if pd.api.types.is_datetime64_any_dtype(existing_df['date']):
                            if existing_df['date'].dt.tz is not None:
                                existing_df['date'] = existing_df['date'].dt.tz_localize(None)
                        
                        if pd.api.types.is_datetime64_any_dtype(combined_df['date']):
                            if combined_df['date'].dt.tz is not None:
                                combined_df['date'] = combined_df['date'].dt.tz_localize(None)
                    
                    # Combine with existing data
                    df = pd.concat([existing_df, combined_df], ignore_index=True)
                    
                    # Remove duplicates and sort
                    df = df.drop_duplicates(subset=['date']).sort_values('date').reset_index(drop=True)
                else:
                    # Ticker exists but no date range info, fetch full range
                    df = get_theta_eod_data(theta_client, ticker, start_date, end_date)
            else:
                # Ticker not in available symbols or skip_existing is False, fetch full range
                df = get_theta_eod_data(theta_client, ticker, start_date, end_date)
            
            if df.empty:
                logger.warning(f"No data returned for {ticker}")
                results[ticker] = False
                continue
            
            # Add to batch
            data_batch[ticker] = df
            results[ticker] = True
            
            # Process batch if we've reached batch_size or this is the last ticker
            if len(data_batch) >= batch_size or i == len(tickers) - 1:
                if data_batch:  # Only try to add if there's data
                    logger.info(f"Adding batch of {len(data_batch)} tickers to storage")
                    writer.add_data_batch(data_batch)
                data_batch = {}  # Reset batch
            
            # Add delay with jitter to avoid rate limiting
            if i < len(tickers) - 1:  # No need to delay after the last ticker
                sleep_time = delay + random.uniform(-jitter, jitter)
                time.sleep(max(0, sleep_time))  # Ensure non-negative sleep time
                
        except Exception as e:
            logger.error(f"Error processing {ticker}: ", e)
            results[ticker] = False
    
    # Summary
    success_count = sum(1 for status in results.values() if status)
    logger.info(f"Backfill complete: {success_count}/{len(tickers)} tickers successful")
    
    return results


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Backfill EOD market data from Theta")
    
    parser.add_argument(
        "--ticker-file", "-f", 
        type=str, 
        required=True,
        help="File containing ticker symbols (one per line)"
    )
    
    parser.add_argument(
        "--start-date", "-s", 
        type=str, 
        default="2024-01-01",
        help="Start date for data (YYYY-MM-DD, default: 2024-01-01)"
    )
    
    parser.add_argument(
        "--end-date", "-e", 
        type=str, 
        default=None,
        help="End date for data (YYYY-MM-DD, default: today)"
    )
    
    parser.add_argument(
        "--base-dir", "-d", 
        type=str, 
        default="market_data",
        help="Base directory for market data storage"
    )
    
    parser.add_argument(
        "--batch-size", "-b", 
        type=int, 
        default=10,
        help="Number of tickers to process before saving (default: 10)"
    )
    
    parser.add_argument(
        "--delay", 
        type=float, 
        default=0.5,
        help="Base delay between API calls in seconds (default: 0.5)"
    )
    
    parser.add_argument(
        "--jitter", 
        type=float, 
        default=0.2,
        help="Random jitter to add to delay (default: ±0.2 seconds)"
    )
    
    parser.add_argument(
        "--output", "-o", 
        type=str, 
        default=None,
        help="Output file to save results (default: None)"
    )
    
    parser.add_argument(
        "--theta-url", 
        type=str, 
        default="http://127.0.0.1:25510",
        help="Base URL for Theta API (default: http://127.0.0.1:25510)"
    )
    
    parser.add_argument(
        "--skip-existing",
        action="store_true",
        help="Skip fetching data for date ranges that already exist (default: True)"
    )
    
    parser.add_argument(
        "--force-update",
        action="store_true",
        help="Force update all data even if it already exists"
    )
    
    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()
    
    try:
        # Parse dates
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d") if args.end_date else None
        
        # Read ticker list
        logger.info(f"Reading tickers from {args.ticker_file}")
        tickers = read_ticker_list(args.ticker_file)
        logger.info(f"Found {len(tickers)} tickers to process")
        
        if not tickers:
            logger.error("No tickers found in the file")
            return 1
        
        # Determine whether to skip existing data
        skip_existing = not args.force_update if hasattr(args, 'force_update') else True
        
        # Backfill data
        logger.info(f"Starting backfill from {start_date} to {end_date or 'today'}")
        logger.info(f"Skip existing data: {skip_existing}")
        
        results = backfill_tickers(
            tickers=tickers,
            start_date=start_date,
            end_date=end_date,
            base_dir=args.base_dir,
            batch_size=args.batch_size,
            delay=args.delay,
            jitter=args.jitter,
            theta_base_url=args.theta_url,
            skip_existing=skip_existing
        )
        
        # Save results if output file specified
        if args.output:
            output_path = Path(args.output)
            
            # Create a DataFrame with results
            result_df = pd.DataFrame({
                'ticker': list(results.keys()),
                'success': list(results.values())
            })
            
            # Save to CSV
            result_df.to_csv(output_path, index=False)
            logger.info(f"Results saved to {output_path}")
        
        # Print summary
        success_count = sum(1 for status in results.values() if status)
        logger.info(f"Backfill complete: {success_count}/{len(tickers)} tickers successful")
        
        # Print failed tickers if any
        failed_tickers = [ticker for ticker, status in results.items() if not status]
        if failed_tickers:
            logger.warning(f"Failed tickers ({len(failed_tickers)}): {', '.join(failed_tickers[:10])}")
            if len(failed_tickers) > 10:
                logger.warning(f"... and {len(failed_tickers) - 10} more")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error in backfill process: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main()) 