import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Optional

import pandas as pd
import pytz
import logging

from .imarketdata import IMarketData

class AutoRefreshingMarketData(IMarketData):
    
    def __init__(self, delegate_market_data: IMarketData, extended_hours: bool = True, stale_check_interval: int = 120):
        """
        Initialize AutoRefreshingMarketData with a delegate market data provider and an extended_hours flag.
        
        The stale_check_interval parameter determines the number of seconds after which cached data is 
        considered stale when using gather_historical_data_async.
        
        The delegate_market_data is used to retrieve the actual market data.
        
        The extended_hours parameter determines which market hours to consider:
          - If True, full market hours are used:
              Pre-market: 4:00 AM - 9:30 AM EST
              Regular: 9:30 AM - 4:00 PM EST
              After-hours: 4:00 PM - 8:00 PM EST
          - If False, only regular trading hours (9:30 AM - 4:00 PM EST) are considered.
          
        This class maintains a cache of recently accessed tickers. Each cached
        entry stores:
          - data : the historical data as a pandas DataFrame,
          - last_refresh : the timestamp representing the last time new data was fetched,
          - last_access : the last time this ticker was requested.
          
        If a ticker is not accessed for more than one hour, it is removed from the cache.
        Meanwhile, a background asynchronous task runs continuously to refresh each ticker's
        cached data if it is more than stale_check_interval seconds old.
        """
        self.delegate = delegate_market_data
        # Cache format: { ticker: {'data': DataFrame, 'last_refresh': datetime, 'last_access': datetime} }
        self.cache = {}
        self.cache_lock = asyncio.Lock()
        self.running = True
        # Using US/Eastern for consistency with our delegate (e.g., ThetaMarketData)
        self.eastern = pytz.timezone("US/Eastern")
        self.bg_task = None
        # If extended_hours is True, full market hours are used.
        self.extended_hours = extended_hours
        # Stale check interval in seconds (default is 120s).
        self.stale_check_interval = stale_check_interval
        # Capture the event loop in which this instance is created.
        self.loop = asyncio.get_running_loop()

    def gather_historical_data(
        self, 
        ticker: str, 
        start_dt: datetime, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Synchronously retrieves historical OHLC market data by wrapping the asynchronous version.
        
        The start_time and end_time are automatically derived from start_dt and end_dt.
        
        :param ticker: The ticker symbol.
        :param start_dt: Start datetime.
        :param end_dt: End datetime.
        :return: A pandas DataFrame containing OHLC data.
        """
        future = asyncio.run_coroutine_threadsafe(
            self.gather_historical_data_async(ticker, start_dt, end_dt),
            self.loop
        )
        return future.result()
        
    async def gather_historical_data_async(
        self, 
        ticker: str, 
        start_dt: datetime, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Retrieves historical OHLC market data for the specified ticker.
        
        When called:
          - If the ticker has not been cached or its last refresh is older than stale_check_interval seconds,
            call the delegate's gather_historical_data_async to (re)fetch the data.
          - Otherwise, return the cached historical data filtered to the requested time range.
        
        On the very first request, the entire requested range is fetched from the delegate.
        Subsequent updates merge in new rows (fetched for the range starting from the last refresh
        minus a 10-minute buffer until now) into the cached DataFrame.
        """
        now = datetime.now(self.eastern)
        if end_dt is None:
            end_dt = now

        async with self.cache_lock:
            # Start the background refresh loop if not already started,
            # creating it under the lock prevents starting multiple background tasks.
            if self.bg_task is None:
                self.bg_task = asyncio.create_task(self._background_refresh_loop())
            
            if ticker not in self.cache:
                logging.info(f"Initial fetch for ticker {ticker} from {start_dt} to {end_dt}")
                df = await self.delegate.gather_historical_data_async(ticker, start_dt, end_dt)
                last_refresh = df.index.max() if not df.empty else now
                self.cache[ticker] = {
                    'data': df,
                    'last_refresh': last_refresh,
                    'last_access': now
                }
            else:
                # Update last_access for the ticker.
                self.cache[ticker]['last_access'] = now
                last_refresh = self.cache[ticker]['last_refresh']
                if (now - last_refresh).total_seconds() >= self.stale_check_interval:
                    new_start = last_refresh - timedelta(minutes=10)
                    new_end = now
                    logging.info(f"Refreshing ticker {ticker} data from {new_start} to {new_end}")
                    new_df = await self.delegate.gather_historical_data_async(ticker, new_start, new_end)
                    if not new_df.empty:
                        cached_df = self.cache[ticker]['data']
                        merged = pd.concat([cached_df, new_df])
                        # Drop any duplicate indices (keeping the latest row).
                        merged = merged[~merged.index.duplicated(keep='last')]
                        merged.sort_index(inplace=True)
                        self.cache[ticker]['data'] = merged
                        self.cache[ticker]['last_refresh'] = merged.index.max()
                    else:
                        # If no new data, update the refresh time.
                        self.cache[ticker]['last_refresh'] = now

            df = self.cache[ticker]['data']

        filtered_df = df.loc[(df.index >= start_dt) & (df.index <= end_dt)]
        return filtered_df

    async def _refresh_ticker(self, ticker: str, now: datetime):
        """
        Refresh data for a single ticker. This helper fetches new data from the delegate,
        and then re-acquires the lock to update the cache.
        """
        # First, get the current last_refresh from the cache.
        async with self.cache_lock:
            entry = self.cache.get(ticker)
            if entry is None:
                return  # ticker might have been removed concurrently
            last_refresh = entry['last_refresh']
            new_start = last_refresh - timedelta(minutes=10)
        new_end = now
        try:
            logging.debug(f"Background refresh for ticker {ticker} from {new_start} to {new_end}")
            new_df = await self.delegate.gather_historical_data_async(ticker, new_start, new_end)
            async with self.cache_lock:
                entry = self.cache.get(ticker)
                if entry is None:
                    return
                if not new_df.empty:
                    merged = pd.concat([entry['data'], new_df])
                    merged = merged[~merged.index.duplicated(keep='last')]
                    merged.sort_index(inplace=True)
                    entry['data'] = merged
                    # Use the query end time instead of the last data point
                    entry['last_refresh'] = new_end
                else:
                    entry['last_refresh'] = new_end
        except Exception as e:
            logging.error(f"Error refreshing ticker {ticker}: {e}")

    async def _refresh_ticker_with_semaphore(self, ticker: str, now: datetime, semaphore: asyncio.Semaphore):
        """
        Wrap ticker refresh with a semaphore to limit the number of concurrent refresh operations.
        """
        async with semaphore:
            await self._refresh_ticker(ticker, now)

    async def _background_refresh_loop(self):
        """
        A background loop that periodically refreshes cached tickers in parallel, up
        to 25 tickers at a time.
        """
        while self.running:
            await asyncio.sleep(1)
            now = datetime.now(self.eastern)
            to_refresh = []
            tickers_to_remove = []

            # Under the lock, decide which tickers need refreshing (or removal)
            async with self.cache_lock:
                for ticker, entry in list(self.cache.items()):
                    # Remove tickers not accessed in the last hour.
                    if (now - entry['last_access']).total_seconds() > 3600 * 24 * 7: # 1 week to straddle holidays/weekends
                        tickers_to_remove.append(ticker)
                        continue
                    # Check if the ticker needs to be refreshed.
                    if (now - entry['last_refresh']).total_seconds() >= 60:
                        if self._within_market_hours(now):
                            to_refresh.append(ticker)
                        else:
                            hours_mode = "extended (pre/after market)" if self.extended_hours else "regular trading"
                            logging.debug(f"Skipping background refresh for ticker {ticker} because current time {now.time()} is outside of {hours_mode} hours.")
                            entry['last_refresh'] = now

                # Remove inactive tickers outside of the per-ticker loop.
                for ticker in tickers_to_remove:
                    logging.info(f"Removing ticker {ticker} from cache due to inactivity.")
                    del self.cache[ticker]

            # Set up a semaphore to limit concurrency to 25 tickers at a time.
            semaphore = asyncio.Semaphore(50)
            refresh_tasks = [
                asyncio.create_task(self._refresh_ticker_with_semaphore(ticker, now, semaphore))
                for ticker in to_refresh
            ]
            if refresh_tasks:
                await asyncio.gather(*refresh_tasks)

    async def get_quote_async(
        self, 
        ticker: str, 
        start_dt: Optional[datetime] = None, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Not implemented.
        """
        raise NotImplementedError("get_quote_async is not implemented in AutoRefreshingMarketData.")

    def get_quote(
        self, 
        ticker: str, 
        start_dt: Optional[datetime] = None, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Not implemented.
        """
        raise NotImplementedError("get_quote is not implemented in AutoRefreshingMarketData.")

    async def shutdown(self):
        """
        Shuts down the background refresh loop.
        """
        self.running = False
        if self.bg_task:
            try:
                await self.bg_task
            except asyncio.exceptions.CancelledError:
                # Optionally log or pass if cancellation is expected.
                logging.info("Background refresh task cancelled during shutdown.")

    def _within_market_hours(self, now: datetime) -> bool:
        """
        Check if the given datetime `now` is within market hours based on the extended_hours flag.
        
        If extended_hours is True, full market hours are considered:
          - Pre-market: 4:00 AM to 9:30 AM EST
          - Regular hours: 9:30 AM to 4:00 PM EST
          - After-hours: 4:00 PM to 8:00 PM EST
        If extended_hours is False, only regular trading hours (9:30 AM to 4:00 PM EST) are considered.
        
        Returns True if `now` is within these hours, False otherwise.
        """
        from datetime import time
        current_time = now.time()
        if self.extended_hours:
            return time(4, 0) <= current_time < time(20, 0)
        else:
            return time(9, 30) <= current_time < time(16, 0)

async def main():
    import pytz
    from datetime import datetime
    import argparse
    

    # Basic logging configuration so that logs are printed to the console.
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
 
    # Parse arguments (or hard-code them)
    parser = argparse.ArgumentParser(description="Auto Refreshing Market Data Collector")
    parser.add_argument("--ticker", type=str, default="AAPL", help="Ticker symbol (default: AAPL)")
    parser.add_argument("--start_dt", type=str, default="20250212", help="Start date in YYYYMMDD format")
    parser.add_argument("--end_dt", type=str, default="20250219", help="End date in YYYYMMDD format")
    args = parser.parse_args()

    eastern = pytz.timezone("US/Eastern")
    start_dt = eastern.localize(datetime.strptime(args.start_dt, "%Y%m%d"))
    end_dt = eastern.localize(datetime.strptime(args.end_dt, "%Y%m%d"))

    # Import your delegate market data provider.
    from theta_market_data import ThetaMarketData

    delegate = ThetaMarketData()
    market_data = AutoRefreshingMarketData(delegate)

    print("Testing gather_historical_data...")
    # Call the async method directly
    historical_df = await market_data.gather_historical_data_async(args.ticker, start_dt, end_dt)
    if not historical_df.empty:
        print("\nHistorical Data Retrieved:")
        print(historical_df)
    else:
        print("\nNo historical data retrieved.")

    # Wait long enough (e.g., 60 seconds) for the background refresh loop to run
    await asyncio.sleep(60)
    await market_data.shutdown()

if __name__ == "__main__":
    import asyncio
    import nest_asyncio
    nest_asyncio.apply()
    
    asyncio.run(main())
