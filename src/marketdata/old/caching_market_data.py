from datetime import datetime
from typing import Optional, Dict
import pandas as pd

from .imarketdata import IMarketData

class CachingMarketData(IMarketData):
    """
    CachingMarketData wraps a delegate IMarketData and caches market data over a specified datetime window.
    
    The delegate is expected to return data as a pandas DataFrame whose index is a timezone‑aware 
    timestamp (e.g., US/Eastern as in PolygonMarketData). All queries on this cache filter based on 
    the timestamp index. A ValueError is raised if the requested datetime range is not a subset 
    of the caching window.
    """
    def __init__(self, delegate: IMarketData, clock, cache_start: datetime, cache_end: datetime):
        if cache_start > cache_end:
            raise ValueError("Caching window start cannot be after caching window end.")
        self.delegate = delegate
        self.clock = clock
        self.cache_start = cache_start
        self.cache_end = cache_end
        
        # Caches for each ticker and for each method.
        self._historical_cache: Dict[str, pd.DataFrame] = {}
        self._quote_cache: Dict[str, pd.DataFrame] = {}

    def gather_historical_data(
        self, ticker: str, start: datetime, end: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Retrieves historical market data for the specified ticker and datetime range by filtering 
        the cached DataFrame on its index.
        
        Parameters:
            ticker : The ticker symbol.
            start  : The start datetime for the query.
            end    : The end datetime for the query. If omitted, defaults to the caching window's end.
            
        Returns:
            pd.DataFrame: Filtered historical market data.
        
        Raises:
            ValueError: If the requested datetime range is not fully within the caching window.
        """
        if end is None:
            end = self.cache_end

        # Ensure the requested range is within the caching window.
        if start < self.cache_start or end > self.cache_end:
            raise ValueError("Requested data range is out of the caching window.")

        # If not already cached, retrieve the full caching window's data.
        if ticker not in self._historical_cache:
            self._historical_cache[ticker] = self.delegate.gather_historical_data(
                ticker, self.cache_start, self.cache_end
            )

        df = self._historical_cache[ticker]
        mask = (df.index >= start) & (df.index <= end)
        return df.loc[mask]

    def get_quote(self, ticker: str, start: Optional[datetime] = None, end: Optional[datetime] = None) -> pd.DataFrame:
        """
        Retrieves market data (quotes) for the specified ticker and datetime range by filtering 
        the cached DataFrame on its index.

        Parameters:
            ticker : The ticker symbol.
            start  : The start datetime for the query.
            end    : The end datetime for the query. If omitted, defaults to the caching window's end.

        Returns:
            pd.DataFrame: Filtered realtime market data.

        Raises:
            ValueError: If the requested datetime range is not fully within the caching window.
        """
        if start is None:
            start = self.clock.now()
        if end is None:
            end = start
            
        # Ensure the requested range is within the caching window
        if start < self.cache_start or end > self.cache_end:
            raise ValueError("Requested data range is out of the caching window.")

        if ticker not in self._quote_cache:
            self._quote_cache[ticker] = self.delegate.get_quote(
                ticker, self.cache_start, self.cache_end
            )

        df = self._quote_cache[ticker]
        mask = (df.index >= start) & (df.index <= end)
        return df.loc[mask]