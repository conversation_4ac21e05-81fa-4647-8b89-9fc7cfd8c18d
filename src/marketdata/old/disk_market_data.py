from abc import ABC, abstractmethod
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from typing import Optional, List, Dict, Tuple, Set, Union
import pyarrow as pa
import pyarrow.parquet as pq
import pyarrow.compute as pc
import pytz
import os
from pathlib import Path
import logging
import json
from dateutil.relativedelta import relativedelta
from marketdata import IMarketData

logger = logging.getLogger(__name__)

class DiskMarketData(IMarketData):
    """
    A market data provider that reads historical data from disk-based
    parquet storage organized by month.
    """
    
    def __init__(self, base_dir: str = "market_data", preload_months: int = 0):
        """
        Initialize the disk market data reader.
        
        Args:
            base_dir: Base directory where market data is stored
            preload_months: Number of most recent months to preload into memory (0 for none)
        """
        self.base_dir = Path(base_dir)
        self.data_dir = self.base_dir / "monthly_data"
        self.metadata_dir = self.base_dir / "metadata"
        self.preload_months = preload_months
        
        # Use the global logger instead of creating a custom one
        self.logger = logger
        
        # Initialize metadata cache
        self._metadata = None
        self._available_symbols = None
        self._date_ranges = None
        
        # Preloaded data cache
        self._preloaded_data = None
        
        # Ensure the directories exist (for reading)
        if not self.data_dir.exists() or not self.metadata_dir.exists():
            self.logger.warning(
                f"Data directories not found. Please ensure data is available at {self.base_dir}"
            )
        
        # Preload data if requested
        if self.preload_months > 0:
            self._preload_recent_data()
    
    def _load_metadata(self) -> None:
        """Load metadata from disk if available."""
        metadata_file = self.metadata_dir / "metadata.json"
        symbols_file = self.metadata_dir / "symbols.json"
        
        if metadata_file.exists() and symbols_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    self._metadata = json.load(f)
                
                with open(symbols_file, 'r') as f:
                    symbols_data = json.load(f)
                    self._available_symbols = set(symbols_data.get('symbols', []))
                    self._date_ranges = symbols_data.get('date_ranges', {})
            except Exception as e:
                self.logger.error(f"Error loading metadata: {str(e)}")
                self._metadata = {}
                self._available_symbols = set()
                self._date_ranges = {}
        else:
            self.logger.warning("Metadata files not found. Limited functionality available.")
            self._metadata = {}
            self._available_symbols = set()
            self._date_ranges = {}
    
    @property
    def available_symbols(self) -> Set[str]:
        """Get set of available ticker symbols."""
        if self._available_symbols is None:
            self._load_metadata()
        return self._available_symbols
    
    def _get_monthly_file_path(self, year: int, month: int) -> Path:
        """Get file path for a specific year-month."""
        year_dir = self.data_dir / str(year)
        return year_dir / f"{month:02d}.parquet"
    
    def _get_date_range_for_ticker(self, ticker: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """Get the available date range for a specific ticker."""
        if self._date_ranges is None:
            self._load_metadata()
        
        if ticker in self._date_ranges:
            start_str = self._date_ranges[ticker]['start']
            end_str = self._date_ranges[ticker]['end']
            
            start = datetime.fromisoformat(start_str) if start_str else None
            end = datetime.fromisoformat(end_str) if end_str else None
            
            return start, end
        
        # If not in metadata, return None
        return None, None
    
    def _get_file_paths_for_date_range(
        self, start_dt: datetime, end_dt: Optional[datetime] = None
    ) -> List[Path]:
        """Get list of file paths that cover the given date range."""
        if end_dt is None:
            end_dt = datetime.now(pytz.timezone('US/Eastern'))
        
        # Ensure datetimes are tz-aware and in EST
        est = pytz.timezone('US/Eastern')
        if start_dt.tzinfo is None:
            start_dt = est.localize(start_dt)
        if end_dt.tzinfo is None:
            end_dt = est.localize(end_dt)
        
        # Convert to EST if in different timezone
        start_dt = start_dt.astimezone(est)
        end_dt = end_dt.astimezone(est)
        
        # Get all year-month combinations in the range
        current_date = date(start_dt.year, start_dt.month, 1)
        end_date = date(end_dt.year, end_dt.month, 1)
        
        file_paths = []
        while current_date <= end_date:
            file_path = self._get_monthly_file_path(current_date.year, current_date.month)
            if file_path.exists():
                file_paths.append(file_path)
            
            # Move to next month
            current_date = (current_date + relativedelta(months=1))
        
        return file_paths
    
    def gather_historical_data(
        self, ticker: str, start_dt: datetime, end_dt: Optional[datetime] = None, interval: int = 86400
    ) -> pd.DataFrame:
        """
        Retrieves historical market data for the given ticker 
        between start_dt and end_dt from disk storage.
        
        Args:
            ticker: The ticker symbol
            start_dt: The start datetime for the data
            end_dt: The end datetime (None for current time)
            interval: Data interval in seconds (default: 86400 for daily data)
                      Note: Currently only daily data (86400) is supported
        
        Returns:
            DataFrame with historical market data
        """
        # Validate ticker
        if ticker not in self.available_symbols:
            return pd.DataFrame()
        
        # Set default end date if not provided
        if end_dt is None:
            end_dt = datetime.now(pytz.timezone('US/Eastern'))
        
        # Validate interval
        if interval != 86400:
            self.logger.warning("Only daily data (interval=86400) is currently supported")
            return pd.DataFrame()
        
        # Get file paths for the date range
        file_paths = self._get_file_paths_for_date_range(start_dt, end_dt)
        
        if not file_paths:
            self.logger.warning(f"No data files found for the specified date range")
            return pd.DataFrame()
        
        # Load and filter data
        dfs = []
        
        for file_path in file_paths:
            try:
                # Use PyArrow for efficient filtering
                table = pq.read_table(file_path)
                
                # Filter by ticker
                symbol_filter = pc.equal(table['symbol'], ticker)
                filtered_table = table.filter(symbol_filter)
                
                if filtered_table.num_rows > 0:
                    df = filtered_table.to_pandas()
                    dfs.append(df)
            except Exception as e:
                self.logger.error(f"Error reading file {file_path}: {str(e)}")
        
        if not dfs:
            return pd.DataFrame()
        
        combined_df = pd.concat(dfs, ignore_index=True)
        # Combine data from all files
        combined_df['date'] = pd.to_datetime(combined_df['date'], utc=True, errors='coerce')

        # Drop rows where date conversion failed
        combined_df = combined_df.dropna(subset=['date'])
        
        if not combined_df.empty:
            combined_df['date'] = combined_df['date'].dt.tz_convert('US/Eastern')
             # Normalize to midnight EST to match writer's intention for daily data
            combined_df['date'] = combined_df['date'].dt.normalize()
        
        # Apply date range filter
        start_dt_naive = start_dt.date()
        end_dt_naive = end_dt.date()
        
        combined_df_dates = combined_df['date'].dt.date
        
        filtered_df = combined_df[
            (combined_df_dates >= start_dt_naive) &
            (combined_df_dates <= end_dt_naive)
        ]
        
        # Sort by date
        sorted_df = filtered_df.sort_values('date')
        
        # Set date as index
        sorted_df = sorted_df.set_index('date')
        
        return sorted_df
    
    def list_available_tickers(self) -> List[str]:
        """Get list of all available tickers."""
        return sorted(list(self.available_symbols))
    
    def get_data_date_range(self, ticker: str = None) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        Get the date range of available data.
        
        Args:
            ticker: Optional ticker to get specific date range
                   If None, returns the overall date range of the dataset
        
        Returns:
            Tuple of (start_date, end_date), may be (None, None) if data not available
        """
        if ticker:
            return self._get_date_range_for_ticker(ticker)
        
        # If no specific ticker, return overall date range from metadata
        if self._metadata is None:
            self._load_metadata()
        
        start_str = self._metadata.get('start_date')
        end_str = self._metadata.get('end_date')
        
        start = datetime.fromisoformat(start_str) if start_str else None
        end = datetime.fromisoformat(end_str) if end_str else None
        
        return start, end
    
    def _preload_recent_data(self) -> None:
        """Preload the most recent months of data into memory."""
        self.logger.info(f"Preloading {self.preload_months} months of market data...")
        
        # Get current date in EST timezone
        est = pytz.timezone('US/Eastern')
        current_date = datetime.now(est).date()
        
        # Calculate the start date for preloading
        start_date = date(current_date.year, current_date.month, 1) - relativedelta(months=self.preload_months-1)
        
        # Get file paths for the months to preload
        file_paths = []
        temp_date = start_date
        while temp_date <= current_date:
            file_path = self._get_monthly_file_path(temp_date.year, temp_date.month)
            if file_path.exists():
                file_paths.append(file_path)
            temp_date = temp_date + relativedelta(months=1)
        
        # Load all files into a single DataFrame
        dfs = []
        for file_path in file_paths:
            try:
                df = pd.read_parquet(file_path)
                dfs.append(df)
                self.logger.info(f"Preloaded {file_path.name}")
            except Exception as e:
                self.logger.error(f"Error preloading {file_path}: {str(e)}")
        
        if dfs:
            self._preloaded_data = pd.concat(dfs, ignore_index=True)
            self.logger.info(f"Successfully preloaded {len(self._preloaded_data)} records from {len(dfs)} files")
        else:
            self.logger.warning("No data was preloaded")
            self._preloaded_data = pd.DataFrame()
    
    def get_preloaded_data(self) -> pd.DataFrame:
        """
        Returns the preloaded market data.
        
        Returns:
            DataFrame containing all preloaded market data, or empty DataFrame if no data was preloaded
        """
        if self._preloaded_data is None:
            self.logger.warning("No data has been preloaded. Use preload_months parameter during initialization.")
            return pd.DataFrame()
        
        return self._preloaded_data.copy()

# Add CLI functionality
def parse_arguments():
    """Parse command line arguments for the CLI."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Market Data Explorer CLI")
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Command to execute')
    
    # Interactive command
    interactive_parser = subparsers.add_parser('interactive', help='Start interactive session with data')
    interactive_parser.add_argument('--ticker', '-t', type=str, required=True,
                                   help='Ticker symbol to load data for')
    interactive_parser.add_argument('--start-date', '-s', type=str, default=None,
                                   help='Start date (YYYY-MM-DD)')
    interactive_parser.add_argument('--end-date', '-e', type=str, default=None,
                                   help='End date (YYYY-MM-DD)')
    interactive_parser.add_argument('--base-dir', '-d', type=str, default="market_data",
                                   help='Base directory for market data')
    
    # Preloaded interactive command
    preloaded_parser = subparsers.add_parser('preloaded', help='Start interactive session with preloaded data')
    preloaded_parser.add_argument('--months', '-m', type=int, default=3,
                                 help='Number of months to preload (default: 3)')
    preloaded_parser.add_argument('--base-dir', '-d', type=str, default="market_data",
                                 help='Base directory for market data')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List available tickers')
    list_parser.add_argument('--base-dir', '-d', type=str, default="market_data",
                            help='Base directory for market data')
    
    # Info command
    info_parser = subparsers.add_parser('info', help='Show information about available data')
    info_parser.add_argument('--ticker', '-t', type=str, default=None,
                            help='Ticker symbol to show info for (optional)')
    info_parser.add_argument('--base-dir', '-d', type=str, default="market_data",
                            help='Base directory for market data')
    
    return parser.parse_args()

def main():
    """Main function for the CLI."""
    args = parse_arguments()
    
    # Initialize market data reader with preloading for the preloaded command
    if args.command == 'preloaded':
        reader = DiskMarketData(
            base_dir=args.base_dir if hasattr(args, 'base_dir') else "market_data",
            preload_months=args.months
        )
    else:
        reader = DiskMarketData(
            base_dir=args.base_dir if hasattr(args, 'base_dir') else "market_data"
        )
    
    if args.command == 'interactive':
        # Parse dates if provided
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d") if args.start_date else None
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d") if args.end_date else None
        
        # If start_date not provided, use earliest available for the ticker
        if start_date is None:
            ticker_start, _ = reader.get_data_date_range(args.ticker)
            if ticker_start:
                start_date = ticker_start
            else:
                # Default to 1 year ago if no data range info
                start_date = datetime.now() - timedelta(days=365)
        
        # Load data
        print(f"Loading data for {args.ticker}...")
        df = reader.gather_historical_data(args.ticker, start_date, end_date)
        
        if df.empty:
            print(f"No data found for {args.ticker} in the specified date range.")
            return 1
        
        print(f"Loaded {len(df)} records for {args.ticker}")
        print(f"Date range: {df.index.min()} to {df.index.max()}")
        print("\nSample data:")
        print(df.head())
        
        # Drop into IPython shell
        print("\nDropping into IPython shell. The data is available as 'df'.")
        print("Variables available:")
        print("  - df: DataFrame with the loaded data")
        print("  - reader: DiskMarketData instance")
        print("  - ticker: Current ticker symbol")
        
        # Set up variables for the interactive session
        ticker = args.ticker
        
        try:
            from IPython import embed
            embed()
        except ImportError:
            print("IPython is not installed. Please install it with 'pip install ipython'")
            return 1
    
    elif args.command == 'preloaded':
        # Get the preloaded data
        df = reader.get_preloaded_data()
        
        if df.empty:
            print("No data was preloaded or preloading failed.")
            return 1
        
        # Show summary of preloaded data
        print(f"Preloaded {len(df)} records covering {args.months} months")
        
        # Get unique symbols in the preloaded data
        symbols = df['symbol'].unique()
        print(f"Data contains {len(symbols)} unique symbols")
        print(f"Sample symbols: {', '.join(symbols[:5])}")
        if len(symbols) > 5:
            print(f"... and {len(symbols) - 5} more")
        
        # Show date range
        if 'date' in df.columns:
            min_date = df['date'].min()
            max_date = df['date'].max()
            print(f"Date range: {min_date} to {max_date}")
        
        # Drop into IPython shell
        print("\nDropping into IPython shell. The preloaded data is available as 'df'.")
        print("Variables available:")
        print("  - df: DataFrame with all preloaded data")
        print("  - reader: DiskMarketData instance")
        print("\nTip: To filter data for a specific symbol, use:")
        print("  symbol_data = df[df['symbol'] == 'AAPL']")
        
        try:
            from IPython import embed
            embed()
        except ImportError:
            print("IPython is not installed. Please install it with 'pip install ipython'")
            return 1
    
    elif args.command == 'list':
        # List available tickers
        tickers = reader.list_available_tickers()
        
        if not tickers:
            print("No tickers found in the data store.")
            return 1
        
        print(f"Available tickers ({len(tickers)}):")
        for ticker in tickers:
            print(f"  {ticker}")
        
    elif args.command == 'info':
        # Show information about available data
        if args.ticker:
            # Show info for specific ticker
            if args.ticker not in reader.available_symbols:
                print(f"Ticker {args.ticker} not found in available symbols")
                return 1
            
            start_date, end_date = reader.get_data_date_range(args.ticker)
            
            print(f"Information for {args.ticker}:")
            print(f"  Date range: {start_date} to {end_date}")
            
            # Try to get a sample to show record count
            sample_df = reader.gather_historical_data(args.ticker, start_date, end_date)
            print(f"  Total records: {len(sample_df)}")
            
        else:
            # Show overall info
            start_date, end_date = reader.get_data_date_range()
            tickers = reader.list_available_tickers()
            
            print("Market Data Store Information:")
            print(f"  Total tickers: {len(tickers)}")
            print(f"  Date range: {start_date} to {end_date}")
            
            # Show some example tickers
            if tickers:
                print(f"  Sample tickers: {', '.join(tickers[:5])}")
                if len(tickers) > 5:
                    print(f"  ... and {len(tickers) - 5} more")
    
    else:
        print("No command specified. Use --help to see available commands.")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())