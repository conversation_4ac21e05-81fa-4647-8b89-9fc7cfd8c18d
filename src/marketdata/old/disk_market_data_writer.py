import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from typing import Optional, List, Dict, Tuple, Set, Union, Any
import pyarrow as pa
import pyarrow.parquet as pq
import pyarrow.compute as pc
import pytz
import os
from pathlib import Path
import logging
import json
import argparse
import sys
from dateutil.relativedelta import relativedelta
import concurrent.futures
from tqdm import tqdm


class DiskMarketDataWriter:
    """
    Writer module for saving market data to disk in a monthly parquet format.
    """
    
    def __init__(self, base_dir: str = "market_data"):
        """
        Initialize the market data writer.
        
        Args:
            base_dir: Base directory where market data will be stored
        """
        self.base_dir = Path(base_dir)
        self.data_dir = self.base_dir / "monthly_data"
        self.metadata_dir = self.base_dir / "metadata"
        
        # Create directories if they don't exist
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.metadata_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.logger = self._setup_logger()
        
        # Initialize metadata
        self._metadata = self._load_metadata()
        self._symbols_data = self._load_symbols_data()
    
    def _setup_logger(self):
        """Set up the logger for the DiskMarketDataWriter class."""
        logger = logging.getLogger("DiskMarketDataWriter")
        logger.setLevel(logging.INFO)
        
        # Add file handler if not already added
        if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
            log_dir = self.base_dir / "logs"
            log_dir.mkdir(exist_ok=True)
            
            log_file = log_dir / "market_data_writer.log"
            file_handler = logging.FileHandler(log_file)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        # Add stream handler if not already added
        if not any(isinstance(h, logging.StreamHandler) for h in logger.handlers):
            stream_handler = logging.StreamHandler()
            formatter = logging.Formatter('%(levelname)s - %(message)s')
            stream_handler.setFormatter(formatter)
            logger.addHandler(stream_handler)
        
        return logger
    
    def _load_metadata(self) -> Dict[str, Any]:
        """Load metadata from disk if available, otherwise initialize it."""
        metadata_file = self.metadata_dir / "metadata.json"
        
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Error loading metadata: {str(e)}")
                return self._initialize_metadata()
        else:
            return self._initialize_metadata()
    
    def _initialize_metadata(self) -> Dict[str, Any]:
        """Initialize a new metadata dictionary."""
        return {
            'last_updated': datetime.now().isoformat(),
            'total_tickers': 0,
            'total_records': 0,
            'start_date': None,
            'end_date': None,
            'version': '1.0.0'
        }
    
    def _load_symbols_data(self) -> Dict[str, Any]:
        """Load symbols data from disk if available, otherwise initialize it."""
        symbols_file = self.metadata_dir / "symbols.json"
        
        if symbols_file.exists():
            try:
                with open(symbols_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Error loading symbols data: {str(e)}")
                return self._initialize_symbols_data()
        else:
            return self._initialize_symbols_data()
    
    def _initialize_symbols_data(self) -> Dict[str, Any]:
        """Initialize a new symbols data dictionary."""
        return {
            'symbols': [],
            'date_ranges': {}
        }
    
    def _save_metadata(self):
        """Save current metadata to disk."""
        metadata_file = self.metadata_dir / "metadata.json"
        
        # Update last_updated timestamp
        self._metadata['last_updated'] = datetime.now().isoformat()
        
        try:
            with open(metadata_file, 'w') as f:
                json.dump(self._metadata, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving metadata: {str(e)}")
    
    def _save_symbols_data(self):
        """Save current symbols data to disk."""
        symbols_file = self.metadata_dir / "symbols.json"
        
        try:
            with open(symbols_file, 'w') as f:
                json.dump(self._symbols_data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving symbols data: {str(e)}")
    
    def _get_monthly_file_path(self, year: int, month: int) -> Path:
        """Get file path for a specific year-month."""
        year_dir = self.data_dir / str(year)
        year_dir.mkdir(exist_ok=True)
        return year_dir / f"{month:02d}.parquet"
    
    def _update_symbol_metadata(self, symbol: str, data: pd.DataFrame):
        """
        Update metadata for a specific symbol with new data.
        
        Args:
            symbol: Ticker symbol
            data: DataFrame containing the symbol's data
        """
        # Ensure date column is datetime
        if not pd.api.types.is_datetime64_any_dtype(data['date']):
            date_col = pd.to_datetime(data['date'])
        else:
            date_col = data['date']
        
        # Get min and max dates for this data
        min_date = date_col.min()
        max_date = date_col.max()
        
        # Ensure timezone consistency (EST)
        eastern_tz = pytz.timezone('US/Eastern')
        
        # Convert min_date and max_date to timezone-aware if they're naive
        if min_date.tzinfo is None:
            min_date = eastern_tz.localize(min_date)
        else:
            min_date = min_date.astimezone(eastern_tz)
        
        if max_date.tzinfo is None:
            max_date = eastern_tz.localize(max_date)
        else:
            max_date = max_date.astimezone(eastern_tz)
        
        # Add symbol to list if not already present
        if symbol not in self._symbols_data['symbols']:
            self._symbols_data['symbols'].append(symbol)
        
        # Update date range for symbol
        if symbol not in self._symbols_data['date_ranges']:
            self._symbols_data['date_ranges'][symbol] = {
                'start': min_date.isoformat(),
                'end': max_date.isoformat()
            }
        else:
            current_start = datetime.fromisoformat(self._symbols_data['date_ranges'][symbol]['start'])
            current_end = datetime.fromisoformat(self._symbols_data['date_ranges'][symbol]['end'])
            
            # Ensure current_start and current_end are timezone-aware
            if current_start.tzinfo is None:
                current_start = eastern_tz.localize(current_start)
            else:
                current_start = current_start.astimezone(eastern_tz)
            
            if current_end.tzinfo is None:
                current_end = eastern_tz.localize(current_end)
            else:
                current_end = current_end.astimezone(eastern_tz)
            
            # Update if new data extends the range
            if min_date < current_start:
                self._symbols_data['date_ranges'][symbol]['start'] = min_date.isoformat()
            
            if max_date > current_end:
                self._symbols_data['date_ranges'][symbol]['end'] = max_date.isoformat()
        
        # Update overall metadata date range
        if self._metadata['start_date'] is None or min_date < datetime.fromisoformat(self._metadata['start_date']):
            self._metadata['start_date'] = min_date.isoformat()
        
        if self._metadata['end_date'] is None or max_date > datetime.fromisoformat(self._metadata['end_date']):
            self._metadata['end_date'] = max_date.isoformat()
        
        # Update ticker count
        self._metadata['total_tickers'] = len(self._symbols_data['symbols'])
    
    def add_data(self, data: pd.DataFrame, symbol: str):
        """
        Add data for a single ticker symbol to the storage.
        
        Args:
            data: DataFrame containing the market data
            symbol: Ticker symbol for the data
        """
        if data.empty:
            self.logger.warning(f"Empty data provided for {symbol}, skipping")
            return
        
        # Ensure we have proper columns
        required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in data.columns:
                self.logger.error(f"Required column '{col}' not found in data for {symbol}")
                return
        
        # Make a copy to avoid modifying the original
        data = data.copy()
        
        # Reset index if date is the index
        if isinstance(data.index, pd.DatetimeIndex) and data.index.name in ['date', None]:
            data = data.reset_index()
            if 'index' in data.columns and 'date' not in data.columns:
                data = data.rename(columns={'index': 'date'})
        
        # Ensure date column is datetime
        if not pd.api.types.is_datetime64_any_dtype(data['date']):
            data['date'] = pd.to_datetime(data['date'])
        
        # Standardize dates to EST timezone and truncate to date only (no time component)
        eastern_tz = pytz.timezone('US/Eastern')
        
        # Convert to timezone-aware if naive, then extract date only
        if data['date'].dt.tz is None:
            data['date'] = data['date'].dt.tz_localize(eastern_tz)
        else:
            data['date'] = data['date'].dt.tz_convert(eastern_tz)
        
        # Truncate to date only (remove time component)
        data['date'] = data['date'].dt.normalize()
        
        # Remove duplicates based on date
        duplicates = data.duplicated(subset=['date'], keep='first')
        if duplicates.any():
            dup_count = duplicates.sum()
            self.logger.warning(f"Removed {dup_count} duplicate date entries for {symbol}")
            data = data.drop_duplicates(subset=['date'], keep='first')
        
        # Add symbol column
        data['symbol'] = symbol
        
        # Group by year-month
        data['year'] = data['date'].dt.year
        data['month'] = data['date'].dt.month
        
        # Update symbol metadata
        self._update_symbol_metadata(symbol, data)
        
        # Process each year-month group
        for (year, month), group in data.groupby(['year', 'month']):
            monthly_file = self._get_monthly_file_path(year, month)
            
            # Remove grouping columns
            group = group.drop(columns=['year', 'month'])
            
            if monthly_file.exists():
                # File exists, need to update
                try:
                    # Read existing data
                    existing_table = pq.read_table(monthly_file)
                    existing_df = existing_table.to_pandas()
                    
                    # Convert date to string for merging
                    existing_df['date_str'] = existing_df['date'].dt.strftime('%Y-%m-%d')
                    group['date_str'] = group['date'].dt.strftime('%Y-%m-%d')
                    
                    # Create key for identifying rows
                    existing_df['key'] = existing_df['symbol'] + '_' + existing_df['date_str']
                    group['key'] = group['symbol'] + '_' + group['date_str']
                    
                    # Find rows to update
                    update_keys = set(group['key'])
                    existing_df = existing_df[~existing_df['key'].isin(update_keys)]
                    
                    # Combine data
                    combined = pd.concat([existing_df, group], ignore_index=True)
                    
                    # Clean up temporary columns
                    combined = combined.drop(columns=['date_str', 'key'])
                    
                    # Save updated file
                    pq.write_table(pa.Table.from_pandas(combined), monthly_file)
                    
                    # Update record count
                    self._metadata['total_records'] += len(group)
                    
                except Exception as e:
                    self.logger.error(f"Error updating file {monthly_file}: {str(e)}")
            else:
                # New file
                try:
                    # Remove any temporary columns if they exist
                    if 'date_str' in group.columns:
                        group = group.drop(columns=['date_str'])
                    if 'key' in group.columns:
                        group = group.drop(columns=['key'])
                    
                    # Save as new file
                    pq.write_table(pa.Table.from_pandas(group), monthly_file)
                    
                    # Update record count
                    self._metadata['total_records'] += len(group)
                    
                except Exception as e:
                    self.logger.error(f"Error creating file {monthly_file}: {str(e)}")
        
        # Save updated metadata
        self._save_metadata()
        self._save_symbols_data()
        
        self.logger.info(f"Added {len(data)} records for {symbol}")
    
    def add_data_batch(self, data_dict: Dict[str, pd.DataFrame]):
        """
        Add data for multiple ticker symbols to the storage.
        
        Args:
            data_dict: Dictionary mapping ticker symbols to their data DataFrames
        """
        if not data_dict:
            self.logger.warning("Empty data dictionary provided, nothing to add")
            return
        
        self.logger.info(f"Adding data for {len(data_dict)} symbols sequentially")
        
        # Process sequentially with progress bar
        for symbol, data in tqdm(data_dict.items(), desc="Adding data"):
            self.add_data(data, symbol)
        
        self.logger.info(f"Completed adding data for {len(data_dict)} symbols")


def parse_arguments():
    """Parse command line arguments for the CLI."""
    parser = argparse.ArgumentParser(description="Market Data Storage CLI")
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Command to execute')
    
    # Add command
    add_parser = subparsers.add_parser('add', help='Add data to storage')
    add_parser.add_argument('--file', '-f', type=str, required=True, 
                           help='CSV or parquet file containing market data')
    add_parser.add_argument('--symbol', '-s', type=str, required=True,
                           help='Ticker symbol for the data')
    add_parser.add_argument('--date-format', type=str, default='%Y-%m-%d',
                           help='Format string for date column (default: %%Y-%%m-%%d)')
    
    # Batch add command
    batch_parser = subparsers.add_parser('batch-add', help='Add data from multiple files')
    batch_parser.add_argument('--dir', '-d', type=str, required=True,
                             help='Directory containing CSV or parquet files')
    batch_parser.add_argument('--pattern', '-p', type=str, default='*.csv',
                             help='File pattern to match (default: *.csv)')
    batch_parser.add_argument('--symbol-from-filename', action='store_true',
                             help='Extract symbol from filename')
    
    # Info command
    info_parser = subparsers.add_parser('info', help='Show information about the storage')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List available symbols')
    list_parser.add_argument('--output', '-o', type=str,
                            help='Output file to save the list (default: print to console)')
    
    return parser.parse_args()


def main():
    """Main function for the CLI."""
    args = parse_arguments()
    
    # Initialize writer
    writer = DiskMarketDataWriter()
    
    if args.command == 'add':
        # Add data from a single file
        file_path = Path(args.file)
        
        if not file_path.exists():
            print(f"Error: File {file_path} not found")
            return 1
        
        # Load data from file
        try:
            if file_path.suffix.lower() == '.csv':
                data = pd.read_csv(file_path, parse_dates=['date'], date_format=args.date_format)
            elif file_path.suffix.lower() == '.parquet':
                data = pd.read_parquet(file_path)
            else:
                print(f"Error: Unsupported file format {file_path.suffix}")
                return 1
            
            # Add data to storage
            writer.add_data(data, args.symbol)
            print(f"Successfully added data for {args.symbol} from {file_path}")
            
        except Exception as e:
            print(f"Error adding data: {str(e)}")
            return 1
    
    elif args.command == 'batch-add':
        # Add data from multiple files
        dir_path = Path(args.dir)
        
        if not dir_path.exists() or not dir_path.is_dir():
            print(f"Error: Directory {dir_path} not found")
            return 1
        
        # Find matching files
        files = list(dir_path.glob(args.pattern))
        
        if not files:
            print(f"No files matching pattern {args.pattern} found in {dir_path}")
            return 1
        
        print(f"Found {len(files)} files to process")
        
        # Process each file
        data_dict = {}
        
        for file_path in tqdm(files, desc="Loading files"):
            try:
                # Determine symbol
                if args.symbol_from_filename:
                    # Extract symbol from filename (assumes filename format like "AAPL.csv")
                    symbol = file_path.stem.split('_')[0].upper()
                else:
                    # Try to extract from first row of data
                    if file_path.suffix.lower() == '.csv':
                        df_temp = pd.read_csv(file_path, nrows=1)
                        if 'symbol' in df_temp.columns:
                            symbol = df_temp['symbol'].iloc[0]
                        else:
                            print(f"Skipping {file_path}: Cannot determine symbol")
                            continue
                    else:
                        print(f"Skipping {file_path}: Cannot determine symbol for non-CSV files")
                        continue
                
                # Load data
                if file_path.suffix.lower() == '.csv':
                    data = pd.read_csv(file_path, parse_dates=['date'])
                elif file_path.suffix.lower() == '.parquet':
                    data = pd.read_parquet(file_path)
                else:
                    print(f"Skipping {file_path}: Unsupported file format")
                    continue
                
                # Add to dictionary
                data_dict[symbol] = data
                
            except Exception as e:
                print(f"Error processing {file_path}: {str(e)}")
        
        # Add batch data to storage
        if data_dict:
            writer.add_data_batch(data_dict)
            print(f"Successfully added data for {len(data_dict)} symbols")
        else:
            print("No valid data found to add")
    
    elif args.command == 'info':
        # Show storage information
        metadata = writer._metadata
        symbols_data = writer._symbols_data
        
        print("Market Data Storage Information:")
        print(f"Total tickers: {metadata['total_tickers']}")
        print(f"Total records: {metadata['total_records']}")
        
        if metadata['start_date'] and metadata['end_date']:
            print(f"Date range: {metadata['start_date']} to {metadata['end_date']}")
        
        print(f"Last updated: {metadata['last_updated']}")
        
        # Show size information
        total_size = 0
        file_count = 0
        
        for year_dir in writer.data_dir.iterdir():
            if year_dir.is_dir():
                for file in year_dir.glob("*.parquet"):
                    total_size += file.stat().st_size
                    file_count += 1
        
        print(f"Storage size: {total_size / (1024*1024):.2f} MB")
        print(f"Number of files: {file_count}")
    
    elif args.command == 'list':
        # List available symbols
        symbols = sorted(writer._symbols_data['symbols'])
        
        if args.output:
            # Save to file
            with open(args.output, 'w') as f:
                for symbol in symbols:
                    f.write(f"{symbol}\n")
            print(f"Saved {len(symbols)} symbols to {args.output}")
        else:
            # Print to console
            print("Available symbols:")
            for symbol in symbols:
                print(symbol)
            print(f"Total: {len(symbols)} symbols")
    
    else:
        # No command or invalid command
        print("Error: No command specified or invalid command")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())