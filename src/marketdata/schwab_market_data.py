from __future__ import annotations

import asyncio
import datetime as dt
from abc import ABC, abstractmethod
from datetime import timed<PERSON><PERSON>
from typing import Async<PERSON>enerator, <PERSON><PERSON>, Dict, Any, Optional

import pandas as pd
from schwab.client import Client
from schwab.streaming import StreamClient

from marketdata.istreamingmarketdata import IStreamingMarketData


class SchwabStreamingMarketData(IStreamingMarketData):
    """Streaming market‑data adapter built on *schwab‑py* using a shared client.

    Parameters
    ----------
    http_client
        A **pre‑logged‑in** :class:`schwab.client.Client`. Share this instance
        across your app (orders, quotes, etc.) to avoid multiple tokens.
    account_id
        *Optional.* Raw numeric account ID used by the streaming API. If
        omitted, the first linked account returned by
        ``Client.get_account_numbers()`` is used automatically.
    """

    # ------------------------------------------------------------------
    # Construction
    # ------------------------------------------------------------------

    def __init__(self, http_client: Client, *, account_id: Optional[int] = None) -> None:
        self._http_client: Client = http_client
        self._explicit_account_id: Optional[int] = account_id  # user‑supplied
        self._resolved_account_id: Optional[int] = None        # lazy‑loaded

        self._stream_client: StreamClient | None = None
        self._login_lock = asyncio.Lock()

    # ------------------------------------------------------------------
    # Helpers
    # ------------------------------------------------------------------

    async def _ensure_account_id(self) -> int:
        """Return a numeric account ID, resolving it once if needed."""
        if self._explicit_account_id is not None:
            return self._explicit_account_id
        if self._resolved_account_id is not None:
            return self._resolved_account_id

        resp = await asyncio.to_thread(self._http_client.get_account_numbers)
        resp.raise_for_status()
        accounts = resp.json()
        if not accounts:
            raise RuntimeError("No linked accounts returned by get_account_numbers().")
        self._resolved_account_id = int(accounts[0]["accountNumber"])
        return self._resolved_account_id

    # ------------------------------------------------------------------
    # Life‑cycle
    # ------------------------------------------------------------------

    async def connect(self) -> None:
        """Initialise the WebSocket client and perform stream login once."""
        async with self._login_lock:
            if self._stream_client is not None:
                return  # already connected

            account_id = await self._ensure_account_id()
            self._stream_client = StreamClient(self._http_client, account_id=account_id)
            await self._stream_client.login()

    async def close(self) -> None:
        if self._stream_client is not None:
            await self._stream_client.logout()
        self._stream_client = None

    # ------------------------------------------------------------------
    # Core API
    # ------------------------------------------------------------------

    async def subscribe_to_bars(
        self,
        ticker: str,
        interval: timedelta,
        start_time: dt.datetime,  # currently unused – retained for interface compatibility
        lookback_window: timedelta,
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """Yield *(historical_df, latest_df)* on every new bar."""

        if interval != timedelta(minutes=1):
            raise ValueError("Schwab streaming only supports 1‑minute bars.")

        if self._stream_client is None:
            await self.connect()

        # --------------------------------------------------------------
        # Historical back‑fill (up to ~48 days)
        # --------------------------------------------------------------
        end_dt = dt.datetime.now(dt.UTC)
        start_dt = end_dt - lookback_window

        try:
            resp = await asyncio.to_thread(
                self._http_client.get_price_history_every_minute,
                ticker,
                start_datetime=start_dt,
                end_datetime=end_dt,
                need_extended_hours_data=True,
                need_previous_close=False,
            )
            resp.raise_for_status()
            candles: list[dict[str, Any]] = resp.json().get("candles", [])
        except Exception:
            candles = []

        def _to_row(c: Dict[str, Any]) -> Dict[str, Any]:
            ts = pd.to_datetime(c["datetime"], unit="ms", utc=True).tz_convert("America/New_York")
            return {
                "timestamp": ts,
                "open": c["open"],
                "high": c["high"],
                "low": c["low"],
                "close": c["close"],
                "volume": c["volume"],
            }

        if not candles:
            hist_df = pd.DataFrame(columns=["open", "high", "low", "close", "volume"])
            hist_df.index.name = "timestamp"
        else:
            hist_df = (
                pd.DataFrame.from_records(map(_to_row, candles))
                .set_index("timestamp")
                .sort_index()
            )

        latest_df = hist_df.tail(1).copy()
        yield hist_df.copy(), latest_df

        # --------------------------------------------------------------
        # Live stream over WebSocket
        # --------------------------------------------------------------
        queue: asyncio.Queue[pd.DataFrame] = asyncio.Queue()

        def _handler(msg: Dict[str, Any]) -> None:
            for item in msg.get("content", []):
                try:
                    ts = pd.to_datetime(item["CHART_TIME_MILLIS"], unit="ms", utc=True).tz_convert("America/New_York")
                    df = pd.DataFrame({
                        "timestamp": [ts],
                        "open": [item["OPEN_PRICE"]],
                        "high": [item["HIGH_PRICE"]],
                        "low": [item["LOW_PRICE"]],
                        "close": [item["CLOSE_PRICE"]],
                        "volume": [item["VOLUME"]],
                    }).set_index("timestamp")
                    queue.put_nowait(df)
                except KeyError:
                    continue  # skip malformed packets

        # Register handler, then subscribe
        if ticker.startswith("/"):
            self._stream_client.add_chart_futures_handler(_handler)
            await self._stream_client.chart_futures_subs([ticker])
        else:
            self._stream_client.add_chart_equity_handler(_handler)
            await self._stream_client.chart_equity_subs([ticker])

        # Background WebSocket pump
        async def _pump() -> None:
            while True:
                await self._stream_client.handle_message()

        asyncio.create_task(_pump())

        # Stream new bars indefinitely
        while True:
            new_bar = await queue.get()
            # exclude empty DataFrames before concatenation to avoid FutureWarning
            if hist_df.empty:
                hist_df = new_bar.copy()
            else:
                hist_df = pd.concat([hist_df, new_bar]).sort_index()
            yield hist_df.copy(), new_bar.copy()

    # ------------------------------------------------------------------
    # Async context
    # ------------------------------------------------------------------

    async def __aenter__(self) -> "SchwabStreamingMarketData":
        await self.connect()
        return self

    async def __aexit__(self, *_exc) -> None:
        await self.close()
