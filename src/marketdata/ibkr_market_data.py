import os
import logging
import pandas as pd
from datetime import date, datetime, time, timedelta
from typing import Optional
import pytz
import asyncio
from ib_async import IB, Stock, Contract, util
import nest_asyncio  
nest_asyncio.apply()

from .imarketdata import IMarketData

class IBKRMarketData(IMarketData):
    """
    IBKR based market data that retrieves historical data for tickers using IBKR's API.
    This class incorporates a caching mechanism in a designated cache directory to store and retrieve historical data.
    """
    def __init__(self, ib: IB):
        """
        Initializes the IBKRMarketData.
        
        :param ib: An instance of the IB client.
        """
        self.ib = ib
    
    async def _create_cont_future(self, symbol: str):
        """
        Quickly build a continuous future (CONTFUT) contract similar to IBKRBroker.
        """
        c = Contract()
        c.symbol   = symbol
        c.secType  = "CONTFUT"
        c.exchange = "CME"
        c.currency = "USD"
        qualified, = await self.ib.qualifyContractsAsync(c)
        return qualified

    async def _gather_historical_data(self, ticker: str, start_date: date, end_date: Optional[date] = None, interval: int = 60) -> pd.DataFrame:
        """
        Asynchronously retrieves historical market data for the specified ticker and date range.
        Internal helper to be run synchronously via a wrapper.
        """
        eastern = pytz.timezone("US/Eastern")
        if end_date is None:
            effective_end_date = datetime.now(eastern).date()
            duration_days = (effective_end_date - start_date).days + 1
            if duration_days < 1:
                duration_days = 1
            durationStr = f"{duration_days} D"
            end_dt_str = ""  # use current time
        else:
            effective_end_date = end_date
            end_dt = eastern.localize(datetime.combine(effective_end_date, time(16, 0)))
            end_dt_str = end_dt.strftime("%Y%m%d %H:%M:%S")
            duration_days = (effective_end_date - start_date).days + 1
            durationStr = f"{duration_days} D"

        # map interval (seconds) → IB barSizeSetting
        if interval % 86400 == 0:
            bar_size_setting = f"{interval // 86400} day"
        elif interval % 3600 == 0:
            bar_size_setting = f"{interval // 3600} hour"
        elif interval % 60 == 0:
            bar_size_setting = f"{interval // 60} min"
        else:
            raise ValueError(f"Interval {interval} not supported; must be multiple of 60s.")

        # support slash-prefixed continuous futures
        if ticker.startswith("/"):
            contract = await self._create_cont_future(ticker)
        else:
            contract = Stock(ticker, "SMART", "USD")

        try:
            details = await self.ib.reqContractDetailsAsync(contract)
            if not details:
                logging.error(f"No contract details found for {ticker}")
                return pd.DataFrame()
        except Exception as e:
            logging.error(f"Error fetching contract details for {ticker}: {e}")
            return pd.DataFrame()
        
        try:
            bars = await self.ib.reqHistoricalDataAsync(
                contract,
                endDateTime=end_dt_str,
                durationStr=durationStr,
                barSizeSetting=bar_size_setting,
                whatToShow="TRADES",
                useRTH=False,
                formatDate=2
            )
            
            if not bars:
                logging.warning(f"No historical data returned for {ticker}")
                return pd.DataFrame()
            
            df = util.df(bars)
            df['date'] = pd.to_datetime(df['date'])
            if df['date'].dt.tz is None:
                df['date'] = df['date'].dt.tz_localize(eastern)
            else:
                df['date'] = df['date'].dt.tz_convert(eastern)
            df.set_index('date', inplace=True)
            
            return df

        except Exception as e:
            logging.error(f"Error fetching historical data for {ticker}: {e}")
            return pd.DataFrame()

    def gather_historical_data(self, ticker: str, start_date: date, end_date: Optional[date] = None, interval: int = 60) -> pd.DataFrame:
        """
        Synchronously retrieves historical market data by wrapping the asynchronous method.
        """
        return asyncio.run(self._gather_historical_data(ticker, start_date, end_date, interval))