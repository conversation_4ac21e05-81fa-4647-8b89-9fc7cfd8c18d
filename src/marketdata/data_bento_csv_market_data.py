import os
import glob
import pandas as pd
from typing import Optional, Union


class DataBentoCSVMarketData:
    """
    A helper class to load (and keep in memory) a limited date‐range of minute‐level futures data from CSVs.

    – Each CSV is assumed to have at least these columns:
         • 'ts_event'   : a UTC‐timestamp column (ISO format, with “+00:00”)
         • 'open', 'high', 'low', 'close', 'volume'
      plus other columns we will drop.

    – Filenames must follow: "{ticker_lower}*.csv", e.g. "nq_data_2010_06_07_to_2025_05_01.csv" or
      "cme_zn_continuous_1min_data_2010_06_06_to_2025_05_30.csv", etc.

    – In __init__, you can specify `memory_start` and `memory_end` (strings or tz‐aware Timestamps). After loading
      a CSV, we immediately drop everything outside [memory_start, memory_end], to reduce RAM usage.

    Usage:
        from databento_csv_market_data import DataBentoCSVMarketData

        # Keep only data between 2020‐01‐01 and 2022‐12‐31 (Eastern) in memory
        db = DataBentoCSVMarketData(
            base_dir="market_data/static",
            memory_start="2020-01-01 00:00:00-05:00",
            memory_end="2022-12-31 23:59:00-05:00"
        )

        # Later…
        df_nq = db.get_per_minute_data(
            ticker="NQ",
            start_dt="2020-06-01 09:30:00-04:00",
            end_dt="2020-06-01 16:00:00-04:00"
        )
        print(df_nq.head())
    """

    def __init__(
        self,
        base_dir: str = "market_data/static",
        memory_start: Optional[Union[str, pd.Timestamp]] = None,
        memory_end: Optional[Union[str, pd.Timestamp]] = None,
        tz_from: str = "UTC",
        tz_to: str = "US/Eastern",
        ts_column: str = "ts_event",
        ohlcv_cols: Optional[list] = None,
    ):
        """
        Parameters
        ----------
        base_dir : str
            Path to the directory where all the CSVs are stored.
            e.g. "market_data/static"

        memory_start / memory_end : str or pandas.Timestamp, optional
            If provided, we will keep *only* rows between [memory_start, memory_end]
            (both inclusive) after we load the CSV into memory. Must be in the same
            timezone as `tz_to`, or include an explicit tz offset.

        tz_from : str, default "UTC"
            Timezone label of the raw 'ts_event' column in the CSV.

        tz_to : str, default "US/Eastern"
            Timezone to which we convert the UTC timestamps.

        ts_column : str, default "ts_event"
            Name of the timestamp column in each CSV (must be parseable by pandas).

        ohlcv_cols : list of str, optional
            List of the OHLCV column names you want to keep. By default:
                ['open', 'high', 'low', 'close', 'volume']
            Any other columns in the CSV will be dropped.
        """
        self.base_dir = base_dir
        self.tz_from = tz_from
        self.tz_to = tz_to
        self.ts_column = ts_column
        self.ohlcv_cols = ohlcv_cols or ["open", "high", "low", "close", "volume"]

        # Normalize memory_start / memory_end into tz‐aware pd.Timestamp (in tz_to)
        def _normalize_bound(b):
            if b is None:
                return None
            b_ts = pd.to_datetime(b)
            if b_ts.tzinfo is None:
                # assume tz_to if no tzinfo given
                return b_ts.tz_localize(self.tz_to)
            else:
                # if user passed a tz‐aware timestamp, convert to tz_to
                return b_ts.tz_convert(self.tz_to)

        self.memory_start = _normalize_bound(memory_start)
        self.memory_end = _normalize_bound(memory_end)

        # A simple in‐memory cache: mapped by ticker_lower -> DataFrame
        self._cache = {}

    def _find_csv_for_ticker(self, ticker: str) -> str:
        """
        Finds exactly one CSV file whose name starts with {ticker_lower} and ends in .csv
        under self.base_dir. Uses glob "{ticker_lower}*.csv".

        Raises FileNotFoundError if none are found.
        Raises RuntimeError if more than one matches.
        """
        ticker_lower = ticker.lower()
        pattern = f"{ticker_lower}*.csv"
        search_path = os.path.join(self.base_dir, pattern)

        matches = glob.glob(search_path)
        if not matches:
            raise FileNotFoundError(
                f"No CSV file found for ticker '{ticker}' under '{self.base_dir}' "
                f"matching pattern '{pattern}'."
            )
        if len(matches) > 1:
            raise RuntimeError(
                f"Multiple CSV files found for ticker '{ticker}':\n  " + "\n  ".join(matches)
            )
        return matches[0]

    def _load_csv(self, ticker: str) -> pd.DataFrame:
        ticker_lower = ticker.lower()
        if ticker_lower in self._cache:
            return self._cache[ticker_lower]

        csv_path = self._find_csv_for_ticker(ticker)
        usecols = [self.ts_column] + self.ohlcv_cols

        # 1) Read only the necessary columns; parse ts_column as datetime
        df = pd.read_csv(
            csv_path,
            usecols=usecols,
            parse_dates=[self.ts_column],
            dtype={col: "float64" for col in self.ohlcv_cols},
        )

        # 2) Fix timezone: if tz-naive, localize; otherwise assume it's already UTC
        series = df[self.ts_column]
        if series.dt.tz is None:
            series = series.dt.tz_localize(self.tz_from)
        series = series.dt.tz_convert(self.tz_to)
        df[self.ts_column] = series

        # 3) Filter to [memory_start, memory_end] ...
        if self.memory_start is not None or self.memory_end is not None:
            mask = pd.Series(True, index=df.index)
            if self.memory_start is not None:
                mask &= (df[self.ts_column] >= self.memory_start)
            if self.memory_end is not None:
                mask &= (df[self.ts_column] <= self.memory_end)
            df = df.loc[mask.values]

        # 4) Set ts_event as index
        df = df.set_index(self.ts_column)

        self._cache[ticker_lower] = df
        return df


    def get_per_minute_data(
        self,
        ticker: str,
        start_dt: Union[str, pd.Timestamp],
        end_dt: Union[str, pd.Timestamp],
    ) -> pd.DataFrame:
        """
        Returns a DataFrame of per‐minute OHLCV data for `ticker`, between `start_dt` and `end_dt` (inclusive).

        Parameters
        ----------
        ticker : str
            The futures symbol (e.g. "NQ", "ES", etc.)

        start_dt : str or pandas.Timestamp
            The start‐of‐slice, e.g. "2021-05-01 09:30:00-04:00". Must be in tz_to,
            or a tz‐naive string (we’ll assume tz_to).

        end_dt : str or pandas.Timestamp
            The end‐of‐slice, e.g. "2021-05-01 16:00:00-04:00".

        Returns
        -------
        A pandas.DataFrame indexed by tz‐aware datetime (US/Eastern), with columns
        ['open','high','low','close','volume'] (in that order).
        """
        # 1) Load (or fetch from cache)
        df_all = self._load_csv(ticker)

        # 2) Normalize start_dt / end_dt to tz_to
        def _normalize_request(r):
            r_ts = pd.to_datetime(r)
            if r_ts.tzinfo is None:
                return r_ts.tz_localize(self.tz_to)
            else:
                return r_ts.tz_convert(self.tz_to)

        start_ts = _normalize_request(start_dt)
        end_ts = _normalize_request(end_dt)

        # 3) Slice by index (DateTimeIndex)
        return df_all.loc[start_ts : end_ts]

