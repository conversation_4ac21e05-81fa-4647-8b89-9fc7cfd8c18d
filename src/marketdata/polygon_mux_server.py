"""polygon_mux_server.py — simple WebSocket multiplexer for Polygon.io

Runs a local WebSocket server that accepts many downstream client
connections while keeping a *single* authenticated connection to
Polygon's Stocks WebSocket feed.  Clients send subscribe / unsubscribe
requests (mirroring Polygon's format) and receive raw Polygon messages
for the symbols they requested.  Multiple clients may subscribe to the
same symbol; messages are fanned‑out to each subscriber.

Environment variables
---------------------
POLYGON_API_KEY  – *required* Polygon.io API key entitled for WebSockets.
POLYGON_WS_URL   – optional override of upstream URL; defaults to the
                   realtime stocks cluster (wss://socket.polygon.io/stocks).

Quick‑start
-----------
$ export POLYGON_API_KEY=YOUR_KEY
$ python polygon_mux_server.py

Downstream protocol (JSON strings)
----------------------------------
# subscribe to AAPL & MSFT minute aggregates
{"action": "subscribe", "params": "AM.AAPL,AM.MSFT"}

# later, unsubscribe from MSFT
{"action": "unsubscribe", "params": "AM.MSFT"}

Clients receive the *raw* Polygon event objects unchanged.
"""


import asyncio
import json
import logging
import os
from collections import defaultdict
from typing import DefaultDict, Dict, Set

import websockets
from websockets.server import WebSocketServerProtocol

import dotenv

dotenv.load_dotenv()

logger = logging.getLogger("polygon_mux")
logging.basicConfig(
    format="%(asctime)s %(levelname)s %(name)s: %(message)s", level=logging.INFO
)

POLYGON_API_KEY: str | None = os.getenv("POLYGON_API_KEY")
POLYGON_WS_URL: str = os.getenv("POLYGON_WS_URL", "wss://socket.polygon.io/stocks")

if not POLYGON_API_KEY:
    raise RuntimeError("POLYGON_API_KEY environment variable is required")


class PolygonMuxServer:
    """Maintain one upstream Polygon connection and many downstream clients."""

    def __init__(self, host: str = "0.0.0.0", port: int = 8765):
        self.host = host
        self.port = port

        # -------------------------- client bookkeeping --------------------------
        # _clients maps client → {"subs": set[channel_str], "auth": bool}
        self._clients: Dict[WebSocketServerProtocol, dict] = {}
        # _channel_subs maps "AM.AAPL", "A.AAPL", … → set[clients]
        self._channel_subs: DefaultDict[str, Set[WebSocketServerProtocol]] = defaultdict(set)

        # -------------------------- upstream objects ----------------------------
        self._polygon_ws: websockets.WebSocketClientProtocol | None = None
        self._send_lock = asyncio.Lock()  # serialize writes upstream

    # =====================================================================
    # public API
    # =====================================================================

    async def start(self):
        """Run downstream server and manage upstream connection forever."""
        async with websockets.serve(
            self._handle_client, self.host, self.port, ping_interval=None
        ):
            logger.info("Downstream WebSocket server listening on %s:%s", self.host, self.port)
            await self._polygon_connection_loop()

    # =====================================================================
    # ↓↓↓ DOWNSTREAM logic (client‑side) ↓↓↓
    # =====================================================================

    async def _handle_client(self, ws: WebSocketServerProtocol):
        """Per‑client handler."""
        self._clients[ws] = {"subs": set(), "auth": False}
        # 1️⃣  send connected status
        await self._send_status(ws, "connected", "Connected Successfully")
        logger.info("Client connected (%d total)", len(self._clients))

        try:
            async for raw in ws:
                # -----------------------------------------------------------------
                # Parse incoming JSON
                # -----------------------------------------------------------------
                try:
                    msg = json.loads(raw)
                except json.JSONDecodeError:
                    await self._send_error(ws, "Invalid JSON")
                    continue

                action = msg.get("action")
                params = msg.get("params", "")

                if action == "auth":
                    # accept any key; you can plug real validation here
                    self._clients[ws]["auth"] = True
                    await self._send_status(ws, "auth_success", "authenticated")
                    continue

                if action in {"subscribe", "unsubscribe"}:
                    await self._process_subscription(ws, action, params)
                else:
                    await self._send_error(ws, "Unsupported action.")
        except websockets.exceptions.ConnectionClosed:  # noqa: PERF203
            pass
        finally:
            await self._disconnect_client(ws)
            logger.info("Client disconnected (%d remaining)", len(self._clients))

    # ---------------------------------------------------------------------
    # subscription helper
    # ---------------------------------------------------------------------

    async def _process_subscription(self, ws: WebSocketServerProtocol, action: str, params: str):
        tokens = [tok.strip() for tok in params.split(",") if tok.strip()]
        channels: list[str] = []

        # Normalise tokens; default prefix is AM (minute).  Accept only AM or A.
        for tok in tokens:
            if "." in tok:
                prefix, sym = tok.split(".", 1)
                prefix = prefix or "AM"
            else:
                prefix, sym = "AM", tok
            if prefix not in {"AM", "A"}:
                await self._send_error(ws, f"Unsupported prefix '{prefix}'.")
                continue
            channels.append(f"{prefix}.{sym}")

        affected: list[str] = []

        if action == "subscribe":
            for ch in channels:
                if ch not in self._clients[ws]["subs"]:
                    self._clients[ws]["subs"].add(ch)
                    first = len(self._channel_subs[ch]) == 0
                    self._channel_subs[ch].add(ws)
                    if first:
                        await self._send_upstream({"action": "subscribe", "params": ch})
                    affected.append(ch)
        else:  # unsubscribe
            for ch in channels:
                if ch in self._clients[ws]["subs"]:
                    self._clients[ws]["subs"].remove(ch)
                    self._channel_subs[ch].discard(ws)
                    if not self._channel_subs[ch]:
                        await self._send_upstream({"action": "unsubscribe", "params": ch})
                    affected.append(ch)

        if affected:
            msg = ("subscribed to: " if action == "subscribe" else "unsubscribed from: ") + ",".join(affected)
            await self._send_status(ws, "success", msg)

    async def _disconnect_client(self, ws: WebSocketServerProtocol):
        client_info = self._clients.pop(ws, None)
        if client_info is None:
            return
        subs = client_info["subs"]
        for ch in subs:
            self._channel_subs[ch].discard(ws)
            if not self._channel_subs[ch]:
                await self._send_upstream({"action": "unsubscribe", "params": ch})
        try:
            await ws.close()
        except Exception:  # noqa: BLE001
            pass

    # ---------------------------------------------------------------------
    # util: send status & error events (Polygon format) to client
    # ---------------------------------------------------------------------

    async def _send_status(self, ws: WebSocketServerProtocol, status: str, message: str):
        payload = [{"ev": "status", "status": status, "message": message}]
        await ws.send(json.dumps(payload))

    async def _send_error(self, ws: WebSocketServerProtocol, message: str):
        payload = [{"ev": "status", "status": "error", "message": message}]
        await ws.send(json.dumps(payload))

    # =====================================================================
    # ↑↑↑ DOWNSTREAM logic ends here ↑↑↑
    # =====================================================================

    # =====================================================================
    # ↓↓↓ UPSTREAM logic (Polygon‑side) ↓↓↓
    # =====================================================================

    async def _polygon_connection_loop(self):
        while True:
            try:
                async with websockets.connect(POLYGON_WS_URL, ping_interval=20) as ws:
                    self._polygon_ws = ws
                    await self._authenticate_polygon()

                    # re‑sync any existing subs
                    if self._channel_subs:
                        await self._send_upstream({"action": "subscribe", "params": ",".join(self._channel_subs.keys())})

                    await self._consume_polygon()  # returns only on disconnect
            except Exception as exc:  # noqa: BLE001
                logger.exception("Polygon connection error (%s). Reconnecting in 5 s…", exc)
                await asyncio.sleep(5)

    async def _authenticate_polygon(self):
        _ = await self._polygon_ws.recv()  # initial connected status
        await self._polygon_ws.send(
            json.dumps({"action": "auth", "params": POLYGON_API_KEY})
        )
        resp = await self._polygon_ws.recv()
        data = json.loads(resp)
        if not (
            isinstance(data, list)
            and any(d.get("status") == "auth_success" for d in data)
        ):
            raise RuntimeError(f"Polygon auth failed: {resp}")
        logger.info("Authenticated with Polygon.")

    async def _consume_polygon(self):
        async for raw in self._polygon_ws:
            try:
                events = json.loads(raw)
            except json.JSONDecodeError:
                continue
            for ev in events:
                ev_type = ev.get("ev")
                if ev_type not in {"AM", "A"}:
                    continue
                tkr = ev.get("sym")
                if not tkr:
                    continue
                channel = f"{ev_type}.{tkr}"
                for client in list(self._channel_subs.get(channel, [])):
                    try:
                        await client.send(json.dumps([ev]))
                    except websockets.exceptions.ConnectionClosed:
                        await self._disconnect_client(client)

    async def _send_upstream(self, msg: dict):
        async with self._send_lock:
            if self._polygon_ws:
                logger.debug("→ Polygon %s", msg)
                await self._polygon_ws.send(json.dumps(msg))
 

async def _main():
    server = PolygonMuxServer()
    await server.start()


if __name__ == "__main__":
    try:
        asyncio.run(_main())
    except KeyboardInterrupt:
        logger.info("Shutting down.")
