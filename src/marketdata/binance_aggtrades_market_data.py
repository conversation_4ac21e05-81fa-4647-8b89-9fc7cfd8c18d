from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List

import pandas as pd
from zoneinfo import ZoneInfo

from marketdata.imarketdata import IMarketData


class BinanceAggTradesMarketData(IMarketData):
    """Convert Binance *aggTrades* CSV dumps into OHLCV bars.

    Expected directory layout::

        market_data/binance/aggTrades/{TICKER}/{TICKER}-aggTrades-YYYY-MM-DD.csv

    Each CSV row provides (at minimum) these columns::

        price, quantity, transact_time

    * ``transact_time`` is `int` milliseconds since epoch **UTC**.
    * All **public** datetimes (method parameters & DataFrame index) are timezone‑aware
      in ``America/New_York`` (EST/EDT).
    * Bars are labelled on their **left edge** and closed on the right.  A 1‑second
      bar starting 09:30:00 covers 09:30:00 ≤ t < 09:30:01.
    """

    _ROOT = Path("market_data/binance/aggTrades")
    _EASTERN = ZoneInfo("America/New_York")
    _UTC = ZoneInfo("UTC")

    # ------------------------------------------------------------------ #
    # IMarketData implementation
    # ------------------------------------------------------------------ #
    def gather_historical_data(
        self,
        ticker: str,
        start_dt: datetime,
        end_dt: Optional[datetime] = None,
        interval: int = 60,
    ) -> pd.DataFrame:
        """Synchronously load OHLCV bars from local aggTrades CSV files."""

        if interval <= 0:
            raise ValueError("`interval` must be a positive integer (seconds)")

        if end_dt is None:
            end_dt = datetime.now(tz=self._EASTERN)

        # ------------------------------------------------------------------ #
        # Normalise & validate user‑supplied datetimes
        # ------------------------------------------------------------------ #
        start_dt = self._ensure_eastern(start_dt)
        end_dt = self._ensure_eastern(end_dt)

        if start_dt >= end_dt:
            raise ValueError("`start_dt` must be earlier than `end_dt`")

        # ------------------------------------------------------------------ #
        # Locate relevant daily CSV files
        # ------------------------------------------------------------------ #
        files = self._files_for_range(ticker, start_dt, end_dt)
        if not files:
            raise FileNotFoundError(
                f"No Binance aggTrades files found for {ticker} "
                f"between {start_dt} and {end_dt}"
            )

        # ------------------------------------------------------------------ #
        # Load & filter rows (UTC millisecond boundaries)
        # ------------------------------------------------------------------ #
        start_ms = int(start_dt.astimezone(self._UTC).timestamp() * 1_000)
        end_ms = int(end_dt.astimezone(self._UTC).timestamp() * 1_000)

        dfs: list[pd.DataFrame] = []
        cols = ["price", "quantity", "transact_time"]
        dtypes = {"price": "float64", "quantity": "float64", "transact_time": "int64"}

        for fpath in files:
            df = pd.read_csv(fpath, usecols=cols, dtype=dtypes)
            df = df.loc[
                (df["transact_time"] >= start_ms) & (df["transact_time"] < end_ms)
            ]
            if not df.empty:
                dfs.append(df)

        if not dfs:
            raise ValueError("No trade rows found within the specified window.")

        trades = pd.concat(dfs, ignore_index=True)

        # ------------------------------------------------------------------ #
        # Index by Eastern datetime
        # ------------------------------------------------------------------ #
        trades["datetime"] = pd.to_datetime(
            trades["transact_time"], unit="ms", utc=True
        ).dt.tz_convert(self._EASTERN)

        trades.set_index("datetime", inplace=True)
        trades.sort_index(inplace=True)

        # ------------------------------------------------------------------ #
        # Resample to OHLCV
        # ------------------------------------------------------------------ #
        freq = f"{interval}S"
        ohlcv = (
            trades.resample(freq, label="left", closed="left")
            .agg(
                open=("price", "first"),
                high=("price", "max"),
                low=("price", "min"),
                close=("price", "last"),
                volume=("quantity", "sum"),
            )
            .dropna(subset=["open"])
            .astype(
                {
                    "open": "float64",
                    "high": "float64",
                    "low": "float64",
                    "close": "float64",
                    "volume": "float64",
                }
            )
        )

        ohlcv.index.name = "datetime"
        return ohlcv

    # ------------------------------------------------------------------ #
    # Internal helpers
    # ------------------------------------------------------------------ #
    def _ensure_eastern(self, dt: datetime) -> datetime:
        """Attach Eastern tz if naive and convert to Eastern otherwise."""
        if dt.tzinfo is None:
            return dt.replace(tzinfo=self._EASTERN)
        return dt.astimezone(self._EASTERN)

    def _files_for_range(
        self, ticker: str, start_dt: datetime, end_dt: datetime
    ) -> List[Path]:
        """Return daily CSV files that may contain trades for `[start_dt, end_dt)`."""
        ticker = ticker.upper()
        directory = self._ROOT / ticker

        day = start_dt.date()
        last_day = end_dt.date()

        files: List[Path] = []
        while day <= last_day:
            fname = f"{ticker}-aggTrades-{day:%Y-%m-%d}.csv"
            fpath = directory / fname
            if fpath.exists():
                files.append(fpath)
            day += timedelta(days=1)
        return files


# ---------------------------------------------------------------------------- #
# Example usage (remove or adapt in your own codebase)
# ---------------------------------------------------------------------------- #
if __name__ == "__main__":
    md = BinanceAggTradesMarketData()
    eastern = ZoneInfo("America/New_York")
    start = datetime(2025, 6, 30, 6, 42, 0, tzinfo=eastern)
    end = datetime(2025, 6, 30, 6, 44, 0, tzinfo=eastern)

    bars = md.gather_historical_data("ETHUSDT", start, end, interval=60)
    print(bars.head())
