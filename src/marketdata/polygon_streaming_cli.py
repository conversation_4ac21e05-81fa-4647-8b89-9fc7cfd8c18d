import argparse
import async<PERSON>
import logging
import os
from datetime import <PERSON><PERSON><PERSON>

import dotenv

from marketdata.polygon_streaming_market_data import PolygonStreamingMarketData
from marketdata.market_data_builder import MarketDataBuilder
from tools.clock import Clock

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def stream_bars(
    ticker: str,
    interval_minutes: int,
    lookback_minutes: int,
    trade_session: str,
    seconds: bool = False
):
    """
    Stream bar data for a specific ticker.
    
    Args:
        ticker: The ticker symbol to stream data for
        interval_minutes: The bar interval value (minutes by default)
        lookback_minutes: How many minutes of historical data to fetch
        trade_session: Trading session to filter ('rth' or 'full')
        seconds: If True, interpret interval_minutes as seconds (for per-second bars)
    """
    # Load environment variables
    dotenv.load_dotenv()
    
    # Get API key from environment
    api_key = os.environ.get("POLYGON_API_KEY")
    if not api_key:
        raise ValueError("POLYGON_API_KEY environment variable not set")
    
    # Set up clock and market data
    clock = Clock()
    
    # Initialize market data using the builder pattern
    market_data_builder = (MarketDataBuilder(clock=clock)
                          .with_trade_session(trade_session))
    
    # Build market data provider for historical data
    market_data_provider = market_data_builder.build_market_data()
    
    # Create streaming market data provider
    streaming_market_data = PolygonStreamingMarketData(
        websocket_url="ws://localhost:9765",
        market_data_provider=market_data_provider,
        clock=clock,
        api_key=api_key,
        trade_session=trade_session
    )
    
    # Set up interval and lookback
    if seconds:
        interval = timedelta(seconds=interval_minutes)
        logger.info(f"Using per-second interval of {interval_minutes}s")
    else:
        interval = timedelta(minutes=interval_minutes)
    lookback_window = timedelta(minutes=lookback_minutes)
    start_time = clock.now()
    
    logger.info(f"Starting stream for {ticker} with {interval_minutes}m interval, {lookback_minutes}m lookback")
    logger.info(f"Using data with {trade_session} session")
    
    try:
        # Subscribe to bar updates
        bar_counter = 0
        async for hist_bars, current_bar in streaming_market_data.subscribe_to_bars(
            ticker=ticker,
            interval=interval,
            start_time=start_time,
            lookback_window=lookback_window
        ):
            bar_counter += 1
            
            # Print information about the data received
            hist_count = len(hist_bars) if not hist_bars.empty else 0
            current_time = clock.now()
            
            logger.info(f"Received bar {bar_counter} at {current_time}")
            logger.info(f"Historical bars count: {hist_count}")
            
            if not current_bar.empty:
                # Print current bar details
                bar = current_bar.iloc[0]
                logger.info(f"Current bar: O={bar['open']:.2f}, H={bar['high']:.2f}, "
                           f"L={bar['low']:.2f}, C={bar['close']:.2f}, V={bar['volume']}")
            else:
                logger.info("No current bar data")
            
            logger.info("-" * 50)
            
            # Add a small delay to avoid flooding the console
            await asyncio.sleep(0.1)
            
    except KeyboardInterrupt:
        logger.info("Stream interrupted by user")
    except Exception as e:
        logger.error(f"Error in streaming: {e}")
        raise
    finally:
        logger.info("Stream ended")

def main():
    parser = argparse.ArgumentParser(description="Test Polygon Streaming Market Data")
    parser.add_argument("ticker", help="Ticker symbol to stream data for")
    parser.add_argument("--interval", type=int, default=1,
                       help="Interval value (default: 1). Use --seconds to interpret as seconds, otherwise minutes.")
    parser.add_argument("--seconds", action="store_true",
                       help="Interpret --interval as seconds (per-second streaming).")
    parser.add_argument("--lookback", type=int, default=30, 
                       help="Historical lookback window in minutes (default: 30)")
    parser.add_argument("--session", choices=["rth", "full"], default="rth",
                       help="Trading session filter (default: rth)")
    
    args = parser.parse_args()
    
    try:
        asyncio.run(stream_bars(
            ticker=args.ticker,
            interval_minutes=args.interval,
            lookback_minutes=args.lookback,
            trade_session=args.session,
            seconds=args.seconds
        ))
    except KeyboardInterrupt:
        print("\nProgram terminated by user")

if __name__ == "__main__":
    main()