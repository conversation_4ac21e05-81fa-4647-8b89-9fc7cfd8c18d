import httpx
import pandas as pd
import csv
from io import StringIO
from datetime import datetime
import asyncio

class ThetaBulkOHLCData:
    """
    A class to interact with ThetaData's Bulk OHLC Snapshot API.
    Retrieves current day OHLC data for stocks.
    """
    
    def __init__(self):
        """Initialize the ThetaBulkOHLCData class with the base URL."""
        self.base_url = "http://127.0.0.1:25510/v2"
        
    async def get_current_day_ohlc(self, root="0", venue="nqb"):
        """
        Retrieve current day OHLC data for stocks asynchronously.
        
        Parameters:
        -----------
        root : str, default "0"
            The symbol of the security. Use "0" to get data for all symbols.
        venue : str, default "nqb"
            The venue to fetch data from ('nqb' for Nasdaq Basic, 'utp_cta' for merged UTP & CTA).
            
        Returns:
        --------
        pandas.DataFrame
            DataFrame with columns: date, open, high, low, close, volume, symbol
            with a standard integer index
        """
        # Endpoint for bulk OHLC snapshot
        endpoint = "/bulk_snapshot/stock/ohlc"
        url = self.base_url + endpoint
        
        # Set parameters for the request
        params = {
            'root': root,
            'use_csv': 'true',
            'venue': venue
        }
        
        try:
            # Make the API request asynchronously
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, timeout=60)
                response.raise_for_status()  # Raise exception for HTTP errors
            
            # Parse the CSV response
            csv_data = StringIO(response.text)
            
            # Read directly using pandas with proper header handling
            df = pd.read_csv(csv_data)
            
            # Check if dataframe is empty or missing expected columns
            if df.empty or 'root' not in df.columns:
                print("No data returned or unexpected format")
                return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume', 'symbol'])
            
            # Rename 'root' column to 'symbol'
            df.rename(columns={'root': 'symbol'}, inplace=True)
            
            # Convert date column (YYYYMMDD format) to datetime
            df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
            
            # Select and reorder only the required columns
            result_df = df[['date', 'open', 'high', 'low', 'close', 'volume', 'symbol']]
            
            # Reset index to get a standard integer index
            result_df.reset_index(drop=True, inplace=True)
            
            return result_df
            
        except httpx.HTTPError as e:
            print(f"HTTP error occurred: {e}")
            return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume', 'symbol'])
        except Exception as e:
            print(f"An error occurred: {e}")
            return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume', 'symbol'])


if __name__ == "__main__":
    theta_ohlc = ThetaBulkOHLCData()
    
    # Create and run an async function to get data
    async def main():
        current_day_df = await theta_ohlc.get_current_day_ohlc()
        print(current_day_df)
    
    # Run the async function
    asyncio.run(main())