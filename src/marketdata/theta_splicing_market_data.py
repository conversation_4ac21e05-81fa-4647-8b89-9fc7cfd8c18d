import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Tuple

import pandas as pd
import pytz

from .imarketdata import IMarketData
from .theta_market_data import ThetaMarketData

logger = logging.getLogger(__name__)

class ThetaSplicingMarketData(IMarketData):
    """
    A market data provider that intelligently routes requests between realtime and historical
    Theta data sources, splicing the results together when necessary.
    
    This class handles three main scenarios:
    1. Recent data (<15 mins): Routes to realtime market data
    2. Mixed timeframes: Splits and routes to appropriate sources, then splices results
    3. Large historical requests: Splits into 30-day chunks for better performance
    """
    
    def __init__(
        self, 
        historical_market_data: ThetaMarketData,
        realtime_market_data: Optional[ThetaMarketData] = None, 
        max_days_per_request: int = 30
    ):
        """
        Initialize the ThetaSplicingMarketData with historical data source and optional realtime source.
        
        Args:
            historical_market_data: ThetaMarketData instance configured for historical data
            realtime_market_data: Optional ThetaMarketData instance configured for realtime data
            max_days_per_request: Maximum number of days to include in a single historical request
        """
        self.historical_market_data = historical_market_data
        self.realtime_market_data = realtime_market_data
        self.max_days_per_request = max_days_per_request
        self.eastern_tz = pytz.timezone("US/Eastern")
    
    def _ensure_eastern_tz(self, dt: datetime) -> datetime:
        """
        Ensure datetime is in Eastern timezone.
        
        Args:
            dt: Datetime to convert
            
        Returns:
            Datetime in Eastern timezone
        """
        if dt.tzinfo is None:
            return self.eastern_tz.localize(dt)
        return dt.astimezone(self.eastern_tz)
    
    def _is_recent(self, dt: datetime) -> bool:
        """
        Check if a datetime is within the last 15 minutes.
        
        Args:
            dt: Datetime to check
            
        Returns:
            True if datetime is within 15 minutes of now, False otherwise
        """
        now = datetime.now(self.eastern_tz)
        return (now - dt) <= timedelta(minutes=15)
    
    def _split_date_range(
        self, start_dt: datetime, end_dt: datetime
    ) -> List[Tuple[datetime, datetime]]:
        """
        Split a date range into chunks of max_days_per_request.
        
        Args:
            start_dt: Start datetime
            end_dt: End datetime
            
        Returns:
            List of (start_dt, end_dt) tuples representing the chunks
        """
        chunks = []
        current_start = start_dt
        
        while current_start < end_dt:
            current_end = min(
                current_start + timedelta(days=self.max_days_per_request), 
                end_dt
            )
            chunks.append((current_start, current_end))
            current_start = current_end
            
        return chunks
    
    async def gather_historical_data_async(
        self, 
        ticker: str, 
        start_dt: datetime, 
        end_dt: Optional[datetime] = None,
        interval: int = 60
    ) -> pd.DataFrame:
        """
        Asynchronously retrieve historical data, intelligently routing between
        realtime and historical data sources if realtime is available.
        
        Args:
            ticker: The ticker symbol
            start_dt: Start datetime
            end_dt: End datetime (defaults to now)
            interval: The interval between data points in seconds (default: 60 seconds)
            
        Returns:
            DataFrame with historical market data
        """
        # Ensure timezone-aware datetimes
        start_dt = self._ensure_eastern_tz(start_dt)
        if end_dt is None:
            end_dt = datetime.now(self.eastern_tz)
        else:
            end_dt = self._ensure_eastern_tz(end_dt)
        
        # If no realtime data source is available, use historical data for everything
        if self.realtime_market_data is None:
            return await self._gather_chunked_historical_data(ticker, start_dt, end_dt, interval)
        
        # Case 1: Entire range is recent - use realtime data
        if self._is_recent(start_dt):
            logger.debug(f"Using realtime data for {ticker} from {start_dt} to {end_dt}")
            return await self.realtime_market_data.gather_historical_data_async(
                ticker, start_dt, end_dt, interval
            )
        
        # Case 2: Range straddles realtime and historical
        if self._is_recent(end_dt):
            realtime_start = datetime.now(self.eastern_tz) - timedelta(minutes=15)
            
            # Get historical data
            historical_df = await self._gather_chunked_historical_data(
                ticker, start_dt, realtime_start - timedelta(microseconds=1), interval
            )
            
            # Get realtime data
            realtime_df = await self.realtime_market_data.gather_historical_data_async(
                ticker, realtime_start, end_dt, interval
            )
            
            # Splice the data
            return self._splice_dataframes(historical_df, realtime_df)
        
        # Case 3: Entire range is historical - chunk if needed
        return await self._gather_chunked_historical_data(ticker, start_dt, end_dt, interval)
    
    async def _gather_chunked_historical_data(
        self, ticker: str, start_dt: datetime, end_dt: datetime, interval: int = 60
    ) -> pd.DataFrame:
        """
        Split a large historical request into chunks and gather data.
        
        Args:
            ticker: The ticker symbol
            start_dt: Start datetime
            end_dt: End datetime
            interval: The interval between data points in seconds (default: 60 seconds)
            
        Returns:
            DataFrame with historical market data
        """
        chunks = self._split_date_range(start_dt, end_dt)
        
        if len(chunks) == 1:
            # Single chunk, no need for parallel requests
            return await self.historical_market_data.gather_historical_data_async(
                ticker, chunks[0][0], chunks[0][1], interval
            )
        
        # Multiple chunks, gather in parallel
        tasks = [
            self.historical_market_data.gather_historical_data_async(
                ticker, chunk_start, chunk_end, interval
            )
            for chunk_start, chunk_end in chunks
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Combine results
        combined_df = pd.concat(results) if results else pd.DataFrame()
        
        # Remove duplicates and sort
        if not combined_df.empty:
            combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
            combined_df = combined_df.sort_index()
            
        return combined_df
    
    def _splice_dataframes(self, df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
        """
        Splice two dataframes together, removing duplicates.
        
        Args:
            df1: First dataframe
            df2: Second dataframe
            
        Returns:
            Combined dataframe
        """
        if df1.empty:
            return df2
        if df2.empty:
            return df1
            
        # Combine and remove duplicates
        combined_df = pd.concat([df1, df2])
        combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
        
        # Validate the combined dataframe
        self._validate_dataframe(combined_df)
        
        return combined_df.sort_index()
    
    def _validate_dataframe(self, df: pd.DataFrame) -> None:
        """
        Validate a dataframe to ensure it has expected structure and values.
        
        Args:
            df: DataFrame to validate
            
        Raises:
            ValueError: If validation fails
        """
        if df.empty:
            return
            
        # Check for expected columns
        expected_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in expected_columns if col not in df.columns]
        if missing_columns:
            logger.warning(f"Missing expected columns: {missing_columns}")
            
        # Check for non-chronological data
        if not df.index.is_monotonic_increasing:
            logger.warning("DataFrame index is not monotonically increasing")
            
        # Check for unreasonable values (e.g., zero prices)
        if 'close' in df.columns and (df['close'] <= 0).any():
            zero_prices = df[df['close'] <= 0]
            logger.warning(f"Found {len(zero_prices)} rows with zero or negative prices")
    
    def gather_historical_data(
        self, 
        ticker: str, 
        start_dt: datetime, 
        end_dt: Optional[datetime] = None,
        interval: int = 60
    ) -> pd.DataFrame:
        """
        Synchronously retrieve historical data by wrapping the async method.
        
        Args:
            ticker: The ticker symbol
            start_dt: Start datetime
            end_dt: End datetime (defaults to now)
            interval: The interval between data points in seconds (default: 60 seconds)
            
        Returns:
            DataFrame with historical market data
        """
        return asyncio.run(
            self.gather_historical_data_async(ticker, start_dt, end_dt, interval)
        )
    
    async def get_quote_async(
        self, 
        ticker: str, 
        start_dt: Optional[datetime] = None, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Asynchronously retrieve quote data, routing to the appropriate data source.
        
        Args:
            ticker: The ticker symbol
            start_dt: Start datetime (defaults to now)
            end_dt: End datetime (defaults to start_dt)
            
        Returns:
            DataFrame with quote data
        """
        if start_dt is None:
            start_dt = datetime.now(self.eastern_tz)
        else:
            start_dt = self._ensure_eastern_tz(start_dt)
            
        if end_dt is None:
            end_dt = start_dt
        else:
            end_dt = self._ensure_eastern_tz(end_dt)
        
        # If no realtime data source is available, use historical data for everything
        if self.realtime_market_data is None:
            return await self.historical_market_data.get_quote_async(ticker, start_dt, end_dt)
        
        # For recent quotes, use realtime data
        if self._is_recent(end_dt):
            return await self.realtime_market_data.get_quote_async(ticker, start_dt, end_dt)
        
        # For historical quotes, use historical data
        return await self.historical_market_data.get_quote_async(ticker, start_dt, end_dt)
    
    def get_quote(
        self, 
        ticker: str, 
        start_dt: Optional[datetime] = None, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Synchronously retrieve quote data by wrapping the async method.
        
        Args:
            ticker: The ticker symbol
            start_dt: Start datetime (defaults to now)
            end_dt: End datetime (defaults to start_dt)
            
        Returns:
            DataFrame with quote data
        """
        return asyncio.run(
            self.get_quote_async(ticker, start_dt, end_dt)
        ) 