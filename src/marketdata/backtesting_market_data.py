from datetime import datetime, timed<PERSON>ta
from typing import AsyncGenerator, Op<PERSON>, Tu<PERSON>, Dict, Any
import pandas as pd
import asyncio
import logging
from zoneinfo import ZoneInfo

from marketdata.istreamingmarketdata import IStreamingMarketData
from marketdata.imarketdata import IMarketData
from tools.clock import Clock

logger = logging.getLogger(__name__)

# Define EST timezone constant
EST = ZoneInfo("US/Eastern")

class BacktestingMarketData(IStreamingMarketData):
    """
    Implementation of IStreamingMarketData for backtesting purposes.
    Simulates streaming market data by using historical data.
    """
    
    def __init__(
        self, 
        market_data: IMarketData, 
        start_date: datetime, 
        end_date: datetime,
        clock: Clock
    ):
        """
        Initialize the backtesting market data provider.
        
        Args:
            market_data: The market data provider to fetch historical data
            start_date: The start date for backtesting
            end_date: The end date for backtesting (defaults to current time if None)
            clock: Clock instance for time management (creates a new one if None)
        """
        # Ensure dates are timezone-aware (EST)
        self.market_data = market_data
        self.start_date = self._ensure_est_timezone(start_date)
        self.end_date = self._ensure_est_timezone(end_date)
        self.clock = clock
        
    def _ensure_est_timezone(self, dt: datetime) -> datetime:
        """
        Ensure datetime is timezone-aware with EST timezone.
        
        Args:
            dt: The datetime to check/convert
            
        Returns:
            Timezone-aware datetime in EST
        """
        if dt.tzinfo is None:
            return dt.replace(tzinfo=EST)
        elif dt.tzinfo != EST:
            return dt.astimezone(EST)
        return dt
        
    async def subscribe_to_bars(
        self,
        ticker: str,
        interval: timedelta,
        start_time: datetime,
        lookback_window: timedelta
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """
        Simulate streaming market data by yielding historical bars one by one.
        
        Args:
            ticker: The ticker symbol
            interval: The time interval for bars
            start_time: timestamp indicating when the ticker was discovered
            lookback_window: The historical data window needed
            
        Yields:
            Tuple of (historical_bars, latest_bar)
        """
        # Ensure start_time is timezone-aware (EST)
        start_time = self._ensure_est_timezone(start_time)
        
        logger.info(f"Backtesting {ticker} from {start_time} to {self.end_date} with interval {interval}")
        
        # Calculate the adjusted start time to account for lookback window
        adjusted_start_time = start_time - lookback_window
        
        # Convert interval to seconds for the API call
        interval_seconds = int(interval.total_seconds())
        
        # Fetch all historical data for the backtesting period, including lookback window
        all_data = await self.market_data.gather_historical_data_async(
            ticker, 
            adjusted_start_time, 
            self.end_date, 
            interval=interval_seconds
        )
        
        if all_data.empty:
            logger.error(f"No data available for {ticker} for entire backtesting period")
            return
                    
        # Sort data by timestamp to ensure chronological order
        all_data = all_data.sort_index()
        
        # Filter data to only include bars from start_time onwards for streaming
        streaming_data = all_data[all_data.index >= start_time]
                        
        if streaming_data.empty:
            logger.error(f"No data available for start time during backtesting period {ticker} after start_time {start_time}")
            return
        
        # Ensure we have data for the last 15 minutes of market hours (3:45pm to 4:00pm EST)
        streaming_data = self._ensure_market_close_data(streaming_data, start_time, interval)
                
        # Simulate streaming by yielding one bar at a time
        for i in range(len(streaming_data)):
            # Get the current bar
            latest_bar = streaming_data.iloc[[i]]
            current_time = latest_bar.index[0]
            
            # Calculate lookback start time based on the timedelta
            lookback_start_time = current_time - lookback_window
            
            # Get historical data for the lookback window using datetime filtering
            historical_bars = all_data[(all_data.index >= lookback_start_time) & 
                                      (all_data.index < current_time)]
            
                        
            # Yield the historical and latest bars
            yield historical_bars, latest_bar
            
    def _ensure_market_close_data(self, data: pd.DataFrame, start_time: datetime, interval: timedelta) -> pd.DataFrame:
        """
        Ensures that the data includes the last 15 minutes of market hours (3:45pm to 4:00pm EST)
        for every market day in the dataset. If this data is missing for any day, it will be 
        created by cloning the most recent available bars for that day.
        
        Args:
            data: The original market data DataFrame
            start_time: The start time for backtesting
            interval: The time interval for bars
            
        Returns:
            DataFrame with guaranteed data for the last 15 minutes of market hours for each day
        """
        if data.empty:
            return data
            
        # Ensure start_time is timezone-aware (EST)
                    
        # Get all unique dates in the data
        unique_dates = sorted(set(dt.date() for dt in data.index if dt >= start_time))
        
        # Create a list to hold all synthetic bars
        all_synthetic_bars = []
        
        # Process each date separately
        for date in unique_dates:
            # Get data for this specific date
            day_data = data[data.index.date == date]
            
            if day_data.empty:
                continue
                
            # Create timestamps for the expected market close period (3:50pm to 4:00pm EST)
            market_close_start = datetime.combine(date, datetime.strptime("15:50", "%H:%M").time(), tzinfo=EST)
            market_close_end = datetime.combine(date, datetime.strptime("16:00", "%H:%M").time(), tzinfo=EST)
            
            # Check if we already have data for the market close period on this day
            close_period_data = day_data[
                (day_data.index >= market_close_start) & 
                (day_data.index <= market_close_end)
            ]
            
            # Only add synthetic data if:
            # 1. We don't have any data in the close period
            # 2. We have some data for this day (to use as a template)
            if close_period_data.empty and not day_data.empty:
                logger.info(f"Adding synthetic market close data (3:45pm-4:00pm EST) for {date}")
                
                # Get the last bar of the day to use as a template
                last_bar = day_data.iloc[-1].copy()
                
                # Create synthetic bars for the market close period based on the interval
                synthetic_bars = []
                current_time = market_close_start
                
                while current_time <= market_close_end:
                    # Create a new bar with the same data but updated timestamp
                    synthetic_bars.append((current_time, last_bar.copy()))
                    # Use the provided interval for the time step
                    current_time += interval
                
                all_synthetic_bars.extend(synthetic_bars)
        
        # If we have synthetic bars, create a DataFrame and append to original data
        if all_synthetic_bars:
            synthetic_df = pd.DataFrame(
                [bar[1] for bar in all_synthetic_bars],
                index=[bar[0] for bar in all_synthetic_bars]
            )
            
            # Append synthetic data to original data and sort by timestamp
            result = pd.concat([data, synthetic_df])
            return result.sort_index()
        
        return data