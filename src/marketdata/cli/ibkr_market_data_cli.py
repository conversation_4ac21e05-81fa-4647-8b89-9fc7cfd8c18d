#!/usr/bin/env python3
import argparse
from datetime import date
import nest_asyncio
from ib_async import IB
from marketdata.ibkr_market_data import IBKRMarketData

nest_asyncio.apply()

def parse_date(s: str) -> date:
    try:
        return date.fromisoformat(s)
    except ValueError:
        raise argparse.ArgumentTypeError(f"Not a valid date: {s!r}, expected YYYY-MM-DD")

def main():
    parser = argparse.ArgumentParser(description="IBKR Historical Market-Data CLI")
    parser.add_argument("symbol", type=str,
                        help="Ticker symbol (e.g. AAPL)")
    parser.add_argument("start_date", type=parse_date,
                        help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end-date", type=parse_date, default=None,
                        help="End date in YYYY-MM-DD format (default: today)")
    parser.add_argument("--host", type=str, default="127.0.0.1",
                        help="IBKR gateway host (default: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8496,
                        help="IBKR gateway port (default: 8496)")
    parser.add_argument("--client-id", type=int, default=1001,
                        help="IBKR clientId (default: 1001)")
    parser.add_argument("--interval", type=int, default=60,
                        help="Interval in seconds (must be multiple of 60)")
    args = parser.parse_args()

    ib = IB()
    print(f"🔌 Connecting to IBKR at {args.host}:{args.port} (clientId={args.client_id})")
    ib.connect(args.host, args.port, clientId=args.client_id)

    data_client = IBKRMarketData(ib)
    print(f"📊 Fetching {args.symbol!r} from {args.start_date} to {args.end_date or 'now'} @ {args.interval}s interval…")
    df = data_client.gather_historical_data(
        args.symbol,
        args.start_date,
        args.end_date,
        args.interval
    )

    if df.empty:
        print("⚠️  No data retrieved.")
    else:
        print(df)

    ib.disconnect()

if __name__ == "__main__":
    main() 