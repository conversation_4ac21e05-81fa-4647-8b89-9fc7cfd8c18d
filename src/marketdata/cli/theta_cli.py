#!/usr/bin/env python3
import argparse
import logging
from datetime import datetime, timedelta
import pandas as pd
import pytz

from .theta_market_data import ThetaMarketData, ThetaVenue

# python -m src.marketdata.theta_cli AAPL --start_date 20250301 --start_time 09:30 --end_date 20250310 \
#          --end_time 16:00 --interval 216000000 --rth --output aapl_data.csv --verbose

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_datetime(date_str, time_str=None):
    """Parse date and optional time string into a timezone-aware datetime object."""
    eastern = pytz.timezone("US/Eastern")
    
    if time_str:
        # If time is provided, parse both date and time
        dt_str = f"{date_str} {time_str}"
        try:
            dt = datetime.strptime(dt_str, "%Y%m%d %H:%M:%S")
        except ValueError:
            try:
                dt = datetime.strptime(dt_str, "%Y%m%d %H:%M")
            except ValueError:
                raise ValueError(f"Invalid datetime format: {dt_str}. Use YYYYMMDD HH:MM[:SS]")
    else:
        # If only date is provided
        try:
            dt = datetime.strptime(date_str, "%Y%m%d")
        except ValueError:
            raise ValueError(f"Invalid date format: {date_str}. Use YYYYMMDD")
    
    return eastern.localize(dt)

def main():
    parser = argparse.ArgumentParser(description="Theta Market Data CLI")
    
    # Required arguments
    parser.add_argument("ticker", type=str, help="Ticker symbol (e.g., AAPL)")
    
    # Date range arguments
    parser.add_argument("--start_date", type=str, required=True, help="Start date in YYYYMMDD format")
    parser.add_argument("--start_time", type=str, help="Start time in HH:MM[:SS] format (default: market open)")
    parser.add_argument("--end_date", type=str, help="End date in YYYYMMDD format (default: same as start_date)")
    parser.add_argument("--end_time", type=str, help="End time in HH:MM[:SS] format (default: market close)")
    
    # Data retrieval options
    parser.add_argument("--interval", type=int, default=60, help="Data interval in seconds (default: 60)")
    parser.add_argument("--venue", type=str, choices=["nqb", "utp_cta"], default="utp_cta", 
                        help="Data venue (default: utp_cta)")
    parser.add_argument("--rth", action="store_true", help="Regular Trading Hours only")
    parser.add_argument("--no_prune", action="store_true", help="Do not prune rows with zero values")
    
    # Output options
    parser.add_argument("--output", type=str, help="Output file path (CSV format)")
    parser.add_argument("--verbose", action="store_true", help="Show detailed output")
    
    args = parser.parse_args()

    # Set logging level based on verbosity
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Parse start datetime
    start_dt = parse_datetime(args.start_date, args.start_time)
    
    # Parse end datetime (default to start_date if not provided)
    end_date = args.end_date if args.end_date else args.start_date
    end_dt = parse_datetime(end_date, args.end_time)
    
    # Determine venue
    venue = ThetaVenue.NQB if args.venue == "nqb" else ThetaVenue.UTP_CTA
    
    # Create market data instance
    market_data = ThetaMarketData(
        venue=venue, 
        prune_zero_prices=not args.no_prune,
        rth=args.rth
    )
    
    logger.info(f"Retrieving {args.ticker} data from {start_dt} to {end_dt} with {args.interval}s interval")
    logger.info(f"Using venue: {args.venue}, RTH only: {args.rth}, Prune zeros: {not args.no_prune}")
    
    # Retrieve historical data
    historical_df = market_data.gather_historical_data(
        args.ticker, 
        start_dt, 
        end_dt,
        args.interval
    )
    
    if historical_df.empty:
        logger.warning("No data retrieved for the specified parameters.")
        return
    
    # Display summary
    logger.info(f"Retrieved {len(historical_df)} data points")
    logger.info(f"Date range: {historical_df.index.min()} to {historical_df.index.max()}")
    
    # Display sample data
    if args.verbose:
        pd.set_option('display.max_rows', 20)
        pd.set_option('display.width', 120)
        logger.info("\nSample data:")
        logger.info("\n" + str(historical_df.head(10)))
    else:
        logger.info("\nFirst 5 rows:")
        logger.info("\n" + str(historical_df.head()))
    
    # Save to file if requested
    if args.output:
        historical_df.to_csv(args.output)
        logger.info(f"Data saved to {args.output}")

if __name__ == "__main__":
    main()
