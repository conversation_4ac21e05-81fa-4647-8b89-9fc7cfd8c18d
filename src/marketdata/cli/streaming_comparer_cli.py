import asyncio
import statistics
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, List, Any, AsyncGenerator

from marketdata.market_data_builder import MarketDataBuilder
from tools.clock import Clock

import pandas as pd

from marketdata.istreamingmarketdata import IStreamingMarketData 
from marketdata.polygon_streaming_market_data import PolygonStreamingMarketData

###############################################################################
# Data structures
###############################################################################

Bar = pd.Series  # One‑row Series with index [open, high, low, close, volume]


@dataclass
class PerFieldStats:
    """Keeps running stats for one numeric column."""

    diffs_pct: List[float] = field(default_factory=list)

    def update(self, pct_diff: float):
        self.diffs_pct.append(pct_diff)

    def summary(self) -> Dict[str, float]:
        if not self.diffs_pct:
            return {"n": 0}
        return {
            "n": len(self.diffs_pct),
            "mean_%": statistics.mean(self.diffs_pct),
            "max_%": max(self.diffs_pct),
        }


@dataclass
class ComparisonStats:
    """Aggregates stats across all fields and timing."""

    interval: timedelta

    # OHLCV fields
    fields: Tuple[str, ...] = ("open", "high", "low", "close", "volume")
    field_stats: Dict[str, PerFieldStats] = field(default_factory=dict)

    # Timing
    last_ts_a: Optional[datetime] = None
    last_ts_b: Optional[datetime] = None
    interval_mismatches_a: int = 0
    interval_mismatches_b: int = 0

    def __post_init__(self):
        for f in self.fields:
            self.field_stats[f] = PerFieldStats()

    # ---------------------------------------------------------------------
    #  OHLCV differences
    # ---------------------------------------------------------------------
    def update_value_diffs(self, bar_a: Bar, bar_b: Bar):
        for f in self.fields:
            v_a, v_b = bar_a[f], bar_b[f]
            if v_a is None or v_b is None:
                continue  # skip NaNs
            if v_a == v_b == 0:
                pct = 0.0
            else:
                denom = (abs(v_a) + abs(v_b)) / 2 or 1e-9  # avoid /0
                pct = abs(v_a - v_b) / v_a * 100
            self.field_stats[f].update(pct)

    # ---------------------------------------------------------------------
    #  Interval checks for each stream separately
    # ---------------------------------------------------------------------
    def update_interval_check(self, provider_tag: str, current_ts: datetime):
        last_ts_attr = "last_ts_a" if provider_tag == "A" else "last_ts_b"
        mismatch_attr = (
            "interval_mismatches_a" if provider_tag == "A" else "interval_mismatches_b"
        )

        last_ts = getattr(self, last_ts_attr)
        if last_ts is not None:
            delta = current_ts - last_ts
            if delta != self.interval:
                setattr(self, mismatch_attr, getattr(self, mismatch_attr) + 1)
        setattr(self, last_ts_attr, current_ts)

    # ---------------------------------------------------------------------
    #  Pretty print
    # ---------------------------------------------------------------------
    def report(self) -> str:
        lines = ["\n====================  COMPARISON SUMMARY  ====================\n"]
        lines.append("OHLCV percentage differences (%) per field:")
        for f, stats in self.field_stats.items():
            s = stats.summary()
            if s["n"]:
                lines.append(
                    f"  {f:<6}: n={s['n']:>4}, mean={s['mean_%']:.3f}%, max={s['max_%']:.3f}%"
                )
            else:
                lines.append(f"  {f:<6}: no matched bars")

        lines.append("\nInterval mismatches:")
        lines.append(
            f"  Stream A: {self.interval_mismatches_a} times bar spacing != {self.interval}"
        )
        lines.append(
            f"  Stream B: {self.interval_mismatches_b} times bar spacing != {self.interval}"
        )
        lines.append("===============================================================\n")
        return "\n".join(lines)

###############################################################################
# Core logic
###############################################################################

async def _bar_emitter(
    tag: str,
    provider: IStreamingMarketData,
    ticker: str,
    interval: timedelta,
    start_time: datetime,
    lookback: timedelta,
    queue: asyncio.Queue,
):
    """Consumes subscribe_to_bars() and pushes *completed* bars onto a queue."""
    last_processed: Optional[datetime] = None
    async for hist, current in provider.subscribe_to_bars(
        ticker, interval, start_time, lookback
    ):
        if hist.empty:
            continue
        latest_completed: Bar = hist.iloc[-1]
        ts = latest_completed.name  # timestamp is index
        if last_processed is None or ts > last_processed:
            await queue.put((tag, ts, latest_completed))
            last_processed = ts


async def compare_streams(
    provider_a: IStreamingMarketData,
    provider_b: IStreamingMarketData,
    ticker: str,
    interval: timedelta,
    run_for: Optional[int] = 100,  # number of matched bars to process
    start_time: Optional[datetime] = None,
    lookback: timedelta = timedelta(0),
) -> ComparisonStats:
    """Run live comparison and return aggregated stats."""
    start_time = start_time or datetime.now(tz=provider_a.clock.tz)  # type: ignore

    q: asyncio.Queue = asyncio.Queue()
    stats = ComparisonStats(interval=interval)

    # Launch two background emitters
    tasks = [
        asyncio.create_task(
            _bar_emitter("A", provider_a, ticker, interval, start_time, lookback, q)
        ),
        asyncio.create_task(
            _bar_emitter("B", provider_b, ticker, interval, start_time, lookback, q)
        ),
    ]

    # Buffers to hold unmatched bars
    buffer_a: Dict[datetime, Bar] = {}
    buffer_b: Dict[datetime, Bar] = {}

    matched = 0
    try:
        while run_for is None or matched < run_for:
            tag, ts, bar = await q.get()

            # Interval check per stream
            stats.update_interval_check(tag, ts)

            # Store in buffer and check for match
            if tag == "A":
                buffer_a[ts] = bar
            else:
                buffer_b[ts] = bar

            if ts in buffer_a and ts in buffer_b:
                stats.update_value_diffs(buffer_a.pop(ts), buffer_b.pop(ts))
                matched += 1
    finally:
        # Ensure tasks are cancelled on exit
        for t in tasks:
            t.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await t
    return stats

###############################################################################
# Sample CLI entry point
###############################################################################

if __name__ == "__main__":
    import argparse, importlib, contextlib

    parser = argparse.ArgumentParser(
        description="Compare two IStreamingMarketData implementations live."
    )
    parser.add_argument("ticker", help="Symbol to stream, e.g. TQQQ")
    parser.add_argument("--interval", type=int, default=60, help="Bar size in seconds")
    parser.add_argument("--bars", type=int, default=100, help="Number of matched bars to collect")
  

    args = parser.parse_args()

    clock = Clock()
    market_data_builder = (MarketDataBuilder(clock=clock)
                          .with_trade_session("full"))
    
    market_data_provider = market_data_builder.build_market_data()
  
    prov_a = PolygonStreamingMarketData(
        market_data_provider=market_data_provider,
        clock=clock,
        api_key='',
        trade_session='full'
    )
    
    # theta
    prov_b = PolygonStreamingMarketData(
        websocket_url="ws://localhost:9765",
        market_data_provider=market_data_provider,
        clock=clock,
        api_key='',
        trade_session='full'
    )

    # Run
    stats = asyncio.run(
        compare_streams(
            prov_a,
            prov_b,
            ticker=args.ticker,
            interval=timedelta(seconds=args.interval),
            run_for=args.bars,
        )
    )
    print(stats.report())
