#!/usr/bin/env python3
import asyncio
import argparse
from datetime import datetime, timedelta

import nest_asyncio
from brokers.schwab_broker import <PERSON><PERSON>bBroker
from marketdata.schwab_market_data import SchwabStreamingMarketData

nest_asyncio.apply()

async def bars_command(args):
    broker = SchwabBroker(token_path=args.token_path)
    try:
        http_client = await broker.get_http_client()
    except RuntimeError:
        print("❌ Failed to connect to Schwab API")
        return

    streaming = SchwabStreamingMarketData(http_client, account_id=args.account_id)

    interval = timedelta(minutes=1)
    lookback = timedelta(minutes=args.lookback)
    start_time = datetime.now()

    print(f"🔔 Subscribing to 1-min bars for {args.symbol!r} (lookback {args.lookback} min)…")
    try:
        async for hist_df, latest_df in streaming.subscribe_to_bars(
            args.symbol, interval, start_time, lookback
        ):
            # On the first yield hist_df is full history, latest_df last bar;
            # thereafter, latest_df is each new bar.
            print("\n=== Historical Data ===")
            print(hist_df)
            print("\n+++ Latest Bar +++")
            print(latest_df)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user.")
    finally:
        await streaming.close()

async def main():
    parser = argparse.ArgumentParser(description="Schwab Market-Data CLI")
    subparsers = parser.add_subparsers(dest="command", required=True)

    bars_p = subparsers.add_parser("bars", help="Subscribe to 1-minute bars")
    bars_p.add_argument("symbol", type=str, help="Ticker (e.g. AAPL or /ES)")
    bars_p.add_argument(
        "-l", "--lookback", type=int, default=60,
        help="Historical lookback window in minutes"
    )
    bars_p.add_argument(
        "--account-id", type=int, default=None,
        help="Optional raw account ID for streaming"
    )
    bars_p.add_argument(
        "--token-path", type=str, default=None,
        help="Optional path to authentication token file (overrides SCHWAB_TOKEN_PATH)"
    )

    try:
        args = parser.parse_args()
    except SystemExit:
        # on parse failure (e.g. no symbol), just show help and exit
        parser.print_help()
        return

    if args.command == "bars":
        await bars_command(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    asyncio.run(main())