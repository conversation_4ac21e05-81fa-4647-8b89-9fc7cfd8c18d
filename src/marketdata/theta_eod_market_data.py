import asyncio
import logging
import csv
from io import StringIO
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Union

import httpx
import pandas as pd
import pytz

from marketdata import IMarketData, ThetaSplicingMarketData

# Create a module-level logger
logger = logging.getLogger(__name__)

class ThetaEODMarketData(IMarketData):
    def __init__(
        self, 
        base_url: str = "http://127.0.0.1:25510", 
        prune_zero_prices: bool = True,
        timeout: int = 60,  # Default timeout of 60 seconds
        max_concurrent_requests: int = 4
    ):
        """
        Initialize ThetaEODMarketData with a base URL for Theta Terminal.
        This class specifically retrieves end-of-day stock data.
        
        :param base_url: The base URL where the Theta terminal API is hosted.
        :param prune_zero_prices: If True, prunes rows where the 'open' or 'close' columns equal zero.
                                 If False, the data is left intact.
        :param timeout: Timeout in seconds for HTTP requests (default: 60 seconds).
        :param max_concurrent_requests: Maximum number of parallel requests (default: 4).
        """
        self.base_url = base_url
        self.prune_zero_prices = prune_zero_prices
        self.timeout = timeout
        self.request_semaphore = asyncio.Semaphore(max_concurrent_requests)

    def _convert_csv_to_dataframe(self, csv_data: str) -> pd.DataFrame:
        """
        Convert CSV data from Theta's response into a pandas DataFrame.
        
        :param csv_data: CSV data returned from the API.
        :return: A pandas DataFrame with a datetime index.
        """
        if not csv_data.strip():
            return pd.DataFrame()
        
        # Parse the CSV data
        csv_reader = csv.reader(StringIO(csv_data))
        header = next(csv_reader, None)  # Extract header row
        
        if not header:
            return pd.DataFrame()
        
        rows = list(csv_reader)
        if not rows:
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(rows, columns=header)
        
        # Convert numeric columns to appropriate types
        numeric_columns = ['ms_of_day', 'ms_of_day2', 'open', 'high', 'low', 'close', 
                          'volume', 'count', 'bid_size', 'bid', 'ask_size', 'ask', 'date']
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Create timestamp from date and ms_of_day (using ms_of_day for the EOD report time)
        if 'date' in df.columns and 'ms_of_day' in df.columns:
            # Convert date to datetime and add ms_of_day
            df['timestamp'] = (
                pd.to_datetime(df['date'].astype(str), format='%Y%m%d')
                + pd.to_timedelta(df['ms_of_day'], unit='ms')
            )
            # Localize the timestamp to US/Eastern
            df['timestamp'] = df['timestamp'].dt.tz_localize('US/Eastern')
            df.set_index('timestamp', inplace=True)
        
        # Filter out rows where the 'open' or 'close' columns are zero,
        # but only if prune_zero_prices is True.
        if self.prune_zero_prices:
            if 'open' in df.columns and 'close' in df.columns:
                df = df[(df['open'] != 0) & (df['close'] != 0)]
        
        # Select only OHLCV columns for simplicity
        ohlcv_columns = ['open', 'high', 'low', 'close', 'volume']
        available_columns = [col for col in ohlcv_columns if col in df.columns]
        
        return df[available_columns]

    async def gather_historical_data_async(
        self, 
        ticker: str, 
        start_dt: datetime, 
        end_dt: Optional[datetime] = None,
        interval: int = 86400  # Default to daily interval (in seconds)
    ) -> pd.DataFrame:
        """
        Asynchronously retrieves EOD market data from Theta.
        
        :param ticker: The ticker symbol.
        :param start_dt: Start datetime.
        :param end_dt: End datetime; if None, uses the current Eastern time.
        :param interval: Ignored for EOD data as it's always daily.
        :return: A pandas DataFrame containing EOD OHLCV data.
        """
        eastern = pytz.timezone('US/Eastern')
        now_eastern = datetime.now(eastern)
        
        if end_dt is None:
            end_dt = now_eastern
        
        # Ensure the dates are in the correct format (YYYYMMDD)
        start_date_str = start_dt.strftime('%Y%m%d')
        end_date_str = end_dt.strftime('%Y%m%d')
        
        params = {
            'root': ticker,
            'start_date': start_date_str,
            'end_date': end_date_str,
            'use_csv': 'true'  # Use CSV format for efficiency
        }
        
        url = f"{self.base_url}/v2/hist/stock/eod"
        accumulated_data = []
        
        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(self.timeout)) as client:
                next_url = url
                current_params = params
                
                while True:
                    async with self.request_semaphore:
                        response = await client.get(next_url, params=current_params)
                    
                    response.raise_for_status()
                    accumulated_data.append(response.text)
                    
                    # Check for pagination via the 'Next-Page' header
                    next_page = response.headers.get('Next-Page')
                    if next_page and next_page != 'null':
                        next_url = next_page
                        current_params = None  # Next pages already have parameters embedded in the URL
                    else:
                        break
        
        except Exception as e:
            logger.error(f"Error encountered while retrieving EOD data for {ticker}: {str(e)}", exc_info=True)
            return pd.DataFrame()
        
        # Combine all CSV data, handling headers correctly
        combined_data = ""
        for i, data in enumerate(accumulated_data):
            if i == 0:
                combined_data = data
            else:
                # Skip the header row for subsequent data chunks
                lines = data.strip().split('\n')
                if len(lines) > 1:  # Ensure there's more than just the header
                    combined_data += '\n' + '\n'.join(lines[1:])
        
        df = self._convert_csv_to_dataframe(combined_data)
        return df

    def gather_historical_data(
        self, 
        ticker: str, 
        start_dt: datetime, 
        end_dt: Optional[datetime] = None,
        interval: int = 86400  # Default to daily interval (in seconds)
    ) -> pd.DataFrame:
        """
        Synchronously retrieves EOD market data by wrapping the asynchronous version.
        
        :param ticker: The ticker symbol.
        :param start_dt: Start datetime.
        :param end_dt: End datetime.
        :param interval: Ignored for EOD data as it's always daily.
        :return: A pandas DataFrame containing EOD OHLCV data.
        """
        return asyncio.run(
            self.gather_historical_data_async(ticker, start_dt, end_dt, interval)
        )

    async def get_quote_async(
        self, 
        ticker: str, 
        start_dt: Optional[datetime] = None, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Not implemented for EOD data. This method is provided for compatibility 
        with the IMarketData interface but raises NotImplementedError.
        
        :raises: NotImplementedError
        """
        raise NotImplementedError("get_quote_async is not implemented for EOD data. Use gather_historical_data instead.")

    def get_quote(
        self, 
        ticker: str, 
        start_dt: Optional[datetime] = None, 
        end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        """
        Not implemented for EOD data. This method is provided for compatibility 
        with the IMarketData interface but raises NotImplementedError.
        
        :raises: NotImplementedError
        """
        raise NotImplementedError("get_quote is not implemented for EOD data. Use gather_historical_data instead.")


if __name__ == "__main__":
    import argparse
    import os
    import sys
    from pathlib import Path
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    
    parser = argparse.ArgumentParser(description='Theta EOD Market Data CLI')
    parser.add_argument('--symbol', '-s', required=True, help='Ticker symbol to download')
    parser.add_argument('--output-dir', '-o', help='Output directory for data (for CSV output)')
    parser.add_argument('--days', '-d', type=int, default=90, help='Number of days of history to download (default: 90)')
    parser.add_argument('--save-csv', action='store_true', help='Save data to CSV file (for debugging)')
    parser.add_argument('--market-data-dir', '-m', help='Directory to store market data using DiskMarketDataWriter')
    
    args = parser.parse_args()
    
    # Setup dates
    eastern = pytz.timezone('US/Eastern')
    end_date = datetime.now(eastern)
    start_date = end_date - timedelta(days=args.days)
    
    # Initialize the EOD market data client
    eod_data = ThetaSplicingMarketData(ThetaEODMarketData())
    
    try:
        # Fetch data
        logger.info(f"Downloading EOD data for {args.symbol} from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        df = eod_data.gather_historical_data(args.symbol, start_date, end_date)
        
        if df.empty:
            logger.warning(f"No data found for {args.symbol}")
            sys.exit(1)
        
        # Save to CSV if requested
        if args.save_csv:
            if not args.output_dir:
                logger.error("Output directory must be specified when using --save-csv")
                sys.exit(1)
                
            output_path = Path(args.output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            output_file = output_path / f"{args.symbol}_eod.csv"
            df.to_csv(output_file)
            logger.info(f"Successfully saved {len(df)} days of EOD data to {output_file}")
        
        # Save to DiskMarketDataWriter if specified
        if args.market_data_dir:
            try:
                from marketdata.disk_market_data_writer import DiskMarketDataWriter
                
                # Prepare data for the writer
                # Reset index to get date as a column if it's in the index
                if isinstance(df.index, pd.DatetimeIndex):
                    df_for_writer = df.reset_index()
                    # Rename 'index' to 'date' if needed
                    if 'index' in df_for_writer.columns:
                        df_for_writer = df_for_writer.rename(columns={'index': 'date'})
                else:
                    df_for_writer = df.copy()
                    # If there's no date column, try to create one from the index
                    if 'date' not in df_for_writer.columns:
                        df_for_writer['date'] = df_for_writer.index
                
                # Ensure all required columns exist
                required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
                missing_columns = [col for col in required_columns if col not in df_for_writer.columns]
                
                if missing_columns:
                    logger.error(f"Missing required columns: {missing_columns}")
                    logger.info(f"Available columns: {df_for_writer.columns.tolist()}")
                    
                    # Try to map common column names
                    column_mapping = {}
                    if 'timestamp' in df_for_writer.columns and 'date' in missing_columns:
                        column_mapping['timestamp'] = 'date'
                    
                    # Apply any mappings found
                    if column_mapping:
                        df_for_writer = df_for_writer.rename(columns=column_mapping)
                        logger.info(f"Renamed columns: {column_mapping}")
                        # Update missing columns list
                        missing_columns = [col for col in required_columns if col not in df_for_writer.columns]
                
                # Check again after attempted fixes
                if missing_columns:
                    logger.error(f"Still missing required columns after fixes: {missing_columns}")
                    logger.error("Cannot save to market data storage")
                    sys.exit(1)
                
                # Debug info
                logger.info(f"DataFrame columns for writer: {df_for_writer.columns.tolist()}")
                logger.info(f"First row sample: {df_for_writer.iloc[0].to_dict()}")
                
                # Initialize the writer
                writer = DiskMarketDataWriter(base_dir=args.market_data_dir)
                
                # Add data
                logger.info(f"Saving {len(df_for_writer)} records for {args.symbol} to market data storage")
                writer.add_data(df_for_writer, args.symbol)
                logger.info(f"Successfully saved data to market data storage at {args.market_data_dir}")
                
            except ImportError:
                logger.error("Could not import DiskMarketDataWriter. Make sure it's in your Python path.")
                sys.exit(1)
            except Exception as e:
                logger.error(f"Error saving to market data storage: {str(e)}", exc_info=True)
                sys.exit(1)
        
        # Print summary
        logger.info(f"Data summary for {args.symbol}:")
        logger.info(f"Date range: {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
        logger.info(f"First row: {df.iloc[0].to_dict()}")
        logger.info(f"Last row: {df.iloc[-1].to_dict()}")
        
        # If neither output option was specified, warn the user
        if not args.save_csv and not args.market_data_dir:
            logger.warning("No output method specified. Data was downloaded but not saved.")
            logger.warning("Use --save-csv with --output-dir or --market-data-dir to save the data.")
        
    except Exception as e:
        logger.error(f"Error downloading data for {args.symbol}: {str(e)}", exc_info=True)
        sys.exit(1)