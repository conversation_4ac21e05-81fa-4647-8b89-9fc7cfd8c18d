from abc import ABC
from datetime import datetime
import asyncio
import pandas as pd
from zoneinfo import ZoneInfo
from typing import Optional

class DateTimeHelper:
    EST = ZoneInfo("America/New_York")

    @staticmethod
    def to_est(dt: datetime) -> datetime:
        if dt is None:
            return None
        if dt.tzinfo is None:
            return dt.replace(tzinfo=DateTimeHelper.EST)
        return dt.astimezone(DateTimeHelper.EST)

    @staticmethod
    def strip_tz(df: pd.DataFrame) -> pd.DataFrame:
        df = df.copy()
        # strip tz from index
        if isinstance(df.index, pd.DatetimeIndex) and df.index.tz is not None:
            df.index = df.index.tz_convert(DateTimeHelper.EST).tz_localize(None)
        # strip tz from any datetime columns
        for col in df.select_dtypes(include=["datetimetz"]).columns:
            df[col] = df[col].dt.tz_convert(DateTimeHelper.EST).dt.tz_localize(None)
        return df

class EstMarketDataWrapper(ABC):
    """
    Wraps any IMarketData, normalizing all datetimes to EST before call,
    and stripping tzinfo off the returned DataFrame.
    """

    def __init__(self, inner: IMarketData):
        self._inner = inner

    def gather_historical_data(
        self, ticker: str, start_dt: datetime, end_dt: Optional[datetime] = None, interval: int = 60
    ) -> pd.DataFrame:
        # normalize inputs
        start_est = DateTimeHelper.to_est(start_dt)
        end_est = DateTimeHelper.to_est(end_dt or datetime.now())
        # delegate
        df = self._inner.gather_historical_data(ticker, start_est, end_est, interval)
        # strip tz on output
        return DateTimeHelper.strip_tz(df)

    async def gather_historical_data_async(
        self, ticker: str, start_dt: datetime, end_dt: Optional[datetime] = None, interval: int = 60
    ) -> pd.DataFrame:
        # normalize inputs
        start_est = DateTimeHelper.to_est(start_dt)
        end_est = DateTimeHelper.to_est(end_dt or datetime.now())
        # delegate async
        df = await self._inner.gather_historical_data_async(ticker, start_est, end_est, interval)
        return DateTimeHelper.strip_tz(df)

    def get_quote(
        self, ticker: str, start_dt: Optional[datetime] = None, end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        start_est = DateTimeHelper.to_est(start_dt) if start_dt else None
        end_est   = DateTimeHelper.to_est(end_dt)   if end_dt   else None
        df = self._inner.get_quote(ticker, start_est, end_est)
        return DateTimeHelper.strip_tz(df)

    async def get_quote_async(
        self, ticker: str, start_dt: Optional[datetime] = None, end_dt: Optional[datetime] = None
    ) -> pd.DataFrame:
        start_est = DateTimeHelper.to_est(start_dt) if start_dt else None
        end_est   = DateTimeHelper.to_est(end_dt)   if end_dt   else None
        df = await self._inner.get_quote_async(ticker, start_est, end_est)
        return DateTimeHelper.strip_tz(df)
