import asyncio
import json
import logging
import pandas as pd
import websockets
from datetime import datetime, timedelta, time
from typing import AsyncGenerator, Dict, List, Optional, Tuple, Any
from zoneinfo import ZoneInfo
import secrets

from marketdata.istreamingmarketdata import IStreamingMarketData
from marketdata.imarketdata import IMarketData
from tools.clock import Clock
from tools.trade_condition_utils import (
    process_trade_for_ohlcv
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

class ThetaDataStreamingMarketData(IStreamingMarketData):
    def __init__(
        self, 
        market_data_provider: IMarketData, 
        clock: Clock,
        websocket_url: str = 'ws://127.0.0.1:25520/v1/events',
        use_polling: bool = False,
        polling_interval: timedelta = timedelta(seconds=30),
        clone_data_near_close: bool = False,
        trade_session: str = 'rth'
    ):
        """
        Initialize the Theta Data streaming market data provider.
        
        Args:
            market_data_provider: An implementation of IMarketData to fetch historical data
            clock: Clock instance for getting the current time
            websocket_url: The WebSocket URL for Theta Data streaming API
            use_polling: If True, use polling with gather_historical_data_async instead of WebSocket
            polling_interval: How often to poll for new data when use_polling is True
            clone_data_near_close: If True, clone the last bar when near market close with no new data
            trade_session: The trading session to filter data. Options:
                           - "rth" (regular trading hours only; default)
                           - "full" (all trading hours)
        """
        self.market_data_provider = market_data_provider
        self.clock = clock
        self.websocket_url = websocket_url
        self.stream_id_counter = secrets.randbits(64)
        self.active_streams = {}
        self.est_tz = ZoneInfo('America/New_York')
        self.use_polling = use_polling
        self.polling_interval = polling_interval
        self.clone_data_near_close = clone_data_near_close
        
        
        if trade_session.lower() not in {"rth", "full"}:
            raise ValueError(f"Invalid trade_session: {trade_session}. Must be either 'rth' or 'full'.")
        self.trade_session = trade_session
    
    def _in_allowed_session(self, dt: datetime) -> bool:
        """
        Check if the provided datetime is within the allowed trading session.
        For 'rth', only allow regular trading hours (9:30-16:00 EST).
        For 'full', allow pre-market (4:00-9:30), regular (9:30-16:00) and after-hours (16:00-20:00) EST.
        """
        from tools.clock import is_trading_time, is_regular_hours
        if self.trade_session.lower() == 'rth':
            return is_regular_hours(dt)
        else:
            return is_trading_time(dt)
    
    async def subscribe_to_bars(
        self,
        ticker: str,
        interval: timedelta,
        start_time: datetime,
        lookback_window: timedelta
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """
        Subscribe to real-time bar updates for a specific ticker and interval.
        
        Args:
            ticker: The ticker symbol to subscribe to
            interval: The time interval for each bar
            start_time: timestamp indicating when the ticker was discovered
            lookback_window: Time window to include historical data
        
        Returns:
            AsyncGenerator yielding tuples of (historical_bars, current_bar)
        """
        # Get historical data if lookback_window is specified
        historical_bars = pd.DataFrame()
        current_time = self.clock.now() # TODO: Use start_time
        
        # Create a unique subscription ID
        subscription_id = f"{ticker}_{interval.total_seconds()}"
        self.active_streams[subscription_id] = True
        
        try:
            if lookback_window:
                start_dt = current_time - lookback_window
                historical_bars = await self.market_data_provider.gather_historical_data_async(
                    ticker, start_dt, current_time
                )
                
                if not historical_bars.empty:
                    # Calculate the current interval start time
                    current_bar_start = self._get_interval_start(current_time, interval)
                    
                    # Split the data into historical bars and current open bar
                    completed_bars = historical_bars[historical_bars.index < current_bar_start]
                    current_bars = historical_bars[historical_bars.index >= current_bar_start]
                    
                    # If we have a current open bar, yield the data immediately
                    if not current_bars.empty:
                        logger.info(f"Historical data contains current time for {ticker}. Yielding immediately.")
                        yield completed_bars, pd.DataFrame([current_bars.iloc[-1]])
            
            if self.use_polling:
                async for hist_bars, current_bar in self._subscribe_using_polling(
                    ticker, interval, historical_bars, subscription_id
                ):
                    yield hist_bars, current_bar
            else:
                async for hist_bars, current_bar in self._subscribe_using_websocket(
                    ticker, interval, historical_bars, subscription_id
                ):
                    yield hist_bars, current_bar
        finally:
            # Clean up when generator exits
            self.active_streams[subscription_id] = False
            logger.info(f"Subscription {subscription_id} closed")

    async def _subscribe_using_polling(
        self,
        ticker: str,
        interval: timedelta,
        historical_bars: pd.DataFrame,
        subscription_id: str
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """
        Subscribe to bar updates using polling with gather_historical_data_async.
        
        Args:
            ticker: The ticker symbol to subscribe to
            interval: The time interval for each bar
            historical_bars: Initial historical bars
            subscription_id: Unique identifier for the subscription
            
        Yields:
            Tuples of (historical_bars, current_bar)
        """
        rapid_poll_count = 0
        
        # Set polling interval to match the requested bar interval for efficiency
        # But ensure it's not too short (minimum 30 seconds)
        effective_polling_interval = max(interval // 2, timedelta(seconds=30)) + timedelta(seconds=2)
        
        # Initialize last update time
        last_update_time = self.clock.now()
        logger.info(f"Polling started for ticker {ticker} with interval {interval}")
        logger.debug(f"Polling started at {last_update_time} for ticker {ticker} with interval {interval}")
        
        # Convert timedelta to seconds for the interval parameter
        interval_seconds = int(interval.total_seconds())
        
        # Variable to control polling frequency
        current_polling_interval = effective_polling_interval.total_seconds()
        
        while True:
            # Check if subscription is still active
            if not self.active_streams.get(subscription_id, False):
                logger.info(f"Polling subscription {subscription_id} was closed, exiting")
                break
            
            logger.debug(f"Polling loop iteration starting with polling interval: {current_polling_interval} seconds for ticker {ticker}")
            await asyncio.sleep(current_polling_interval)
            current_time = self.clock.now()
            logger.debug(f"Woke up from sleep, current_time: {current_time}")
            
            # Fetch the latest data using the specified interval
            fetch_start_time = min(last_update_time - interval, current_time - interval)
            logger.debug(f"Fetching data for ticker {ticker} from {fetch_start_time} to {current_time}")
        
            if not self._in_allowed_session(fetch_start_time):
                logger.debug(f"Outside allowed trading session '{self.trade_session}', skipping data fetch for ticker {ticker}.")
                last_update_time = current_time
                continue

            latest_data = await self.market_data_provider.gather_historical_data_async(
                ticker, fetch_start_time, current_time, interval=interval_seconds
            )
            logger.debug(f"Fetched data for ticker {ticker}: {len(latest_data)} rows")
            
            if latest_data.empty:
                # If no data received, handle the empty data case
                current_polling_interval, latest_data = self._handle_empty_data(
                    ticker, current_time, interval, historical_bars, 
                    effective_polling_interval, current_polling_interval, rapid_poll_count
                )
                
                # Update rapid poll count based on whether we're still doing rapid polling
                rapid_poll_count = rapid_poll_count + 1 if current_polling_interval == 10 else 0
                
                # If we still have no data after handling, continue to next polling cycle
                if latest_data.empty:
                    last_update_time = current_time
                    continue
                # Otherwise, proceed with the existing logic for processing data
            else:
                # Reset rapid poll count when we get data
                rapid_poll_count = 0
                # Reset to normal polling interval when data is received
                current_polling_interval = effective_polling_interval.total_seconds()
            
            # Calculate the current interval start time
            current_bar_start = self._get_interval_start(current_time, interval)
                        
            # Update historical bars with completed bars, using newer data if there's overlap
            new_completed_bars = latest_data[latest_data.index < current_bar_start]
            if historical_bars.empty:
                historical_bars = new_completed_bars
            else:
                # Drop any overlapping timestamps in historical_bars
                historical_bars = historical_bars[~historical_bars.index.isin(new_completed_bars.index)]
                historical_bars = pd.concat([historical_bars, new_completed_bars]).sort_index()
            logger.debug(f"Updated historical_bars: {len(historical_bars)} rows (added {len(new_completed_bars)} new rows)")
            
            # Extract current bar
            current_bar = latest_data[latest_data.index >= current_bar_start]
            logger.debug(f"Current bar extracted with {len(current_bar)} rows starting from {current_bar_start}")
            
            if current_bar.empty:
                # Clone the last bar and update its timestamp to the current bar start
                last_bar = historical_bars.iloc[-1].copy()
                last_bar.name = current_bar_start  # update index
                current_bar = pd.DataFrame([last_bar])
                logger.debug(f"No new current bar data; cloning last bar for current bar starting at {current_bar_start}")
         
            # Update the last update time
            last_update_time = current_time
            
            yield historical_bars, current_bar

    async def _subscribe_using_websocket(
        self,
        ticker: str,
        interval: timedelta,
        historical_bars: pd.DataFrame,
        subscription_id: str
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """
        Subscribe to bar updates using WebSocket streaming.
        
        Args:
            ticker: The ticker symbol to subscribe to
            interval: The time interval for each bar
            historical_bars: Initial historical bars
            subscription_id: Unique identifier for the subscription
            
        Yields:
            Tuples of (historical_bars, current_bar)
        """
        # Connect to Theta Data WebSocket
        async with websockets.connect(self.websocket_url) as websocket:
            # Subscribe to trade stream
            stream_id = self._get_next_stream_id()
            subscribe_request = {
                "msg_type": "STREAM",
                "sec_type": "STOCK",
                "req_type": "TRADE",
                "add": True,
                "id": stream_id,
                "contract": {
                    "root": ticker
                }
            }
            
            await websocket.send(json.dumps(subscribe_request))
            
            # Wait for verification response as per Theta Data Streaming API with a timeout
            verification_response = await asyncio.wait_for(websocket.recv(), timeout=5)
            verification_msg = json.loads(verification_response)
            header = verification_msg.get('header', {})
            if header.get('type') != 'REQ_RESPONSE' or header.get('response') != 'SUBSCRIBED' or header.get('req_id') != stream_id:
                logger.error('Subscription verification failed: %s', verification_response)
                raise Exception('Subscription verification failed')
            logger.info(f"Subscription verification successful: {verification_response}")
            
            # Process incoming trade data and aggregate into bars
            current_time = self.clock.now()
            current_bar_start = self._get_interval_start(current_time, interval)
            trades_in_current_bar = []
            
            try:
                while True:
                    response = await websocket.recv()
                    logger.debug(f"Received websocket message: {response}")
                    trade_data = json.loads(response)
                    
                    # Skip non-trade messages or connection status messages
                    if not self._is_valid_trade_message(trade_data):
                        continue
                    
                    # Process the trade
                    trade = self._process_trade(trade_data)
                    trade_time = trade['timestamp']
                    
                    # Check if this trade belongs to the current bar or a new bar
                    if trade_time >= current_bar_start + interval:
                        # Close the current bar and start a new one
                        if trades_in_current_bar:
                            closed_bar = self._create_bar_from_trades(trades_in_current_bar, current_bar_start, interval)
                            
                            # Add the closed bar to historical bars
                            if historical_bars.empty:
                                # Create a new DataFrame with the closed bar
                                new_bar_df = pd.DataFrame([closed_bar])
                                # Set timestamp as index if it exists as a column
                                if 'timestamp' in new_bar_df.columns:
                                    new_bar_df = new_bar_df.set_index('timestamp')
                                historical_bars = new_bar_df
                            else:
                                # Create a DataFrame with the closed bar
                                new_bar_df = pd.DataFrame([closed_bar])
                                # Set timestamp as index if it exists as a column
                                if 'timestamp' in new_bar_df.columns:
                                    new_bar_df = new_bar_df.set_index('timestamp')
                                # Concatenate with historical bars
                                historical_bars = pd.concat([historical_bars, new_bar_df])
                            
                            historical_bars = self.ensure_datetime_index(historical_bars)
                            # Sort the historical bars by timestamp
                            historical_bars = historical_bars.sort_index()
                            
                            # Log the addition of the new bar
                            logger.debug(f"Added closed bar at {current_bar_start} to historical bars. Now have {len(historical_bars)} bars.")
                        
                        # Start a new bar
                        current_bar_start = self._get_interval_start(trade_time, interval)
                        trades_in_current_bar = [trade]
                        
                        # Create the current open bar
                        current_bar = pd.DataFrame([self._create_partial_bar_from_trades(trades_in_current_bar, current_bar_start, interval)])
                        # Ensure timestamp is the index and it's a datetime object
                        current_bar = current_bar.set_index('timestamp')
                        
                        # Only yield if we're in an allowed trading session.
                        if self._in_allowed_session(self.clock.now()):
                            yield historical_bars, current_bar
                    else:
                        # Add trade to the current bar
                        trades_in_current_bar.append(trade)
                        
                        # Update the current open bar
                        current_bar = pd.DataFrame([self._create_partial_bar_from_trades(trades_in_current_bar, current_bar_start, interval)])
                        # Ensure timestamp is the index and it's a datetime object
                        current_bar = self.ensure_datetime_index(current_bar.set_index('timestamp'))
            finally:
                # Unsubscribe from the trade stream
                unsubscribe_request = {
                    "msg_type": "STREAM",
                    "sec_type": "STOCK",
                    "req_type": "TRADE",
                    "add": False,
                    "id": stream_id,
                    "contract": {
                        "root": ticker
                    }
                }
                try:
                    await websocket.send(json.dumps(unsubscribe_request))
                except:
                    logger.warning(f"Failed to unsubscribe from trade stream for {ticker}")
    
    def _get_next_stream_id(self) -> int:
        """Get the next stream ID and increment the counter."""
        stream_id = self.stream_id_counter
        self.stream_id_counter += 1
        return stream_id
    
    def _get_interval_start(self, dt: datetime, interval: timedelta) -> datetime:
        """
        Calculate the start time of the interval containing the given datetime.
        
        Args:
            dt: The datetime to find the interval start for
            interval: The interval duration
            
        Returns:
            The datetime representing the start of the interval
        """
        # Ensure dt is timezone-aware in EST
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=self.est_tz)  # Use replace instead of localize
        elif dt.tzinfo != self.est_tz:
            dt = dt.astimezone(self.est_tz)
            
        # For minute intervals
        if interval.total_seconds() < 86400:  # Less than a day
            seconds = int(interval.total_seconds())
            total_seconds = dt.hour * 3600 + dt.minute * 60 + dt.second
            interval_seconds = (total_seconds // seconds) * seconds
            
            return datetime(
                dt.year,
                dt.month,
                dt.day,
                interval_seconds // 3600,
                (interval_seconds % 3600) // 60,
                interval_seconds % 60,
                0,
                tzinfo=self.est_tz  # Directly attach timezone
            )
        # For daily intervals
        else:
            return datetime(
                dt.year, 
                dt.month, 
                dt.day, 
                0, 0, 0, 0, 
                tzinfo=self.est_tz  # Directly attach timezone
            )
    
    def _is_valid_trade_message(self, message: Dict[str, Any]) -> bool:
        """Check if the message is a valid trade message."""
        return (
            isinstance(message, dict) and
            message.get('header', {}).get('type') == 'TRADE' and
            'trade' in message
        )
    
    def _process_trade(self, trade_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a trade message from Theta Data.
        
        Args:
            trade_data: The trade data from Theta Data
            
        Returns:
            A dictionary with processed trade information
        """
        trade = trade_data['trade']
        
        # Convert ms_of_day to a timestamp
        date = trade['date']
        ms_of_day = trade['ms_of_day']
        
        # Convert date (YYYYMMDD) to year, month, day
        year = date // 10000
        month = (date // 100) % 100
        day = date % 100
        
        # Convert ms_of_day to hours, minutes, seconds, microseconds
        seconds_of_day = ms_of_day // 1000
        ms = ms_of_day % 1000
        hours = seconds_of_day // 3600
        minutes = (seconds_of_day % 3600) // 60
        seconds = seconds_of_day % 60
        
        # Create timezone-aware datetime in EST using ZoneInfo
        timestamp = datetime(
            year, month, day, hours, minutes, seconds, ms * 1000, 
            tzinfo=self.est_tz
        )
        
        return {
            'timestamp': timestamp,
            'price': trade['price'],
            'size': trade['size'],
            'exchange': trade['exchange'],
            'condition': trade['condition']
        }
    
    def _create_bar_from_trades(self, trades: List[Dict[str, Any]], bar_start: datetime, interval: timedelta) -> Dict[str, Any]:
        """
        Create an OHLCV bar from a list of trades.
        
        Args:
            trades: List of processed trades
            bar_start: The start time of the bar
            interval: The bar interval
            
        Returns:
            A dictionary representing an OHLCV bar
        """
        if not trades:
            return {
                'timestamp': bar_start,
                'open': None,
                'high': None,
                'low': None,
                'close': None,
                'volume': 0
            }
        
        # Initialize OHLCV data
        ohlcv = {
            'open': None,
            'high': float('-inf'),
            'low': float('inf'),
            'close': None,
            'volume': 0
        }
        
        # Process each trade and update OHLCV data
        for trade in trades:
            ohlcv = process_trade_for_ohlcv(trade, ohlcv)
        
        # If no eligible trades were found, use the first trade's price for all values
        if ohlcv['open'] is None:
            first_price = trades[0]['price']
            ohlcv['open'] = first_price
            ohlcv['high'] = first_price
            ohlcv['low'] = first_price
            ohlcv['close'] = first_price
        
        # If high/low were not updated (no eligible trades), reset them
        if ohlcv['high'] == float('-inf'):
            ohlcv['high'] = ohlcv['open']
        if ohlcv['low'] == float('inf'):
            ohlcv['low'] = ohlcv['open']
        
        # Add timestamp to the result
        ohlcv['timestamp'] = bar_start
        
        return ohlcv
    
    def _create_partial_bar_from_trades(self, trades: List[Dict[str, Any]], bar_start: datetime, interval: timedelta) -> Dict[str, Any]:
        """
        Create a partial OHLCV bar from a list of trades (for the currently open bar).
        
        Args:
            trades: List of processed trades
            bar_start: The start time of the bar
            interval: The bar interval
            
        Returns:
            A dictionary representing a partial OHLCV bar
        """
        # Use the same logic as _create_bar_from_trades but mark it as incomplete
        bar = self._create_bar_from_trades(trades, bar_start, interval)
        bar['is_complete'] = False
        return bar
    
    def _create_typed_ohlcv_dataframe(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Create a properly typed DataFrame for OHLCV data to avoid pandas dtype warnings.
        
        Args:
            data: Input DataFrame with OHLCV data
            
        Returns:
            DataFrame with explicitly defined dtypes
        """
        if data.empty:
            # Create empty DataFrame with proper dtypes
            return pd.DataFrame({
                'timestamp': pd.Series(dtype='datetime64[ns]'),
                'open': pd.Series(dtype='float64'),
                'high': pd.Series(dtype='float64'),
                'low': pd.Series(dtype='float64'),
                'close': pd.Series(dtype='float64'),
                'volume': pd.Series(dtype='int64')
            })
        
        # For existing data, convert to proper dtypes
        typed_df = data.copy()
        typed_df['timestamp'] = pd.to_datetime(typed_df['timestamp'])
        typed_df['open'] = typed_df['open'].astype('float64')
        typed_df['high'] = typed_df['high'].astype('float64')
        typed_df['low'] = typed_df['low'].astype('float64')
        typed_df['close'] = typed_df['close'].astype('float64')
        typed_df['volume'] = typed_df['volume'].astype('int64')
        
        return typed_df

    def _handle_empty_data(
        self, 
        ticker: str, 
        current_time: datetime, 
        interval: timedelta, 
        historical_bars: pd.DataFrame,
        effective_polling_interval: timedelta,
        current_polling_interval: float,
        rapid_poll_count: int
    ) -> Tuple[float, pd.DataFrame]:
        """
        Handle the case when no new data is received during polling.
        
        Args:
            ticker: The ticker symbol
            current_time: Current timestamp
            interval: Bar interval
            historical_bars: Historical bars DataFrame
            effective_polling_interval: Normal polling interval
            current_polling_interval: Current polling interval
            rapid_poll_count: Number of consecutive rapid polls so far
            
        Returns:
            Tuple containing:
            - new polling interval
            - latest_data (may be empty or contain a cloned bar)
        """
        # Check if we're near market close (after 15:50 EST)
        current_time_est = current_time.astimezone(self.est_tz)
        is_near_market_close = (
            current_time_est.time() >= time(15, 50) and 
            current_time_est.time() < time(16, 0)
        )
        
        # Empty DataFrame to return if we don't create data
        empty_data = pd.DataFrame()
        
        if self.clone_data_near_close and is_near_market_close and not historical_bars.empty:
            # Near market close - clone the last bar to ensure strategy can run
            logger.info(f"Near market close ({current_time_est.time()}) with no new data for {ticker}. Cloning last bar.")
            
            # Calculate the current interval start time
            current_bar_start = self._get_interval_start(current_time, interval)
            
            # Clone the last historical bar
            if not historical_bars.empty:
                last_bar = historical_bars.iloc[-1].copy()
                last_bar.name = current_bar_start  # update index
                
                # Create a DataFrame with the cloned bar
                cloned_data = pd.DataFrame([last_bar])
                
                # Reset polling interval to normal
                return effective_polling_interval.total_seconds(), cloned_data
            
        elif rapid_poll_count >= 2:
            # If we've tried rapid polling 2 times, go back to normal interval
            logger.debug(f"No data after {rapid_poll_count} rapid polls for {ticker}. Returning to normal polling interval.")
            return effective_polling_interval.total_seconds(), empty_data
            
        else:
            # Continue with rapid polling
            logger.debug(f"No data for {ticker}. Rapid polling attempt {rapid_poll_count}/6.")
            return 10, empty_data 
        
    def ensure_datetime_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """Ensure the DataFrame has a DatetimeIndex."""
        if not isinstance(df.index, pd.DatetimeIndex):
            if len(df.index) > 0:
                # Create a new list to store the converted datetimes
                converted_index = []
                
                for dt in df.index:
                    # Check if datetime is timezone-aware
                    if hasattr(dt, 'tzinfo') and dt.tzinfo is not None:
                        # Convert to EST and then remove timezone info
                        converted_index.append(dt.astimezone(self.est_tz).replace(tzinfo=None))
                    else:
                        # For non-timezone aware dates, just keep as is
                        converted_index.append(dt)
                
                # Create a new DatetimeIndex with the converted values
                df.index = pd.DatetimeIndex(converted_index)
            else:
                # Empty DataFrame - just create an empty DatetimeIndex
                df.index = pd.DatetimeIndex([])
        return df