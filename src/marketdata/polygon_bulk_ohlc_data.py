import pandas as pd
from datetime import datetime
import pytz
from polygon import RESTClient
from polygon.rest.models import TickerSnapshot, LastTrade
from typing import Optional, List
import asyncio
import logging

logger = logging.getLogger(__name__)

class PolygonBulkOHLCData:
    """
    A class to interact with Polygon.io's Full Market Snapshot API.
    Retrieves current day OHLC data for stocks using the official Polygon client.
    """
    
    def __init__(self, api_key: str):
        """
        Initialize the PolygonBulkOHLCData class with API key.
        
        Parameters:
        -----------
        api_key : str
            Your Polygon.io API key
        """
        self.client = RESTClient(api_key=api_key)
    
    async def get_current_day_ohlc(self, tickers: Optional[List[str]] = None, include_otc: bool = False) -> pd.DataFrame:
        """
        Retrieve current day OHLC data for stocks.
        
        Parameters:
        -----------
        tickers : Optional[List[str]], default None
            List of ticker symbols to retrieve data for. If None, retrieves data for all tickers.
        include_otc : bool, default False
            Whether to include OTC securities in the response.
            
        Returns:
        --------
        pandas.DataFrame
            DataFrame with columns: date, open, high, low, close, volume, symbol
            with a standard integer index
        """
        try:
            # Get the snapshot for all stocks or specific tickers
            if tickers:
                # Convert list to comma-separated string for the API
                tickers_str = ",".join(tickers)
                # Run in a thread pool to avoid blocking the event loop
                snapshot = await asyncio.to_thread(self.client.get_snapshot_all, "stocks", tickers=tickers_str, include_otc=include_otc)
            else:
                snapshot = await asyncio.to_thread(self.client.get_snapshot_all, "stocks", include_otc=include_otc)
            
            # Check if snapshot is valid
            if not snapshot:
                print("No data returned from Polygon API")
                return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume', 'symbol'])
            
            # Process the snapshot data
            processed_data = []
            eastern = pytz.timezone('US/Eastern')
            current_date = datetime.now(eastern).date()
            
            for item in snapshot:
                # Verify this is a TickerSnapshot
                if not isinstance(item, TickerSnapshot):
                    continue
                
                last_trade = item.last_trade
                day = item.day
                minute = item.min
                
                if last_trade :
                    processed_data.append({
                        'date': current_date,
                        'open': last_trade.price,
                        'high': last_trade.price,
                        'low': last_trade.price,
                        'close': last_trade.price,
                        'volume': last_trade.size,
                        'day_volume': day.volume if day else None,
                        'day_open': day.open if day else None,
                        'day_high': day.high if day else None,
                        'day_low': day.low if day else None,
                        'day_close': day.close if day else None,
                        'minute_accumulated_volume': minute.accumulated_volume if minute else None,
                        'minute_high': minute.high if minute else None,
                        'minute_low' : minute.low if minute else None, 
                        'minute_open' : minute.open if minute else None,
                        'symbol': item.ticker
                    })
            
            # Create DataFrame from processed data
            result_df = pd.DataFrame(processed_data)
            
            if result_df.empty:
                return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume', 'symbol'])
            
            # Convert date column to datetime
            result_df['date'] = pd.to_datetime(result_df['date'])
            
            # Reset index to get a standard integer index
            result_df.reset_index(drop=True, inplace=True)
            
            return result_df
            
        except Exception as e:
            logger.error("An error occurred {e}", e)
            return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume', 'symbol'])


if __name__ == "__main__":
    import os
    from dotenv import load_dotenv
    
    # Load environment variables (assuming API key is stored there)
    load_dotenv()
    
    api_key = os.getenv("POLYGON_API_KEY")
    if not api_key:
        print("POLYGON_API_KEY environment variable not set.")
        exit(1)
    
    polygon_ohlc = PolygonBulkOHLCData(api_key)
    
    # Create and run an async function to get data
    async def main():
        # Get data for all tickers
        current_day_df = await polygon_ohlc.get_current_day_ohlc()
        print(f"Retrieved data for {len(current_day_df)} tickers")
        print(current_day_df.head())
        
        # Alternatively, get data for specific tickers
        specific_tickers = ['AAPL', 'MSFT', 'GOOG', 'AMZN', 'META']
        specific_df = await polygon_ohlc.get_current_day_ohlc(tickers=specific_tickers)
        print(f"\nRetrieved data for specific tickers:")
        print(specific_df)
    
    # Run the async function
    asyncio.run(main())