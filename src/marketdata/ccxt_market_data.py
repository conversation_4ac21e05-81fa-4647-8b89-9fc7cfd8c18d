from abc import ABC, abstractmethod
import time
from datetime import datetime
from marketdata.imarketdata import IMarketData
import ccxt, time, pandas as pd
from typing import Optional, Dict, Tuple, List
import os
import json

class TickerNotFound(Exception): ...

PREFERRED_EXCHANGES = [
    'binanceus',         #  'binance', not accessible from US
    'coinbase', 'kraken',
    'bitstamp', 'kucoin',
    'okx', 'bybit',
    'bitfinex', 'gemini',
]

CATALOG_FILE = os.path.expanduser('~/.ccxt_pairs_catalog.json')
ALIAS: Dict[str, str] = {
    'MIOTA': 'IOTA',
    'BTS'  : 'BITSHARES',
}

def _build_catalog(quotes=('USD','USDT','USDC','BUSD')) -> Dict[str, List[Tuple[str,str]]]:
    """Return {TICKER: [(exchange_id, pair), …]} across *all* ccxt exchanges."""
    catalog: Dict[str, List[Tuple[str,str]]] = {}
    for ex_id in ccxt.exchanges:
        try:
            ex = getattr(ccxt, ex_id)({'enableRateLimit': True})
            if not ex.has.get('fetchOHLCV'):
                continue
            ex.load_markets()
        except Exception:
            continue                # dead / requires credentials / etc.

        for sym, info in ex.markets.items():
            if info.get('active', True) is False:
                continue
            try:
                base, quote = sym.split('/')
            except ValueError:
                continue
            if quote.upper() not in quotes:
                continue
            catalog.setdefault(base.upper(), []).append((ex_id, sym))

    return catalog

def _load_or_make_catalog() -> Dict[str, List[Tuple[str,str]]]:
    if os.path.exists(CATALOG_FILE):
        with open(CATALOG_FILE) as f:
            return json.load(f)
    cat = _build_catalog()
    with open(CATALOG_FILE, 'w') as f:
        json.dump(cat, f)
    return cat

CATALOG = _load_or_make_catalog()  

class CcxtMarketData(IMarketData):
    """
    Search-across-exchanges market-data provider.

    Parameters
    ----------
    exchanges : Optional[List[str]]
        Ordered list of ccxt exchange IDs to prioritize (e.g. ['binanceus','binance','kucoin']).
        If None, all exchanges are tried in catalog order.
    quotes    : List[str]
        Quote currencies to try for each ticker (order matters).
    """

    INTERVAL_MAP = {60:'1m', 300:'5m', 900:'15m', 3600:'1h', 86400:'1d'}

    def __init__(self,
                 exchanges: Optional[List[str]] = PREFERRED_EXCHANGES,
                 quotes: Tuple[str, ...] = ('USD','USDT','USDC','BUSD')):
        self.quotes        = tuple(q.upper() for q in quotes)
        self.exchanges     = [e.lower() for e in exchanges] if exchanges else []
        self._ex_objs: Dict[str, ccxt.Exchange] = {}

    def gather_historical_data(
        self,
        ticker: str,
        start_dt: datetime,
        end_dt: Optional[datetime] = None,
        interval: int = 60
    ) -> pd.DataFrame:

        base = ALIAS.get(ticker.upper(), ticker.upper())
        if base not in CATALOG:
            raise TickerNotFound(f"{ticker} not listed against {self.quotes}")

        # Filter by allowed quote currencies
        pairs = [p for p in CATALOG[base]
                 if p[1].split('/')[1].upper() in self.quotes]
        if not pairs:
            raise TickerNotFound(f"{ticker} exists, but not with {self.quotes}")

        # If user provided preferred exchanges, sort pairs accordingly
        if self.exchanges:
            def _pref_key(item):
                ex_id = item[0].lower()
                try:
                    return (0, self.exchanges.index(ex_id))
                except ValueError:
                    return (1, 0)
            pairs.sort(key=_pref_key)

        tf = self._timeframe(interval)
        since = int(start_dt.timestamp() * 1000)

        # Attempt to fetch from each exchange in order
        for ex_id, pair in pairs:
            ex = self._get_exchange(ex_id)
            try:
                df = self._fetch_ohlcv_df(ex, pair, tf, since)
                if not df.empty:
                    if end_dt is not None:
                        df = df.loc[:pd.to_datetime(end_dt, utc=True)]
                    print(f"✅ {ticker} via {ex_id} ({pair})")
                    return df
            except Exception as e:
                print(f"   ⚠️ {ex_id} failed on {pair}: {e}")
                continue

        raise TickerNotFound(f"All exchanges failed for {ticker}")

    # ---------- internals ---------- #

    def _timeframe(self, secs: int) -> str:
        try:
            return self.INTERVAL_MAP[secs]
        except KeyError:
            raise ValueError(
                f"Unsupported interval {secs}s – choose one of {list(self.INTERVAL_MAP)}"
            )

    def _get_exchange(self, ex_id: str) -> ccxt.Exchange:
        if ex_id not in self._ex_objs:
            ex = getattr(ccxt, ex_id)({'enableRateLimit': True})
            ex.load_markets()
            self._ex_objs[ex_id] = ex
        return self._ex_objs[ex_id]

    def _fetch_ohlcv_df(
        self,
        ex: ccxt.Exchange,
        symbol: str,
        tf: str,
        since: int,
        limit: int = 1000
    ) -> pd.DataFrame:
        out, cursor = [], since
        while True:
            ohlcv = ex.fetch_ohlcv(symbol, tf, cursor, limit)
            if not ohlcv:
                break
            out.extend(ohlcv)
            cursor = ohlcv[-1][0] + 1
            time.sleep(ex.rateLimit / 1000)
        df = pd.DataFrame(out, columns=['ts','open','high','low','close','volume'])
        df['datetime'] = pd.to_datetime(df['ts'], unit='ms', utc=True)
        return df.set_index('datetime').drop(columns='ts')

# —— Usage example ——
if __name__ == '__main__':
    from datetime import datetime

    md = CcxtMarketData()
    start = datetime(2023, 1, 1)
    end   = datetime(2023, 1, 10)

    df = md.gather_historical_data('BTC', start, end, interval=86400)
    print(df.shape)   # e.g. (10, 5)
    print(df.head())
