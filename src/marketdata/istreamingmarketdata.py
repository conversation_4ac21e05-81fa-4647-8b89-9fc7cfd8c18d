from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Async<PERSON>enerator, Optional, Tuple

import pandas as pd

from marketdata.imarketdata import IMarketData


class IStreamingMarketData(ABC):
    """
    Interface for streaming market data services.
    Extends the IMarketData interface to add real-time streaming capabilities.
    """

    @abstractmethod
    async def subscribe_to_bars(
        self,
        ticker: str,
        interval: timedelta,
        start_time: datetime,
        lookback_window: timedelta,
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """
        Subscribe to market data bars for a specific ticker.
        
        Args:
            ticker: The ticker symbol to subscribe to
            interval: The time interval for bars
            start_time: timestamp indicating when the ticker was discovered
            lookback_window: window of historical data to include

        Returns:
            An async generator that yields tuples of (historical_bars, latest_bar)
        """
        pass
