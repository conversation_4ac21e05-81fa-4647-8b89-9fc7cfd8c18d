from marketdata.imarketdata import IMarketData
from datetime import datetime
import pandas as pd
from portwine.loaders.base import MarketDataLoader
from typing import Optional

class MarketDataLoaderAdapter(MarketDataLoader):
    """
    An adapter that makes an IMarketData source compatible with MarketDataLoader.

    It pre-loads data for a specified date range using the IMarketData source
    the first time a ticker is requested and then caches it.
    """
    SOURCE_IDENTIFIER = None

    def __init__(self,
                 market_data_source: IMarketData,
                 start_date: datetime,
                 end_date: datetime,
                 interval: int = 86400,
                 source_identifier: Optional[str] = None):
        """
        Parameters
        ----------
        market_data_source : IMarketData
            An instance of a class implementing the IMarketData interface.
        start_date : datetime
            The start date for the data range to pre-load.
        end_date : datetime
            The end date for the data range to pre-load.
        interval : int, optional
            The data interval in seconds to request from the source, by default 86400 (daily).
            Ensure this matches the structure expected by MarketDataLoader (e.g., daily).
        """
        
        if not isinstance(market_data_source, IMarketData):
             raise TypeError("market_data_source must be an instance of IMarketData")
        if not isinstance(start_date, datetime) or not isinstance(end_date, datetime):
             raise TypeError("start_date and end_date must be datetime objects")
             
        self.market_data_source = market_data_source
        self.start_date = start_date
        self.end_date = end_date
        self.interval = interval
        self.SOURCE_IDENTIFIER = source_identifier
        super().__init__() # Initialize the base class cache etc.

    def load_ticker(self, ticker: str) -> pd.DataFrame | None:
        """
        Loads data for the given ticker using the underlying IMarketData source
        for the pre-configured date range.

        Returns None if data retrieval fails or returns an empty DataFrame.
        Ensures the DataFrame has a datetime index and the required columns.
        """
        print(f"Adapter: Loading {ticker} from {self.start_date} to {self.end_date} via IMarketData source...")
        try:
            df = self.market_data_source.gather_historical_data(
                ticker=ticker,
                start_dt=self.start_date,
                end_dt=self.end_date,
                interval=self.interval
            )

            # --- Validation and Formatting ---
            if df is None or df.empty:
                print(f"Warning: No data returned for {ticker} in the specified range.")
                return None

            # Ensure index is datetime
            if not isinstance(df.index, pd.DatetimeIndex):
                 # Attempt conversion if a likely date column exists
                 if 'date' in df.columns:
                      df['date'] = pd.to_datetime(df['date'])
                      df = df.set_index('date')
                 elif 'timestamp' in df.columns:
                      df['timestamp'] = pd.to_datetime(df['timestamp'])
                      df = df.set_index('timestamp')
                 else:
                      print(f"Warning: DataFrame for {ticker} does not have a DatetimeIndex and no 'date' or 'timestamp' column found.")
                      return None # Cannot proceed without a time index

            # Ensure required columns exist (case-insensitive check might be robust)
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"Warning: DataFrame for {ticker} is missing required columns: {missing_cols}")
                # Decide how to handle: return None, or return df with missing columns?
                # Returning None is safer for MarketDataLoader expectations.
                return None 

            # Ensure correct data types (optional but good practice)
            for col in required_cols:
                 if col in df.columns: # Check again in case some were missing
                      # Attempt conversion to float, handling potential errors
                      df[col] = pd.to_numeric(df[col], errors='coerce') 
                      
            # Remove rows with NaN in essential columns introduced by coercion
            df.dropna(subset=required_cols, inplace=True)
            
            if df.empty:
                 print(f"Warning: DataFrame for {ticker} became empty after data type conversion/NaN removal.")
                 return None

            # Select only required columns and ensure index is sorted
            df = df[required_cols]
            df.sort_index(inplace=True)

            print(f"Adapter: Successfully loaded and formatted {ticker}.")
            return df

        except Exception as e:
            print(f"Error loading data for {ticker} via IMarketData source: {e}")
            return None