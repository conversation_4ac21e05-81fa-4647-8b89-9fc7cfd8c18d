from marketdata.imarketdata import IMarketData
import pandas as pd
import yfinance as yf
import os
from datetime import datetime
from typing import Optional
import pathlib
import traceback
from curl_cffi import requests

class YFinanceMarketData(IMarketData):
    """
    Implementation of IMarketData interface for yfinance data source with robust caching.
    """

    def __init__(self, cache_dir: str = "yfinance_cache", enable_cache: bool = True):
        """
        Initialize the YFinance market data source.

        Parameters
        ----------
        cache_dir : str
            Directory where to store cached CSV files
        enable_cache : bool
            Whether to enable caching or not
        """
        self.cache_dir = cache_dir
        self.enable_cache = enable_cache
        self.session = requests.Session(impersonate="chrome")
        # Ensure cache directory exists if caching is enabled
        if self.enable_cache:
            pathlib.Path(cache_dir).mkdir(parents=True, exist_ok=True)
        print(f"YFinanceMarketData initialized. Cache directory: {os.path.abspath(self.cache_dir)}, caching enabled: {self.enable_cache}")

    def _get_cache_path(self, ticker: str, start_dt: datetime, end_dt: datetime, interval: int) -> str:
        """Generate a cache filename based on the query parameters"""
        # Use interval string in filename for clarity
        interval_str = self._get_yfinance_interval_str(interval)
        filename = f"{ticker}_{start_dt.strftime('%Y%m%d')}_{end_dt.strftime('%Y%m%d')}_{interval_str}.csv"
        return os.path.join(self.cache_dir, filename)

    def _load_from_cache(self, cache_path: str) -> Optional[pd.DataFrame]:
            """Attempt to load data from cache, ensuring DatetimeIndex."""
            if os.path.exists(cache_path):
                try:
                    # Load with basic date parsing
                    df = pd.read_csv(cache_path, index_col=0, parse_dates=True)

                    if not df.empty:
                        # --- Robustness Check ---
                        # 1. Verify the index is DatetimeIndex
                        if not isinstance(df.index, pd.DatetimeIndex):
                            print(f"Warning: Index loaded from {cache_path} was not DatetimeIndex type ({type(df.index)}). Attempting conversion.")
                            # Try converting the loaded index column (which is now the index)
                            try:
                                # *** KEY CHANGE HERE: Add utc=True ***
                                # This parses tz-aware strings and converts them to UTC DatetimeIndex
                                df.index = pd.to_datetime(df.index, utc=True)

                                # Check again
                                if not isinstance(df.index, pd.DatetimeIndex):
                                    raise ValueError("Index conversion to DatetimeIndex failed even with utc=True.")
                                print(f"Successfully converted index for {cache_path} to DatetimeIndex (UTC).")

                            except Exception as conv_err:
                                print(f"Error converting index for {cache_path}: {conv_err}. Cache load failed.")
                                # Print traceback for the conversion error specifically
                                # traceback.print_exc() # Uncomment for detailed debug if needed
                                return None # Failed to make it a DatetimeIndex

                        # 2. Restore/Check the index name (yfinance usually uses 'Date' or 'Datetime')
                        # If loaded as UTC, the concept of 'daily' based on midnight might be shifted
                        # Safer to just ensure it has *a* name. yfinance uses 'Date' or 'Datetime'.
                        # We can try to be smart or just default.
                        if df.index.name != 'Date' and df.index.name != 'Datetime':
                            original_name = df.index.name
                            # Check if original index was likely intraday based on filename? Less reliable.
                            # Defaulting based on the UTC time might be misleading.
                            # Let's check the interval from the filename if possible, or just default to 'Timestamp'
                            try:
                                parts = os.path.basename(cache_path).split('_')
                                interval_str = parts[-1].replace('.csv', '')
                                is_intraday = 'm' in interval_str or 'h' in interval_str
                            except:
                                is_intraday = False # Default assumption

                            df.index.name = 'Datetime' if is_intraday else 'Date'
                            # print(f"Restored index name from '{original_name}' to '{df.index.name}' for {cache_path}")

                        # 3. Timezone Handling:
                        # The index is now timezone-aware UTC (datetime64[ns, UTC])
                        # Decide if you want to keep it UTC or convert it (e.g., to naive or local)
                        # Keeping it UTC is often safest unless downstream code *requires* naive.
                        # Example: Convert to timezone naive (loses info)
                        # df.index = df.index.tz_convert(None)
                        # print(f"Converted cached index to timezone naive for {cache_path}")
                        # Example: Convert back to estimated original timezone (e.g., New York for US equities)
                        # try:
                        #    df.index = df.index.tz_convert('America/New_York')
                        #    print(f"Converted cached index from UTC to America/New_York for {cache_path}")
                        # except Exception as tz_conv_err:
                        #    print(f"Warning: Could not convert UTC index to America/New_York: {tz_conv_err}")

                        # For now, let's return it as UTC as loaded.
                        print(f"Loaded data from cache: {cache_path}. Index type: {type(df.index)}, TZ: {df.index.tz}")
                        return df
                    else:
                        print(f"Cache file {cache_path} is empty.")
                        # Decide if you want to return empty DataFrame or None
                        # Returning None triggers a fresh download, which is often desired.
                        return None
                except Exception as e:
                    print(f"Error loading from cache {cache_path}: {e}")
                    # traceback.print_exc() # Uncomment for detailed debug if needed
            return None

    def _save_to_cache(self, df: pd.DataFrame, cache_path: str) -> None:
        """Save DataFrame to cache"""
        if df.empty:
            print(f"Skipping cache save for empty DataFrame: {cache_path}")
            return
        try:
            # Ensure index has a name before saving, otherwise read_csv might struggle
            if df.index.name is None:
                print("Warning: DataFrame index name is None. Setting to 'Timestamp' before saving.")
                df.index.name = 'Timestamp' # Assign a default name

            df.to_csv(cache_path)
            print(f"Saved data to cache: {cache_path}")
        except Exception as e:
            print(f"Error saving to cache {cache_path}: {e}")
            traceback.print_exc()


    def _get_yfinance_interval_str(self, interval: int) -> str:
         """Converts interval in seconds to yfinance string format."""
         if interval >= 86400: # Daily or higher
             # Map common intervals, default to 1d
             interval_map = {
                 86400: '1d',
                 432000: '5d', # 5 days
                 604800: '1wk', # 1 week
                 2592000: '1mo', # Approx 1 month
                 7776000: '3mo'  # Approx 3 months
             }
             # Find closest match or default
             closest_interval = min(interval_map.keys(), key=lambda k: abs(k - interval))
             if abs(closest_interval - interval) > 86400: # If it's too far off, default to daily
                 print(f"Warning: Interval {interval}s maps far from standard daily intervals, using '1d'.")
                 return '1d'
             else:
                 return interval_map[closest_interval]
         elif interval >= 3600: # Hourly
             hours = interval // 3600
             if hours * 3600 != interval:
                 print(f"Warning: Interval {interval}s is not an exact number of hours, using '1h'.")
             return '1h' # yfinance mainly supports 1h for hourly
         elif interval >= 60: # Minutely
             minutes = interval // 60
             allowed_minute_intervals = {1, 2, 5, 15, 30, 60, 90}
             if minutes * 60 != interval or minutes not in allowed_minute_intervals:
                  closest_minute = min(allowed_minute_intervals, key=lambda m: abs(m-minutes))
                  print(f"Warning: Interval {interval}s ({minutes}m) not directly supported by yfinance or not exact minutes. Using closest: '{closest_minute}m'.")
                  minutes = closest_minute

             # Intraday data limits: Max 7 days for 1m, max 60 days for others up to 1h
             # This logic should ideally be checked *before* the call based on start/end date
             return f'{minutes}m'
         else:
             print(f"Warning: Interval {interval}s is less than 60s, not supported by yfinance. Defaulting to '1m'.")
             return '1m'


    def gather_historical_data(self, ticker: str, start_dt: datetime, end_dt: datetime, interval: int = 86400) -> pd.DataFrame:
        """
        Gather historical data for a ticker from start_dt to end_dt.

        Parameters
        ----------
        ticker : str
            The ticker symbol
        start_dt : datetime
            Start date/time (inclusive)
        end_dt : datetime
            End date/time (exclusive)
        interval : int
            Interval in seconds, defaults to 86400 (daily)

        Returns
        -------
        pd.DataFrame
            DataFrame with OHLCV data and DatetimeIndex named 'Date' or 'Datetime'.
            Returns an empty DataFrame on failure.
        """
        # Convert interval seconds to yfinance string format
        interval_str = self._get_yfinance_interval_str(interval)

        cache_path = self._get_cache_path(ticker, start_dt, end_dt, interval) # Use original interval for path

        # Try to load from cache first
        if self.enable_cache:
            cached_df = self._load_from_cache(cache_path)
            if cached_df is not None:
                # Ensure the loaded data matches the requested columns/index format
                # (The robustness checks are now inside _load_from_cache)
                # Optional: Add further validation here if needed
                return cached_df

        print(f"Cache miss or invalid cache for {ticker} ({start_dt} - {end_dt}, {interval_str}). Fetching from yfinance...")

        try:
            ticker_obj = yf.Ticker(ticker, session=self.session)

            # Fetch data using yfinance
            # Note: yfinance 'end' is exclusive, 'start' is inclusive.
            df = ticker_obj.history(
                start=start_dt,
                end=end_dt,
                interval=interval_str,
                auto_adjust=True, # Set to False to get Adj Close separately if needed
                actions=False,
            )

            # Post-fetch processing
            if df.empty:
                print(f"Warning: No data returned from yfinance for {ticker} with specified parameters.")
                # Create empty df with expected index type/name for consistency downstream?
                # Or just cache the empty result to avoid refetching?
                # Let's cache the fact that it was empty.
                if self.enable_cache:
                    self._save_to_cache(df, cache_path)
                return pd.DataFrame() # Return empty

            # --- Data Cleaning & Standardization ---
            # 1. Handle potential multi-level columns if actions=True and interval is weekly/monthly
            if isinstance(df.columns, pd.MultiIndex):
                print("Warning: MultiIndex columns detected, attempting to flatten.")
                # Example flattening strategy (may need adjustment based on actual structure)
                df.columns = ['_'.join(col).strip() if isinstance(col, tuple) else col for col in df.columns.values]


            # 2. Standardize column names to lowercase
            df.columns = [str(col).lower() for col in df.columns] # Ensure col is string first

            # 3. Rename 'adj close' if present (auto_adjust=False might add it)
            if 'adj close' in df.columns:
                df = df.rename(columns={'adj close': 'adjusted_close'}) # Use a distinct name

            # 4. Ensure required columns exist (optional, depends on downstream needs)
            # required_cols = ['open', 'high', 'low', 'close', 'volume']
            # for col in required_cols:
            #     if col not in df.columns:
            #         print(f"Warning: Column '{col}' missing in data for {ticker}.")
            #         df[col] = pd.NA # Or 0 or np.nan

            # 5. Verify Index Type and Name from yfinance (should be correct, but good practice)
            if not isinstance(df.index, pd.DatetimeIndex):
                 print(f"CRITICAL WARNING: yfinance did not return DatetimeIndex for {ticker}. Index type: {type(df.index)}. Attempting conversion.")
                 try:
                     df.index = pd.to_datetime(df.index)
                 except Exception as e:
                     print(f"Failed to convert yfinance index to DatetimeIndex: {e}")
                     return pd.DataFrame() # Return empty if index is unusable

            # yfinance uses 'Date' for daily/longer, 'Datetime' for intraday. Standardize name.
            if df.index.name != 'Date' and df.index.name != 'Datetime':
                 df.index.name = 'Date' if interval_str in ['1d', '5d', '1wk', '1mo', '3mo'] else 'Datetime'


            # --- Caching ---
            if self.enable_cache:
                self._save_to_cache(df, cache_path)

            print(f"Successfully fetched and processed data for {ticker}. Shape: {df.shape}. Index type: {type(df.index)}")
            return df

        except Exception as e:
            print(f"Error gathering data for {ticker}: {e}")
            traceback.print_exc() # Print full traceback for debugging
            return pd.DataFrame() # Return empty DataFrame on error
