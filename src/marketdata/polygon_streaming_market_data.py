import asyncio
import json
import logging
import pandas as pd
import websockets
from datetime import datetime, timedelta
from typing import AsyncGenerator, Dict, List, Optional, Tuple, Any
from zoneinfo import ZoneInfo

from marketdata.istreamingmarketdata import IStreamingMarketData
from marketdata.imarketdata import IMarketData
from tools.clock import Clock
from tools.clock import is_trading_time, is_regular_hours

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class PolygonStreamingMarketData(IStreamingMarketData):
    def __init__(
        self,
        market_data_provider: IMarketData,
        clock: Clock,
        api_key: str,
        websocket_url: str = "ws://localhost:8765",
        trade_session: str = "rth",
    ):
        self.market_data_provider = market_data_provider
        self.clock = clock
        self.api_key = api_key
        self.websocket_url = websocket_url
        self.active_streams = {}  # Store active status and tasks
        self.est_tz = ZoneInfo("America/New_York")

        if trade_session.lower() not in {"rth", "full"}:
            raise ValueError(
                f"Invalid trade_session: {trade_session}. Must be 'rth' or 'full'."
            )
        self.trade_session = trade_session
        self.receive_timeout = (
            60  # Seconds to wait for a message before checking connection
        )

    def _in_allowed_session(self, dt: datetime) -> bool:
        """Check if the provided datetime is within the allowed trading session."""
        if self.trade_session.lower() == "rth":
            return is_regular_hours(dt)
        else:
            return is_trading_time(dt)

    async def subscribe_to_bars(
        self,
        ticker: str,
        interval: timedelta,
        start_time: datetime,
        lookback_window: timedelta,
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """
        Subscribe to real-time bar updates, decoupling message reception and processing.
        Now supports:
            • 1-second bars  → Polygon channel prefix  "A"
            • Whole-minute bars (60 s, 120 s, …) → prefix "AM"
        """
        interval_seconds = int(interval.total_seconds())
        if interval_seconds == 1:
            channel_prefix = "A"          # per-second stream
        elif interval_seconds % 60 == 0:
            channel_prefix = "AM"         # per-minute stream (as before)
        else:
            raise RuntimeError(
                f"Unsupported interval {interval}. Only 1-sec or whole-minute multiples supported."
            )
        subscription_id = f"{ticker}_{interval_seconds}"
        if subscription_id in self.active_streams:
            logger.warning(
                f"Subscription {subscription_id} already active. Returning existing stream is not implemented. Starting new."
            )
            # Or handle reusing streams if needed

        message_queue = asyncio.Queue()  # Queue for decoupling
        self.active_streams[subscription_id] = {"active": True, "tasks": []}
        websocket_connection = None  # To manage the connection state

        try:
            # --- 1. Fetch Historical Data (potentially blocking, but done before streaming starts) ---
            historical_bars = pd.DataFrame()
            current_time_est = self.clock.now()

            if lookback_window:
                start_dt = current_time_est - lookback_window
                end_dt = current_time_est  # Fetch up to the current moment
                # Ensure the provider returns timezone-aware (EST) data or convert it
                historical_bars = (
                    await self.market_data_provider.gather_historical_data_async(
                        ticker, start_dt, end_dt, interval_seconds
                    )
                )
                historical_bars = historical_bars.sort_index()
                historical_bars = self._ensure_est_index(historical_bars).sort_index()

                # Initial yield if historical data contains a bar overlapping the current time
                if not historical_bars.empty:
                    current_bar_start = self._get_interval_start(
                        current_time_est, interval
                    )
                    completed_bars = historical_bars[
                        historical_bars.index < current_bar_start
                    ]
                    current_bars_in_hist = historical_bars[
                        historical_bars.index >= current_bar_start
                    ]

                    if not current_bars_in_hist.empty:
                        logger.info(
                            f"Historical data contains current bar for {ticker}. Yielding initial state."
                        )
                        # Use the last entry from historical data that falls into the current interval
                        initial_current_bar_df = pd.DataFrame(
                            [current_bars_in_hist.iloc[-1]]
                        )
                        yield completed_bars, initial_current_bar_df

            # --- 2. Establish WebSocket Connection and Start Receiver Task ---
            logger.info(f"Connecting to Polygon WebSocket for {subscription_id}...")
            websocket_connection = await websockets.connect(self.websocket_url)
            await self._authenticate_and_subscribe(
                websocket_connection, ticker, channel_prefix
            )

            receiver_task = asyncio.create_task(
                self._websocket_receiver(
                    websocket_connection, message_queue, subscription_id
                ),
                name=f"receiver_{subscription_id}",
            )
            self.active_streams[subscription_id]["tasks"].append(receiver_task)
            logger.info(f"WebSocket receiver task started for {subscription_id}.")

            # --- 3. Start Message Processor Task (which is the generator) ---
            logger.info(f"Starting message processor for {subscription_id}...")
            async for hist_bars, current_bar in self._message_processor(
                message_queue, ticker, interval, historical_bars, subscription_id
            ):
                yield hist_bars, current_bar  # Yield processed data

        except websockets.exceptions.ConnectionClosedError as e:
            logger.error(
                f"WebSocket connection closed unexpectedly for {subscription_id}: {e}"
            )
            # Consider implementing reconnect logic here or in the receiver
        except Exception as e:
            logger.error(
                f"Error during subscription setup or processing for {subscription_id}: {e}",
                exc_info=True,
            )
            # Consider re-raising specific errors if needed by the caller
        finally:
            # --- 4. Cleanup ---
            logger.info(f"Cleaning up subscription {subscription_id}...")
            if subscription_id in self.active_streams:
                self.active_streams[subscription_id]["active"] = False
                tasks = self.active_streams[subscription_id]["tasks"]

                # Cancel running tasks
                for task in tasks:
                    if task and not task.done():
                        task.cancel()
                        try:
                            await task  # Allow cancellation to propagate
                        except asyncio.CancelledError:
                            logger.info(f"Task {task.get_name()} cancelled.")
                        except Exception as e_cancel:
                            logger.error(
                                f"Error during task cancellation for {task.get_name()}: {e_cancel}"
                            )

                # Close WebSocket connection if open
                if websocket_connection:
                    logger.info(f"Closing WebSocket connection for {subscription_id}.")
                    # Optional: Send unsubscribe message if API supports/requires it
                    # await self._unsubscribe(websocket_connection, ticker)
                    await websocket_connection.close()

                del self.active_streams[subscription_id]  # Remove from active streams
            logger.info(f"Cleanup complete for {subscription_id}.")

    async def _authenticate_and_subscribe(
        self,
        websocket: websockets.WebSocketClientProtocol,
        ticker: str,
        channel_prefix: str,
    ):
        """Handles authentication and subscription logic."""
        # Wait for initial connection message
        conn_response = await websocket.recv()
        logger.debug(f"Connection response: {conn_response}")
        if not self._is_connection_successful(json.loads(conn_response)):
            raise ConnectionError(f"Polygon connection failed: {conn_response}")
        logger.info("Polygon WebSocket connected successfully.")

        # Authenticate
        auth_request = {"action": "auth", "params": self.api_key}
        await websocket.send(json.dumps(auth_request))
        auth_response = await websocket.recv()
        logger.debug(f"Auth response: {auth_response}")
        if not self._is_auth_successful(json.loads(auth_response)):
            raise PermissionError(f"Polygon authentication failed: {auth_response}")
        logger.info("Polygon WebSocket authentication successful.")

        # Subscribe to Aggregates (“A” = per-second, “AM” = per-minute)
        # Using {prefix}.{ticker} is more efficient than wildcard.
        subscription_param = f"{channel_prefix}.{ticker}"
        subscribe_request = {"action": "subscribe", "params": subscription_param}
        await websocket.send(json.dumps(subscribe_request))
        logger.info(f"Sent subscription request for {subscription_param}")

    async def _websocket_receiver(
        self,
        websocket: websockets.WebSocketClientProtocol,
        queue: asyncio.Queue,
        subscription_id: str,
    ):
        """Task to receive messages from WebSocket and put them on the queue."""
        while self.active_streams.get(subscription_id, {}).get("active", False):
            try:
                message = await asyncio.wait_for(
                    websocket.recv(), timeout=self.receive_timeout
                )
                await queue.put(message)
                logger.debug(f"Receiver {subscription_id}: Queued message.")
            except asyncio.TimeoutError:
                logger.debug(
                    f"WebSocket receive timeout for {subscription_id}. Sending ping."
                )
                try:
                    await websocket.ping()
                except websockets.exceptions.ConnectionClosed:
                    logger.warning(
                        f"WebSocket connection closed for {subscription_id} during ping check."
                    )
                    break  # Exit loop if connection is closed
            except websockets.exceptions.ConnectionClosedOK:
                logger.info(
                    f"WebSocket connection closed normally for {subscription_id}."
                )
                break
            except websockets.exceptions.ConnectionClosedError as e:
                logger.error(
                    f"WebSocket connection closed with error for {subscription_id}: {e}"
                )
                # Potentially put an error signal on the queue or raise?
                break
            except Exception as e:
                logger.error(
                    f"Error receiving WebSocket message for {subscription_id}: {e}",
                    exc_info=True,
                )
                # Decide if we should break or try to continue
                await asyncio.sleep(1)  # Avoid tight loop on persistent error

        # Signal end to the processor
        await queue.put(None)
        logger.info(f"WebSocket receiver task finished for {subscription_id}.")

    async def _message_processor(
        self,
        queue: asyncio.Queue,
        ticker: str,  # Added ticker for logging clarity
        interval: timedelta,  # Interval needed for processing logic
        initial_historical_bars: pd.DataFrame,
        subscription_id: str,
    ) -> AsyncGenerator[Tuple[pd.DataFrame, pd.DataFrame], None]:
        """Task to process messages from the queue and yield results."""
        historical_bars = initial_historical_bars.copy()  # Work on a copy

        while self.active_streams.get(subscription_id, {}).get("active", False):
            # 1) Wait for at least one message
            first_raw = await queue.get()

            # 2) Drain everything that's already buffered
            batch = [first_raw]
            while True:
                try:
                    nxt = queue.get_nowait()
                    batch.append(nxt)
                except asyncio.QueueEmpty:
                    break

            # 3) Sentinel → clean shutdown
            if any(item is None for item in batch):
                for _ in batch:
                    queue.task_done()
                logger.info(
                    f"Processor {subscription_id}: Received None signal, ending."
                )
                break

            # 4) Process every raw payload
            bars: list[Dict[str, Any]] = []
            for raw in batch:
                bar = self._process_single_raw_message(raw, ticker, interval)
                if bar:
                    bars.append(bar)

            if not bars:
                for _ in batch:
                    queue.task_done()
                continue

            # 5) Append all but the last bar straight into history
            for bar in bars[:-1]:
                historical_bars = self._append_bar(historical_bars, bar)

            # 6) Append the *last* bar + build the synthetic current bar
            historical_bars, current_df = self._append_bar_and_build_synthetic(
                historical_bars, bars[-1], interval
            )

            # 7) Yield once for the whole drain
            yield historical_bars, current_df

            # 8) Mark every dequeued item as processed
            for _ in batch:
                queue.task_done()

        logger.info("Message processor task finished for %s.", subscription_id)

    def _process_single_raw_message(
        self,
        raw: str,
        ticker: str,
        interval: timedelta,
    ) -> Optional[Dict[str, Any]]:
        """
        Decode one raw WebSocket payload and return a *single* cleaned bar
        (or None if the payload is not an AM bar we care about).
        """
        try:
            data = json.loads(raw)
        except json.JSONDecodeError:
            logger.error("Bad JSON from WS: %s", raw)
            return None

        if not isinstance(data, list):
            return None
        # Warn if multiple messages received since only the first is processed
        if len(data) > 1:
            logger.error("Received %d messages in payload, only processing the first one", len(data))

        interval_seconds = int(interval.total_seconds())
        expected_ev = "A" if interval_seconds == 1 else "AM"

        for msg in data:
            if not (isinstance(msg, dict) and msg.get("ev") == expected_ev):
                continue
            bar = self._process_aggregate_bar(msg)
            if bar and self._in_allowed_session(bar["timestamp"]):
                return bar
        return None

    def _append_bar(
        self, historical_bars: pd.DataFrame, bar: Dict[str, Any]
    ) -> pd.DataFrame:
        """
        Add a completed bar to the historical DataFrame,
        keeping only the most-recent copy of any duplicate timestamp.
        """
        bar_df = pd.DataFrame([bar]).set_index("timestamp")
        combined = pd.concat([historical_bars, bar_df])
        combined = combined[~combined.index.duplicated(keep="last")].sort_index()
        return combined

    def _append_bar_and_build_synthetic(
        self,
        historical_bars: pd.DataFrame,
        bar: Dict[str, Any],
        interval: timedelta,
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        1. Append <bar> to historical_bars.
        2. Create a synthetic 'current' bar that opens the *next* interval.
        If that next interval is outside the allowed session, return an
        empty DataFrame instead.
        """
        historical_bars = self._append_bar(historical_bars, bar)

        next_start = bar["timestamp"] + interval  # interval is 1-min here
        if self._in_allowed_session(next_start):
            synthetic = {
                "timestamp": next_start,
                "open": bar["close"],
                "high": bar["close"],
                "low": bar["close"],
                "close": bar["close"],
                "volume": 0,
                "vwap": bar.get("vwap"),
                "trades": 0,
                "accumulated_volume": bar.get("accumulated_volume", 0),
            }
            current_df = pd.DataFrame([synthetic]).set_index("timestamp")
        else:
            current_df = pd.DataFrame(columns=historical_bars.columns).set_index(
                pd.DatetimeIndex([])
            )

        return historical_bars, current_df

    def _is_connection_successful(self, conn_result: Any) -> bool:
        """Check if Polygon connection was successful."""
        if isinstance(conn_result, list):
            for item in conn_result:
                if (
                    isinstance(item, dict)
                    and item.get("ev") == "status"
                    and item.get("status") == "connected"
                ):
                    return True
        return False

    def _is_auth_successful(self, auth_result: Any) -> bool:
        """Check if Polygon authentication was successful."""
        if isinstance(auth_result, list):
            for item in auth_result:
                if (
                    isinstance(item, dict)
                    and item.get("ev") == "status"
                    and item.get("status") == "auth_success"
                ):
                    return True
        return False

    def _process_aggregate_bar(
        self, message: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Process a minute aggregate message from Polygon into a bar dict."""
        try:
            # Polygon AM message timestamps 's' and 'e' are epoch milliseconds UTC
            # Convert start timestamp 's' to datetime, localized to EST
            timestamp_ms = message.get("s", message.get("t"))
            if timestamp_ms is None:
                return None  # Skip if essential data missing

            timestamp_utc = datetime.fromtimestamp(
                timestamp_ms / 1000, tz=ZoneInfo("UTC")
            )
            timestamp_est = timestamp_utc.astimezone(self.est_tz)

            # Ensure required fields are present
            if not all(k in message for k in ("o", "h", "l", "c", "v")):
                logger.warning(f"Missing OHLCV data in message: {message}")
                return None

            return {
                "timestamp": timestamp_est,  # Start time of the aggregate window (EST)
                "open": message["o"],
                "high": message["h"],
                "low": message["l"],
                "close": message["c"],
                "volume": int(message["v"]),  # Ensure volume is integer
                "vwap": message.get("vw"),  # Optional
                "trades": message.get(
                    "n", 0
                ),  # Number of trades in window (often field 'n')
                "accumulated_volume": message.get(
                    "av", 0
                ),  # Optional: Today's accumulated volume
            }
        except Exception as e:
            logger.error(
                f"Error processing raw aggregate bar: {message}, Error: {e}",
                exc_info=True,
            )
            return None

    def _get_interval_start(self, dt: datetime, interval: timedelta) -> datetime:
        """Calculate the start time of the interval containing the given datetime (in EST)."""
        # Ensure dt is timezone-aware in EST
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=self.est_tz)  # Assume EST if naive
        elif dt.tzinfo != self.est_tz:
            dt = dt.astimezone(self.est_tz)

        # Calculate interval start based on total seconds
        interval_seconds = int(interval.total_seconds())
        if interval_seconds <= 0:
            interval_seconds = 60  # Default to 1 minute if invalid

        # Use total seconds since midnight to find the start of the interval bin
        seconds_since_midnight = (
            dt - dt.replace(hour=0, minute=0, second=0, microsecond=0)
        ).total_seconds()
        interval_number = seconds_since_midnight // interval_seconds
        start_seconds = interval_number * interval_seconds

        # Reconstruct the start datetime
        start_dt = dt.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(
            seconds=start_seconds
        )
        return start_dt

    def _ensure_est_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Make df.index a tz-aware DatetimeIndex in America/New_York.
        Works whether the incoming index is strings, naïve datetimes or already tz-aware.
        """
        idx = pd.to_datetime(df.index, errors="coerce")  # strings → dt64
        if idx.tz is None:  # still naïve?
            idx = idx.tz_localize(self.est_tz)  # assume local
        else:  # tz-aware already
            idx = idx.tz_convert(self.est_tz)  # unify the zone
        df.index = idx
        return df
