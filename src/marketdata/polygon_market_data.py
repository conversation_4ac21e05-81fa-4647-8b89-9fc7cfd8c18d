import pandas as pd
import pytz
import logging
import time as time_module
import asyncio
from datetime import datetime, timedelta
from polygon import RESTClient
from typing import Optional
from .imarketdata import IMarketData, LastQuote

logging.getLogger("urllib3.connectionpool").setLevel(logging.ERROR)
logger = logging.getLogger(__name__)
logger.setLevel(logging.ERROR)

class PolygonMarketData(IMarketData):
    def __init__(self, client: RESTClient, max_concurrent_requests: int = 4, adjusted: bool = True):
        """
        Initialize PolygonMarketData with a RESTClient.
        
        :param client: The Polygon RESTClient.
        :param max_concurrent_requests: Maximum number of parallel requests (default: 4).
        :param adjusted: Whether to use adjusted data (default: True).
        """
        self.client = client
        self.adjusted = adjusted
        self.request_semaphore = asyncio.Semaphore(max_concurrent_requests)
        # Counter for tracking concurrent requests in synchronous context
        self._active_requests = 0
        self._lock = asyncio.Lock()  # Lock for synchronous context

    def _gather_historical_data(self, ticker: str, start_dt: datetime, end_dt: datetime, interval: int) -> pd.DataFrame:
        eastern = pytz.timezone('US/Eastern')
        utc = pytz.utc

        # Ensure the datetime objects are timezone aware (assume Eastern if naive)
        if start_dt.tzinfo is None:
            start_dt = eastern.localize(start_dt)
        if end_dt.tzinfo is None:
            end_dt = eastern.localize(end_dt)

        try:
            return self._fetch_data(ticker, start_dt, end_dt, utc, interval)
        except Exception as e:
            error_message = str(e)
            timeframe_error_msg = "Your plan doesn't include this data timeframe"
            if timeframe_error_msg in error_message:
                new_end_dt = (datetime.now(utc) - timedelta(minutes=15)).astimezone(eastern)
                logger.warning(f"{error_message}. Using new end datetime {new_end_dt} (15 minutes before current time).")
                try:
                    return self._fetch_data(ticker, start_dt, new_end_dt, utc, interval)
                except Exception as e2:
                    logger.error(f"Data timeframe error even after override for {ticker}: {str(e2)}")
                    return pd.DataFrame()
            else:
                logger.error(f"Error fetching historical data for {ticker}: {error_message}")
                return pd.DataFrame()

    def _acquire_semaphore(self):
        """Simulate semaphore acquisition in synchronous context"""
        # In synchronous code, we need to manually track and limit concurrent requests
        max_attempts = 10
        attempt = 0
        
        while attempt < max_attempts:
            if self._active_requests < self.request_semaphore._value:
                self._active_requests += 1
                return True
            # Wait a bit before checking again (simulating await)
            time_module.sleep(1)
            attempt += 1
        
        # If we can't acquire after max attempts, log and continue anyway
        logger.warning("Could not acquire semaphore after max attempts, proceeding anyway")
        return False

    def _release_semaphore(self):
        """Simulate semaphore release in synchronous context"""
        if self._active_requests > 0:
            self._active_requests -= 1

    def _fetch_data(self, ticker: str, start_dt: datetime, end_dt: datetime, utc, interval: int) -> pd.DataFrame:
        start_dt_utc = start_dt.astimezone(utc)
        end_dt_utc = end_dt.astimezone(utc)

        # Determine timespan and multiplier based on interval (in seconds)
        seconds_per_day = 24 * 3600
        if interval % seconds_per_day == 0:
            multiplier = interval // seconds_per_day
            timespan = "day"
        elif interval % 3600 == 0:
            multiplier = interval // 3600
            timespan = "hour"
        elif interval % 1800 == 0:
            multiplier = (interval // 1800) * 30
            timespan = "minute"
        elif interval % 60 == 0:
            multiplier = interval // 60
            timespan = "minute"
        elif interval == 1:
            multiplier = 1
            timespan = "second"
        else:
            raise ValueError(f"Interval {interval} is not supported. Must be a multiple of 60 seconds.")

        all_data = []
        current_start = start_dt_utc

        while current_start <= end_dt_utc:
            try:
                # Acquire semaphore before making the request
                self._acquire_semaphore()
                
                try:
                    aggs = self.client.get_aggs(
                        ticker=ticker,
                        multiplier=multiplier,
                        timespan=timespan,
                        from_=current_start,
                        to=end_dt_utc,
                        limit=50000,
                        adjusted=self.adjusted
                    )
                finally:
                    # Always release the semaphore, even if there's an error
                    self._release_semaphore()
                
                if not aggs:
                    break
                batch_data = [{
                    'timestamp': agg.timestamp,
                    'open': agg.open,
                    'high': agg.high,
                    'low': agg.low,
                    'close': agg.close,
                    'volume': agg.volume
                } for agg in aggs]
                all_data.extend(batch_data)
                if batch_data:
                    current_start = pd.to_datetime(batch_data[-1]['timestamp'] + interval*1000, unit="ms", utc=True)
                else:
                    break
            except Exception as e:
                if "429" in str(e):
                    logger.warning(f"Rate limit hit for {ticker}, sleeping for 60 seconds")
                    time_module.sleep(60)
                    continue
                else:
                    raise

        if not all_data:
            return pd.DataFrame()

        df = pd.DataFrame(all_data)
        if df.empty:
            return df
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit="ms", utc=True)
        df['timestamp'] = df['timestamp'].dt.tz_convert("US/Eastern")
        df.set_index("timestamp", inplace=True)
        df["date"] = df.index.date
        return df

    def gather_historical_data(self, ticker: str, start_dt: datetime, end_dt: Optional[datetime] = None, interval: int = 60) -> pd.DataFrame:
        eastern = pytz.timezone('US/Eastern')
        if end_dt is None:
            end_dt = datetime.now(eastern)
        return self._gather_historical_data(ticker, start_dt, end_dt, interval)

    async def get_quote_async(
        self, ticker: str, start_dt: Optional[datetime] = None, end_dt: Optional[datetime] = None
    ) -> LastQuote:
        """Asynchronously retrieves the most recent NBBO quote for a ticker. Start/end times not supported."""
        if start_dt is not None or end_dt is not None:
            raise NotImplementedError("Start and end times are not supported for get_quote")
        quote = await asyncio.to_thread(self.client.get_last_quote, ticker)
        return LastQuote(
            ticker=ticker,
            ask_price=quote.ask_price,
            ask_size=quote.ask_size,
            bid_price=quote.bid_price,
            bid_exchange=quote.bid_exchange
        )

if __name__ == "__main__":
    import os
    from datetime import datetime
    import datetime as dt
    from polygon import RESTClient
    from dotenv import load_dotenv
    import argparse

    load_dotenv()

    api_key = os.getenv("POLYGON_API_KEY")
    if not api_key:
        print("POLYGON_API_KEY environment variable not set.")
        exit(1)

    client = RESTClient(api_key=api_key)
    data = PolygonMarketData(client)

    parser = argparse.ArgumentParser(description="Market Data Collector")
    parser.add_argument("--tickers", type=str, default="PBM,HSDT,TENX,AGFY,XCUR,SMX,MLGO,TAOP,WNW,HKIT", 
                        help="Comma-separated list of tickers (default: PBM,HSDT,TENX,AGFY,XCUR,SMX,MLGO,TAOP,WNW,HKIT)")
    parser.add_argument("--start_dt", type=str, default="2023-10-02", help="Start datetime in YYYY-MM-DD format (time can be included)")
    parser.add_argument("--end_dt", type=str, default="2023-10-03", help="End datetime in YYYY-MM-DD format (time can be included)")
    args = parser.parse_args()

    def main():
        print(f"Fetching data for AAPL from {args.start_dt} to {args.end_dt}")
        start_dt = datetime.strptime(args.start_dt, "%Y-%m-%d")
        end_dt = datetime.strptime(args.end_dt, "%Y-%m-%d")
        start_dt = dt.datetime.combine(start_dt.date(), dt.time(0, 0))
        end_dt = dt.datetime.combine(end_dt.date(), dt.time(23, 59))
        historical_data = data.gather_historical_data("AAPL", start_dt, end_dt, interval = 1800)
        if not historical_data.empty:
            print("\nValidation successful! Retrieved data:")
            print(historical_data.head(3))
            print(f"\nDataset shape: {historical_data.shape}")
            print("\nCache file not found")
        else:
            print("\nValidation failed - no data retrieved")
        print("\nValidation complete")

    main()