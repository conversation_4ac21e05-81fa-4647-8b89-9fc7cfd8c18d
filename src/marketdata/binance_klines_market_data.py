# binance_klines_hist_market_data.py
import asyncio
import glob
import os
from datetime import datetime, timedelta
from functools import lru_cache
from pathlib import Path
from typing import Dict, List, Optional

import pandas as pd
from pandas._libs.tslibs.np_datetime import OutOfBoundsDatetime
import pytz

import logging
logger = logging.getLogger(__name__)

# Fallback header for older Binance CSV files missing header row
EXPECTED_CSV_COLUMNS = [
    "open_time", "open", "high", "low", "close", "volume",
    "close_time", "quote_volume", "count",
    "taker_buy_volume", "taker_buy_quote_volume", "ignore",
]

from marketdata.imarketdata import IMarketData


class BinanceKlinesHistMarketData(IMarketData):
    """
    Lazy-loads Binance monthly-kline CSVs from disk and serves them as
    Eastern-time-indexed pandas DataFrames.

    Directory layout (created by your download script)
    --------------------------------------------------
    {base_dir}/klines/{market}/{SYMBOL}/{interval}/
        SYMBOL-interval-YYYY-MM.csv

    Example
    -------
    market_data/binance/klines/futures/BTCUSDT/1d/BTCUSDT-1d-2024-01.csv
    """

    def __init__(
        self,
        base_dir: str = "market_data/binance",
        market: str = "futures",                  # "spot" or "futures"
        interval: str = "1d",
        history_back_months: int = 120,           # preload this much history
        tz_to: str = "US/Eastern",
    ):
        self.base_dir = Path(base_dir)
        self.market = market
        self.interval = interval
        self.tz_to = pytz.timezone(tz_to)

        # Discover every symbol that has at least one CSV for the interval
        glob_pattern = (
            f"{self.base_dir}/klines/{self.market}/*/{self.interval}/*-{self.interval}-*.csv"
        )
        self._symbols: List[str] = sorted(
            {
                Path(p).parts[-3]  # …/{SYMBOL}/{interval}/file.csv
                for p in glob.glob(glob_pattern)
            }
        )
        if not self._symbols:
            raise RuntimeError(f"No CSV files found at {glob_pattern}")

        # Optionally preload a rolling window of history for every symbol
        self._cache: Dict[str, pd.DataFrame] = {}
        cutoff = datetime.now(tz=pytz.UTC) - timedelta(days=history_back_months * 30)
        valid_symbols: List[str] = []
        for sym in self._symbols:
            try:
                df = self._load_symbol(sym, cutoff=cutoff)
            except RuntimeError as e:
                logger.warning("Skipping symbol %s: %s", sym, e)
                continue
            valid_symbols.append(sym)
            self._cache[sym] = df
        self._symbols = valid_symbols
        if not self._symbols:
            raise RuntimeError(f"No symbols with valid data found at {glob_pattern}")

    # --------------------------------------------------------------------- #
    # public helpers
    # --------------------------------------------------------------------- #
    def list_all_pairs(self) -> List[str]:
        """Return the list of symbols discovered in base_dir."""
        return self._symbols.copy()

    # --------------------------------------------------------------------- #
    # IMarketData required methods
    # --------------------------------------------------------------------- #
    def gather_historical_data(
        self,
        ticker: str,
        start_dt: datetime,
        end_dt: Optional[datetime] = None,
        interval: int = 60,  # ignored – kept for interface completeness
    ) -> pd.DataFrame:
        """
        Return a tz-aware Eastern-indexed OHLCV DataFrame for `ticker`
        between start_dt and end_dt (inclusive).

        Preconditions
        -------------
        * `start_dt` / `end_dt` **must** be tz-aware and already in US/Eastern.
        """
        self._validate_est(start_dt, "start_dt")
        if end_dt is None:
            end_dt = datetime.now(tz=self.tz_to)
        self._validate_est(end_dt, "end_dt")

        if ticker not in self._symbols:
            raise KeyError(f"{ticker} not found on disk")

        # If the timeframe extends earlier than what we cached, load full history once
        df = self._cache.get(ticker)
        if df is None or df.index[0] > start_dt:
            df = self._load_symbol(ticker)
            self._cache[ticker] = df

        return df.loc[start_dt:end_dt]

    async def gather_historical_data_async(  # type: ignore[override]
        self,
        ticker: str,
        start_dt: datetime,
        end_dt: Optional[datetime] = None,
        interval: int = 60,
    ) -> pd.DataFrame:
        """
        Async wrapper that simply runs the sync method inside a worker thread.
        """
        return await asyncio.to_thread(
            self.gather_historical_data, ticker, start_dt, end_dt, interval
        )

    # --------------------------------------------------------------------- #
    # internals
    # --------------------------------------------------------------------- #
    def _load_symbol(
        self,
        symbol: str,
        cutoff: Optional[datetime] = None,
    ) -> pd.DataFrame:
        pattern = (
            f"{self.base_dir}/klines/{self.market}/{symbol}/{self.interval}/"
            f"{symbol}-{self.interval}-*.csv"
        )
        pieces = []
        tz_to = self.tz_to

        for csv_path in sorted(glob.glob(pattern)):
            # Extract YYYY, MM from filename: .../SYMBOL-interval-YYYY-MM.csv
            fname = Path(csv_path).name  # SYMBOL-interval-YYYY-MM.csv
            try:
                year = int(fname.split("-")[-2])
                month = int(fname.split("-")[-1].split(".")[0])
            except Exception:
                logger.warning("Could not parse year/month from %s; skipping", csv_path)
                continue

            # Cutoff fast check
            if cutoff:
                # Month end
                month_start = datetime(year, month, 1, tzinfo=pytz.UTC)
                next_month = (month_start + timedelta(days=32)).replace(day=1)
                month_end = next_month - timedelta(seconds=1)
                if month_end < cutoff:
                    continue

            # Primary read (let pandas guess header)
            df = pd.read_csv(
                csv_path,
                dtype={
                    "open": "float64", "high": "float64", "low": "float64",
                    "close": "float64", "volume": "float64"
                },
            )
            if "open_time" not in df.columns:
                # Fallback headerless
                df = pd.read_csv(
                    csv_path,
                    header=None,
                    names=EXPECTED_CSV_COLUMNS,
                    dtype={
                        "open": "float64", "high": "float64", "low": "float64",
                        "close": "float64", "volume": "float64"
                    },
                )

            # --- Detect epoch unit per file -----------------------------------
            try:
                raw_open = df["open_time"].astype("int64")
            except Exception as e:
                logger.warning("File %s missing open_time numeric values: %s", csv_path, e)
                continue
            first_val = int(raw_open.iloc[0])

            def detect_unit(market: str, year: int, sample: int) -> str:
                # Explicit rule from Binance docs
                if market == "spot" and year >= 2025:
                    # Expect microseconds (>=16 digits)
                    return "us"
                return "ms"

            unit = detect_unit(self.market, year, first_val)
            digits = len(str(first_val))
            if unit == "ms" and digits > 13:
                logger.warning(
                    "Unexpected >13 digit epoch in ms file %s (digits=%d); coercing to microseconds",
                    csv_path, digits
                )
                unit = "us"
            if unit == "us" and digits <= 13:
                logger.warning(
                    "Unexpected <=13 digit epoch in us file %s (digits=%d); treating as milliseconds",
                    csv_path, digits
                )
                unit = "ms"

            # Convert open_time & close_time
            try:
                df["open_time"]  = pd.to_datetime(df["open_time"].astype("int64"), unit=unit, utc=True)
                df["close_time"] = pd.to_datetime(df["close_time"].astype("int64"), unit=unit, utc=True)
            except OutOfBoundsDatetime as e:
                logger.error("OutOfBounds converting %s with unit=%s: %s", csv_path, unit, e)
                continue

            # Basic sanity (reject future > now + 2d)
            now_utc = datetime.now(tz=pytz.UTC)
            if df["open_time"].max() > now_utc + timedelta(days=2):
                logger.error("Future timestamps detected in %s (max %s); skipping", csv_path, df["open_time"].max())
                continue

            df = df.set_index("open_time")
            df.index = df.index.tz_convert(tz_to)
            pieces.append(df)

        if not pieces:
            raise RuntimeError(f"No CSVs loaded for {symbol}")

        df_all = pd.concat(pieces).sort_index()

        # Post-merge validation for daily interval
        if self.interval == "1d":
            # Most diffs should be 1 day (allow for DST anomalies)
            diffs = df_all.index.to_series().diff().dropna()
            frac_daily = (diffs == pd.Timedelta(days=1)).mean()
            if frac_daily < 0.90:
                logger.debug("Irregular daily spacing for %s (daily fraction %.2f)", symbol, frac_daily)

        # Guard: max index not in far future
        if df_all.index.max() > datetime.now(tz=tz_to) + timedelta(days=2):
            raise RuntimeError(f"Future dates after merge for {symbol}")

        return df_all

    # ------------------------------------------------------------------ #
    @staticmethod
    def _validate_est(ts: datetime, argname: str) -> None:
        if ts.tzinfo is None:
            raise ValueError(f"{argname} must be tz-aware (US/Eastern)")
        if ts.tzinfo.zone not in ("US/Eastern", "America/New_York"):
            raise ValueError(f"{argname} must be in US/Eastern timezone")


# ------------------------------------------------------------------------- #
# quick demo
if __name__ == "__main__":
    from datetime import timezone

    md = BinanceKlinesHistMarketData(history_back_months=24)  # 2-year cache
    print("Pairs:", md.list_all_pairs()[:10], "…")

    est = pytz.timezone("US/Eastern")
    start = est.localize(datetime(2024, 1, 5))
    end = est.localize(datetime(2024, 2, 10))
    df_btc = md.gather_historical_data("BTCUSDT", start, end)
    print(df_btc.head())
