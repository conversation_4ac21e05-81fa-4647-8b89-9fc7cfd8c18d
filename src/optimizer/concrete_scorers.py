# optimization_framework/concrete_scorers.py
from optimizer.base_scorer import TradeDataFrameScorer
import pandas as pd
import numpy as np

class TotalPnlScorer(TradeDataFrameScorer):
    """Scores based on the sum of the 'pnl' column."""
    def _calculate_score(self, trades_df: pd.DataFrame, initial_capital: float | None, date_col: str | None) -> float:
        # Ensure pnl column is numeric, coercing errors
        pnl_numeric = pd.to_numeric(trades_df['pnl'], errors='coerce')
        if pnl_numeric.isnull().all(): # Check if all values became NaN
             print("Warning: 'pnl' column contains non-numeric data.")
             return -np.inf
        return pnl_numeric.sum() # Sum only numeric values

    def _requires_equity_curve(self) -> bool:
        return False

class SharpeScorer(TradeDataFrameScorer):
    """Scores based on the annualized Sharpe Ratio calculated from daily returns."""
    def __init__(self, risk_free_rate: float = 0.0, ann_factor: int = 252):
        self.risk_free_rate = risk_free_rate
        self.ann_factor = ann_factor
        if ann_factor <= 0:
            raise ValueError("Annualization factor must be positive.")

    def _calculate_score(self, trades_df: pd.DataFrame, initial_capital: float | None, date_col: str | None) -> float:
        if initial_capital is None:
            print("Warning: Initial capital required for SharpeScorer.")
            return -np.inf
        if initial_capital <= 0:
             print("Warning: Initial capital must be positive for SharpeScorer.")
             return -np.inf
        if date_col is None:
             print("Warning: Date column required for SharpeScorer.")
             return -np.inf

        equity_curve = self._build_equity_curve(trades_df, initial_capital, date_col)

        if equity_curve is None or len(equity_curve) < 2:
            # print("Debug: SharpeScorer - Equity curve insufficient.") # Debug print
            return -np.inf

        daily_ret = equity_curve.pct_change()
        # Drop initial NaN and any other NaNs/Infs that might arise
        daily_ret = daily_ret.replace([np.inf, -np.inf], np.nan).dropna()

        if daily_ret.empty or len(daily_ret) < 2: # Need at least 2 returns for std dev
            # print(f"Debug: SharpeScorer - Not enough valid daily returns ({len(daily_ret)}).") # Debug print
            return -np.inf

        mean_ret_ann = daily_ret.mean() * self.ann_factor
        std_dev_ann = daily_ret.std() * np.sqrt(self.ann_factor)

        if std_dev_ann is None or std_dev_ann == 0 or not np.isfinite(std_dev_ann):
            # Handle zero volatility case - return 0 if mean return is non-negative, else -inf
            # print(f"Debug: SharpeScorer - Invalid annual std dev ({std_dev_ann}). Mean ann ret: {mean_ret_ann}") # Debug print
            return 0.0 if mean_ret_ann >= 0 else -np.inf
            # return -np.inf # Stricter: No Sharpe if no volatility

        sharpe = (mean_ret_ann - self.risk_free_rate) / std_dev_ann
        # print(f"Debug: Sharpe Calc -> MeanAnn={mean_ret_ann:.4f}, StdAnn={std_dev_ann:.4f}, Sharpe={sharpe:.4f}") # Debug print
        
        return float(sharpe) # Ensure float return

    def _requires_equity_curve(self) -> bool:
        return True

# Example: Calmar Scorer (requires Max Drawdown)
class CalmarScorer(TradeDataFrameScorer):
    """Scores based on Calmar Ratio (CAGR / Max Drawdown)."""
    def __init__(self, ann_factor: int = 252):
         self.ann_factor = ann_factor
         if ann_factor <= 0: raise ValueError("Annualization factor must be positive.")

    def _calculate_score(self, trades_df: pd.DataFrame, initial_capital: float | None, date_col: str | None) -> float:
        if initial_capital is None: return -np.inf
        if initial_capital <= 0: return -np.inf
        if date_col is None: return -np.inf

        equity_curve = self._build_equity_curve(trades_df, initial_capital, date_col)
        if equity_curve is None or len(equity_curve) < 2: return -np.inf

        # Calculate CAGR
        start_val = equity_curve.iloc[0]
        end_val = equity_curve.iloc[-1]
        num_days = len(equity_curve)
        years = num_days / self.ann_factor
        if years <= 0 or start_val <= 0: return -np.inf # Avoid math errors

        cagr = ( (end_val / start_val) ** (1 / years) ) - 1

        # Calculate Max Drawdown
        running_max = equity_curve.cummax()
        drawdown = (equity_curve - running_max) / running_max
        max_dd = abs(drawdown.min()) # Use positive value for ratio

        if max_dd == 0 or not np.isfinite(max_dd):
             # Handle zero drawdown: return large number if CAGR positive, 0 if CAGR zero, -inf if CAGR negative?
             # Or simply return 0 or -inf as Calmar is undefined/infinite. Let's return -inf.
             return -np.inf

        calmar = cagr / max_dd
        return float(calmar) if np.isfinite(calmar) else -np.inf

    def _requires_equity_curve(self) -> bool:
        return True