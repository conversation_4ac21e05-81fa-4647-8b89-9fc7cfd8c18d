from __future__ import annotations

# main_optimization_script.py

# --- Standard Library Imports ---
from datetime import datetime, timedelta
import pytz
import pandas as pd
from typing import Dict, Set, Callable # For type hints
from IPython.display import display # For displaying DataFrames nicely in notebooks

# --- Framework Imports ---
from optimizer import (
    Optimizer,
    TotalPnlScorer,
    SharpeScorer,
)
# --- Strategy Implementation Imports ---
from optimizer.gap_executor import LongGapStrategyExecutor

# --- Data Fetching Functions ---

import pytz
from sandbox.utils import *
from marketdata import MarketDataBuilder, disk_market_data
import pandas as pd
import numpy as np
from datetime import datetime
import pytz
import os
import matplotlib.pyplot as plt
import seaborn as sns
import sys
import warnings
from tqdm.notebook import tqdm 


# Filter pandas SettingWithCopyWarning more broadly if needed
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', message="The behavior of DataFrame concatenation with empty or all-NA entries is deprecated")
pd.options.mode.chained_assignment = None # 'warn' or None ('raise' is default)

intraday_market_data = (MarketDataBuilder()
                        .with_trade_session("full") 
                        .with_period("intraday")
                        .build_market_data())

daily_market_data = (MarketDataBuilder()
                        .with_period("daily") 
                        .with_disk_data(start_date=datetime(2024, 1, 1))
                        .build_market_data())

def get_daily_data(ticker, start_dt, end_dt):
    """Fetches daily data using the configured daily_market_data instance."""
    try:
        return daily_market_data.gather_historical_data(
            ticker=ticker,
            start_dt=start_dt,
            end_dt=end_dt,
            interval=86400 # Daily interval
        )
    except Exception as e:
        print("Exception", e)
        return None

def get_per_minute_data(ticker, start_dt, end_dt) -> pd.DataFrame:
    """
        Output is a pandas dataframe
        
        DatetimeIndex with timezone ( 2010-02-11 09:43:00-05:00 to 2025-04-22 19:59:00-04:00)
        Data columns (total 6 columns):
        #   Column  Dtype  
        ---  ------  -----  
        0   open    float64
        1   high    float64
        2   low     float64
        3   close   float64
        4   volume  float64
        5   date    object 
        dtypes: float64(5), object(1)

    """
    data = intraday_market_data.gather_historical_data(
        ticker=ticker,
        start_dt=start_dt,
        end_dt=end_dt,
        interval=60
    )
    return data

def get_hourly_data(ticker, start_dt, end_dt) -> pd.DataFrame:
    """
        Output is a pandas dataframe
        
        DatetimeIndex with timezone ( 2010-02-11 09:43:00-05:00 to 2025-04-22 19:59:00-04:00)
        Data columns (total 6 columns):
        #   Column  Dtype  
        ---  ------  -----  
        0   open    float64
        1   high    float64
        2   low     float64
        3   close   float64
        4   volume  float64
        5   date    object 
        dtypes: float64(5), object(1)

    """
    data = intraday_market_data.gather_historical_data(
        ticker=ticker,
        start_dt=start_dt,
        end_dt=end_dt,
        interval=3600
    )
    return data

# --- Ticker Collection Class ---
from dateutil.relativedelta import relativedelta
from datetime import datetime
import pandas as pd
import pytz
from tickers.ticker_helpers import TickerInfoStore

class SmallCapTickerCollector:
    """
    Collect all tickers (listed or later delisted) that at any snapshot
    had a market cap within [min_cap, max_cap].
    """
    def __init__(
        self,
        start_dt: datetime,
        end_dt: datetime,
        min_market_cap: float,
        max_market_cap: float,
        interval_months: int = 6,
        timezone: str = "US/Eastern",
    ):
        self.tz = pytz.timezone(timezone)
        self.start_dt = self._localize(start_dt)
        self.end_dt = self._localize(end_dt)
        self.min_cap = min_market_cap
        self.max_cap = max_market_cap
        self.interval = interval_months
        self.ticker_info = TickerInfoStore()

    def _localize(self, dt: datetime) -> datetime:
        return dt if dt.tzinfo else self.tz.localize(dt)

    def _snapshot_dates(self) -> list[datetime]:
        dates = []
        dt = self.start_dt
        while dt <= self.end_dt:
            dates.append(dt)
            dt += relativedelta(months=self.interval)
        return dates

    def _fetch_snapshot(self, snapshot_dt: datetime) -> pd.DataFrame:
        df = self.ticker_info.load_ticker_data(snapshot_dt)
        if df.empty:
            df = self.ticker_info.fetch_ticker_data(snapshot_dt)
        return df

    def _filter_by_cap(self, df: pd.DataFrame) -> pd.Series:
        return (
            df["market_cap"].notna() &
            (df["market_cap"] >= self.min_cap) &
            (df["market_cap"] <= self.max_cap)
        )

    def collect(self, include_delisted: bool = True) -> set[str]:
        """
        Returns a set of tickers that ever met the market-cap criteria
        between start_dt and end_dt, sampled every interval_months.
        If include_delisted, also fetches delisted tickers up to end_dt.
        """
        all_tickers: set[str] = set()

        # 1) snapshot-listed
        for snap in self._snapshot_dates():
            df = self._fetch_snapshot(snap)
            mask = self._filter_by_cap(df)
            all_tickers |= set(df.loc[mask, "ticker"])

        # 2) optionally include delisted
        if include_delisted:
            del_df = self.ticker_info.fetch_ticker_data(self.end_dt, active=False)
            del_df["delisted_date"] = pd.to_datetime(
                del_df["delisted_utc"]
            ).dt.date
            in_window = del_df[
                (del_df["delisted_date"] >= self.start_dt.date()) &
                (del_df["delisted_date"] <= self.end_dt.date())
            ]
            mask2 = self._filter_by_cap(in_window)
            all_tickers |= set(in_window.loc[mask2, "ticker"])

        return all_tickers

# --- Metric Calculation Functions ---
def compute_metrics(trades_df: pd.DataFrame,
                    initial_capital: float = 100_000,
                    rf: float = 0.0) -> Dict[str, float]:
    """
    Metrics based on daily returns of the equity curve.
    """
    if trades_df.empty:
        return {}

    wins   = trades_df[trades_df.pnl > 0]
    losses = trades_df[trades_df.pnl <= 0]

    eq_curve = build_equity_curve_by_date(trades_df, initial_capital)
    daily_ret = eq_curve.pct_change().dropna()

    # time span in years for CAGR
    years = (eq_curve.index[-1] - eq_curve.index[0]).days / 365.25 or np.nan

    vol_ann = daily_ret.std() * np.sqrt(252)
    neg_vol_ann = daily_ret[daily_ret < 0].std() * np.sqrt(252)

    running_max = eq_curve.cummax()
    max_dd_pct = ((running_max - eq_curve) / running_max).max() * 100

    return {
        'Total Trades':       len(trades_df),
        'Win Rate (%)':       len(wins) / len(trades_df) * 100,
        'Avg PnL ($)':        trades_df.pnl.mean(),
        'Sum PnL ($)':        trades_df.pnl.sum(),
        'Avg PnL (%)':        trades_df.pnl_pct.mean(),
        'Profit Factor':      wins.pnl.sum() / abs(losses.pnl.sum()) if len(losses) else np.nan,
        'Payoff Ratio':       wins.pnl.mean() / abs(losses.pnl.mean()) if len(losses) else np.nan,
        'Expectancy ($)':     (len(wins)/len(trades_df)) * wins.pnl.mean()
                              - (len(losses)/len(trades_df)) * abs(losses.pnl.mean()),
        'CAGR (%)':           ((eq_curve.iloc[-1] / initial_capital) ** (1/years) - 1) * 100
                              if years and years > 0 else np.nan,
        'Volatility (%)':     vol_ann * 100,
        'Sharpe Ratio':       ((daily_ret.mean()*252 - rf) / vol_ann) if vol_ann else np.nan,
        'Sortino Ratio':      ((daily_ret.mean()*252 - rf) / neg_vol_ann) if neg_vol_ann else np.nan,
        'Max Drawdown (%)':   max_dd_pct,
        'Calmar Ratio':       (((eq_curve.iloc[-1]/initial_capital)-1) / (max_dd_pct/100))
                              if max_dd_pct else np.nan
    }
    
    
def build_equity_curve_by_date(trades_df: pd.DataFrame,
                               initial_capital: float = 100_000) -> pd.Series:
    """
    Time-indexed equity curve – one data-point per calendar day.
    """
    if trades_df.empty:
        return pd.Series(dtype=float)

    # aggregate PnL by calendar date
    pnl_by_date = trades_df.groupby('date')['pnl'].sum()
    pnl_by_date.index = pd.to_datetime(pnl_by_date.index)

    # full date range (fill missing days with 0 PnL)
    full_idx = pd.date_range(pnl_by_date.index.min(), pnl_by_date.index.max())
    pnl_by_date = pnl_by_date.reindex(full_idx, fill_value=0)

    return initial_capital + pnl_by_date.cumsum()

# ==========================================================================

# --- Main Execution Block ---
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)

    print("Starting Optimization Script...")
    eastern_tz = pytz.timezone("US/Eastern")
    initial_capital_setting = 100_000 # Define once

    # --- 1. Prepare Dependencies for the Executor ---
    print("Preparing fixed dependencies...")
    # Use a relevant date range for collecting tickers
    collector_start = eastern_tz.localize(datetime(2025, 1, 1))
    collector_end = eastern_tz.localize(datetime(2025, 5, 1))
    try:
        collector = SmallCapTickerCollector(
            start_dt=collector_start, end_dt=collector_end,
            min_market_cap=50_000_000, max_market_cap=500_000_000
        )
        base_tickers = collector.collect(include_delisted=False)
        if not base_tickers:
             print("Error: Ticker collection yielded no results. Exiting.")
             exit()
        print(f"Collected {len(base_tickers)} base tickers.")
    except Exception as e:
         print(f"Error during ticker collection: {e}. Exiting.")
         exit()

    # Bundle data fetchers (make sure these functions are correctly defined/imported above)
    data_fetchers_dict = {
        'daily': get_daily_data,
        'hourly': get_hourly_data,
        'minute': get_per_minute_data
    }

    # --- 2. Instantiate Strategy Executor ---
    try:
        long_gap_executor = LongGapStrategyExecutor(
            data_fetchers=data_fetchers_dict,
            base_tickers=base_tickers,
            initial_capital=initial_capital_setting
        )
    except Exception as e:
         print(f"Error initializing Strategy Executor: {e}. Exiting.")
         exit()

    # --- 3. Define Parameter Grid ---
    # Using a smaller grid for a quicker example run
    long_gap_param_grid = {
        'min_gap_pct': [25.0, 50.0],
        'min_premkt_dvol': [100_000, 250_000],
        'risk_reward': [2.0, 5.0, 10.0],
        'position_size_pct': [0.2],
        'risk_percent': [0.05]
    }
    print(f"Parameter Grid: {long_gap_param_grid}")

    # --- 4. Choose a Scorer ---
    # scorer = TotalPnlScorer()
    scorer = TotalPnlScorer() # SharpeScorer(risk_free_rate=0.0)
    print(f"Using Scorer: {type(scorer).__name__}")

    # --- 5. Instantiate the Optimizer ---
    optimizer = Optimizer(
        strategy_executor=long_gap_executor,
        scorer=scorer,
        param_grid=long_gap_param_grid,
        initial_capital=initial_capital_setting,
        use_parallel=True, # Set to False for easier debugging
        max_workers=8      # Adjust based on your CPU cores
    )

    # --- 6. Define Overall Period and Split Point ---
    overall_start = collector_start
    overall_end = collector_end
    split_fraction = 0.7 # Example: 70% train, 30% test
    # split_date = eastern_tz.localize(datetime(2024, 6, 1)) # Alternative: specify date

    # --- 7. Run Train/Test Optimization ---
    print("\nStarting Train/Test Optimization...")
    try:
        train_test_results = optimizer.run_train_test(
            overall_start_dt=overall_start,
            overall_end_dt=overall_end,
            split_point=split_fraction
        )
    except Exception as e:
         print(f"Critical error during optimizer run: {e}")
         import traceback
         print(traceback.format_exc())
         train_test_results = None # Ensure it's None on failure

    # --- 8. Display Results ---
    print("\n--- Train/Test Optimization Summary ---")
    if train_test_results and train_test_results.get('best_params_train'):
        best_params = train_test_results['best_params_train']
        best_train_score = train_test_results['best_train_score']
        test_score = train_test_results['test_score_validation']
        test_error = train_test_results['test_error'] # Get error from test run

        print(f"Best Parameters (from Training): {best_params}")
        print(f"Best Training Score ({type(scorer).__name__}): {best_train_score:.4f}")
        print(f"Validation Test Score ({type(scorer).__name__}): {test_score:.4f}")
        if test_error:
             print(f"Error during Test Run: {test_error}")

        # --- Assess Overfitting ---
        if best_train_score > -np.inf and test_score > -np.inf:
             score_diff = best_train_score - test_score
             score_diff_pct = (score_diff / abs(best_train_score) * 100) if best_train_score != 0 else 0
             print(f"\nScore Drop-off (Train vs Test): {score_diff:.4f} ({score_diff_pct:.2f}%)")
             if score_diff_pct > 50: # Example threshold
                  print("WARNING: High performance drop-off observed (>50%), potential overfitting.")
             elif test_score < 0 < best_train_score:
                  print("WARNING: Strategy profitable in-sample but unprofitable out-of-sample.")
             elif test_score <= best_train_score: # Allow for some noise
                  print("Performance drop-off seems reasonable or improved.")
             else: # Test score higher than train score
                  print("Note: Test score is higher than train score.") # Could be noise or luck

        # --- Display Metrics Comparison ---
        print("\nDetailed Metrics Comparison (Best Params):")
        train_list = train_test_results.get('train_results_list', [])
        train_df_best = None
        for r in train_list:
            # Ensure comparison handles potential floating point differences in params if needed
            if r.params == best_params:
                train_df_best = r.result_data
                break

        test_df_best = train_test_results.get('test_result_data') # This is the trades_df from test

        metrics_comp = {}
        if isinstance(train_df_best, pd.DataFrame) and not train_df_best.empty:
             metrics_comp['Train'] = compute_metrics(train_df_best, initial_capital_setting)
        else:
             print("Could not retrieve or process Training trade data for best params.")

        if isinstance(test_df_best, pd.DataFrame) and not test_df_best.empty:
             metrics_comp['Test'] = compute_metrics(test_df_best, initial_capital_setting)
        else:
             print("Could not retrieve or process Test trade data for best params.")

        if metrics_comp:
             metrics_compare_df = pd.DataFrame(metrics_comp).round(2)
             if 'Train' in metrics_compare_df.columns and 'Test' in metrics_compare_df.columns:
                  # Calculate difference robustly, handle NaNs
                  diff = metrics_compare_df['Test'].sub(metrics_compare_df['Train'], fill_value=0)
                  metrics_compare_df['Diff (Test-Train)'] = diff
             display(metrics_compare_df)
        else:
             print("Could not generate detailed metrics comparison (missing trade data).")

    else:
        print("Train/Test Optimization did not produce valid results or failed.")

    print("\nOptimization Script Finished.")