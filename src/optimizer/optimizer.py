# optimization_framework/optimizer.py
import itertools
import concurrent.futures
from tqdm.auto import tqdm
from typing import List, Dict, Any, Tuple, Type
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import pytz
from dataclasses import dataclass, field # Added field for default factory

from optimizer.base_executor import BaseStrategyExecutor
from optimizer.base_scorer import BaseScorer

@dataclass
class OptimizationResult:
    """Holds the result of a single parameter combination run."""
    params: Dict[str, Any]
    score: float = -np.inf # Default to worst score
    result_data: Any | None = None # e.g., trades_df or None if failed
    error: Exception | None = None # Store exception if execution failed

    def __lt__(self, other):
        # Define comparison for sorting (higher score is better)
        if not isinstance(other, OptimizationResult):
            return NotImplemented
        return self.score < other.score


class Optimizer:
    """
    Orchestrates the optimization process for a given strategy executor,
    scorer, and parameter grid. Supports parallel execution and train/test splits.
    """
    def __init__(self,
                 strategy_executor: BaseStrategyExecutor,
                 scorer: BaseScorer,
                 param_grid: Dict[str, List[Any]],
                 initial_capital: float = 100_000, # Default capital
                 use_parallel: bool = True,
                 max_workers: int | None = None): # Default for ProcessPoolExecutor/ThreadPoolExecutor

        if not isinstance(strategy_executor, BaseStrategyExecutor):
             raise TypeError("strategy_executor must be an instance of BaseStrategyExecutor")
        if not isinstance(scorer, BaseScorer):
             raise TypeError("scorer must be an instance of BaseScorer")
        if not isinstance(param_grid, dict) or not param_grid:
             raise ValueError("param_grid must be a non-empty dictionary")
        if initial_capital <= 0:
             raise ValueError("initial_capital must be positive")

        self.executor = strategy_executor
        self.scorer = scorer
        self.param_grid = param_grid
        self.initial_capital = initial_capital
        self.use_parallel = use_parallel
        self.max_workers = max_workers


    def _generate_param_combos(self) -> List[Dict[str, Any]]:
        """Generates all parameter combinations from the grid."""
        if not self.param_grid: return []
        param_names = list(self.param_grid.keys())
        param_values = list(self.param_grid.values())
        # Ensure all values in the grid are lists or tuples
        if not all(isinstance(v, (list, tuple)) for v in param_values):
             raise TypeError("All values in param_grid must be lists or tuples")

        all_combos_tuples = list(itertools.product(*param_values))
        all_combos_dicts = [dict(zip(param_names, combo)) for combo in all_combos_tuples]
        return all_combos_dicts


    def _execute_task(self, params_dict: Dict[str, Any], start_dt: datetime, end_dt: datetime) -> OptimizationResult:
        """Executes and scores one parameter combination."""
        result_data = None
        score = -np.inf
        error = None
        try:
            # Run simulation using the configured executor
            result_data = self.executor.run(
                params=params_dict,
                start_dt=start_dt,
                end_dt=end_dt
            )
            # Score the result using the configured scorer and stored initial_capital
            score = self.scorer.score(result_data, self.initial_capital)

        except Exception as e:
            print(f"Execute Task Error | Params: {params_dict} | Error: {e}")
            # import traceback # Uncomment for detailed debugging
            # print(traceback.format_exc())
            error = e # Store the actual error

        return OptimizationResult(params=params_dict, score=score, result_data=result_data, error=error)


    def run(self, start_dt: datetime, end_dt: datetime) -> List[OptimizationResult]:
        """
        Runs the optimization over the specified period.

        Args:
            start_dt: Start date for the optimization runs.
            end_dt: End date for the optimization runs.

        Returns:
            A list of OptimizationResult objects, sorted by score (descending).
        """
        all_param_combos = self._generate_param_combos()
        if not all_param_combos:
             print("Warning: Parameter grid is empty or invalid. No optimization run.")
             return []

        print(f"Optimizer: Running {len(all_param_combos)} combinations for {type(self.executor).__name__}")
        print(f"Period: {start_dt.date()} to {end_dt.date()}")
        print(f"Scorer: {type(self.scorer).__name__}")

        results_list: List[OptimizationResult] = []
        executor_func = self._execute_task # Function to be called by executor

        # Decide which executor based on needs (Pickling for ProcessPool)
        # ThreadPoolExecutor is generally safer for complex objects / IO-bound tasks
        ExecutorChoice = concurrent.futures.ThreadPoolExecutor if self.use_parallel else None
        # ExecutorChoice = concurrent.futures.ProcessPoolExecutor if self.use_parallel else None # If tasks are CPU-bound & pickleable

        if ExecutorChoice:
            with ExecutorChoice(max_workers=self.max_workers) as pool:
                future_to_params = {
                    pool.submit(executor_func, params, start_dt, end_dt): params
                    for params in all_param_combos
                }
                print(f"Submitted {len(future_to_params)} tasks to {ExecutorChoice.__name__}.")
                for future in tqdm(concurrent.futures.as_completed(future_to_params),
                                   total=len(future_to_params), desc="Optimizing", unit="combo"):
                    try:
                        result = future.result() # Get result from the future
                        results_list.append(result)
                    except Exception as exc:
                         # Catch errors from the future/task execution if not caught in _execute_task
                         params_err = future_to_params[future]
                         print(f'Critical Error for params {params_err} in future: {exc}')
                         # Create a result indicating failure
                         results_list.append(OptimizationResult(params=params_err, score=-np.inf, result_data=None, error=exc))
        else: # Sequential execution
            print("Running sequentially...")
            for params in tqdm(all_param_combos, desc="Optimizing", unit="combo"):
                result = executor_func(params, start_dt, end_dt)
                results_list.append(result)

        # Sort results by score (descending)
        # Handle potential errors during sorting if score is not comparable (e.g., None)
        try:
            results_list.sort(reverse=True) # Uses the __lt__ defined in OptimizationResult
        except TypeError as e:
             print(f"Warning: Could not sort results, likely due to non-comparable scores: {e}. Returning unsorted.")

        print(f"Optimization run complete. Processed {len(results_list)} results.")
        return results_list


    def run_train_test(self,
                       overall_start_dt: datetime,
                       overall_end_dt: datetime,
                       split_point: datetime | float) -> Dict[str, Any]:
        """
        Orchestrates train/test optimization.

        Args:
            overall_start_dt: Start date for the entire historical period.
            overall_end_dt: End date for the entire historical period.
            split_point: The date or fraction (0 to 1) to split train/test data.

        Returns:
            A dictionary containing 'best_params_train', 'best_train_score',
            'test_score_validation', 'train_results_list', 'test_result_data', 'test_error'.
        """
        # --- Determine Train/Test Periods (Handles Timezones) ---
        tz = overall_start_dt.tzinfo # Infer timezone from start date
        if tz and not isinstance(tz, (pytz.BaseTzInfo, datetime.tzinfo)):
             print(f"Warning: Timezone '{tz}' might not be fully compatible. Using pytz is recommended.")

        if isinstance(split_point, float):
            if not (0 < split_point < 1): raise ValueError("split_point fraction must be between 0 and 1")
            duration = overall_end_dt - overall_start_dt
            # Ensure calculation happens correctly even with timezones
            split_dt_naive = (overall_start_dt.replace(tzinfo=None) + duration * split_point)
            if tz: split_dt_actual = tz.localize(split_dt_naive) # Apply original timezone
            else: split_dt_actual = split_dt_naive
        elif isinstance(split_point, datetime):
            # Make split_point timezone-aware consistently with overall dates
            if tz and not split_point.tzinfo: split_dt_actual = tz.localize(split_point)
            elif not tz and split_point.tzinfo: split_dt_actual = split_point.replace(tzinfo=None)
            else: split_dt_actual = split_point # Assume consistent or both naive
            if not (overall_start_dt < split_dt_actual < overall_end_dt): raise ValueError("split_point date must be within the overall date range")
        else: raise TypeError("split_point must be a float (fraction) or datetime object")

        # Ensure end date is strictly after start date for periods
        train_start_dt = overall_start_dt
        train_end_dt = split_dt_actual - timedelta(microseconds=1) # Use microsecond to avoid overlap
        test_start_dt = split_dt_actual
        test_end_dt = overall_end_dt

        if train_end_dt < train_start_dt:
             raise ValueError(f"Calculated training end date ({train_end_dt}) is before start date ({train_start_dt}). Check split point.")
        if test_end_dt < test_start_dt:
              raise ValueError(f"Calculated testing end date ({test_end_dt}) is before start date ({test_start_dt}). Check split point.")


        print(f"Train/Test Split Date: {split_dt_actual.date()}")
        print(f"Training Period: {train_start_dt.date()} to {train_end_dt.date()}")
        print(f"Testing Period:  {test_start_dt.date()} to {test_end_dt.date()}")

        # --- Run Optimization on Training Data ---
        print("\n--- Running Optimization on Training Set ---")
        train_results_list = self.run(train_start_dt, train_end_dt) # Calls the run method above

        if not train_results_list or train_results_list[0].score == -np.inf:
            print("Optimization on training set failed or yielded no valid results.")
            return {'best_params_train': None, 'best_train_score': -np.inf,
                    'test_score_validation': -np.inf, 'train_results_list': train_results_list,
                    'test_result_data': None, 'test_error': None}

        best_train_result = train_results_list[0]
        best_params_train = best_train_result.params
        best_train_score = best_train_result.score

        print(f"\nBest Parameters from Training: {best_params_train}")
        print(f"Best Training Score ({type(self.scorer).__name__}): {best_train_score:.4f}")

        # --- Run Validation on Test Data ---
        print("\n--- Running Validation on Test Set with Best Training Params ---")
        # Use _execute_task directly for a single run on the test set
        test_validation_result: OptimizationResult = self._execute_task(
            best_params_train, test_start_dt, test_end_dt
        )
        test_score_validation = test_validation_result.score
        test_result_data = test_validation_result.result_data # This is the trades_df from test run
        test_error = test_validation_result.error # Capture potential error during test run

        if test_error:
             print(f"Test Set Execution Error: {test_error}")
        print(f"Test Set Score ({type(self.scorer).__name__}): {test_score_validation:.4f}")


        # --- Return Combined Results ---
        return {
            'best_params_train': best_params_train,
            'best_train_score': best_train_score,
            'test_score_validation': test_score_validation,
            'train_results_list': train_results_list, # Full list from training phase
            'test_result_data': test_result_data, # Result (trades_df) from the single test run
            'test_error': test_error # Error during the test run, if any
        }