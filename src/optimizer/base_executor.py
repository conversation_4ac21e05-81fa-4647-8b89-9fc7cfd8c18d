from abc import ABC, abstractmethod
from typing import Any, Dict
from datetime import datetime
import pandas as pd

class BaseStrategyExecutor(ABC):
    """
    Abstract Base Class for executing a single run of a trading strategy.
    Subclasses will contain the logic specific to a particular strategy
    and define their own __init__ method to accept necessary dependencies.
    """

    # No __init__ here - subclasses define their own dependencies

    @abstractmethod
    def run(self, params: Dict[str, Any], start_dt: datetime, end_dt: datetime) -> pd.DataFrame | None:
        """
        Executes one simulation run of the strategy with specific parameters.

        Args:
            params: Dictionary of parameters specific to this optimization run.
            start_dt: Start date for the simulation period.
            end_dt: End date for the simulation period.

        Returns:
            A pandas DataFrame containing the trade log (e.g., 'trades_df'),
            or None if the simulation failed or produced no trades.
            Must contain at least 'pnl' and a date column ('date' or 'exit_time').
        """
        pass