# optimization_framework/base_scorer.py
from abc import ABC, abstractmethod
from typing import Any, Dict
import pandas as pd
import numpy as np
from datetime import datetime, timedelta # Added timedelta

class BaseScorer(ABC):
    """Abstract Base Class for scoring the results of a strategy simulation."""

    @abstractmethod
    def score(self, result_data: Any, initial_capital: float | None = None) -> float:
        """
        Calculates a score based on the simulation result. Higher is better.

        Args:
            result_data: The output from the BaseStrategyExecutor's run method
                         (e.g., a trades DataFrame).
            initial_capital: Optional initial capital for context if needed by score.

        Returns:
            A float representing the score. Returns -np.inf for invalid/failed results.
        """
        pass

class TradeDataFrameScorer(BaseScorer):
    """
    Base scorer class that expects the result_data to be a pandas DataFrame
    containing trade information, typically including a 'pnl' column and a
    date column ('date' or 'exit_time').
    Includes helper for building daily equity curve if needed by subclasses.
    """
    def score(self, result_data: pd.DataFrame | None, initial_capital: float | None = None) -> float:
        # --- Basic Validation ---
        if result_data is None or not isinstance(result_data, pd.DataFrame) or result_data.empty:
            # print("Debug: Scorer received None or empty DataFrame.") # Debug print
            return -np.inf
        if 'pnl' not in result_data.columns:
             print("Warning: 'pnl' column not found in trades DataFrame. Cannot score.")
             return -np.inf

        # --- Date Column Handling ---
        date_col = None
        if 'exit_time' in result_data.columns:
            date_col = 'exit_time'
        elif 'date' in result_data.columns:
             date_col = 'date'

        trades_df_processed = result_data.copy() # Work on a copy

        if date_col:
            try:
                # Ensure date column is suitable datetime type
                if not pd.api.types.is_datetime64_any_dtype(trades_df_processed[date_col]):
                    trades_df_processed[date_col] = pd.to_datetime(trades_df_processed[date_col], errors='coerce')
                    if trades_df_processed[date_col].isnull().any():
                         print(f"Warning: Could not convert all values in '{date_col}' to datetime.")
                         if self._requires_equity_curve(): return -np.inf

                # Make timezone-naive for consistent daily aggregation
                if trades_df_processed[date_col].dt.tz is not None:
                    trades_df_processed[date_col] = trades_df_processed[date_col].dt.tz_localize(None)

            except Exception as e:
                 print(f"Warning: Error processing date column '{date_col}': {e}")
                 if self._requires_equity_curve(): return -np.inf
        elif self._requires_equity_curve():
             print("Warning: No 'date' or 'exit_time' column found, needed for equity-based scoring.")
             return -np.inf

        # --- Calculate Score ---
        try:
            # Call the specific scoring logic implemented by subclasses
            calculated_score = self._calculate_score(trades_df_processed, initial_capital, date_col)
            # Ensure score is a float and finite, default to -inf otherwise
            if isinstance(calculated_score, (int, float)) and np.isfinite(calculated_score):
                return float(calculated_score)
            else:
                # print(f"Debug: Score calculated as non-finite: {calculated_score}") # Debug print
                return -np.inf
        except Exception as e:
            print(f"Error during scoring calculation: {e}")
            # import traceback
            # print(traceback.format_exc())
            return -np.inf

    @abstractmethod
    def _calculate_score(self, trades_df: pd.DataFrame, initial_capital: float | None, date_col: str | None) -> float:
        """Subclasses implement their specific calculation logic here."""
        pass

    def _requires_equity_curve(self) -> bool:
        """Subclasses override this if they need the equity curve helpers."""
        return False

    # --- Helper method to build equity curve ---
    def _build_equity_curve(self, trades_df: pd.DataFrame, initial_capital: float, date_col: str) -> pd.Series | None:
        """Builds daily equity curve from trades."""
        if date_col not in trades_df.columns:
             print(f"Error: date_col '{date_col}' not found for equity curve.")
             return None
        if trades_df[date_col].isnull().any():
            print(f"Warning: Null values found in date column '{date_col}', equity curve might be incorrect.")
            trades_df = trades_df.dropna(subset=[date_col])
            if trades_df.empty: return None

        try:
             # Ensure dates are just dates (no time component) for daily grouping
             trades_df['pnl_date'] = trades_df[date_col].dt.date
             daily_pnl = trades_df.groupby('pnl_date')['pnl'].sum()

             if daily_pnl.empty:
                  # print("Debug: No PNL data after grouping by date.") # Debug print
                  return None

             # Convert index back to datetime for date range generation
             daily_pnl.index = pd.to_datetime(daily_pnl.index)

             start_date = daily_pnl.index.min()
             end_date = daily_pnl.index.max()

             if pd.isna(start_date) or pd.isna(end_date):
                 print("Error: Invalid start/end date after PnL aggregation.")
                 return None

             # Use daily frequency ('D') for standard metrics calc
             # Handle cases where start and end might be the same day after aggregation
             if start_date == end_date:
                  date_range = pd.DatetimeIndex([start_date])
             else:
                  date_range = pd.date_range(start=start_date, end=end_date, freq='D')

             full_pnl = daily_pnl.reindex(date_range, fill_value=0.0)
             equity_curve = initial_capital + full_pnl.cumsum()
             
             if not np.isfinite(equity_curve).all():
                 print("Warning: Non-finite values found in equity curve.")
                 # Handle or return None, depending on desired behavior
                 # equity_curve = equity_curve.fillna(method='ffill').fillna(initial_capital) # Example handling

             return equity_curve

        except Exception as e:
            print(f"Error building equity curve: {e}")
            # import traceback
            # print(traceback.format_exc())
            return None