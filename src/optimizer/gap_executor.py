from __future__ import annotations

from optimizer.base_executor import BaseStrategyExecutor
from typing import Any, Dict, Callable, Set
from datetime import datetime
import pandas as pd
from typing import Dict, Tuple, List

# ==========================================================================


from datetime import time, timedelta
from typing import Dict, List

import numpy as np
import pandas as pd

# ---------------------------------------------------------------------------
# 1) Gap scan – pre-market hourly (04:00-09:00)                                
# ---------------------------------------------------------------------------

def _scan_ticker_premarket_hourly_range(
    ticker: str,
    start_dt: pd.Timestamp,
    end_dt: pd.Timestamp,
    *,
    get_daily_data,
    get_hourly_data,
    min_gap_pct: float = 5.0,
    max_gap_pct: float = 50.0,
    min_premkt_dvol: float = 250_000,
    premkt_start: time = time(4, 0),
) -> pd.DataFrame:
    """Return one row per qualifying date or empty DataFrame."""

    # Daily closes for prev-close reference
    ddf = get_daily_data(
        ticker,
        start_dt - timedelta(days=1),
        end_dt,
        # get_daily_data returns tz-naive index in your code-base
    )
    if ddf is None or len(ddf) < 2:
        return pd.DataFrame()
    ddf.sort_index(inplace=True)
    ddf["prev_close"] = ddf["close"].shift(1)

    # Hourly bars 04:00-09:00 (bar start < 09:00)
    hdf = get_hourly_data(
        ticker,
        start_dt.replace(hour=premkt_start.hour, minute=0, second=0),
        end_dt.replace(hour=9, minute=0, second=0),
    )
    if hdf is None or hdf.empty:
        return pd.DataFrame()

    hdf = hdf[hdf.index.time < time(9, 0)]
    if hdf.empty:
        return pd.DataFrame()

    hdf["dollar_vol"] = hdf["close"] * hdf["volume"]

    premkt = (
        hdf.groupby(hdf.index.normalize())
        .agg({"high": "max", "dollar_vol": "sum"})
        .rename(columns={"high": "premkt_high", "dollar_vol": "premkt_dollarvol"})
    )

    out = (
        premkt.join(ddf["prev_close"], how="inner")
        .dropna(subset=["prev_close"])
    )
    out["gap_up_pct"] = (out["premkt_high"] - out["prev_close"]) / out["prev_close"] * 100

    mask = (
        (out["gap_up_pct"] >= min_gap_pct) &
        (out["gap_up_pct"] <= max_gap_pct) &
        (out["premkt_dollarvol"] >= min_premkt_dvol)
    )
    res = out.loc[mask]
    if res.empty:
        return pd.DataFrame()

    res = res.reset_index().rename(columns={"index": "date"})
    res["stock"] = ticker
    return res[["date", "stock", "gap_up_pct", "premkt_high", "premkt_dollarvol", "prev_close"]]


def find_gap_candidates_small_caps(
    tickers: List[str],
    start_dt: pd.Timestamp,
    end_dt: pd.Timestamp,
    *,
    get_daily_data,
    get_hourly_data,
    min_gap_pct: float = 5.0,
    max_gap_pct: float = 50.0,
    min_premkt_dvol: float = 250_000,
    max_workers: int = 8,
) -> pd.DataFrame:
    """Parallel wrapper returning all gap-up events in the desired range."""

    from concurrent.futures import ThreadPoolExecutor, as_completed

    events: List[pd.DataFrame] = []
    with ThreadPoolExecutor(max_workers=max_workers) as pool:
        futs = {
            pool.submit(
                _scan_ticker_premarket_hourly_range,
                t,
                start_dt,
                end_dt,
                get_daily_data=get_daily_data,
                get_hourly_data=get_hourly_data,
                min_gap_pct=min_gap_pct,
                max_gap_pct=max_gap_pct,
                min_premkt_dvol=min_premkt_dvol,
            ): t
            for t in tickers
        }
        for f in as_completed(futs):
            try:
                df = f.result()
                if not df.empty:
                    events.append(df)
            except Exception as e:
                print(f"[WARN] {futs[f]} → {e}")

    if not events:
        return pd.DataFrame()

    return pd.concat(events, ignore_index=True).sort_values(["date", "gap_up_pct"], ascending=[True, False])

import concurrent.futures
from tqdm.auto import tqdm

def get_minute_data_for_gap_events_parallel(gap_events_df, pre_market_start_hour=4, post_market_end_hour=20, 
                                           max_workers=None, get_per_minute_data=None):
    """
    Retrieve per-minute data for each gap up event in parallel.
    
    Args:
        gap_events_df: DataFrame containing gap up events with date and stock columns
        pre_market_start_hour: Hour to start collecting data (in EST, 24-hour format)
        post_market_end_hour: Hour to end collecting data (in EST, 24-hour format)
        max_workers: Maximum number of worker processes (defaults to number of CPU cores)
        
    Returns:
        Dictionary with keys as (date, ticker) tuples and values as DataFrames of minute data
    """
    minute_data_dict = {}
    
    def process_single_event(row):
        """Process a single gap event and return the minute data."""
        ticker = row['stock']
        event_date = row['date']
        
        # Create start and end datetime for the data request
        start_datetime = event_date.replace(hour=pre_market_start_hour, minute=0, second=0)
        end_datetime = event_date.replace(hour=post_market_end_hour, minute=0, second=0)
        
        try:
            # Get per-minute data
            minute_data = get_per_minute_data(ticker, start_datetime, end_datetime)
            
            if minute_data is not None and len(minute_data) > 0:
                # Return the data with its key
                key = (event_date.date(), ticker)
                return key, minute_data
                
        except Exception as e:
            print(f"Error retrieving minute data for {ticker} on {event_date.date()}: {e}")
        return None
    
    print("Processing found gap events in parallel...")
    
    # Convert DataFrame to list of records for parallel processing
    records = gap_events_df.to_dict('records')
    
    # Use ThreadPoolExecutor for I/O bound tasks like API calls
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_record = {executor.submit(process_single_event, record): record 
                           for record in records}
        
        # Process as they complete with a progress bar
        for future in tqdm(concurrent.futures.as_completed(future_to_record), 
                          total=len(future_to_record), 
                          desc="Processing Gap Events"):
            result = future.result()
            if result:
                key, data = result
                minute_data_dict[key] = data
    
    print(f"Retrieved minute data for {len(minute_data_dict)} out of {len(gap_events_df)} gap events")
    return minute_data_dict


from datetime import time
from typing import Dict, List

import numpy as np
import pandas as pd

# ---------------------------------------------------------------------------
# Helpers                                                                      
# ---------------------------------------------------------------------------

def simulate_long_gap_strategy(
    gap_df: pd.DataFrame,
    minute_data_dict: Dict[tuple, pd.DataFrame],
    *,
    initial_capital: float = 100_000,
    position_size_pct: float = 0.05,
    risk_percent: float = 0.05,
    risk_reward: float = 2.0,
    min_gap_pct: float = 5.0,
    market_open: time = time(9, 30),
    eod_exit: time = time(15, 55),
    entry_session: str = "open",  # "open" or "premarket"
) -> pd.DataFrame:
    """Run the long strategy and return a trade‑log DataFrame."""

    assert entry_session in {"open", "premarket"}, "entry_session must be 'open' or 'premarket'"
    trades: List[dict] = []

    for _, ev in gap_df.iterrows():
        ticker = ev.stock
        d = ev.date.date()
        key = (d, ticker)
        mdf = minute_data_dict.get(key)
        if mdf is None or mdf.empty:
            continue

        prev_close = ev.prev_close
        entry_price = entry_time = None

        if entry_session == "premarket":
            threshold_price = prev_close * (1 + min_gap_pct / 100)
            pre = mdf[mdf.index.time < market_open]
            crossed = pre[pre.high >= threshold_price]
            if not crossed.empty:
                first = crossed.iloc[0]
                entry_time = first.name
                entry_price = first.open  # take liquidity at bar open

        # fallback to 09:30 open if no pre‑market trigger (or if entry_session == "open")
        if entry_session == "open" and entry_price is None:
            open_rows = mdf[mdf.index.time == market_open]
            if open_rows.empty:
                open_rows = mdf[mdf.index.time > market_open]
            if open_rows.empty:
                continue
            open_bar = open_rows.iloc[0]
            entry_time = open_bar.name
            entry_price = open_bar.open

        if entry_price is None or entry_price <= 0:
            continue

        low_so_far = mdf.loc[:entry_time, "low"].min()
        stop_price = low_so_far
        risk = entry_price - stop_price
        if risk <= 0:
            continue

        # Risk-based position sizing
        risk_per_share = risk
        max_risk_amount = initial_capital * risk_percent  # Need to define risk_percent
        risk_based_qty = int(max_risk_amount / risk_per_share)
        
        # Max position size
        max_position_notional = initial_capital * position_size_pct
        max_qty = int(max_position_notional / entry_price)
        
        # Take the lower of the two quantities
        qty = min(risk_based_qty, max_qty)
        if qty <= 0:
            continue
        
        target_price = entry_price + risk_reward * risk
        exit_time = exit_price = exit_reason = None
        post = mdf.loc[entry_time:]
        for ts, bar in post.iloc[1:].iterrows():
            if bar.low <= stop_price:
                exit_time, exit_price, exit_reason = ts, stop_price, "STOP_LOSS"
                break
            if bar.high >= target_price:
                exit_time, exit_price, exit_reason = ts, target_price, f"TP_{int(risk_reward)}R"
                break
            if ts.time() >= eod_exit:
                exit_time, exit_price, exit_reason = ts, bar.close, "EOD_FLAT"
                break

        if exit_time is None:
            last_bar = mdf.iloc[-1]
            exit_time, exit_price, exit_reason = last_bar.name, last_bar.close, "EOD_MISSING"

        pnl = (exit_price - entry_price) * qty
        pnl_pct = pnl / (entry_price * qty) * 100

        trades.append(
            dict(
                stock=ticker,
                date=d,
                entry_time=entry_time,
                entry_price=entry_price,
                entry_quantity=qty,
                stop_price=stop_price,
                target_price=target_price,
                exit_time=exit_time,
                exit_price=exit_price,
                exit_reason=exit_reason,
                pnl=pnl,
                pnl_pct=pnl_pct,
            )
        )

    df = pd.DataFrame(trades)
    if not df.empty:
        df["cumulative_pnl"] = df.pnl.cumsum()
    return df


# ==========================================================================


class LongGapStrategyExecutor(BaseStrategyExecutor):
    """
    Executes the specific workflow for the Long Gap strategy.
    Dependencies like data fetchers are injected via __init__.
    """
    def __init__(self,
                 data_fetchers: Dict[str, Callable],
                 base_tickers: Set[str],
                 initial_capital: float = 100_000):
        """
        Initializes the executor with specific dependencies for this strategy.

        Args:
            data_fetchers: Dict containing callable functions like 'daily', 'hourly', 'minute'.
            base_tickers: The set of tickers to consider for the strategy universe.
            initial_capital: The starting capital for simulations.
        """
        if not all(k in data_fetchers for k in ['daily', 'hourly', 'minute']):
             raise ValueError("data_fetchers dict must contain 'daily', 'hourly', 'minute' keys")
        if not isinstance(base_tickers, set) or not base_tickers:
             raise ValueError("base_tickers must be a non-empty set")

        self.data_fetchers = data_fetchers
        self.base_tickers = base_tickers
        self.initial_capital = initial_capital
        self.max_gap_pct = 500
        print(f"Initialized LongGapStrategyExecutor with {len(base_tickers)} tickers.")

    def run(self, params: Dict[str, Any], start_dt: datetime, end_dt: datetime) -> pd.DataFrame | None:
        """Runs the full simulation workflow for the long gap strategy."""
        # print(f"Debug: LongGapExecutor running with params: {params}") # Debug if needed
        try:
            # --- Step 1: Find Gap Candidates (using params and instance attributes) ---
            gap_df = find_gap_candidates_small_caps(
                tickers=list(self.base_tickers), # Use instance attribute
                start_dt=start_dt, end_dt=end_dt,
                get_daily_data=self.data_fetchers['daily'], # Use instance attribute
                get_hourly_data=self.data_fetchers['hourly'], # Use instance attribute
                min_gap_pct=params.get('min_gap_pct', 5.0),
                max_gap_pct=self.max_gap_pct,
                min_premkt_dvol=params.get('min_premkt_dvol', 250_000)
            )
            if gap_df is None or gap_df.empty:
                 # print("Debug: No gap candidates found.") # Debug if needed
                 return pd.DataFrame(columns=['pnl', 'date']) # Return empty with expected cols

            # --- Step 2: Get Minute Data ---
            minute_data_dict = get_minute_data_for_gap_events_parallel(
                 gap_df,
                 get_per_minute_data=self.data_fetchers['minute']
                 # Pass max_workers etc. if needed
            )
            if not minute_data_dict:
                 # print("Debug: No minute data retrieved.") # Debug if needed
                 return pd.DataFrame(columns=['pnl', 'date'])

            # --- Step 3: Simulate Trades (using params and instance attributes) ---
            trades_df = simulate_long_gap_strategy(
                gap_df=gap_df, minute_data_dict=minute_data_dict,
                initial_capital=self.initial_capital, # Use instance attribute
                position_size_pct=params.get('position_size_pct'),
                risk_percent=params.get('risk_percent'),
                risk_reward=params.get('risk_reward'),
                min_gap_pct=params.get('min_gap_pct'), # Ensure consistency if needed
                entry_session=params.get('entry_session', 'premarket')
                # Add other necessary args
            )
            
            # Ensure returned DataFrame is valid or None
            if trades_df is None:
                # print("Debug: Simulation returned None.") # Debug if needed
                return pd.DataFrame(columns=['pnl', 'date'])
            elif not isinstance(trades_df, pd.DataFrame):
                 print(f"Warning: Simulation did not return a DataFrame (returned {type(trades_df)}).")
                 return None # Indicate failure more clearly
                 
            # Ensure essential columns exist for scorer
            if trades_df.empty:
                 # print("Debug: Simulation returned empty DataFrame.") # Debug if needed
                 # Return it as is, scorer should handle empty
                 return trades_df
                 
            if 'pnl' not in trades_df.columns:
                 print("Warning: trades_df missing 'pnl' column after simulation.")
                 # Decide handling: add dummy column, return None, or let scorer fail?
                 # trades_df['pnl'] = 0 # Example: Add dummy - risky if scorer expects real values
                 return None # Safer to indicate failure

            date_col_exists = 'exit_time' in trades_df.columns or 'date' in trades_df.columns
            if not date_col_exists:
                 print("Warning: trades_df missing 'exit_time' or 'date' column after simulation.")
                 return None # Scorer likely needs this

            return trades_df

        except Exception as e:
             print(f"Error during Long Gap Executor run | Params: {params} | Error: {e}")
             import traceback # More detailed error for debugging
             print(traceback.format_exc())
             return None # Indicate failure