# Dockerfile
# Use an official Python runtime as a parent image. Adjust the version as needed.
FROM python:3.13-slim

# Set the working directory in the container
WORKDIR /app

# If you have a requirements.txt, copy it and install dependencies.
# Otherwise, remove the next two lines and install packages using your preferred method.
COPY requirements.txt . 
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of your application code to the container
COPY . /app