#!/bin/bash

cd /home/<USER>/w/backtest/

source .venv/bin/activate

# Check if today is a market day
is_market_day=$(python -c '
import pandas_market_calendars as mcal
import datetime
nyse = mcal.get_calendar("NYSE")
today = datetime.datetime.now().strftime("%Y-%m-%d")
print(len(nyse.valid_days(start_date=today, end_date=today)))
')

supervisorctl restart polygon_mux_server
supervisorctl restart theta_mux_server
supervisorctl restart control_plane_hub

sleep 10 # sleep for a bit

if [ "$is_market_day" -gt 0 ]; then
    echo "$(date): Today is a market day. Starting trading processes" >> /home/<USER>/w/backtest/output/logs/trading_schedule.log
    
    # Start all trading processes
    supervisorctl start trading_runners:*
    
    echo "$(date): All trading processes started" >> /home/<USER>/w/backtest/output/logs/trading_schedule.log
else
    echo "$(date): Today is NOT a market day. Not starting trading processes" >> /home/<USER>/w/backtest/output/logs/trading_schedule.log
fi