#!/bin/bash

exit 0;

python -m backtest_runner.run --strategy reversal_short_eod_strategy_v2 --risk 0.05 --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 50000000  --start-date 2025-02-01 --end-date 2025-03-08

python -m backtest_runner.run --strategy simple_vwap_trend_strategy --risk 1 --tickers TQQQ --start-date 2025-02-01 --end-date 2025-03-08

python -m backtest_runner.run --strategy reversal_short_eod_strategy_v2 --risk 0.05 --tickers GV --start-date 2025-02-01 --end-date 2025-03-08 

python -m backtest_runner.run --strategy opening_range_breakout_strategy --risk 0.05 --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 50000000  --start-date 2025-02-01 --end-date 2025-03-01

python -m backtest_runner.run --strategy opening_range_breakout_strategy --risk 0.05 --tickers ATCH --start-date 2025-02-02 --end-date 2025-03-01

time python -m backtest_runner.run --strategy opening_range_breakout_strategy --risk 0.05 --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 50000000  --start-date 2025-01-01 --end-date 2025-03-01

time python -m backtest_runner.run --strategy simple_vwap_trend_strategy --risk 1 --tickers TQQQ --start-date 2025-01-01 --end-date 2025-03-01

python -u -m live_runner.ticker_event_buy_live_runner 2>&1 | tee mar_10_citrini_output

python -u -m backtest_runner.run --strategy reversal_short_eod_strategy --risk 0.05 --tickers GV --start-date 2025-02-01 --end-date 2025-03-08 --trade-session full

python -u -m backtest_runner.run --strategy reversal_short_eod_strategy \
        --risk 0.05 --tickers GV --start-date 2025-02-01 --end-date 2025-03-08 

python -u -m backtest_runner.run --strategy reversal_short_eod_strategy_v2 \
        --risk 0.05 --tickers GV --start-date 2025-02-01 --end-date 2025-03-08 

python -u -m live_runner.reversal_short_eod_live_runner 2>&1 | tee mar_10_small_cap_output

python -u -m live_runner.simple_vwap_trend_live_runner 2>&1 | tee mar_10_vwap_output

python -m backtest_runner.run --strategy reversal_short_eod_strategy_v2 \
            --risk 0.05 --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 50000000  \
            --start-date 2025-01-01 --end-date 2025-03-10

time python -m backtest_runner.run --strategy simple_vwap_trend_strategy --risk 1 \
        --tickers TQQQ --start-date 2020-01-01 --end-date 2025-03-10

python -m backtest_runner.run --strategy reversal_short_eod_strategy_v2 \
            --risk 0.05 --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 50000000  \
            --start-date 2020-01-01 --end-date 2025-03-11

python -m backtest_runner.run --strategy reversal_short_eod_strategy_v2 \
            --risk 0.05 --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 50000000  \
            --start-date 2020-03-11 --end-date 2025-03-12

time python -u -m backtest_runner.run --strategy orb_strategy --risk .01 \
        --tickers TQQQ --start-date 2025-03-16 --end-date 2025-03-21  --capital 20000

time python -u -m backtest_runner.run --strategy orb_strategy --risk .01 \
                --tickers TQQQ --start-date 2025-03-01 --end-date 2025-03-07

sleep 6h && python -u -m live_runner.orb_live_runner 2>&1 | tee output/logs/mar_17_orb_output


time python -u -m backtest_runner.run --strategy para_short_strategy --risk .02 \
                --tickers MSTR,RGTI,QBTS --start-date 2025-01-01 --end-date 2025-03-19 

time python -u -m backtest_runner.run --strategy para_short_strategy --risk .02 \
             --ticker-selection dynamic --min-market-cap 500000000 --max-market-cap 5000000000 \
            --start-date 2025-01-01 --end-date 2025-03-19

            
python -u -m live_runner.simple_vwap_trend_live_runner 2>&1  | tee output/logs/mar_17_vwap_output

time python -u -m backtest_runner.run --strategy para_short_strategy --risk .025 --max-position-size .10 \
             --ticker-selection dynamic --min-market-cap 500000000 --max-market-cap 5000000000 \
            --start-date 2025-03-01 --end-date 2025-03-21


python -u -m tickers.scan_cli --preload-months 1 --criteria strategies.para_short_strategy

python  scripts/supervisor.py "python -u -m live_runner.orb_live_runner" --log-suffix "orb_output" 



python scripts/supervisor.py "python -u -m live_runner.simple_vwap_trend_live_runner" --log-suffix "vwap_output"

python scripts/supervisor.py "python -u -m live_runner.ticker_event_buy_live_runner" --log-suffix "citrini_output"

python  scripts/supervisor.py "python -u -m live_runner.orb_live_runner" --log-suffix "orb_output" 

python  scripts/supervisor.py "python -u -m live_runner.para_short_live_runner" --log-suffix "para_output"

# Daily

python -u -m backtest_runner.run --strategy orb_strategy --risk .01 --capital 20000 \
        --tickers TQQQ --start-date 2025-03-24 --end-date 2025-03-24 


python -u -m backtest_runner.run --strategy para_short_strategy --risk .025 --max-position-size .10 \
             --ticker-selection dynamic --min-market-cap 500000000 --max-market-cap 5000000000 \
            --start-date 2025-03-24 --end-date 2025-03-25

python -u -m backtest_runner.run --strategy orb_strategy --risk .01 --capital 100000 \
        --tickers TQQQ --start-date 2025-03-01 --end-date 2025-03-25 

python -u -m backtest_runner.run --strategy para_short_twap_strategy --risk 0.05 --max-position-size .10 \
             --ticker-selection dynamic --min-market-cap 500000000 --max-market-cap 5000000000 \
            --start-date 2025-03-01 --end-date 2025-03-25

# 5 million to 500 million
python -u -m backtest_runner.run --strategy opb_short_strategy --risk 0.1 --max-position-size .1 \
             --ticker-selection dynamic --min-market-cap 5000000 --max-market-cap 500000000 \
            --start-date 2025-01-01 --end-date 2025-03-25 --trade-session full

python -u -m backtest_runner.run --strategy orb_breakout_strategy --risk .01 --capital 100000 \
        --tickers TQQQ --start-date 2024-01-01 --end-date 2025-03-28 

time python -u -m backtest_runner.run --strategy orb_strategy --risk 0.01 --max-position-size 1 --capital 100000 \
        --tickers TQQQ --start-date 2020-01-01 --end-date 2025-04-04

time python -u -m backtest_runner.run --strategy orb_strategy --risk 0.025 --max-position-size 1 --capital 100000 \
        --tickers TSLL --start-date 2025-01-01 --end-date 2025-04-04

## (Incubation)
#  ATR Gap Up from 50M to 500M
time python -u -m backtest_runner.run --strategy atr_gap_up_strategy --risk 0.1 --max-position-size .1 \
             --ticker-selection dynamic --min-market-cap 50000000 --max-market-cap 500000000 \
            --start-date 2025-01-01 --end-date 2025-03-25 --trade-session full

time python -u -m backtest_runner.run --strategy atr_gap_up_strategy --risk 0.1 --max-position-size .1 \
             --ticker-selection dynamic --min-market-cap 50000000 --max-market-cap 500000000 \
            --start-date 2025-01-01 --end-date 2025-02-01 --trade-session full

# ATR Gap Up from 2M to 50M (Quicker feedback) - 1 week - small cap
python -u -m backtest_runner.run --strategy atr_gap_up_strategy --risk 0.1 --max-position-size .1 \
             --ticker-selection dynamic --min-market-cap 2000000 --max-market-cap 50000000 \
            --start-date 2025-03-01 --end-date 2025-03-07 --trade-session full


python -u -m backtest_runner.run --strategy opb_short_strategy --risk 0.1 --max-position-size .1 \
             --tickers YJ --min-market-cap 1000000 --max-market-cap 500000000 \
            --start-date 2025-04-10 --end-date 2025-04-15 --trade-session full --no-log-file

python -u -m backtest_runner.run --strategy opb_short_strategy --risk 0.1 --max-position-size .1 \
             --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 500000000 \
            --start-date 2025-04-10 --end-date 2025-04-15 --trade-session full


python -u -m backtest_runner.run --strategy premarket_gap_short --risk 0.05 --max-position-size .05 \
             --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 500000000 \
            --start-date 2025-01-01 --end-date 2025-01-15 --trade-session full

python -u -m backtest_runner.run --strategy intraday_noise_momentum_strategy --risk 0.03 --max-position-size 1 \
             --tickers TQQQ  \
            --start-date 2025-01-01 --end-date 2025-05-01 --trade-session rth --no-log-file