#!/usr/bin/env python3
"""

CLI utility to extend an existing DataBento minute-level CSV in place.

Usage
-----
$ python update_databento_csv.py \
    --csv market_data/static/nq_data_2010_06_07_to_2025_05_01.csv \
    --dataset GLBX.MDP3 \
    --symbol NQ.v.0               # or ES.v.0, etc.
    --schema ohlcv-1m             # default; leave off for minutes
    --tz_from UTC                 # timezone of ts_event in CSV
    --ts_col ts_event             # column holding timestamps
    [--api_key <key>]             # else read DATABENTO_API_KEY env
    
./scripts/download_databento_csv.py --csv market_data/static/nq_data_2010_06_07_to_2025_05_01.csv \
        --dataset GLBX.MDP3 --symbol NQ.c.0

"""

import argparse
import os
from datetime import datetime, timezone, timedelta
import dotenv

import pandas as pd
import databento as db
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
)

dotenv.load_dotenv()

def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser(description="Append latest DataBento OHLCV-1m data to a CSV")
    p.add_argument("--csv", required=True, help="Path to existing minute CSV file")
    p.add_argument("--dataset", required=True, help="DataBento dataset code, e.g. GLBX.MDP3")
    p.add_argument("--symbol", required=True, help='Instrument symbol, e.g. "NQ.c.0"')
    p.add_argument("--schema", default="ohlcv-1m",
                   help='DataBento schema (default "ohlcv-1m")')
    p.add_argument("--stype_in", default="continuous",
                   help='Input symbol type (default "instrument_id"; use "continuous" for *.c.0)')
    p.add_argument("--tz_from", default="UTC",
                   help="Timezone of the ts_event column in the CSV (default UTC)")
    p.add_argument("--ts_col", default="ts_event",
                   help='Timestamp column name in CSV (default "ts_event")')
    p.add_argument("--api_key", default=os.getenv("DATABENTO_API_KEY"),
                   help="DataBento API key (env DATABENTO_API_KEY if omitted)")
    return p.parse_args()


def get_last_timestamp(csv_path: str, ts_col: str, tz: str) -> pd.Timestamp:
    """Return the LAST timestamp (tz-aware) found in the CSV."""
    df_tail = pd.read_csv(
        csv_path,
        usecols=[ts_col],
        parse_dates=[ts_col],
        skiprows=lambda i: i < 0  # noop so we can use tail() next
    ).tail(1)
    if df_tail.empty:
        raise RuntimeError(f"{csv_path} contains no rows.")
    ts = df_tail.iloc[0][ts_col]
    if ts.tzinfo is None:
        ts = ts.tz_localize(tz)
    return ts


def fetch_new_data(
    client: db.Historical,
    dataset: str,
    schema: str,
    symbol: str,
    stype_in: str,
    start_ts: pd.Timestamp,
    end_ts: pd.Timestamp,
) -> pd.DataFrame:
    """Download minute bars between start_ts and end_ts (inclusive)."""
    data = client.timeseries.get_range(
        dataset=dataset,
        schema=schema,
        symbols=symbol,
        stype_in=stype_in,
        start=start_ts.isoformat(),
        end=end_ts.isoformat(),
    )
    return data.to_df()


def main():
    args = parse_args()
    logging.info(f"Starting download_databento_csv with args: {args}")

    if not args.api_key:
        raise SystemExit("API key missing: pass --api_key or set DATABENTO_API_KEY")

    # 1️⃣ locate last bar already in the file
    logging.info(
        f"Locating last timestamp in {args.csv} (ts_col={args.ts_col}, tz={args.tz_from})"
    )
    last_ts = get_last_timestamp(args.csv, args.ts_col, args.tz_from)
    logging.info(f"Last timestamp found: {last_ts}")

    next_min = last_ts + pd.Timedelta(minutes=1)
    logging.info(f"Next minute to fetch: {next_min}")

    now_utc = pd.Timestamp(datetime.now(timezone.utc)).floor("T") - pd.Timedelta(days=1)
    logging.info(f"Current UTC time floored to minute: {now_utc}")

    if next_min > now_utc:
        logging.info("CSV already up to date.")
        return

    # 2️⃣ pull fresh data
    logging.info(
        f"Fetching new data: dataset={args.dataset}, schema={args.schema}, "
        f"symbol={args.symbol}, start={next_min}, end={now_utc}"
    )
    client = db.Historical(args.api_key)
    df_new = fetch_new_data(
        client,
        dataset=args.dataset,
        schema=args.schema,
        symbol=args.symbol,
        stype_in=args.stype_in,
        start_ts=next_min,
        end_ts=now_utc,
    )

    if isinstance(df_new.index, pd.DatetimeIndex):
       df_new = df_new.reset_index().rename(columns={df_new.index.name: args.ts_col})

    if df_new.empty:
        logging.info("No new rows returned by DataBento.")
        return

    # 3️⃣ ensure same column order as existing CSV
    df_new = df_new[
        [args.ts_col, "rtype", "publisher_id", "instrument_id",
         "open", "high", "low", "close", "volume", "symbol"]
    ]

    # 4️⃣ append, deduplicate, sort
    df_new.to_csv(args.csv, mode="a", header=False, index=False)
    logging.info(f"Appended {len(df_new)} new rows to {args.csv}")


if __name__ == "__main__":
    main()
