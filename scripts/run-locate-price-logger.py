#!/usr/bin/env python3
"""
Usage:
    python scripts/track_locate_prices.py --account <ACCT> --mpid <MPID> \
           --creds clearstreet-creds.json
"""

import argparse, asyncio, os, logging
from datetime import datetime
from dateutil.relativedelta import relativedelta

from brokers.clear_street_broker import ClearStreetBroker
from locates.locate_price_logger import LocatePriceLogger
from tickers.ticker_discover_using_scan_criteria import TickerDiscoverUsingScanCriteria
from tickers.ticker_info_scan_criteria import TickerInfoScanCriteria, TickerType
from strategies.opb_short_strategy import OPBShortStrategy
from marketdata.market_data_builder import MarketDataBuilder
from tools.clock import Clock
from log_utils import configure_basic_logging

async def orchestrate(args):
    # Set up logging
    log_dir = os.path.join("output", "logs")
    os.makedirs(log_dir, exist_ok=True)
    configure_basic_logging(level=logging.INFO, 
                            log_file=os.path.join(log_dir, "track_locate_prices.log"))
    logger = logging.getLogger(__name__)
    
    logger.info(f"Starting locate price tracker with account {args.account}")
    
    # ------------------------------------------------ broker / logger
    broker = ClearStreetBroker(
        account_id=args.account,
        mpid=args.mpid,
        credentials_file_path=args.creds,
        environment=args.env,
        load_existing_locates=False,      # we only need quotes, not inventory
    )
    logger.info(f"Initialized ClearStreetBroker in {args.env} environment")
    
    locate_logger = LocatePriceLogger(broker, csv_path=args.out)
    logger.info(f"Initialized LocatePriceLogger, writing to {args.out}")

    # ------------------------------------------------ ticker discovery
    clock = Clock()
    
    # Set up market data components similar to opb_short_live_runner
    live_data_builder = (MarketDataBuilder(clock=clock)
                         .with_trade_session('full'))
    
    # Use data from ~2 months ago for historical analysis
    two_months_ago = datetime.now() - relativedelta(months=2)
    disk_market_data = (MarketDataBuilder()
                        .with_disk_data(start_date=two_months_ago)
                        .build_market_data())
    
    bulk_ohlc = live_data_builder.build_bulk_ohlc()
    
    # Use the OPBShortStrategy as the criteria, wrapped in market cap filter
    market_cap_criteria = TickerInfoScanCriteria(
        min_market_cap=1000000,  # 1M
        max_market_cap=500000000,  # 500M
        allowed_ticker_types={TickerType.COMMON_STOCK},
        delegate_criteria=OPBShortStrategy(entry_window_minutes=5)
    )
    logger.info("Initialized ticker discovery criteria")

    discover = TickerDiscoverUsingScanCriteria(
        disk_market_data=disk_market_data,
        theta_bulk_ohlc=bulk_ohlc,
        criteria=market_cap_criteria,
        max_run_hours=1
    )
    logger.info("Initialized ticker discovery service")

    # task registry so we don't start the same symbol twice
    running = {}

    logger.info("Starting ticker discovery...")
    async for df in discover.discover():
        for sym in df["ticker"]:
            if sym not in running:
                logger.info(f"📈 Starting locate-price log for {sym}")
                running[sym] = asyncio.create_task(locate_logger.run_forever(sym))

    # never returns; cancel with Ctrl-C
    await asyncio.gather(*running.values())

# -------------------- entry-point ----------------------------------
if __name__ == "__main__":
    p = argparse.ArgumentParser()
    p.add_argument("--account", required=True)
    p.add_argument("--mpid",    required=True)
    p.add_argument("--creds",   help="JSON {client_id, client_secret}")
    p.add_argument("--token",   help="Pass token directly instead of creds")
    p.add_argument("--env", default="sandbox", choices=["sandbox", "production"])
    p.add_argument("--out", default="data/locate_prices.csv")
    args = p.parse_args()

    try:
        asyncio.run(orchestrate(args))
    except KeyboardInterrupt:
        print("\nStopped by user")