#!/bin/bash

# Get current date in PDT/PST (Pacific Time)
current_date=$(TZ=America/Los_Angeles date +%Y-%m-%d)

# First command with current date as both start and end date
# python -u -m backtest_runner.run --strategy orb_strategy --risk 1 --capital 5000 \
#         --tickers TQQQ --start-date "$current_date" --end-date "$current_date"

# Second command with current date as both start and end date
# python -u -m backtest_runner.run --strategy opb_short_strategy --risk 0.05 --max-position-size .05 --capital 10000 \
#         --ticker-selection dynamic --min-market-cap 1000000 --max-market-cap 500000000 \
#         --start-date "$current_date" --end-date "$current_date" --trade-session full