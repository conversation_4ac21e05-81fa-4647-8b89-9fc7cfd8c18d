#!/usr/bin/env python3
import argparse
import os
import subprocess
import sys
import re

# --- Configuration Constants ---
# You can modify these paths if your setup is different.
SUPERVISOR_CONF_DIR = "/etc/supervisor/conf.d"
GROUP_CONF_FILE_NAME = "live_trading_runners.conf"
PROJECT_DIR = "/home/<USER>/w/backtest"
LOG_DIR = os.path.join(PROJECT_DIR, "output/logs")
VENV_PATH = os.path.join(PROJECT_DIR, ".venv/bin")
USER = "abhigna"

# This is the template for the new runner's configuration file.
CONF_TEMPLATE = """[program:{program_name}]
command=bash -c "cd {project_dir} && source {venv_path}/activate && python -u -m live_runner.{module_name}"
directory={project_dir}
user={user}
autostart=true
autorestart=unexpected
stopasgroup=true
killasgroup=true
startretries=3
redirect_stderr=true
stdout_logfile={log_file_path}
environment=PATH="{venv_path}:%(ENV_PATH)s"
"""

def _get_group_conf_path():
    """Returns the full path to the group config file."""
    return os.path.join(SUPERVISOR_CONF_DIR, GROUP_CONF_FILE_NAME)

def _read_group_programs():
    """Reads the list of enabled programs from the group config file."""
    group_conf_path = _get_group_conf_path()
    if not os.path.exists(group_conf_path):
        return []
    
    with open(group_conf_path, 'r') as f:
        content = f.read()
    
    match = re.search(r"^programs\s*=\s*(.*)", content, re.MULTILINE)
    if not match:
        return []
        
    programs_str = match.group(1).strip()
    return [p.strip() for p in programs_str.split(',') if p.strip()]

def _write_group_programs(programs: list):
    """Writes the list of programs back to the group config file."""
    group_conf_path = _get_group_conf_path()
    
    with open(group_conf_path, 'r') as f:
        lines = f.readlines()

    updated_lines = []
    was_updated = False
    for line in lines:
        if line.strip().startswith("programs="):
            line = f"programs={','.join(programs)}\n"
            was_updated = True
        updated_lines.append(line)

    # If the programs line didn't exist, add it.
    if not was_updated:
        updated_lines.append(f"\nprograms={','.join(programs)}\n")

    with open(group_conf_path, 'w') as f:
        f.writelines(updated_lines)
    print(f"...group file '{GROUP_CONF_FILE_NAME}' updated successfully.")

def create_action(args):
    """Action to create a new supervisor config file."""
    program_name = args.module_name
    conf_file_name = f"{program_name}.conf"
    conf_file_path = os.path.join(SUPERVISOR_CONF_DIR, conf_file_name)
    log_file_path = os.path.join(LOG_DIR, f"supervisor_{program_name}.log")
    reference_conf_file = _get_group_conf_path()

    print(f"--- Creating new runner config for: {program_name} ---")

    # Safety Checks
    if not os.path.exists(reference_conf_file):
        print(f"Error: Reference group file not found at {reference_conf_file}", file=sys.stderr)
        sys.exit(1)

    if os.path.exists(conf_file_path):
        print(f"Error: Config file already exists at {conf_file_path}", file=sys.stderr)
        sys.exit(1)
        
    os.makedirs(LOG_DIR, exist_ok=True)

    print(f"Creating new config file: {conf_file_path}")
    conf_content = CONF_TEMPLATE.format(
        program_name=program_name,
        module_name=args.module_name,
        project_dir=PROJECT_DIR,
        venv_path=VENV_PATH,
        user=USER,
        log_file_path=log_file_path
    ).strip()

    try:
        with open(conf_file_path, 'w') as f:
            f.write(conf_content + '\n')
        
        print(f"Copying permissions from {reference_conf_file}...")
        subprocess.run(['chmod', '--reference', reference_conf_file, conf_file_path], check=True)
        subprocess.run(['chown', '--reference', reference_conf_file, conf_file_path], check=True)
        
        print("\n✅ --- Success! --- ✅")
        print(f"Created '{conf_file_path}'.")
        print(f"To enable it, run: sudo {sys.argv[0]} enable {program_name}")

    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        if os.path.exists(conf_file_path):
            os.remove(conf_file_path)
        sys.exit(1)

def enable_action(args):
    """Action to enable a runner by adding it to the group."""
    program_name = args.module_name
    conf_file_path = os.path.join(SUPERVISOR_CONF_DIR, f"{program_name}.conf")
    
    print(f"--- Enabling runner: {program_name} ---")
    
    if not os.path.exists(conf_file_path):
        print(f"Error: Config file '{program_name}.conf' not found.", file=sys.stderr)
        print(f"Create it first by running: sudo {sys.argv[0]} create {program_name}", file=sys.stderr)
        sys.exit(1)
        
    enabled_programs = _read_group_programs()
    
    if program_name in enabled_programs:
        print(f"'{program_name}' is already enabled. No changes made.")
        return

    enabled_programs.append(program_name)
    _write_group_programs(enabled_programs)
    
    print("\n✅ --- Success! --- ✅")
    print(f"Enabled '{program_name}'.")
    print("Run the following commands to apply changes:")
    print("   sudo supervisorctl reread")
    print("   sudo supervisorctl update")

def disable_action(args):
    """Action to disable a runner by removing it from the group."""
    program_name = args.module_name
    print(f"--- Disabling runner: {program_name} ---")
    
    enabled_programs = _read_group_programs()
    
    if program_name not in enabled_programs:
        print(f"'{program_name}' is not currently enabled. No changes made.")
        return
        
    enabled_programs.remove(program_name)
    _write_group_programs(enabled_programs)
    
    print("\n✅ --- Success! --- ✅")
    print(f"Disabled '{program_name}'. Its .conf file has been kept.")
    print("Run the following commands to apply changes:")
    print("   sudo supervisorctl reread")
    print("   sudo supervisorctl update")

def list_action(args):
    """Action to list all available and enabled/disabled runners."""
    print("--- Listing Supervisor Runners ---")
    
    enabled_programs = set(_read_group_programs())
    
    available_programs = set()
    for filename in os.listdir(SUPERVISOR_CONF_DIR):
        if filename.endswith('.conf') and filename != GROUP_CONF_FILE_NAME:
            available_programs.add(filename[:-5]) # Remove .conf extension
            
    disabled_programs = available_programs - enabled_programs
    
    print("\n🟢 Enabled Runners:")
    if enabled_programs:
        for prog in sorted(list(enabled_programs)):
            print(f"  - {prog}")
    else:
        print("  (None)")
        
    print("\n⚪️ Disabled Runners (Available to be enabled):")
    if disabled_programs:
        for prog in sorted(list(disabled_programs)):
            print(f"  - {prog}")
    else:
        print("  (None)")
        
    untracked_programs = enabled_programs - available_programs
    if untracked_programs:
        print("\n⚠️  Warning: The following enabled runners do not have a .conf file:")
        for prog in sorted(list(untracked_programs)):
            print(f"  - {prog}")

if __name__ == "__main__":
    if os.geteuid() != 0:
        print("This script modifies files in /etc and must be run as root.", file=sys.stderr)
        print(f"Usage: sudo {sys.argv[0]} <command> [args]", file=sys.stderr)
        sys.exit(1)

    parser = argparse.ArgumentParser(
        description="A CLI tool to manage Supervisor runner configurations.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    subparsers = parser.add_subparsers(dest='command', required=True, help='Available commands')

    # Create command
    parser_create = subparsers.add_parser('create', help='Create a new runner .conf file.')
    parser_create.add_argument('module_name', help='The name of the runner module (e.g., fast_scalp_live_runner).')
    parser_create.set_defaults(func=create_action)

    # Enable command
    parser_enable = subparsers.add_parser('enable', help='Enable a runner by adding it to the group.')
    parser_enable.add_argument('module_name', help='The name of the runner to enable.')
    parser_enable.set_defaults(func=enable_action)

    # Disable command
    parser_disable = subparsers.add_parser('disable', help='Disable a runner by removing it from the group.')
    parser_disable.add_argument('module_name', help='The name of the runner to disable.')
    parser_disable.set_defaults(func=disable_action)

    # List command
    parser_list = subparsers.add_parser('list', help='List all enabled and available runners.')
    parser_list.set_defaults(func=list_action)

    args = parser.parse_args()
    args.func(args)
