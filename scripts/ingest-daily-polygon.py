#!/usr/bin/env python3

import argparse
import pandas as pd
import numpy as np
from datetime import datetime, date, time, timedelta
import pytz
import json
from pathlib import Path
import shutil
import logging
import os
import re
import tempfile
from typing import Optional, List, Tuple
import boto3
from botocore.config import Config
import traceback


# --- Configuration ---
METADATA_FILENAME = "metadata.json"
SYMBOLS_FILENAME = "symbols.json"
EXPECTED_DATE_FORMAT = r"^\d{4}-\d{2}-\d{2}" # Regex for YYYY-MM-DD at start of filename
# Define expected columns and dtypes from Polygon format
POLYGON_COLUMNS = {
    'ticker': str,
    'volume': float,
    'open': float,
    'close': float,
    'high': float,
    'low': float,
    'window_start': np.int64, # Read as integer first
    'transactions': float
}
# Use US/Eastern for consistency as market data is typically aligned there
TARGET_TIMEZONE = pytz.timezone('US/Eastern')
# Polygon S3 configuration
POLYGON_BUCKET = 'flatfiles'
POLYGON_PREFIX = 'us_stocks_sip/day_aggs_v1'
POLYGON_ENDPOINT = 'https://files.polygon.io'

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Helper Functions ---

def parse_date_from_filename(filename: Path) -> Optional[date]:
    """Extracts YYYY-MM-DD date from the start of a filename."""
    match = re.match(EXPECTED_DATE_FORMAT, filename.name)
    if match:
        try:
            return date.fromisoformat(match.group(0))
        except ValueError:
            logger.warning(f"Could not parse date from filename '{filename.name}' despite matching format.")
            return None
    else:
        logger.debug(f"Filename '{filename.name}' does not match expected date format '{EXPECTED_DATE_FORMAT}...'.")
        return None

def load_json_safe(filepath: Path, default: dict) -> dict:
    """Loads JSON from a file, returning default if file not found or invalid."""
    if not filepath.exists():
        logger.info(f"Metadata file not found: {filepath}. Initializing.")
        return default
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
            logger.info(f"Successfully loaded metadata from {filepath}")
            return data
    except json.JSONDecodeError:
        logger.warning(f"Could not decode JSON from {filepath}. File might be corrupt. Using default.")
        return default
    except Exception as e:
        logger.error(f"Error loading JSON from {filepath}: {e}. Using default.")
        return default

def save_json_atomic(data: dict, filepath: Path):
    """Saves data to JSON file atomically using a temporary file."""
    try:
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with tempfile.NamedTemporaryFile('w', dir=filepath.parent, delete=False) as temp_f:
            json.dump(data, temp_f, indent=4, sort_keys=True)
            temp_path = Path(temp_f.name)
        os.replace(temp_path, filepath)
        logger.info(f"Successfully saved metadata to {filepath}")
    except Exception as e:
        logger.error(f"Failed to save JSON atomically to {filepath}: {e}")
        if 'temp_path' in locals() and temp_path.exists():
            try:
                temp_path.unlink()
            except OSError:
                pass

def datetime_to_iso(dt: Optional[datetime]) -> Optional[str]:
    """Converts a timezone-aware datetime to ISO 8601 string."""
    return dt.isoformat() if dt else None

def iso_to_datetime(iso_str: Optional[str]) -> Optional[datetime]:
    """Converts an ISO 8601 string back to a timezone-aware datetime."""
    if not iso_str:
        return None
    try:
        return datetime.fromisoformat(iso_str)
    except ValueError:
        logger.warning(f"Could not parse ISO date string: {iso_str}")
        return None

def derive_date_from_window_start(df: pd.DataFrame) -> Optional[date]:
    """
    Derives a single, consistent date from the 'window_start' column.
    Returns None if DataFrame is empty, dates are inconsistent, or column missing.
    """
    if df.empty:
        logger.warning("Cannot derive date from window_start: DataFrame is empty.")
        return None
    if 'window_start' not in df.columns:
        logger.error("Cannot derive date: 'window_start' column missing.")
        return None

    try:
        # Convert ns -> UTC -> Target Timezone -> Date component
        dates = pd.to_datetime(df['window_start'], unit='ns', utc=True)
        dates_local = dates.dt.tz_convert(TARGET_TIMEZONE)
        file_dates = dates_local.dt.date # Extract just the date part

        # Check for consistency
        if file_dates.nunique() == 1:
            derived_date = file_dates.iloc[0]
            logger.info(f"Derived consistent date {derived_date} from window_start.")
            return derived_date
        else:
            unique_dates = file_dates.unique()
            logger.error(f"Inconsistent dates found in 'window_start' column: {unique_dates}. Cannot determine file date.")
            return None
    except Exception as e:
        logger.error(f"Error deriving date from window_start: {e}")
        return None

# --- Polygon Integration Functions ---

def get_polygon_s3_client():
    """Create and return a boto3 S3 client for Polygon.io."""
    # Use boto3's default credential chain
    session = boto3.Session()
    return session.client(
        's3',
        endpoint_url=POLYGON_ENDPOINT,
        config=Config(signature_version='s3v4'),
    )

def get_available_dates(s3_client, days_back: int) -> List[date]:
    """
    Returns a list of dates with available Polygon data within the last 'days_back' days.
    """
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days_back)
    logger.info(f"Checking for data between {start_date} and {end_date}")
    
    # Get a list of all files in the bucket with the given prefix
    available_dates = []
    
    # For each day we want to check
    current_date = start_date
    while current_date <= end_date:
        year_str = str(current_date.year)
        month_str = f"{current_date.month:02d}"
        date_str = current_date.isoformat()
        
        # Construct the prefix for this specific date
        date_prefix = f"{POLYGON_PREFIX}/{year_str}/{month_str}/{date_str}"
        
        try:
            # Check if any objects exist with this prefix
            response = s3_client.list_objects_v2(
                Bucket=POLYGON_BUCKET,
                Prefix=date_prefix,
                MaxKeys=1
            )
            
            if 'Contents' in response and len(response['Contents']) > 0:
                available_dates.append(current_date)
                logger.info(f"Found data for {date_str}")
            else:
                logger.info(f"No data available for {date_str} (likely weekend or holiday)")
        except Exception as e:
            logger.warning(f"Error checking data for {date_str}: {e}")
        
        current_date += timedelta(days=1)
    
    return available_dates

def download_polygon_file(s3_client, date_to_download: date, temp_dir: Path) -> Optional[Path]:
    """
    Downloads a Polygon daily file for the specified date.
    Returns the path to the downloaded file or None if download failed.
    """
    year_str = str(date_to_download.year)
    month_str = f"{date_to_download.month:02d}"
    date_str = date_to_download.isoformat()
    
    # Construct the object key
    object_key = f"{POLYGON_PREFIX}/{year_str}/{month_str}/{date_str}.csv.gz"
    
    # Define the local file path
    local_file_path = temp_dir / f"{date_str}.csv.gz"
    
    try:
        logger.info(f"Downloading {object_key} to {local_file_path}")
        s3_client.download_file(POLYGON_BUCKET, object_key, local_file_path)
        logger.info(f"Successfully downloaded data for {date_str}")
        return local_file_path
    except Exception as e:
        logger.error(f"Failed to download data for {date_str}: {e}")
        return None

def process_date_range(dates_to_process: List[date], base_dir: Path, overwrite: bool) -> Tuple[int, int]:
    """
    Downloads and ingests Polygon data for the specified dates.
    Returns a tuple of (successful_days, failed_days).
    """
    # Create S3 client
    s3_client = get_polygon_s3_client()
    
    # Create a temporary directory for downloads
    with tempfile.TemporaryDirectory() as temp_dir_str:
        temp_dir = Path(temp_dir_str)
        logger.info(f"Created temporary directory for downloads: {temp_dir}")
        
        successful = 0
        failed = 0
        
        for date_to_process in dates_to_process:
            try:
                # Download the file
                local_file_path = download_polygon_file(s3_client, date_to_process, temp_dir)
                
                if not local_file_path:
                    logger.error(f"Could not download file for {date_to_process}. Skipping.")
                    failed += 1
                    continue
                
                # Ingest the file
                success = ingest_daily_file(local_file_path, base_dir, overwrite)
                
                if success:
                    successful += 1
                    logger.info(f"Successfully ingested data for {date_to_process}")
                else:
                    failed += 1
                    logger.error(f"Failed to ingest data for {date_to_process}")
            
            except Exception as e:
                failed += 1
                logger.error(f"Unexpected error processing {date_to_process}: {e}")
                logger.debug(traceback.format_exc())
    
    logger.info(f"Temporary directory and downloaded files have been cleaned up")
    return successful, failed

def download_and_ingest_days(days_back: int, base_dir: Path, overwrite: bool) -> Tuple[int, int]:
    """
    Downloads and ingests the last X days of Polygon data.
    Returns a tuple of (successful_days, failed_days).
    """
    # Create S3 client
    s3_client = get_polygon_s3_client()
    
    # Get available dates
    available_dates = get_available_dates(s3_client, days_back)
    
    if not available_dates:
        logger.warning(f"No data found for the last {days_back} days")
        return 0, 0
    
    return process_date_range(available_dates, base_dir, overwrite)

# --- Main Ingestion Logic ---

def ingest_daily_file(input_filepath: Path, base_dir: Path, overwrite: bool):
    """Ingests a single daily Polygon CSV file into the data store with enhanced date handling."""
    logger.info(f"Starting ingestion for: {input_filepath}")
    logger.info(f"Using base directory: {base_dir}")

    # --- 1. Input Validation ---
    if not input_filepath.is_file():
        logger.error(f"Input file not found or is not a file: {input_filepath}")
        return False

    # --- 2. Attempt Date Extraction from Filename ---
    filename_date = parse_date_from_filename(input_filepath)
    if filename_date:
        logger.info(f"Extracted date {filename_date} from filename.")
    else:
        logger.info("Could not extract date from filename, will attempt to derive from file content.")

    # --- 3. Read Input CSV ---
    # Read the CSV regardless of filename date success, as we need it for validation or derivation.
    try:
        compression = 'gzip' if input_filepath.suffix == '.gz' else None
        logger.info(f"Reading CSV: {input_filepath} (compression={compression})")
        df = pd.read_csv(
            input_filepath,
            usecols=POLYGON_COLUMNS.keys(),
            dtype=POLYGON_COLUMNS,
            na_values=[''],
            keep_default_na=True,
            low_memory=False,
            compression=compression
        )
        logger.info(f"Read {len(df)} rows from {input_filepath.name}")

    except FileNotFoundError:
         logger.error(f"Read error: Input file disappeared? {input_filepath}")
         return False
    except pd.errors.EmptyDataError:
        logger.warning(f"Input file is empty: {input_filepath}. Cannot derive date or update metadata.")
        # We can't process an empty file if the filename doesn't give the date.
        # Even if the filename gave a date, we can't validate it. Best to skip.
        return False # Treat as failure since we can't be sure of the date/content.
    except KeyError as e:
         logger.error(f"Missing expected column in {input_filepath}: {e}. Cannot process.")
         return False
    except Exception as e:
        logger.error(f"Failed to read CSV file {input_filepath}: {e}")
        return False

    # --- 4. Determine Canonical Date ---
    window_start_date = derive_date_from_window_start(df)
    canonical_date = None

    if filename_date and window_start_date:
        if filename_date == window_start_date:
            logger.info(f"Filename date ({filename_date}) matches window_start date ({window_start_date}). Using this date.")
            canonical_date = filename_date
        else:
            logger.warning(f"Date mismatch! Filename date ({filename_date}) != window_start date ({window_start_date}). "
                           f"Using window_start date as canonical date for storage.")
            canonical_date = window_start_date
    elif window_start_date:
        logger.info(f"Using date derived from window_start: {window_start_date} (filename lacked date).")
        canonical_date = window_start_date
    elif filename_date:
        # This case should ideally not happen if derive_date_from_window_start worked correctly
        # on a non-empty file, but handle defensively.
        logger.warning(f"Could not derive date from window_start, but filename provided {filename_date}. "
                       f"Proceeding with filename date, but data validation is incomplete.")
        canonical_date = filename_date
    else:
        # Both failed
        logger.error("Could not determine file date from filename or window_start column. Aborting.")
        return False

    logger.info(f"Canonical date for processing: {canonical_date}")
    # Represent canonical date as a datetime at midnight in the target timezone for metadata
    canonical_datetime = TARGET_TIMEZONE.localize(datetime.combine(canonical_date, time.min))


    # --- 5. Setup Paths and Directories using Canonical Date ---
    data_dir = base_dir / "daily_data"
    metadata_dir = base_dir / "metadata"
    metadata_filepath = metadata_dir / METADATA_FILENAME
    symbols_filepath = metadata_dir / SYMBOLS_FILENAME

    target_year_dir = data_dir / str(canonical_date.year)
    target_month_dir = target_year_dir / f"{canonical_date.month:02d}"
    # Standardize the target filename based on the canonical date
    target_filename = f"{canonical_date.isoformat()}{input_filepath.suffix}" # e.g., 2023-03-29.csv.gz
    target_filepath = target_month_dir / target_filename

    # Create directories if they don't exist
    base_dir.mkdir(parents=True, exist_ok=True)
    metadata_dir.mkdir(parents=True, exist_ok=True)
    target_month_dir.mkdir(parents=True, exist_ok=True)
    logger.info(f"Ensured target directory exists: {target_month_dir}")


    # --- 6. Check if Target File Exists ---
    if target_filepath.exists() and not overwrite:
        logger.warning(f"Target file already exists: {target_filepath}. Skipping (use --overwrite to replace).")
        # Even if skipping, we could potentially update metadata if the source file is newer,
        # but for simplicity, we skip entirely if not overwriting.
        return True # Existing data is considered success
    elif target_filepath.exists() and overwrite:
         logger.info(f"Target file {target_filepath} exists and --overwrite specified. Will replace.")


    # --- 7. Load Existing Metadata ---
    default_metadata = {"start_date": None, "end_date": None}
    default_symbols_data = {"symbols": [], "date_ranges": {}}
    metadata = load_json_safe(metadata_filepath, default_metadata)
    symbols_data = load_json_safe(symbols_filepath, default_symbols_data)
    available_symbols_set = set(symbols_data.get("symbols", []))
    symbol_date_ranges = {
        ticker: {
            "start": iso_to_datetime(dates.get("start")),
            "end": iso_to_datetime(dates.get("end"))
        } for ticker, dates in symbols_data.get("date_ranges", {}).items()
    }
    overall_start_date = iso_to_datetime(metadata.get("start_date"))
    overall_end_date = iso_to_datetime(metadata.get("end_date"))


    # --- 8. Update Metadata ---
    metadata_updated = False
    try:
        # Convert potential nan or other non-strings safely
        current_tickers_raw = df['ticker'].unique()
        current_tickers = set()
        for t in current_tickers_raw:
            ticker_str = str(t)
            if ticker_str == 'nan':
                    logger.warning(f"Ignoring problematic 'nan' ticker found in file {input_filepath.name}")
                    continue
            # Add other potential filtering here if needed (e.g., empty strings)
            current_tickers.add(ticker_str)
    except Exception as e:
        logger.error(f"Error processing tickers from DataFrame in {input_filepath.name}: {e}")
        return False # Cannot proceed without valid tickers

    # Update Overall Date Range using canonical_datetime
    if overall_start_date is None or canonical_datetime < overall_start_date:
        overall_start_date = canonical_datetime
        metadata_updated = True
    if overall_end_date is None or canonical_datetime > overall_end_date:
        overall_end_date = canonical_datetime
        metadata_updated = True

    # Update Symbols List
    new_symbols = current_tickers - available_symbols_set
    if new_symbols:
        available_symbols_set.update(new_symbols)
        logger.info(f"Added {len(new_symbols)} new symbols: {', '.join(list(new_symbols)[:5])}{'...' if len(new_symbols)>5 else ''}")
        metadata_updated = True

    # Update Individual Symbol Date Ranges using canonical_datetime
    for ticker in current_tickers:
        ticker_range = symbol_date_ranges.get(ticker)
        ticker_updated = False
        if ticker_range is None:
            symbol_date_ranges[ticker] = {"start": canonical_datetime, "end": canonical_datetime}
            ticker_updated = True
        else:
            # Ensure start/end are datetime objects before comparison
            current_start = ticker_range.get("start")
            current_end = ticker_range.get("end")
            if current_start is None or canonical_datetime < current_start:
                ticker_range["start"] = canonical_datetime
                ticker_updated = True
            if current_end is None or canonical_datetime > current_end:
                ticker_range["end"] = canonical_datetime
                ticker_updated = True
        if ticker_updated:
            metadata_updated = True # Mark overall metadata as updated

    # --- 9. Save Updated Metadata (if changed) ---
    if metadata_updated:
        logger.info("Metadata has changed, saving updates...")
        # Prepare data for JSON saving
        metadata["start_date"] = datetime_to_iso(overall_start_date)
        metadata["end_date"] = datetime_to_iso(overall_end_date)
        symbols_data["symbols"] = sorted(list(available_symbols_set))
        symbols_data["date_ranges"] = {
            ticker: {
                "start": datetime_to_iso(dates.get("start")),
                "end": datetime_to_iso(dates.get("end"))
            } for ticker, dates in symbol_date_ranges.items()
        }

        # Save atomically
        save_json_atomic(metadata, metadata_filepath)
        save_json_atomic(symbols_data, symbols_filepath)
    else:
        logger.info("No metadata changes detected.")

    # --- 10. Copy File to Target Location ---
    try:
        logger.info(f"Copying {input_filepath} to {target_filepath}")
        # copy2 preserves metadata like modification time
        shutil.copy2(input_filepath, target_filepath)
        logger.info("File copy successful.")
        return True
    except Exception as e:
        logger.error(f"Failed to copy file to target location: {e}")
        logger.critical("CRITICAL: Metadata may have been updated, but file copy failed. Data store might be inconsistent.")
        return False


# --- CLI Argument Parsing and Main Execution ---
if __name__ == "__main__":
    import dotenv
    dotenv.load_dotenv()
    
    parser = argparse.ArgumentParser(
        description="Ingest Polygon.io daily stock aggregate data into the market data store.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Create a mutually exclusive group for different input modes
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "--input-file",
        type=Path,
        help="Path to the Polygon daily CSV or CSV.gz file (e.g., 2023-03-29.csv.gz)"
    )
    input_group.add_argument(
        "--days-back",
        type=int,
        help="Download and ingest data for the last N days"
    )
    input_group.add_argument(
        "--backfill",
        nargs=2,
        metavar=('START_DATE', 'END_DATE'),
        help="Backfill data between START_DATE and END_DATE (format: YYYY-MM-DD)"
    )
    
    # Common arguments
    parser.add_argument(
        "--base-dir",
        type=Path,
        default="market_data/polygon",
        help="Base directory for the market data store"
    )
    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="Overwrite destination files if they already exist"
    )
    
    args = parser.parse_args()
    
    # Check for required environment variables for modes that need Polygon access
    if args.days_back or args.backfill:
        if not os.environ.get('AWS_ACCESS_KEY_ID') or not os.environ.get('AWS_SECRET_ACCESS_KEY'):
            logger.error("AWS credentials not provided. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.")
            exit(1)
    
    # Determine execution mode and run appropriate function
    if args.input_file:
        success = ingest_daily_file(args.input_file, args.base_dir, args.overwrite)
        exit(0 if success else 1)
    
    elif args.days_back:
        successful, failed = download_and_ingest_days(
            args.days_back,
            args.base_dir,
            args.overwrite
        )
    
    else:  # args.backfill
        try:
            start_date = datetime.strptime(args.backfill[0], "%Y-%m-%d").date()
            end_date = datetime.strptime(args.backfill[1], "%Y-%m-%d").date()
            
            if start_date > end_date:
                logger.error("Start date must be before or equal to end date")
                exit(1)
            
            # Generate list of dates to process
            date_range = []
            current_date = start_date
            while current_date <= end_date:
                date_range.append(current_date)
                current_date += timedelta(days=1)
            
            logger.info(f"Backfilling data from {start_date} to {end_date} ({len(date_range)} days)")
            successful, failed = process_date_range(date_range, args.base_dir, True)  # Force overwrite for backfill
            
        except ValueError as e:
            logger.error(f"Invalid date format: {e}")
            exit(1)
    
    logger.info(f"Processing complete. Successful: {successful} days, Failed: {failed} days")
    exit(1 if failed > 0 else 0)