#!/usr/bin/env python3
"""
download_binance_monthly_klines.py
---------------------------------
Download & extract Binance “monthly klines” ZIPs for **all** symbols
(Spot or USD-M Futures) over a given date window.

Examples
--------
# All Spot symbols, 1-day candles, from 2019-01 to today
python download_binance_monthly_klines.py --market spot --interval 1d --start 2019-01

# All USD-M futures symbols, 1-h candles, 2022-01..2024-12
python download_binance_monthly_klines.py --market futures --interval 1h \
       --start 2022-01 --end 2024-12 --dest ./binance-um-kline

Tips
----
* Re-run any time – already-processed months are skipped.
* ^C safe: CTRL-C stops cleanly and progress resumes on next run.
* To limit concurrency, change --workers (default = #CPU cores).
"""
import argparse
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

import boto3
import botocore
from botocore import UNSIGNED
from botocore.client import Config
from dateutil.relativedelta import relativedelta
from tqdm import tqdm
import zipfile


# ------------- helpers ----------------------------------------------------- #
def month_iter(start_ym: str, end_ym: str):
    """Yield datetime objects (first of month) from start_ym..end_ym inclusive."""
    cur = datetime.strptime(start_ym, "%Y-%m").replace(day=1)
    last = datetime.strptime(end_ym, "%Y-%m").replace(day=1)
    while cur <= last:
        yield cur
        cur += relativedelta(months=1)


def list_symbols(s3, bucket: str, base_prefix: str):
    """Return every <SYMBOL>/ folder under the given prefix."""
    paginator = s3.get_paginator("list_objects_v2")
    for page in paginator.paginate(
        Bucket=bucket, Prefix=base_prefix, Delimiter="/"
    ):
        for cp in page.get("CommonPrefixes", []):
            # cp['Prefix'] = "data/spot/monthly/klines/BTCUSDT/"
            parts = cp["Prefix"].rstrip("/").split("/")
            yield parts[-1]  # symbol


def download_one(s3, bucket: str, key: str, dest_csv: Path, tmp_dir: Path):
    """
    Download key -> tmp/, extract csv into dest_csv.parent,
    delete zip, skip work if csv already exists.
    """
    if dest_csv.exists():
        return "exists"

    tmp_dir.mkdir(parents=True, exist_ok=True)
    zip_path = tmp_dir / os.path.basename(key)

    if not zip_path.exists():  # may be there from earlier interrupt
        try:
            s3.download_file(bucket, key, str(zip_path))
        except botocore.exceptions.ClientError as e:
            if e.response["Error"]["Code"] in ("404", "NoSuchKey"):
                return "missing"
            raise

    # extract
    try:
        with zipfile.ZipFile(zip_path) as z:
            z.extractall(path=dest_csv.parent)
    finally:
        zip_path.unlink(missing_ok=True)

    return "downloaded"


# ------------- main -------------------------------------------------------- #
def build_cli() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Download & extract Binance monthly kline CSVs.",
    )
    p.add_argument("--market", choices=["spot", "futures"], required=True,
                   help="spot  |  futures (USD-M)")
    p.add_argument("--interval", default="1d",
                   help="kline interval: 1m 1h 1d 1w 1mo …")
    p.add_argument("--start", required=True,
                   help="YYYY-MM (inclusive)")
    p.add_argument("--end", default=datetime.utcnow().strftime("%Y-%m"),
                   help="YYYY-MM (inclusive); default = current month")
    p.add_argument("--dest", default="market_data/binance/klines",
                   help="destination folder for extracted CSVs")
    p.add_argument("--workers", type=int, default=os.cpu_count(),
                   help="parallel download threads")
    return p


def main():
    args = build_cli().parse_args()

    # ---------- S3 anonymous client ---------- #
    s3 = boto3.client("s3", config=Config(signature_version=UNSIGNED))
    bucket = "data.binance.vision"

    if args.market == "spot":
        base_prefix = "data/spot/monthly/klines/"
    else:  # USD-M futures
        base_prefix = "data/futures/um/monthly/klines/"

    symbols = sorted(list_symbols(s3, bucket, base_prefix))
    if not symbols:
        sys.exit("No symbols discovered – check connectivity.")

    months = list(month_iter(args.start, args.end))

    # Build the job list
    jobs = []
    for sym in symbols:
        for m in months:
            key = (
                f"{base_prefix}{sym}/{args.interval}/"
                f"{sym}-{args.interval}-{m.year}-{m.month:02d}.zip"
            )
            dest_dir = Path(args.dest) / args.market / sym / args.interval
            dest_csv = dest_dir / f"{sym}-{args.interval}-{m.year}-{m.month:02d}.csv"
            jobs.append((key, dest_csv))

    # Filter out work already done to keep tqdm accurate
    jobs = [(k, d) for (k, d) in jobs if not d.exists()]

    if not jobs:
        print("All requested data already present – nothing to do.")
        return

    tmp_dir = Path(args.dest) / "_tmp_zips"
    progress = tqdm(total=len(jobs), desc="Downloading")

    stats = {"downloaded": 0, "exists": 0, "missing": 0}

    def worker(job):
        k, dest = job
        result = download_one(s3, bucket, k, dest, tmp_dir)
        progress.update(1)
        return result

    with ThreadPoolExecutor(max_workers=args.workers) as pool:
        for r in pool.map(worker, jobs):
            stats[r] += 1

    progress.close()
    tmp_dir.rmdir()  # will silently fail if not empty

    print(
        f"\nDone.  {stats['downloaded']} new CSVs, "
        f"{stats['exists']} already present, "
        f"{stats['missing']} objects missing on S3."
    )


if __name__ == "__main__":
    main()
