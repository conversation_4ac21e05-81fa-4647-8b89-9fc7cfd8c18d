#!/usr/bin/env python3
import os
import glob
import sys
import csv
import json
import re
from typing import Dict, List, Tuple, Union, Optional

# Paths
THIS_DIR = os.path.dirname(__file__)
REPO_ROOT = os.path.abspath(os.path.join(THIS_DIR, ".."))

# Columns we care about
COLUMNS = ["TotalReturn", "CAGR", "AnnualVol", "Sharpe", "MaxDrawdown", "WinRate"]


# ===== Extractor logic (inlined) =====

def read_notebook_text_outputs(nb_path: str) -> str:
    with open(nb_path, "r", encoding="utf-8") as f:
        nb = json.load(f)
    texts: List[str] = []
    for cell in nb.get("cells", []):
        if cell.get("cell_type") != "code":
            continue
        for out in cell.get("outputs", []):
            if out.get("output_type") == "stream":
                text = out.get("text", "")
                if isinstance(text, list):
                    text = "".join(text)
                texts.append(text)
            elif out.get("output_type") in {"execute_result", "display_data"}:
                data = out.get("data", {})
                if isinstance(data, dict):
                    tp = data.get("text/plain")
                    if isinstance(tp, list):
                        texts.append("".join(tp))
                    elif isinstance(tp, str):
                        texts.append(tp)
    return "\n".join(texts)


def parse_strategy_summary_block(text: str) -> Optional[Dict[str, str]]:
    match = re.search(r"===\s*Strategy\s*Summary\s*===\s*(.*?)(?:\n\s*===|\n\n|\Z)", text, re.DOTALL | re.IGNORECASE)
    if not match:
        return None
    block = match.group(1)

    def find(pattern: str) -> Optional[str]:
        m = re.search(pattern, block, re.IGNORECASE)
        return m.group(1).strip() if m else None

    total_return = find(r"TotalReturn:\s*([+-]?\d+(?:\.\d+)?)%")
    cagr = find(r"CAGR:\s*([+-]?\d+(?:\.\d+)?)%")
    annual_vol = find(r"(?:AnnualVol|Annual Vol|Vol):\s*([+-]?\d+(?:\.\d+)?)%")
    sharpe = find(r"Sharpe:\s*([+-]?\d+(?:\.\d+)?)")
    max_dd = find(r"MaxDrawdown:\s*([+-]?\d+(?:\.\d+)?)%")

    if any(v is not None for v in [total_return, cagr, annual_vol, sharpe, max_dd]):
        result: Dict[str, str] = {}
        if total_return is not None:
            result["TotalReturn"] = f"{total_return}%"
        if cagr is not None:
            result["CAGR"] = f"{cagr}%"
        if annual_vol is not None:
            result["AnnualVol"] = f"{annual_vol}%"
        if sharpe is not None:
            result["Sharpe"] = f"{sharpe}"
        if max_dd is not None:
            result["MaxDrawdown"] = f"{max_dd}%"
        return result
    return None


def parse_dict_line_fallback(text: str) -> Optional[Dict[str, str]]:
    dict_line = None
    for line in text.splitlines():
        if "{'" in line and ("cagr" in line and "sharpe" in line):
            dict_line = line
            break
    if not dict_line:
        return None

    def extract_float(key: str) -> Optional[float]:
        m = re.search(rf"'{key}'\s*:\s*(?:np\.float64\()?([+-]?[0-9]*\.?[0-9]+(?:[eE][+-]?\d+)?)\)?", dict_line)
        if m:
            try:
                return float(m.group(1))
            except ValueError:
                return None
        return None

    vals: Dict[str, float] = {}
    for k in ["cum_return", "cagr", "vol", "sharpe", "max_dd", "win_rate"]:
        v = extract_float(k)
        if v is not None:
            vals[k] = v

    if not vals:
        return None

    out: Dict[str, str] = {}
    if "cum_return" in vals:
        out["TotalReturn"] = f"{vals['cum_return'] * 100:.2f}%"
    if "cagr" in vals:
        out["CAGR"] = f"{vals['cagr'] * 100:.2f}%"
    if "vol" in vals:
        out["AnnualVol"] = f"{vals['vol'] * 100:.2f}%"
    if "sharpe" in vals:
        out["Sharpe"] = f"{vals['sharpe']:.2f}"
    if "max_dd" in vals:
        out["MaxDrawdown"] = f"{vals['max_dd'] * 100:.2f}%"
    if "win_rate" in vals:
        out["WinRate"] = f"{vals['win_rate'] * 100:.2f}%"

    return out if out else None


def extract_metrics(nb_path: str) -> Tuple[Dict[str, str], str]:
    text = read_notebook_text_outputs(nb_path)
    metrics = parse_strategy_summary_block(text)
    source = "Strategy Summary"
    if not metrics:
        metrics = parse_dict_line_fallback(text)
        source = "Printed dict"
    if not metrics:
        metrics = {}
        source = "Not found"
    return metrics, source


# ===== Aggregation & output =====

def collect_metrics(root: str) -> List[Tuple[str, Dict[str, str]]]:
    pattern = os.path.join(root, "src", "sandbox", "**", "*.ipynb")
    files = sorted(glob.glob(pattern, recursive=True))
    results: List[Tuple[str, Dict[str, str]]] = []
    for nb in files:
        if ".ipynb_checkpoints" in nb:
            continue
        metrics, _ = extract_metrics(nb)
        if metrics:
            results.append((nb, metrics))
    return results


def build_markdown_table(rows: List[Tuple[str, Dict[str, str]]], root: str) -> str:
    header = ["Notebook"] + COLUMNS
    lines = ["| " + " | ".join(header) + " |",
             "|" + "|".join(["---"] * len(header)) + "|"]
    for nb_path, metrics in rows:
        rel = os.path.relpath(nb_path, os.path.join(root, "src", "sandbox"))
        values = [rel]
        for col in COLUMNS:
            values.append(metrics.get(col, ""))
        lines.append("| " + " | ".join(values) + " |")
    return "\n".join(lines) + "\n"


def to_number(value: str) -> Union[float, str]:
    if value is None:
        return ""
    value = str(value).strip()
    if value == "":
        return ""
    if value.endswith("%"):
        try:
            return float(value[:-1])
        except ValueError:
            return value
    try:
        return float(value)
    except ValueError:
        return value


def write_csv(rows: List[Tuple[str, Dict[str, str]]], root: str, out_path: str) -> None:
    header = ["Notebook"] + COLUMNS
    with open(out_path, "w", encoding="utf-8", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(header)
        for nb_path, metrics in rows:
            rel = os.path.relpath(nb_path, os.path.join(root, "src", "sandbox"))
            row: List[Union[str, float]] = [rel]
            for col in COLUMNS:
                row.append(to_number(metrics.get(col, "")))
            writer.writerow(row)


def main():
    repo_root = REPO_ROOT
    rows = collect_metrics(repo_root)

    out_dir = os.path.join(repo_root, "output", "notebook_metrics")
    os.makedirs(out_dir, exist_ok=True)

    csv_path = os.path.join(out_dir, "sandbox_metrics.csv")
    write_csv(rows, repo_root, csv_path)

    md_path = os.path.join(out_dir, "sandbox_metrics.md")
    if not rows:
        md_content = "No notebooks with metrics found.\n"
    else:
        md_content = build_markdown_table(rows, repo_root)
    with open(md_path, "w", encoding="utf-8") as f:
        f.write(md_content)

    print(f"Found {len(rows)} notebooks with metrics.")
    print(f"Wrote CSV: {csv_path}")
    print(f"Wrote MD : {md_path}")


if __name__ == "__main__":
    main()
