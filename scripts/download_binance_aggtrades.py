#!/usr/bin/env python3
"""
download_binance_aggtrades.py
--------------------------------
Download AND UNZIP daily aggTrades data for a USDT-M futures symbol
between START_DATE and END_DATE (inclusive).

Usage
-----
$ python download_binance_aggtrades.py BTCUSDT 2023-01-01 2023-01-31
"""
import sys
from datetime import datetime, timedelta, date
from pathlib import Path
import zipfile
import requests

BASE_URL   = "https://data.binance.vision/data/futures/um/daily/aggTrades"
LOCAL_ROOT = Path("market_data/binance/aggTrades")
TIMEOUT    = 30   # seconds

# ---------- helpers ---------------------------------------------------------
def date_range(start: date, end: date):
    for i in range((end - start).days + 1):
        yield start + timedelta(i)

def download_day(symbol: str, day: date):
    fname_zip = f"{symbol}-aggTrades-{day:%Y-%m-%d}.zip"
    url       = f"{BASE_URL}/{symbol}/{fname_zip}"
    dest_zip  = LOCAL_ROOT / symbol / fname_zip
    dest_zip.parent.mkdir(parents=True, exist_ok=True)

    # 1. Download ZIP if missing
    if not dest_zip.exists():
        print(f"[get ] {url}")
        try:
            r = requests.get(url, stream=True, timeout=TIMEOUT)
            if r.status_code == 200:
                with open(dest_zip, "wb") as f:
                    for chunk in r.iter_content(chunk_size=1 << 15):
                        if chunk:
                            f.write(chunk)
                print(f"[save] {dest_zip}")
            elif r.status_code == 404:
                print(f"[404 ] {url} — file not found, skipping")
                return
            else:
                print(f"[warn] {url} — HTTP {r.status_code}")
                return
        except Exception as exc:
            print(f"[err ] {url} — {exc}")
            return
    else:
        print(f"[skip] {dest_zip} already present")

    # 2. Unzip (always tries, in case file was present but csv missing)
    fname_csv = fname_zip.replace(".zip", ".csv")
    dest_csv  = dest_zip.with_suffix(".csv")

    if dest_csv.exists():
        print(f"[skip] {dest_csv} already extracted")
        return

    try:
        with zipfile.ZipFile(dest_zip) as z:
            members = z.namelist()
            if len(members) != 1:
                print(f"[warn] {dest_zip} has {len(members)} entries, expected 1")
            z.extractall(dest_csv.parent)
        print(f"[unzip] -> {dest_csv}")
    except zipfile.BadZipFile:
        print(f"[err ] {dest_zip} is not a valid ZIP (corrupt?)")
    except Exception as exc:
        print(f"[err ] extracting {dest_zip}: {exc}")

# ---------- entry point -----------------------------------------------------
def main(symbol: str, start_str: str, end_str: str):
    start = datetime.strptime(start_str, "%Y-%m-%d").date()
    end   = datetime.strptime(end_str,   "%Y-%m-%d").date()
    if end < start:
        raise SystemExit("END_DATE must not be before START_DATE")

    symbol = symbol.upper()
    for day in date_range(start, end):
        download_day(symbol, day)

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python download_binance_aggtrades.py SYMBOL START_DATE END_DATE")
        sys.exit(1)
    main(*sys.argv[1:])
