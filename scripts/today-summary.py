#!/usr/bin/env python3
import json
import glob
import argparse
import os
from datetime import datetime
from tabulate import tabulate
import sys
import resend
import pytz
import dotenv

dotenv.load_dotenv()


def process_json_files(date, run_type='live'):
    # Find all metadata.json files for the specified date and run type
    pattern = f'stats/*/*/{date}/{run_type}/metadata.json'
    json_files = glob.glob(pattern)
    
    data_list = []
    
    for file_path in json_files:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                
                # Handle multiple JSON objects in one file
                if '}{' in content:
                    # Split the content on }{ and properly close/open each JSON object
                    parts = content.split('}{')
                    json_strings = [parts[0] + '}'] + ['{' + part + '}' for part in parts[1:-1]] + ['{' + parts[-1]]
                else:
                    json_strings = [content]
                
                for json_str in json_strings:
                    try:
                        data = json.loads(json_str)
                        # Format the symbols_traded to be a comma-separated string
                        if isinstance(data.get('symbols_traded', []), list):
                            data['symbols_traded'] = ', '.join(data.get('symbols_traded', []))
                        # Add the run type to the data
                        data['run_type'] = run_type
                        data_list.append(data)
                    except json.JSONDecodeError as e:
                        print(f"Error parsing JSON object in {file_path}: {e}")
        except Exception as e:
            print(f"Error processing file {file_path}: {e}")
    
    return data_list

def format_table(data_list):
    if not data_list:
        return "No data found for the specified date and run type."
    
    # Define columns for the table
    headers = [
        "Strategy Name", 
        "Date", 
        "Run Type",
        "Initial Capital", 
        "Final Capital", 
        "Total Trades", 
        "Symbols Traded", 
    ]
    
    # Prepare rows for the table
    rows = []
    for data in data_list:
        row = [
            data.get('strategy_name', 'N/A'),
            data.get('start_date', 'N/A'),
            data.get('run_type', 'N/A'),
            f"${data.get('initial_capital', 0):,.2f}",
            f"${data.get('final_capital', 0):,.2f}",
            data.get('total_trades', 0),
            data.get('symbols_traded', 'N/A'),
        ]
        rows.append(row)
    
    # Return the table as a string
    return tabulate(rows, headers=headers, tablefmt="grid")

def display_table(data_list):
    table_str = format_table(data_list)
    print(table_str)

def send_email(recipient, subject, data_list, api_key=None):
    """Send an email with trading strategy results using Resend SDK"""
    
    # Check for API key
    if not api_key:
        api_key = os.environ.get('RESEND_API_KEY')
        if not api_key:
            print("Error: Resend API key not provided. Use --api-key or set RESEND_API_KEY environment variable.")
            sys.exit(1)
    
    # Set API key for Resend SDK
    resend.api_key = api_key
    
    # Format date for email
    today = datetime.now().strftime("%Y-%m-%d")
    
    # Create email content
    html_content = f"""
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                font-size: 16px;
                color: #333;
                margin: 0;
                padding: 15px;
            }}
            h2 {{
                color: #444;
                margin-top: 0;
                font-size: 22px;
            }}
            .strategy-card {{
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
                background-color: #f9f9f9;
            }}
            .strategy-name {{
                font-weight: bold;
                font-size: 18px;
                margin-bottom: 10px;
                color: #0056b3;
            }}
            .strategy-details {{
                display: flex;
                flex-wrap: wrap;
                margin: -5px;
            }}
            .detail-item {{
                flex: 1 0 45%;
                padding: 5px;
                box-sizing: border-box;
            }}
            .detail-label {{
                font-weight: bold;
                display: block;
                color: #666;
                font-size: 14px;
            }}
            .detail-value {{
                font-size: 16px;
            }}
            .run-type {{
                display: inline-block;
                padding: 3px 8px;
                border-radius: 4px;
                font-size: 12px;
                margin-left: 8px;
                text-transform: uppercase;
                background-color: #e6f7ff;
                color: #0070d1;
            }}
            .run-type.backtest {{
                background-color: #fff3e6;
                color: #d46b08;
            }}
            .symbols {{
                margin-top: 10px;
                font-size: 14px;
            }}
            .footer {{
                margin-top: 20px;
                font-size: 14px;
                color: #666;
                border-top: 1px solid #eee;
                padding-top: 10px;
            }}
            @media (max-width: 600px) {{
                .detail-item {{
                    flex: 1 0 100%;
                }}
            }}
        </style>
    </head>
    <body>
        <h2>Trading Strategy Results - {today}</h2>
        <p>Below are the trading strategy results for the specified date:</p>
    """
    
    # Add cards for each strategy instead of table rows
    for data in data_list:
        strategy_name = data.get('strategy_name', 'N/A')
        start_date = data.get('start_date', 'N/A')
        run_type = data.get('run_type', 'live')
        run_type_class = "backtest" if run_type == "backtest" else ""
        initial_capital = f"${data.get('initial_capital', 0):,.2f}"
        final_capital = f"${data.get('final_capital', 0):,.2f}"
        total_trades = data.get('total_trades', 0)
        symbols_traded = data.get('symbols_traded', 'N/A')
        
        html_content += f"""
        <div class="strategy-card">
            <div class="strategy-name">
                {strategy_name}
                <span class="run-type {run_type_class}">{run_type}</span>
            </div>
            <div class="strategy-details">
                <div class="detail-item">
                    <span class="detail-label">Date</span>
                    <span class="detail-value">{start_date}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Initial Capital</span>
                    <span class="detail-value">{initial_capital}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Final Capital</span>
                    <span class="detail-value">{final_capital}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Total Trades</span>
                    <span class="detail-value">{total_trades}</span>
                </div>
            </div>
            <div class="symbols">
                <span class="detail-label">Symbols Traded</span>
                <span class="detail-value">{symbols_traded}</span>
            </div>
        </div>
        """
    
    html_content += """
    </body>
    </html>
    """
    
    # Create plain text version for email clients that don't support HTML
    text_content = f"Trading Strategy Results - {today}\n\n"
    text_content += format_table(data_list)
    text_content += "\n\n"
    
    # Get the sender email from environment or use default
    sender_email = os.environ.get('EMAIL_FROM')
    
    try:
        # Prepare the email parameters using Resend SDK format
        params = {
            "from": sender_email,
            "to": [recipient],
            "subject": subject,
            "html": html_content,
            "text": text_content
        }
        
        # Send email using Resend SDK
        email = resend.Emails.send(params)
        print(f"Email sent successfully to {recipient} (ID: {email['id']})")
        return True
    except Exception as e:
        print(f"Error sending email: {e}")
        return False

def main():
    # Parse command line arguments
    today = datetime.now(pytz.timezone('America/Los_Angeles')).strftime('%Y%m%d')
    parser = argparse.ArgumentParser(description='Display trading strategy results as a table and optionally send via email.')
    parser.add_argument('--date', type=str, default=today,
                        help='Date in format YYYYMMDD (default: today)')
    parser.add_argument('--email', type=str, 
                        help='Send results to this email address')
    parser.add_argument('--subject', type=str, default=f'Report for {today}',
                        help='Email subject line (default: "Report for YYYYMMDD")')
    parser.add_argument('--api-key', type=str,
                        help='Resend API key (can also use RESEND_API_KEY environment variable)')
    parser.add_argument('--run-type', type=str, choices=['live', 'backtest', 'all'], default='live',
                        help='Type of run to include: live, backtest, or all (default: live)')
    
    args = parser.parse_args()
    
    # Process data based on run type
    data_list = []
    
    if args.run_type == 'live' or args.run_type == 'all':
        live_data = process_json_files(args.date, 'live')
        data_list.extend(live_data)
        
    if args.run_type == 'backtest' or args.run_type == 'all':
        backtest_data = process_json_files(args.date, 'backtest')
        data_list.extend(backtest_data)
    
    # Display table to console
    display_table(data_list)
    
    # Send email if requested
    if args.email and data_list:
        send_email(args.email, args.subject, data_list, args.api_key)
    elif args.email and not data_list:
        print(f"No data found for {args.date} with run type '{args.run_type}'. Email not sent.")

if __name__ == "__main__":
    main()