#!/bin/bash

# Function to run the backfill process
run_backfill() {
  # Calculate date from 2 days ago in YYYY-MM-DD format
  START_DATE=$(date -d "2 days ago" +%Y-%m-%d)
  
  # Run the Python command with the calculated start date
  python -m marketdata.backfill_theta_eod --ticker-file all_tickers.txt --start-date $START_DATE
  
  echo "Backfill completed at $(date)"
}

# If script is called with --now argument, run immediately and exit
if [[ "$1" == "--now" ]]; then
  run_backfill
  exit 0
fi

# Main loop to run at 9:30PM PDT every day
echo "Starting scheduled execution - will run daily at 9:30PM PDT"
while true; do
  # Set timezone to PDT/PST
  export TZ=America/Los_Angeles
  
  # Calculate seconds until next 9:30PM
  current_time=$(date +%s)
  
  # Get current hour and minute
  current_hour=$(date +%H)
  current_minute=$(date +%M)
  
  # Calculate next run time
  if [ $current_hour -lt 21 ] || ([ $current_hour -eq 21 ] && [ $current_minute -lt 30 ]); then
    # If current time is before 9:30PM, schedule for today at 9:30PM
    next_run=$(date -d "today 21:30:00" +%s)
  else
    # If current time is after 9:30PM, schedule for tomorrow at 9:30PM
    next_run=$(date -d "tomorrow 21:30:00" +%s)
  fi
  
  sleep_seconds=$((next_run - current_time))
  
  echo "Next run scheduled at $(date -d "@$next_run"). Waiting for $sleep_seconds seconds..."
  
  # Sleep until 9:30PM
  sleep $sleep_seconds
  
  # Run the backfill
  run_backfill
done