#!/usr/bin/env python3

import logging
from tickers.ticker_helpers import TickerInfoStore
from dotenv import load_dotenv

load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add a CLI entrypoint
def create_cli():
    """
    Creates a command-line interface for refreshing ticker data and managing cache files.
    
    Returns:
        The parsed command-line arguments.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Refresh ticker data and manage cache files")
    parser.add_argument(
        "--cache-dir", 
        default="cache", 
        help="Directory where cache files are stored (default: cache)"
    )
    parser.add_argument(
        "--max-age", 
        type=int, 
        default=90, 
        help="Maximum age of cache files in days (default: 90)"
    )
    parser.add_argument(
        "--force-refresh",
        action="store_true",
        help="Force refresh even if today's data exists"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Enable verbose logging"
    )
    
    return parser.parse_args()


if __name__ == "__main__":
    # Parse command-line arguments
    args = create_cli()
    
    # Configure logging
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # Create TickerInfoStore instance
    store = TickerInfoStore(cache_dir=args.cache_dir)
    
    # Perform refresh
    if args.force_refresh:
        # Fetch regardless of existing cache
        today = datetime.date.today()
        try:
            df = store.fetch_ticker_data(today)
            logger.info(f"Force refreshed {len(df)} ticker records")
        except Exception as e:
            logger.error(f"Failed to refresh ticker data: {e}")
        
        # Prune old cache files
        pruned, kept = store.prune_old_cache_files(args.max_age)
        logger.info(f"Cache pruning complete: {pruned} files pruned, {kept} files kept")
    else:
        # Normal refresh
        try:
            store.refresh_ticker_data(max_age_days=args.max_age)
        except Exception as e:
            logger.error(f"Failed to refresh ticker data: {e}")